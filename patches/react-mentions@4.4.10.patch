diff --git a/CHANGELOG.md b/CHANGELOG.md
deleted file mode 100644
index c24a07e10372e75ce11d3724f8d49eda822cb630..0000000000000000000000000000000000000000
diff --git a/dist/react-mentions.esm.js b/dist/react-mentions.esm.js
index 2efebba1599ee54c7b54715c020f3e107760718c..fbd516cd49f75a5a86eaa376e3a7cc4b1d27f0a3 100644
--- a/dist/react-mentions.esm.js
+++ b/dist/react-mentions.esm.js
@@ -708,6 +708,7 @@ function Highlighter(_ref) {
       containerRef = _ref.containerRef,
       children = _ref.children,
       singleLine = _ref.singleLine,
+      renderMention = _ref.renderMention,
       style = _ref.style;
 
   var _useState = useState({
@@ -794,7 +795,8 @@ function Highlighter(_ref) {
       key: key
     };
     var child = Children.toArray(children)[mentionChildIndex];
-    return /*#__PURE__*/React.cloneElement(child, props);
+    var cloned = /*#__PURE__*/React.cloneElement(child, props);
+    return typeof renderMention === 'function' ? renderMention(cloned, id, display) : cloned;
   };
 
   var renderHighlighterCaret = function renderHighlighterCaret(children) {
@@ -838,6 +840,8 @@ var styled = createDefaultStyle({
   wordWrap: 'break-word',
   border: '1px solid transparent',
   textAlign: 'start',
+  pointerEvents: 'none',
+
   '&singleLine': {
     whiteSpace: 'pre',
     wordWrap: null
@@ -1223,6 +1227,10 @@ var MentionsInput = /*#__PURE__*/function (_React$Component) {
         onScroll: _this.updateHighlighterScroll
       }, !readOnly && !disabled && {
         onChange: _this.handleChange,
+        onMouseUp: e => {
+          _this.handleSelect(e);
+          _this.props.onMouseUp?.(e);
+        },
         onSelect: _this.handleSelect,
         onKeyDown: _this.handleKeyDown,
         onBlur: _this.handleBlur,
@@ -1245,7 +1253,7 @@ var MentionsInput = /*#__PURE__*/function (_React$Component) {
 
       var inputProps = _this.getInputProps();
 
-      return /*#__PURE__*/React.createElement("div", style('control'), _this.renderHighlighter(), singleLine ? _this.renderInput(inputProps) : _this.renderTextarea(inputProps));
+      return /*#__PURE__*/React.createElement("div", style('control'), singleLine ? _this.renderInput(inputProps) : _this.renderTextarea(inputProps), _this.renderHighlighter());
     });
 
     _defineProperty(_assertThisInitialized(_this), "renderInput", function (props) {
@@ -1321,6 +1329,7 @@ var MentionsInput = /*#__PURE__*/function (_React$Component) {
           selectionEnd = _this$state.selectionEnd;
       var _this$props3 = _this.props,
           singleLine = _this$props3.singleLine,
+          renderMention = _this$props3.renderMention,
           children = _this$props3.children,
           value = _this$props3.value,
           style = _this$props3.style;
@@ -1329,6 +1338,7 @@ var MentionsInput = /*#__PURE__*/function (_React$Component) {
         style: style('highlighter'),
         value: value,
         singleLine: singleLine,
+        renderMention: renderMention,
         selectionStart: selectionStart,
         selectionEnd: selectionEnd,
         onCaretPositionChange: _this.handleCaretPositionChange
@@ -1380,6 +1390,11 @@ var MentionsInput = /*#__PURE__*/function (_React$Component) {
         }
       }
 
+      // 如果是粘贴的话，直接跳过
+      if (ev.inputType === 'insertFromPaste') {
+        return;
+      }
+
       var value = _this.props.value || '';
       var config = readConfigFromChildren(_this.props.children);
       var newPlainTextValue = ev.target.value;
@@ -1938,7 +1953,7 @@ var MentionsInput = /*#__PURE__*/function (_React$Component) {
     }
   }, {
     key: "handlePaste",
-    value: function handlePaste(event) {
+    value: async function handlePaste(event) {
       if (event.target !== this.inputElement) {
         return;
       }
@@ -1947,10 +1962,13 @@ var MentionsInput = /*#__PURE__*/function (_React$Component) {
         return;
       }
 
-      event.preventDefault();
+      // 拦截默认行为会导致原生文本框的撤销事件无法正常工作
+      // event.preventDefault();
       var _this$state3 = this.state,
-          selectionStart = _this$state3.selectionStart,
-          selectionEnd = _this$state3.selectionEnd;
+          // 在preact中如果插入了一个mention，此时不触发onSelect或者onChange不会更新state里的光标位置
+          // 且 onFocus 事件也拿不到最新的selectionStart, 基于业务，假设paste的位置没有光标，则一定是在最后面
+          selectionStart = _this$state3.selectionStart ?? this.props.value.length,
+          selectionEnd = _this$state3.selectionEnd ?? this.props.value.length;
       var _this$props7 = this.props,
           value = _this$props7.value,
           children = _this$props7.children;
@@ -1959,9 +1977,29 @@ var MentionsInput = /*#__PURE__*/function (_React$Component) {
       var markupEndIndex = mapPlainTextIndex(value, config, selectionEnd, 'END');
       var pastedMentions = event.clipboardData.getData('text/react-mentions');
       var pastedData = event.clipboardData.getData('text/plain');
-      var newValue = spliceString(value, markupStartIndex, markupEndIndex, pastedMentions || pastedData).replace(/\r/g, '');
+      var rawPastedText = pastedMentions || pastedData;
+
+      // 改为劫持复制事件，因为要回退一个字符
+      if (typeof this.props.transformPastedText === 'function') {
+        try {
+          event.preventDefault();
+          await this.props.transformPastedText(rawPastedText);
+          return;
+        }
+        catch(ex) {
+          // 如果转换失败了，走正常的粘贴逻辑
+        }
+      }
+
+      var newValue = spliceString(
+        value,
+        markupStartIndex,
+        markupEndIndex,
+        rawPastedText,
+      ).replace(/\r/g, '');
       var newPlainTextValue = getPlainText(newValue, config);
       var eventMock = {
+        inputType: 'insertFromPaste',
         target: _objectSpread$1(_objectSpread$1({}, event.target), {}, {
           value: newValue
         })
@@ -1969,7 +2007,7 @@ var MentionsInput = /*#__PURE__*/function (_React$Component) {
       this.executeOnChange(eventMock, newValue, newPlainTextValue, getMentions(newValue, config)); // Move the cursor position to the end of the pasted data
 
       var startOfMention = findStartOfMentionInPlainText(value, config, selectionStart);
-      var nextPos = (startOfMention || selectionStart) + getPlainText(pastedMentions || pastedData, config).length;
+      var nextPos = (startOfMention || selectionStart) + getPlainText(rawPastedText, config).length;
       this.setState({
         selectionStart: nextPos,
         selectionEnd: nextPos,
