{"name": "comate-enterprise", "displayName": "Baidu Comate Enterprise", "description": "Coding mate, Pair you create. Your AI Coding Assistant with Autocomplete & Chat for Java, Go, JS, Python & more", "publisher": "BaiduComate", "homepage": "https://comate.baidu.com", "version": "3.3.2", "license": "UNLICENSED", "private": true, "keywords": ["文心", "百度", "文心快码", "快码", "kuaima", "wenxinkuaima", "baidu", "comate", "assistant", "ernie-bot", "ernie-code", "ai", "AI code", "AI 自动编程", "AI 编程", "AI 代码助手", "AI代码助手", "AI 编码助手", "AI编码助手", "编码助手", "智能", "百度云", "百度 AI", "chat", "autocomplete", "AI copilot", "智能编程", "自动编程", "ai coding assistant", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Copilot", "autocoding", "coplit", "githubcopilot", "documentation", "autocomplete", "intellisense", "refactor", "javascript", "python", "typescript", "php", "go", "golang", "ruby", "c++", "java"], "engines": {"vscode": "^1.69.0"}, "icon": "assets/icon.png", "categories": ["Programming Languages", "Machine Learning", "Education", "Snippets"], "tags": ["文心", "百度", "文心快码", "快码", "kuaima", "wenxinkuaima", "ai", "autocomplete", "code completion", "chat", "intellisense", "intellicode", "generative ai", "ernie-code", "ernie-bot", "javascript", "python", "java", "typescript", "c#", "c++", "php", "shell", "c", "ruby"], "activationEvents": ["onFileSystem:comate-diff", "onFileSystem:comate-ut-report", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "baidu.comate.generate", "title": "%command.generate.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.cancel", "title": "%command.cancel.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.createUnitTest", "title": "%command.createUnitTest.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.showChatPanel", "title": "%command.showChatPanel.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.optimizeCode", "title": "%command.optimizeCode.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.clearSession", "title": "%command.clearSession.title%", "icon": "$(clear-all)", "enablement": "baidu.comate.context.messagesCount > 0 || baidu.comate.context.comatePairMessagesCount > 0 || baidu.comate.context.helpSendEvent", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.openPluginConfigPanel", "title": "%command.openPluginConfigPanel.title%", "category": "<PERSON><PERSON> Comate", "enablement": "baidu.comate.pluginConfigPanelEnabled", "icon": "$(extensions)"}, {"command": "baidu.comate.openSettings", "title": "%command.openSettings.title%", "icon": "$(gear)", "enablement": "baidu.comate.context.isLogin", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.visitHelpDocs", "title": "%command.visitHelpDocs.title%", "icon": "$(question)", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.explainSelectedCode", "title": "%command.explainSelectedCode.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.authorized", "title": "%command.authorized.title%", "enablement": "false", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.renewLicense", "title": "%command.renewLicense.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.setLogLevel", "title": "%command.setLogLevel.title%", "category": "<PERSON><PERSON> Comate", "icon": "$(gear)"}, {"command": "baidu.comate.feedback", "title": "%command.feedback.title%", "category": "<PERSON><PERSON> Comate", "icon": "$(feedback)"}, {"command": "baidu.comate.logout", "title": "%command.logout.title%", "category": "<PERSON><PERSON> Comate", "enablement": "baidu.comate.context.isLogin", "icon": "$(log-out)"}, {"command": "baidu.comate.inlineChat.close", "title": "关闭inlineChat", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.sourceControlGenerateMessage", "title": "%command.generateMessage.title%", "icon": "assets/newIcon.png"}, {"command": "baidu.comate.recreateIndex", "title": "%command.recreateIndex.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.createNewChat", "title": "%command.createNewChat.title%", "icon": "$(add)", "enablement": "baidu.comate.context.isLogin", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.historyChat", "title": "历史对话", "icon": "$(history)", "enablement": "baidu.comate.context.isLogin", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.moreOperations", "title": "更多", "icon": "$(ellipsis)", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.downloadLogs", "title": "%command.downloadLogs.title%", "icon": "$(cloud-download)", "category": "<PERSON><PERSON> Comate"}], "menus": {"commandPalette": [{"command": "baidu.comate.generate", "when": "baidu.comate.activated"}, {"command": "baidu.comate.cancel", "when": "false"}, {"command": "baidu.comate.clearSession", "when": "false"}, {"command": "baidu.comate.explainSelectedCode", "when": "false"}, {"command": "baidu.comate.optimizeCode", "when": "false"}, {"command": "baidu.comate.createUnitTest", "when": "false"}, {"command": "baidu.comate.explainSelectedCode", "when": "false"}, {"command": "baidu.comate.authorized", "when": "false"}, {"command": "baidu.comate.renewLicense", "when": "false"}, {"command": "baidu.comate.openPluginConfigPanel", "when": "false"}, {"command": "baidu.comate.logout", "when": "baidu.comate.activated"}, {"command": "baidu.comate.inlineChat.close", "when": "false"}, {"command": "baidu.comate.historyChat", "when": "false"}, {"command": "baidu.comate.moreOperations", "when": "false"}], "editor/context": [{"submenu": "baidu.comate.commands", "group": "0_0_comate@1"}], "baidu.comate.commands": [{"command": "baidu.comate.createUnitTest", "when": "baidu.comate.activated && baidu.comate.enableUnitTest && editorHasSelection", "group": "1_comate@1"}, {"command": "baidu.comate.explainSelectedCode", "when": "baidu.comate.activated && editorHasSelection", "group": "1_comate@2"}, {"command": "baidu.comate.optimizeCode", "when": "baidu.comate.activated && editorHasSelection", "group": "1_comate@3"}, {"command": "baidu.comate.showChatPanel", "when": "baidu.comate.activated", "group": "1_comate@4"}], "view/title": [{"command": "baidu.comate.moreOperations", "when": "view == comate.views.chat && baidu.comate.context.isLogin", "group": "navigation@5"}, {"command": "baidu.comate.visitHelpDocs", "when": "false", "group": "navigation@5"}, {"command": "baidu.comate.openSettings", "when": "view == comate.views.chat", "group": "navigation@4"}, {"command": "baidu.comate.openPluginConfigPanel", "when": "false", "group": "navigation@3"}, {"command": "baidu.comate.historyChat", "when": "view == comate.views.chat", "group": "navigation@2"}, {"command": "baidu.comate.createNewChat", "when": "view == comate.views.chat", "group": "navigation@1"}], "scm/title": [{"command": "baidu.comate.sourceControlGenerateMessage", "group": "navigation"}]}, "submenus": [{"id": "baidu.comate.commands", "label": "<PERSON><PERSON> Comate"}], "keybindings": [{"command": "baidu.comate.toggleChatPanel", "key": "ctrl+y", "win": "win+y", "mac": "cmd+y"}, {"command": "baidu.comate.toggleChatPanel", "key": "ctrl+alt+y", "win": "ctrl+shift+y", "mac": "cmd+alt+y"}, {"command": "baidu.comate.cancel", "key": "escape", "mac": "escape", "when": "inlineSuggestionVisible"}, {"command": "baidu.comate.inlinechat.cancel", "key": "cmd+z", "when": "baidu.comate.activated && editorTextFocus && baidu.comate.editor.isLoading"}, {"command": "baidu.comate.diffAllReject", "key": "ctrl+z", "win": "ctrl+z", "mac": "cmd+z", "when": "baidu.comate.activated && editorTextFocus && !editorReadonly && baidu.comate.inlinechat.codelensVisible"}, {"command": "baidu.comate.diffDualScreenOpen", "key": "alt+d", "when": "baidu.comate.activated && editorTextFocus && !editorReadonly"}, {"command": "editor.action.inlineSuggest.commit", "key": "Tab", "when": "inlineSuggestionHasIndentationLessThanTabSize && inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && suggestWidgetVisible"}, {"command": "editor.action.inlineSuggest.trigger", "key": "alt+\\", "when": "editorTextFocus && !editorHasSelection && baidu.comate.activated && !inlineSuggestionsVisible"}, {"command": "baidu.comate.acceptNextLine", "key": "ctrl+down", "win": "ctrl+down", "mac": "cmd+down", "when": "editorTextFocus && !editorReadonly && baidu.comate.activated && inlineSuggestionVisible"}, {"command": "baidu.comate.inlinechat.cancel", "key": "ctrl+z", "win": "ctrl+z", "mac": "cmd+z", "when": "baidu.comate.activated && editorTextFocus && baidu.comate.editor.isLoading"}, {"command": "baidu.comate.inlinechat.cancel", "key": "escape", "when": "baidu.comate.activated && editorTextFocus && baidu.comate.editor.isLoading"}, {"command": "baidu.comate.diffAllAccept", "key": "alt+a", "when": "baidu.comate.activated && editorTextFocus && !editorReadonly"}, {"command": "baidu.comate.diffAllAccept", "key": "ctrl+s", "win": "ctrl+s", "mac": "cmd+s", "when": "baidu.comate.activated && editorTextFocus && !editorReadonly && baidu.comate.inlinechat.codelensVisible"}, {"command": "baidu.comate.diffAllReject", "key": "alt+x", "when": "baidu.comate.activated && editorTextFocus && !editorReadonly"}, {"command": "baidu.comate.diffAccept", "key": "ctrl+s", "win": "ctrl+s", "mac": "cmd+s", "when": "baidu.comate.activated && editorTextFocus && isInDiffEditor &&baidu.comate.inlinechat.isDiffCodelensVisible"}, {"command": "baidu.comate.diffAccept", "key": "alt+a", "when": "baidu.comate.activated && editorTextFocus && isInDiffEditor"}, {"command": "baidu.comate.diffReject", "key": "alt+x", "when": "baidu.comate.activated && editorTextFocus && isInDiffEditor"}, {"command": "baidu.comate.diffReject", "key": "ctrl+z", "win": "ctrl+z", "mac": "cmd+z", "when": "baidu.comate.activated && editorTextFocus && isInDiffEditor && baidu.comate.inlinechat.isDiffCodelensVisible"}, {"command": "baidu.comate.tabStreak.tab", "key": "Tab", "when": "editorTextFocus && baidu.comate.context.tabStreak"}, {"command": "baidu.comate.tabStreak.escape", "key": "Escape", "when": "editorTextFocus && baidu.comate.context.tabStreak"}], "viewsContainers": {"activitybar": [{"id": "comate", "title": "%activitybar.comate.title%", "icon": "assets/Comate.svg"}]}, "views": {"comate": [{"id": "comate.views.chat", "type": "webview", "name": ""}]}, "configuration": [{"title": "%configuration.basic.title%", "properties": {"baidu.comate.username": {"type": "string", "default": "", "description": "%configuration.basic.username.description%"}, "baidu.comate.license": {"type": "string", "default": "", "description": "%configuration.basic.license.description%"}, "baidu.comate.privateService": {"type": "string", "default": "", "description": "%configuration.private.serviceBaseUrl.description%"}}}, {"title": "%configuration.suggestion.title%", "properties": {"baidu.comate.enableInlineSuggestion": {"type": "boolean", "default": true, "description": "%configuration.suggestion.enableInlineSuggestion.description%"}, "baidu.comate.langSuggestion": {"type": "object", "default": {"all": true, "cpp": true, "css": true, "go": true, "html": true, "java": true, "javascript": true, "less": true, "perl": true, "php": true, "python": true, "ruby": true, "shell": true, "swift": true, "typescript": true, "others": true, "vue": true, "san": true, "sass": true, "scss": true, "vhdl": true, "lua": true, "mermaid": true, "pug": true, "swan": true, "stylus": true, "rust": true, "kotlin": true, "graphql": true, "objectivec": true, "ada": true, "agda": true, "alloy": true, "antlr": true, "applescript": true, "assembly": true, "augeas": true, "awk": true, "batchfile": true, "bluespec": true, "csharp": true, "clojure": true, "cmake": true, "coffeescript": true, "commonlisp": true, "cuda": true, "dart": true, "dockerfile": true, "elixir": true, "elm": true, "emacslisp": true, "erlang": true, "fsharp": true, "fortran": true, "glsl": true, "groovy": true, "haskell": true, "idris": true, "isabelle": true, "javaserverpages": true, "json": true, "julia": true, "lean": true, "literateagda": true, "literatecoffeescript": true, "literatehaskell": true, "makefile": true, "maple": true, "markdown": true, "mathematica": true, "matlab": true, "ocaml": true, "pascal": true, "powershell": true, "prolog": true, "protocolbuffer": true, "r": true, "racket": true, "restructuredtext": true, "rmarkdown": true, "sas": true, "scala": true, "scheme": true, "smalltalk": true, "solidity": true, "sparql": true, "sql": true, "stan": true, "standardml": true, "stata": true, "systemverilog": true, "tcl": true, "tcsh": true, "tex": true, "thrift": true, "verilog": true, "visualbasic": true, "xslt": true, "yacc": true, "yaml": true, "zig": true, "jupyter": true, "xml": true}, "description": "%configuration.suggestion.langSuggestion.description%"}, "baidu.comate.inlineSuggestionMode": {"type": "string", "enum": ["extremeFast", "fast", "balance", "accurate", "extremeAccurate"], "enumItemLabels": ["%configuration.suggestion.inlineSuggestionMode.item.extremeFast%", "%configuration.suggestion.inlineSuggestionMode.item.fast%", "%configuration.suggestion.inlineSuggestionMode.item.balance%", "%configuration.suggestion.inlineSuggestionMode.item.accurate%", "%configuration.suggestion.inlineSuggestionMode.item.extremeAccurate%"], "description": "%configuration.suggestion.inlineSuggestionMode.description%", "default": "extremeAccurate"}, "baidu.comate.linePreferMode": {"type": "string", "enum": ["auto", "singleLine", "multiLine"], "enumItemLabels": ["%configuration.suggestion.linePreferMode.item.auto%", "%configuration.suggestion.linePreferMode.item.single%", "%configuration.suggestion.linePreferMode.item.multi%"], "description": "%configuration.suggestion.linePreferMode.description%", "default": "auto"}}}, {"title": "%configuration.codeLens.title%", "properties": {"baidu.comate.enableCodelens": {"type": "object", "default": {"enableInlineUnitTest": true, "enableInlineExplain": true, "enableInlineDocstring": true, "enableInlineSplit": true, "enableInlineComment": true, "enableInlineOptimize": true, "enableInlineLog": true}, "properties": {"enableInlineDocstring": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineDocstring.description%"}, "enableInlineComment": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineComment.description%"}, "enableInlineUnitTest": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineUnitTest.description%"}, "enableInlineSplit": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineSplit.description%"}, "enableInlineOptimize": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineOptimize.description%"}, "enableInlineExplain": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineExplain.description%"}, "enableInlineLog": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineLog.description%"}}, "additionalProperties": false, "description": "%configuration.codeLens.enableCodelens.description%"}, "baidu.comate.codelensDisplayMode": {"type": "string", "enum": ["expand", "collapse"], "enumItemLabels": ["%configuration.codeLens.codelensDisplayMode.item.expand%", "%configuration.codeLens.codelensDisplayMode.item.collapse%"], "description": "%configuration.codeLens.codelensDisplayMode.description%", "default": "expand"}, "baidu.comate.docstringOutputPosition": {"type": "string", "enum": ["sidebar", "editor"], "enumItemLabels": ["%configuration.codeLens.docstringOutputPosition.item.sidebar%", "%configuration.codeLens.docstringOutputPosition.item.editor%"], "description": "%configuration.codeLens.docstringOutputPosition.description%", "default": "sidebar"}}}, {"title": "%configuration.unitTest.title%", "properties": {"baidu.comate.unitTestFrameworkForJava": {"type": "string", "enum": ["junit4", "junit5", "auto"], "enumDescriptions": ["JUnit4", "JUnit5", "%configuration.unitTest.framework.item.auto%"], "description": "%configuration.unitTest.unitTestFrameworkForJava.description%", "default": "auto"}, "baidu.comate.unitTestMockForJava": {"type": "string", "enum": ["<PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "auto", "off"], "enumDescriptions": ["<PERSON><PERSON><PERSON>", "JMockit", "%configuration.unitTest.mock.item.auto%", "%configuration.unitTest.mock.item.off%"], "description": "%configuration.unitTest.unitTestMockForJava.description%", "default": "auto"}, "baidu.comate.unitTestFrameworkForGo": {"type": "string", "enum": ["gotests", "auto"], "enumDescriptions": ["gotests", "%configuration.unitTest.framework.item.auto%"], "description": "%configuration.unitTest.unitTestFrameworkForGo.description%", "default": "auto"}, "baidu.comate.unitTestMockForGo": {"type": "string", "enum": ["gomock", "monkey", "sqlmock", "httptest", "auto", "off"], "enumDescriptions": ["GoMock", "Monkey", "Sqlmock", "httptest", "%configuration.unitTest.mock.item.auto%", "%configuration.unitTest.mock.item.off%"], "description": "%configuration.unitTest.unitTestMockForGo.description%", "default": "auto"}, "baidu.comate.unitTestFrameworkForC/C++": {"type": "string", "enum": ["gtest", "auto"], "enumDescriptions": ["GoogleTest", "%configuration.unitTest.framework.item.auto%"], "description": "%configuration.unitTest.unitTestFrameworkForC/C++.description%", "default": "auto"}, "baidu.comate.unitTestMockForC/C++": {"type": "string", "enum": ["gmock", "auto", "off"], "enumDescriptions": ["Gmock", "%configuration.unitTest.mock.item.auto%", "%configuration.unitTest.mock.item.off%"], "description": "%configuration.unitTest.unitTestMockForC/C++.description%", "default": "auto"}, "baidu.comate.unitTestFrameworkForJs/Ts": {"type": "string", "enum": ["jest", "mocha", "auto"], "enumDescriptions": ["Jest", "<PERSON><PERSON>", "%configuration.unitTest.framework.item.auto%"], "description": "%configuration.unitTest.unitTestFrameworkForJs/Ts.description%", "default": "auto"}, "baidu.comate.unitTestMockForJs/Ts": {"type": "string", "enum": ["auto", "off"], "enumDescriptions": ["%configuration.unitTest.mock.item.auto%", "%configuration.unitTest.mock.item.off%"], "description": "%configuration.unitTest.unitTestMockForJs/Ts.description%", "default": "auto"}, "baidu.comate.unitTestFrameworkForPython": {"type": "string", "enum": ["pytest", "unittest", "auto"], "enumDescriptions": ["<PERSON><PERSON><PERSON>", "Unittest", "%configuration.unitTest.framework.item.auto%"], "description": "%configuration.unitTest.unitTestFrameworkForPython.description%", "default": "auto"}, "baidu.comate.unitTestMockForPython": {"type": "string", "enum": ["auto", "off"], "enumDescriptions": ["%configuration.unitTest.mock.item.auto%", "%configuration.unitTest.mock.item.off%"], "description": "%configuration.unitTest.unitTestMockForPython.description%", "default": "auto"}}}], "icons": {"comate-logo": {"description": "Baidu Comate icon", "default": {"fontPath": "assets/comate.woff", "fontCharacter": "\\0041"}}, "comate-logo-disabled": {"description": "Baidu Comate icon", "default": {"fontPath": "assets/comate.woff", "fontCharacter": "\\0042"}}, "comate-logo-mini": {"description": "Baidu Comate icon", "default": {"fontPath": "assets/comateMini.woff", "fontCharacter": "\\0041"}}}, "iconFonts": [{"id": "comate-font", "src": [{"path": "assets/comate.woff", "format": "woff"}]}], "colors": [{"id": "baidu.comate.colors.inlineDiff.insertedTextBackground", "description": "Color for inline diff inserted text background", "defaults": {"dark": "#4e5a34", "light": "#e9f2d2"}}, {"id": "baidu.comate.colors.inlineDiff.removedTextBackground", "description": "Color for inline diff removed text background", "defaults": {"dark": "#692621", "light": "#fbd6d3"}}, {"id": "baidu.comate.colors.inlineDiff.insertedTextForeground", "description": "Color for inline diff inserted text foreground", "defaults": {"dark": "#ffffff56", "light": "#00000077", "highContrast": "#ffffff", "highContrastLight": "#292929"}}]}}