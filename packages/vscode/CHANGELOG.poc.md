# Change Log

版本：v2.3.0（2024-04-29）
- 💡 支持在 Vue 语言文件中的 JS/TS 代码中使用函数的快捷按钮。
- 💡 新增了对 JS/TS 语言中箭头函数的快捷按钮支持，让您的编程过程更加高效、流畅。
- 🛠️ 修复其他已知问题，提升用户体验

版本：v2.2.0（2024-03-31）
- 💡 对话区代码操作现在支持新建文件和插入到终端，代码采纳过程更加顺畅。
- 🌈 对停止生成功能的交互进行了优化，使其变得更加容易操作。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.1.0（2024-03-20）
- 💡 现在，我们支持在消息流式输出过程中对代码进行操作，可以更快的采纳或复制代码。
- 🌈 对消息的渲染性能进行了优化，现在渲染长消息的速度提升了4倍，有效地解决了滚动卡顿的问题。
- 🌈 对采纳操作和Tab键的交互逻辑进行了智能化处理，让您在使用过程中享受到更流畅的用户体验。
- 🌈 我们对代码库相关检索逻辑进行了深度优化，提升了检索效率。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.0.0（2024-03-01）
- 💡 新增对XML语言的智能补全提示，让您在编写XML文件时也能得到便利的帮助。
- 💡 现在，当我们进行多行代码推荐时，您可以选择逐行采纳，让您根据需要更精准地把握每一行代码。
- 🌈 我们对对话区的样式进行了全新改版，现在的页面信息呈现更加清晰，界面更加清爽。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v1.14.1（2024-02-02）
- 对话区顶部新增“设置”入口，便于您更加方便地进行配置
- 我们将文案“自然语言生成代码”更改为“开始对话”，使其更容易理解
- 我们增加了消息的前置判断，防止在未输入有效内容时发送了消息
- 对消息的渲染样式进行了优化，以呈现更好的视觉效果
- 修复其他已知问题

版本：v1.13.11（2024-01-19）
- 优化代码生成异常时的提示信息
- 修复部分 C++ 语言的文件不显示函数功能按钮的问题
- 修复侧边栏流式输出问答偶现输出中断的问题

版本：v1.13.1（2024-01-02）
- 新增数据看板，助力企业研发效率提升
- 购买会员权益后可直接扫码登录使用，无需配置 License
- 优化代码解释功能的上下文获取策略，提升解释准确率

版本：v1.13.0（2023-12-19）
- 支持百度账号认证，扫码快速登录
- Jupyter Notebook 文档支持代码续写功能
- 优化 Comate 推荐和 IDE 自带推荐框冲突的问题
- 修复链接远端机器时不显示函数功能按钮的问题

版本：v1.12.1（2023-12-05）
- 优化侧边栏流式输出问答时，自动向下滑动的体验问题
- 优化打开侧边栏后输入框自动聚焦的问题，提升问答体验
- 修复函数注释结果的行首空格问题
- 修复侧边栏没有历史消息时仍可以清空消息的问题
- 修复获取默认用户名失败时插件激活失败的问题

版本：v1.2.0（2023-11-21）
- 增加侧边栏会话中查看生成代码变更的功能
- 增加生成结果自定义反馈的功能
- 增加敏感信息过滤的功能
- 支持侧边栏的流式输出，内容输出更流畅
- 支持行中推荐

版本：v1.1.0（2023-11-07）
- 优化存在推荐内容时删除字符不流畅的体验问题
- 优化取消本次推荐命令并默认绑定快捷键的设置问题
- 修复单测生成不支持 C++ 语言的问题
- 修复获取不到设备 ID 的问题
- 修复侧边栏生成结果部分语言没有语法高亮的问题

版本：v1.0.0（2023-10-24）
- 行级/函数级实时续写：单行续写、多行续写、注释生成代码（函数注释）
- 代码生成：自然语言描述生成代码、增强生成
- 单元测试生成：单测生成、接口文档生成测试代码
- 代码优化：长函数拆分代码重构、自然语言优化代码
- 代码注释生成：函数注释、行间注释
- 智能 CLI
- 技术问答