export type ComposerFileAction = 'create' | 'edit' | 'delete' | 'explain' | 'preview';

export enum ComposerCodeStatus {
    UNREADY = 'UNREADY',
    PROCESSING = 'PROCESSING',
    DONE = 'DONE',
    CANCELLED = 'CANCELLED',
}

export enum AcceptState {
    UNTOUCHED = 0,
    ACCEPT = 1,
    REJECT = 2,
}

export type IllegalFileType = 'image' | 'video' | 'audio';
export interface IllegalTextExtDefinition {
    type: IllegalFileType;
    extensions: string[];
}
