import {merge} from 'lodash';
import {Platform} from './types';

const DEFAULT: Platform = {
    brand: 'Baidu Comate',
    homePage: 'https://comate.baidu.com',
    helpDocUrl: 'https://comate.baidu.com/readme',
    feedbackUrl: 'https://comate.baidu.com/?loadFeedback=true',
};

let platform: Platform = DEFAULT;

const use = (values: Partial<Platform>) => {
    platform = merge(platform, values);
};

const resolve = (key: keyof Platform) => {
    return platform[key];
};

export default {
    use,
    resolve,
};
