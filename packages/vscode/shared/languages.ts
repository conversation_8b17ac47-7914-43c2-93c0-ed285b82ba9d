// 将 vscode 的语言 ID 映射到 react syntax highlighter 的语言 ID，这里是手动维护的 id 不同的语言
const VSCODE_LANGUAGE_ID_TO_HIGHLIGHTER_LANGUAGE: Record<string, string> = {
    bat: 'batch',
    dockerfile: 'docker',
    dockercompose: 'docker',
    'cuda-cpp': 'cpp',
    'git-commit': 'git',
    'git-rebase': 'git',
    html: 'cshtml',
    javascriptreact: 'jsx',
    jsonc: 'json5',
    'objective-c': 'objectivec',
    'objective-cpp': 'objectivec',
    perl6: 'perl',
    plaintext: 'text',
    jade: 'pug',
    razor: 'cshtml',
    shellscript: 'bash',
    typescriptreact: 'tsx',
    arkts: 'ts',
    tex: 'latex',
    vb: 'visualBasic',
    vue: 'javascript',
    xml: 'cshtml',
};

export const mapToHighlighterLanguage = (vscodeLanguageId: string | undefined) => {
    if (!vscodeLanguageId) {
        return vscodeLanguageId;
    }
    return VSCODE_LANGUAGE_ID_TO_HIGHLIGHTER_LANGUAGE[vscodeLanguageId] ?? vscodeLanguageId;
};
