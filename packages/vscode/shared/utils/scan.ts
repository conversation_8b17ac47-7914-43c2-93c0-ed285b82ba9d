import {
    Accept<PERSON>ethod,
    <PERSON><PERSON>hunk,
    DrawElement,
    Replace<PERSON>ontentAcceptMethod,
    SimpleCode<PERSON>lock,
    TaskProgressChunk,
} from '@comate/plugin-shared-internals';

interface ExtendedSimpleCodeBlock extends SimpleCodeBlock {
    id: string;
    diffLines: {add: number[], remove: number[]};
}

export const isCodeBlock = (element: DrawElement): element is ExtendedSimpleCodeBlock => {
    return typeof element === 'object' && element.type === 'code-block';
};

export const isReplaceMethod = (block: AcceptMethod): block is ReplaceContentAcceptMethod => {
    return block.method === 'replaceContent';
};

const isDrawElement = (element: TaskProgressChunk): element is DrawChunk => {
    return typeof element === 'object';
};

export const findCodeBlocks = (chunk: TaskProgressChunk) => {
    if (Array.isArray(chunk)) {
        // @ts-expect-error
        return chunk.filter(isCodeBlock);
    }
    else if (isDrawElement(chunk)) {
        if (Array.isArray(chunk.content)) {
            // @ts-expect-error
            return chunk.content.filter(isCodeBlock);
        }
    }
    return [];
};
