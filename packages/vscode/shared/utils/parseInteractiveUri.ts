interface InteractiveUriComponents {
    protocol: string;
    agent: string;
    slash?: string;
    query: string;
    params: Record<string, string>;
}
/**
 * chat 协议，内容会当做用户输入使用，支持 agent 和 slash 参数
 */
export function isInteractiveChatUri(uriString: string) {
    return uriString.startsWith('chat://');
}

/**
 * 解析交互式 URI，protocol://agent[:slash]@query[?paramString]
 */
export function parseInteractiveUri(uriString: string): InteractiveUriComponents {
    // 正则表达式匹配模式
    const pattern = /^(\w+):\/\/([^:@]+)(?::([^@]+))?@([^?]+)(\?.*)?$/;

    // 尝试匹配
    const match = pattern.exec(uriString);

    if (!match) {
        throw new Error('Invalid interactive URI format');
    }

    // 解构匹配结果
    const [, protocol, agent, slash, query, paramString] = match;

    // 解析参数
    const params: Record<string, string> = {};
    if (paramString) {
        const searchParams = new URLSearchParams(paramString.slice(1));
        searchParams.forEach((value, key) => {
            params[key] = value;
        });
    }

    // 构建返回对象
    const result: InteractiveUriComponents = {
        protocol,
        agent,
        query: decodeURIComponent(query),
        params,
    };

    // 只有当 slash 存在时才添加到结果中
    if (slash !== undefined) {
        result.slash = slash;
    }

    return result;
}
