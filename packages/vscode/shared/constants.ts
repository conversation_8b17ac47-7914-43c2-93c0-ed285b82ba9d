export const LANGUAGE_DISPLAY_NAME_MAP: Record<string, string> = {
    cpp: 'C/C++',
    css: 'CSS',
    go: 'Go',
    html: 'HTML',
    java: 'Java',
    javascript: 'JavaScript',
    js: 'JavaScript',
    less: 'Less',
    perl: 'Perl',
    php: 'PHP',
    python: 'Python',
    ruby: 'Ruby',
    shell: 'Shell',
    swift: 'Swift',
    typescript: 'TypeScript',
    pug: 'Pug',
    Sass: 'Sass',
    scss: 'Scss',
    vue: 'Vue',
    stylus: 'Stylus',
    vhdl: 'VHDL',
    rust: 'Rust',
    mermaid: 'Mermaid',
    graphql: 'GraphQL',
    san: 'San',
    swan: 'swan',
    kotlin: 'Kotlin',
    objectivec: 'Objective-C',
    lua: 'Lua',
};

// 用户输入以下的值可以搜索到，在系统里不是通过这个值来判断的，比如functionComment，系统里是docstring
export const API_DOC = 'apiDoc';
export const FUNCTION_COMMENT = 'functionComment';
export const INLINE_COMMENT = 'inlineComment';
export const INLINE_LOG = 'inlineLog';
export const CODE_EXPLAIN = 'explain';
export const OPTIMIZE = 'optimize';
export const FUNCTION_SPLIT = 'functionSplit';
export const AUTO_TEST = 'autoTest';
export const IAPI = 'iapi';
export const UT = 'ut';

// slash 标识，本标识的具体值是与RD同学约定的，用于AutoWork服务交互，请勿随意修改
export enum SlashType {
    COMPOSER = 'Composer',
    ASK_V2 = '智能问答-V2',
    AUTO_DEBUG = 'Auto Debug',
    AUTO_TEST = '智能测试',
    IAPI = '生成API调用代码',
}

// 直接给个随机字符串，迷惑一下逆向的人
export const TEXT_CHANGE_DETAILS = 'zxkjqpbfmryhtwvnegolsdi';
export const TEXT_CHANGE_DETAILS_ACCEPT = 'hfnxaqlgbpwutzesmcrijok';
export const WATI_FOR_ACCEPT_CODE_FROM_CHAT = 'evrfcjnzumqpxtdwkoaihbly';
export const TEXT_CHANGE_EVENT_TYPE = 'tlbsmoyrqpnwxazhfgcjeikd';
