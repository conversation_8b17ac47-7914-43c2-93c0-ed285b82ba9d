'use strict';

const path = require('path');
const {IgnorePlugin} = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const {DefinePlugin} = require('webpack');
const WebpackObfuscator = require('webpack-obfuscator');

const platform = process.env.PLATFORM ?? 'internal';
const enterpriseVersion = process.env.ENTERPRISE_VERSION ?? '';
const webviewConsumer = process.env.WEBVIEW_CONSUMER ?? 'vscode';
const isDevelopment = process.env.NODE_ENV === 'development';
const environment = process.env.ENVIRONMENT ?? 'production';
const isInternal = platform === 'internal';

const extensionEntryPoint = isInternal
    ? './src/extension.ts'
    : `./src/extension.${platform}.ts`;


const extensionConfig = {
    name: 'extension',
    target: 'node',
    entry: {
        extension: extensionEntryPoint,
    },
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: 'extension.js',
        libraryTarget: 'commonjs2',
        devtoolModuleFilenameTemplate: '../[resource-path]',
    },
    devtool: 'source-map',
    externals: {
        vscode: 'commonjs vscode',
        bufferutil: 'bufferutil',
        'utf-8-validate': 'utf-8-validate',
    },
    resolve: {
        extensions: ['.ts', '.tsx', '.js', '.jsx'],
        symlinks: true,
        alias: {
            '@shared': path.resolve(__dirname, 'shared'),
            '@': path.resolve(__dirname, 'src'),
        },
        // 引入 vscode-languageserver-types 时 webpack 打包会有 warning，参考这里暂时修复，还没仔细看：https://github.com/microsoft/vscode-languageserver-node/issues/1355#issuecomment-1807675786
        conditionNames: ['import', 'require'],
    },
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                use: 'ts-loader',
                exclude: /node_modules/,
            },
            {
                test: /\.prompt$/,
                use: 'raw-loader',
                exclude: /node_modules/,
            },
        ],
    },
    plugins: [
        ...((isDevelopment || isInternal) ? [] : [
            new WebpackObfuscator({
                target: 'node',
            }),
        ]),
        new DefinePlugin({
            '$features.PLATFORM': JSON.stringify(platform),
            '$features.WEBVIEW_CONSUMER': JSON.stringify(webviewConsumer),
            '$features.ENVIRONMENT': JSON.stringify(environment),
            '$features.ENTERPRISE_VERSION': JSON.stringify(enterpriseVersion),
        }),
        new IgnorePlugin({
            contextRegExp: /adm-zip/,
            resourceRegExp: /original-fs/,
        }),
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: 'child-process',
                    to: 'child-process',
                },
            ],
        }),
    ],
};

module.exports = [
    extensionConfig,
];
