# Change Log

版本：v3.3.0（2025-01-03）
- 🌈 优化全链路的性能，带来更流畅的交互体验。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v3.2.0（2024-12-30）
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v3.2.0 (2024-12-30)
- 🛠️ Fixed other known issues to enhance user experience.

版本：v3.1.0（2024-12-18）
- 🎉 对话支持可选更快模型、更强模型，基础模型全新升级到128K。
- 🎉 安全智能体新增支持指定单文件、目录扫描。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v3.1.0（2024-12-18）
- 🎉 Conversations now support optional faster and more powerful models, with the base model upgraded to 128K.
- 🎉 The security agent now supports specifying single files and directory scanning.
- 🛠️ Fixed other known issues and improved user experience.

版本：v3.0.0（2024-11-12）
- 🌈 开发全流程升级，发布编码智能体、Debug智能体、单测智能体、安全智能体、全栈编程智能体

Version: v3.0.0 (2024-11-12)
- 🌈 Complete development workflow upgrade, introducing Development Agent, Debug Agent, Unit Test Agent, Security Agent, and Full-Stack Programming Agent.

版本：v2.17.0（2024-10-28）
- 🛠️ 修复其他已知问题，提升用户体验。

Version：v2.17.0（2024-10-28）
- 🛠️ Fixed other known issues and improved user experience.

版本：v2.16.0（2024-10-14）
- 🌈 优化首页推荐问题内容。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.16.0 (2024-10-14)
- 🌈 Optimized recommended question content on the homepage.
- 🛠️ Fixed other known issues and improved user experience.

版本：v2.15.0（2024-09-30）
- 🌈 行间对话交互优化：更佳的输出效果。
- 🌈 Paddle 插件体验优化，新增指令代码转换的功能指引更清晰。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.15.0 (2024-09-30)
- 🌈 Improved inline dialogue interaction: Better output results.
- 🌈 Enhanced Paddle plugin experience, with clearer guidance for new instruction code conversion functionality.
- 🛠️ Fixed other known issues and improved user experience.

版本：v2.14.0（2024-09-20）
- 🎉 从终端报错跳转到对应代码行时提供分析与修复的快捷按钮，直面问题现场。
- 🎉 Paddle 插件支持根据目录将文件中 Pytorch 框架转化为飞桨框架。
- 🎉 提问后增加关联问题建议，开拓提问角度和思路，让提问更深入和全面。
- 🌈 行间对话交互优化：更佳的流式输出效果，更准确的触发范围识别。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.14.0 (2024-09-20)
- 🎉 Added a CodeLens to provide fix suggestions for the line of code referenced in terminal error stacks.
- 🎉 Added support for translating code written with PyTorch to the PaddlePaddle framework in the Paddle plugin.
- 🎉 Added follow-up question suggestions after initial queries, broadening the scope and perspective of inquiries for more comprehensive and in-depth understanding.
- 🌈 Improved the inline chat experience: smoother edit streaming in your editor and more accurate target code selection.
- 🛠️ Fixed other known issues to enhance user experience.

版本：v2.13.0（2024-09-03）
- 🎉 行间对话生成的代码支持流式输出了，更快看到编辑的效果，提升协同效率。
- 🎉 支持手动触发代码库索引构建，当回复未能参考代码库最新的代码时，可以手动触发更新。
- 🌈 对话侧边栏迎来样式全面升级，减少干扰元素，功能指示更清晰，便于专注在内容上。
- 🌈 优化对话回复中的 Mermaid 流程图样式，提升阅读体验。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.13.0 (2024-09-03)
- 🎉 Code generated in inline conversations now supports streaming output, allowing you to see editing effects faster and improving collaboration efficiency.
- 🎉 Added support for manually triggering codebase index building. When responses don't reference the latest code in the codebase, you can manually trigger an update.
- 🌈 The conversation sidebar has received a comprehensive style upgrade, reducing distracting elements and providing clearer function indicators for better focus on content.
- 🌈 Improved the style of Mermaid flowcharts in conversation replies, enhancing the reading experience.
- 🛠️ Fixed other known issues to improve user experience.


版本：v2.12.0（2024-08-19）
- 🎉 Comate 行间对话更新了，支持对划选的代码进行提问或编辑，清晰展示代码变更，快使用快捷键 `cmd+I`(macOS) / `ctrl+I`(Windows) 体验吧！
- 🎉 新增【智能生成 commit message】
- 🎉 新增「添加日志」按钮，一键为函数内部增加打印日志，便于 debug。
- 🎉 光标悬浮在任意插件时，浮窗显示更详细的插件信息和快捷提问按钮，插件能力更清晰。
- 🎉 光标悬浮在不同知识类型时会浮窗显示详细信息、并提供了快捷示例，激发灵感。
- 🌈 对话消息过多后自动隐藏历史消息，滚动到顶部后再加载，减小渲染开销。
- 🌈 提升首次打开对话侧边栏时的显示速度，提问 0 距离。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.12.0 (2024-08-19)
- 🎉 Comate inline dialogue has been updated, supporting querying or editing selected code, clearly displaying code changes. Try it with the shortcut cmd+I (macOS) / ctrl+I (Windows)!
- 🎉 Added "Smart commit message generation"
- 🎉 Added "Add log" button, allowing one-click addition of print logs inside functions for easier debugging.
- 🎉 When hovering over any plugin, a floating window displays more detailed plugin information and quick question buttons, making plugin capabilities clearer.
- 🎉 When hovering over different knowledge types, a floating window displays detailed information and provides quick examples, inspiring creativity.
- 🌈 Automatically hides historical messages when there are too many dialogue messages, loading them again when scrolling to the top, reducing rendering overhead.
- 🌈 Improved display speed when opening the dialogue sidebar for the first time, enabling zero-delay questioning.
- 🛠️ Fixed other known issues and improved user experience.

版本：v2.11.0（2024-07-31）
- 🌈 新增【续写状态】展示，实时展示生成状态。
- 🌈 优化多轮对话体验，生成更连贯的上下文。
- 🌈 Diff 视图状态下支持一键采纳。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.11.0 (2024-07-31)
- 🌈 Added the display of "Continuation Status" to showcase the real-time generation status.
- 🌈 Enhanced the multi-turn dialogue experience for more coherent context generation.
- 🌈 Introduced one-click adoption support in Diff view mode.
- 🛠️ Fixed other known issues to improve user experience.

版本：v2.10.0（2024-07-17）
- 🌈 提升对话以及续写的速度。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.10.0 (2024-07-17)
- 🌈 Improved the speed of conversations and continuations.
- 🛠️ Fixed other known issues to enhance user experience.

版本：v2.9.0（2024-07-01）
- 🔍 使用能力更精准快捷，现在它不仅支持传统的关键字搜索，还增加了拼音搜索功能。
- 🌈 优化官方知识集展示形态。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.9.0 (2024-07-01)
- 🔍 Enhanced search capabilities are now more precise and efficient, supporting not only traditional keyword search but also pinyin search.
- 🌈 Optimize the presentation form of the official knowledge base
- 🛠️ Fixed other known issues to enhance user experience.

版本：v2.7.0（2024-06-19）
- 🎉 新增【智能Debug】功能。当编译运行报错时，通过快捷按钮或划选错误日志，一键定位错误并给出修复代码。
- 🎉 新增【代码安全】功能，企业版用户在管理员开启该功能后可在侧边栏中使用。插件会扫描代码中的安全漏洞，并提供修改建议。用户采纳后可一键修复安全漏洞。
- 🎉 支持混合云服务部署方案。
- 🌈 优化Go语言续写推荐效果。
- 🌈 优化用户个人账号信息的展示。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.7.0（2024-06-19）
- 🎉 New AI Debug Feature: Easily locate and fix errors with a single click using shortcut buttons or by selecting error logs when compilation or runtime errors occur.
- 🎉 New Code Security Feature: Available in the sidebar for enterprise users once enabled by the administrator. This plugin scans for security vulnerabilities in the code and provides suggestions for fixes. Users can apply these suggestions and resolve security issues with one click.
- 🎉 Support for Hybrid Cloud Service Deployment.
- 🌈 Enhanced Go Language Inline Suggestion.
- 🌈 Improved Display of Personal Account Information.
- 🛠️ Fixed other known issues, improving user experience.

版本：v2.6.0（2024-06-04）
- 🌈 修改侧边栏启动样式：新增使用引导，帮助用户快速熟悉侧边栏功能。
- 🌈 续写推荐方式可配置：允许用户在设置中自定义续写推荐的方式，选择单行或多行优先。
- 🌈 注释生成代码交互优化：注释生成代码时，增加实时加载状态提示。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.6.0 (2024-06-04)
- 🌈 Improved the on-boarding experience of the chat interface: Added a few examples on the welcome page to help you quickly explore our latest features.
- 🌈 Customize auto-completion setting: Allow users to customize how continuation recommendations are displayed in settings, choosing between single-line or multi-line prioritization.
- 🌈 Optimize comment-to-code generation interaction: Provide real-time loading status when generating.
- 🛠️ Fixed other known issues, improving user experience.

版本：v2.5.0（2024-05-21）
- 🎉 搜索功能升级：输入「#」后选择「网络搜索」，可以轻松在侧边栏中进行网络搜索，获取更多相关信息。
- 💡 历史提问便捷切换：通过键盘上的上下键，可以在侧边栏中快速切换历史提问内容，优化使用体验。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.5.0 (2024-05-21)
- 🎉 Improved Search: You can now search the web by typing “#” and selecting “Web Search” in the sidebar.
- 💡 Quickly Switch Historical Questions: Use the up and down arrow keys to switch between previous questions in the sidebar.
- 🛠️ Fixed Other Known Issues: We’ve resolved some known problems to enhance your experience.

版本：v2.4.0（2024-04-30）
- 💡 在 Vue 文件中，JS/TS 代码中的函数支持通过快捷按钮，快速生成注释、生成单测等能力。
- 💡 新增了对 JS/TS 代码中箭头函数的快捷按钮的支持。
- 🌈 在侧边栏中新增唤起「快捷指令」、「插件」和「知识」的按钮。
- 🌈 对调优建议指令进行了针对性优化，效果提升明显。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.4.0 (2024-04-30)
- 💡 In Vue files, functions within JS/TS code now support quick generation of comments, unit tests etc through shortcut buttons.
- 💡 Added support for shortcut buttons in JS/TS code for arrow functions.
- 🌈 New buttons for invoking "Commands", "Agents", and "Context" have been added to the sidebar.
- 🌈 The optimization suggestion command has been specifically optimized, resulting in a significant improvement.
- 🛠️ Fixed other known issues, improving user experience.

版本：v2.3.0（2024-04-17）
- 🎉 对英文版做了进一步的支持，为全球用户提供智能编码服务：英文版个人官网、用户手册、更新日志上线；插件内支持英文使用 ；AutoWork高级生成能力支持英文对话
- 🎉 在Comate中可以关联网页了，支持更个性化的问答：你可以通过在侧边栏中通过「#」关联你的URL链接，Comate可以帮你深度解读信息，并生成更贴合你需求背景的回答
- 🎉 发布「工具箱」插件，支持Base64编解码、JSON和YAML互转等能力：你可以在侧边栏中通过「@」唤起「工具箱」插件，或者通过「/」直接调起快捷指令
- 🛠️ 修复其他已知问题，提升用户体验

Version: v2.3.0 (2024-04-17)
- 🎉 Further support has been added for the English version, providing intelligent coding services for global users: The English version of the personal profile, quick start, and change log are now available. English language is supported within the extension, and AutoWork's advanced generation capability now supports English chat.
- 🎉 Now you can now link web pages, allowing for more personalized question and answer experiences in Baidu Comate: By using "#" in the sidebar, you can associate your URL links, enabling Comate to deeply interpret information and generate answers that are more tailored to your specific needs and background.
- 🎉 We've released the "Toolbox" plugin, which supports Base64 encoding/decoding, JSON and YAML conversion, and more: You can summon the "Toolbox" plugin in the chat using "@" or directly activate shortcut commands using "/".
- 🛠️ Fixed other known issues, improving user experience.

版本：v2.2.0（2024-03-31）
- 🎉 现在，我们的应用已经支持英文，为更多全球用户提供便利。
- 💡 对话区代码操作现在支持新建文件和插入到终端，代码采纳过程更加顺畅。
- 💡 对话框现在支持 # 引用多个文件或者文件来进行提问，已获得更准确的回答。
- 🌈 对停止生成功能的交互进行了优化，使其变得更加容易操作。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.2.0 (2024-03-31)
- 🎉 Our application now supports English for more global users.
- 💡 Chat area now supports creating new files and inserting into terminals, making the code adoption process smoother.
- 💡 Chat now support referencing multiple files or directories with # for more accurate answers.
- 🌈 Optimized the interaction for stopping generation, making it easier to operate.
- 🛠️ Fixed other known issues, improving user experience.

版本：v2.1.0（2024-03-20）
- 🎉 新增飞桨PaddlePaddle官方插件，支持代码生成，智能问答。
- 🎉 新增百度智能小程序官方插件，支持代码生成，智能问答。
- 💡 现在，我们支持在消息流式输出过程中对代码进行操作，可以更快的采纳或复制代码。
- 🌈 对消息的渲染性能进行了优化，现在渲染长消息的速度提升了4倍，有效地解决了滚动卡顿的问题。
- 🌈 对采纳操作和Tab键的交互逻辑进行了智能化处理，让您在使用过程中享受到更流畅的用户体验。
- 🌈 我们对代码库相关检索逻辑进行了深度优化，提升了检索效率。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.1.0 (2024-03-20)
- 🎉 Added official plugin PaddlePaddle, supporting code generation and intelligent Q&A.
- 🎉 Added official plugin 百度智能小程序, supporting code generation and intelligent Q&A.
- 💡 Now supports code manipulation during message stream output for faster adoption or copying of code.
- 🌈 Optimized rendering performance of messages, resulting in a 4x speed increase in rendering long messages, effectively addressing scroll lag issues.
- 🌈 Intelligently handled the interaction logic for adoption actions and Tab key, providing a smoother user experience.
- 🌈 Deeply optimized code library related search logic, improving retrieval efficiency.
- 🛠️ Fixed other known issues, enhancing user experience.

版本：v2.0.0（2024-03-01）
- 🎉 我们正式发布了Comate+开放平台，现支持通过插件扩展Comate能力，提供强大的开放性与定制性，让你的编程现场从未有过的强大。
- 🎉 ──发布多个插件，通过Git插件连接Github、Gitee、GitLab等多个常用工具，为你智能生成提交信息、创建Issue，快来通过【@】【/】【#】在Comate侧边栏中尝试最新的智能化开发吧。
- 🎉 ──上线插件配置中心与知识中心。Comate的能力可以通过配置呈现更贴合你需求的效果；你还可以通过知识中心上传私域知识，让Comate更懂你，生成更个性化、更准确的内容与代码。
- 🎉 ──通过丰富的开放基座，企业可以通过Comate的插件形式将自身服务、能力、知识等连接到编程现场，增强推荐效果、提供个性化能力，为组织量身打造自有智能代码助手。
- 🎉 上线了AutoWork，它能深度解读你的代码库，洞悉必要的背景知识，为你生成量身定制的代码。
- 💡 新增对XML语言的智能补全提示，让您在编写XML文件时也能得到便利的帮助。
- 💡 现在，当我们进行多行代码推荐时，您可以选择逐行采纳，让您根据需要更精准地把握每一行代码。
- 💡 新增了插件内的小调研功能，让我们可以及时了解您对各项功能的满意度，便于我们进行针对性的优化。
- 💡 导航栏新增了意见反馈入口，我们随时关注并解决用户遇到的问题。
- 🌈 我们对对话区的样式进行了全新改版，现在的页面信息呈现更加清晰，界面更加清爽。
- 🌈 代码检索策略进行了优化，提高您查找代码的速度和准确性，对检索和生成的界面进行了视觉效果的升级，使用更加方便并拥有更好的视觉感受。
- 🛠️ 修复其他已知问题，提升用户体验。

Version: v2.0.0 (2024-03-01)
- 🎉 Officially launched the ComatePlus platform, now supporting extension of Comate capabilities through plugins, providing powerful openness and customization, making your programming scene more powerful than ever.
- 🎉 Released multiple plugins, such as Git plugins connecting to Github, Gitee, GitLab, etc., intelligently generating commit messages, creating issues. Try out the latest intelligent development through [@] [/] [#] in the Chat panel.
- 🎉 Launched Plugin Configuration Center and Docs Center. Comate's capabilities can be presented more fittingly through configuration; you can also upload domain-specific docs through the Docs Center, making Comate understand you better and generate more personalized, accurate content and code.
- 🎉 Enterprises can connect their own services, capabilities, and knowledge to the programming scene through the rich open base of Comate in the form of plugins, enhancing recommendation effects, providing personalized capabilities, and tailoring intelligent code assistants for organizations.
- 🎉 Launched AutoWork, which deeply interprets your codebase, understands necessary background docs, and generates customized code for you.
- 💡 Added intelligent autocompletion prompts for XML language, providing convenient assistance when writing XML files.
- 💡 Now, when performing multi-line code suggestions, you can choose to accept line by line, allowing you to grasp each line of code more accurately as needed.
- 💡 Added a mini survey feature within plugins to promptly understand your satisfaction with various features, facilitating targeted optimization.
- 💡 Added a feedback entry to the navigation bar, where we constantly monitor and address issues encountered by users.
- 🌈 Completely redesigned the style of the Chat area for clearer page information and a fresher interface.
- 🌈 Optimized code retrieval strategy, improving the speed and accuracy of code lookup, and upgraded the visual effects of retrieval and generation interfaces for easier use and better visual experience.
- 🛠️ Fixed other known issues, enhancing user experience.

版本：v1.14.1（2024-02-02）
- 迎新年，贺新春，龙年主题活动开启，参与活动有机会赢积分、兑京东卡！
- 对话区顶部新增“帮助文档”和“设置”两个入口，便于您更加方便地获取帮助和进行配置
- 对插件的分类和标签进行更新，使您在搜索 Comate 插件时更加轻松
- 我们将文案“自然语言生成代码”更改为“开始对话”，使其更容易理解
- 我们增加了消息的前置判断，防止在未输入有效内容时发送了消息
- 对消息的渲染样式进行了优化，以呈现更好的视觉效果
- 修复其他已知问题

Version: v1.14.1 (2024-02-02)
- Celebrate the new year with the Dragon Year theme event! Engage for an opportunity to earn credit scores or redeem JD cards!
- Added "Help" and "Settings" entries at the top of the Chat area for easier access to help and configuration.
- Updated plugin categorization and tags to make it easier for you to search for Comate plugins.
- Changed the phrase "Natural Language Code Generation" to "Start Chat" for easier understanding.
- Added pre-checking for messages to prevent sending messages when no valid content is entered.
- Optimized message rendering styles for better visual effects.
- Fixed other known issues.

版本：v1.13.11（2024-01-19）
- 优化代码生成异常时的提示信息
- 修复部分 C++ 语言的文件不显示函数功能按钮的问题
- 修复侧边栏流式输出问答偶现输出中断的问题

Version: v1.13.11 (2024-01-19)
- Enhanced error messages for code generation exceptions.
- Fixed the issue where certain C++ language files were not displaying buttons above functions.
- Resolved intermittent interruptions in the Chat panel output.

版本：v1.13.1（2024-01-02）
- 新增数据看板，助力企业研发效率提升
- 购买会员权益后可直接扫码登录使用，无需配置 License
- 优化代码解释功能的上下文获取策略，提升解释准确率

Version: v1.13.1 (2024-01-02)
- Introduced a data dashboard to boost enterprise development efficiency.
- VIP Members can now directly log in via QR code after purchasing membership benefits, eliminating the need for license configuration.
- Optimized context retrieval strategies for code interpretation, enhancing accuracy.

版本：v1.13.0（2023-12-19）
- 支持百度账号认证，扫码快速登录
- Jupyter Notebook 文档支持代码续写功能
- 优化 Comate 推荐和 IDE 自带推荐框冲突的问题
- 修复链接远端机器时不显示函数功能按钮的问题

Version: v1.13.0 (2023-12-19)
- Added support for Baidu account authentication, enabling quick login via QR code scanning.
- Jupyter Notebook documents now support code suggestions.
- Resolved conflicts between Comate suggestions and built-in IDE suggestion boxes.
- Fixed the issue where buttons above functions were not displayed when linking to remote machines.

版本：v1.12.1（2023-12-05）
- 优化侧边栏流式输出问答时，自动向下滑动的体验问题
- 优化打开侧边栏后输入框自动聚焦的问题，提升问答体验
- 修复函数注释结果的行首空格问题
- 修复侧边栏没有历史消息时仍可以清空消息的问题
- 修复获取默认用户名失败时插件激活失败的问题

Version: v1.12.1 (2023-12-05)
- Improved the user experience of automatic downward scrolling during Chat output streaming.
- Enhanced the focus behavior of the input box upon opening the Chat, improving the chat experience.
- Fixed the leading whitespace issue in function comment results.
- Fixed the ability to clear messages in the Chat even when there are no historical messages.
- Resolved the issue of plugin activation failure when the default username retrieval fails.

版本：v1.2.0（2023-11-21）
- 增加侧边栏会话中查看生成代码变更的功能
- 增加生成结果自定义反馈的功能
- 增加敏感信息过滤的功能
- 支持侧边栏的流式输出，内容输出更流畅
- 支持行中推荐

Version: v1.2.0 (2023-11-21)
- Added functionality to view code changes in Chat.
- Introduced customizable feedback for generated results.
- Implemented sensitive code filtering capabilities.
- Enabled smooth streaming output in the Chat, enhancing content delivery.
- Supported suggestions in the middle of line.

版本：v1.1.0（2023-11-07）
- 优化存在推荐内容时删除字符不流畅的体验问题
- 优化取消本次推荐命令并默认绑定快捷键的设置问题
- 修复单测生成不支持 C++ 语言的问题
- 修复获取不到设备 ID 的问题
- 修复侧边栏生成结果部分语言没有语法高亮的问题

Version: v1.1.0 (2023-11-07)
- Improved the user experience of deleting characters when suggestion exist.
- Streamlined the process of canceling suggestions and default shortcut key bindings.
- Fixed the issue where unit test generation did not support the C++ language.
- Resolved the problem of failing to retrieve device IDs.
- Fixed the lack of syntax highlighting for some languages in the Chat results.

版本：v1.0.0（2023-10-24）
- 行级/函数级实时续写：单行续写、多行续写、注释生成代码（函数注释）
- 代码生成：自然语言描述生成代码、增强生成
- 单元测试生成：单测生成、接口文档生成测试代码
- 代码优化：长函数拆分代码重构、自然语言优化代码
- 代码注释生成：函数注释、行间注释
- 智能 CLI
- 技术问答

Version: v1.0.0 (2023-10-24)
- Real-time Suggestions at the line/function level: single-line suggestions, multi-line suggestions, code generation from comments (function comments).
- Code generation: code generation from natural language descriptions, enhanced generation.
- Unit test generation: unit test generation, API documentation generation for test code.
- Code optimization: refactoring of long functions, optimization of code through natural language.
- Docstring generation: function docstring, inline comments.
- Intelligent CLI.
- Technical Q&A.
