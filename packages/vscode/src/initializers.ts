import * as vscode from 'vscode';
import {compare} from 'compare-versions';
import platform from '@shared/platform';
import {Position} from 'vscode-languageclient';
import {ITextDocument} from '@/common/lsp/types';
import {UserService} from './services/UserService';
import {
    CompletionProvider,
    LegacyCompletionProvider,
    ProposalEnabledCompletionProvider,
} from './services/CompletionProvider';
import {VSCodeConfigProvider} from './services/ConfigProvider';
import {iocContainer} from './iocContainer';
import {TreeSitterProvider} from './services/TreeSitterProvider';
import {SWAN_APP_NAME} from './constants';
import consoleLogger from './common/consoleLogger';
import {registerLogLevelQuickPick, trace} from './common/outputChannel';
import {GhostTextUsageGuide} from './services/CompletionProvider/GhostTextUsageGuide';
import {MultiLineCompletionTracker} from './services/CompletionProvider/MultiLineCompletionTracker';
import {CompletionContextProvider} from './services/CompletionContextProvider';
import {LoadingDecorations} from './services/CompletionProvider/LoadingDecorations';
import {LineCommentDecorations} from './services/CompletionProvider/LineCommentDecorations';
import {isInternal} from './utils/features';
import {LogicalEditTracker} from './services/LogicalEditTracker';
import {modifyCode, uploadEditDiff} from './api';
import {IWorkspaceProvider} from './common/lsp/types';
import {AbstractCompletionProvider} from './services/CompletionProvider/AbstractCompletionProvider';
import {buildParams} from './common/Fetcher';
import {LLMBlockRewrite, SimilarLineCursorPrediction} from './services/EditPredictionProvider';
import {VSCodeTextDocument} from './common/lsp/adapters/VSCodeTextDocument';
import {TabStreakController} from './services/TabStreak/Controller';
import {IDiffViewer} from './services/DiffViewer/types';
import {convertToOneBasedRange} from './utils/common';
import {TerminalCommandTracker} from './services/TerminalCommandTracker';
import {ProgrammingContextTracker} from './services/ProgrammingContextTracker';
import {VSCodeDiagnosticTracker} from './services/DiagnosticTracker/VSCodeDiagnosticTracker';
import {CursorMoveEventEmitter} from './services/CursorMoveEventEmitter';
import {IProgrammingContextTracker} from './services/ProgrammingContextTracker/types';
import {InlineCompletionItem} from './services/types';
import {formatForRequest} from './services/ProgrammingContextTracker/utils';
import {ClientGlobalConfigService} from './services/CompletionProvider/ClientGlobalConfigService';
import {TriggerSource} from './api/smartTab';
import {RewriteHandler} from './services/CompletionProvider/types';
import {VSCodeWorkspace} from './common/lsp/adapters/VSCodeWorkspace';
import {CrossFileContextAnalyzer} from './services/CrossFileContextAnalyzer';
import {SymbolLinkResolver} from './services/CrossFileContextAnalyzer/SymbolLinkResolver';
import {SymbolSignatureController} from './services/CrossFileContextAnalyzer/SymbolSignatureReader';
import {LspSymbolSignatureReader} from './services/CrossFileContextAnalyzer/SymbolSignatureReader/lsp';
import {
    CppSymbolSignatureReader,
    JsSymbolSignatureReader,
} from './services/CrossFileContextAnalyzer/SymbolSignatureReader/treeSitter';
import {ISymbolUsageFinder, ILocationLinkResolver} from './services/CrossFileContextAnalyzer/types';
import {TYPES} from './inversify.config';

export function initCompletionProvider(
    context: vscode.ExtensionContext,
    completionContextProvider: CompletionContextProvider,
    configProvider: VSCodeConfigProvider,
    treeSitterProvider: TreeSitterProvider,
    rewriteHandler: RewriteHandler | undefined
) {
    const appName = vscode.env.appName;
    const ghostTextGuide = new GhostTextUsageGuide(configProvider);
    const multiLineCompletionTracker = new MultiLineCompletionTracker();
    const loadingDecorations = new LoadingDecorations();
    const lineCommentDecorations = isInternal
        ? new LineCommentDecorations(
            loadingDecorations,
            treeSitterProvider,
            configProvider
        )
        : undefined;
    if (appName === SWAN_APP_NAME && compare(vscode.version, '1.68.0', '<')) {
        return new LegacyCompletionProvider(
            iocContainer,
            ghostTextGuide,
            multiLineCompletionTracker,
            completionContextProvider,
            treeSitterProvider,
            loadingDecorations,
            lineCommentDecorations,
            configProvider,
            rewriteHandler
        );
    }
    else {
        const enabledApiProposals = context.extension.packageJSON?.enabledApiProposals ?? [];
        const isProposalEnable = enabledApiProposals.includes('inlineCompletionsAdditions')
            || appName === 'iCoding';
        return isProposalEnable
            ? new ProposalEnabledCompletionProvider(
                iocContainer,
                ghostTextGuide,
                multiLineCompletionTracker,
                completionContextProvider,
                treeSitterProvider,
                loadingDecorations,
                lineCommentDecorations,
                configProvider,
                rewriteHandler
            )
            : new CompletionProvider(
                iocContainer,
                ghostTextGuide,
                multiLineCompletionTracker,
                completionContextProvider,
                treeSitterProvider,
                loadingDecorations,
                lineCommentDecorations,
                configProvider,
                rewriteHandler
            );
    }
}

export function registerInlineCompletionListeners(
    context: vscode.ExtensionContext,
    completionProvider: AbstractCompletionProvider,
    programmingContextTracker: IProgrammingContextTracker
) {
    const reportPostProcessResult = (completions: InlineCompletionItem[]) => {
        for (const item of completions) {
            if (!item.cache && item.range) {
                let range = item.range;
                if (item.selectedCompletionInfo && item.isExtensionToSelectedCompletionInfo) {
                    // 当候选列表有选中项时，我们会用该候选的文本替换原文，发给模型的是修改后的原文。同时，服务端记录的也是修改后的原文，而非用户看到的原文。
                    // 然而，这里的 item.range 是基于用户看到的原文的范围，因此在上报后处理结果时，要额外修正一下 range，以便服务端可以正确还原。
                    // 在修改原文过程中，我们只会在光标后追加候选项的字符，因此只需要移动 range 的 end 位置即可，以便匹配到修改后的原文的范围。
                    const replacement = item.selectedCompletionInfo;
                    const replacedLength = replacement.range.end.character - replacement.range.start.character;
                    const newEndPosition = range.end.translate(
                        0,
                        replacement.text.length - replacedLength
                    );
                    range = range.with(range.start, newEndPosition);
                }
                modifyCode({
                    uuid: item.uuid,
                    mergeResult: {
                        replaceRange: convertToOneBasedRange(range),
                        replaceText: typeof item.insertText === 'string' ? item.insertText : item.insertText.value,
                    },
                });
            }
        }
    };

    const reportProgrammingContext = async (completions: InlineCompletionItem[]) => {
        if (!isInternal) {
            return;
        }
        // 70% 的概率上传
        if (Math.random() < 0.3) {
            return;
        }
        const item = completions[0];
        if (item?.uuid && !item.cache) {
            const actions = await programmingContextTracker.getContext(item.uri, item.range?.end);
            if (actions.length > 0) {
                const result = {
                    uuid: item.uuid,
                    // eslint-disable-next-line camelcase
                    edit_diff: formatForRequest(actions),
                };
                uploadEditDiff(result);
                trace('userActionTracker(result)', JSON.stringify(result));
            }
        }
    };

    // 端上有后处理逻辑，比如用续写内容覆盖光标后的文本，此时需要上报便于还原用户真实看到的续写效果。
    context.subscriptions.push(
        completionProvider.onDidProvideInlineCompletionItems(completions => {
            reportPostProcessResult(completions);
            reportProgrammingContext(completions);
        })
    );
}

export function registerTrivialCommands(context: vscode.ExtensionContext) {
    return [
        vscode.commands.registerCommand('baidu.comate.toggleDebugLog', () => {
            consoleLogger.toggleDebugMode();
        }),
        vscode.commands.registerCommand('baidu.comate.openSettings', () => {
            vscode.commands.executeCommand('workbench.action.openSettings', {
                query: `@ext:${context.extension.id}`,
            });
        }),
        vscode.commands.registerCommand('baidu.comate.visitHelpDocs', () => {
            vscode.env.openExternal(vscode.Uri.parse(platform.resolve('helpDocUrl')));
        }),
        vscode.commands.registerCommand('baidu.comate.feedback', () => {
            vscode.env.openExternal(vscode.Uri.parse(platform.resolve('feedbackUrl')));
        }),
        registerLogLevelQuickPick(),
    ];
}

export function initProgrammingContextTracker(context: vscode.ExtensionContext, workspaceProvider: IWorkspaceProvider) {
    const logicalEditTracker = new LogicalEditTracker(workspaceProvider);

    const terminalCommandTracker = new TerminalCommandTracker();

    const diagnosticTracker = VSCodeDiagnosticTracker.getInstance();

    const programmingContextTracker = new ProgrammingContextTracker(
        workspaceProvider,
        logicalEditTracker,
        terminalCommandTracker,
        diagnosticTracker
    );

    context.subscriptions.push(
        logicalEditTracker,
        terminalCommandTracker,
        terminalCommandTracker.onDidStartTerminalShellExecution(() => {
            logicalEditTracker.finalizeEdit();
        })
    );

    return programmingContextTracker;
}

async function initAcceptanceTriggeredRewrite(
    context: vscode.ExtensionContext,
    enablement: {enabled: boolean},
    tabStreak: TabStreakController,
    completionProvider: AbstractCompletionProvider,
    programmingContextTracker: ProgrammingContextTracker
) {
    let cancelTokenSource = new vscode.CancellationTokenSource();

    context.subscriptions.push(
        completionProvider.onDidAcceptInlineCompletionItem(async completion => {
            if (!enablement.enabled) {
                consoleLogger.debug('rewrite-by-acceptance: rewrite is disabled');
                return;
            }
            cancelTokenSource.cancel();
            cancelTokenSource = new vscode.CancellationTokenSource();
            const cancelToken = cancelTokenSource.token;
            const editor = vscode.window.activeTextEditor;
            if (completion.range && editor?.document.uri.toString() === completion.uri.toString()) {
                const cursor = editor.selection.active;
                const document = new VSCodeTextDocument(editor.document);
                const programmingContext = await programmingContextTracker.getContext(document.uriObject);
                tabStreak.run(
                    editor,
                    cursor,
                    {
                        acceptedInlineCompletion: completion.displayText,
                        refreshContext: programmingContextTracker.getContext.bind(programmingContextTracker),
                        programmingContext,
                    },
                    cancelToken
                );
            }
        }),
        completionProvider.onDidProvideInlineCompletionItems(completions => {
            if (completions.length > 0) {
                cancelTokenSource.cancel();
            }
        })
    );
}

function initDiagnosticsTriggeredRewrite(
    context: vscode.ExtensionContext,
    enablement: {enabled: boolean},
    tabStreak: TabStreakController,
    completionProvider: AbstractCompletionProvider,
    programmingContextTracker: ProgrammingContextTracker,
    cursorMoveEventEmitter: CursorMoveEventEmitter,
    clientGlobalConfigService: ClientGlobalConfigService
) {
    const diagnosticTracker = VSCodeDiagnosticTracker.getInstance();
    let cancelTokenSource = new vscode.CancellationTokenSource();

    const register = (debounce: number) => {
        // eslint-disable-next-line max-statements, complexity
        const disposable = cursorMoveEventEmitter.onDidMoveCursorManually(async event => {
            if (!enablement.enabled) {
                consoleLogger.debug('cursorMove(skip): tab streak is disabled');
                return;
            }
            if (event.uri.scheme !== 'file') {
                // 只在文件协议类型的文件中触发
                return;
            }
            if (
                event.kind !== vscode.TextEditorSelectionChangeKind.Mouse
                && event.kind !== vscode.TextEditorSelectionChangeKind.Keyboard
            ) {
                // 只在键盘或鼠标移动时触发
                return;
            }
            const editors = vscode.window.visibleTextEditors;
            const nonOutputEditors = editors.filter(item => item.document.uri.scheme !== 'output');
            if (nonOutputEditors.length !== 1) {
                consoleLogger.debug('cursorMove(skip): there is not exactly one visible non-output editor');
                return;
            }
            const editor = nonOutputEditors[0];
            if (editor.document.uri.toString() !== event.uri.toString()) {
                consoleLogger.debug('cursorMove(skip): cursor is not in the currently visible editor');
                return;
            }
            consoleLogger.debug('cursorMove: detected');
            const cursor = event.selection.active;
            if (tabStreak.isDeciding) {
                consoleLogger.debug('cursorMove(skip): user is deciding');
                return;
            }
            if (tabStreak.isEnclosedByPendingRewrite(cursor)) {
                consoleLogger.debug('cursorMove(skip): cursor is enclosed by pending rewrite');
                return;
            }
            const diagnostics = diagnosticTracker.getDiagnostics(event.uri, cursor);
            if (diagnostics.length <= 0) {
                consoleLogger.debug('cursorMove(skip): no diagnostics near cursor');
                return;
            }
            if (Date.now() - programmingContextTracker.lastEditTimestamp > 1000 * 60 * 5) {
                consoleLogger.debug('cursorMove(skip): last edit is too old (> 5 minutes)');
                return;
            }
            const programmingContext = await programmingContextTracker.getContext(editor.document.uri, cursor);
            if (!programmingContext.some(item => item.name === 'edit_diff')) {
                consoleLogger.debug('cursorMove(skip): no edit_diff context');
                return;
            }
            cancelTokenSource.cancel();
            cancelTokenSource = new vscode.CancellationTokenSource();
            const cancelToken = cancelTokenSource.token;
            tabStreak.runDiagnosticFix(
                editor,
                cursor,
                {
                    refreshContext: programmingContextTracker.getContext.bind(programmingContextTracker),
                    programmingContext,
                    triggerSource: event.kind === vscode.TextEditorSelectionChangeKind.Keyboard
                        ? TriggerSource.DiagnosticInfoKeyboardMove
                        : TriggerSource.DiagnosticInfo,
                },
                cancelToken
            );
        }, debounce);

        return disposable;
    };

    const getDebounceTime = () => {
        const config = clientGlobalConfigService.config;
        return config?.commonConfig.rewriteByDiagnosticInfoDelayMillSeconds || 1000;
    };

    let debounceTime = getDebounceTime();
    let disposable = register(debounceTime);

    context.subscriptions.push(
        clientGlobalConfigService.onDidChange(() => {
            const newDebounceTime = getDebounceTime();
            if (debounceTime !== newDebounceTime) {
                debounceTime = newDebounceTime;
                disposable.dispose();
                disposable = register(newDebounceTime);
            }
        }),
        completionProvider.onDidProvideInlineCompletionItems(completions => {
            if (completions.length > 0) {
                cancelTokenSource.cancel();
            }
        })
    );
}

export function initTabStreak(context: vscode.ExtensionContext, diffViewer: IDiffViewer, userService: UserService) {
    if (!isInternal) {
        // vscode 目前只在厂内开启续写智能体
        return;
    }
    const adaptBuildParams = (document: ITextDocument, position: Position) => {
        const vscDocument = {
            uri: vscode.Uri.parse(document.uri),
            getText(range?: vscode.Range): string {
                return document.getText(range);
            },
        };
        const vscPosition = new vscode.Position(position.line, position.character);
        return buildParams(vscDocument, vscPosition, userService);
    };
    const similarLinePrediction = new SimilarLineCursorPrediction(adaptBuildParams);
    const llmBlockRewrite = new LLMBlockRewrite(adaptBuildParams);
    const tabStreak = new TabStreakController(diffViewer, similarLinePrediction, llmBlockRewrite);

    context.subscriptions.push(tabStreak);

    return tabStreak;
}

export async function registerTabStreakFeatures(
    context: vscode.ExtensionContext,
    tabStreak: TabStreakController,
    configProvider: VSCodeConfigProvider,
    completionProvider: AbstractCompletionProvider,
    programmingContextTracker: ProgrammingContextTracker,
    cursorMoveEventEmitter: CursorMoveEventEmitter,
    clientGlobalConfigService: ClientGlobalConfigService
) {
    const enablement = {
        enabled: false,
    };

    const updateEnablement = async () => {
        const agentConfig = await configProvider.getAgentConfig();
        if (agentConfig.enableIntelligenceAgent) {
            enablement.enabled = agentConfig.enableIntelligenceAgent.enableCompletionIntelligence;
        }
    };

    context.subscriptions.push(
        configProvider.onDidChange(async () => {
            updateEnablement();
        })
    );

    await updateEnablement();

    initAcceptanceTriggeredRewrite(
        context,
        enablement,
        tabStreak,
        completionProvider,
        programmingContextTracker
    );

    initDiagnosticsTriggeredRewrite(
        context,
        enablement,
        tabStreak,
        completionProvider,
        programmingContextTracker,
        cursorMoveEventEmitter,
        clientGlobalConfigService
    );
}

export function initCompletionContextProvider(context: vscode.ExtensionContext) {
    const treeSitterProvider = iocContainer.get(TreeSitterProvider);
    const symbolUsageFinder = iocContainer.get<ISymbolUsageFinder>(TYPES.ISymbolUsageFinder);
    const symbolSignatureController = iocContainer.get(SymbolSignatureController);
    const locationLinkResolver = iocContainer.get<ILocationLinkResolver>(TYPES.ILocationLinkResolver);
    const symbolLinkResolver = new SymbolLinkResolver(
        symbolUsageFinder,
        locationLinkResolver
    );
    const lspSymbolSignatureReader = new LspSymbolSignatureReader({
        maxFileLimit: 50,
        maxSymbolsLimit: 100,
    });
    symbolSignatureController.addReader(
        lspSymbolSignatureReader,
        new CppSymbolSignatureReader(treeSitterProvider),
        new JsSymbolSignatureReader(treeSitterProvider)
    );
    const workspaceProvider = VSCodeWorkspace.getInstance();
    const crossFileContextAnalyzer = new CrossFileContextAnalyzer(
        symbolLinkResolver,
        symbolSignatureController,
        // 重新分析文件依赖的最小时间间隔，20s
        1000 * 20,
        workspaceProvider
    );

    // Note: 当文件打开时，预先开始跨文件依赖分析，尽量让首次续写触发就能获取到跨文件的下文
    context.subscriptions.push(
        lspSymbolSignatureReader,
        vscode.window.onDidChangeActiveTextEditor(e => {
            if (e?.document.uri.scheme === 'file') {
                const fileName = e.document.fileName;
                if (crossFileContextAnalyzer.checkSupport(fileName)) {
                    crossFileContextAnalyzer.prepare(fileName);
                }
            }
        })
    );

    const completionContextProvider = new CompletionContextProvider(crossFileContextAnalyzer);
    return completionContextProvider;
}
