import * as vscode from 'vscode';
import {ENVIRONMENT, PLATFORM, setPlatformAndEnvironment} from '@comate/plugin-shared-internals';
import {activateCommon} from './main';
import {iocContainer} from './iocContainer';
import {BasicTimeTracker} from './services/TimeTracker';
import {TYPES} from './inversify.config';
import {ITimeTracker} from './services/TimeTracker/types';
import {BasicLicenseController} from './services/LicenseController/Basic';
import {ILicenseController} from './services/LicenseController/types';
import {ICompletionSuccessRateTracker} from './services/CompletionProvider/SuccessRateTracker/types';
import {EmptyCompletionSuccessRateTracker} from './services/CompletionProvider/SuccessRateTracker/Empty';
import {extensionLogin} from './utils/login';
import {reportEvent} from './utils/trackerSaas';
import {EmbeddingsController} from './services/EmbeddingsService/controller';
import {KernelProvider} from './services/KernelProvider';
import {ComatePlusChatSession} from './services/ComatePlusChatSession';
// import {ComatePairModePluginProvider} from './services/ComatePairModePluginProvider';
import {ILogger} from './services/Logger/types';
import {BasicLogger} from './services/Logger';
import {IL10nProvider} from './common/L10nProvider/types';
import {L10nProvider} from './common/L10nProvider/L10nProvider';
import {CustomizeProvider} from './services/CustomizeProvider';
import {TerminalLinkProvider} from './services/TerminalLink/TerminalLinkProvider';
import CodeRatioCalculatorByGit from './services/CodeRatioCalculator/byGit';
import {PartialPrivatizationProvider} from './services/PartialPrivatizationProvider';
import uriProtocolHandler from './services/UriProtocolHandler';
import {QuickFixProvider} from './services/QuickFixProvider';

setPlatformAndEnvironment(PLATFORM.SAAS, $features.ENVIRONMENT as ENVIRONMENT);

export function activate(context: vscode.ExtensionContext) {
    activateCommon(context, {
        createL10nProvider: () => {
            iocContainer.bind<IL10nProvider>(TYPES.IL10nProvider).to(L10nProvider).inSingletonScope();
            return iocContainer.get<IL10nProvider>(TYPES.IL10nProvider);
        },
        createTimeTracker: () => {
            iocContainer
                .bind<ITimeTracker>(TYPES.ITimeTracker)
                .to(BasicTimeTracker)
                .inSingletonScope();
            return iocContainer.get<ITimeTracker>(TYPES.ITimeTracker);
        },
        createLogger: () => {
            iocContainer
                .bind<ILogger>(TYPES.ILogger)
                .to(BasicLogger)
                .inSingletonScope();
            return iocContainer.get<ILogger>(TYPES.ILogger);
        },
        createLicenseController: () => {
            iocContainer
                .bind<ILicenseController>(TYPES.ILicenseController)
                .to(BasicLicenseController)
                .inSingletonScope();
            return iocContainer.get<ILicenseController>(TYPES.ILicenseController);
        },
        createCompletionSuccessRateTracker() {
            iocContainer
                .bind<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker)
                .to(EmptyCompletionSuccessRateTracker)
                .inSingletonScope();
            return iocContainer.get<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker);
        },
        createExclusiveFeatures() {
            iocContainer.bind(EmbeddingsController).toSelf().inSingletonScope();
            iocContainer.bind(ComatePlusChatSession).toSelf().inSingletonScope();
            iocContainer.bind(KernelProvider).toSelf().inSingletonScope();
            // iocContainer.bind(ComatePairModePluginProvider).toSelf().inSingletonScope();
            iocContainer.bind(TerminalLinkProvider).toSelf().inSingletonScope();
            iocContainer.bind(CodeRatioCalculatorByGit).toSelf().inSingletonScope();
            iocContainer.bind(PartialPrivatizationProvider).toSelf().inSingletonScope();
            iocContainer.bind(QuickFixProvider).toSelf().inSingletonScope();

            iocContainer.get(KernelProvider).start();
            iocContainer.get(CustomizeProvider).start();
            iocContainer.get(TerminalLinkProvider).start();
            iocContainer.get(CodeRatioCalculatorByGit).start();
            iocContainer.get(PartialPrivatizationProvider).start();

            iocContainer.get(ComatePlusChatSession).start();
            iocContainer.get(EmbeddingsController).start();

            return [
                iocContainer.get(ComatePlusChatSession),
                iocContainer.get(EmbeddingsController),
                iocContainer.get(KernelProvider),
                iocContainer.get(TerminalLinkProvider),
                iocContainer.get(QuickFixProvider),
                uriProtocolHandler,
            ];
        },
    });

    const licenseController = iocContainer.get<ILicenseController>(TYPES.ILicenseController);
    if (!licenseController.hasLicense) {
        extensionLogin(iocContainer);
    }

    licenseController.checkExpire();
    setInterval(
        () => {
            licenseController.checkExpire();
        },
        1000 * 60 * 60
    );

    reportEvent('extension', 'active', 'success');
}

export function deactivate() {}
