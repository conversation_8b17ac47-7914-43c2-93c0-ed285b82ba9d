import os from 'node:os';
import * as vscode from 'vscode';
import platform from '@shared/platform';
import {getExtensionContextAsync} from './utils/extensionContext';

const POPUP_ID_STORAGE_KEY = 'COMATE_PROMOTION_POPUP';

const CMD_SHOW_PROMOTION = 'baidu.comate.showPromotion';

const osPlatform = os.platform();

const promotion = {
    description: `你好，我是 ${platform.resolve('brand')}，任何编程问题都可以来找我！`,
    options: [
        {
            name: `试一下 (${osPlatform === 'win32' ? 'Win' : '⌘'} + Y)`,
            command: 'baidu.comate.showChatPanel',
        },
        {
            name: '知道了',
        },
    ],
    id: 2, // NOTE: 每次更新公告后都需要手动 id + 1
};

export async function showPromotionPopup() {
    const context = await getExtensionContextAsync();
    const prevPopupId = context.globalState.get(POPUP_ID_STORAGE_KEY, -1);
    if (prevPopupId < promotion.id) {
        await vscode.commands.executeCommand(CMD_SHOW_PROMOTION);
        context.globalState.update(POPUP_ID_STORAGE_KEY, promotion.id);
    }
}

export function registerShowPromptionCommand() {
    return vscode.commands.registerCommand(CMD_SHOW_PROMOTION, async () => {
        const choice = await vscode.window.showInformationMessage(
            promotion.description,
            ...promotion.options.map(item => item.name)
        );
        const option = promotion.options.find(item => item.name === choice);
        if (option?.command) {
            vscode.commands.executeCommand(option.command);
        }
    });
}
