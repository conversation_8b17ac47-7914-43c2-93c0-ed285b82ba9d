import * as vscode from 'vscode';
import {Range} from 'vscode-languageserver';
import {UserService} from './services/UserService';
import {VSCodeConfigProvider} from './services/ConfigProvider';
import {ComateStatusBar} from './services/StatusBar';
import {iocContainer} from './iocContainer';
import {ComatePanel} from './services/ComatePanel';
import {SmartUTProvider} from './services/SmartUTProvider';
import {TreeSitterProvider} from './services/TreeSitterProvider';
import {DocstringCodeLensesProvider} from './services/DocstringProvider';
import {NL2CodeProvider} from './services/NL2CodeProvider';
import {ChatViewProvider} from './services/ChatViewProvider';
import {CodeExplainProvider} from './services/CodeExplainProvider';
import {FunctionSplitProvider} from './services/FunctionSplitProvider';
import {InlineCommentProvider} from './services/InlineCommentProvider';
import {CodeSelectionActionsProvider} from './services/CodeSelectionActions';
import {CodeSelectionTipProvider} from './services/CodeSelectionTip';
import {QueryVisibilitySelectorProvider} from './services/QueryVisibilitySelectorProvider';
import {FunctionOptimizeProvider} from './services/FunctionOptimizeProvider';
import {DiffProvider} from './services/DiffProvider';
import {RegexHoverProvider} from './services/RegexHoverProvider';
import {FeatureFlags} from './services/FeatureFlags';
import {BuiltinGitProvider} from './services/BuiltinGitProvider';
import {setExtensionContext} from './utils/extensionContext';
import {ITimeTracker} from './services/TimeTracker/types';
import {ILicenseController} from './services/LicenseController/types';
import {LocationFactory} from './services/Helper/LocationFactory';
import {TextDocumentManager} from './services/Helper/TextDocumentManager';
import {ICompletionSuccessRateTracker} from './services/CompletionProvider/SuccessRateTracker/types';
import {NewAutoComateChatSession} from './services/AutoComateChatSession';
import {EmbeddingsService} from './services/EmbeddingsService';
import {EmbeddingsIndexProgressStore} from './services/EmbeddingsService/IndexProgressStore';
import {PluginConfigPanelProvider} from './services/PluginConfigPanelProvider';
// import {ComatePairStatusBar} from './services/ComatePairModePluginProvider/ComatePairStatusBar';
// import {ReplaceTextProvider} from './services/ComatePairModePluginProvider/ReplaceTextProvider';
import {FileProvider} from './services/FileProvider';
import {LogUploaderProvider} from './services/LogUploaderProvider';
import {PerformanceLogProvider} from './services/PerformanceLogProvider';
import {UsageLogProvider} from './services/UsageLogProvider';
import {FoldedCodeLensProvider} from './services/FoldedCodeLensProvider';
import {VSCodeLocationLinkResolver} from './services/CrossFileContextAnalyzer/LocationLinkResolver';
import {TreeSitterBasedSymbolUsageFinder} from './services/CrossFileContextAnalyzer/SymbolUsageFinder';
import {setApiUserInfoGetter, updateApiHeadersConfig} from './api/common';
import {ILogger} from './services/Logger/types';
import {ChatTrialProvider} from './services/ChatTrialProvider';
import {IL10nProvider} from './common/L10nProvider/types';
import {SymbolSignatureController} from './services/CrossFileContextAnalyzer/SymbolSignatureReader';
import {CustomizeProvider} from './services/CustomizeProvider';
import {UserGuideProvider} from './services/UserGuideProvider';
import {SmartAPIProvider} from './services/SmartAPIProvider';
import {InlineChatProvider} from './services/InlineChatProvider';
import {DynamicDiffManager} from './services/InlineChatProvider/DynamicDiffManager';
import {DualScreenDiffCodeLensProvider} from './services/DiffProvider/DiffCodeLensProvider';
import {DiffHighlightProvider} from './services/DiffProvider/DiffHighlightProvider';
import {TYPES} from './inversify.config';
import {UTContextAnalyzer} from './services/SmartUTProvider/context';
import {ContextualFilterManager} from './services/Helper/ContextualFilter';
import {InlineLogProvider} from './services/InlineLogProvider';
import {RipgrepProvider} from './common/RipgrepProvider';
import {PaddleConvertProvider} from './services/PaddleConvertProvider';
import {MessageGenerateProvider} from './services/IssueGenerateProvider/MessageGenerateProvider';
import {ReactComponentSplitProvider} from './services/ReactComponentSplitProvider';
import {TemporaryCodeLensProvider} from './services/TemporaryCodeLensProvider';
import {CodeRatioCalculator} from './services/CodeRatioCalculator';
import {EnhanceCompletionProvider} from './services/CompletionProvider/EnhanceCompletionProvider';
import AppLaunchTracker from './services/AppLaunchTracker';
import {AgentProvider} from './services/AgentProvider';
import {VirtualEditorProvider} from './services/VirtualEditorProvider';
import {TerminalManager} from './services/TerminalLink/TerminalManager';
import {
    initCompletionProvider,
    initTabStreak,
    registerTrivialCommands,
    initProgrammingContextTracker,
    registerInlineCompletionListeners,
    registerTabStreakFeatures,
    initCompletionContextProvider,
} from './initializers';
import {SideBySideDiffViewer} from './services/DiffViewer/SideBySideDiffViewer';
import {VisualStateManagerProvider} from './services/VisualStateManagerProvider';
import {SmartApplyProvider} from './services/SmartApply';
import {EngineRAGCapability} from './services/AutoComateChatSession/EngineRAGCapability';
import ModelSelector from './services/ModelSelector';
import {PromptTemplateProvider} from './services/PromptTemplateProvider';
import CompletionPositionChecker from './services/CompletionProvider/CompletionPositionChecker';
import {EditorWatcherProvider} from './services/EditorWatcherProvider';
import {CursorMoveEventEmitter} from './services/CursorMoveEventEmitter';
import {ClientGlobalConfigService} from './services/CompletionProvider/ClientGlobalConfigService';
import {RewriteHandler} from './services/CompletionProvider/types';
import {VSCodeWorkspace} from './common/lsp/adapters/VSCodeWorkspace';
import consoleLogger from './common/consoleLogger';
import {isInternal} from './utils/features';
import {TooBotheredHeuristic} from './services/TabStreak/conditions/TooBothered';

try {
    // 修复 tree-sitter 加载失败的问题
    // @ts-ignore
    delete WebAssembly.instantiateStreaming;
}
catch {
    //
}

interface PlatformContext {
    createL10nProvider: () => IL10nProvider;
    createTimeTracker: () => ITimeTracker;
    createLogger: () => ILogger;
    createLicenseController: () => ILicenseController;
    createCompletionSuccessRateTracker: () => ICompletionSuccessRateTracker;
    createExclusiveFeatures: () => vscode.Disposable[];
}

// eslint-disable-next-line max-statements
export function activateCommon(context: vscode.ExtensionContext, platformContext: PlatformContext) {
    setExtensionContext(context);

    updateApiHeadersConfig({
        clientType: vscode.env.appName,
        clientVersion: context.extension.packageJSON.version,
    });

    setApiUserInfoGetter(async () => {
        const configProvider = iocContainer.get(VSCodeConfigProvider);
        const userService = iocContainer.get(UserService);
        const customizeProvider = iocContainer.get(CustomizeProvider);
        const [user] = await userService.getCurrentUser();
        const isCustomizeUser = customizeProvider.isCustomizeUser();
        const customizeService = isCustomizeUser ? customizeProvider.getCustomizeServiceConfig() : undefined;
        return {
            user,
            license: configProvider.getLicense(),
            customizeService,
        };
    });

    vscode.commands.executeCommand('setContext', 'baidu.comate.activated', true);

    // order does matter!
    iocContainer.bind(UserService).toSelf().inSingletonScope();
    iocContainer.bind(FeatureFlags).toSelf().inSingletonScope();
    iocContainer.bind(ComateStatusBar).toSelf().inSingletonScope();
    // iocContainer.bind(ComatePairStatusBar).toSelf().inSingletonScope();
    iocContainer.bind(TemporaryCodeLensProvider).toSelf().inSingletonScope();
    iocContainer.bind(SmartUTProvider).toSelf().inSingletonScope();
    iocContainer.bind(TreeSitterProvider).toSelf().inSingletonScope();
    iocContainer.bind(DocstringCodeLensesProvider).toSelf().inSingletonScope();
    iocContainer.bind(VSCodeConfigProvider).toSelf().inSingletonScope();
    iocContainer.bind(NL2CodeProvider).toSelf().inSingletonScope();
    iocContainer.bind(LocationFactory).toSelf().inSingletonScope();
    iocContainer.bind(TextDocumentManager).toSelf().inSingletonScope();
    iocContainer.bind(ChatViewProvider).toSelf().inSingletonScope();
    iocContainer.bind(ChatTrialProvider).toSelf().inSingletonScope();
    iocContainer.bind(CodeExplainProvider).toSelf().inSingletonScope();
    // paddle v
    iocContainer.bind(PaddleConvertProvider).toSelf().inSingletonScope();
    // paddle ^
    iocContainer.bind(FunctionSplitProvider).toSelf().inSingletonScope();
    iocContainer.bind(FunctionOptimizeProvider).toSelf().inSingletonScope();
    iocContainer.bind(InlineCommentProvider).toSelf().inSingletonScope();
    iocContainer.bind(FoldedCodeLensProvider).toSelf().inSingletonScope();
    iocContainer.bind(InlineChatProvider).toSelf().inSingletonScope();
    iocContainer.bind(DynamicDiffManager).toSelf().inSingletonScope();
    iocContainer.bind(DualScreenDiffCodeLensProvider).toSelf().inSingletonScope();
    iocContainer.bind(CodeSelectionActionsProvider).toSelf().inSingletonScope();
    iocContainer.bind(CodeSelectionTipProvider).toSelf().inSingletonScope();
    iocContainer.bind(QueryVisibilitySelectorProvider).toSelf().inSingletonScope();
    iocContainer.bind(DiffProvider).toSelf().inSingletonScope();
    iocContainer.bind(RegexHoverProvider).toSelf().inSingletonScope();
    iocContainer.bind(NewAutoComateChatSession).toSelf().inSingletonScope();
    iocContainer.bind(EmbeddingsService).toSelf().inSingletonScope();
    iocContainer.bind(EmbeddingsIndexProgressStore).toSelf().inSingletonScope();
    iocContainer.bind(PluginConfigPanelProvider).toSelf().inSingletonScope();
    iocContainer.bind(FileProvider).toSelf().inSingletonScope();
    // iocContainer.bind(ReplaceTextProvider).toSelf().inSingletonScope();
    iocContainer.bind(LogUploaderProvider).toSelf().inSingletonScope();
    iocContainer.bind(UserGuideProvider).toSelf().inSingletonScope();
    iocContainer.bind(PerformanceLogProvider).toSelf().inSingletonScope();
    iocContainer.bind(UsageLogProvider).toSelf().inSingletonScope();
    iocContainer.bind(SmartAPIProvider).toSelf().inSingletonScope();
    iocContainer.bind(CustomizeProvider).toSelf().inSingletonScope();
    iocContainer.bind(SymbolSignatureController).toSelf().inSingletonScope();
    iocContainer.bind(TYPES.ILocationLinkResolver).to(VSCodeLocationLinkResolver).inSingletonScope();
    iocContainer.bind(TYPES.ISymbolUsageFinder).to(TreeSitterBasedSymbolUsageFinder).inSingletonScope();
    iocContainer.bind(UTContextAnalyzer).toSelf().inSingletonScope();
    iocContainer.bind(ContextualFilterManager).toSelf().inSingletonScope();
    iocContainer.bind(RipgrepProvider).toSelf().inSingletonScope();
    iocContainer.bind(InlineLogProvider).toSelf().inSingletonScope();
    iocContainer.bind(DiffHighlightProvider).toSelf().inSingletonScope();
    iocContainer.bind(MessageGenerateProvider).toSelf().inSingletonScope();
    iocContainer.bind(ReactComponentSplitProvider).toSelf().inSingletonScope();
    iocContainer.bind(CodeRatioCalculator).toSelf().inSingletonScope();
    iocContainer.bind(EnhanceCompletionProvider).toSelf().inSingletonScope();
    iocContainer.bind(VirtualEditorProvider).toSelf().inSingletonScope();
    iocContainer.bind(AppLaunchTracker).toSelf().inSingletonScope();
    iocContainer.bind(AgentProvider).toSelf().inSingletonScope();
    iocContainer.bind(TerminalManager).toSelf().inSingletonScope();
    iocContainer.bind(VisualStateManagerProvider).toSelf().inSingletonScope();
    iocContainer.bind(SmartApplyProvider).toSelf().inSingletonScope();
    iocContainer.bind(EngineRAGCapability).toSelf().inSingletonScope();
    iocContainer.bind(ModelSelector).toSelf().inSingletonScope();
    iocContainer.bind(PromptTemplateProvider).toSelf().inSingletonScope();
    iocContainer.bind(CompletionPositionChecker).toSelf().inSingletonScope();
    iocContainer.bind(EditorWatcherProvider).toSelf().inSingletonScope();
    iocContainer.bind(CursorMoveEventEmitter).toSelf().inSingletonScope();
    iocContainer.bind(ClientGlobalConfigService).toSelf().inSingletonScope();

    const treeSitterProvider = iocContainer.get(TreeSitterProvider);
    const configProvider = iocContainer.get(VSCodeConfigProvider);

    iocContainer.get(CodeRatioCalculator).start();

    // 语言初始化放前面，防止初始化时部分接口需要用到语言
    const l10nProvider = platformContext.createL10nProvider();

    const completionContextProvider = initCompletionContextProvider(context);

    const workspaceProvider = VSCodeWorkspace.getInstance();

    const programmingContextTracker = initProgrammingContextTracker(context, workspaceProvider);

    const diffViewer = new SideBySideDiffViewer();

    const cursorMoveEventEmitter = iocContainer.get(CursorMoveEventEmitter);

    const tabStreakController = initTabStreak(
        context,
        diffViewer,
        iocContainer.get(UserService)
    );

    const tooBotheredHeuristic = new TooBotheredHeuristic(workspaceProvider);

    const rewriteHandler: RewriteHandler = {
        contextTracker: programmingContextTracker,
        showRewrite: (uri, rewrite) => {
            const activeEditor = vscode.window.activeTextEditor;
            if (tabStreakController && activeEditor?.document.uri.toString() === uri.toString()) {
                const cursor = activeEditor.selection.active;
                if (tabStreakController.isDeciding) {
                    consoleLogger.debug('inlineEdit(skip): user is deciding');
                    return;
                }
                if (tabStreakController.isEnclosedByPendingRewrite(cursor)) {
                    consoleLogger.debug('inlineEdit(skip): cursor is enclosed by pending rewrite');
                    return;
                }
                const startLine = rewrite.startRow - 1;
                const endLine = rewrite.endRow - 1;
                const endTextLine = activeEditor.document.lineAt(endLine);
                const endCharacter = endTextLine.range.end.character;
                tabStreakController.showInlineEdit(activeEditor, {
                    range: Range.create(startLine, 0, endLine, endCharacter),
                    content: rewrite.generatedContent,
                    uuidPromise: Promise.resolve(rewrite.uuid),
                });
            }
        },
        getEnabled: () => {
            const previewEnabled = vscode.workspace.getConfiguration('editor.suggest').get<boolean>('preview', false);
            return isInternal && !previewEnabled;
        },
        shouldSkip(uri: vscode.Uri, startLine: number, endLine: number) {
            const result = tooBotheredHeuristic.match(uri.toString(), startLine, endLine);
            result && consoleLogger.debug('inlineEdit(skip): too bothering otherwise');
            return result;
        },
    };

    const completionProvider = initCompletionProvider(
        context,
        completionContextProvider,
        configProvider,
        treeSitterProvider,
        isInternal ? rewriteHandler : undefined
    );

    registerInlineCompletionListeners(context, completionProvider, programmingContextTracker);

    if (tabStreakController) {
        tabStreakController.addSkipRewriteCondition(tooBotheredHeuristic);
        registerTabStreakFeatures(
            context,
            tabStreakController,
            configProvider,
            completionProvider,
            programmingContextTracker,
            cursorMoveEventEmitter,
            iocContainer.get(ClientGlobalConfigService)
        );
        tabStreakController.onDidAcceptRewrite(() => {
            completionProvider.freeze(100);
        });
        tabStreakController.onDidEscapeRewrite(item => {
            tooBotheredHeuristic.addEscapedRewrite(item);
        });
    }

    const comatePanel = new ComatePanel(iocContainer);
    const gitProvider = BuiltinGitProvider.createProvider();

    const timeTracker = platformContext.createTimeTracker();
    const logger = platformContext.createLogger();
    const licenseController = platformContext.createLicenseController();
    const completionSuccessRateTracker = platformContext.createCompletionSuccessRateTracker();
    const exclusiveFeatures = platformContext.createExclusiveFeatures();

    const foldedCodeLensProvider = iocContainer.get(FoldedCodeLensProvider);
    context.subscriptions.push(iocContainer.get(ChatTrialProvider));
    foldedCodeLensProvider.addCodeLensProvider(iocContainer.get(FunctionOptimizeProvider));
    foldedCodeLensProvider.addCodeLensProvider(iocContainer.get(FunctionSplitProvider));
    foldedCodeLensProvider.addCodeLensProvider(iocContainer.get(ReactComponentSplitProvider));
    // paddle
    foldedCodeLensProvider.addCodeLensProvider(iocContainer.get(PaddleConvertProvider));
    foldedCodeLensProvider.addCodeLensProvider(iocContainer.get(SmartUTProvider));
    foldedCodeLensProvider.addCodeLensProvider(iocContainer.get(InlineLogProvider));
    foldedCodeLensProvider.addCodeLensProvider(iocContainer.get(InlineCommentProvider));
    foldedCodeLensProvider.addCodeLensProvider(iocContainer.get(DocstringCodeLensesProvider));
    foldedCodeLensProvider.addCodeLensProvider(iocContainer.get(CodeExplainProvider));

    iocContainer.get(FeatureFlags).start();

    context.subscriptions.push(
        iocContainer.get(UserService),
        iocContainer.get(ComateStatusBar),
        // iocContainer.get(ComatePairStatusBar),
        iocContainer.get(TemporaryCodeLensProvider),
        iocContainer.get(SmartUTProvider),
        iocContainer.get(EnhanceCompletionProvider),
        iocContainer.get(TreeSitterProvider),
        iocContainer.get(DocstringCodeLensesProvider),
        iocContainer.get(VSCodeConfigProvider),
        iocContainer.get(NL2CodeProvider),
        iocContainer.get(ChatViewProvider),
        iocContainer.get(CodeExplainProvider),
        iocContainer.get(ReactComponentSplitProvider),
        // paddle
        iocContainer.get(PaddleConvertProvider),
        iocContainer.get(FunctionSplitProvider),
        iocContainer.get(FunctionOptimizeProvider),
        iocContainer.get(InlineCommentProvider),
        iocContainer.get(FoldedCodeLensProvider),
        iocContainer.get(InlineChatProvider),
        iocContainer.get(DualScreenDiffCodeLensProvider),
        iocContainer.get(CodeSelectionActionsProvider),
        iocContainer.get(CodeSelectionTipProvider),
        iocContainer.get(QueryVisibilitySelectorProvider),
        iocContainer.get(DiffProvider),
        iocContainer.get(RegexHoverProvider),
        iocContainer.get(NewAutoComateChatSession),
        iocContainer.get(PluginConfigPanelProvider),
        // iocContainer.get(ReplaceTextProvider),
        iocContainer.get(FileProvider),
        iocContainer.get(CustomizeProvider),
        iocContainer.get(UserGuideProvider),
        iocContainer.get(SmartAPIProvider),
        iocContainer.get(ContextualFilterManager),
        iocContainer.get(InlineLogProvider),
        iocContainer.get(DiffHighlightProvider),
        iocContainer.get(MessageGenerateProvider),
        iocContainer.get(VirtualEditorProvider),
        iocContainer.get(AppLaunchTracker),
        iocContainer.get(AgentProvider),
        iocContainer.get(TerminalManager),
        iocContainer.get(VisualStateManagerProvider),
        iocContainer.get(SmartApplyProvider),
        iocContainer.get(EngineRAGCapability),
        iocContainer.get(ModelSelector),
        iocContainer.get(PromptTemplateProvider),
        iocContainer.get(CompletionPositionChecker),
        iocContainer.get(ClientGlobalConfigService),
        iocContainer.get(EditorWatcherProvider),
        cursorMoveEventEmitter,
        iocContainer.get(UsageLogProvider),
        completionProvider,
        comatePanel,
        ...(gitProvider ? [gitProvider] : []),
        timeTracker,
        logger,
        licenseController,
        completionSuccessRateTracker,
        ...registerTrivialCommands(context),
        ...exclusiveFeatures,
        l10nProvider,
        workspaceProvider,
        tooBotheredHeuristic
    );
}
