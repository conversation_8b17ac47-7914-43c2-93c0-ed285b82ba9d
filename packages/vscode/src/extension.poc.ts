import * as vscode from 'vscode';
import {PLATFORM, setPlatform} from '@comate/plugin-shared-internals';
import {autoUpdatePoc} from './utils/autoUpdaterPoc';
import {activateCommon} from './main';
import {iocContainer} from './iocContainer';
import {EmptyTimeTracker} from './services/TimeTracker';
import {TYPES} from './inversify.config';
import {ITimeTracker} from './services/TimeTracker/types';
import {BasicLicenseController} from './services/LicenseController/Basic';
import {ILicenseController} from './services/LicenseController/types';
import {ICompletionSuccessRateTracker} from './services/CompletionProvider/SuccessRateTracker/types';
import {EmptyCompletionSuccessRateTracker} from './services/CompletionProvider/SuccessRateTracker/Empty';
import {EmbeddingsController} from './services/EmbeddingsService/controller';
import {KernelProvider} from './services/KernelProvider';
import {ComatePlusChatSession} from './services/ComatePlusChatSession';
// import {ComatePairModePluginProvider} from './services/ComatePairModePluginProvider';
import {SQLSchemaProvider} from './services/SQLSchemaProvider';
import {ILogger} from './services/Logger/types';
import {EmptyLogger} from './services/Logger';
import {IL10nProvider} from './common/L10nProvider/types';
import {TerminalLinkProvider} from './services/TerminalLink/TerminalLinkProvider';
import CodeRatioCalculatorByGit from './services/CodeRatioCalculator/byGit';
import {PartialPrivatizationProvider} from './services/PartialPrivatizationProvider';
import {CustomizeProvider} from './services/CustomizeProvider';
import {L10nProvider} from './common/L10nProvider/L10nProvider';

setPlatform(PLATFORM.SAAS);

async function checkConflict() {
    const saasVersion = vscode.extensions.getExtension('BaiduComate.comate');
    if (saasVersion) {
        const choice = await vscode.window.showInformationMessage(
            '检测到已安装Baidu Comate（个人版），为避免和Baidu Comate Enterprise（企业版）发生冲突，请先卸载个人版',
            '卸载并重启'
        );
        if (choice === '卸载并重启') {
            await vscode.commands.executeCommand(
                'workbench.extensions.uninstallExtension',
                'BaiduComate.comate'
            );
            vscode.commands.executeCommand('workbench.action.reloadWindow');
        }
    }
}

export function activate(context: vscode.ExtensionContext) {
    checkConflict();
    activateCommon(context, {
        createL10nProvider: () => {
            iocContainer.bind<IL10nProvider>(TYPES.IL10nProvider).to(L10nProvider).inSingletonScope();
            return iocContainer.get<IL10nProvider>(TYPES.IL10nProvider);
        },
        createTimeTracker: () => {
            iocContainer
                .bind<ITimeTracker>(TYPES.ITimeTracker)
                .to(EmptyTimeTracker)
                .inSingletonScope();
            return iocContainer.get<ITimeTracker>(TYPES.ITimeTracker);
        },
        createLogger: () => {
            iocContainer
                .bind<ILogger>(TYPES.ILogger)
                .to(EmptyLogger)
                .inSingletonScope();
            return iocContainer.get<ILogger>(TYPES.ILogger);
        },
        createLicenseController: () => {
            iocContainer
                .bind<ILicenseController>(TYPES.ILicenseController)
                .to(BasicLicenseController)
                .inSingletonScope();
            return iocContainer.get<ILicenseController>(TYPES.ILicenseController);
        },
        createCompletionSuccessRateTracker() {
            iocContainer
                .bind<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker)
                .to(EmptyCompletionSuccessRateTracker)
                .inSingletonScope();
            return iocContainer.get<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker);
        },
        createExclusiveFeatures() {
            iocContainer.bind(EmbeddingsController).toSelf().inSingletonScope();
            iocContainer.bind(ComatePlusChatSession).toSelf().inSingletonScope();
            iocContainer.bind(KernelProvider).toSelf().inSingletonScope();
            iocContainer.bind(TerminalLinkProvider).toSelf().inSingletonScope();
            iocContainer.bind(CodeRatioCalculatorByGit).toSelf().inSingletonScope();
            iocContainer.bind(PartialPrivatizationProvider).toSelf().inSingletonScope();
            // iocContainer.bind(ComatePairModePluginProvider).toSelf().inSingletonScope();
            iocContainer.bind(SQLSchemaProvider).toSelf().inSingletonScope();

            iocContainer.get(KernelProvider).start();
            iocContainer.get(CustomizeProvider).start();
            iocContainer.get(CodeRatioCalculatorByGit).start();
            iocContainer.get(PartialPrivatizationProvider).start();
            iocContainer.get(ComatePlusChatSession).start();
            iocContainer.get(EmbeddingsController).start();
            iocContainer.get(SQLSchemaProvider).start();
            // iocContainer.get(TerminalLinkProvider).start();

            return [
                iocContainer.get(ComatePlusChatSession),
                iocContainer.get(EmbeddingsController),
                iocContainer.get(KernelProvider),
                iocContainer.get(TerminalLinkProvider),
                iocContainer.get(SQLSchemaProvider),
                // iocContainer.get(ComatePairModePluginProvider),
            ];
        },
    });
    autoUpdatePoc();
}

export function deactivate() {}
