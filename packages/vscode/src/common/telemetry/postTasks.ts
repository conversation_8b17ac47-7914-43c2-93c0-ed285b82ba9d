import * as vscode from 'vscode';
import {Container} from 'inversify';
import {editDistance, lexEditDistance} from '../../utils/lexicalAnalyzer';
import {TextDocumentManager, tryGetTextDocument} from '../../services/Helper/TextDocumentManager';
import {acceptCode, modifyCode} from '../../api';
import {ChangeTracker} from '../ChangeTracker';
import {telemetryRejected} from '.';
import {TelemetryData} from './index';

const changeCheckEvents = [
    {
        seconds: 15,
        captureCode: false,
        captureRejection: false,
        telemetryKey: 'persistent15s',
    },
    {
        seconds: 30,
        captureCode: true,
        captureRejection: true,
        telemetryKey: 'persistent30s',
    },
    {
        seconds: 120,
        captureCode: false,
        captureRejection: false,
        telemetryKey: 'persistent120s',
    },
    {
        seconds: 300,
        captureCode: false,
        captureRejection: false,
        telemetryKey: 'persistent300s',
    },
    {
        seconds: 600,
        captureCode: false,
        captureRejection: false,
        telemetryKey: 'persistent600s',
    },
];

function calculateSimilarity(text: string, displayText: string, span: number, trackOffset: number) {
    const context = text.substring(
        Math.max(0, trackOffset - span),
        Math.min(text.length, trackOffset + displayText.length + span)
    );
    // 字串的最小词编辑距离
    const subLexEditDistance = lexEditDistance(context, displayText);
    const relativeLexEditDistance = subLexEditDistance.lexDistance / subLexEditDistance.needleLexLength;
    // 字符的编辑距离
    const {distance} = editDistance(
        context.substring(subLexEditDistance.startOffset, subLexEditDistance.endOffset),
        displayText
    );

    return {
        relativeLexEditDistance,
        charEditDistance: distance,
        completionLexLength: subLexEditDistance.needleLexLength,
        foundOffset: subLexEditDistance.startOffset + Math.max(0, trackOffset - span),
        lexEditDistance: subLexEditDistance.lexDistance,
        stillInCodeHeuristic: relativeLexEditDistance <= 0.5 ? 1 : 0,
    };
}

async function resolveInsertionTrack(
    globalContext: Container,
    uri: vscode.Uri,
    displayText: string,
    changeTracker: ChangeTracker
) {
    const textDocument = await tryGetTextDocument(globalContext.get(TextDocumentManager), uri.toString());

    if (textDocument) {
        const fullText = textDocument.getText();
        const similarity = calculateSimilarity(fullText, displayText, 50, changeTracker.offset);
        return similarity;
    }
    return undefined;
}

interface CompletionInfo {
    telemetry: TelemetryData;
    uuid: string;
    displayText: string;
    uri: vscode.Uri;
    offset: number;
}

type Source = 'completion' | 'chat';

export async function postInsertionTasks(globalContext: Container, completionData: CompletionInfo, source?: Source) {
    const {offset, displayText, uri} = completionData;
    const changeTracker = new ChangeTracker(globalContext, uri, offset);
    const trimmedDisplayText = displayText.trim();
    async function scheduleEvent(index: number, source?: Source) {
        if (index >= changeCheckEvents.length) {
            return;
        }

        const event = changeCheckEvents[index];
        const waitTime = index === 0 ? event.seconds : event.seconds - changeCheckEvents[index - 1].seconds;

        // 如果当前时间检测相似度已经过低，则不继续执行接下来的检测，减少事件
        setTimeout(
            async () => {
                const similarity = await resolveInsertionTrack(
                    globalContext,
                    uri,
                    trimmedDisplayText,
                    changeTracker
                );

                if (similarity && !similarity.stillInCodeHeuristic) {
                    const telemetryParams = Object.fromEntries(
                        changeCheckEvents.filter(v => v.seconds >= event.seconds).map(v => [v.telemetryKey, false])
                    );
                    if (source === 'chat') {
                        modifyCode({
                            uuid: completionData.uuid,
                            ...telemetryParams,
                        });
                    }
                    else {
                        acceptCode({
                            uuid: completionData.uuid,
                            accepted: true,
                            content: '',
                            ...telemetryParams,
                        });
                    }
                    changeTracker.dispose();
                }
                else {
                    scheduleEvent(index + 1, source);
                }
            },
            1000 * waitTime
        );
    }

    scheduleEvent(0, source);
}

interface DisplayedInlineCompletion {
    completionText: string;
    completionTelemetryData: TelemetryData;
}

// 留着，现在无法完美实现reject的逻辑
export function postRejectionTasks(
    globalContext: Container,
    _offset: number,
    _uri: vscode.Uri,
    displayedInlineCompletions: DisplayedInlineCompletion[]
) {
    displayedInlineCompletions.forEach(({completionTelemetryData}) => {
        telemetryRejected(globalContext, completionTelemetryData);
    });
}
