import {IncomingMessage} from 'http';
import * as vscode from 'vscode';
import {CancelToken} from 'axios';
import {SSEProcessor} from '@comate/plugin-shared-internals';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {TasksProcessor} from '@/utils/TasksProcessor';
import {ICompletionSuccessRateTracker} from '@/services/CompletionProvider/SuccessRateTracker/types';
import {TYPES} from '@/inversify.config';
import {iocContainer} from '@/iocContainer';
import {getDeviceUUIDThrottled} from '@/utils/deviceUUID';
import {VSCodeConfigProvider} from '@/services/ConfigProvider';
import {isSaasOrPoc} from '@/utils/features';
import {getTraceRepoInfo} from '@/utils/trace';
import {
    ActionType,
    apiRewriteCodeBlock,
    RewriteBaseResult,
    TriggerSource,
} from '@/api/smartTab';
import {RewriteHandler} from '@/services/CompletionProvider/types';
import {formatForRequest} from '@/services/ProgrammingContextTracker/utils';
import {adjustRewriteRange} from '@/services/EditPredictionProvider/utils';
import {checkSubsequence} from '@/utils/common';
import {
    generateBatchCode,
    generateCode,
    GenerateCodeOptions,
    generateStreamCode,
    RawGenerateCode,
    GenerateCodeResponse,
} from '../api';
import {SWAN_APP_NAME} from '../constants';
import {UserService} from '../services/UserService';
import {SuccessReturn, UnsuccessReturn} from '../services/types';
import {isForbidden, showForbiddenMessage} from './showForbiddenMessage';
import {debug, error} from './outputChannel';

export const getIdeName = () => {
    const appName = vscode.env.appName;
    if ($features.ENTERPRISE_VERSION && $features.ENTERPRISE_VERSION === 'gitee') {
        return 'vscode-gitee';
    }
    switch (appName) {
        case 'iCoding':
            return 'vscode-iCoding';
        case SWAN_APP_NAME:
            return 'vscode-swan';
        default:
            return 'vscode-local';
    }
};

export async function buildParams(
    document: Pick<vscode.TextDocument, 'uri' | 'getText'> | undefined,
    position: vscode.Position,
    userService: UserService
): Promise<SuccessReturn<GenerateCodeOptions> | UnsuccessReturn> {
    const usernamePromise = userService.getCurrentUser();

    const repoInfoPromise = getTraceRepoInfo();

    const contextPromise = getExtensionContextAsync();

    const [usernameRes, repoInfo, contextRes] = await Promise.allSettled(
        [usernamePromise, repoInfoPromise, contextPromise]
    );

    // username 如果获取失败，就不往下走了
    if (usernameRes.status === 'rejected') {
        return {
            type: 'failed',
            reason: 'get username failed',
        };
    }
    const username = usernameRes.value[0];

    const repo = (() => {
        if (repoInfo.status === 'rejected') {
            return '';
        }
        return repoInfo.value.repoName;
    })();

    const path = document ? vscode.workspace.asRelativePath(document.uri, false) : '';

    const version = (() => {
        if (contextRes.status === 'rejected') {
            return '';
        }
        const currentVersion: string = contextRes.value.extension.packageJSON.version;
        return currentVersion;
    })();

    const key = isSaasOrPoc
        ? iocContainer.get(VSCodeConfigProvider).getLicense()
        : undefined;

    const device = isSaasOrPoc
        ? await getDeviceUUIDThrottled() ?? ''
        : undefined;

    const params = {
        key,
        device,
        ide: getIdeName(),
        username,
        repo,
        path,
        content: document?.getText() ?? '',
        row: `${position.line + 1}`,
        col: `${position.character + 1}`,
        pluginVersion: version,
    };

    return {type: 'success', value: params};
}

export type GeneratorReturn = AsyncGenerator<RawGenerateCode[], void, void>;

const isValidData = (position: vscode.Position, responseData?: Partial<RawGenerateCode>) => {
    if (
        !responseData
        || !responseData.content
        || !responseData.range
        || !responseData.uuid
    ) {
        return false;
    }
    const [startLine, startCharacter, endLine] = responseData.range;
    if (startLine !== endLine || startCharacter - 1 !== position.character) {
        return false;
    }

    return true;
};

export async function fetchWithParams(params: GenerateCodeOptions) {
    const editor = vscode.window.activeTextEditor;
    const line = editor?.selection.start.line;
    const uri = editor?.document.uri;
    const result = await generateCode(params);
    const data = result.data;
    const tracker = iocContainer.get<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker);
    if (data.status === 'OK') {
        tracker.trackSuccess(uri, line);
        return data.data as RawGenerateCode;
    }
    // 如果是非forbidden的错误，则生成失败
    if (!isForbidden(data)) {
        tracker.trackFailure(uri, line);
    }
    throw new Error(data.message);
}

// eslint-disable-next-line complexity, max-statements
function castRewriteToCompletion(
    document: vscode.TextDocument,
    position: vscode.Position,
    rewrite: RewriteBaseResult
): RawGenerateCode | null {
    const rewriteStartLine = rewrite.startRow - 1;
    const rewriteEndLine = rewrite.endRow - 1;
    // 光标不在改写范围
    if (position.line < rewriteStartLine || position.line > rewriteEndLine) {
        return null;
    }
    const oldText = document.getText(
        new vscode.Range(rewriteStartLine, 0, rewriteEndLine, document.lineAt(rewriteEndLine).text.length)
    );
    const newText = rewrite.generatedContent;
    if (newText.length <= oldText.length) {
        return null;
    }
    const oldLines = oldText.split(/\r?\n/);
    const newLines = newText.split(/\r?\n/);
    if (newLines.length < oldLines.length) {
        return null;
    }
    const normalizedPosition = position.with(position.line - rewriteStartLine);

    const firstN = normalizedPosition.line;
    const lastN = oldLines.length - 1 - normalizedPosition.line;

    for (let i = 0; i < firstN; i++) {
        if (newLines.at(i) !== oldLines.at(i)) {
            return null;
        }
    }
    for (let i = 1; i < lastN + 1; i++) {
        if (newLines.at(-i) !== oldLines.at(-i)) {
            return null;
        }
    }
    const oldCursorLine = oldLines[normalizedPosition.line];
    const newCursorLine = newLines[normalizedPosition.line];
    const oldCursorLineSuffix = oldCursorLine.slice(normalizedPosition.character);
    const newCursorLineSuffix = newCursorLine.slice(normalizedPosition.character);
    if (oldCursorLine.slice(0, normalizedPosition.character) !== newCursorLine.slice(0, normalizedPosition.character)) {
        return null;
    }
    if (oldCursorLineSuffix.length > 0 && !checkSubsequence(newCursorLineSuffix, oldCursorLineSuffix)) {
        return null;
    }
    const insertedLines = [
        newCursorLineSuffix,
        ...newLines.slice(firstN + 1, lastN ? -lastN : undefined),
    ];
    return {
        uuid: rewrite.uuid,
        content: insertedLines.join('\n'),
        range: [
            position.line + 1,
            position.character + 1,
            position.line + 1,
            position.character + 1,
        ],
        score: 0,
    };
}

export interface InlineCompletionResult {
    kind: 'completion';
    value: RawGenerateCode;
}

export interface InlineRewriteResult {
    kind: 'rewrite';
    value: RewriteBaseResult;
}

export type InlineEditResult = InlineCompletionResult | InlineRewriteResult;

export async function fetchInlineEdit(
    document: vscode.TextDocument,
    position: vscode.Position,
    selectedCompletionInfo: vscode.SelectedCompletionInfo | undefined,
    params: GenerateCodeOptions,
    rewriteHandler?: RewriteHandler,
    cancelToken?: vscode.CancellationToken
): Promise<InlineEditResult> {
    if (rewriteHandler && rewriteHandler.getEnabled()) {
        const startLine = Math.max(0, position.line - 1);
        const endLine = Math.min(position.line + 2, document.lineCount - 1);
        const [rewriteStartLine, rewriteEndLine] = adjustRewriteRange(document, position, startLine, endLine);
        if (!rewriteHandler.shouldSkip(document.uri, rewriteStartLine, rewriteEndLine)) {
            const context = await rewriteHandler.contextTracker.getContext(document.uri, position);
            const result = await apiRewriteCodeBlock(
                {
                    ...params,
                    diffList: JSON.stringify(formatForRequest(context)),
                    triggerSource: TriggerSource.EditCode,
                    actionType: ActionType.Rewrite,
                    rewriteSpecificStartRow: rewriteStartLine + 1,
                    rewriteSpecificEndRow: rewriteEndLine + 1,
                },
                1000 * 20,
                cancelToken
            );
            if (result.modiType === 'rewrite') {
                // eslint-disable-next-line max-depth
                if (!selectedCompletionInfo) {
                    // TODO: 有候选项时，发给模型的原文会有拼接，这里转化逻辑没有考虑这类情况，无法正确计算出 insertedText @tianzerun
                    const completion = castRewriteToCompletion(
                        document,
                        position,
                        result
                    );
                    // eslint-disable-next-line max-depth
                    if (completion) {
                        // 如果这个改写可以转为续写，则改为续写结果
                        return {
                            kind: 'completion',
                            value: completion,
                        };
                    }
                }
                return {
                    kind: 'rewrite',
                    value: result,
                };
            }
            return {
                kind: 'completion',
                value: {
                    uuid: result.uuid,
                    content: result.generatedContent,
                    range: result.range,
                    score: result.score,
                },
            };
        }
    }
    return {
        kind: 'completion',
        value: await fetchWithParams(params),
    };
}

export async function fetchCompletionBatch(params: GenerateCodeOptions, position: vscode.Position) {
    const editor = vscode.window.activeTextEditor;
    const line = editor?.selection.start.line;
    const uri = editor?.document.uri;

    const result = await generateBatchCode(params);
    const data = result.data;

    const tracker = iocContainer.get<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker);

    if (data.status !== 'OK') {
        tracker.trackFailure(uri, line);
        throw new Error(data.message);
    }

    const completions: RawGenerateCode[] = [];
    for (const item of data.data ?? []) {
        if (isValidData(position, item)) {
            completions.push(item as RawGenerateCode);
        }
    }

    tracker.trackSuccess(uri, line);
    return completions;
}

const batchSize = 2;

export async function fetchAndStreamCompletions(
    document: vscode.TextDocument,
    position: vscode.Position,
    useService: UserService,
    count: number
): Promise<SuccessReturn<GeneratorReturn> | UnsuccessReturn> {
    const params = await buildParams(document, position, useService);
    if (params.type !== 'success') {
        return params;
    }
    const tasks: Array<Promise<RawGenerateCode[]>> = [];
    // 写死 10 个，后端现在 batch 接口是 2 条
    for (let i = 0; i < count / batchSize; i++) {
        tasks.push(fetchCompletionBatch(params.value, position));
    }

    const processResult = new TasksProcessor(tasks).processSSE();

    return {
        type: 'success',
        value: processResult,
    };
}

export type StreamResponse = Partial<RawGenerateCode> & GenerateCodeResponse;

export async function processIncomingMessage(
    data: IncomingMessage,
    update: (content: string, uuid: string, chatId: string) => void,
    cancellationToken?: vscode.CancellationToken
) {
    const processor = new SSEProcessor<StreamResponse>(data, cancellationToken);
    let uuid = '';
    let content = '';
    let chatId = '';
    const updateContent = (data: Partial<RawGenerateCode>, completed = false) => {
        if (data.uuid && !uuid) {
            uuid = data.uuid;
            debug('chat response uuid:', uuid);
        }
        if (data.chatId && !chatId) {
            chatId = data.chatId;
        }
        if (data.content) {
            content += data.content;
            // WARN 为什么结束了不更新：我们现在的实现都是等流结束了使用的return的内容更新，update里就不需要更新了，我们的调用现在是按这个逻辑来的
            !completed && update(content, uuid, chatId);
        }
    };
    try {
        for await (const chunk of processor.processSSE()) {
            if (showForbiddenMessage(chunk as any) || chunk.message) {
                processor.error = true;
                processor.errorMsg = chunk.message || (chunk as any);
                processor.cancel();
                throw new Error((chunk as GenerateCodeResponse).message);
            }
            updateContent(chunk.data ? chunk.data : chunk, data.complete);
        }
        return {content, uuid, processor, chatId};
    }
    catch (e: any) {
        // 临时处理生成到一半然后异常的情况
        if (content || e.message) {
            error('request failed: ', e.message);
            return {content, uuid, processor, chatId};
        }
        throw e;
    }
}

export async function fetchAndStreamCode(
    params: GenerateCodeOptions,
    update: (content: string, uuid: string, chatId: string) => void,
    cancellationToken?: vscode.CancellationToken,
    cancelToken?: CancelToken
) {
    const res = await generateStreamCode(params, cancelToken);
    return processIncomingMessage(res.data, update, cancellationToken);
}
