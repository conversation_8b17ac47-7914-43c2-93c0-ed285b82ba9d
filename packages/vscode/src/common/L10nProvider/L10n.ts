import {readFileSync} from 'fs';
import {resolve} from 'path';
import {capitalize} from 'lodash';
import 'reflect-metadata';
import {config, t, L10nReplacement} from '@vscode/l10n';
import {ExtensionDisplayLanguages} from '@shared/protocols';
import {error} from '../outputChannel';
import {GlobalText, TranslationKey} from './constants';

const getBundlePath = (language: string) => {
    if (language === 'en') {
        return resolve(__dirname, '..', 'l10n', 'bundle.l10n.json');
    }
    if ($features.ENTERPRISE_VERSION === 'gitee') {
        return resolve(__dirname, '..', 'l10n', 'bundle-gitee.i10n.zh-cn.json');
    }
    return resolve(__dirname, '..', 'l10n', 'bundle.l10n.zh-cn.json');
};

const loadResource = (language: string) => {
    const path = getBundlePath(language);
    return readFileSync(path).toString();
};

class L10nController {
    private language: ExtensionDisplayLanguages | undefined = undefined;

    /**
     * 翻译文本
     *
     * @param key 翻译键
     * @param args 可选参数，替换文本中的占位符
     * @returns 翻译后的文本, 若未找到对应文本，则返回空字符串
     */
    t(key: TranslationKey, ...args: L10nReplacement[]) {
        if (!this.language) {
            error(`translation not initialized, failed to translate: ${key}`);
            return '';
        }
        return t(key, ...args);
    }

    /**
     * 和翻译函数 t 一样，但会将英文的首字母大写
     *
     * @param key 翻译键，即需要翻译的字符串的标识符。
     * @param args 替换参数列表，用于替换翻译字符串中的占位符。
     */
    ti(key: TranslationKey, ...args: L10nReplacement[]) {
        const text = this.t(key, ...args);
        if (this.isEnglish) {
            return capitalize(text);
        }
        return text;
    }

    init(language: ExtensionDisplayLanguages) {
        this.updateLanguage(language);
    }

    updateLanguage(language: ExtensionDisplayLanguages) {
        try {
            const contents = loadResource(language);
            config({contents});
            this.language = language;
        }
        catch (e) {
            error(`failed to load l10n resource: ${language}`);
        }
    }

    get currentLanguage() {
        return this.language;
    }

    get isEnglish() {
        return this.language === 'en';
    }
}

export const L10n = new L10nController();

export const getGenerationFailureText = (error?: string) =>
    L10n.t(
        GlobalText.COMMON_GENERATE_ERROR,
        error ?? L10n.t(GlobalText.COMMON_UNKNOWN_ERROR)
    );

export const getRequestFailureText = (error?: string) =>
    L10n.t(
        GlobalText.COMMON_REQUEST_ERROR,
        error ?? L10n.t(GlobalText.COMMON_UNKNOWN_ERROR)
    );
