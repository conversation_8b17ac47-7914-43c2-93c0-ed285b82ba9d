import {injectable} from 'inversify';
import * as vscode from 'vscode';
import 'reflect-metadata';
import {CONTEXT_SHOW_FEEDBACK, CONTEXT_SHOW_PLUGIN_CONFIG} from '@/constants';
import {IL10nProvider} from './types';
import {L10n} from './L10n';

@injectable()
export class EmptyL10nProvider implements IL10nProvider {
    constructor() {
        // 默认只有中文
        L10n.init('zh');
        vscode.commands.executeCommand('setContext', CONTEXT_SHOW_FEEDBACK, true);
        vscode.commands.executeCommand('setContext', CONTEXT_SHOW_PLUGIN_CONFIG, true);
    }

    dispose() {
        // do nothing
    }
}
