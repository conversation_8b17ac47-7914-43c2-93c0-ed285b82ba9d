import path from 'path';
import Parser from 'web-tree-sitter';
import {SymbolKind} from 'vscode-languageserver-types';
import {
    checkTreeSitterSupport,
    getFirstPrecedingComment,
    isJsFamiliy,
    parseTreeSitter,
    queryExports,
    TreeSitterLanguage,
} from '../../utils/treeSitterUtils';
import {readFile, mtime as getMtime} from './fileSystem';
import {CursorHistoryFile} from './types';

interface ImportSnippetItem {
    snippet: string;
    uri: string;
}

function getIndexFilePath(file: string) {
    const suffix = path.extname(file);
    return path.join(file.slice(0, -suffix.length), 'index' + suffix);
}

function getRelativeImportPath(uri: string, node: Parser.SyntaxNode, languageId: string): string | null {
    const suffix = languageId === 'javascript' ? '.js' : '.ts';
    let text = node.namedChild(1)?.text.slice(1, -1);
    if (!text || !text.startsWith('.')) {
        return null;
    }
    if (path.extname(text) === '') {
        text += suffix;
    }
    else if (path.extname(text) !== suffix) {
        return null;
    }
    return path.join(path.dirname(uri), text);
}

interface ImportSpecifier {
    name: string;
    alias?: string;
}

function extractImportSpecifiers(node: Parser.SyntaxNode) {
    const res: ImportSpecifier[] = [];
    if (node.namedChild(0)?.type === 'import_clause') {
        const importSpecifier = node.namedChild(0);
        if (!importSpecifier) {
            return res;
        }

        const identifier = importSpecifier.namedChild(0);
        if (!identifier || identifier.type !== 'named_imports') {
            return res;
        }

        for (const node of identifier.namedChildren ?? []) {
            if (node.type !== 'import_specifier') {
                continue;
            }
            const name = node.childForFieldName('name')?.text;
            if (name) {
                const aliasName = node.childForFieldName('alias')?.text;
                res.push({name: name, alias: aliasName});
            }
        }
    }
    return res;
}

// 提取函数签名
function extractFunctionDeclare(source: string, node: Parser.SyntaxNode) {
    const bodyStartIndex = node.childForFieldName('return_type')?.endIndex
        ?? node.childForFieldName('parameters')?.endIndex;
    if (undefined !== bodyStartIndex) {
        const signature = source.substring(node.startIndex, bodyStartIndex) + ';';
        return node.type === 'function_declaration' || node.type === 'function_signature'
            ? 'declare ' + signature
            : signature;
    }
    return '';
}

export function getDocComment(text: string, node: Parser.SyntaxNode) {
    const firstPrecedingComment = getFirstPrecedingComment(node);
    return firstPrecedingComment
        ? text.substring(firstPrecedingComment.startIndex, node.startIndex)
        : '';
}

function getNodePrefixIndent(source: string, node: Parser.SyntaxNode) {
    let startIndex = node.startIndex - 1;
    for (; startIndex >= 0 && (source[startIndex] === ' ' || source[startIndex] === '\t');) {
        startIndex--;
    }
    if (startIndex < 0 || source[startIndex] === '\n') {
        return source.substring(startIndex + 1, node.startIndex);
    }
    return undefined;
}

// 提取 class 声明
// eslint-disable-next-line complexity
function resolveClassMethod(source: string, node: Parser.SyntaxNode): string {
    if (node?.firstChild?.type === 'accessibility_modifier' && node.firstChild.text === 'private') {
        return '';
    }
    const text = getNodePrefixIndent(source, getFirstPrecedingComment(node) ?? node) ?? '';
    const docComment = getDocComment(source, node);

    switch (node.type) {
        case 'ambient_declaration': {
            const firstNode = node.namedChild(0);
            return firstNode ? text + docComment + resolveClassMethod(source, firstNode) : '';
        }
        case 'method_definition':
        case 'method_signature':
        case 'abstract_method_signature':
            return text + docComment + extractFunctionDeclare(source, node);
        case 'public_field_definition': {
            const endIndex = node.childForFieldName('type')?.endIndex ?? node.childForFieldName('name')?.endIndex;
            if (undefined !== endIndex) {
                return text + docComment + source.substring(node.startIndex, endIndex) + ';';
            }
        }
    }
    return '';
}

function getLexicalDeclarationText(source: string, node: Parser.SyntaxNode) {
    const firstChild = node.namedChild(0);
    if (firstChild?.type !== 'variable_declarator') {
        return undefined;
    }
    const value = firstChild.childForFieldName('value');
    const name = firstChild.childForFieldName('name');
    if (!value || !name || name.type !== 'identifier') {
        return undefined;
    }
    if (value.type === 'arrow_function') {
        const parameters = value.childForFieldName('parameters');
        if (!parameters) {
            return undefined;
        }
        const returnType = value.childForFieldName('return_type')?.firstNamedChild?.text;
        const text = 'declare '
            + source.substring(node.startIndex, name.endIndex)
            + `: ${parameters.text} => `
            + (returnType ?? 'any');
        return {name: name.text, decl: text, kind: SymbolKind.Function};
    }
    return undefined;
}

interface CachedFileExportItem {
    mtime: number;
    exports: Map<string, string>;
}

const fileExportsMap = new Map<string, CachedFileExportItem>();

function declarationNodeTypeToSymbolKind(type: string): SymbolKind | null {
    switch (type) {
        case 'interface_declaration':
            return SymbolKind.Interface;
        case 'enum_declaration':
            return SymbolKind.Enum;
        case 'function_declaration':
            return SymbolKind.Function;
        case 'function_signature':
            return SymbolKind.Function;
        case 'class_declaration':
        case 'abstract_class_declaration':
            return SymbolKind.Class;
        default:
            return null;
    }
}

// eslint-disable-next-line complexity
export function getDeclarationText(
    source: string,
    declarationNode?: Parser.SyntaxNode | null
): {name: string, decl: string, kind: SymbolKind | null} {
    const emptyDeclaration = {name: '', decl: '', kind: null};
    if (!declarationNode) {
        return emptyDeclaration;
    }
    const fieldName = declarationNode.childForFieldName('name')?.text ?? '';
    const symbolKind = declarationNodeTypeToSymbolKind(declarationNode.type);
    switch (declarationNode.type) {
        case 'ambient_declaration':
            return getDeclarationText(source, declarationNode.namedChild(0));
        case 'interface_declaration':
        case 'enum_declaration':
        case 'type_alias_declaration':
            return {
                name: fieldName,
                decl: declarationNode.text,
                kind: symbolKind,
            };
        case 'function_declaration':
        case 'function_signature':
            return {
                name: fieldName,
                decl: extractFunctionDeclare(source, declarationNode),
                kind: symbolKind,
            };
        case 'lexical_declaration': {
            return getLexicalDeclarationText(source, declarationNode) ?? emptyDeclaration;
        }
        case 'class_declaration':
        case 'abstract_class_declaration': {
            const classBody = declarationNode.childForFieldName('body');
            const children = classBody
                ? classBody.namedChildren.map(node => resolveClassMethod(source, node)).filter(v => v)
                : undefined;

            let decl = '';
            if (children && classBody) {
                decl = `declare ${source.substring(declarationNode.startIndex, classBody.startIndex + 1)}`;
                decl += children.map(text => '\n' + text).join('');
                decl += '\n}';
            }
            return {name: fieldName, decl: decl, kind: symbolKind};
        }
    }

    return {name: fieldName, decl: '', kind: symbolKind};
}

async function extractExportFromFile(
    languageId: TreeSitterLanguage,
    rootNode: Parser.SyntaxNode,
    source: string,
    exportsMap: Map<string, string>
) {
    for (const exportStatement of queryExports(languageId, rootNode)) {
        for (const capture of exportStatement.captures) {
            const node = capture.node;
            if (node.type !== 'export_statement') {
                continue;
            }
            const declarationNode = node.childForFieldName('declaration');
            if (!declarationNode || declarationNode.hasError()) {
                continue;
            }
            const {name, decl} = getDeclarationText(source, declarationNode);
            if (!name || !decl) {
                continue;
            }
            const text = getDocComment(source, node) + decl;
            exportsMap.set(name, text);
        }
    }
}

// eslint-disable-next-line complexity
async function getFileValidExports(
    uri: string,
    languageId: TreeSitterLanguage,
    maxLength = 1e5
): Promise<Map<string, string>> {
    const exportMap = new Map<string, string>();
    let mtime = -1;
    let fileUri = uri;
    const [fileRes, indexFileRes] = await Promise.allSettled([
        getMtime(uri),
        // 同时尝试获取index.ts/js的内容
        getMtime(getIndexFilePath(uri)),
    ]);
    if (fileRes.status === 'fulfilled') {
        mtime = fileRes.value;
    }
    else if (indexFileRes.status === 'fulfilled') {
        mtime = indexFileRes.value;
        fileUri = getIndexFilePath(uri);
    }
    if (mtime === -1) {
        return exportMap;
    }
    const cachedFileExports = fileExportsMap.get(fileUri);
    if (cachedFileExports && cachedFileExports.mtime === mtime) {
        return cachedFileExports.exports;
    }
    if (isJsFamiliy(languageId)) {
        let tree: Parser.Tree | null = null;
        try {
            const content = (await readFile(fileUri)).toString();
            if (content.length <= maxLength) {
                tree = await parseTreeSitter(languageId, content);
                await extractExportFromFile(languageId, tree.rootNode, content, exportMap);
            }
        }
        catch {
            // noop
        }
        finally {
            tree && tree.delete();
        }
    }
    if (fileExportsMap.size > 500) {
        for (const key of fileExportsMap.keys()) {
            fileExportsMap.delete(key);
            if (fileExportsMap.size <= 250) {
                break;
            }
        }
    }
    fileExportsMap.set(fileUri, {
        mtime: mtime,
        exports: exportMap,
    });
    return exportMap;
}

const importRegExp = /^\s*import\s*(type|)\s*\{[^}]*\}\s*from\s*['"]\./gm;
function findRelativeImportEnd(source: string) {
    // eslint-disable-next-line @typescript-eslint/init-declarations
    let match;
    let lastIndex = -1;
    importRegExp.lastIndex = -1;
    do {
        match = importRegExp.exec(source);
        if (match) {
            lastIndex = importRegExp.lastIndex + match.length;
        }
    }
    while (match);
    if (lastIndex === -1) {
        return -1;
    }
    const nextNewLineIndex = source.indexOf('\n', lastIndex);
    return nextNewLineIndex === -1 ? source.length : nextNewLineIndex;
}

function getImportStatements(rootNode: Parser.SyntaxNode) {
    const res: Parser.SyntaxNode[] = [];
    for (const node of rootNode.namedChildren) {
        node.type === 'import_statement' && res.push(node);
    }
    return res;
}

async function extractTSImport(thisFile: CursorHistoryFile, languageId: TreeSitterLanguage) {
    const {source, uri} = thisFile;
    const res: ImportSnippetItem[] = [];
    const importStatementEnd = findRelativeImportEnd(source);
    if (importStatementEnd === -1) {
        return res;
    }
    const importStatementText = source.substring(0, importStatementEnd);
    // 只解析 import 相关的代码
    const tree = await parseTreeSitter(languageId, importStatementText);

    try {
        for (const importNode of getImportStatements(tree.rootNode)) {
            // 只拿相对路径的 import
            const importFileUri = getRelativeImportPath(uri, importNode, languageId);
            if (!importFileUri) {
                continue;
            }
            const specifiers = extractImportSpecifiers(importNode);
            const fileExports = await getFileValidExports(importFileUri, languageId);
            // 如果是整个文件引入的话，先不处理
            // if (specifiers.length === 0) {
            //     // eslint-disable-next-line max-depth
            //     for (const item of fileExports.values()) {
            //         res.push(...item.map(v => ({snippet: v, uri: importFileUri})));
            //     }
            // }

            for (const specifier of specifiers) {
                const text = fileExports.get(specifier.name);
                text && res.push({snippet: text, uri: importFileUri});
            }
        }
    }
    finally {
        tree.delete();
    }
    return res;
}

export async function extractLocalImportContext(thisFile: CursorHistoryFile): Promise<ImportSnippetItem[]> {
    const languageId = checkTreeSitterSupport(thisFile.languageId);
    return languageId && isJsFamiliy(languageId)
        ? extractTSImport(thisFile, languageId)
        : [];
}
