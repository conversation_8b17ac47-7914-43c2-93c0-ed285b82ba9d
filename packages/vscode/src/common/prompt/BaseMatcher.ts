import {splitIntoWords} from '@/utils/textSimilarity';
import {CursorHistoryFile, FileDescription} from './types';

export enum SnippetSelectionOption {
    BestMatch = 'bestMatch',
    TopK = 'topK',
}

export enum SortOptions {
    Ascending = 'ascending',
    Descending = 'descending',
    None = 'none',
}

// eslint-disable-next-line max-len
const commonWords = new Set([
    'we',
    'our',
    'you',
    'it',
    'its',
    'they',
    'them',
    'their',
    'this',
    'that',
    'these',
    'those',
    'is',
    'are',
    'was',
    'were',
    'be',
    'been',
    'being',
    'have',
    'has',
    'had',
    'having',
    'do',
    'does',
    'did',
    'doing',
    'can',
    'don',
    't',
    's',
    'will',
    'would',
    'should',
    'what',
    'which',
    'who',
    'when',
    'where',
    'why',
    'how',
    'a',
    'an',
    'the',
    'and',
    'or',
    'not',
    'no',
    'but',
    'because',
    'as',
    'until',
    'again',
    'further',
    'then',
    'once',
    'here',
    'there',
    'all',
    'any',
    'both',
    'each',
    'few',
    'more',
    'most',
    'other',
    'some',
    'such',
    'above',
    'below',
    'to',
    'during',
    'before',
    'after',
    'of',
    'at',
    'by',
    'about',
    'between',
    'into',
    'through',
    'from',
    'up',
    'down',
    'in',
    'out',
    'on',
    'off',
    'over',
    'under',
    'only',
    'own',
    'same',
    'so',
    'than',
    'too',
    'very',
    'just',
    'now',
]);
// eslint-disable-next-line max-len
const keywords = new Set([
    'if',
    'then',
    'else',
    'for',
    'while',
    'with',
    'def',
    'function',
    'return',
    'TODO',
    'import',
    'try',
    'catch',
    'raise',
    'finally',
    'repeat',
    'switch',
    'case',
    'match',
    'assert',
    'continue',
    'break',
    'const',
    'class',
    'enum',
    'struct',
    'static',
    'new',
    'super',
    'this',
    'var',
    ...commonWords,
]);
const keywordsForLanguageMap = new Map<string, typeof keywords>([]);

export class Tokenizer {
    private readonly filterForLanguage: Set<string>;

    constructor(file: FileDescription) {
        this.filterForLanguage = keywordsForLanguageMap.get(file.languageId) ?? keywords;
    }

    tokenize(text: string) {
        return new Set(splitIntoWords(text).filter(word => !this.filterForLanguage.has(word)));
    }
}

class TokenCache {
    private readonly keys: string[];
    private readonly cache: Record<string, Array<Set<string>>>;
    private readonly size: number;

    constructor(size: number) {
        this.keys = [];
        this.cache = {};
        this.size = size;
    }

    put(id: string, item: Array<Set<string>>) {
        this.cache[id] = item;

        if (this.keys.length > this.size) {
            this.keys.push(id);
            const eliminated = this.keys.shift() ?? '';
            delete this.cache[eliminated];
        }
    }

    get(id: string) {
        return this.cache[id];
    }
}

const fileTokenCache = new TokenCache(20);

interface ScoredWindow {
    score: number;
    startLine: number;
    endLine: number;
}

export interface ScoredSnippetInfo extends ScoredWindow {
    snippet: string;
}

export abstract class WindowedMatcher {
    readonly tokenizer: Tokenizer;
    // 基线文档里所有的token
    readonly referenceTokens: Set<string>;

    constructor(thisFile: CursorHistoryFile) {
        this.tokenizer = new Tokenizer(thisFile);
        this.referenceTokens = this.tokenizer.tokenize(this.trimDocument(thisFile));
    }

    protected abstract id(): string;

    protected abstract getWindowsDelineations(lines: string[]): Array<[startLine: number, endLine: number]>;

    protected abstract trimDocument(file: CursorHistoryFile): string;

    protected abstract similarityScore(tokens: Set<string>, referenceTokens: Set<string>): number;

    sortScoredSnippets(input: ScoredWindow[], sort = SortOptions.Descending) {
        return sort === SortOptions.Ascending
            ? input.sort((a, b) => (a.score > b.score ? 1 : -1))
            : sort === SortOptions.Descending
            ? input.sort((a, b) => (a.score > b.score ? -1 : 1))
            : input;
    }

    retrieveAllSnippets(file: FileDescription, sortType = SortOptions.Descending) {
        const results: ScoredWindow[] = [];

        if (file.source.length === 0 || this.referenceTokens.size === 0) {
            return results;
        }

        const lines = file.source.split('\n');
        const id = this.id() + ':' + file.source;
        const cachedItem = fileTokenCache.get(id) ?? [];
        const isCacheEmpty = cachedItem.length === 0;
        const tokensInLines = isCacheEmpty ? lines.map(this.tokenizer.tokenize, this.tokenizer) : [];

        for (const [windowIndex, [startLine, endLine]] of this.getWindowsDelineations(lines).entries()) {
            if (isCacheEmpty) {
                const windowTokens = new Set<string>();
                // 取对应窗口的内容
                tokensInLines.slice(startLine, endLine).forEach(line => {
                    line.forEach(windowTokens.add, windowTokens);
                });
                cachedItem.push(windowTokens);
            }
            const currentWindowTokens = cachedItem[windowIndex];
            // 与全文件的内容做相似度比较
            const score = this.similarityScore(currentWindowTokens, this.referenceTokens);
            results.push({
                score: score,
                startLine: startLine,
                endLine: endLine,
            });
        }
        if (isCacheEmpty) {
            fileTokenCache.put(id, cachedItem);
        }
        return this.sortScoredSnippets(results, sortType);
    }

    async findTopKMatches(file: FileDescription, k = 1): Promise<ScoredSnippetInfo[] | undefined> {
        if (file.source.length === 0 || this.referenceTokens.size === 0 || k < 1) {
            return undefined;
        }

        const lines = file.source.split('\n');
        const snippets = this.retrieveAllSnippets(file, SortOptions.Descending);

        if (snippets.length === 0 || snippets[0].score === 0) {
            return undefined;
        }

        const results = [snippets[0]];
        for (let i = 1; i < snippets.length && results.length < k; i++) {
            const snippet = snippets[i];
            // 如果有交集就不要
            if (results.findIndex(v => snippet.startLine < v.endLine && snippet.endLine > v.startLine) === -1) {
                results.push(snippet);
            }
        }
        return results.map(item => ({
            snippet: lines.slice(item.startLine, item.endLine).join('\n'),
            ...item,
        }));
    }
}
