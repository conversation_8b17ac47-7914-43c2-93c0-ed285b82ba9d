import {Container} from 'inversify';
import * as vscode from 'vscode';
import {TextDocumentManager} from '../../services/Helper/TextDocumentManager';

export const accessTimes = new Map();

export function sortByAccessTimes(documents: readonly vscode.TextDocument[]) {
    return [...documents].sort((a, b) => {
        const accessTimeA = accessTimes.get(a.uri.toString()) ?? 0;
        const accessTimeB = accessTimes.get(b.uri.toString()) ?? 0;
        return accessTimeB - accessTimeA;
    });
}

export function registerDocumentTracker(globalContext: Container) {
    return globalContext.get(TextDocumentManager).onDidFocusTextDocument(editor => {
        if (editor) {
            accessTimes.set(editor.document.uri.toString(), Date.now());
        }
    });
}

export interface CursorHistory {
    uri: string;
    offset: number;
    timestamp: number;
}

export const cursorHistoryStack: CursorHistory[] = [];

export function registerCursorTracker(globalContext: Container) {
    return globalContext
        .get(TextDocumentManager)
        .onDidChangeCursor(event => {
            try {
                // 这个判断当前没有选上一段内容，只是单纯的光标在某个位置
                if (
                    event
                    && event.selections.length > 0
                    && event.selections[0].active.line === event.selections[0].anchor.line
                    && event.selections[0].active.character === event.selections[0].anchor.character
                ) {
                    cursorHistoryStack.push({
                        uri: event.textEditor.document.uri.toString(),
                        offset: event.textEditor.document.offsetAt(event.selections[0].active),
                        timestamp: Date.now(),
                    });

                    // 最多1000条
                    if (cursorHistoryStack.length > 1000) {
                        cursorHistoryStack.shift();
                    }
                }
            }
            catch {
                // noop
            }
        });
}
