import {WindowedMatcher} from './BaseMatcher';
import {CursorHistoryFile} from './types';

export function computeScore(sourceTokenSet: Set<string>, targetTokenSet: Set<string>) {
    // 2个列表中重复的数量 / 去重后的数量
    const intersectionSet = new Set();
    sourceTokenSet.forEach(v => targetTokenSet.has(v) && intersectionSet.add(v));
    return intersectionSet.size / (sourceTokenSet.size + targetTokenSet.size - intersectionSet.size);
}

export class FixedWindowSizeJaccardMatcher extends WindowedMatcher {
    private readonly windowLength: number;

    static FACTORY(windowLength: number) {
        return {
            to: (file: CursorHistoryFile) => {
                return new FixedWindowSizeJaccardMatcher(file, windowLength);
            },
        };
    }
    constructor(file: CursorHistoryFile, windowLength: number) {
        super(file);
        this.windowLength = windowLength;
    }

    id() {
        return 'fixed:' + this.windowLength;
    }

    getWindowsDelineations(lines: string[]) {
        // 假如一个文件有10行，`windowLength`为4，就是以4行为一个窗口，从上往下扫描，最终输出：
        //
        // ```
        // [0, 4]
        // [1, 5]
        // [2, 6]
        // [3, 7]
        // [4, 8]
        // [5, 9]
        // ```
        const results: Array<[number, number]> = [];
        const linesCount = lines.length;
        for (let startLine = 0; startLine === 0 || startLine < linesCount - this.windowLength; startLine++) {
            const endLine = Math.min(startLine + this.windowLength, linesCount);
            results.push([startLine, endLine]);
        }
        return results;
    }

    trimDocument(file: CursorHistoryFile) {
        return file.source.slice(0, file.offset).split('\n').slice(-this.windowLength).join('\n');
    }

    similarityScore(sourceTokenSet: Set<string>, targetTokenSet: Set<string>) {
        return computeScore(sourceTokenSet, targetTokenSet);
    }
}
