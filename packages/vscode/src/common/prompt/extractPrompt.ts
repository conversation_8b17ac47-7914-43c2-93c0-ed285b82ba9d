import * as vscode from 'vscode';

const MIN_PROMPT_CHARS = 15;

export interface PromptInfo {
    prefix: string;
    suffix: string;
    isFimEnabled: boolean;
    trailingWs: string;
}

export interface PromptExtracted {
    type: 'prompt';
    prompt: PromptInfo;
}

interface ContextTooShort {
    type: 'contextTooShort';
    prompt: PromptInfo;
}

export interface NotebookContext {
    type: 'notebookContext';
    prompt: PromptInfo;
    fullText: string;
    position: vscode.Position;
}

export type PromptContext = ContextTooShort | NotebookContext | PromptExtracted;

// trim 掉最后一行，只会整行去掉，如果最后一行不全是空格是不会去掉的
// 主要用于判断光标所在这一行，光标前面是否都是空格
export function trimLastLine(text: string) {
    const lines = text.split('\n');
    const lastLine = lines[lines.length - 1];
    const wsLength = lastLine.length - lastLine.trimEnd().length;
    const prefix = text.slice(0, text.length - wsLength);
    const trailingWs = text.slice(prefix.length);
    const isLastLineEmpty = lastLine.length === wsLength;
    return isLastLineEmpty ? [prefix, trailingWs] : [text, ''];
}

export function findNotebook(document: vscode.TextDocument) {
    const notebook = vscode.workspace.notebookDocuments.find(notebook => {
        return notebook.getCells().some(cell => cell.document === document);
    });
    return notebook;
}

export function getPromptHelper(fullText: string, offset: number): PromptContext {
    const textBeforePos = fullText.slice(0, offset);
    const textAfterPos = fullText.slice(offset);

    const [prefix, trailingWs] = trimLastLine(textBeforePos);

    const type = fullText.length < MIN_PROMPT_CHARS
        ? 'contextTooShort'
        : 'prompt';

    return {
        type,
        prompt: {
            prefix,
            suffix: textAfterPos,
            isFimEnabled: false, // 为啥一直是false
            trailingWs,
            // prefixTokens,
            // suffixTokens,
        },
        // TODO 改了很多，先这么试一下
        // trailingWs,
        // promptChoices,
        // computeTimeMs,
        // promptBackground,
        // neighborSource,
    };
}

function getPromptForRegularDoc(document: vscode.TextDocument, position: vscode.Position) {
    return getPromptHelper(document.getText(), document.offsetAt(position));
}

function getPromptForNotebook(
    notebook: vscode.NotebookDocument,
    document: vscode.TextDocument,
    position: vscode.Position
) {
    const currentCell = notebook.getCells().find(v => v.document.uri.fsPath === document.uri.fsPath);
    if (!currentCell) {
        return getPromptForRegularDoc(document, position);
    }
    const previousCells = notebook.getCells().filter(v => (
        v.index < currentCell.index && v.document.languageId === currentCell.document.languageId
    ));
    const previousCellsText = previousCells.length > 0
        ? previousCells.map(v => v.document.getText()).join('\n\n') + '\n\n'
        : '';
    const fullText = previousCellsText + document.getText();
    const offset = previousCellsText.length + document.offsetAt(position);
    const prompt = getPromptHelper(fullText, offset);
    const relativePosition = new vscode.Position(
        fullText.slice(0, offset).split('\n').length - 1,
        position.character
    );
    if (prompt.type === 'prompt') {
        return {
            type: 'notebookContext',
            prompt: prompt.prompt,
            fullText: fullText,
            position: relativePosition,
        } as NotebookContext;
    }
    return prompt;
}

export function extractPrompt(document: vscode.TextDocument, position: vscode.Position): PromptContext {
    const notebook = findNotebook(document);
    return notebook === undefined
        ? getPromptForRegularDoc(document, position)
        : getPromptForNotebook(notebook, document, position);
}
