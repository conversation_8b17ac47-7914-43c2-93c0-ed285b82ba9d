/* eslint-disable new-cap */
import {Container} from 'inversify';
import * as vscode from 'vscode';
import {UserService} from '../../services/UserService';
import {trackEvent} from '../../utils/tracker';
import {modifyCode} from '../../api';
import {TextDocumentManager, tryGetTextDocument} from '../../services/Helper/TextDocumentManager';
import {ChangeTracker} from '../ChangeTracker';
import {ScoredSnippetInfo, WindowedMatcher} from './BaseMatcher';
import {extractLocalImportContext} from './extractLocalImportContext';
import {FixedWindowSizeJaccardMatcher} from './JaccardMatcher';
import {NeighborSource, CursorHistoryStrategy} from './NeighborSource';
import {CursorHistoryFile, FileDescription} from './types';

const SNIPPET_LENGTH = 60;

const MAX_CAPTURED_CODE_LINE = 60;

const MAX_FIXED_WINDOW_SNIPPET_SIZE = 4;

export interface SnippetItem {
    content: string;
    filePath: string;
    type: 'NEIGHBOR_SOURCE' | 'IMPORT_SOURCE';
}

async function captureAndNotifyCode(globalContext: Container, uri: vscode.Uri, trackOffset: number, uuid: string) {
    const textDocument = await tryGetTextDocument(globalContext.get(TextDocumentManager), uri.toString());
    if (!textDocument) {
        return;
    }
    const text = textDocument.getText();
    const suffix = text.substring(trackOffset);
    const capturedActualContext = suffix.split('\n').slice(0, MAX_CAPTURED_CODE_LINE).join('\n');
    modifyCode({uuid, capturedActualContext});
}

interface ScoredSnippet extends ScoredSnippetInfo {
    uri: string;
}

async function getFileBestMatch(sourceFiles: FileDescription[], matcher: WindowedMatcher) {
    const res: ScoredSnippet[] = [];
    await Promise.allSettled(sourceFiles.map(async file => {
        const snippets = await matcher.findTopKMatches(file, MAX_FIXED_WINDOW_SNIPPET_SIZE);
        if (snippets) {
            res.push(...snippets.map(v => ({...v, uri: file.uri})));
        }
    }));
    return res;
}

interface NeighborSnippetsOptions {
    neighborSource: boolean;
    importSource: boolean;
}

export class NeighborSnippets {
    start = Date.now();
    fixedWindowSnippets: SnippetItem[] | undefined = undefined;
    importSnippets: SnippetItem[] | undefined = undefined;
    snippetResults: SnippetItem[] | undefined = undefined;
    docs: FileDescription[] | undefined = undefined;
    thisFile: CursorHistoryFile;

    constructor(
        readonly globalContext: Container,
        readonly document: vscode.TextDocument,
        readonly position: vscode.Position,
        readonly options: NeighborSnippetsOptions = {
            neighborSource: true,
            importSource: true,
        }
    ) {
        const fullText = this.document.getText();
        const offset = this.document.offsetAt(this.position);

        this.thisFile = {
            uri: this.document.uri.toString(),
            source: fullText,
            offset,
            languageId: this.document.languageId,
            fsPath: this.document.uri.fsPath,
        };
        if (this.options.neighborSource) {
            this.extractBestNeighborSnippets();
        }
        if (this.options.importSource) {
            this.extractLocalImport();
        }
    }

    getSnippets() {
        return this.snippetResults;
    }

    captureCode(uuid?: string) {
        if (!uuid) {
            return;
        }
        const changeTracker = new ChangeTracker(
            this.globalContext,
            this.document.uri,
            this.thisFile.offset
        );
        changeTracker.push(
            () => {
                captureAndNotifyCode(
                    this.globalContext,
                    this.document.uri,
                    changeTracker.offset,
                    uuid
                );
            },
            2 * 60 * 1000
        );
    }

    getFixedWindowSnippets() {
        return this.fixedWindowSnippets;
    }

    private async resolve() {
        // undefined 可能是还没计算好
        if (this.options.neighborSource && this.fixedWindowSnippets === undefined) {
            return;
        }
        if (this.options.importSource && this.importSnippets === undefined) {
            return;
        }
        this.snippetResults = [
            ...(this.fixedWindowSnippets ?? []).slice(0, MAX_FIXED_WINDOW_SNIPPET_SIZE),
            ...(this.importSnippets ?? []),
        ];
        const duration = Date.now() - this.start;
        const [username] = await this.globalContext.get(UserService).getCurrentUser();
        trackEvent(username, 'snippet', 'duration', {result: duration.toString()});
    }

    private async extractLocalImport() {
        try {
            const allImports = await extractLocalImportContext(this.thisFile);
            this.importSnippets = allImports.map(v => ({
                filePath: v.uri,
                content: v.snippet,
                type: 'IMPORT_SOURCE',
            }));
        }
        catch {
            // noop
        }
        this.resolve();
    }

    private async extractBestNeighborSnippets() {
        const neighborSourceObj = new NeighborSource(true, CursorHistoryStrategy.None);
        const {docs} = await neighborSourceObj.getNeighborFiles(
            this.globalContext.get(TextDocumentManager),
            this.document.uri,
            this.document.languageId
        );
        this.docs = docs;

        // 小于5万字符
        const sourceValidDocs = docs
            .filter(doc => doc.source.length < 5e4 && doc.source.length > 0)
            .slice(0, 20);

        await this.extractFixedWindowSnippets(this.thisFile, sourceValidDocs);
        this.resolve();
    }

    private async extractFixedWindowSnippets(thisFile: CursorHistoryFile, files: FileDescription[]) {
        try {
            const Matcher = FixedWindowSizeJaccardMatcher.FACTORY(SNIPPET_LENGTH).to(thisFile);
            const snippets = await getFileBestMatch(files, Matcher);
            const sortedSnippets = snippets.filter(v => v.score && v.snippet).sort((x, y) => y.score - x.score);
            this.fixedWindowSnippets = sortedSnippets.map(v => ({
                filePath: v.uri,
                content: v.snippet,
                type: 'NEIGHBOR_SOURCE',
            }));
        }
        catch {
            // noop
        }
    }
}
