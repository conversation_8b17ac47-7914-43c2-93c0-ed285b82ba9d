import * as vscode from 'vscode';
import {TextDocumentManager, tryGetTextDocument} from '../../services/Helper/TextDocumentManager';
import {sortByAccessTimes, cursorHistoryStack, CursorHistory} from './fileTracker';
import {CursorHistoryFile, FileDescription} from './types';

export enum NeighborFileType {
    Open = 'Open',
    Cursor = 'Cursor',
}

export enum CursorHistoryStrategy {
    None = 'none',
    MostRecent = 'mostrecent',
    MostCount = 'mostcount',
    BeforeCurrentFile = 'beforecurrentfile',
}

interface UriCount {
    uri: string;
    offset: number;
    timestamp: number;
    count: number;
    doc: vscode.TextDocument;
}

export class NeighborSource {
    static MAX_NEIGHBOR_AGGREGATE_LENGTH = 200000;
    static MAX_NEIGHBOR_FILES = 20;
    static MAX_MOST_RECENT_TIME_RANGE = 1800000;

    constructor(
        private readonly openFileStrategy: boolean,
        private readonly cursorHistoryStrategy: CursorHistoryStrategy
    ) {}

    async getCursorHistoryFiles(
        textDocumentManager: TextDocumentManager,
        fsPath: string,
        languageId: string,
        cursorHistoryStrategy: CursorHistoryStrategy
    ): Promise<CursorHistoryFile[]> {
        if (cursorHistoryStrategy === CursorHistoryStrategy.None) {
            return [];
        }
        const stack = [...cursorHistoryStack].reverse();
        if (cursorHistoryStrategy === CursorHistoryStrategy.MostRecent) {
            return this.getMostRecentFiles(textDocumentManager, stack, fsPath, languageId);
        }
        else if (cursorHistoryStrategy === CursorHistoryStrategy.BeforeCurrentFile) {
            return this.getFilesBeforeCurrentFile(textDocumentManager, stack, fsPath, languageId);
        }
        else if (cursorHistoryStrategy === CursorHistoryStrategy.MostCount) {
            return this.getMostCountFiles(textDocumentManager, stack, fsPath, languageId);
        }
        return [];
    }

    // 直接按访问顺序获取最近满足条件的文件
    private async getMostRecentFiles(
        textDocumentManager: TextDocumentManager,
        stack: CursorHistory[],
        fsPath: string,
        languageId: string
    ) {
        let sum = 0;
        const uriSet = new Set();
        const files: CursorHistoryFile[] = [];
        for (const {uri, offset} of stack) {
            if (uriSet.has(uri)) {
                continue;
            }
            const document = await tryGetTextDocument(textDocumentManager, uri);
            if (
                document !== undefined
                && !this.fileLengthExceeded(sum, document)
                && document.uri.scheme === 'file'
                && document.uri.fsPath !== fsPath
                && document.languageId === languageId
            ) {
                files.push({
                    uri: uri,
                    languageId: document.languageId,
                    source: document.getText(),
                    fsPath: document.uri.fsPath,
                    offset,
                });
                uriSet.add(uri);
                sum += document.getText().length;

                if (this.fileCountExceeded(files)) {
                    break;
                }
            }
        }
        return files;
    }

    // 根据使用记录，获取从当前文件直接跳过去的文件
    private async getFilesBeforeCurrentFile(
        textDocumentManager: TextDocumentManager,
        stack: CursorHistory[],
        fsPath: string,
        languageId: string
    ) {
        let sum = 0;
        let lastFileName = null;
        const uriSet = new Set();
        const files: CursorHistoryFile[] = [];
        for (const {uri, offset} of stack) {
            if (this.fileCountExceeded(files)) {
                break;
            }
            const document = await tryGetTextDocument(textDocumentManager, uri);
            if (
                document !== undefined
                && !this.fileLengthExceeded(sum, document)
                && document.uri.scheme === 'file'
                && document.languageId === languageId
            ) {
                if (
                    document.uri.fsPath !== fsPath
                    && lastFileName === fsPath
                    && !uriSet.has(uri)
                ) {
                    uriSet.add(uri);
                    files.push({
                        uri: uri,
                        languageId: document.languageId,
                        source: document.getText(),
                        fsPath: document.uri.fsPath,
                        offset,
                    });
                    sum += document.getText().length;
                }
                lastFileName = document.fileName;
            }
        }
        return files;
    }

    // 有效时间内打开访问次数最多的
    // eslint-disable-next-line complexity
    private async getMostCountFiles(
        textDocumentManager: TextDocumentManager,
        stack: CursorHistory[],
        fsPath: string,
        languageId: string
    ) {
        let lastTimestamp = null;
        const files: CursorHistoryFile[] = [];
        const uriCounter = new Map<string, UriCount>();
        for (const {uri, offset, timestamp} of stack) {
            if (lastTimestamp !== null && lastTimestamp - timestamp > NeighborSource.MAX_MOST_RECENT_TIME_RANGE) {
                break;
            }
            const doc = await tryGetTextDocument(textDocumentManager, uri);
            if (
                doc !== undefined
                && doc.uri.scheme === 'file'
                && doc.uri.fsPath !== fsPath
                && doc.languageId === languageId
            ) {
                if (lastTimestamp === null) {
                    lastTimestamp = timestamp;
                }
                const uriCount = uriCounter.get(uri) ?? {
                    uri: uri,
                    offset: offset,
                    timestamp: timestamp,
                    count: 0,
                    doc: doc,
                };
                uriCount.count += 1;
                uriCounter.set(uri, uriCount);
            }
        }
        const filesMostCount = [...uriCounter.entries()].sort((uri, uriCount) => {
            return uriCount[1].count - uri[1].count;
        });
        let sum = 0;
        for (const [uri, uriCount] of filesMostCount) {
            if (this.fileCountExceeded(files)) {
                break;
            }
            const doc = uriCount.doc;
            const offset = uriCount.offset;

            if (!this.fileLengthExceeded(sum, doc)) {
                files.push({
                    uri: uri,
                    fsPath: doc.uri.fsPath,
                    languageId: doc.languageId,
                    source: doc.getText(),
                    offset: offset,
                });
                sum += doc.getText().length;
            }
        }
        return files;
    }

    fileCountExceeded(files: FileDescription[]) {
        return files.length >= NeighborSource.MAX_NEIGHBOR_FILES;
    }

    fileLengthExceeded(sum: number, doc: vscode.TextDocument) {
        return sum + doc.getText().length > NeighborSource.MAX_NEIGHBOR_AGGREGATE_LENGTH;
    }

    // 直接获取满足条件的打开过的文件
    getOpenFiles(textDocumentManager: TextDocumentManager, fsPath: string, languageId: string): FileDescription[] {
        const files: FileDescription[] = [];
        const textDocuments = sortByAccessTimes(textDocumentManager.textDocuments);
        let sum = 0;
        for (const doc of textDocuments) {
            if (this.fileCountExceeded(files) || this.fileLengthExceeded(sum, doc)) {
                break;
            }
            if (
                doc.uri.scheme === 'file'
                && doc.uri.fsPath !== fsPath
                && doc.languageId === languageId
            ) {
                files.push({
                    uri: doc.uri.toString(),
                    languageId: doc.languageId,
                    source: doc.getText(),
                    fsPath: doc.uri.fsPath,
                });
                sum += doc.getText().length;
            }
        }
        return files;
    }

    /**
     * @param textDocumentManager 文档管理器，将 vscode.TextDocument 相关的操作封装起来，方便后面复用
     * @param uri 当前文档的 uri
     * @param languageId 当前文档的 languageId
     * @returns
     */
    async getNeighborFiles(textDocumentManager: TextDocumentManager, uri: vscode.Uri, languageId: string) {
        const files = this.getOpenFiles(textDocumentManager, uri.fsPath, languageId);
        if (this.openFileStrategy) {
            return {
                docs: files,
                neighborSource: new Map([
                    [
                        NeighborFileType.Open,
                        files.map(file => file.uri),
                    ],
                ]),
            };
        }

        const historyFiles = await this.getCursorHistoryFiles(
            textDocumentManager,
            uri.fsPath,
            languageId,
            this.cursorHistoryStrategy
        );

        return {
            docs: historyFiles,
            neighborSource: new Map([
                [
                    NeighborFileType.Open,
                    files.map(file => file.uri),
                ],
                [
                    NeighborFileType.Cursor,
                    historyFiles.map(file => file.uri),
                ],
            ]),
        };
    }
}
