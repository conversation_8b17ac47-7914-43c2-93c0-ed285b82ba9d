import {repeat} from 'lodash';
import * as vscode from 'vscode';

const tabDecorationStyle: vscode.ThemableDecorationAttachmentRenderOptions = {
    textDecoration:
        // eslint-disable-next-line max-len
        'none; white-space: pre; border-radius: 4px; box-sizing: border-box; line-height: normal; padding-top: 1px;',
    color: '#939796',
    border: '1px solid #03CAEE',
    backgroundColor: new vscode.ThemeColor('editor.background'),
    height: '100%',
};

export const createTabToJumpGuide = (): vscode.TextEditorDecorationType => {
    return vscode.window.createTextEditorDecorationType({
        before: {
            ...tabDecorationStyle,
            contentText: ' ↯ Tab ',
        },
    });
};

export const createTabToAcceptGuide = (): vscode.TextEditorDecorationType => {
    return vscode.window.createTextEditorDecorationType({
        before: {
            ...tabDecorationStyle,
            contentText: ' ✓ Tab ',
        },
    });
};

export const createInvisibleTabToAcceptGuide = (): vscode.TextEditorDecorationType => {
    return vscode.window.createTextEditorDecorationType({
        before: {
            ...tabDecorationStyle,
            contentText: ' ✓ Tab ',
            textDecoration: tabDecorationStyle.textDecoration + 'border-radius: 0;',
            color: new vscode.ThemeColor('editor.background'),
            border: '1px solid transparent',
        },
    });
};

export const createEndOfLineWhitespace = (count: number = 1): vscode.TextEditorDecorationType => {
    return vscode.window.createTextEditorDecorationType({
        before: {
            contentText: repeat(' ', count),
            textDecoration: 'none; white-space: pre',
            backgroundColor: new vscode.ThemeColor('editor.background'),
            height: '100%',
        },
    });
};
