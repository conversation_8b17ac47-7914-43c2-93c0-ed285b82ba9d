import * as vscode from 'vscode';
import {Range, Position} from 'vscode-languageserver-textdocument';
import {URI} from 'vscode-uri';
import {ITextDocument, TextLine} from '../types';
import {adaptToVSCodePosition, adaptToVSCodeRange} from './utils';

export class VSCodeTextDocument implements ITextDocument {
    constructor(private readonly document: vscode.TextDocument) {}

    get fileName() {
        return this.document.fileName;
    }

    get uri() {
        return this.document.uri.toString();
    }

    get uriObject() {
        return URI.parse(this.uri);
    }

    get languageId() {
        return this.document.languageId;
    }

    get version() {
        return this.document.version;
    }

    get lineCount() {
        return this.document.lineCount;
    }

    lineAt(line: number | Position): TextLine {
        const lineNumber = typeof line === 'number' ? line : line.line;
        return this.document.lineAt(lineNumber);
    }

    getText(range?: Range): string {
        if (!range) {
            return this.document.getText();
        }
        return this.document.getText(adaptToVSCodeRange(range));
    }

    positionAt(offset: number): Position {
        return this.document.positionAt(offset);
    }

    offsetAt(position: Position): number {
        return this.document.offsetAt(adaptToVSCodePosition(position));
    }
}
