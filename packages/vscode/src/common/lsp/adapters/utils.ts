import * as vscode from 'vscode';
import {
    Range,
    Position,
    WorkspaceFolder,
    Location,
    Diagnostic,
    DiagnosticRelatedInformation,
    DiagnosticSeverity,
} from 'vscode-languageserver-protocol';

export const adaptToVSCodePosition = (position: Position): vscode.Position => {
    return new vscode.Position(position.line, position.character);
};

export function adaptToVSCodeRange(range: Range): vscode.Range;
export function adaptToVSCodeRange(range: undefined): undefined;
export function adaptToVSCodeRange(range: Range | undefined): vscode.Range | undefined {
    if (!range) {
        return undefined;
    }
    return new vscode.Range(adaptToVSCodePosition(range.start), adaptToVSCodePosition(range.end));
}

export const adaptToLspPosition = (position: vscode.Position): Position => {
    return {line: position.line, character: position.character};
};

export function adaptToLspRange(range: vscode.Range): Range;
export function adaptToLspRange(range: undefined): undefined;
export function adaptToLspRange(range: vscode.Range | undefined): Range | undefined {
    if (!range) {
        return undefined;
    }
    return {start: adaptToLspPosition(range.start), end: adaptToLspPosition(range.end)};
}

export function adaptToLspWorkspaceFolder(workspaceFolder: vscode.WorkspaceFolder): WorkspaceFolder {
    return {
        uri: workspaceFolder.uri.toString(),
        name: workspaceFolder.name,
    };
}

export function adaptToLspLocation(location: vscode.Location): Location {
    return Location.create(
        location.uri.toString(),
        adaptToLspRange(location.range)
    );
}

export function adaptToLspDiagnosticSeverity(severity: vscode.DiagnosticSeverity): DiagnosticSeverity {
    return severity + 1 as DiagnosticSeverity;
}

export function adaptToLspDiagnosticRelatedInformation(
    relatedInformation: vscode.DiagnosticRelatedInformation
): DiagnosticRelatedInformation {
    return DiagnosticRelatedInformation.create(
        adaptToLspLocation(relatedInformation.location),
        relatedInformation.message
    );
}

export function adaptToLspDiagnostic(diagnostic: vscode.Diagnostic): Diagnostic {
    return Diagnostic.create(
        adaptToLspRange(diagnostic.range),
        diagnostic.message,
        adaptToLspDiagnosticSeverity(diagnostic.severity),
        typeof diagnostic.code === 'object' ? undefined : diagnostic.code,
        diagnostic.source,
        diagnostic.relatedInformation?.map(adaptToLspDiagnosticRelatedInformation)
    );
}
