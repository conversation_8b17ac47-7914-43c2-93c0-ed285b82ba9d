import {execFile} from 'child_process';
import {promisify} from 'util';
import * as vscode from 'vscode';
import {WorkspaceFolder, TextDocumentItem} from 'vscode-languageserver-types';
import {Event, DidChangeTextDocumentParams, DidOpenTextDocumentParams} from 'vscode-languageclient';
import {isFileExist} from '@/utils/fs';
import {IWorkspaceProvider, ITextDocument} from '../types';
import {VSCodeTextDocument} from './VSCodeTextDocument';
import {adaptToLspWorkspaceFolder} from './utils';

const execFileAsync = promisify(execFile);

export class VSCodeWorkspace implements IWorkspaceProvider {
    private disposables: vscode.Disposable[] = [];
    private readonly goroots = new Map<string, string | undefined>();
    private readonly _onDidChangeTextDocument = new vscode.EventEmitter<DidChangeTextDocumentParams>();
    onDidChangeTextDocument: Event<DidChangeTextDocumentParams> = this._onDidChangeTextDocument.event;
    private readonly _onDidOpenTextDocument = new vscode.EventEmitter<DidOpenTextDocumentParams>();
    onDidOpenTextDocument: Event<DidOpenTextDocumentParams> = this._onDidOpenTextDocument.event;
    private static instance: VSCodeWorkspace | null = null;

    private constructor() {
        this.disposables.push(
            vscode.workspace.onDidChangeTextDocument(e => {
                this._onDidChangeTextDocument.fire({
                    textDocument: {
                        uri: e.document.uri.fsPath,
                        version: e.document.version,
                    },
                    contentChanges: e.contentChanges.map(item => ({
                        range: item.range,
                        text: item.text,
                    })),
                });
            }),
            vscode.workspace.onDidOpenTextDocument(doc => {
                if (doc.uri.scheme === 'file') {
                    this._onDidOpenTextDocument.fire({
                        textDocument: {
                            uri: doc.uri.fsPath,
                            languageId: doc.languageId,
                            version: doc.version,
                            text: doc.getText(),
                        },
                    });
                }
            })
        );
    }

    static getInstance(): IWorkspaceProvider {
        if (!VSCodeWorkspace.instance) {
            VSCodeWorkspace.instance = new VSCodeWorkspace();
        }
        return VSCodeWorkspace.instance;
    }

    get workspaceFolders(): WorkspaceFolder[] | undefined {
        return vscode.workspace.workspaceFolders?.map(folder => {
            return {
                uri: folder.uri.fsPath,
                name: folder.name,
            };
        });
    }

    get visibleTextDocuments(): TextDocumentItem[] {
        return vscode.window.visibleTextEditors.map(editor => {
            return {
                uri: editor.document.uri.fsPath,
                languageId: editor.document.languageId,
                version: editor.document.version,
                text: editor.document.getText(),
            };
        });
    }

    async openTextDocument(path: string): Promise<ITextDocument> {
        const document = await vscode.workspace.openTextDocument(path);
        return new VSCodeTextDocument(document);
    }

    async getGoRoot(path: string): Promise<string | undefined> {
        const workspace = vscode.workspace.getWorkspaceFolder(vscode.Uri.file(path));
        // 如果无法根据路径找到对应 workspace，则用空字符串代替作为 key
        const workspacePath = workspace?.uri.fsPath ?? '';
        if (!this.goroots.has(workspacePath)) {
            try {
                const {stdout} = await execFileAsync(
                    'go',
                    ['env', 'GOROOT'],
                    {cwd: workspacePath ?? undefined}
                );
                const goroot = stdout.trim();
                const gorootExists = await isFileExist(goroot);
                this.goroots.set(workspacePath, gorootExists ? goroot : undefined);
            }
            catch {
                this.goroots.set(workspacePath, undefined);
            }
        }
        return this.goroots.get(workspacePath);
    }

    asRelativePath(path: string): string {
        return vscode.workspace.asRelativePath(path);
    }

    getWorkspaceFolder(path: string): WorkspaceFolder | undefined {
        const workspace = vscode.workspace.getWorkspaceFolder(vscode.Uri.file(path));
        return workspace ? adaptToLspWorkspaceFolder(workspace) : undefined;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
