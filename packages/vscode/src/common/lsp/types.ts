import {URI} from 'vscode-uri';
import {TextDocument as VscodeTextDocument} from 'vscode-languageserver-textdocument';
import {
    Position,
    Range,
    TextDocumentItem,
    WorkspaceFolder,
    DidChangeTextDocumentParams,
    DidOpenTextDocumentParams,
    Event,
    Disposable,
} from 'vscode-languageserver-protocol';

export interface TextLine {
    text: string;
    range: Range;
    isEmptyOrWhitespace: boolean;
}

export interface ITextDocument extends VscodeTextDocument {
    readonly fileName: string;
    readonly uriObject: URI;
    lineAt(line: number | Position): TextLine;
}

export interface IWorkspaceProvider extends Disposable {
    openTextDocument(path: string): Promise<ITextDocument>;
    workspaceFolders: WorkspaceFolder[] | undefined;
    visibleTextDocuments: TextDocumentItem[];
    getGoRoot(path: string): Promise<string | undefined>;
    onDidChangeTextDocument: Event<DidChangeTextDocumentParams>;
    onDidOpenTextDocument: Event<DidOpenTextDocumentParams>;
    asRelativePath(path: string): string;
    getWorkspaceFolder(path: string): WorkspaceFolder | undefined;
}
