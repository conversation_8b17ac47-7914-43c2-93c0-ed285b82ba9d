import * as vscode from 'vscode';
import {iocContainer} from '@/iocContainer';
import {VSCodeConfigProvider} from '@/services/ConfigProvider';

let message = '';

// eslint-disable-next-line no-undef-init
let key: string | undefined = undefined;

interface Result {
    status: string;
    message?: string;
}

export function isForbidden(res: Result) {
    return res.status && res.status.toLowerCase() === 'forbidden';
}

export function showForbiddenMessage(res: Result) {
    if (isForbidden(res)) {
        const configProvider = iocContainer.get(VSCodeConfigProvider);
        const currentKey = configProvider.getLicense();
        if (res.message && (message !== res.message || key !== currentKey)) {
            vscode.window.showInformationMessage(res.message);
            message = res.message;
            key = currentKey;
        }
        return true;
    }
    return false;
}
