import * as vscode from 'vscode';
import {registerOutputChannel} from '@/services/outputChannelRegistry';
import {L10n} from './L10nProvider/L10n';
import {GlobalText} from './L10nProvider/constants';

export enum LogLevel {
    Off = 0,
    Trace = 1,
    Debug = 2,
    Info = 3,
    Warning = 4,
    Error = 5,
}

const DEFAULT_LOG_LEVEL = $features.PLATFORM === 'internal'
    ? LogLevel.Debug
    : LogLevel.Info;

// default level
let logLevel = DEFAULT_LOG_LEVEL;

const outputChannel = vscode.window.createOutputChannel('Baidu Comate');
registerOutputChannel(outputChannel);

const getCurrentTime = () => {
    const now = new Date();

    // get the date and time components from the Date object
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    // concatenate the date and time components into the desired format
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

type Texts = Array<string | number | boolean | undefined>;

export const trace = (...texts: Texts) => {
    if (logLevel > LogLevel.Off && logLevel <= LogLevel.Trace) {
        outputChannel.appendLine(`[${getCurrentTime()}][TRACE] ${texts.join(' ')}`);
    }
};

export const debug = (...texts: Texts) => {
    if (logLevel > LogLevel.Off && logLevel <= LogLevel.Debug) {
        outputChannel.appendLine(`[${getCurrentTime()}][DEBUG] ${texts.join(' ')}`);
    }
};

export const info = (...texts: Texts) => {
    if (logLevel > LogLevel.Off && logLevel <= LogLevel.Info) {
        outputChannel.appendLine(`[${getCurrentTime()}][INFO] ${texts.join(' ')}`);
    }
};

export const warning = (...texts: Texts) => {
    if (logLevel > LogLevel.Off && logLevel <= LogLevel.Warning) {
        outputChannel.appendLine(`[${getCurrentTime()}][WARNING] ${texts.join(' ')}`);
    }
};

export const error = (...texts: Texts) => {
    if (logLevel > LogLevel.Off && logLevel <= LogLevel.Error) {
        outputChannel.appendLine(`[${getCurrentTime()}][ERROR] ${texts.join(' ')}`);
    }
};

export const setLogLevel = (level: LogLevel) => {
    logLevel = level;
};

export const log = (level: LogLevel, ...texts: Texts) => {
    switch (level) {
        case LogLevel.Trace:
            trace(...texts);
            break;
        case LogLevel.Debug:
            debug(...texts);
            break;
        case LogLevel.Info:
            info(...texts);
            break;
        case LogLevel.Warning:
            warning(...texts);
            break;
        case LogLevel.Error:
            error(...texts);
            break;
    }
};

export const registerLogLevelQuickPick = () => {
    return vscode.commands.registerCommand('baidu.comate.setLogLevel', async () => {
        const quickPickItems: Array<vscode.QuickPickItem & {id: LogLevel}> = [
            {
                label: 'Trace',
                id: LogLevel.Trace,
                // @ts-ignore vscode 1.69.0 版本中，QuickPickItem 的 iconPath 属性没有定义
                iconPath: logLevel === LogLevel.Trace
                    ? new vscode.ThemeIcon('check')
                    : undefined,
            },
            {
                label: 'Debug',
                id: LogLevel.Debug,
                // @ts-ignore
                iconPath: logLevel === LogLevel.Debug
                    ? new vscode.ThemeIcon('check')
                    : undefined,
            },
            {
                label: 'Info',
                id: LogLevel.Info,
                // @ts-ignore
                iconPath: logLevel === LogLevel.Info
                    ? new vscode.ThemeIcon('check')
                    : undefined,
            },
            {
                label: 'Warning',
                id: LogLevel.Warning,
                // @ts-ignore
                iconPath: logLevel === LogLevel.Warning
                    ? new vscode.ThemeIcon('check')
                    : undefined,
            },
            {
                label: 'Error',
                id: LogLevel.Error,
                // @ts-ignore
                iconPath: logLevel === LogLevel.Error
                    ? new vscode.ThemeIcon('check')
                    : undefined,
            },
            {
                label: 'Off',
                id: LogLevel.Off,
                // @ts-ignore
                iconPath: logLevel === LogLevel.Off
                    ? new vscode.ThemeIcon('check')
                    : undefined,
            },
        ];
        const defaultItem = quickPickItems.find(item => item.id === DEFAULT_LOG_LEVEL);
        if (defaultItem) {
            defaultItem.description = 'Default';
        }
        const prevSelection = quickPickItems.find(item => item.id === logLevel);
        if (prevSelection) {
            prevSelection.description = 'Current';
        }
        const selection = await vscode.window.showQuickPick(
            quickPickItems,
            {placeHolder: L10n.t(GlobalText.LOG_LEVEL_PLACEHOLDER)}
        );
        if (selection) {
            setLogLevel(selection.id);
        }
    });
};
