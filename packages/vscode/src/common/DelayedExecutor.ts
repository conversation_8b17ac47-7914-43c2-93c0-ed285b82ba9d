/**
 * 该实现支持提前定义待执行的 runner 逻辑，并通过 execute 方法控制 runners 执行时机。首次执行后，添加 runner 时便即刻执行。
 */
// TODO 如果用户不点取消，输出也没结束，需要把回调的队列清理一下，想办法上报
export class DelayedExecutor {
    private readonly runnersRecord = new Map<string, Array<() => void>>();
    private readonly executionGrantedRecord = new Map<string, boolean>();

    /**
     * 在指定 id 下添加一个待执行的 runner，如果该 id 已经执行过，则立即执行 runner。
     * @param id 执行 runner 的 id
     * @param runner 从属于该 id 的 runner
     */
    add(id: string, runner: () => void) {
        if (!this.runnersRecord.has(id)) {
            this.runnersRecord.set(id, []);
        }
        this.runnersRecord.get(id)!.push(runner);
        this._executeIfGranted(id);
    }

    /**
     * 执行指定 id 的所有 runner，如果该 id 没有执行过，则将执行标记为 true。
     * @param id 执行 runner 的 id
     */
    execute(id: string) {
        this.executionGrantedRecord.set(id, true);
        this._executeIfGranted(id);
    }

    private async _executeIfGranted(id: string) {
        const isGranted = this.executionGrantedRecord.get(id);
        if (isGranted) {
            const runners = this.runnersRecord.get(id);
            if (runners) {
                for (const runner of runners) {
                    await runner();
                }
                this.runnersRecord.delete(id);
            }
        }
    }
}
