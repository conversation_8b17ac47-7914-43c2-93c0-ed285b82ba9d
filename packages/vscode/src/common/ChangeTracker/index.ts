import * as vscode from 'vscode';
import {Container} from 'inversify';
import {TextDocumentManager} from '../../services/Helper/TextDocumentManager';

export class ChangeTracker {
    get offset() {
        return this._offset;
    }

    constructor(globalContext: Container, uri: vscode.Uri, offset: number) {
        this._referenceCount = 0;
        this._isDisposed = false;
        this._offset = offset;
        const textDocumentManager = globalContext.get(TextDocumentManager);
        this._tracker = textDocumentManager.onDidChangeTextDocument(async event => {
            if (event.document.uri.fsPath === uri.fsPath) {
                // 监听 offset 前面的内容，如果有变化，offset 需要相应的修改
                for (const contentChange of event.contentChanges) {
                    if (contentChange.rangeOffset + contentChange.rangeLength <= this.offset) {
                        const insertLength = contentChange.text.length - contentChange.rangeLength;
                        this._offset = this._offset + insertLength;
                    }
                }
            }
        });
    }

    push(callback: () => void, timer: number) {
        if (this._isDisposed) {
            throw new Error('Unable to push new actions to a disposed ChangeTracker');
        }
        this._referenceCount++;
        setTimeout(
            () => {
                if (this._referenceCount <= 0) {
                    return;
                }
                callback();
                this._referenceCount--;
                if (this._referenceCount === 0) {
                    this._tracker.dispose();
                    this._isDisposed = true;
                }
            },
            timer
        );
    }

    dispose() {
        this._referenceCount = 0;
        this._tracker.dispose();
        this._isDisposed = true;
    }

    private _offset: number;
    private _referenceCount: number;
    private _isDisposed: boolean;
    private readonly _tracker: vscode.Disposable;
}
