import {StringDecoder} from 'string_decoder';
import * as vscode from 'vscode';
import {error} from '../outputChannel';

type RgBytesOrText = {bytes: string} | {text: string};

interface RgSubmatch {
    match: RgBytesOrText;
    start: number;
    end: number;
}

interface RgMatchData {
    path: RgBytesOrText;
    lines: RgBytesOrText;
    line_number: number;
    absolute_offset: number;
    submatches: RgSubmatch[];
}

interface RgMatchLine {
    type: 'match' | 'context' | string;
    data: RgMatchData;
}

function formatToString(obj: any): string {
    return obj.bytes ? Buffer.from(obj.bytes, 'base64').toString() : obj.text;
}

interface RipgrepSubMatch {
    matchText: string;
    range: vscode.Range;
}

export interface RipgrepMatchResult {
    preview: string;
    uri: vscode.Uri;
    matches: RipgrepSubMatch[];
}

interface RipgrepParserHandler {
    onResult: (result: RipgrepMatchResult) => void;
    /**
     * 到 limit 限制时触发
     */
    onHitLimit?: () => void;
}

/**
 * 用来解析 ripgrep 的输出结果
 */
export class RipgrepParser {
    private remainder = '';
    private canceled = false;
    private count = 0;
    private readonly decoder: StringDecoder;

    constructor(
        private readonly root: vscode.Uri,
        private readonly handler: RipgrepParserHandler,
        private readonly limit?: number
    ) {
        this.decoder = new StringDecoder();
    }

    handleData(data: Buffer | string) {
        if (this.canceled) {
            return;
        }

        const dataStr = typeof data === 'string' ? data : this.decoder.write(data);
        this.handleDecodedData(dataStr);
    }

    cancel() {
        this.canceled = true;
    }

    hitLimit() {
        this.handler.onHitLimit?.();
        this.cancel();
    }

    private handleDecodedData(decodedData: string) {
        const dataStr = this.remainder + '\n' + decodedData;

        const lines = dataStr.split('\n');
        // 总是将最后一行的内容作为剩余数据方到下一次处理，避免最后一行被截断的情况
        this.remainder = lines.pop() ?? '';

        for (const line of lines) {
            if (line.trim() === '') {
                continue;
            }
            this.handleLine(line.trim());
        }
    }

    private handleLine(lineStr: string) {
        if (this.canceled) {
            return;
        }

        if (typeof this.limit === 'number' && this.count >= this.limit) {
            return;
        }

        try {
            const matchResult: RgMatchLine = JSON.parse(lineStr);

            if (matchResult.type === 'match') {
                const matchPath = formatToString(matchResult.data.path);
                const uri = vscode.Uri.joinPath(this.root, matchPath);
                const result = this.createSearchMatch(matchResult.data, uri);
                this.handler.onResult(result);
            }
        }
        catch (e) {
            error('Ripgrep Error: Failed to parse: ', lineStr);
        }
    }

    private createSearchMatch(matchData: RgMatchData, uri: vscode.Uri) {
        const lineNumber = matchData.line_number - 1;
        const fullText = formatToString(matchData.lines);

        const matches = [];
        for (const submatch of matchData.submatches) {
            const matchText = formatToString(submatch.match);

            const range = new vscode.Range(
                lineNumber,
                submatch.start,
                lineNumber,
                submatch.end
            );

            matches.push({
                matchText,
                range,
            });

            this.count++;
            if (typeof this.limit === 'number' && this.count >= this.limit) {
                this.hitLimit();
                break;
            }
        }

        return {
            preview: fullText,
            uri,
            matches,
        };
    }
}
