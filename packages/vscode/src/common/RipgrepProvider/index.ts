import {spawn} from 'child_process';
import 'reflect-metadata';
import {injectable} from 'inversify';
import * as vscode from 'vscode';
import {getRgPath} from '@/utils/rg';
import {error} from '../outputChannel';
import {RipgrepMatchResult, RipgrepParser} from './RipgrepParser';

interface SearchOptions {
    /**
     * 搜索的目录
     */
    folderUri: vscode.Uri;

    /**
     * 搜索结果最大条数，没有不限制
     */
    limit?: number;
}

interface SearchQuery {
    // 搜索的文本或正则表达式字符串
    pattern: string;

    // 表示 pattern 是否为正则表达式
    isRegExp?: boolean;

    // 是否大小写敏感，使用正则时才生效
    isCaseSensitive?: boolean;
}

interface SearchProgress {
    /**
     * 异步获取搜索结果
     * @param result RipgrepMatchResult
     * @returns 返回 boolean 可以控制是否继续搜索，true 表示停止搜索
     */
    onResult: (result: RipgrepMatchResult) => boolean;
    onHitLimit?: () => void;
}

/**
 * 封装的 ripgrep 相关类，可以进行全局的搜索
 * 初始化时尝试从 vsocde 根目录中查找 ripgrep 的二进制文件，正常 vscode 的搜索用的 ripgrep 所以理论上应该能找到
 * 找不到就算了，后面再想其他办法实现吧
 */
@injectable()
export class RipgrepProvider {
    // ripgrep 的可执行文件路径
    private binPath: string = '';

    constructor() {
        this.getRipgrepBinPath();
    }

    /**
     * 特定目录下的文本搜索，这是一个异步方法，通过 onResult 获取内容，注意返回的 Promise 并没有结果，可以在 onResult 中返回 true 停止搜索
     */
    searchText(query: SearchQuery, options: SearchOptions, progress: SearchProgress, token?: vscode.CancellationToken) {
        if (!this.binPath) {
            return;
        }
        return this.applyRgSearch(query, options, progress, token);
    }

    /**
     * 将 search 方法的参数转为为 rg 命令的参数
     *
     * @param query 搜索查询对象，包含是否区分大小写、是否使用正则表达式以及搜索模式。
     * @returns 返回一个rg命令参数的字符串数组。
     */
    private buildRgArgs(query: SearchQuery) {
        const {isCaseSensitive, isRegExp, pattern} = query;
        const args: string[] = ['--no-require-git', '--max-columns=300', '--max-filesize=1M'];
        args.push(isCaseSensitive ? '--case-sensitive' : '--ignore-case');

        if (isRegExp) {
            args.push('--regexp', pattern);
        }

        args.push('--json');

        // Folder to search
        args.push('--');

        if (!isRegExp) {
            // Put the query after --, in case the query starts with a dash
            args.push(pattern);
        }

        args.push('.');
        return args;
    }

    /**
     * 获取 Ripgrep 二进制文件路径
     *
     * @param vscodeAppRoot VSCode 应用程序根目录
     * @returns 如果找到 Ripgrep 二进制文件，则返回其绝对路径；否则返回空字符串
     */
    private async getRipgrepBinPath() {
        try {
            const rgPath = await getRgPath();
            if (rgPath) {
                this.binPath = rgPath;
            }
            error('Failed to get ripgrep');
        }
        catch (e: any) {
            error('RipgrepProvider Error: ', e?.message);
        }
    }

    private applyRgSearch(
        query: SearchQuery,
        options: SearchOptions,
        progress: SearchProgress,
        token?: vscode.CancellationToken
    ) {
        return new Promise<void>((resolve, reject) => {
            const rgArgs = this.buildRgArgs(query);

            let killed = false;
            const rgProc = spawn(this.binPath, rgArgs, {cwd: options.folderUri.fsPath});

            const parser = new RipgrepParser(
                options.folderUri,
                {
                    onResult: res => {
                        const stop = progress.onResult(res);
                        if (stop) {
                            parser.cancel();
                            if (!killed) {
                                rgProc.kill();
                            }
                        }
                    },
                    onHitLimit: () => {
                        progress.onHitLimit?.();
                        if (!killed) {
                            rgProc.kill();
                            killed = true;
                        }
                    },
                },
                options.limit
            );

            token?.onCancellationRequested(() => {
                if (!killed) {
                    rgProc.kill();
                    parser.cancel();
                }
            });

            rgProc.on('error', e => {
                const msg = e?.message ?? 'unknown error';
                error('Ripgrep Error: ', msg);
                reject(new Error(msg));
            });

            rgProc.stdout.on('data', data => {
                parser.handleData(data);
            });

            let stderr = '';
            rgProc.stderr.on('data', data => {
                const message = data.toString();
                if (stderr.length + message.length < 1e6) {
                    stderr += message;
                }
            });

            rgProc.on('close', () => {
                if (stderr) {
                    error('Ripgrep Error: ', stderr);
                    reject(stderr);
                }
                else {
                    resolve();
                }
            });
        });
    }
}
