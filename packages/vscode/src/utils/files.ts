import * as path from 'path';
import * as fs from 'fs';
import {promisify} from 'util';
import * as vscode from 'vscode';
import {uniq, sortBy, groupBy} from 'lodash';
import {globby} from 'globby';
import {info} from '@/common/outputChannel';
import {illegalTextFile} from '@/services/Composer/api';
import {CodeChunk, Position} from '../common/types';
import {getExtensionContextAsync} from './extensionContext';
import {getPathRelativeToWorkspace, reviveFullUriToWorkspace} from './workspace';

export const RECENT_FILES_STORAGE_KEY = 'COMATE_RECENT_FILES';
export const SELECTED_FILES_STORAGE_KEY = 'COMATE_SELECTED_FILES';

export async function getRecentFiles() {
    const context = await getExtensionContextAsync();
    const folders = vscode.workspace.workspaceFolders;
    if (!folders || folders.length === 0) {
        return [];
    }
    const repo = folders[0].uri.fsPath;
    if (!repo) {
        return [];
    }
    const folderFileMapping = context.globalState.get(RECENT_FILES_STORAGE_KEY, {}) as Record<string, string[]>;
    return folderFileMapping[repo]?.filter(file => file.length !== 0) || [];
}

export async function readIgnoreFile(uri: vscode.Uri): Promise<IgnoreRecord> {
    const ignore: IgnoreRecord = {};
    try {
        const data = await vscode.workspace.fs.readFile(uri);
        for (let line of Buffer.from(data).toString('utf-8').split('\n')) {
            if (line.startsWith('!')) {
                continue;
            }

            // Strip comment and trailing whitespace.
            line = line.replace(/\s*(#.*)?$/, '');

            if (line === '') {
                continue;
            }

            if (line.endsWith('/')) {
                line = line.slice(0, -1);
            }
            if (!line.startsWith('/') && !line.startsWith('**/')) {
                line = `**/${line}`;
            }
            ignore[line] = true;
        }
    }
    catch {
        //
    }
    return ignore;
}

type IgnoreRecord = Record<string, boolean>;

async function getExcludePatterns(workspaceFolder: vscode.WorkspaceFolder | null): Promise<string[]> {
    const config = vscode.workspace.getConfiguration('', workspaceFolder);
    const filesExclude = config.get<IgnoreRecord>('files.exclude', {});
    const searchExclude = config.get<IgnoreRecord>('search.exclude', {});

    const useIgnoreFiles = config.get<boolean>('search.useIgnoreFiles');
    const gitignoreExclude =
        useIgnoreFiles && workspaceFolder
            ? await readIgnoreFile(vscode.Uri.joinPath(workspaceFolder.uri, '.gitignore'))
            : {};
    const ignoreExclude =
        useIgnoreFiles && workspaceFolder
            ? await readIgnoreFile(vscode.Uri.joinPath(workspaceFolder.uri, '.ignore'))
            : {};

    const mergedExclude: IgnoreRecord = {
        ...filesExclude,
        ...searchExclude,
        ...gitignoreExclude,
        ...ignoreExclude,
    };
    const excludePatterns = Object.keys(mergedExclude).filter(key => mergedExclude[key] === true);
    return excludePatterns;
}

export async function findWorkspaceFiles(
    workspaceFolder: vscode.WorkspaceFolder,
    queryPattern: string,
    keyword: string,
    maxResults?: number
) {
    const excludePatterns = await getExcludePatterns(workspaceFolder);
    // 额外单独来一次关键字搜索，目的是将包含关键字的文件排在前面，将两个 query 合起来好像没用，感觉是按目录顺序来搜的
    // 时间消耗看起来和一次搜索差不多，性能好像影响也还行，起码比 globby 快很多
    // 后面可以考虑都搜索出来后 fuzzysort 排序一下，应该会快一些
    const [keywordMatches, fuzzyMatches] = await Promise.all([
        vscode.workspace.findFiles(
            new vscode.RelativePattern(workspaceFolder, `**/*${keyword}*`),
            `{${excludePatterns.join(',')}}`,
            maxResults
        ),
        vscode.workspace.findFiles(
            new vscode.RelativePattern(workspaceFolder, queryPattern),
            `{${excludePatterns.join(',')}}`,
            maxResults
        ),
    ]);

    const files = [...keywordMatches, ...fuzzyMatches].map(uri => getPathRelativeToWorkspace(uri));
    return uniq(files);
}

/**
 * 根据关键词模糊搜索当前所有工作区中的文件
 *
 * @param keyword 关键词
 * @param maxResults 单个工作区搜索结果的最大数量，默认值为 50
 * @returns 符合条件的文件数组
 */
export async function getFilesByKeyword(keyword: string, maxResults = 50): Promise<string[]> {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
        return [];
    }

    const patternKeyword = keyword.split('').map(char => `[${char.toUpperCase()}${char.toLowerCase()}]`).join('*');
    const pattern = `**/*${patternKeyword}*`;
    const workspaceFiles = await Promise.all(
        workspaceFolders.map(folder => findWorkspaceFiles(folder, pattern, keyword, maxResults))
    );
    return workspaceFiles.flat();
}

async function getFoldersByKeyword(keyword: string) {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
        return [];
    }
    const patternKeyword = keyword.split('').map(char => `[${char.toUpperCase()}${char.toLowerCase()}]`).join('*');
    const pattern = [`**/*${patternKeyword}*/`];
    const queries = await Promise.all(workspaceFolders.map(async folder => {
        const excludePattern = await getExcludePatterns(folder);
        return globby(
            pattern,
            {gitignore: true, cwd: folder.uri.fsPath, ignore: excludePattern, onlyDirectories: true}
        );
    }));
    return queries.flat();
}

export async function recordRecentSelectedFiles(recentSelectedFile: string) {
    const folders = vscode.workspace.workspaceFolders;
    if (!folders || folders.length === 0) {
        return;
    }
    const repo = folders[0].uri.fsPath;
    const context = await getExtensionContextAsync();
    const recent = context.globalState.get(SELECTED_FILES_STORAGE_KEY, {}) as Record<string, string[]>;

    if (!repo) {
        return;
    }

    if (fs.existsSync(path.join(repo, recentSelectedFile))) {
        const recentRepoFiles = recent[repo] || [];
        const updatedFiles = {
            ...recent,
            [repo]: [...recentRepoFiles, recentSelectedFile],
        };
        context.globalState.update(SELECTED_FILES_STORAGE_KEY, updatedFiles);
    }
}

export function partitionPathsByType(paths: string[] = []) {
    const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    const directoryPaths: string[] = [];
    const filePaths: string[] = [];
    if (!rootPath) {
        return [directoryPaths, filePaths];
    }
    for (const file of paths) {
        try {
            const pathToFile = path.join(rootPath, file);
            const stats = fs.statSync(pathToFile);
            if (stats.isDirectory()) {
                directoryPaths.push(file);
            }
            else {
                filePaths.push(file);
            }
        }
        catch (error) {
            console.error(`Error reading the path: ${file}. Error details: ${error}`);
        }
    }
    return [directoryPaths, filePaths];
}

export async function getRecentSelectedFilesAndFolders() {
    const context = await getExtensionContextAsync();
    const folders = vscode.workspace.workspaceFolders;
    if (!folders || folders.length === 0) {
        return [[], []];
    }
    const repo = folders[0].uri.fsPath;
    if (!repo) {
        return [];
    }
    const folderFileMapping = context.globalState.get(SELECTED_FILES_STORAGE_KEY, {}) as Record<string, string[]>;
    return partitionPathsByType(folderFileMapping[repo] || []);
}

const isFileSchema = (uri?: vscode.Uri) => uri && uri.scheme === 'file';

function getAllTabGroupsFiles() {
    const files = [];
    for (const tabGroup of vscode.window.tabGroups.all) {
        for (const tab of tabGroup.tabs) {
            if (tab.input instanceof vscode.TabInputText
                || tab.input instanceof vscode.TabInputNotebook
            ) {
                files.push(tab.input.uri);
            }
        }
    }
    return files.filter(isFileSchema).map(getPathRelativeToWorkspace);
}

export async function getOpenAndRecentFiles() {
    const recentFiles = await getRecentFiles();
    const recentSelectedFiles = await getRecentSelectedFilesAndFolders();
    const openFiles = vscode.workspace.textDocuments.filter(doc => isFileSchema(doc.uri));
    const openFilePaths = openFiles.map(doc => getPathRelativeToWorkspace(doc.uri));
    // textDocuments 只是打开过缓存中的文档，有些 tab 上能看到的文档有可能不包含，所以单独再处理下
    const tabGroupFiles = getAllTabGroupsFiles();
    const openAndRecentFiles = uniq(
        [...openFilePaths, ...tabGroupFiles, ...recentFiles.slice(0, 5), ...recentSelectedFiles[1].slice(0, 5)]
    );
    return openAndRecentFiles.slice(0, 20);
}

export async function getOpenAndRecentFolders() {
    const openAndRecentFiles = await getOpenAndRecentFiles();
    const recentSelectedFiles = await getRecentSelectedFilesAndFolders();
    const openAndRecentFolders = uniq(openAndRecentFiles.map(file => path.dirname(file)));
    return uniq([...openAndRecentFolders, ...recentSelectedFiles[0].slice(0, 5)]).slice(0, 20);
}

/**
 * 根据关键词和类型获取文件或文件夹
 *
 * @param keyword 关键词
 * @param type 'file' | 'folder' | undefined，默认不传时搜索文件
 * @returns 文件或文件夹数组，都是相对workspace的相对路径
 */
export async function getFilesOrFoldersByKeywordAndType(keyword: string, type?: string): Promise<string[]> {
    if (type === 'file') {
        return getFilesByKeyword(keyword);
    }
    if (type === 'folder') {
        return getFoldersByKeyword(keyword);
    }
    return getFilesByKeyword(keyword);
}

const stat = promisify(fs.stat);

export function getFilesFromDir(dirPath: string): string[] {
    const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!rootPath) {
        return [];
    }
    const files: string[] = [];
    if (fs.existsSync(path.join(rootPath, dirPath))) {
        const entries = fs.readdirSync(path.join(rootPath, dirPath));
        for (const entry of entries) {
            const fullEntryPath = path.join(dirPath, entry);
            if (fs.statSync(path.join(rootPath, fullEntryPath)).isDirectory()) {
                files.push(...getFilesFromDir(fullEntryPath));
            }
            else {
                files.push(fullEntryPath);
            }
        }
    }
    return files;
}

async function calculateFileSize(filePath: string): Promise<number> {
    const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!rootPath) {
        return 0;
    }
    const fileUri = vscode.Uri.file(path.join(rootPath, filePath));
    try {
        const stats = await stat(fileUri.fsPath);
        return stats.size;
    }
    catch (error) {
        return 0;
    }
}

async function calculateDirectorySize(directoryPath: string, limit: number, currentTotalSize: number): Promise<number> {
    const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!rootPath) {
        return 0;
    }
    const files = getFilesFromDir(directoryPath);
    let totalSize = 0;
    for (const file of files) {
        const filePath = path.join(rootPath, file);
        try {
            const stats = await stat(filePath);
            if (stats.isFile()) {
                const {illegal} = await illegalTextFile(file);
                // eslint-disable-next-line max-depth
                if (illegal) {
                    continue;
                }

                const fileSize = await calculateFileSize(file);
                totalSize += fileSize;
                // 如果当前总字符数（包含已处理的文件和目录）加上当前文件大小超过限制，停止继续处理
                // eslint-disable-next-line max-depth
                if (currentTotalSize + totalSize > limit) {
                    return totalSize;
                }
            }
        }
        catch (error) {
            console.error(`Error reading the path: ${file}. Error details: ${error}`);
        }
    }
    return totalSize;
}

export async function isCharacterOverLimit(filePaths: string[], directoryPaths: string[], limit: number) {
    let totalSize = 0;
    for (const filePath of filePaths) {
        const fileSize = await calculateFileSize(filePath);
        totalSize += fileSize;
        if (totalSize > limit) {
            return true;
        }
    }
    for (const directoryPath of directoryPaths) {
        const directorySize = await calculateDirectorySize(directoryPath, limit, totalSize);
        totalSize += directorySize;
        if (totalSize > limit) {
            return true;
        }
    }
    return false;
}

function convertToCustomPosition(vsPosition: vscode.Position): Position {
    return {
        line: vsPosition.line,
        column: vsPosition.character,
    };
}

export async function getCodeChunkFromFilePath(filePath: string, range?: vscode.Range): Promise<CodeChunk> {
    const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!rootPath) {
        return {} as CodeChunk;
    }
    try {
        const {illegal, type: fileType} = await illegalTextFile(filePath);
        if (illegal) {
            const chunk: CodeChunk = {
                repo: rootPath,
                type: fileType,
                path: filePath,
                content: '',
                contentStart: {line: 0, column: 0},
                contentEnd: {line: 0, column: 0},
            };
            return chunk;
        }
        const doc = await vscode.workspace.openTextDocument(path.join(rootPath, filePath));
        if (range) {
            const chunk: CodeChunk = {
                repo: rootPath,
                type: 'fullContent',
                path: filePath,
                content: doc.getText(range),
                contentStart: {line: range.start.line, column: range.start.character},
                contentEnd: {line: range.end.line, column: range.end.character},
            };
            return chunk;
        }

        const lastLine = doc.lineAt(doc.lineCount - 1);
        const chunk: CodeChunk = {
            repo: rootPath,
            type: 'fullContent',
            path: filePath,
            content: doc.getText(),
            contentStart: convertToCustomPosition(doc.lineAt(0).range.start),
            contentEnd: convertToCustomPosition(lastLine.range.end),
        };
        return chunk;
    }
    catch (ex) {
        return {} as CodeChunk;
    }
}

export async function getCodeChunksFromFilesAndDirs(filePaths: string[], directoryPaths: string[]) {
    const allFiles: string[] = filePaths.concat(directoryPaths.map(getFilesFromDir).flat());
    const codeChunks = Promise.all(allFiles.map(filePath => getCodeChunkFromFilePath(filePath)));
    return codeChunks;
}

export function getCurrentFileRelativePath() {
    const currentFile = vscode.window.activeTextEditor?.document.uri;
    if (!currentFile) {
        return undefined;
    }
    return vscode.workspace.asRelativePath(currentFile);
}

export function getCurrentFileContent() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        return [];
    }

    const text = editor.document.getText();
    return text.split('\n');
}

export async function mergeCodeChunk(currentChunk: CodeChunk, mergeChunk: CodeChunk): Promise<CodeChunk | null> {
    if (currentChunk.path === mergeChunk.path) {
        const curStartLine = currentChunk.contentStart.line;
        const curEndLine = currentChunk.contentEnd.line;
        const mergeStartLine = mergeChunk.contentStart.line;
        const mergeEndLine = mergeChunk.contentEnd.line;
        // 检查两个代码块是否行数存在重叠或连续
        if (
            (mergeStartLine >= curStartLine && mergeStartLine <= curEndLine + 1)
            || (mergeEndLine >= curStartLine - 1 && mergeEndLine <= curEndLine)
        ) {
            // 如果存在重叠或连续，合并这两个代码块的行
            const contentStart = mergeStartLine < curStartLine ? mergeChunk.contentStart : currentChunk.contentStart;
            const contentEnd = mergeEndLine > curEndLine ? mergeChunk.contentEnd : currentChunk.contentEnd;
            try {
                const doc = await vscode.workspace.openTextDocument(currentChunk.path);
                const content = doc.getText(
                    new vscode.Range(
                        contentStart.line,
                        0,
                        contentEnd.line,
                        Number.MAX_SAFE_INTEGER
                    )
                );
                return {...currentChunk, contentStart, contentEnd, content};
            }
            catch (error) {
                return null;
            }
        }
        else {
            // 如果不存在重叠或连续，则返回 null
            return null;
        }
    }
    return currentChunk;
}

export async function mergeCodeChunks(chunks: CodeChunk[]): Promise<CodeChunk[]> {
    const groups = groupBy(chunks, 'path');

    const mergedChunks: CodeChunk[] = [];
    for (const chunkGroup of Object.values(groups)) {
        const sortedGroup = sortBy(chunkGroup, 'contentStart.line');

        let prevChunk = sortedGroup[0];
        for (let i = 1; i < sortedGroup.length; i++) {
            const mergedChunk = await mergeCodeChunk(prevChunk, sortedGroup[i]);
            if (mergedChunk === null) {
                // 如果无法合并，将 `prevChunk` 添加到 `mergedChunks` 并将 `sortedGroup[i]` 设置为 `prevChunk`
                mergedChunks.push(prevChunk);
                prevChunk = sortedGroup[i];
            }
            else {
                // 如果可以合并，将合并的代码块设置为 `prevChunk`
                prevChunk = mergedChunk;
            }
        }
        // 将最后一个 `prevChunk` 添加到 `mergedChunks`
        if (prevChunk !== null) {
            mergedChunks.push(prevChunk);
        }
    }
    return mergedChunks;
}

export function workspaceStatus() {
    const workspaceOpened = vscode.workspace.workspaceFolders !== undefined;
    const fileOpened = vscode.window.activeTextEditor !== undefined;
    return {
        workspaceOpened: workspaceOpened,
        fileOpened: fileOpened,
    };
}

export const getVSCodeDocumentByUri = async (filePath: string) => {
    const uri = await reviveFullUriToWorkspace(filePath);
    if (uri) {
        const editor = await vscode.window.showTextDocument(uri, {
            preview: false,
        });
        return editor;
    }
    throw new Error('file not existed');
};

export const getDocumentReplaceRange = (document: vscode.TextDocument, content: string) => {
    const match = document.getText().indexOf(content);
    // 注意0也是，代表起始位开始
    if (match >= 0) {
        const fromPosition = document.positionAt(match);
        // Get the first match's range
        const matchRange = new vscode.Range(
            new vscode.Position(fromPosition.line, 0),
            document.positionAt(match + content.length)
        );
        return matchRange;
    }

    return undefined;
};

export async function acceptMatchedCodeBlock(filePath: string, from: string, to: string) {
    info(`acceptMatchedCodeBlock: filepath=${filePath}`);
    const editor = await getVSCodeDocumentByUri(filePath);
    if (editor) {
        const document = editor.document;
        const matchRange = getDocumentReplaceRange(document, from);
        if (matchRange) {
            const edit = new vscode.WorkspaceEdit();
            edit.replace(document.uri, matchRange, to);
            await vscode.workspace.applyEdit(edit);

            const selectionRange = getDocumentReplaceRange(document, to);
            if (selectionRange) {
                editor.selection = new vscode.Selection(selectionRange.start, selectionRange.end);
                editor.revealRange(matchRange);
            }
            return;
        }
    }
    throw new Error('file not existed');
}
