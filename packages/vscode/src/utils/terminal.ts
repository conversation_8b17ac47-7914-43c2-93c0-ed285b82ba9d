import {platform} from 'node:process';
import {sep, join} from 'node:path';
import {EOL, homedir} from 'node:os';
import {readFile} from 'node:fs/promises';
import * as vscode from 'vscode';
import {flatten} from 'lodash';
import {compare} from 'compare-versions';
import {execCommand} from '@/utils/cp';

const findTerminalSplitPrefix = (output: string[]) => {
    // 找到第一行字符数长度符合规则，且至少出现两次的行
    const validFirstLineIndex = output.findIndex(line => {
        const prefix = line.substring(0, 12);
        return line.length > 2 && output.filter(line => line.startsWith(prefix)).length > 1;
    });
    const [startLine, ...lines] = output.slice(validFirstLineIndex, Infinity);
    const maxLength = startLine.length;

    let filteredLines = [...lines];
    let isOutputLineAllMatchedPrefix = false;
    for (let i = 1; i < maxLength; i++) {
        const prefix = startLine.slice(0, i);
        const outputLinesOmitByPrefix = filteredLines.filter(line => line.startsWith(prefix));
        const outputLineOmitted = filteredLines.length !== outputLinesOmitByPrefix.length;
        if (outputLineOmitted && isOutputLineAllMatchedPrefix) {
            return startLine.slice(0, i - 1);
        }
        else if (filteredLines.length === outputLinesOmitByPrefix.length) {
            isOutputLineAllMatchedPrefix = true;
        }
        filteredLines = outputLinesOmitByPrefix;
    }
    return startLine;
};

export const splitTerminalOutputToCommandChunks = (output: string) => {
    const lines = output.split(/\r?\n/);
    const prefix = findTerminalSplitPrefix(lines);
    const chunks = lines.reduce<string[][]>(
        (chunks, line) => {
            if (line.startsWith(prefix)) {
                chunks.unshift([line]);
                return chunks;
            }
            const lastChunk = chunks[0];
            if (Array.isArray(lastChunk)) {
                lastChunk.push(line);
            }
            else if (line.trim()) {
                chunks.unshift([line.trim()]);
            }
            return chunks;
        },
        []
    );
    return chunks;
};
/**
 * 拆解一行字符串内的文件路径，不考虑文件名中有空格的情况，不考虑文件夹，使用`path.sep`分隔符兼容windows
 * ```
 * 例如，输入字符串'npm ERR! File not exist /path/to/file'
 * 返回 [[25, 37]], 代表序号25~37为一段路径，序号从0开始计算
 * ```
 * @returns Array<[start, end]>
 */
export const matchFilePath = (input: string) => {
    const chunks = input.split(' ');
    const {positions} = chunks.reduce<{cursor: number, positions: Array<[number, number]>}>(
        ({cursor, positions}, chunk) => {
            if (
                new RegExp(`^(.+)\\${sep}([^\\${sep}]+)$`).test(chunk)
                || /:\d{1,}?:/.test(chunk)
            ) {
                positions.push([cursor, cursor + chunk.length]);
            }

            return {cursor: chunk.length + cursor + 1, positions};
        },
        {cursor: 0, positions: []}
    );
    return positions;
};

/**
 * 将一段字符中，非路径的部分按照TerminalLink的格式拆分出来
 * ```
 * 例如，输入字符串 'npm ERR! File not exist /path/to/file'
 * 返回的链接坐标是：[[25, 37]]
 * 处理完后展示链接的的区间为：[{startIndex: 0, length: 24}]
 * ```
 * @returns Array<{startIndex: number, length: number}>
 */
export const matchFilePathPositionToTerminalLinkRange = (input: string) => {
    const positions = matchFilePath(input);
    const coordinates = flatten(positions);
    return coordinates.reduce<Array<{startIndex: number, length: number}>>(
        (result, coordinate, i) => {
            // 假设长度为20，文件路径的坐标为 [[2,4], [8,10]]
            // flatten后得到 [2,4,8,10]
            // 预期返回 [0, 1],[5,7]..[11, 20]
            if (i % 2 === 0) {
                if (result.length === 0) {
                    if (coordinate !== 0) {
                        // 如果第一组是0, 比如 [0, 2, 4, 6], 那么要考虑第一次进入这个分支是4
                        const startIndex = coordinates[0] === 0 ? coordinates[i - 1] + 1 : 0;
                        const length = coordinates[0] === 0
                            ? coordinate - coordinates[i - 1] - 1
                            : coordinate - 1;
                        result.push({startIndex, length});
                    }
                }
                else {
                    result.push({
                        startIndex: coordinates[i - 1] + 1,
                        length: coordinates[i] - coordinates[i - 1] - 1,
                    });
                }
            }

            if (i === coordinates.length - 1 && coordinate <= input.length) {
                result.push({startIndex: coordinate + 1, length: input.length - coordinate});
            }

            return result;
        },
        []
    );
};

/**
 * 矫正所有操作系统不同格式的换行符，换成当前操作系统的换行符
 * ```
 * \r = CR (Carriage Return) → Used as a new line character in Mac OS before X
 * \n = LF (Line Feed) → Used as a new line character in Unix/Mac OS X
 * \r\n = CR + LF → Used as a new line character in Windows
 * ```
 */
export const uniformEOLChar = (input: string) => {
    if (EOL === '\r\n') {
        return input.replace(/([^\r])\n/g, '$1\r\n');
    }
    return input;
};

/** 合并重叠的换行符，如果是Window是系统，先换到\n字符上 */
export const removeOverlapedEOLChar = (line: string) => {
    return line.replace(/(\r?\n)+/g, '\n');
};
/** 移除字符串结尾的换行符，兼容所有所有系统 */
export const removeTrailingEOLChar = (line: string) => line.replace(/\r\n/, '').replace(/\r/, '');
/** 通过进程ID获取当前进程所有在的目录，只适配了unix系统，windows暂不支持，返回空字符串 */
export const getTerminalProcessIdPWD = async (processId: number | undefined, {defaultRoot}: {defaultRoot: string}) => {
    if (typeof processId !== 'number') {
        return '';
    }

    try {
        switch (platform) {
            case 'win32':
                return '';
            case 'darwin':
            case 'linux':
                return await execCommand(`lsof -p ${processId} | awk 'NR==2 {print $NF}'`, defaultRoot);
            default:
                return '';
        }
    }
    catch (ex) {
        return '';
    }
};

/**
 * 通过 workbench.action.terminal.copyLastCommand 命令，将上次执行的terminal命令复制到剪贴板中，然后从剪贴板中读取出来。
 * 低于 1.85 版本无法获取
 *
 * @returns 返回从剪贴板获取的最新命令字符串，如果失败则返回空字符串。
 */
const getLastCommandFromClipboard = async () => {
    try {
        if (compare(vscode.version, '1.85.0', '<')) {
            return '';
        }
        // 需要将原本的内容清空，防止命令执行失败时读取到原本粘贴板的内容
        await vscode.env.clipboard.writeText('');
        await vscode.commands.executeCommand('workbench.action.terminal.copyLastCommand');
        const output = await vscode.env.clipboard.readText();
        return output;
    }
    catch (ex) {
        return '';
    }
};

/**
 * 根据 terminal 的 shellPath 返回历史命令存储的文件路径
 *
 * @param shellPath shell路径
 * @returns 历史命令文件的路径，如果无法确定则返回空字符串
 */
const getHistoryFilePath = (shellPath = '') => {
    if (process.env.HISTFILE) {
        return process.env.HISTFILE;
    }

    const homeDir = homedir();
    if (shellPath.includes('zsh')) {
        return join(homeDir, '.zsh_history');
    }
    else if (shellPath.includes('bash')) {
        return join(homeDir, '.bash_history');
    }

    return '';
};

const extractLastSection = (line: string, sep: string) => line.split(sep).slice(1).join(sep);

const parseHistoryFileContent = (content: string) => {
    const lines = content.split('\n');
    const commands: string[] = [];
    for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine || trimmedLine.startsWith('#')) {
            continue;
        }
        const reBashHistory = /^: \d+:0;/;
        if (reBashHistory.test(line)) {
            commands.push(extractLastSection(line, ';'));
        }
        else if (/^\d+ {2}/.test(line)) {
            commands.push(extractLastSection(line, '  '));
        }
        else {
            commands.push(line);
        }
    }
    return commands;
};

const getHistoryByCommand = async (shellPath: string, n = 5) => {
    try {
        if (process.platform === 'win32' && shellPath.toLowerCase().includes('powershell')) {
            const path = await execCommand('(Get-PSReadlineOption).HistorySavePath', homedir(), {shell: shellPath});
            const output = await readFile(path, 'utf8');
            return output.split('\n').slice(-n);
        }
    }
    catch (ex) {
        //
    }
    // 其他系统拿不到先不管
    return [];
};

/**
 * terminal 退出后，会将缓存在内存中的历史命令写入到文件中
 * 这个函数就是根据 terminal 的 shellPath 从对应的文件中读取历史命令
 */
const getCommandsFromHistoryFile = async (shellPath: string, n = 5) => {
    try {
        const historyFilePath = getHistoryFilePath(shellPath);
        if (!historyFilePath) {
            return getHistoryByCommand(shellPath);
        }
        const output = await execCommand(
            `tail -n ${n} ${historyFilePath}`,
            homedir(),
            {shell: shellPath}
        );
        const commands = parseHistoryFileContent(output);
        return commands.reverse();
    }
    catch (ex) {
        return [];
    }
};

/** 拿历史命令，然后匹配 log 看是否能匹配上，匹配上则返回该命令 */
export const extractCommandFromTerminalLog = async (log: string, shellPath?: string) => {
    try {
        const [lastCommand, commands] = await Promise.all([
            getLastCommandFromClipboard(),
            getCommandsFromHistoryFile(shellPath ?? ''),
        ]);
        const validCommands = [lastCommand, ...commands].filter(v => v.trim());
        if (!validCommands.length || !log) {
            return '';
        }
        const matchedCommand = validCommands.find(command => log.includes(command.trim()));
        return matchedCommand ?? '';
    }
    catch (ex) {
        return '';
    }
};
