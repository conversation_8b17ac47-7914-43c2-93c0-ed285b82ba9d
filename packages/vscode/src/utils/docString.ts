import * as vscode from 'vscode';
import Parser from 'web-tree-sitter';
import {findLeadingNonWhitespaceIndex} from './common';

export const computeAfterDefinitionDocstringLocation = (document: vscode.TextDocument, node: Parser.SyntaxNode) => {
    const signatureNode = node.childForFieldName('return_type') ?? node.childForFieldName('parameters');
    const signatureNextRow = signatureNode!.endPosition.row + 1;
    const position = new vscode.Position(signatureNextRow, 0);
    const bodyNode = node.childForFieldName('body')!;
    const bodyStartLine = document.lineAt(bodyNode.startPosition.row).text;
    const padding = bodyStartLine.slice(0, findLeadingNonWhitespaceIndex(bodyStartLine));

    return {
        position,
        padding,
    };
};

export const computeBeforeDefinitionDocstringLocation = (document: vscode.TextDocument, node: Parser.SyntaxNode) => {
    const position = new vscode.Position(node.startPosition.row, 0);
    const definitionLine = document.lineAt(node.startPosition.row).text;
    const padding = definitionLine.slice(0, findLeadingNonWhitespaceIndex(definitionLine));

    return {
        position,
        padding,
    };
};

export const findDocstringInsertionLocation = (document: vscode.TextDocument, functionNode: Parser.SyntaxNode) => {
    const languageId = document.languageId;
    if (languageId === 'python') {
        return computeAfterDefinitionDocstringLocation(document, functionNode);
    }
    return computeBeforeDefinitionDocstringLocation(document, functionNode);
};
