import * as vscode from 'vscode';
import {ContextType, KnowledgeList} from '@shared/protocols';
import {isFileExist} from './fs';
import {getRepoName} from './git';

export async function getFilesWithExtension(directoryPath: string, extension: string): Promise<vscode.Uri[]> {
    const files = await vscode.workspace.fs.readDirectory(vscode.Uri.file(directoryPath));
    return files
        .filter(([path, type]) => type === vscode.FileType.File && path.endsWith(extension))
        .map(file => vscode.Uri.joinPath(vscode.Uri.file(directoryPath), file[0]));
}

export async function readFileContent(uri: vscode.Uri): Promise<string> {
    const fileContent = await vscode.workspace.fs.readFile(uri);
    return fileContent.toString();
}

export function getFileStats(uri: vscode.Uri) {
    const segments = uri.path.split('/');
    const directory = segments.slice(0, -1).join('/');
    const fileName = segments[segments.length - 1];
    const extension = fileName.includes('.') ? fileName.split('.').pop()! : '';
    return {
        directory,
        fileName,
        extension: `.${extension}`,
    };
}

export async function getFileDetailedStats(uri: vscode.Uri) {
    const basic = getFileStats(uri);
    const content = await readFileContent(uri);
    const characters = content.length;
    const lines = content.split('\n').length;
    return {
        ...basic,
        characters,
        lines,
        content,
    };
}

export async function isFileEmpty(uri: vscode.Uri) {
    const {size} = await vscode.workspace.fs.stat(uri);
    return size === 0;
}

export async function isFileWritable(uri: vscode.Uri) {
    const {permissions} = await vscode.workspace.fs.stat(uri);
    const isFSWritable = vscode.workspace.fs.isWritableFileSystem(uri.scheme);
    return permissions !== vscode.FilePermission.Readonly && isFSWritable;
}

/**
 * 获取相对于工作区的路径。
 *
 * @param uri - 文件 Uri
 * @returns 返回相对于工作区的路径
 */
export function getPathRelativeToWorkspace(uri: vscode.Uri) {
    const workspace = vscode.workspace.getWorkspaceFolder(uri);
    if (workspace) {
        return uri.path.slice(workspace.uri.path.length + 1);
    }
    return uri.path;
}

export const joinWorkspacePath = (relativePath: string) => {
    if (vscode.workspace.workspaceFolders) {
        return vscode.workspace.workspaceFolders.map(({uri}) => {
            return vscode.Uri.joinPath(uri, vscode.workspace.asRelativePath(relativePath));
        });
    }
    return [];
};

export async function openDocument(uris: vscode.Uri[], selection?: vscode.Range) {
    if (!uris.length) {
        throw new Error('[openDocument] uri not found');
    }

    try {
        const uri = uris[0];
        await vscode.window.showTextDocument(uri, {selection});
    }
    catch (ex) {
        await openDocument(uris.slice(1), selection);
    }
}

export async function reviveFullUriToWorkspace(relativePath: string | undefined | null) {
    if (!relativePath) {
        return undefined;
    }

    const uniformPath = /^\/[a-z]:\//i.test(relativePath)
        ? relativePath.slice(1)
        // Windows 系统会返回 .\ 的路径，到这里会decode成 .%5c，仅遇到这个case
        : decodeURIComponent(relativePath).replace(new RegExp(/^\.\\/), '');

    if (await isFileExist(uniformPath)) {
        return vscode.Uri.parse(uniformPath);
    }

    const workspaces = vscode.workspace.workspaceFolders;
    if (!workspaces) {
        return undefined;
    }

    for (const item of workspaces) {
        const uri = vscode.Uri.joinPath(item.uri, uniformPath);
        const existed = await isFileExist(uri.fsPath);
        if (existed) {
            return uri;
        }
    }
    return undefined;
}

export async function getFirstWorkspaceRepoName() {
    const workspace = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspace) {
        return '';
    }
    return getRepoName(workspace);
}

/**
 * 对话知识的逻辑调整，去掉了#当前文件，当用户选择文件作为上下文后，会在提交到服务端之前找到和当前打开文件对应的context，并将其替换为currentFile。
 */
export const serializeChatKnowledges = (knowledgeList: KnowledgeList[] | undefined) => {
    if (!knowledgeList) {
        return [];
    }

    const uri = vscode.window.activeTextEditor?.document.uri;
    const activeDocumentRelativePath = uri ? vscode.workspace.asRelativePath(uri) : undefined;
    const knowledgeListTransformedCurrrentFile = knowledgeList.map((knowledge: KnowledgeList) => {
        if (knowledge.type === ContextType.FILE && knowledge.id === activeDocumentRelativePath) {
            return {...knowledge, path: 'currentFile', type: ContextType.CURRENT_FILE};
        }
        return knowledge;
    });
    return knowledgeListTransformedCurrrentFile;
};
