import * as vscode from 'vscode';

let context: vscode.ExtensionContext | null = null;

let resolveFn: (value: vscode.ExtensionContext) => void = () => {};

const contextPromise: Promise<vscode.ExtensionContext> = new Promise(resolve => {
    resolveFn = resolve;
});

export function setExtensionContext(extContext: vscode.ExtensionContext) {
    context = extContext;
    resolveFn(extContext);
}

/**
 * 同步获取 extension context，如果在 extension activate 前执行代码，会获取失败
 * 仅在能保证获取成功的情况下使用
 */
export function getExtensionContext() {
    if (context == null) {
        throw new Error('Extension context not found');
    }
    return context;
}

/**
 * 异步获取 extension context，保证获取成功
 */
export function getExtensionContextAsync() {
    return contextPromise;
}
