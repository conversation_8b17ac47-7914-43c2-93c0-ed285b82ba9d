import fs from 'fs';
import https from 'https';
import http from 'http';
import {join} from 'path';
import {makeDirectorySync} from './fs';

export function download(url: string, destinationFolder: string, filename?: string): Promise<string> {
    return new Promise((resolve, reject) => {
        try {
            makeDirectorySync(destinationFolder);
        }
        catch (e) {
            reject(e);
            return;
        }

        const resolvedFilename = filename || url.split('/').pop() || `download_${Date.now()}`;
        const destination = join(destinationFolder, resolvedFilename);
        const file = fs.createWriteStream(destination);

        // 根据URL选择使用http还是https
        const client = url.startsWith('https://') ? https : http;

        const request = client.get(url, response => {
            if (response.statusCode !== 200) {
                reject(new Error('Server responded with status code ' + response.statusCode));
            }
            response.pipe(file);
        });

        file.on('finish', () => {
            file.close();
            resolve(destination);
        });

        file.on('error', err => {
            fs.unlink(destination, () => reject(err));
        });

        request.on('error', err => {
            fs.unlink(destination, () => reject(err));
        });
    });
}
