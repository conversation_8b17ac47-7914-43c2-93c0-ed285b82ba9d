export function removeEmptyLines(text: string) {
    const lines = text.split('\n');
    const nonEmptyLines = lines.filter(line => {
        // 移除行中的所有空白字符
        const trimmedLine = line.replace(/\s+/g, '');
        // 如果去除空白后还有内容，则保留该行
        return trimmedLine.length > 0;
    });

    return nonEmptyLines;
}

export function getTextWithLineTirm(text: string) {
    return removeEmptyLines(text).join('\n');
}
