import fs from 'node:fs/promises';
import path from 'path';
import * as vscode from 'vscode';
import {execCommand} from '@/utils/cp';

const candidateDirs = [
    'node_modules/@vscode/ripgrep/bin/',
    'node_modules/vscode-ripgrep/bin/',
    'node_modules.asar.unpacked/vscode-ripgrep/bin/',
    'node_modules.asar.unpacked/@vscode/ripgrep/bin/',
];

const isWindows = process.platform.startsWith('win');
const rgExe = isWindows ? 'rg.exe' : 'rg';

/**
 * 获取 vscode 包中自带的 ripgrep 二进制文件路径
 */
export async function getRgPath(): Promise<string | null> {
    for (const dir of candidateDirs) {
        const rgPath = path.resolve(vscode.env.appRoot, dir, rgExe);
        const exists = await fs
            .access(rgPath)
            .then(() => true)
            .catch(() => false);
        if (exists) {
            return rgPath;
        }
    }
    return null;
}

// 执行 rg 命令
export async function runRg(args: string[], rootPath: string, searchPath?: string) {
    const rgPath = await getRgPath();
    if (!rgPath) {
        throw new Error('Did not find the rg binary bundled with vscode.');
    }
    try {
        const command = `"${rgPath}" ${args.join(' ')} ${searchPath ? '-g ' + searchPath : './'}`;
        const stdout = await execCommand(command, rootPath, {timeout: 10 * 1000});
        return stdout;
    }
    catch (e) {
        throw e;
    }
}
