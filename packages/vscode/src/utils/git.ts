import path from 'node:path';
import pMemoize from 'p-memoize';
import {execCommand} from '@/utils/cp';
import {parseRemoteUri} from './parseRemoteUri';

export async function getRepoHostAndName(cwd: string) {
    const remoteUrl = await execCommand(
        'git config --get remote.origin.url',
        cwd
    )
        .catch(() => undefined);
    if (!remoteUrl) {
        return undefined;
    }
    const {host, repository} = parseRemoteUri(remoteUrl);
    if (host === 'localhost') {
        return undefined;
    }
    return `${host}/${repository}`;
}

export const getRepoHostAndNameMemoized = pMemoize(getRepoHostAndName);

export async function getRepoName(cwd: string) {
    const remoteUrl = await execCommand(
        'git config --get remote.origin.url',
        cwd
    )
        .catch(() => undefined);
    if (!remoteUrl) {
        return '';
    }
    const {repository} = parseRemoteUri(remoteUrl);
    return repository;
}

export const getRepoNameMemoized = pMemoize(getRepoName);

export async function getCurrentBranch(cwd: string) {
    const branch = await execCommand(
        'git symbolic-ref --short HEAD',
        cwd
    )
        .catch(() => undefined);
    return branch;
}

export async function getRepoRootPath(cwd: string) {
    const rootPath = await execCommand(
        'git rev-parse --show-toplevel',
        cwd
    )
        .catch(() => undefined);
    return rootPath;
}

export const getRepoRootPathMemoized = pMemoize(getRepoRootPath);

export async function isGitRepo(cwd: string) {
    const repoRoot = await getRepoRootPath(cwd);
    return repoRoot !== undefined;
}

export const getStagedDiffContent = async (cwd: string): Promise<string> => {
    const result = await execCommand(
        'git diff --cached',
        cwd
    );
    return result;
};

export const getLog = async (cwd: string): Promise<string> => {
    try {
        const result = await execCommand(
            'git log -n 10 --pretty=format:"%s"',
            cwd
        );
        return result;
    }
    catch (e) {
        return '';
    }
};

export const getLogByAuthor = async (cwd: string, author: string): Promise<string> => {
    try {
        const result = await execCommand(
            `git log -n 10 --pretty=format:"%s" --author="${author}"`,
            cwd
        );
        return result;
    }
    catch (e) {
        return '';
    }
};

export const getStagedStat = async (cwd: string): Promise<string> => {
    try {
        const result = await execCommand(
            'git diff --cached --stat',
            cwd
        );
        return result;
    }
    catch (e) {
        return '';
    }
};

export const getCurrentGitAuthor = async (cwd: string): Promise<string> => {
    try {
        const username = await execCommand(
            'git config user.name',
            cwd
        );
        const email = await execCommand(
            'git config user.email',
            cwd
        );
        const author = `${username.trim()} <${email.trim()}>`;
        return author;
    }
    catch (e) {
        return '';
    }
};

export const getTraceRepo = async (cwd: string) => {
    try {
        const repository = await getRepoNameMemoized(cwd);
        if (repository) {
            return repository;
        }
    }
    catch (ex) {
        //
    }

    // 取前两级
    return path.relative(path.resolve(cwd, path.join('..', '..')), cwd);
};
