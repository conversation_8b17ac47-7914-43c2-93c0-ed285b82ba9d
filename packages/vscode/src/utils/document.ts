import path from 'path';
import {ActivationContext} from '@comate/plugin-shared-internals';
import * as vscode from 'vscode';

interface ComputeCursorOffsetOptions {
    ignoreEmptySelection?: boolean; // true 时，如果光标选中部分为空，则不计算光标的位置
}

/**
 * 计算光标在当前活跃编辑器文件中的 offset 距离。如果光标选中了一部分文本，则返回选中文本的中点 offset。
 * @returns 光标 offset 距离
 */
export function computeCursorOffset(options?: ComputeCursorOffsetOptions) {
    const {ignoreEmptySelection = false} = options || {};
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        return;
    }
    const document = editor.document;
    const selection = editor.selection;
    if (ignoreEmptySelection && selection.isEmpty) {
        return;
    }
    const startOffset = document.offsetAt(selection.start);
    const endOffset = document.offsetAt(selection.end);
    return Math.round((startOffset + endOffset) / 2);
}

/**
 * 返回当前光标选中的内容和文件语言。
 * @returns [选中的文本，选中的语言]
 */
export function getCurrentSelection(): [string | undefined, string | undefined] {
    const editor = vscode.window.activeTextEditor;
    const document = editor?.document;
    if (document && !editor.selection.isEmpty) {
        return [document.getText(editor.selection), document.languageId];
    }
    return [undefined, undefined];
}

// 获取选中的范围的坐标
export function getCurrentSelectionRanges() {
    const editor = vscode.window.activeTextEditor;
    const ranges: Array<[vscode.Position, vscode.Position]> = [];
    if (editor) {
        const selections = editor.selections;
        for (const selection of selections) {
            ranges.push([selection.start, selection.end]);
        }
    }
    return ranges;
}

export function getCompleteFirstLine(document: vscode.TextDocument, range: vscode.Range) {
    const firstLineRange = new vscode.Range(range.start.line, 0, range.start.line, Number.MAX_SAFE_INTEGER);
    const content = document.getText(firstLineRange);
    return content;
}

/**
 * 开放平台获取当前上下文
 * @param document
 * @returns Omit<ActivationContext, 'query'>
 */
// eslint-disable-next-line complexity
export function activeFileContext(document?: vscode.TextDocument): Omit<ActivationContext, 'query'> {
    const editor = vscode.window.activeTextEditor;
    let lineContent = '';
    if (editor) {
        const position = editor.selection.active;
        const line = editor.document.lineAt(position.line);
        lineContent = line.text;
    }

    const activateDocument = document || vscode.window.activeTextEditor?.document;

    // 当前激活的文件相对于workspace的路径
    const relativePath = activateDocument ? vscode.workspace.asRelativePath(activateDocument?.uri) : undefined;
    // 当前激活的文件的文件名
    const fileName = activateDocument && activateDocument?.uri.scheme === 'file'
        ? path.basename(activateDocument?.fileName)
        : undefined;

    // 当前激活的文件的编程语言
    const language = activateDocument ? activateDocument?.languageId : undefined;
    return {
        activeFileContent: activateDocument?.getText() ?? '',
        activeFileLineContent: lineContent,
        activeFilePath: relativePath ?? '',
        activeFileName: fileName ?? '',
        activeFileLanguage: language ?? '',
        selectedCode: getCurrentSelection()[0] ?? '',
        selectedRange: vscode.window.activeTextEditor
            ? [vscode.window.activeTextEditor?.selection.anchor, vscode.window.activeTextEditor?.selection.active]
            : [],
    };
}
