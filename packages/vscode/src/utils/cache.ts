import crypto from 'crypto-js';
import {PromptInfo} from '../common/prompt/extractPrompt';

export function keyForPrompt(prompt: PromptInfo) {
    // eslint-disable-next-line new-cap
    return crypto.SHA256(prompt.prefix + '\u200B' + prompt.suffix).toString();
}

export class LRUCache<T> {
    values: Map<string, T>;
    lruKeys: string[];
    size: number;

    constructor(size = 10) {
        this.values = new Map();
        this.lruKeys = [];
        this.size = size;
    }

    removeKeyFromLRU(searchKey: string) {
        const key = this.lruKeys.indexOf(searchKey);
        if (key !== -1) {
            this.lruKeys.splice(key, 1);
        }
    }

    touchKeyInLRU(key: string) {
        this.removeKeyFromLRU(key);
        this.lruKeys.push(key);
    }

    clear() {
        this.values.clear();
        this.lruKeys = [];
    }

    deleteKey(key: string) {
        this.removeKeyFromLRU(key);
        if (this.values.get(key) !== undefined) {
            this.values.delete(key);
        }
    }

    get(key: string): T | undefined {
        if (this.values.has(key)) {
            const value = this.values.get(key);
            this.touchKeyInLRU(key);
            return value;
        }
        return undefined;
    }

    put(key: string, value: T) {
        let matches: string[] = [];
        if (this.values.has(key)) {
            matches = [key];
        }
        else if (this.lruKeys.length >= this.size) {
            matches = this.lruKeys.splice(0, 1);
        }

        for (const key of matches) {
            this.deleteKey(key);
        }
        this.values.set(key, value);
        this.touchKeyInLRU(key);
    }

    has(key: string): boolean {
        return this.values.has(key);
    }
}
