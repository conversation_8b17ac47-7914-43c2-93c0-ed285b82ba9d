type FunctionWithArgs<R, A extends any[] = any[]> = (...args: A) => R;

export function throttle<R, A extends any[]>(func: FunctionWithArgs<Promise<R> | R, A>, wait: number) {
    if (typeof func !== 'function') {
        throw new TypeError('Expected a function');
    }
    let lastComputeTime = 0;
    // eslint-disable-next-line @typescript-eslint/init-declarations
    let memoizedValue: Promise<R> | R | undefined;
    return function (...args: A) {
        if (Date.now() - lastComputeTime > wait) {
            // @ts-expect-error
            memoizedValue = func.apply(this, args);
            lastComputeTime = Date.now();
        }
        return memoizedValue;
    };
}
