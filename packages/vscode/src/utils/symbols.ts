import * as vscode from 'vscode';
import {SearchSymbol} from '../api/codeSearch';
import {findMatch} from './common';

export const SYMBOL_KIND_MAPPING: Record<SearchSymbol['kind'], vscode.SymbolKind> = {
    FILE: vscode.SymbolKind.File,
    MODULE: vscode.SymbolKind.Module,
    NAMESPACE: vscode.SymbolKind.Namespace,
    PACKAGE: vscode.SymbolKind.Package,
    CLASS: vscode.SymbolKind.Class,
    ENUM: vscode.SymbolKind.Enum,
    INTERFACE: vscode.SymbolKind.Interface,
    STRUCT: vscode.SymbolKind.Struct,
    METHOD: vscode.SymbolKind.Method,
    CONSTRUCTOR: vscode.SymbolKind.Constructor,
    FUNCTION: vscode.SymbolKind.Function,
    STRING: vscode.SymbolKind.String,
    NUMBER: vscode.SymbolKind.Number,
    BOOLEAN: vscode.SymbolKind.Boolean,
    ARRAY: vscode.SymbolKind.Array,
    OBJECT: vscode.SymbolKind.Object,
    NULL: vscode.SymbolKind.Null,
    VARIABLE: vscode.SymbolKind.Variable,
    CONSTANT: vscode.SymbolKind.Constant,
    PROPERTY: vscode.SymbolKind.Property,
    FIELD: vscode.SymbolKind.Field,
    KEY: vscode.SymbolKind.Key,
    ENUMMEMBER: vscode.SymbolKind.EnumMember,
    TYPEPARAMETER: vscode.SymbolKind.TypeParameter,
    EVENT: vscode.SymbolKind.Event,
    OPERATOR: vscode.SymbolKind.Operator,
};

async function searchWorkspaceSymbol(symbol: SearchSymbol, file?: string | undefined) {
    const symbols = await vscode.commands.executeCommand<vscode.SymbolInformation[]>(
        'vscode.executeWorkspaceSymbolProvider',
        symbol.name
    );
    if (!symbols) {
        return undefined;
    }
    const result = symbols.find(item => {
        if (!item.name.startsWith(symbol.name)) {
            return false;
        }
        if (Object.keys(SYMBOL_KIND_MAPPING).includes(symbol.kind) && item.kind !== SYMBOL_KIND_MAPPING[symbol.kind]) {
            return false;
        }
        if (file && file !== item.location.uri.fsPath) {
            return false;
        }
        // 全局搜symbol时排除.class
        if (item.location.uri.fsPath.endsWith('.class')) {
            return false;
        }
        return true;
    });
    return result;
}

export async function getDetailedSymbolInfo(symbol: vscode.SymbolInformation) {
    const documentSymbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
        'vscode.executeDocumentSymbolProvider',
        symbol.location.uri
    );
    if (!documentSymbols) {
        throw new Error(`Failed to get symbols from the file ${symbol.location.uri.fsPath}`);
    }
    const symbolName = (() => {
        if (symbol.kind === vscode.SymbolKind.Function || symbol.kind === vscode.SymbolKind.Method) {
            return symbol.name.replace(/\(\)$/, '');
        }
        return symbol.name;
    })();
    const detailedSymbolInfo = findMatch(
        documentSymbols,
        item => item.name === symbolName && item.kind === symbol.kind
    );
    return detailedSymbolInfo;
}

export async function getDocumentSymbols(uri: vscode.Uri) {
    const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
        'vscode.executeDocumentSymbolProvider',
        uri
    );
    return symbols;
}

export async function getSymbolDefinition(symbol: SearchSymbol, uri?: vscode.Uri) {
    if (symbol.name.trim().length === 0) {
        throw new Error('The symbol name cannot be empty');
    }
    const symbolDefinition = await searchWorkspaceSymbol(symbol, uri?.fsPath);
    if (!symbolDefinition) {
        throw new Error('No matching symbol found in the current workspace');
    }
    return symbolDefinition;
}

export async function getSymbolReferences(symbol: SearchSymbol, uri?: vscode.Uri) {
    const symbolDefinition = await searchWorkspaceSymbol(symbol, uri?.fsPath);
    if (!symbolDefinition) {
        throw new Error('No matching symbol found in the current workspace');
    }
    const detailedSymbolInfo = await getDetailedSymbolInfo(symbolDefinition);
    if (!detailedSymbolInfo) {
        // eslint-disable-next-line max-len
        throw new Error(
            `Did not find the symbol ${symbolDefinition.name} from ${symbolDefinition.location.uri.fsPath}`
        );
    }
    const references = await vscode.commands.executeCommand<vscode.Location[]>(
        'vscode.executeReferenceProvider',
        symbolDefinition.location.uri,
        detailedSymbolInfo.selectionRange.start
    );
    return references;
}

export async function getSymbolImplementations(symbol: SearchSymbol, uri?: vscode.Uri) {
    const symbolDefinition = await searchWorkspaceSymbol(symbol, uri?.fsPath);
    if (!symbolDefinition) {
        throw new Error('No matching symbol found in the current workspace');
    }
    const detailedSymbolInfo = await getDetailedSymbolInfo(symbolDefinition);
    if (!detailedSymbolInfo) {
        // eslint-disable-next-line max-len
        throw new Error(
            `Did not find the symbol ${symbolDefinition.name} from ${symbolDefinition.location.uri.fsPath}`
        );
    }
    const implementations = await vscode.commands.executeCommand<Array<vscode.Location | vscode.LocationLink>>(
        'vscode.executeImplementationProvider',
        symbolDefinition.location.uri,
        detailedSymbolInfo.selectionRange.start
    );
    return implementations;
}
