import * as vscode from 'vscode';

export function getHtmlForWebview(scriptPath: vscode.Uri | undefined, title: string, extra: string = '') {
    return `<!DOCTYPE html>
        <html lang="zh-CN" style="width:100%;height:100%;">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
        </head>
        <body class="${process.platform}" style="padding:0;margin:0;width:100%;height:100%">
            ${extra}
            ${scriptPath ? `<script src="${scriptPath}"></script>` : ''}
        </body>
        </html>`;
}
