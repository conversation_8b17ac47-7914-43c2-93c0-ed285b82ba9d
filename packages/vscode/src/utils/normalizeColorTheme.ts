import * as vscode from 'vscode';

export function normalizeColorTheme(kind: vscode.ColorThemeKind) {
    switch (kind) {
        case vscode.ColorThemeKind.Dark:
        case vscode.ColorThemeKind.HighContrast:
            return 'dark';
        case vscode.ColorThemeKind.HighContrastLight:
        case vscode.ColorThemeKind.Light:
            return 'light';
        default:
            return 'dark';
    }
}
