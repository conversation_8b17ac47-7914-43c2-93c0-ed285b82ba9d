import * as vscode from 'vscode';
import axios from 'axios';
import {iocContainer} from '@/iocContainer';
import {VSCodeConfigProvider} from '../services/ConfigProvider';
import {getDeviceUUIDThrottled} from './deviceUUID';
import {getExtensionContextAsync} from './extensionContext';

const hostUrl: string = 'https://comate.baidu.com/logger/comate.log';

function sendLogBase(type: string, payload: any) {
    return axios.post(
        hostUrl,
        {type, ...payload, platform: $features.PLATFORM},
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
}

async function getCommonPayload() {
    const timestamp = Date.now();
    const context = await getExtensionContextAsync();
    const app = 'vscode-ide';
    const version = context.extension.packageJSON.version;
    const session = vscode.env.machineId;
    return {
        app,
        version,
        session,
        timestamp,
        from: vscode.env.appName,
    };
}

export type ComateAction = 'active' | 'login' | 'click-banner';

export type Category = 'chat-view' | 'extension';

export type EventType = 'click' | 'success' | 'failure' | number;

/**
 * @deprecated 废弃，使用 logProvider的 logUserAction
 */
export async function reportEvent(category: Category, action: ComateAction, type: EventType) {
    try {
        const configProvider = iocContainer.get(VSCodeConfigProvider);
        const license = configProvider.getLicense();
        const clientId = await getDeviceUUIDThrottled();
        const commonPayload = await getCommonPayload();
        await sendLogBase('event', {
            common: {
                ...commonPayload,
                isLogin: !!license,
                clientId,
                license,
            },
            event: {category, action, type},
        });
    }
    catch {
        // noop
    }
}
