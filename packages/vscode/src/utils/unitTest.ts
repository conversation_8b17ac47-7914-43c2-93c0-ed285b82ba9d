import {join, parse, basename, relative} from 'node:path';
import {statSync} from 'node:fs';

/**
 * 根据给定的文件**绝对路径**，查找存在的单元测试文件，先匹配的先返回，仅返回第一个
 * @returns string | null
 */
export const findRelatedUnitTest = (rootPath: string, relativePath: string) => {
    try {
        const {dir, ext, name} = parse(join(rootPath, relativePath));
        const possibleTestFilePaths = [
            join(dir, `${name}.spec${ext}`),
            join(dir, `${name}.test${ext}`),
            join(dir, `${name}_test${ext}`),
            join(dir, `${name}Test${ext}`),
            join(dir, '__tests__', `${name}.test${ext}`),
            join(dir, '__tests__', `${name}.spec${ext}`),
            join(dir, '__tests__', `${name}_test${ext}`),
            join(dir, '__tests__', `${name}Test${ext}`),
            join(dir, '__tests__', `index${ext}`),
        ];

        for (const path of possibleTestFilePaths) {
            try {
                // eslint-disable-next-line max-depth
                if (statSync(path).isFile()) {
                    return {
                        absolutePath: path,
                        relativePath: relative(rootPath, path),
                        name: basename(path),
                    };
                }
            }
            catch (ex) {
                //
            }
        }

        return null;
    }
    catch (ex) {
        return null;
    }
};
