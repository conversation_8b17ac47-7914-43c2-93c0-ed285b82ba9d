const defaultDistance = (sourceChar: string | number, targetChar: string | number) => {
    return sourceChar === targetChar ? 0 : 1;
};

type EndCharDistance = (
    sourceChar: string | number,
    targetChar: string | number,
    sourceIndex: number,
    targetIndex: number
) => number;

// eslint-disable-next-line complexity, max-statements
export function editDistance(
    source: number[] | string,
    target: number[] | string,
    getEndCharDistance: EndCharDistance = defaultDistance
) {
    if (target.length === 0 || source.length === 0) {
        return {
            distance: target.length,
            startOffset: 0,
            endOffset: 0,
        };
    }

    // dp
    let n = new Array(target.length + 1).fill(0);
    let i = new Array(target.length + 1).fill(0);
    let s = new Array(source.length + 1).fill(0);
    let o = new Array(source.length + 1).fill(0);
    let targetElement = target[0];

    for (let t = 0; t < source.length + 1; t++) {
        n[t] = t === 0 ? 1 : getEndCharDistance(source[t - 1], targetElement, t - 1, 0);
        i[t] = t > 0 ? t - 1 : 0;
    }

    for (let c = 1; c < target.length; c++) {
        let l = s;
        s = n;
        n = l;
        l = o;
        o = i;
        i = l;
        targetElement = target[c];
        n[0] = c + 1;
        for (let t = 1; t < source.length + 1; t++) {
            const l = 1 + s[t];
            const u = 1 + n[t - 1];
            const p = getEndCharDistance(source[t - 1], targetElement, t - 1, c) + s[t - 1];
            // 取 dp[i-1][j-1] + endCharDistance、dp[i-1][j]、dp[i][j-1] 中的最小值
            n[t] = Math.min(u, l, p);
            if (n[t] === p) {
                i[t] = o[t - 1];
            }
            else if (n[t] === l) {
                i[t] = o[t];
            }
            else {
                i[t] = i[t - 1];
            }
        }
    }
    let c = 0;
    for (let t = 0; t < source.length + 1; t++) {
        n[t] < n[c] && (c = t);
    }
    return {
        distance: n[c],
        startOffset: i[c],
        endOffset: c,
    };
}

export function emptyLexDictionary() {
    return new Map();
}

export function reverseLexDictionary(dict: Map<string, number>) {
    const reversedDict = new Array(dict.size);
    for (const [key, value] of dict) {
        reversedDict[value] = key;
    }
    return reversedDict;
}

export function* lexGeneratorWords(text: string) {
    const tokenTypes: Record<any, any> = {};
    let currentLexeme = '';
    tokenTypes[tokenTypes.Word = 0] = 'Word';
    tokenTypes[tokenTypes.Space = 1] = 'Space';
    tokenTypes[tokenTypes.Other = 2] = 'Other';

    let currentToken = tokenTypes.Word;
    for (const char of text) {
        const charType = /(\p{L}|\p{Nd}|_)/u.test(char)
            ? tokenTypes.Word
            : char === ' '
            ? tokenTypes.Space
            : tokenTypes.Other;
        if (charType === currentToken && charType !== tokenTypes.Other) {
            currentLexeme += char;
        }
        else {
            if (currentLexeme.length > 0) {
                yield currentLexeme;
            }
            currentLexeme = char;
            currentToken = charType;
        }
    }
    if (currentLexeme.length > 0) {
        yield currentLexeme;
    }
}

export function lexicalAnalyzer(
    text: string,
    dict: Map<string, number>,
    lexGenerator: (text: string) => Generator<string, void, unknown>,
    isTokenValid: (token: string) => boolean
) {
    const tokens: Array<[number, number]> = [];
    let offset = 0;
    for (const token of lexGenerator(text)) {
        if (isTokenValid(token)) {
            if (!dict.has(token)) {
                dict.set(token, dict.size);
            }
            // @ts-expect-error
            tokens.push([dict.get(token), offset]);
        }
        offset += token.length;
    }
    return [tokens, dict] as const;
}

function isNotSpace(char: string) {
    return char !== ' ';
}

// 计算 targetText 到 sourceText 任意字串中的最小编辑距离
export function lexEditDistance(sourceText: string, targetText: string, lexGenerator = lexGeneratorWords) {
    const [sourceTokens, sourceDict] = lexicalAnalyzer(
        sourceText,
        emptyLexDictionary(),
        lexGenerator,
        isNotSpace
    );
    const [targetTokens, targetDict] = lexicalAnalyzer(targetText, sourceDict, lexGenerator, isNotSpace);
    if (targetTokens.length === 0 || sourceTokens.length === 0) {
        return {
            lexDistance: targetTokens.length,
            startOffset: 0,
            endOffset: 0,
            haystackLexLength: sourceTokens.length,
            needleLexLength: targetTokens.length,
        };
    }
    const reversedTargetDict = reverseLexDictionary(targetDict);
    const targetLen = targetTokens.length;
    const targetStartToken = reversedTargetDict[targetTokens[0][0]];
    const targetEndToken = reversedTargetDict[targetTokens[targetLen - 1][0]];
    const editDist = editDistance(
        sourceTokens.map(token => {
            return token[0];
        }),
        targetTokens.map(token => {
            return token[0];
        }),
        (source, target, sourceIdx, targetIdx) => {
            // 计算 dp[0][j] 时判断的是 sourceToken[i] 是否以 targetTokens[0] 开头
            // 效果是在 source 里边取一个字串，与 target 的编辑距离最小
            if (targetIdx === 0 || targetIdx === targetLen - 1) {
                const sourceToken = reversedTargetDict[sourceTokens[sourceIdx][0]];
                const dist = targetIdx === 0 && sourceToken.endsWith(targetStartToken)
                    || targetIdx === targetLen - 1 && sourceToken.startsWith(targetEndToken);
                return dist ? 0 : 1;
            }
            return source === target ? 0 : 1;
        }
    );
    const startOffset = sourceTokens[editDist.startOffset][1];
    let endOffset = editDist.endOffset < sourceTokens.length
        ? sourceTokens[editDist.endOffset][1]
        : sourceText.length;
    if (endOffset > 0 && sourceText[endOffset - 1] === ' ') {
        --endOffset;
    }
    return {
        lexDistance: editDist.distance,
        startOffset,
        endOffset,
        haystackLexLength: sourceTokens.length,
        needleLexLength: targetTokens.length,
    };
}
