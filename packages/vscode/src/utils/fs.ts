import {access} from 'node:fs/promises';
import {homedir} from 'node:os';
import {join} from 'path';
import fs from 'fs';
import path from 'path';
import {debug} from '@/common/outputChannel';

export const makeDirectorySync = (path: string) => {
    if (!fs.existsSync(path)) {
        fs.mkdirSync(path, {recursive: true});
    }
};

export async function isFileExist(path: string): Promise<boolean> {
    try {
        await access(path);
        return true;
    }
    catch (e) {
        debug('file does not exist:', (e as Error).message);
        return false;
    }
}

export async function ensureFilePathExists(filePath: string): Promise<boolean> {
    const dir = path.dirname(filePath); // 获取文件所在目录

    try {
        // 递归创建文件夹，确保路径存在
        await fs.promises.mkdir(dir, {recursive: true});
        return true;
    }
    catch (err) {
        return false;
    }
}

export const COMATE_HOME_FOLDER = join(homedir(), '.comate');
