export function assertFulfilled<T>(item: PromiseSettledResult<T>): item is PromiseFulfilledResult<T> {
    return item.status === 'fulfilled';
}

export const TIMEOUT_ERROR = Symbol('timeout');

export async function timeout<T>(promise: Promise<T>, ms: number): Promise<T> {
    let timer: NodeJS.Timer | null = null;
    const timeoutPromise = new Promise<never>((_, reject) => {
        timer = setTimeout(reject, ms, TIMEOUT_ERROR);
    });
    return Promise.race([promise, timeoutPromise]).finally(() => {
        timer && clearTimeout(timer);
    });
}

export async function timeoutSafe<T>(promise: Promise<T>, ms: number, fallbackWhenTimeout: T): Promise<T> {
    return timeout(promise, ms).catch(e => {
        if (e === TIMEOUT_ERROR) {
            return fallbackWhenTimeout;
        }
        throw e;
    });
}
