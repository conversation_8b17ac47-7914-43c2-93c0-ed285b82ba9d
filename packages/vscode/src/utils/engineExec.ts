/* eslint-disable @typescript-eslint/no-use-before-define */
import {platform, arch} from 'node:process';
import {chmod, unlink} from 'node:fs/promises';
import path from 'node:path';
import os from 'node:os';
import {spawn, fork, ChildProcess} from 'node:child_process';
import {exit} from 'node:process';
import * as vscode from 'vscode';
import {compare} from 'compare-versions';
import {execCommand} from '@/utils/cp';
import {LogCategory, LogUploaderProvider} from '@/services/LogUploaderProvider';
import {LogLevel, log, error} from '@/common/outputChannel';
import {COMATE_CONFIG_PREFIX} from '@/constants';
import {download} from './download';
import {isFileExist} from './fs';
import {isSaaS} from './features';
import {getFileMD5} from './getFileMD5';
import {checkConnectivity} from './testNet';
import {getExtensionContextAsync} from './extensionContext';

const activeChildProcesses: ChildProcess[] = [];

// TODO 为避免引入新问题，先不做下载压缩包的逻辑，直接下载二进制文件
// 已有资源的md5值
export const nodeVersionToMD5Map = {
    'node-darwin-arm64': '169284e44a7b377a4ced4d15bc51b779',
    'node-darwin-x64': '2ab351eb13de4ebcaf227308b38ffd7c',
    'node-linux-arm64': '6a2793450cc8eb1a14150749c228658a',
    'node-linux-x64': '1fce89e0c6471cbf13902e80cce35f20',
    'node-patchelf-linux-x64': '6909ac3306820d6294c925a4b37da04e',
    'node-win-arm64.exe': '49e7379f9362ac4fe59eb120bae747db',
    'node-win-x64.exe': '2b2f53960f71d4a7d833c8000132b08a',
    'node-win-x86.exe': '8f27195cddefed8d79a88ac1dc645978',
};

const gccPath = '/opt/compiler/gcc-10/lib64/ld-linux-x86-64.so.2';

const productEngineEntry = 'comate-engine/bin/comate.js';
const devEngineEntry = '../../engine-connector/bin/comate.js';

const engineExecCommand = process.env.NODE_ENV === 'development'
    ? `--experimental-default-type=module --experimental-json-modules --inspect=6019 ${
        path.resolve(__dirname, devEngineEntry)
    }`
    : `--experimental-default-type=module ${productEngineEntry}`;

const engineExecCommandByIDENode = process.env.NODE_ENV === 'development'
    ? path.resolve(__dirname, devEngineEntry)
    : productEngineEntry;

const fallbackEngineExecCommand = process.env.NODE_ENV === 'development'
    ? path.resolve(__dirname, '../../engine-connector/dist/fallbackServer.js')
    : path.resolve(__dirname, 'comate-engine/fallbackServer.js');

// eslint-disable-next-line complexity
export const getNodePlatform = async () => {
    if (platform === 'darwin') {
        if (arch === 'arm64' || arch === 'arm') {
            return 'node-darwin-arm64';
        }
        if (arch === 'x64') {
            return 'node-darwin-x64';
        }
    }
    else if (platform === 'linux') {
        if (arch === 'arm64' || arch === 'arm') {
            return 'node-linux-arm64';
        }
        if (arch === 'x64') {
            // 如果是icoding并且gcc存在，使用patchelf版本
            if (vscode.env.appName.toLowerCase() === 'icoding' && (await isFileExist(gccPath))) {
                return 'node-patchelf-linux-x64';
            }
            return 'node-linux-x64';
        }
    }
    else if (platform === 'win32') {
        if (arch === 'x64') {
            return 'node-win-x64.exe';
        }
        if (arch === 'ia32') {
            return 'node-win-x86.exe';
        }
        if (arch === 'arm64' || arch === 'arm') {
            return 'node-win-arm64.exe';
        }
    }
    return undefined;
};

const DOWNLOAD_HOST = ['now.bdstatic.com', 'vercel-static.bj.bcebos.com', 'comate.baidu.com'];
export async function getDownloadUrl(platform: string) {
    const httpHost = vscode.workspace.getConfiguration(COMATE_CONFIG_PREFIX).get<string>('privateService');
    if ($features.PLATFORM === 'poc') {
        return `${httpHost}/storage/plugin-resource/node/${platform}`;
    }
    const connected = await Promise.all(DOWNLOAD_HOST.map(host => checkConnectivity(host, 80)));
    const firstConnectedIndex = connected.findIndex(connectedHost => connectedHost);
    return `https://${DOWNLOAD_HOST[firstConnectedIndex] || 'now.bdstatic.com'}`
        + `/stash/v1/dc8c989/frontend/92932cd/resources/node/${platform}`;
}

export function getNodeBinaryPath() {
    return path.join(os.homedir(), '.comate-engine', 'bin', 'node' + (platform === 'win32' ? '.exe' : ''));
}

export async function downloadNodeBinary(downloadFrom: string, downloadTo: string): Promise<void> {
    const destFolder = path.dirname(downloadTo);
    const binaryName = path.basename(downloadTo);
    await download(downloadFrom, destFolder, binaryName);
    await chmod(downloadTo, 0o755);
    await execCommand(`${downloadTo} -v`, destFolder);
}

export async function ensureNodeExists(
    logger: LogUploaderProvider,
    category: LogCategory
): Promise<string | undefined> {
    const downloadTo = getNodeBinaryPath();
    const isExist = await isFileExist(downloadTo);
    const nodePlatform = await getNodePlatform();
    // 没有对应的平台
    if (!nodePlatform) {
        logger.logUserAction({
            category,
            content: 'no suitable node version found',
        }, 'error');
        log(LogLevel.Debug, 'no suitable node version found');
        return undefined;
    }
    const downloadFrom = await getDownloadUrl(nodePlatform);
    if (isExist) {
        try {
            log(LogLevel.Debug, 'isExist');
            // 已经存在的文件
            const md5 = await getFileMD5(downloadTo);
            // 没有下载完整的文件删掉
            if (md5 !== nodeVersionToMD5Map[nodePlatform]) {
                await unlink(downloadTo);
                await downloadNodeBinary(downloadFrom, downloadTo);
            }
            await execCommand(`${downloadTo} -v`, vscode.env.appRoot);
            return downloadTo;
        }
        catch (e) {
            log(LogLevel.Debug, `existed error: ${String(e)}`, downloadFrom, downloadTo);
            logger.logUserAction({
                category,
                content: `existed error: ${String(e)}`,
            }, 'error');
            return undefined;
        }
    }
    log(LogLevel.Debug, 'not Exist', downloadFrom, downloadTo);
    try {
        await downloadNodeBinary(downloadFrom, downloadTo);
        return downloadTo;
    }
    catch (e) {
        log(LogLevel.Error, `download exec error: ${String(e)}`, downloadFrom, downloadTo);
        logger.logUserAction({
            category,
            content: `download exec error: ${String(e)}`,
        }, 'error');
        return undefined;
    }
}

// eslint-disable-next-line no-undef-init
let pid: number | undefined = undefined;

export const getEnginePid = () => pid;

export async function engineExec(logger: LogUploaderProvider) {
    const httpProxyUrl = vscode.workspace.getConfiguration('http').get<string>('proxy');
    const httpHost = vscode.workspace.getConfiguration(COMATE_CONFIG_PREFIX).get<string>('privateService');
    const context = await getExtensionContextAsync();
    // TODO 这几个变量依赖的逻辑都要删掉
    const options = {
        shell: true,
        cwd: __dirname,
        env: {
            ...process.env,
            IDE: 'vscode',
            COMATE_VERSION: context.extension.packageJSON.version,
            // Playground | VS Code
            COMATE_ENGINE_APP_NAME: vscode.env.appName.toLowerCase() === 'icoding' ? 'icoding' : 'VS Code',
            COMATE_ENGINE_PLATFORM: isSaaS ? 'baidu-saas' : 'baidu-int',
            COMATE_ENGINE_DEBUG: process.env.NODE_ENV === 'development' ? 'true' : 'false',
            PLATFORM: $features.PLATFORM,
            ENVIRONMENT: process.env.NODE_ENV,
            HTTP_PROXY_URL: httpProxyUrl,
            HTTP_HOST: httpHost,
        },
    };
    const isLowerThan182 = compare(vscode.version, '1.82.0', '<=');
    const isHigherThan188 = compare(vscode.version, '1.88.0', '>=');

    if (isHigherThan188) {
        const command = engineExecCommandByIDENode;
        log(LogLevel.Debug, 'high engine fork', command);
        const child = fork(command, [], {
            stdio: 'pipe',
            execArgv: [],
            ...options,
        });
        addMonitor(child, logger);
        pid = child.pid;
        return child;
    }

    const nodePath = await ensureNodeExists(logger, LogCategory.Engine);
    if (!nodePath) {
        let command = engineExecCommandByIDENode;
        // 1.82.0版本之前不能运行engine，使用fallbackServer，不让ide抛错
        if (isLowerThan182) {
            error('Failed to run node binary');
            command = fallbackEngineExecCommand;
        }
        log(LogLevel.Debug, 'engine fork', command);
        const fallbackChild = fork(command, [], {
            stdio: 'pipe',
            ...options,
        });
        addMonitor(fallbackChild, logger);
        pid = fallbackChild?.pid;
        return fallbackChild;
    }

    const child = spawn(`${nodePath} ${engineExecCommand}`, options);
    pid = child.pid;

    log(LogLevel.Debug, 'engine pid', child.pid);
    addMonitor(child, logger);
    return child;
}

function addMonitor(child: ChildProcess, logger: LogUploaderProvider) {
    activeChildProcesses.push(child);
    // 定义具体的事件处理函数，这样可以在清理时精确移除
    const handleStderr = (data: Buffer) => {
        console.error(`stderr: ${data}`);
        logger.logUserAction({
            category: LogCategory.Engine,
            content: `engine stderr: ${data}`,
        }, 'error');
    };

    const handleError = (error: Error) => {
        console.error(`exec error: ${error}`);
        logger.logUserAction({
            category: LogCategory.Engine,
            content: `exec error: ${error}`,
        }, 'error');
        cleanupListeners();
    };

    const handleClose = (code: number | null, sig: string | null) => {
        if (code !== 0) {
            console.error(`engine process close with code ${code}-${sig}`);
            logger.logUserAction({
                category: LogCategory.EngineInit,
                action: 'close',
                label: String(code),
                content: `exec close: ${code}-${sig}`,
            }, 'error');
        }
        cleanupListeners();
        const index = activeChildProcesses.indexOf(child);
        if (index > -1) {
            activeChildProcesses.splice(index, 1);
        }
    };

    const handleDisconnect = () => {
        console.info('engine process disconnect................ ');
    };

    const handleExit = (code: number | null, sig: string | null) => {
        if (code !== 0) {
            console.error(`engine process exited with code ${code}-${sig}`);
            logger.logUserAction({
                category: LogCategory.EngineInit,
                action: 'exit',
                label: String(code),
                content: `exec exit: ${code}-${sig}`,
            }, 'error');
        }
    };

    const cleanupListeners = () => {
        // 精确移除每个监听器
        child?.stderr?.removeListener('data', handleStderr);
        child.removeListener('error', handleError);
        child.removeListener('close', handleClose);
        child.removeListener('disconnect', handleDisconnect);
        child.removeListener('exit', handleExit);
    };

    // 添加事件监听器
    child?.stderr?.on('data', handleStderr);
    child.on('error', handleError);
    child.on('close', handleClose);
    child.on('disconnect', handleDisconnect);
    child.on('exit', handleExit);
}

// 进程退出后把子进程也干掉
['SIGINT', 'SIGTERM', 'beforeExit', 'exit'].forEach(signal => {
    process.on(signal, () => {
        activeChildProcesses.forEach(childProcess => {
            try {
                if (!childProcess.killed) {
                    childProcess.kill();
                }
            }
            catch (err) {
                console.error(`Failed to kill child process (PID: ${childProcess.pid}):`, err);
            }
        });

        activeChildProcesses.length = 0;

        if (signal !== 'exit') {
            exit(0);
        }
    });
});
