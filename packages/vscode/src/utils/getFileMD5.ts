import crypto from 'crypto';
import fs from 'fs';

export function getFileMD5(filePath: string) {
    return new Promise((resolve, reject) => {
        const hash = crypto.createHash('md5'); // 创建md5 hash
        const stream = fs.createReadStream(filePath); // 创建文件读取流

        stream.on('data', chunk => {
            hash.update(chunk); // 更新hash内容
        });

        stream.on('end', () => {
            resolve(hash.digest('hex')); // 返回hex格式的hash值
        });

        stream.on('error', err => {
            reject(err);
        });
    });
}
