/* eslint-disable max-len */
import {join} from 'node:path';
import * as vscode from 'vscode';
import {KnowledgeList, Metadata} from '@shared/protocols';
import {SlashType} from '@shared/constants';
import {
    CMD_CHAT_WITH_TERMINAL_SELECTION_CONTEXT,
    CMD_START_AUTO_WORK_SESSION,
} from '@/services/AutoComateChatSession/constants';
import {RegisteredCommand} from '@/constants';
import {removeOverlapedEOLChar} from './terminal';

interface OpenChatPanelOptions {
    // 聊天面板的标题
    activeTabKey?: string;
    source?: string;
}

export interface QueryPromptConfig {
    /** 界面上展示的query */
    query: string;
    /** 真实用户的query，对应发送给服务端的参数 */
    prompt?: string;
    agent: string;
    slash?: SlashType;
    skipAnalyze?: boolean;
    knowledgeList?: KnowledgeList[];
    responseReminder?: string;
    disableCode?: boolean;
    metadata?: Metadata;
    chatIntentRecognition?: boolean;
}

export function askAutoWork(messageId: number, queryPromptConfig: QueryPromptConfig): void;
export function askAutoWork(queryPromptConfig: QueryPromptConfig): void;
export function askAutoWork(...args: any[]) {
    vscode.commands.executeCommand(CMD_START_AUTO_WORK_SESSION, ...args);
}

interface AutoWorkParameters {
    regenMessageId: number | undefined;
    agent: string;
    prompt?: string;
    query: string;
    slash: SlashType;
    skipAnalyze?: boolean;
    metadata?: Metadata;
    chatIntentRecognition: boolean;
    selectedResources: KnowledgeList[];
    responseReminder?: string;
    disableCode?: boolean;
}

export function spreadAskAutoWorkParameters(
    messageId: number,
    queryPromptConfig: QueryPromptConfig
): AutoWorkParameters;
export function spreadAskAutoWorkParameters(queryPromptConfig: QueryPromptConfig): AutoWorkParameters;
export function spreadAskAutoWorkParameters(...args: any[]): AutoWorkParameters {
    const regenMessageId = typeof args[0] === 'number' ? args[0] : undefined;
    const config = typeof args[0] === 'number' ? args[1] : args[0];
    const {
        skipAnalyze,
        metadata,
        query,
        prompt,
        slash = SlashType.ASK_V2,
        knowledgeList = [],
        agent,
        responseReminder,
        disableCode,
        chatIntentRecognition,
    } = config;

    return {
        regenMessageId,
        query,
        prompt,
        slash,
        agent,
        skipAnalyze,
        metadata,
        selectedResources: knowledgeList,
        chatIntentRecognition,
        responseReminder,
        disableCode,
    };
}

export const vscodeCommands = {
    openChatPanel(opts?: OpenChatPanelOptions) {
        vscode.commands.executeCommand('baidu.comate.showChatPanel', opts);
    },
    askAutoWork,
    chatWithTerminalSelectionContext(selection: string) {
        vscode.commands.executeCommand(CMD_CHAT_WITH_TERMINAL_SELECTION_CONTEXT, {selection});
    },
    cancelInlineChat(path: string) {
        return vscode.commands.executeCommand(RegisteredCommand.cancelInlineChat, path);
    },
    async exposeTerminalOutput(activeTerminal?: vscode.Terminal) {
        await vscode.commands.executeCommand('workbench.action.terminal.selectAll');
        const terminal = activeTerminal || vscode.window.activeTerminal;
        if (terminal) {
            // @ts-expect-error 实际是有的
            const selection = terminal.selection;
            await vscode.commands.executeCommand('workbench.action.terminal.clearSelection');
            if (typeof selection === 'string') {
                return removeOverlapedEOLChar(selection);
            }
        }
        return '';
    },
    async findDefinitions(path: string, position: vscode.Position) {
        const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!rootPath) {
            return [];
        }
        const uri = vscode.Uri.file(join(rootPath, path));
        const definitions = await vscode.commands.executeCommand<vscode.Location[]>(
            'vscode.executeDefinitionProvider',
            uri,
            position
        );

        return definitions;
    },
    async findTypeDefinitions(uri: vscode.Uri, position: vscode.Position) {
        const definitions = await vscode.commands.executeCommand<vscode.Location[]>(
            'vscode.executeTypeDefinitionProvider',
            uri,
            position
        );

        return definitions;
    },
    async getCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position
    ): Promise<vscode.CompletionItem[]> {
        const {items} = await vscode
            .commands
            .executeCommand(
                'vscode.executeCompletionItemProvider',
                document.uri,
                position,
                '.'
            ) as any;
        return items;
    },
    openUrlInEditorWebview(params: {url: string, title: string}) {
        const panel = vscode.window.createWebviewPanel(
            'customWebview',
            params.title,
            vscode.ViewColumn.One,
            {enableScripts: true}
        );
        panel.webview.html = `
            <!DOCTYPE html>
            <html lang="en">
            <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${params.title}</title>
            <style>
                body, html {
                    margin: 0;
                    padding: 0;
                    height: 100%;
                    overflow: hidden;
                }
                iframe {
                    width: 100%;
                    height: 100%;
                    border: none;
                }
            </style>
            </head>
            <body>
                <iframe src="${params.url}"></iframe>
            </body>
            </html>
        `;
    },
};
