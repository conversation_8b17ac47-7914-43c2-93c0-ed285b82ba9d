export interface ContextIndentation {
    prev: number | undefined;
    current: number;
    next: number | undefined;
}

const regGroups = [
    ...['\\{', '\\}', '\\[', '\\]', '\\(', '\\)'],
    ...['then', 'else', 'elseif', 'elif', 'catch', 'finally', 'fi', 'done', 'end', 'loop', 'until', 'where', 'when'],
]
    .map(keyword => keyword + '\\b');

const keywordReg = new RegExp(`^(${regGroups.join('|')})`);

function startWithKeyword(line: string) {
    return keywordReg.test(line.trimStart().toLowerCase());
}

function getIndentationLength(line: string) {
    const regexResult = /^(\s*)([^]*)$/.exec(line);
    return regexResult && regexResult[2] && regexResult[2].length > 0
        ? regexResult[1].length
        : undefined;
}

export function contextIndentationFromText(source: string, offset: number, languageId: string): ContextIndentation {
    const prefixLines = source.slice(0, offset).split('\n');
    const suffixLines = source.slice(offset).split('\n');
    // eslint-disable-next-line complexity
    function findMatchingLine(prefixLines: string[], maxLineIndex: number, offsetPerStep: number) {
        // eslint-disable-next-line @typescript-eslint/init-declarations
        let indentationLength: number | undefined;
        // eslint-disable-next-line @typescript-eslint/init-declarations
        let matchIndex: number | undefined;
        let currentIndex = maxLineIndex;

        for (; undefined === indentationLength && currentIndex >= 0 && currentIndex < prefixLines.length;) {
            indentationLength = getIndentationLength(prefixLines[currentIndex]);
            matchIndex = currentIndex;
            currentIndex += offsetPerStep;
        }
        if (languageId !== 'python' || offsetPerStep !== -1) {
            return [indentationLength, matchIndex];
        }

        currentIndex++;
        const t = prefixLines[currentIndex].trim();
        if (t.endsWith('"""')) {
            if (!t.startsWith('"""') || t === '"""') {
                for (currentIndex--; currentIndex >= 0 && !prefixLines[currentIndex].trim().startsWith('"""');) {
                    currentIndex--;
                }
            }
            if (currentIndex >= 0) {
                indentationLength = undefined;
                currentIndex--;
                for (; undefined === indentationLength && currentIndex >= 0;) {
                    indentationLength = getIndentationLength(prefixLines[currentIndex]);
                    matchIndex = currentIndex;
                    currentIndex--;
                }
            }
        }
        return [indentationLength, matchIndex];
    }

    const [indentationLength, matchIndex] = findMatchingLine(prefixLines, prefixLines.length - 1, -1);
    const l = (() => {
        if (undefined !== indentationLength && undefined !== matchIndex) {
            for (let e = matchIndex - 1; e >= 0; e--) {
                const t = getIndentationLength(prefixLines[e]);
                if (undefined !== t && t < indentationLength) {
                    return t;
                }
            }
        }
        return undefined;
    })();
    const [u] = findMatchingLine(suffixLines, 1, 1);
    return {
        prev: l,
        current: indentationLength ?? 0,
        next: u,
    };
}

// eslint-disable-next-line complexity
export function completionCutOrContinue(text: string, indentation: any, nextIndentation: string) {
    const lines = text.split('\n');
    const hasNextIndentation = undefined !== nextIndentation;
    const lastLine = nextIndentation?.split('\n').pop();
    let lineIndex = 0;
    if (hasNextIndentation && lastLine?.trim() !== '' && lines[0].trim() !== '') {
        lineIndex++;
        if (!hasNextIndentation) {
            lineIndex++;
        }
        if (lines.length === lineIndex) {
            return 'continue';
        }
    }
    const maxIndentation = Math.max(indentation.current, indentation.next ?? 0);
    for (let i = lineIndex; i < lines.length; i++) {
        let line = lines[i];
        i === 0 && undefined !== lastLine && (line = lastLine + line);
        const lineIndentation = getIndentationLength(line);
        if (
            undefined !== lineIndentation
            && (lineIndentation < maxIndentation || lineIndentation === maxIndentation && !startWithKeyword(line))
        ) {
            return lines.slice(0, i).join('\n').length;
        }
    }
    return 'continue';
}

export function indentationBlockFinished(contextIndentation: ContextIndentation, t?: any) {
    return async (text: string) => {
        const cutOrContinue = completionCutOrContinue(text, contextIndentation, t);
        return cutOrContinue === 'continue' ? undefined : cutOrContinue;
    };
}
