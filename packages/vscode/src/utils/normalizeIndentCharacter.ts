import * as vscode from 'vscode';

type PaddingFn = (indent: string) => string;

function stripIndentation(text: string, paddingFunction: PaddingFn) {
    const resolvedLines = text.split('\n').map(line => {
        const index = line.length - line.trimStart().length;
        if (index <= 0) {
            return line;
        }
        const indent = line.slice(0, index);
        const strippedLine = line.slice(index);
        return paddingFunction(indent) + strippedLine;
    });
    return resolvedLines.join('\n');
}

const padWithTabs = (content: string) =>
    stripIndentation(
        content,
        indent => indent.replace(/ /g, '\t')
    );

const padWithSpaces = (content: string, tabSize: number) => {
    return stripIndentation(content, indent => indent.replace(/\t/g, ' '.repeat(tabSize)));
};

export function normalizeIndentCharacter(text: string, options: vscode.TextEditorOptions) {
    const tabSize = undefined === options.tabSize || typeof options.tabSize === 'string' ? 4 : options.tabSize;
    if (options.insertSpaces === false) {
        // 每一个空格替换一个\t
        return padWithTabs(text);
    }
    else if (options.insertSpaces === true) {
        return padWithSpaces(text, tabSize);
    }
    return text;
}
