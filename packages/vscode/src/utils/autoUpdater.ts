import {chmod, unlink} from 'node:fs/promises';
import * as vscode from 'vscode';
import axios from 'axios';
import {compareVersions} from 'compare-versions';
import {iocContainer} from '../iocContainer';
import {error, info} from '../common/outputChannel';
import {UserService} from '../services/UserService';
import {trackEvent} from './tracker';
import {download} from './download';
import {COMATE_HOME_FOLDER} from './fs';
import {getExtensionContextAsync} from './extensionContext';

const trackAutoUpgradeError = async (message: string) => {
    const userService = iocContainer.get(UserService);
    const [username] = await userService.getCurrentUser();
    await trackEvent(username, 'autoUpgrade', 'error', {
        result: message,
    });
};

interface IDEInfo {
    name: string;
    needRestart: boolean;
    minVersion: string;
    maxVersion: string;
    extensionVersion: string;
    bundleIds: string[];
}

interface ComateRelease {
    introduction: string;
    moreInfoUrl: string;
    icodingUrl: string;
    docUrl: string;
    installScriptUrl: string;
    checkExistScriptUrl: string;
    getToolboxIdeScriptUrl: string;
    ideInfos: IDEInfo[];
}

interface ComateReleasesResponse {
    status: string;
    message: string;
    data: ComateRelease;
}

const apiGetComateReleases = async () => {
    const res = await axios.get<ComateReleasesResponse>(
        'https://comate.now.baidu-int.com/api/duguanjia/config',
        {
            proxy: false,
        }
    );
    if (res.data.status === 'OK') {
        return res.data.data;
    }
    throw new Error(res.data.message);
};

const downloadLatestComate = async (version: string) => {
    try {
        const vsixFilePath = await download(
            `https://baidu-ide.bj.bcebos.com/comate/vscode-plugin/comate-${version}.vsix`,
            COMATE_HOME_FOLDER
        );
        await chmod(vsixFilePath, 0o777);
        return vsixFilePath;
    }
    catch (e) {
        error(`failed to download comate version ${version}: ${(e as Error).message}`);
        trackAutoUpgradeError(`failed to download comate version ${version}: ${(e as Error).message}`);
        return null;
    }
};

const doUpgrade = async (latestVersion: string) => {
    info('attempting to upgrade...');

    const newComateLocation = await downloadLatestComate(latestVersion);
    info(`downloaded new comate at ${newComateLocation}`);
    if (!newComateLocation) {
        return;
    }
    try {
        await vscode
            .commands
            .executeCommand(
                'workbench.extensions.installExtension',
                vscode.Uri.file(newComateLocation)
            )
            .then(() => {
                unlink(newComateLocation);
            });
    }
    catch (e) {
        const message = (e as Error).message;
        error(`failed to upgrade comate: ${message}`);
        trackAutoUpgradeError(`failed to upgrade comate. Error: ${message}`);
        throw new Error(`Failed to upgrade comate: ${message}`);
    }
};

export const autoUpdate = async () => {
    info('checking for updates...');
    const releases = await apiGetComateReleases();
    const latestVersion = releases.ideInfos.find(item => item.name === 'VSCODE')?.extensionVersion;
    if (!latestVersion) {
        trackAutoUpgradeError('failed to get the latest version of comate');
        error('failed to get the latest version of comate');
        return;
    }
    const context = await getExtensionContextAsync();

    const currentVersion: string = context.extension.packageJSON.version;
    info(`current version ${currentVersion}, latest version ${latestVersion}`);

    if (compareVersions(currentVersion, latestVersion) < 0) {
        await doUpgrade(latestVersion);
    }
};
