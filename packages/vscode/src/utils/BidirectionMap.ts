export class BidirectionMap<K, V> {
    private readonly map: Map<K, V> = new Map();
    private readonly reverseMap: Map<V, K> = new Map();

    set(key: K, value: V) {
        this.map.set(key, value);
        this.reverseMap.set(value, key);
    }

    get(key: K): V | undefined {
        return this.map.get(key);
    }

    getReverse(value: V): K | undefined {
        return this.reverseMap.get(value);
    }

    clear() {
        this.map.clear();
        this.reverseMap.clear();
    }
}
