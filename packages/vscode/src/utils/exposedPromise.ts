export interface ExposedPromise<T> {
    promise: Promise<T>;
    resolve: (value: T) => void;
    reject: (err: any) => void;
}

export function createExposedPromise<T>() {
    // eslint-disable-next-line @typescript-eslint/init-declarations
    let resolve: (value: T) => void;
    // eslint-disable-next-line @typescript-eslint/init-declarations
    let reject: (err: any) => void;
    const promise = new Promise<T>((r, j) => {
        resolve = r;
        reject = j;
    });
    return {promise, resolve: resolve!, reject: reject!};
}
