export type LanguageId =
    | 'cpp'
    | 'go'
    | 'java'
    | 'js'
    | 'php'
    | 'proto'
    | 'python'
    | 'ruby'
    | 'rust'
    | 'scala'
    | 'swift'
    | 'markdown'
    | 'latex'
    | 'html';

interface LanguageSpec {
    languageId: LanguageId;
    fileExts: string[];
}

const cppSpec: LanguageSpec = {
    languageId: 'cpp',
    fileExts: [
        '.c',
        '.cc',
        '.cpp',
        '.cxx',
        '.hh',
        '.h',
        '.hpp',
        '.ino', // Arduino
        '.m', // Objective-C
        '.pc', // Pro-C by Oracle RDBMS
        '.pcc', // Pro-C by Oracle RDBMS
    ],
};

const goSpec: LanguageSpec = {
    languageId: 'go',
    fileExts: ['.go'],
};

const javaSpec: LanguageSpec = {
    languageId: 'java',
    fileExts: ['.java'],
};

const jsSpec: LanguageSpec = {
    languageId: 'js',
    fileExts: ['.ts', '.tsx', '.js', '.jsx'],
};

const phpSpec: LanguageSpec = {
    languageId: 'php',
    fileExts: [
        '.php',
        '.phtml',
        '.php3',
        '.php4',
        '.php5',
        '.php6',
        '.php7',
        '.phps',
    ],
};

const protoSpec: LanguageSpec = {
    languageId: 'proto',
    fileExts: ['.proto'],
};

const pythonSpec: LanguageSpec = {
    languageId: 'python',
    fileExts: ['.py'],
};

const rubySpec: LanguageSpec = {
    languageId: 'ruby',
    fileExts: [
        '.rb',
        '.builder',
        '.eye',
        '.fcgi',
        '.gemspec',
        '.god',
        '.jbuilder',
        '.mspec',
        '.pluginspec',
        '.podspec',
        '.rabl',
        '.rake',
        '.rbuild',
        '.rbw',
        '.rbx',
        '.ru',
        '.ruby',
        '.spec',
        '.thor',
        '.watchr',
    ],
};

const rustSpec: LanguageSpec = {
    languageId: 'rust',
    fileExts: ['.rs', '.rs.in'],
};

const scalaSpec: LanguageSpec = {
    languageId: 'scala',
    fileExts: ['.sbt', '.sc', '.scala'],
};

const swiftSpec: LanguageSpec = {
    languageId: 'swift',
    fileExts: ['.swift'],
};

const markdownSpec: LanguageSpec = {
    languageId: 'markdown',
    fileExts: ['.md', '.markdown', '.mkd', '.mkdn', '.mkdown', '.ron'],
};

const latexSpec: LanguageSpec = {
    languageId: 'latex',
    fileExts: ['.tex'],
};

const htmlSpec: LanguageSpec = {
    languageId: 'html',
    fileExts: ['.html', '.htm', '.html.hl', '.inc', '.st', '.xht', '.xhtml'],
};

const languageSpecs: LanguageSpec[] = [
    cppSpec,
    goSpec,
    javaSpec,
    jsSpec,
    phpSpec,
    protoSpec,
    pythonSpec,
    rubySpec,
    rustSpec,
    scalaSpec,
    swiftSpec,
    markdownSpec,
    latexSpec,
    htmlSpec,
];

export function inferLanguage(filePath: string) {
    const spec = languageSpecs.find(item => item.fileExts.some(ext => filePath.endsWith(ext)));
    return spec && spec.languageId;
}
