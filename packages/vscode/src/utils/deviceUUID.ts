import {platform} from 'node:process';
import crypto from 'crypto';
import * as vscode from 'vscode';
import {machineId} from 'node-machine-id';
import {execCommand} from '@/utils/cp';
import {error} from '../common/outputChannel';
import {throttle} from './throttle';

async function executeAndFormatOutput(command: string, shell?: string) {
    try {
        const stdout = await execCommand(command, vscode.env.appRoot, {shell});
        const formatted = stdout
            .split(/\r?\n/)
            .map(item => item.trim())
            .filter(item => item.length > 0)
            .join('\n');
        return formatted;
    }
    catch (e) {
        error((e as Error).message);
        return '';
    }
}

async function createMacHardwareUUID() {
    const grepValueField = (text: string, property: string) => {
        const reg = new RegExp(`${property}:\\s+(.*)`);
        const match = text.match(reg);
        if (match) {
            return match[1];
        }
        return '';
    };

    const stdout = await executeAndFormatOutput('system_profiler  SPHardwareDataType');
    const values = [
        grepValueField(stdout, 'Serial Number \\(system\\)'),
        grepValueField(stdout, 'Model Name'),
        grepValueField(stdout, 'Model Identifier'),
        grepValueField(stdout, 'Model Number'),
        grepValueField(stdout, 'Total Number of Cores'),
        grepValueField(stdout, 'Memory'),
        grepValueField(stdout, 'Hardware UUID'),
    ];
    if (values.every(value => !value)) {
        return '';
    }
    return `mac-${values.join(':')}`;
}

async function createMacAddressUUID() {
    const values = await Promise.all([
        executeAndFormatOutput('ifconfig en0 | grep "ether"'),
        executeAndFormatOutput('ifconfig en1 | grep "ether"'),
    ]);
    if (values.every(value => !value)) {
        return '';
    }
    return `mac-${values.join(':')}`;
}

async function getMacDeviceUUID() {
    const uuid = await createMacHardwareUUID()
        || await createMacAddressUUID()
        || '';
    return uuid;
}

async function getWindowsDeviceUUID() {
    const getFromRegistry = async () => {
        try {
            const id = await machineId(true);
            return id ? `windows-${id}` : '';
        }
        catch (e) {
            error('Failed to get machine id on windows', (e as Error).message);
            return '';
        }
    };
    const getFromWmic = async () => {
        const rawOutput = await Promise.all([
            executeAndFormatOutput('wmic bios get serialnumber'),
            executeAndFormatOutput('wmic csproduct get UUID'),
            executeAndFormatOutput('wmic baseboard get serialnumber'),
        ]);
        const values = rawOutput.map(item => item.split('\n')[1]);
        if (values.every(value => !value)) {
            return '';
        }
        return `windows-${values.join(':')}`;
    };
    const getFromGetCimInstance = async () => {
        const rawOutput = await Promise.all([
            executeAndFormatOutput(
                'Get-CimInstance Win32_BIOS | Select-Object -Property SerialNumber',
                'powershell.exe'
            ),
            executeAndFormatOutput(
                'Get-CimInstance Win32_ComputerSystemProduct | Select-Object -Property UUID',
                'powershell.exe'
            ),
            executeAndFormatOutput(
                'Get-CimInstance Win32_BaseBoard | Select-Object -Property SerialNumber',
                'powershell.exe'
            ),
        ]);
        const values = rawOutput.map(item => item.split('\n')[2]);
        if (values.every(value => !value)) {
            return '';
        }
        return `windows-${values.join(':')}`;
    };
    const uuid = await getFromRegistry()
        || await getFromWmic()
        || await getFromGetCimInstance();
    return uuid;
}

async function getLinuxDeviceUUID() {
    const [machineId, machineId2, diskId, macAddress] = await Promise.all([
        executeAndFormatOutput('cat /etc/machine-id'),
        executeAndFormatOutput('cat /var/lib/dbus/machine-id'),
        executeAndFormatOutput('ls -l /dev/disk/by-uuid | awk \'{print $9}\''),
        executeAndFormatOutput('ip link show | awk \'/ether/ {print $2}\''),
    ]);
    const firstTry = [machineId, machineId2, diskId];
    if (firstTry.some(value => Boolean(value))) {
        return `linux-${firstTry.join(':')}`;
    }
    return macAddress ? `linux-${macAddress}` : '';
}

export async function getDeviceUUID() {
    let uuid = '';
    switch (platform) {
        case 'win32':
            uuid = await getWindowsDeviceUUID();
            break;
        case 'darwin':
            uuid = await getMacDeviceUUID();
            break;
        case 'linux':
            uuid = await getLinuxDeviceUUID();
            break;
        default:
            error(`cannot compute device uuid on ${platform}`);
            return '';
    }
    if (!uuid) {
        error(`got an empty device uuid on ${platform}`);
        return '';
    }
    const md5 = crypto
        .createHash('md5')
        .update(uuid)
        .digest('hex');
    return md5;
}

// 1小时内不重新计算设备的uuid
export const getDeviceUUIDThrottled = throttle(getDeviceUUID, 1 * 60 * 60 * 1000);
