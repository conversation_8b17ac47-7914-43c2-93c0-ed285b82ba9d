import * as vscode from 'vscode';
import {marked, Tokens} from 'marked';
import {Position, Range} from 'vscode-languageserver-protocol';
import {OneBasedPosition, OneBasedRange} from '@/api';

// 如果最后一行非空，则添加一个空行
export function addEolNewline(str: string) {
    const lines = str.split('\n');
    const lastLine = lines[lines.length - 1];
    if (lastLine.trim().length === 0) {
        return str;
    }
    return str + '\n';
}

export function splitEol(str: string) {
    return str.split(/\r?\n/);
}

export function findLeadingNonWhitespaceIndex(str: string | undefined) {
    if (!str) {
        return -1;
    }
    // Matches the first non-whitespace or non-tab character
    const regex = /[^\s\t]/;
    return str.search(regex);
}

export function addMarkdownCodeBlock(value: string, language?: string) {
    const fragments = [
        `\`\`\`${language ?? ''}`,
        value,
        '```',
    ];
    return fragments.join('\n');
}

function stripLeadingMarkdownBlock(text: string, languageId: string) {
    if (/^```[a-zA-Z]+\n/.test(text)) {
        return text.replace(/```[a-zA-Z]+\n/, '');
    }
    else if (text.startsWith(`\`\`\`${languageId.toLowerCase()}`)) {
        return text.replace(`\`\`\`${languageId.toLowerCase()}`, '');
    }
    return text.replace(/```\n?/, '');
}

function stripTrailingMarkdownBlock(text: string) {
    return text.replace(/\n?```$/, '');
}

export function stripMarkdownCodeBlock(text: string, languageId: string) {
    return stripTrailingMarkdownBlock(stripLeadingMarkdownBlock(text, languageId));
}

export function extractMarkdownCodeBlockContent(text: string): string[] {
    const pattern = /^```.*\n([\s\S]*?)?```/gm;
    const matches = text.matchAll(pattern);
    return Array.from(matches).map(match => match[1].trim());
}

export function extractMarkdownCodeBlocks(text: string): string[] {
    const pattern = /^```[\s\S]*?```/gm;
    const matches = text.matchAll(pattern);
    return Array.from(matches).map(match => match[0].trim());
}

export function replaceMiddleWithAsterisk(str: string) {
    if (str.length <= 2) {
        return str;
    }

    const firstChar = str.charAt(0);
    const lastChar = str.charAt(str.length - 1);
    let middleString = '';
    for (let i = 1; i < str.length - 1; i++) {
        middleString += '*';
    }
    return firstChar + middleString + lastChar;
}

// eslint-disable-next-line max-len, no-useless-escape
const accessAndSecretKeyRegex =
    '(?:access[-_. ]?key[-_. ]?id|access[-_. ]?key[-_. ]?secret|(?:aws|ibm|gcp|oracle|jd|alibaba|baidu|tencent|api|app|client)[-_. ]?secret|(?:api|app|access|secret|security|private)[-_. ]?key|secret[-_. ]?id|securityJsCode|(?:access|private|secret|api|security|classic|fine-grained)[-_. ]?token|ak|[-_. ]sk|password|passwd|pwd)[ \t\'"]*?(?:[:=]|=>)[ \t"\']*?(?:cli_|JDC_|ghp_|github_pat_\w{22}_)?([a-z0-9\/+=]{16,})(?:["\']?;?$)';

export function hideSensitiveContent(str: string) {
    const regex = new RegExp(accessAndSecretKeyRegex, 'gim');
    return str.replace(regex, (fullMatch, subString) => {
        return fullMatch.replace(subString, replaceMiddleWithAsterisk(subString));
    });
}

export function hideSensitive(params: Record<string, any>, fields: string[]) {
    for (const field of fields) {
        if (params[field] && typeof params[field] === 'string') {
            params[field] = hideSensitiveContent(params[field]);
        }
    }
    return params;
}

export function safeStringify(data: any) {
    try {
        return JSON.stringify(data);
    }
    catch (e) {
        return undefined;
    }
}

export function safeParseJson<T>(data: string) {
    try {
        return JSON.parse(data) as T;
    }
    catch (e) {
        return undefined;
    }
}

export function findMatch<T extends {children?: T[]}>(
    collection: T[],
    predicate: (element: T) => boolean
): T | undefined {
    for (const element of collection) {
        if (predicate(element)) {
            return element;
        }
        if (element.children) {
            const result = findMatch(element.children, predicate);
            if (result) {
                return result;
            }
        }
    }
    return undefined;
}

export const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));

export const isValidUri = (path: string) => {
    try {
        vscode.Uri.parse(path);
        return true;
    }
    catch (error) {
        return false;
    }
};

/**
 * 从给定的 markdown 字符串中提取第一个代码块。即使代码块没有闭合，也能够正确处理。
 *
 * @param {string} markdown - 要搜索代码块的 markdown 内容。
 * @returns {string | undefined} - 找到的第一个代码块的文本内容，如果没有找到代码块，则返回 undefined。
 */
export const getFirstCodeBlock = (markdown: string) => {
    const tokens = marked.lexer(markdown);
    const codeBlock = tokens.find((token): token is Tokens.Code => token.type === 'code');
    if (codeBlock) {
        return codeBlock.text;
    }
    return undefined;
};

export const countChineseCharacter = (text: string) => {
    return text.match(/\p{Script=Han}/ug)?.length || 0;
};

export const convertToOneBasedPosition = (position: Position): OneBasedPosition => {
    return {
        line: position.line + 1,
        character: position.character + 1,
    };
};

export const convertToOneBasedRange = (range: Range): OneBasedRange => {
    return {
        start: convertToOneBasedPosition(range.start),
        end: convertToOneBasedPosition(range.end),
    };
};

export const elideLongLine = (line: string, maxChars: number) => {
    if (line.length <= maxChars) {
        return line;
    }
    return line.slice(0, maxChars) + '...';
};

/**
 * 判断一个字符串是否是另一个字符串的子序列
 *
 * @param parent 主字符串
 * @param subsequence 子序列字符串
 * @returns 如果 subsequence 是 parent 的子序列，则返回 true，否则返回 false
 */
export const checkSubsequence = (parent: string, subsequence: string) => {
    let pointer = -1;
    for (const char of subsequence) {
        const idx = parent.indexOf(char, pointer + 1);
        if (idx <= pointer) {
            pointer = -1;
            break;
        }
        pointer = idx;
    }
    return pointer !== -1;
};
