import net from 'net';
import dns from 'dns';

export function checkConnectivity(host: string, port: number, timeout?: number) {
    return new Promise(resolve => {
        const socket = new net.Socket();
        const onError = () => {
            socket.destroy();
            resolve(false);
        };

        socket.setTimeout(timeout || 2000);
        socket.once('error', onError);
        socket.once('timeout', onError);
        socket.connect(port, host, () => {
            socket.end();
            resolve(true);
        });
    });
}

export function checkDNS(host: string) {
    return new Promise(resolve => {
        dns.lookup(host, err => {
            if (err) {
                resolve(false);
            }
            else {
                resolve(true);
            }
        });
    });
}
