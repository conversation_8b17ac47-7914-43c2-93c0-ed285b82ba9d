import {LanguageId} from './languages';

const SLASH_STYLE_LANGUAGES = new Set<LanguageId>([
    'cpp',
    'go',
    'java',
    'js',
    'php',
    'proto',
    'rust',
    'scala',
    'swift',
]);

const HASH_STYLE_LANGUAGES = new Set<LanguageId>([
    'python',
    'ruby',
]);

export function getLineCommentStyle(language: LanguageId) {
    if (SLASH_STYLE_LANGUAGES.has(language)) {
        return '//';
    }
    if (HASH_STYLE_LANGUAGES.has(language)) {
        return '#';
    }
    return undefined;
}

export function commentCode(code: string, language: LanguageId | undefined) {
    const lineCommentStyle = language ? getLineCommentStyle(language) : undefined;
    if (!lineCommentStyle) {
        return code;
    }
    return code.split('\n').map(line => `${lineCommentStyle} ${line}`).join('\n');
}
