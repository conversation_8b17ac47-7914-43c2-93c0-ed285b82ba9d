/* eslint-disable max-len */
import {resolve} from 'node:path';
import * as vscode from 'vscode';
import {flatten, minBy} from 'lodash';
import Parser, {SyntaxNode, TreeCursor} from 'web-tree-sitter';

export enum TreeSitterLanguage {
    Python = 'python',
    JavaScript = 'javascript',
    TypeScript = 'typescript',
    Tsx = 'tsx',
    Go = 'go',
    Ruby = 'ruby',
    Java = 'java',
    Cpp = 'cpp',
    Markdown = 'markdown',
    C = 'c',
    Vue = 'vue',
}

export const isJsFamiliy = (language: TreeSitterLanguage) => {
    return language === TreeSitterLanguage.JavaScript
        || language === TreeSitterLanguage.TypeScript
        || language === TreeSitterLanguage.Tsx;
};

const treeSitterLanguageIds = {
    python: TreeSitterLanguage.Python,
    javascript: TreeSitterLanguage.JavaScript,
    javascriptreact: TreeSitterLanguage.JavaScript,
    jsx: TreeSitterLanguage.JavaScript,
    typescript: TreeSitterLanguage.TypeScript,
    typescriptreact: TreeSitterLanguage.Tsx,
    go: TreeSitterLanguage.Go,
    ruby: TreeSitterLanguage.Ruby,
    java: TreeSitterLanguage.Java,
    cpp: TreeSitterLanguage.Cpp,
    markdown: TreeSitterLanguage.Markdown,
    c: TreeSitterLanguage.C,
    vue: TreeSitterLanguage.Vue,
};
// 捕获impor torch 或者 import torch as xx
const torchImportPatterns: Record<string, string[]> = {
    python: [
        '(module (import_statement name: (dotted_name (identifier) @package)))',
        '(module (import_statement name: (aliased_import name: (dotted_name (identifier) @package) alias: (identifier))))',
    ],
    javascript: [],
    typescript: [],
    tsx: [],
    go: [],
    ruby: [],
};
export const functionStatementPatterns: Record<string, string[]> = {
    python: [
        '(function_definition body: (block\n             (expression_statement (string))* @docstring) @body) @function',
        '(ERROR ("def" (identifier) (parameters))) @function',
    ],
    javascript: [
        '[\n            (function_declaration body: (statement_block) @body)\n            (generator_function_declaration body: (statement_block) @body)\n            (method_definition body: (statement_block) @body)\n            (lexical_declaration (variable_declarator name: (identifier) value: (arrow_function)))\n            (variable_declaration (variable_declarator name: (identifier) value: (arrow_function)))\n] @function',
    ],
    typescript: [
        '[\n            (function_declaration body: (statement_block) @body)\n            (generator_function_declaration body: (statement_block) @body)\n            (method_definition body: (statement_block) @body)\n            (lexical_declaration (variable_declarator name: (identifier) value: (arrow_function)))\n            (variable_declaration (variable_declarator name: (identifier) value: (arrow_function)))\n    (public_field_definition name: (property_identifier) value: (arrow_function body: (statement_block) @body))\n] @function',
    ],
    tsx: [
        '[\n            (function_declaration body: (statement_block) @body)\n            (generator_function_declaration body: (statement_block) @body)\n            (method_definition body: (statement_block) @body)\n            (lexical_declaration (variable_declarator name: (identifier) value: (arrow_function)))\n            (variable_declaration (variable_declarator name: (identifier) value: (arrow_function)))\n    (public_field_definition name: (property_identifier) value: (arrow_function body: (statement_block) @body))\n] @function',
    ],
    vue: [
        '[\n            (function_declaration body: (statement_block) @body)\n            (generator_function_declaration body: (statement_block) @body)\n            (method_definition body: (statement_block) @body)\n            (lexical_declaration (variable_declarator name: (identifier) value: (arrow_function)))\n            (variable_declaration (variable_declarator name: (identifier) value: (arrow_function)))\n] @function',
    ],
    go: [
        '[\n            (function_declaration body: (block) @body)\n            (method_declaration body: (block) @body)\n          ] @function',
    ],
    java: [
        '[\n            (method_declaration body: (block) @body)\n          ] @function',
    ],
    ruby: [
        '[\n            (method name: (_) parameters: (method_parameters)? @params [(_)+ "end"] @body)\n            (singleton_method name: (_) parameters: (method_parameters)? @params [(_)+ "end"] @body)\n          ] @function',
    ],
    cpp: [
        '[\n            (function_definition body: (compound_statement) @body)\n          ] @function',
    ],
    c: [
        '[\n            (function_definition body: (compound_statement) @body)\n          ] @function',
    ],
};

const exportStatementPatterns: Record<string, string[]> = {
    python: [],
    javascript: [
        '(program (export_statement) @export)',
    ],
    typescript: [
        '(program (export_statement) @export)',
    ],
    tsx: [
        '(program (export_statement) @export)',
    ],
    go: [],
    ruby: [],
};

const typeDefineDeclarationPatterns: Record<string, string[]> = {
    python: [],
    javascript: [],
    typescript: [
        '(program (interface_declaration) @type)',
        '(program (type_alias_declaration) @type)',
    ],
    tsx: [
        '(program (interface_declaration) @type)',
        '(program (type_alias_declaration) @type)',
    ],
    go: [],
    ruby: [],
};

export const topNodePredicateForLanguage: Record<string, (node: Parser.SyntaxNode) => boolean> = {
    python: (node: Parser.SyntaxNode) => {
        return node.type === 'module' || node.type === 'block' && node.parent?.type === 'class_definition';
    },
    javascript: (node: Parser.SyntaxNode) => {
        return node.type === 'program' || node.type === 'class_body';
    },
    typescript: (node: Parser.SyntaxNode) => {
        return node.type === 'program' || node.type === 'class_body';
    },
    tsx: (node: Parser.SyntaxNode) => {
        return node.type === 'program' || node.type === 'class_body';
    },
    go: (node: Parser.SyntaxNode) => {
        return node.type === 'source_file';
    },
    ruby: (node: Parser.SyntaxNode) => {
        return node.type === 'program' || node.type === 'class';
    },
};

export function checkTreeSitterSupport(languageId: string) {
    if (!(languageId in treeSitterLanguageIds)) {
        return undefined;
    }
    return treeSitterLanguageIds[languageId as keyof typeof treeSitterLanguageIds];
}

export function isCppInnerFunctionDefinition(node: Parser.SyntaxNode) {
    if (node.parent && node.parent.type === 'compound_statement') {
        const ancestor = node.parent.parent;
        return ancestor?.type === 'function_definition';
    }

    return false;
}

function getFunctionDeclaratorFromFunctionDefinition(node: Parser.SyntaxNode) {
    let current = node.childForFieldName('declarator');
    while (current && current.type === 'pointer_declarator') {
        const child = current.childForFieldName('declarator');
        // 确保不会一直拿到自己然后死循环
        if (child?.equals(current)) {
            return undefined;
        }
        current = child;
    }
    return current;
}

export function getNameFromCppFunctionDefinition(node: Parser.SyntaxNode) {
    const declaratorNode = getFunctionDeclaratorFromFunctionDefinition(node);

    if (!declaratorNode || declaratorNode.type !== 'function_declarator') {
        return undefined;
    }
    const identifierNode = declaratorNode.childForFieldName('declarator');
    return identifierNode?.text;
}

// eslint-disable-next-line complexity
export function extnameToWasmLanguage(extname?: string) {
    switch (extname) {
        case '.java':
            return TreeSitterLanguage.Java;
        case '.go':
            return TreeSitterLanguage.Go;
        case '.cpp':
        case '.hpp':
        case '.cc':
        case '.c++':
        case '.h':
            return TreeSitterLanguage.Cpp;
        case '.js':
        case '.jsx':
            return TreeSitterLanguage.JavaScript;
        case '.ts':
            return TreeSitterLanguage.TypeScript;
        case '.tsx':
            return TreeSitterLanguage.Tsx;
        default:
            return undefined;
    }
}

export function getBlockCloseToken(vscodeLanguageId: string) {
    switch (checkTreeSitterSupport(vscodeLanguageId)) {
        case TreeSitterLanguage.Python:
            return null;
        case TreeSitterLanguage.JavaScript:
        case TreeSitterLanguage.TypeScript:
        case TreeSitterLanguage.Tsx:
        case TreeSitterLanguage.Java:
        case TreeSitterLanguage.Go:
            return '}';
        case TreeSitterLanguage.Ruby:
            return 'end';
        default:
            return undefined;
    }
}

export function queryPatternMatches(treeNode: Parser.SyntaxNode, patterns: string[] = []) {
    const result = [];
    for (const queryText of patterns) {
        const language = treeNode.tree.getLanguage();
        const query = language.query(queryText);
        result.push(...query.matches(treeNode));
    }
    return result;
}

export function extractFunctionNodes(languageId: TreeSitterLanguage, node: Parser.SyntaxNode) {
    const functionMatches = queryPatternMatches(
        node,
        functionStatementPatterns[languageId] ?? []
    );
    const res: Parser.SyntaxNode[] = [];
    functionMatches.forEach(match => {
        const functionCapture = match.captures.find(item => item.name === 'function');
        if (functionCapture?.node) {
            res.push(functionCapture.node);
        }
    });
    return res;
}

export function extractTypeDefinitionNodes(languageId: TreeSitterLanguage, node: Parser.SyntaxNode) {
    const typeDefineMatches = queryPatternMatches(
        node,
        typeDefineDeclarationPatterns[languageId] ?? []
    );
    const res: Parser.SyntaxNode[] = [];
    typeDefineMatches.forEach(match => {
        const typeDefineCapture = match.captures.find(item => item.name === 'type');
        if (typeDefineCapture?.node) {
            res.push(typeDefineCapture.node);
        }
    });
    return res;
}

export function extractTorchImportNodes(
    languageId: TreeSitterLanguage,
    node: Parser.SyntaxNode,
    document: vscode.TextDocument
) {
    const functionMatches = queryPatternMatches(
        node,
        torchImportPatterns[languageId] ?? []
    );
    const res: Parser.SyntaxNode[] = [];
    functionMatches.forEach(match => {
        const functionCapture = match.captures.find(item => item.name === 'package');
        if (functionCapture?.node) {
            const {startPosition, endPosition} = functionCapture.node;
            if (
                document
                    .getText(
                        new vscode.Range(
                            new vscode.Position(startPosition.row, startPosition.column),
                            new vscode.Position(endPosition.row, endPosition.column)
                        )
                    )
                    .includes('torch')
            ) {
                res.push(functionCapture.node);
            }
        }
    });
    return res;
}

const parserLanguageItemCache = new Map();
export async function getParserLanguageItem(languageId: TreeSitterLanguage): Promise<Parser.Language> {
    if (!parserLanguageItemCache.has(languageId)) {
        const wasmFile = resolve(__dirname, '..', 'static', 'tree-sitter.wasm');
        await Parser.init({
            locateFile() {
                return wasmFile;
            },
        });
        const wasmPath = resolve(__dirname, '..', 'static', `tree-sitter-${languageId}.wasm`);
        const language = await Parser.Language.load(wasmPath);
        parserLanguageItemCache.set(languageId, language);
    }
    return parserLanguageItemCache.get(languageId);
}

export async function getTreeSitterParser(languageId: TreeSitterLanguage) {
    const language = await getParserLanguageItem(languageId);
    const parser = new Parser();
    parser.setLanguage(language);
    return parser;
}

export async function parseTreeSitter(languageId: TreeSitterLanguage, content: string) {
    const parser = await getTreeSitterParser(languageId);
    const tree = parser.parse(content);
    parser.delete();
    return tree;
}

export function findPreviousSiblings(node: Parser.SyntaxNode, predicate: (node: Parser.SyntaxNode) => boolean) {
    let previous = node.previousNamedSibling;
    while (previous) {
        if (predicate(previous)) {
            return previous;
        }
        previous = previous.previousNamedSibling;
    }
    return undefined;
}

export function getClosestNodeTo(position: number, nodes: Parser.SyntaxNode[]) {
    return minBy(nodes, item => {
        return Math.min(Math.abs(item.startIndex - position), Math.abs(item.endIndex - position));
    });
}

export function findMatchedNodes(
    node: Parser.SyntaxNode,
    predicate: (node: Parser.SyntaxNode) => boolean
): Parser.SyntaxNode[] {
    const rest = flatten(node.children.map(item => findMatchedNodes(item, predicate)));
    if (predicate(node)) {
        return [node, ...rest];
    }
    return rest;
}

export function findMatchedParent(current: Parser.SyntaxNode, predicate: (node: Parser.SyntaxNode) => boolean) {
    let parent = current.parent;
    while (parent) {
        if (predicate(parent)) {
            return parent;
        }
        parent = parent.parent;
    }
    return null;
}

export function getImportStatements(rootNode: Parser.SyntaxNode) {
    const res: Parser.SyntaxNode[] = [];
    for (const node of rootNode.namedChildren) {
        node.type === 'import_statement' && res.push(node);
    }
    return res;
}

export function isJSFunctionDeclaration(node: Parser.SyntaxNode): boolean {
    if (node.type === 'lexical_declaration' && node.firstNamedChild?.type === 'variable_declarator') {
        const firstNamedChild = node.firstNamedChild;
        const nameNode = firstNamedChild?.childForFieldName('name');
        const valueNode = firstNamedChild?.childForFieldName('value');
        return !!(nameNode && nameNode.type === 'identifier' && valueNode && valueNode.type === 'arrow_function');
    }

    return (
        node.type === 'function_declaration'
        || node.type === 'generator_function_declaration'
        || node.type === 'method_definition'
    );
}

export function isJSNodeInTopLevel(node: Parser.SyntaxNode): boolean {
    const parent = node.parent;
    if (node.type === 'method_definition') {
        if (
            node.firstChild
            && node.firstChild.type === 'accessibility_modifier'
            && node.firstChild.text === 'private'
        ) {
            return false;
        }
        const classDeclaration = node?.parent?.parent;
        return classDeclaration ? isJSNodeInTopLevel(classDeclaration) : false;
    }
    if (parent && (parent.type === 'program' || parent.type === 'export_statement')) {
        return true;
    }
    return false;
}

export async function getFunctionPositions(languageId: TreeSitterLanguage, text: string) {
    const parser = await getTreeSitterParser(languageId);
    const program = parser.parse(text);
    parser.delete();
    const nodes = extractFunctionNodes(languageId, program.rootNode).map(node => {
        return {
            startIndex: node.startIndex,
            endIndex: node.endIndex,
        };
    });
    return nodes;
}

export function queryExports(languageId: TreeSitterLanguage, node: Parser.SyntaxNode) {
    return queryPatternMatches(
        node,
        exportStatementPatterns[languageId]
    );
}

export function getFirstPrecedingComment(node: Parser.SyntaxNode) {
    let current = node;
    for (; current.previousSibling?.type === 'comment';) {
        const previousSibling = current.previousSibling;
        if (previousSibling.endPosition.row < current.startPosition.row - 1) {
            break;
        }
        current = previousSibling;
    }
    return current?.type === 'comment' ? current : null;
}

// eslint-disable-next-line complexity
export function isFunctionCall(languageId: TreeSitterLanguage, node: Parser.SyntaxNode) {
    if (
        languageId === TreeSitterLanguage.JavaScript
        || languageId === TreeSitterLanguage.TypeScript
        || languageId === TreeSitterLanguage.Tsx
        || languageId === TreeSitterLanguage.Go
    ) {
        return node.type === 'identifier'
            && node.parent
            && node.parent.type === 'call_expression'
            && node.parent.childForFieldName('function')?.equals(node);
    }
    else if (languageId === TreeSitterLanguage.Python) {
        return node.type === 'identifier'
            && node.parent
            && node.parent.type === 'call'
            && node.parent.childForFieldName('function')?.equals(node);
    }

    return false;
}

export function isBlockComment(node: SyntaxNode, line: number) {
    const cursor = node.tree.walk();

    function checkNode(cursor: TreeCursor, line: number): boolean {
        const node: SyntaxNode = cursor.currentNode();
        const commentLine = line - 1;
        // 有些语言 treesitter 可以直接解析出块级注释（函数注释），如果不行，则按多行注释来筛选
        const isBlockCommentType = node.type === 'block_comment'
            || (node.type === 'comment' && node.endPosition.row - node.startPosition.row > 0);
        if (isBlockCommentType && node.endPosition.row === commentLine) {
            return true;
        }

        if (cursor.gotoFirstChild()) {
            do {
                if (checkNode(cursor, line)) {
                    return true;
                }
            }
            while (cursor.gotoNextSibling());
            cursor.gotoParent();
        }
        return false;
    }

    return checkNode(cursor, line);
}

export function isLineCommentAndCursorAtEndOfCommentOrNextNewLine(
    node: SyntaxNode,
    cursorLine: number,
    cursorColumn: number,
    document: vscode.TextDocument
) {
    const cursor = node.tree.walk();

    // eslint-disable-next-line complexity
    function checkNode(cursor: TreeCursor, cursorLine: number, cursorColumn: number): boolean {
        const node: SyntaxNode = cursor.currentNode();
        const isCommentType = node.type === 'comment' || node.type === 'line_comment';
        if (isCommentType) {
            if (node.text.length < 5) {
                return false;
            }
            if (node.endPosition.row === cursorLine && node.endPosition.column === cursorColumn) {
                return true;
            }
            else if (node.endPosition.row === cursorLine - 1 && document.lineAt(cursorLine).isEmptyOrWhitespace) {
                return true;
            }
        }

        if (cursor.gotoFirstChild()) {
            do {
                if (checkNode(cursor, cursorLine, cursorColumn)) {
                    return true;
                }
            }
            while (cursor.gotoNextSibling());
            cursor.gotoParent();
        }
        return false;
    }

    return checkNode(cursor, cursorLine, cursorColumn);
}

export function getAncestor(wasmLanguageId: TreeSitterLanguage, node: Parser.SyntaxNode) {
    const isTopNode = topNodePredicateForLanguage[wasmLanguageId ?? ''];
    if (!isTopNode) {
        return null;
    }
    let current = node;
    for (; current.parent;) {
        if (isTopNode(current.parent)) {
            return current;
        }
        current = current.parent;
    }
    return current.parent ? current : null;
}

export function isTopLevelNode(wasmLanguageId: TreeSitterLanguage, node?: Parser.SyntaxNode | null) {
    const isTopNode = topNodePredicateForLanguage[wasmLanguageId ?? ''];
    if (!node || !isTopNode) {
        return false;
    }
    return isTopNode(node);
}

/**
 * 获取 Vue 文件内容中 js 代码的范围，用于parser解析
 *
 * @param content Vue 文件的内容
 * @returns range
 */
export function getVueScriptRange(content: string): Parser.Range[] {
    // 简单粗暴找第一个 script 标签中的内容，先不考虑其他更复杂情况
    // 后面不满足需求时再看是要上正则，还是要用 html 解析得到script标签
    const lines = content.split('\n');
    const scriptStartRegex = /^<script[^>]*>\s*$/;
    const scriptStartLine = lines.findIndex(line => scriptStartRegex.test(line));
    if (scriptStartLine === -1) {
        return [];
    }
    const scriptEndRegex = /^<\/script>\s*$/;
    const scriptEndLine = lines.findIndex(line => scriptEndRegex.test(line));
    if (scriptEndLine === -1 || scriptEndLine < scriptStartLine) {
        return [];
    }
    const range = {
        startPosition: {row: scriptStartLine + 1, column: 0},
        endPosition: {row: scriptEndLine, column: 0},
        startIndex: lines.slice(0, scriptStartLine + 1).join('\n').length + 1,
        endIndex: lines.slice(0, scriptEndLine).join('\n').length + 1,
    };
    return [range];
}

export function cursorAtEmptyLine(node: SyntaxNode, cursorLine: number, document: vscode.TextDocument): boolean {
    const cursor = node.tree.walk();
    function checkNode(cursor: TreeCursor, cursorLine: number): boolean {
        if (document.lineAt(cursorLine).isEmptyOrWhitespace) {
            return true;
        }

        if (cursor.gotoFirstChild()) {
            do {
                if (checkNode(cursor, cursorLine)) {
                    return true;
                }
            }
            while (cursor.gotoNextSibling());
            cursor.gotoParent();
        }
        return false;
    }

    return checkNode(cursor, cursorLine);
}

/**
 * 判断当前节点是否有匹配的祖先节点
 *
 * @param current 当前节点
 * @param ancestorPredicate 判断祖先节点的回调函数，如果回调函数返回 true，则视为找到匹配的祖先节点
 * @returns 若有匹配的祖先节点，则返回 true；否则返回 false
 */
export function hasMatchingAncestor(
    current: Parser.SyntaxNode,
    ancestorPredicate: (ancestor: Parser.SyntaxNode) => boolean
) {
    let parent = current.parent;
    while (parent) {
        if (ancestorPredicate(parent)) {
            return true;
        }
        parent = parent.parent;
    }
    return false;
}
