/**
 * @file 日志
 */

import * as vscode from 'vscode';
import axios from 'axios';
import {getExtensionContextAsync} from './extensionContext';

const hostUrl: string = 'https://comate.baidu-int.com/logger/comate.log';

function sendLogBase(type: string, payload: any) {
    axios.post(
        hostUrl,
        {...payload, type, platform: $features.PLATFORM},
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
}

async function getCommonPayload(username: string) {
    const context = await getExtensionContextAsync();
    const app = 'comate';
    const version = context.extension.packageJSON.version;
    const session = vscode.env.machineId;
    return {
        app,
        username,
        version,
        session,
    };
}

export type ComateAction =
    | 'shown'
    | 'accepted'
    | 'rejected'
    | 'error'
    | 'success'
    | 'enable'
    | 'ignore'
    | 'duration'
    | 'generate'
    | 'view'
    | 'info'
    | 'fail';

export type Category =
    | 'autoUpgrade'
    | 'enablePopup'
    | 'snippet'
    | 'quickFix'
    | 'autoFocus'
    | 'completion';

/**
 * @deprecated 废弃，使用 logProvider的 logUserAction
 */
export async function trackEvent(username: string, category: Category, action: ComateAction, operation?: {
    [key: string]: any;
    result?: string;
}) {
    if ($features.PLATFORM !== 'internal') {
        return;
    }
    const commonPayload = await getCommonPayload(username);
    sendLogBase('event', {
        common: commonPayload,
        event: {
            category,
            action,
            operation,
        },
    });
}
