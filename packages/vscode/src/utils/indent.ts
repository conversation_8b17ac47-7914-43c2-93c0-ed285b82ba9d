function getValidIndents(lines: string[]) {
    // 对于没有实际内容的行，它不参与寻找公共最小空字符的计算
    const validLines = lines.filter(line => line.trim().length > 0);
    return validLines.map(line => line.length - line.trimStart().length);
}

function stripLinesIndent(lines: string[], indentLength: number) {
    return lines.map(line => line.slice(Math.min(line.length - line.trimStart().length, indentLength)));
}

export function stripMinIndent(lines: string[]) {
    const minIndent = Math.min(...getValidIndents(lines));
    return stripLinesIndent(lines, minIndent).join('\n');
}

export function stripExtraIndent(content: string, replaceFirstLine: boolean, completeFirstLine?: string) {
    if (!completeFirstLine) {
        return content;
    }
    if (replaceFirstLine && completeFirstLine) {
        return stripMinIndent([completeFirstLine, ...content.split('\n').slice(1)]);
    }
    const lines = content.split('\n');
    const [contentFirstLine, ...restLines] = lines;

    const firstLineIndent = completeFirstLine.length - completeFirstLine.trimStart().length;
    const isContentFirstLineEmpty = contentFirstLine.trim().length === 0;
    const isContentFirstLinePartial = completeFirstLine.length - contentFirstLine.length > firstLineIndent;
    const ignoreFirstLineIndent = isContentFirstLineEmpty || isContentFirstLinePartial;

    const indents = [
        // 当第一行没有实际内容或未包含全部非空内容时，第一行不参与寻找公共最小空字符的计算，因此直接返回最大可能的值
        ignoreFirstLineIndent ? Number.MAX_SAFE_INTEGER : firstLineIndent,
        ...getValidIndents(restLines),
    ];
    const minIndent = Math.min(...indents);

    const firstLinePrefix = completeFirstLine
        .replace(/\S/g, ' ')
        .slice(0, completeFirstLine.length - contentFirstLine.length);
    const extendedContentFirstLine = `${firstLinePrefix}${contentFirstLine}`;
    return stripLinesIndent([extendedContentFirstLine, ...restLines], minIndent).join('\n');
}

export function isLastLineEmpty(text: string) {
    const lastLine = text.split('\n').pop();
    return lastLine !== undefined && lastLine.trim().length === 0;
}
