interface PendingPromise {
    timer: NodeJS.Timeout;
    reject: () => void;
}

export class Debouncer {
    private pendingPromise: PendingPromise | undefined;

    async debounce(delay: number) {
        if (this.pendingPromise) {
            clearTimeout(this.pendingPromise.timer);
            this.pendingPromise.reject();
            this.pendingPromise = undefined;
        }
        return new Promise<void>((resolve, reject) => {
            this.pendingPromise = {
                timer: setTimeout(
                    () => resolve(),
                    delay
                ),
                reject,
            };
        });
    }
}

type AnyFunc<P, T> = (...args: P[]) => T;

export function debounce<A extends AnyFunc<any, any>>(delay: number, fn: A) {
    let timeoutId: NodeJS.Timeout | null = null;
    return (...args: Parameters<A>): Promise<ReturnType<A>> => {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        return new Promise(resolve => {
            timeoutId = setTimeout(
                () => {
                    const result = fn(...args);
                    resolve(result);
                },
                delay
            );
        });
    };
}
