import {Position, Range} from 'vscode-languageserver-types';

export function isPositionBefore(a: Position, b: Position): boolean {
    return a.line < b.line || (a.line === b.line && a.character <= b.character);
}

export function isRangeEqual(a: Range, b: Range): boolean {
    return a.start.line === b.start.line && a.end.line === b.end.line
        && a.start.character === b.start.character && a.end.character === b.end.character;
}

export function isInRange(position: Position, range: Range): boolean {
    return isPositionBefore(range.start, position) && isPositionBefore(position, range.end);
}

export function isWithinRange(containingRange: Range, range: Range): boolean {
    return isPositionBefore(containingRange.start, range.start) && isPositionBefore(range.end, containingRange.end);
}
