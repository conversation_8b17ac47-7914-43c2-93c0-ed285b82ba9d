import * as cp from 'child_process';

interface ExecCommandOptions {
    timeout?: number;
    shell?: string;
    env?: NodeJS.ProcessEnv;
}

/**
 * cp.exec 的 Promise 封装
 */
export function execCommand(command: string, cwd: string, options?: ExecCommandOptions): Promise<string> {
    return new Promise((resolve, reject) => {
        cp.exec(command, {
            cwd,
            encoding: 'utf8',
            ...options,
        }, (err, stdout) => {
            if (err) {
                reject(err);
            }
            else {
                resolve(stdout.trim());
            }
        });
    });
}
