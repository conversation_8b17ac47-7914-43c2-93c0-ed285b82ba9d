interface Options {
    /** 回调，注意参数是字符串 */
    onUpdate: (current: string) => void;
    /** 小数，比如 0-100过程中，展示99.9 */
    decimal?: number;
    /** 一次update的耗时, 值越大越慢 */
    frameElapsedTime?: number;
}

export class CountUp {
    private readonly opts: Options;
    constructor(opts: Options) {
        this.opts = opts;
    }

    get frameElapsedTime() {
        return this.opts.frameElapsedTime ?? 200;
    }

    get decimal() {
        return this.opts.decimal || 0;
    }

    private current = 0;
    private intervalRef: NodeJS.Timer | null = null;

    private updateAndNotify(current: number) {
        this.opts.onUpdate(current.toFixed(this.decimal));
        this.current = current;
    }

    update(to: number) {
        if (this.current === to) {
            return;
        }

        clearInterval(this.intervalRef!);
        const step = (to - this.current) / 100;
        // 跑完从当前值-目标值，需要多少个step，拿`this.frameElapsedTime`总耗时除以step获取间隔
        const interval = Math.floor(this.frameElapsedTime / ((to - this.current) / step));
        this.intervalRef = setInterval(
            () => {
                const next = Math.min(to, this.current + step);
                if (next === to) {
                    clearInterval(this.intervalRef!);
                }
                this.updateAndNotify(next);
            },
            interval
        );
    }

    reset(value: number) {
        clearInterval(this.intervalRef!);
        this.current = value;
    }
}
