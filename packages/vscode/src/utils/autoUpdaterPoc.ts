import {chmod, unlink} from 'node:fs/promises';
import * as vscode from 'vscode';
import platform from '@shared/platform';
import {getLatestRelease} from '@/api';
import {error, info} from '../common/outputChannel';
import {download} from './download';
import {COMATE_HOME_FOLDER} from './fs';
import {getExtensionContextAsync} from './extensionContext';


const downloadLatestComate = async (url: string) => {
    try {
        const vsixFilePath = await download(
            url,
            COMATE_HOME_FOLDER
        );
        await chmod(vsixFilePath, 0o777);
        return vsixFilePath;
    }
    catch (e) {
        error(`failed to download comate from ${url}: ${(e as Error).message}`);
        return null;
    }
};

const doUpgrade = async (url: string) => {
    info('attempting to upgrade...');

    const newComateLocation = await downloadLatestComate(url);
    info(`downloaded new comate at ${newComateLocation}`);
    if (!newComateLocation) {
        return;
    }
    try {
        await vscode.commands.executeCommand(
            'workbench.extensions.installExtension',
            vscode.Uri.file(newComateLocation)
        ).then(() => {
            unlink(newComateLocation);
        });
        vscode.window.showInformationMessage(`[${platform.resolve('brand')}]插件安装成功，重新启动插件即可使用最新版本`);
    }
    catch (e) {
        const message = (e as Error).message;
        error(`failed to upgrade comate: ${message}`);
        throw new Error(`Failed to upgrade comate: ${message}`);
    }
};

export const autoUpdatePoc = async () => {
    info('checking for updates...');
    try {
        const release = await getLatestRelease();
        const latestVersion = release.pluginVersion;
        if (!latestVersion) {
            error('failed to get the latest version of comate');
            return;
        }
        const context = await getExtensionContextAsync();

        const currentVersion: string = context.extension.packageJSON.version;
        info(`current version ${currentVersion}, latest version ${latestVersion}`);

        if (currentVersion !== latestVersion) {
            const selection = await vscode.window.showInformationMessage(
                `[${platform.resolve('brand')}]检测到插件有最新版本(${latestVersion})，建议更新到新版本使用`,
                '安装最新版本',
                '不再提醒'
            );
            if (selection === '安装最新版本') {
                await doUpgrade(release.url);
            }
        }
    }
    catch (e) {
        if (e instanceof Error) {
            error(`failed during autoUpdate: ${e.message}`);
        }
    }
};
