export async function* asyncIterableMap<T, R>(
    sourceAsyncIterable: AsyncIterable<T>,
    mapFn: (item: T) => R
): AsyncGenerator<R> {
    for await (const item of sourceAsyncIterable) {
        yield mapFn(item);
    }
}

export async function* asyncIterableFilter<T>(
    sourceAsyncIterable: AsyncIterable<T>,
    filterFn: (item: T) => Promise<boolean>
): AsyncGenerator<T> {
    for await (const item of sourceAsyncIterable) {
        if (await filterFn(item)) {
            yield item;
        }
    }
}

export async function* asyncIterableMapFilter<U, R>(
    sourceAsyncIterable: AsyncIterable<U>,
    mapFilterFn: (item: U) => Promise<R | undefined>
): AsyncGenerator<R> {
    for await (const item of sourceAsyncIterable) {
        const mappedItem = await mapFilterFn(item);
        if (mappedItem !== undefined) {
            yield mappedItem;
        }
    }
}

export async function* asyncIterableFromArray<T>(array: T[]): AsyncGenerator<T> {
    for (const item of array) {
        yield item;
    }
}
