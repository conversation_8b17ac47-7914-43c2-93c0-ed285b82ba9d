/**
 * @file 将 promise 的 err 从 try catch 变成 [err, data] 数组
 * <AUTHOR> <<EMAIL>>
 */

export async function promiseCatchError<T>(promise: Promise<T>): Promise<[any] | [null, T]> {
    try {
        const res = await promise;
        const successResult: [null, T] = [null, res];
        return successResult;
    }
    catch (err) {
        return [err];
    }
}

// copy from vscode.d.ts
/**
 * Thenable is a common denominator between ES6 promises, Q, jquery.Deferred, WinJS.Promise,
 * and others. This API makes no assumption about what promise library is being used which
 * enables reusing existing code without migrating to a specific promise implementation. Still,
 * we recommend the use of native promises which are available in this editor.
 */
interface Thenable<T> {
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    // eslint-disable-next-line max-len
    then<TResult>(
        onfulfilled?: (value: T) => TResult | Thenable<TResult>,
        onrejected?: (reason: any) => TResult | Thenable<TResult>
    ): Thenable<TResult>;
    // eslint-disable-next-line max-len, @typescript-eslint/unified-signatures
    then<TResult>(
        onfulfilled?: (value: T) => TResult | Thenable<TResult>,
        onrejected?: (reason: any) => void
    ): Thenable<TResult>;
}

export async function thenableCatchError<T>(promise: Thenable<T>): Promise<[any] | [null, T]> {
    return new Promise(resolve => {
        promise.then(
            value => {
                resolve([null, value]);
            },
            error => {
                resolve([error]);
            }
        );
    });
}
