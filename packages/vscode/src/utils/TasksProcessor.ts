/* eslint-disable max-depth */
import {createExposedPromise, ExposedPromise} from './exposedPromise';

export class TasksProcessor<T> {
    latest: number = 0;
    sortedTasks: Array<ExposedPromise<T>> = [];
    errMsgs: string[] = [];

    constructor(private readonly tasks: Array<Promise<T>>) {
        this.tasks.forEach(() => this.sortedTasks.push(createExposedPromise()));
        this.tasks.forEach(task => this.resolve(task));
    }

    async *processSSE(): AsyncGenerator<Awaited<T>, void, void> {
        for (const task of this.sortedTasks) {
            try {
                yield await task.promise;
            }
            catch (e: any) {
                this.errMsgs.push(e.message ?? 'unknown error');
            }
        }
    }

    latestTask() {
        return this.sortedTasks[this.latest];
    }

    resolve(task: Promise<T>) {
        task
            .then(res => {
                this.latestTask().resolve(res);
                this.latest++;
            })
            .catch(err => {
                this.latestTask().reject(err);
                this.latest++;
            });
    }
}
