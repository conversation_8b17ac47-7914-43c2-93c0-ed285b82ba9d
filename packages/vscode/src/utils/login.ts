import * as os from 'os';
import * as vscode from 'vscode';
import {Container} from 'inversify';
import platform from '@shared/platform';
import {getLoginUrlPrefix} from '@shared/utils/features';
import {showLicenseLoginVisible} from '@shared/utils/features';
import {error, warning} from '@/common/outputChannel';
import {L10n} from '@/common/L10nProvider/L10n';
import {ExtensionText, GlobalText} from '@/common/L10nProvider/constants';
import {AGREEMENT_VERSION_CONFIG_KEY, BASE_WEB_HOST} from '@/constants';
import {getLicenseByToken, getTokenByClientId, removePreviousBinding} from '../api';
import {VSCodeConfigProvider, getBasicConfigSectionName} from '../services/ConfigProvider';
import {getDeviceUUIDThrottled} from './deviceUUID';
import {reportEvent} from './trackerSaas';
import {getExtensionContextAsync} from './extensionContext';
import {isSaaS} from './features';

export const CURRENT_LOGIN_ID = 'comate_login_ID';
export const CURRENT_LOGIN_USER = 'comate_login_user';
export const CURRENT_LOGIN_AVATAR = 'comate_login_avatar';

export const INTERNAL_DOCUMENT_URL =
    'https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/rAvpoLh-pjNSaY';

async function showInternalDocument() {
    const installText = L10n.t(ExtensionText.LOGIN_INSTALL_INTERNAL);
    const choice = await vscode.window.showInformationMessage(
        L10n.t(ExtensionText.LOGIN_INTERNAL_USER),
        installText,
        L10n.t(GlobalText.DO_NOT_SHOW_AGAIN)
    );
    if (choice === installText) {
        await vscode.env.openExternal(vscode.Uri.parse(INTERNAL_DOCUMENT_URL));
    }
}

interface LoginContext {
    clientId: string;
    key: string;
    displayName: string;
    userAvatarUrl?: string;
}

async function onLoginSuccess(globalContext: Container, {clientId, displayName, key, userAvatarUrl}: LoginContext) {
    const context = await getExtensionContextAsync();
    context.secrets.store(CURRENT_LOGIN_ID, clientId);
    context.secrets.store(CURRENT_LOGIN_USER, displayName);
    context.secrets.store(CURRENT_LOGIN_AVATAR, userAvatarUrl ?? '');

    const configProvider = globalContext.get(VSCodeConfigProvider);
    await configProvider.updateKeyAndUserName(key, displayName);
    vscode.window.showInformationMessage(L10n.t(ExtensionText.LOGIN_SUCCESS));
    reportEvent('extension', 'login', 'success');
}

async function fetchLicense(globalContext: Container, clientId: string, token: string) {
    const res = await getLicenseByToken(clientId, token);
    if (res.status === 'OK') {
        if (!res.data || !res.data.key) {
            throw new Error(L10n.t(ExtensionText.LOGIN_KEY_ERROR));
        }
        await onLoginSuccess(
            globalContext,
            {
                clientId,
                displayName: res.data.displayName,
                key: res.data.key,
                userAvatarUrl: res.data.userAvatarUrl,
            }
        );
        if (res.data.isInternalUser === true) {
            showInternalDocument();
        }
    }
    else {
        throw new Error(res.message ?? L10n.t(ExtensionText.LOGIN_KEY_ERROR));
    }
}

function pollLoginStatus(
    globalContext: Container,
    clientId: string,
    progress: vscode.Progress<{message?: string}>,
    resolve: () => void,
    reject: (reason: any) => void,
    token: vscode.CancellationToken
) {
    setTimeout(
        async () => {
            if (token.isCancellationRequested) {
                return;
            }
            try {
                const res = await getTokenByClientId(clientId);
                if (res.status === 'OK') {
                    if (!res.data) {
                        throw new Error(L10n.t(ExtensionText.LOGIN_TOKEN_ERROR));
                    }
                    await fetchLicense(globalContext, clientId, res.data);
                    resolve();
                }
                else {
                    pollLoginStatus(globalContext, clientId, progress, resolve, reject, token);
                }
            }
            catch (e) {
                reject(e);
            }
        },
        2 * 1000
    );
}

async function removePreviousClientBinding(clientId: string) {
    try {
        const res = await removePreviousBinding(clientId);
        if (res.status === 'OK' && res.data === true) {
            return;
        }
        throw new Error(res.message ?? 'unknown error');
    }
    catch (e) {
        const message = e instanceof Error ? e.message : `${e}`;
        warning('failed to logout: ' + message);
    }
}

function getDisplayType() {
    const osType = os.type();
    switch (osType) {
        case 'Windows_NT':
            return 'Windows';
        case 'Darwin':
            return 'Mac OS';
        case 'Linux':
            return 'Linux';
        default:
            return osType;
    }
}

const truncateString = (str: string, n: number) => (str.length > n ? str.substring(0, n) + ' ...' : str);

function getDeviceDisplayName() {
    try {
        const userInfo = os.userInfo();
        const username = truncateString(userInfo.username ?? '', 100);
        const hostname = truncateString(os.hostname(), 100);
        return `${username} ${hostname} (${getDisplayType()} ${os.arch()} ${os.release()})`;
    }
    catch (e: any) {
        error('failed to get device info: ' + (e instanceof Error ? e.message : `${e}`));
        return '';
    }
}

const getLoginUrl = (lng: 'en' | 'zh', clientId: string) => {
    const deviceInfo = getDeviceDisplayName();
    const prefix = getLoginUrlPrefix();
    return `${BASE_WEB_HOST}/${lng}/${prefix}login?track=vscode&clientId=${clientId}&device=${deviceInfo}`;
};

export async function login(globalContext: Container) {
    const clientId = await getDeviceUUIDThrottled();
    if (!clientId) {
        vscode.window.showErrorMessage(L10n.t(ExtensionText.LOGIN_DEVICE_ERROR));
        return;
    }
    vscode.window.withProgress(
        {
            location: vscode.ProgressLocation.Notification,
            cancellable: true,
        },
        async (progress, token) => {
            progress.report({message: L10n.t(ExtensionText.LOGIN_LOGGING_IN)});
            try {
                await removePreviousClientBinding(clientId);
                const loginUrl = L10n.isEnglish ? getLoginUrl('en', clientId) : getLoginUrl('zh', clientId);
                await vscode.env.openExternal(vscode.Uri.parse(loginUrl));
                await new Promise<void>((resolve, reject) => {
                    pollLoginStatus(globalContext, clientId, progress, resolve, reject, token);
                });
            }
            catch (e: any) {
                reportEvent('extension', 'login', 'failure');
                vscode.window.showErrorMessage(
                    L10n.t(ExtensionText.LOGIN_FAILED, e.message ?? L10n.t(GlobalText.COMMON_UNKNOWN_ERROR))
                );
            }
        }
    );
}

export const loginByPassport = async (globalContext: Container) => {
    const loginText = L10n.t(ExtensionText.LOGIN_ACTION_TEXT);
    const showLicenseLogin = showLicenseLoginVisible();
    const loginWithLicense = L10n.t(ExtensionText.LOGIN_ACTION_WITH_LICENSE);
    const buttons = [loginText];
    if (showLicenseLogin) {
        buttons.push(loginWithLicense);
    }
    const choice = await vscode.window.showInformationMessage(
        L10n.t(ExtensionText.LOGIN_MESSAGE, platform.resolve('brand')),
        ...buttons
    );
    if (choice === loginText) {
        login(globalContext);
    }
    else if (choice === loginWithLicense) {
        const context = await getExtensionContextAsync();
        vscode.commands.executeCommand('workbench.action.openSettings', {
            query: `@ext:${context.extension.id} ${getBasicConfigSectionName()}`,
        });
    }
};

export const extensionLogin = async (globalContext: Container) => {
    // 预获取
    getDeviceUUIDThrottled();
    return loginByPassport(globalContext);
};

const latestAgreementVersion = 1;

export async function showUserAgreementMessage() {
    try {
        // 只有 SaaS 才有用户协议，现在提示协议是登录后判断 license 然后提示的
        // poc 因为也有 license 会走到这里的逻辑，所以这里需要判断一下
        if (!isSaaS) {
            return;
        }
        const context = await getExtensionContextAsync();
        const current = context.globalState.get<number>(AGREEMENT_VERSION_CONFIG_KEY) ?? 0;
        if (latestAgreementVersion > current) {
            context.globalState.update(AGREEMENT_VERSION_CONFIG_KEY, latestAgreementVersion);
            vscode.window.showInformationMessage(L10n.t(ExtensionText.LOGIN_AGREEMENT));
        }
    }
    catch (e: any) {
        error('Failed to show user agreement message: ' + e.message ?? 'unknown error');
    }
}
