import {it, describe, expect} from 'vitest';
import {matchFilePath, matchFilePathPositionToTerminalLinkRange} from '../terminal';

describe('terminal', () => {
    it('matchFilePath', () => {
        expect(matchFilePath('npm ERR! File not exist /path/to/file')).toEqual([[24, 37]]);
        expect(matchFilePath('./path/to/file this is a error')).toEqual([[0, 14]]);
        expect(
            // eslint-disable-next-line max-len
            matchFilePath(
                'gin_not_found_package.go:11:2: no required module provides package icode.baidu.com/baidu/bcloud/auto-debug-test/src/user; to add it'
            )
        )
            .toEqual([[0, 30], [67, 121]]);
        {
            // eslint-disable-next-line max-len
            const log =
                '        imports icode.baidu.com/baidu/bcloud/auto-debug-test/src/exclude: build constraints exclude all Go files in /Volumes/workdir/baidu/bcloud/auto-debug-test/src/exclude';
            expect(matchFilePath(log)).toEqual([[16, 73], [116, 173]]);
        }
    });

    it('matchFilePathPositionToTerminalLinkRange', () => {
        {
            // eslint-disable-next-line max-len
            const log =
                '        imports icode.baidu.com/baidu/bcloud/auto-debug-test/src/exclude: build constraints exclude all Go files in /Volumes/workdir/baidu/bcloud/auto-debug-test/src/exclude';
            const t = matchFilePathPositionToTerminalLinkRange;
            expect(t(log)).toEqual([
                {length: 15, startIndex: 0},
                {length: 42, startIndex: 74},
                {length: 0, startIndex: 174},
            ]);
        }
    });
});
