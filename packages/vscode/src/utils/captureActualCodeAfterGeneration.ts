import * as vscode from 'vscode';
import {uploadContext} from '@/api';

/**
 * 在代码生成后捕获实际代码
 *
 * @param uri 文件URI
 * @param uuid 回溯生成记录的唯一标识符
 * @param chance 捕获概率，默认为0.6
 */
export default function captureActualCodeAfterGeneration(uri: vscode.Uri, uuid: string, chance: number = 0.6) {
    if ($features.PLATFORM !== 'internal') {
        return;
    }
    if (Math.random() > chance) {
        return;
    }
    setTimeout(
        async () => {
            try {
                const document = await vscode.workspace.openTextDocument(uri);
                if (!document) {
                    return;
                }
                const text = document.getText();
                uploadContext({
                    uuid,
                    capturedActualAllContext: text,
                });
            }
            catch {
                // noop
            }
        },
        1000 * 60 * 2
    );
}
