import path from 'node:path';
import * as vscode from 'vscode';
import {getTraceRepo} from './git';

/**
 * 统计用于代码生成的代码库信息
 * 1. 获取执行目录：当前文件存在，取当前文件的cwd, 不存在取workspace
 * 2. 在执行目录获取 gitRemoteUrl, 只要能够按规则解析，取 host:port/ 后面的地址
 * 3. 如果2解析不出来，假设1的执行目录为 /home/<USER>/xxx/yyy ，往上取两级吧，示例为 xxx/yyy
 */
export const getTraceRepoInfo = async () => {
    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor?.document.uri.scheme === 'file') {
        const cwd = activeEditor.document.uri.fsPath;
        const repoName = await getTraceRepo(path.dirname(cwd));
        return {repoName, repoId: '', currentFilePath: activeEditor.document.uri.fsPath};
    }

    const workspaceRoot = vscode.workspace.workspaceFolders?.[0].uri.fsPath;
    if (workspaceRoot) {
        const repoName = await getTraceRepo(workspaceRoot);
        return {repoName: repoName, repoId: '', currentFilePath: ''};
    }

    return {repoName: '', repoId: '', currentFilePath: ''};
};
