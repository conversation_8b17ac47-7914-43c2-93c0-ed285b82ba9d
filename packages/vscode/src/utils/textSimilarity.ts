import {levenshteinEditDistance} from 'levenshtein-edit-distance';
import {intersection, union} from 'lodash';

/**
 * 计算编辑相似度，返回一个介于0到1之间的值。
 * 该函数接受两个字符串参数：source和target，分别表示源字符串和目标字符串。
 * 返回值是两个字符串之间的编辑距离与最大长度的比值，如果其中任一字符串为空则返回0。
 *
 * @param {string} source 源字符串。
 * @param {string} target 目标字符串。
 * @returns {number} 介于0到1之间的编辑相似度值，0表示完全不同，1表示完全相同。
 */
export function computeEditSimilarity(source: string, target: string): number {
    const distance = levenshteinEditDistance(source, target);
    const maxLength = Math.max(source.length, target.length);
    if (maxLength === 0) {
        return 0;
    }
    return distance / maxLength;
}

/**
 * 计算两个字符串的前缀相似度
 *
 * @param source 源字符串
 * @param target 目标字符串
 * @returns 返回一个介于0到1之间的数字，表示两个字符串的前缀相似度。如果目标字符串为空，则返回0。
 */
export function computePrefixSimilarity(source: string, target: string) {
    if (target.length === 0) {
        return 0;
    }
    let matchedLength = 0;
    for (let i = 0; i < source.length && i < target.length; ++i) {
        if (source[i] === target[i]) {
            matchedLength++;
        }
        else {
            break;
        }
    }
    return matchedLength / target.length;
}

/**
 * 将输入的字符串按照非字母数字字符分割成单词数组
 *
 * @param text 待分割的字符串
 * @returns 返回一个由单词组成的数组，其中每个单词由字母和数字组成
 */
export function splitIntoWords(text: string) {
    return text.split(/[^a-zA-Z0-9]/).filter(word => word.length > 0);
}

/**
 * 计算两个字符串的 jaccard 相似度
 *
 * @param source 第一个字符串
 * @param target 第二个字符串
 * @returns 两个字符串的 jaccard 相似度，范围在0到1之间
 */
export function computeJaccardSimilarity(source: string, target: string) {
    const sourceVocab = splitIntoWords(source);
    const targetVocab = splitIntoWords(target);
    const intersectionSet = intersection(sourceVocab, targetVocab);
    const unionSet = union(sourceVocab, targetVocab);
    if (unionSet.length === 0) {
        return 0;
    }
    return intersectionSet.length / unionSet.length;
}

/**
 * 计算两个字符串长度的相似度得分，长度一致时分时为1，当长度差异超过 allowedLengthDifference 时，返回0。
 *
 * @param source 源字符串
 * @param target 目标字符串
 * @param allowedLengthDifference 允许的最大长度差，默认为10
 * @returns 返回长度相似度得分，范围在0到1之间，分数越高表示两个字符串长度越接近
 */
export function computeLengthScore(source: string, target: string, allowedLengthDifference: number = 10) {
    const maxLengthDifference = Math.min(allowedLengthDifference, target.length);
    if (maxLengthDifference === 0) {
        return source.length === target.length ? 1 : 0;
    }
    return 1 - Math.min(Math.abs(source.length - target.length), maxLengthDifference) / maxLengthDifference;
}

/**
 * 计算两个字符串的最长公共子序列长度
 *
 * @param source 第一个字符串
 * @param target 第二个字符串
 * @returns 返回最长公共子序列的长度
 */
export function computeLCS(source: string, target: string) {
    const m = source.length;
    const n = target.length;
    const dp: number[][] = [];
    for (let i = 0; i <= m; ++i) {
        dp[i] = new Array(n + 1).fill(0);
    }
    for (let i = 1; i <= m; ++i) {
        for (let j = 1; j <= n; ++j) {
            if (source[i - 1] === target[j - 1]) {
                dp[i][j] = dp[i - 1][j - 1] + 1;
            }
            else {
                dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
            }
        }
    }
    return dp[m][n];
}

export function computeLCSScore(source: string, target: string) {
    return computeLCS(source, target) / Math.max(source.length, target.length);
}
