import * as vscode from 'vscode';
import {CancelToken} from 'axios';
import {GenerateCodeResponse, RawGenerateCode} from '@/api';
import {processIncomingMessage} from '@/common/Fetcher';
import {ParamsStreamInlineLog, apiStreamInlineLog} from './api';

export type StreamResponse = Partial<RawGenerateCode> & GenerateCodeResponse;

export async function fetchAndStreamInlineLog(
    params: ParamsStreamInlineLog,
    update: (content: string, uuid: string, chatId: string) => void,
    cancellationToken?: vscode.CancellationToken,
    cancelToken?: CancelToken
) {
    const res = await apiStreamInlineLog(params, cancelToken);
    return processIncomingMessage(res.data, update, cancellationToken);
}
