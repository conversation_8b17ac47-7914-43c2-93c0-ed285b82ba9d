import {basename, dirname, extname, sep} from 'path';
import * as vscode from 'vscode';
import {injectable, inject, LazyServiceIdentifer} from 'inversify';
import Parser from 'web-tree-sitter';
import axios from 'axios';
import {debounce} from 'lodash';
import {TYPES} from '@/inversify.config';
import {CMD_ADD_INLINE_LOG} from '@/constants';
import {error, warning} from '@/common/outputChannel';
import {readFileContent} from '@/utils/workspace';
import {RipgrepProvider} from '@/common/RipgrepProvider';
import {getCompleteFirstLine} from '@/utils/document';
import {stripExtraIndent} from '@/utils/indent';
import {addMarkdownCodeBlock, findLeadingNonWhitespaceIndex} from '@/utils/common';
import {buildParams} from '@/common/Fetcher';
import {iocContainer} from '@/iocContainer';
import {L10n, getGenerationFailureText} from '@/common/L10nProvider/L10n';
import {InlineLogProviderText} from '@/common/L10nProvider/constants';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {CodeLensDisplayMode, CodelensConfig, ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {DiffProvider} from '../DiffProvider';
import {ILicenseController} from '../LicenseController/types';
import {CodeLensProvider} from '../FoldedCodeLensProvider';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {UserService} from '../UserService';
import {Conversation} from '../ChatViewProvider/Conversation';
import {TextResponse} from '../ChatViewProvider/TextResponse';
import {ChatBaseProvider} from '../ChatBaseProvider';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';
import {
    LogModuleItem,
    apiGetLogModule,
    DependencyConfigItem,
    ParamsStreamInlineLog,
    apiGetDependencyConfig,
} from './api';
import {fetchAndStreamInlineLog} from './fetcher';

// 最大输入长度限制，跟前面的行间注释等功能保持一致
const MAX_INPUT_LENGTH = 9000;

const LOGGER_CONFIG_FILES = [
    'pom.xml',
    'build.gradle',
    'build.gradle.kts',
    'go.mod',
    'requirements.txt',
];

export function filterFunctionNode(node: Parser.SyntaxNode) {
    if (node.hasError()) {
        return false;
    }
    const lineCount = node.endPosition.row - node.startPosition.row;
    return lineCount > 0 && node.text.length <= MAX_INPUT_LENGTH;
}

interface InlineLogContext {
    snippet: string[];
    logModules: LogModuleItem[];
    needUpdate: boolean;
    configUri: vscode.Uri;
    isSnippetUpdating: boolean;
    isLogModuleUpdating: boolean;
}

function getAllParentDirectories(filePath: string): string[] {
    const result: string[] = [];
    let currentDir = dirname(filePath);
    while (currentDir !== dirname(currentDir)) {
        result.push(currentDir);
        currentDir = dirname(currentDir);
    }
    return result;
}

const characterToColumn = (pos: vscode.Position) => ({line: pos.line, column: pos.character});

@injectable()
export class InlineLogProvider extends ChatBaseProvider implements CodeLensProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
    ];
    private disposables: vscode.Disposable[] = [];
    private readonly _onDidChangeCodeLenses = new vscode.EventEmitter<void>();
    onDidChangeCodeLenses?: vscode.Event<void> | undefined = this._onDidChangeCodeLenses.event;

    // 是否初始化，现在改为只有第一次用户点击使用时才初始化
    private initialized = false;
    // 这个配置服务端下发的，用来控制找什么样的代码片段作为上下文
    private readonly dependencyConfig = new Map<string, DependencyConfigItem>();

    // 以 folder 为 key 的 map，在里面存了一些上下文，包括一些已有的日志代码片段和当前代码库的日志模块信息
    private readonly contextMap = new Map<string, InlineLogContext>();

    constructor(
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(TYPES.ILicenseController) private readonly licenseController: ILicenseController,
        @inject(PerformanceLogProvider) private readonly performanceLog: PerformanceLogProvider,
        @inject(RipgrepProvider) private readonly ripgrepProvider: RipgrepProvider,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider
    ) {
        super(diffProvider);
        this.disposables.push(
            vscode.languages.registerCodeLensProvider(
                InlineLogProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this
            ),
            vscode.commands.registerCommand(
                CMD_ADD_INLINE_LOG,
                async (document: vscode.TextDocument, range: vscode.Range) => {
                    const startTime = performance.now();
                    this.handleAddInlineLog(document, range, startTime);
                }
            )
        );
    }

    async provideCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();
        if (codelensDisplayMode !== CodeLensDisplayMode.TextTitle) {
            return [];
        }
        if (this.diffProvider.isInDiff(document.uri.fsPath)) {
            return [];
        }
        const codeLenses = await this.computeCodeLenses(document);
        return this.tmpCodeLensProvider.filterOverlapCodeLenses(document, codeLenses);
    }

    async computeCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensConfig = this.configProvider.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        if (codelensConfig?.enableInlineLog !== true || !this.licenseController.hasLicense) {
            return [];
        }

        if (!this.showInlineLogCodeLenses(document)) {
            return [];
        }

        return this.treeSitterProvider.getFunctionCodeLens(
            document,
            filterFunctionNode,
            (document: vscode.TextDocument, range: vscode.Range) => {
                return {
                    title: L10n.t(InlineLogProviderText.CODELENS_TITLE),
                    tooltip: L10n.t(InlineLogProviderText.CODELENS_TOOLTIP),
                    command: CMD_ADD_INLINE_LOG,
                    arguments: [document, range],
                };
            }
        );
    }

    private async handleAddInlineLog(
        document: vscode.TextDocument,
        range: vscode.Range,
        startTime: number = performance.now()
    ) {
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_ADD_INLINE_LOG});
        const firstLine = getCompleteFirstLine(document, range);
        const functionContent = stripExtraIndent(document.getText(range), true, firstLine);
        const conversation = this.chatViewProvider.createConversation(
            L10n.t(InlineLogProviderText.PROMPT),
            'addInlineLog',
            addMarkdownCodeBlock(functionContent, document.languageId)
        );
        const definitionLine = document.lineAt(range.start).text;
        const padding = definitionLine.slice(0, findLeadingNonWhitespaceIndex(definitionLine));
        const res = await this.resolve(
            document,
            range,
            conversation,
            functionContent,
            padding,
            startTime
        );
        if (res?.uuid) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'addInlineLog',
                duration: performance.now() - startTime,
                uuid: res.uuid,
                type: 'all',
            });
        }
    }

    private async fetchAndStream(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        padding: string,
        reply: TextResponse,
        params: ParamsStreamInlineLog,
        startTime: number
    ) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        let firstToken = true;
        const {content, uuid, processor, chatId} = await fetchAndStreamInlineLog(
            params,
            (content, uuid, chatId) => {
                if (content) {
                    reply.update(
                        content,
                        this.getSuccessActions(
                            reply,
                            document,
                            functionRange,
                            padding,
                            uuid,
                            chatId
                        )
                    );
                    if (firstToken) {
                        this.performanceLog.log({
                            plugin: 'comate',
                            skill: 'addInlineLog',
                            duration: performance.now() - startTime,
                            uuid,
                            type: 'first-token',
                        });
                        firstToken = false;
                    }
                }
            },
            reply.message.cancelTokenSource?.token,
            axiosTokenSource.token
        );
        if (processor.error) {
            reply.fail(getGenerationFailureText(processor.errorMsg));
            return;
        }
        if (!content) {
            reply.fail(L10n.t(InlineLogProviderText.GENERATE_ERROR));
            return;
        }
        reply.success(
            content,
            this.getSuccessActions(
                reply,
                document,
                functionRange,
                padding,
                uuid,
                chatId
            ),
            uuid
        );
        return {uuid};
    }

    private async resolve(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        conversation: Conversation,
        functionContent: string,
        padding: string,
        startTime: number
    ) {
        const reply = conversation.addResponse('stream', '', 'inProgress', {
            regenerate: async () => {
                this.resolve(document, functionRange, conversation, functionContent, padding, performance.now());
            },
            // 打开全文复制按钮
            copyAll: () => {},
        });

        const paramsRes = await this.buildStreamParams(document, functionRange, functionContent);
        if (paramsRes.type !== 'success') {
            reply.fail(getGenerationFailureText(paramsRes.reason));
            return {uuid: ''};
        }

        try {
            const res = await this.fetchAndStream(document, functionRange, padding, reply, paramsRes.value, startTime);
            return res;
        }
        catch (e: any) {
            reply.fail(getGenerationFailureText(e.message));
            return {uuid: ''};
        }
    }

    private getSuccessActions(
        reply: TextResponse,
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        padding: string,
        uuid?: string,
        chatId?: string
    ) {
        const {diff, accept} = this.diffProvider.createReplacementDiffHandler(
            document,
            functionRange,
            content => {
                const lines = content.split('\n');
                const paddingLines = lines.map((line, index) => (line.trim() && index !== 0
                    ? `${padding}${line}`
                    : line)
                );
                return paddingLines.join('\n').trimEnd();
            },
            uuid
        );
        const chatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getChatId: () => chatId,
            getMessageContent: () => reply.message.content,
            getTrackUuid: () => uuid || reply.message.extra?.uuid,
        };
        return {
            ...this.defaultActions(chatResponseProxy, undefined, undefined, {ignoreSmartApplyFeature: true}),
            diff,
            accept,
        };
    }

    private async buildStreamParams(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        functionContent: string
    ) {
        const paramsRes = await buildParams(
            document,
            functionRange.start,
            iocContainer.get(UserService)
        );
        if (paramsRes.type !== 'success') {
            return paramsRes;
        }
        const [context] = this.getContext(document);
        const logModules = context?.logModules ?? [];
        const {path, content, row, col, username, ...restParams} = paramsRes.value;
        const params = {
            ...restParams,
            username: restParams.key ? restParams.key : username,
            file: {
                path,
                name: path.split(sep).pop() ?? '',
                start: characterToColumn(functionRange.start),
                end: characterToColumn(functionRange.end),
                content,
                functionCode: functionContent,
            },
            logCodes: context && context.snippet ? context.snippet : [],
            logModules: logModules.map(v => ({name: v.module})),
        };
        return {type: 'success', value: params} as const;
    }

    /**
     * 根据当前文件路径去找对应的context，context不是以workspace为单位的，而是以找到的配置文件所在的目录为单位
     * 通常来说都是 workspace 下只有一个context，这部分感觉做复杂了
     */
    private getContext(document: vscode.TextDocument): [InlineLogContext | undefined, string | undefined] {
        const directories = getAllParentDirectories(document.uri.fsPath);
        const key = directories.find(v => this.contextMap.has(v));
        if (key) {
            return [this.contextMap.get(key), key];
        }
        return [undefined, key];
    }

    private showInlineLogCodeLenses(document: vscode.TextDocument) {
        if (!this.checkInitialize()) {
            return false;
        }

        const [context, key] = this.getContext(document);
        if (!key || !context) {
            return false;
        }

        if (context.needUpdate) {
            this.doExtractContext(document, key);
            return false;
        }

        if (context.isLogModuleUpdating || context.isSnippetUpdating) {
            return false;
        }

        return true;
    }

    /**
     * 更新指定路径下的context内容，这里的更新是在 CodeLense 触发展示时触发
     *
     * @param document vscode.TextDocument - 要更新的文档对象
     * @param key string - 上下文信息的键名
     */
    private async doExtractContext(document: vscode.TextDocument, key: string) {
        const context = this.contextMap.get(key);
        if (!context) {
            return;
        }
        this.contextMap.set(key, {...context, needUpdate: false});
        this.doExtractSimilarLogSnippet(document, key);
        this.doExtractLogModule(key);
    }

    /**
     * 异步更新对应目录下的相似日志片段，根据后端的配置用 ripgrep 搜索相似日志片段，并存入 context.snippet 中
     *
     * @param document 当前文档
     * @param key 文件目录作为key
     */
    private async doExtractSimilarLogSnippet(document: vscode.TextDocument, key: string) {
        const context = this.contextMap.get(key);
        if (!context) {
            return;
        }
        const start = performance.now();
        context.isSnippetUpdating = true;
        try {
            const dependency = this.dependencyConfig.get(document.languageId);
            const regexp = dependency?.log_regexp;
            if (!regexp || !regexp[0]) {
                return;
            }
            const folderUri = vscode.Uri.joinPath(context.configUri, '..');
            const currentExtname = extname(document.uri.fsPath);
            const keywords = dependency?.log_keyword ?? ['info', 'warn', 'error', 'exception'];
            const resultWithKeyword = new Map<string, string>();
            const resultWithoutKeyword: string[] = [];
            const resolveSearchResult = (preview: string) => {
                const target = keywords.find(v => {
                    const regexp = new RegExp(`.${v}`, 'i');
                    return regexp.test(preview);
                });
                if (target) {
                    if (!resultWithKeyword.has(target)) {
                        resultWithKeyword.set(target, preview);
                    }
                }
                else {
                    resultWithoutKeyword.push(preview);
                }
            };

            await this.ripgrepProvider.searchText(
                {pattern: regexp[0], isCaseSensitive: false, isRegExp: true},
                {folderUri, limit: 10},
                {
                    onResult: result => {
                        if (currentExtname !== extname(result.uri.fsPath)) {
                            return false;
                        }
                        if (resultWithKeyword.size < keywords.length) {
                            resolveSearchResult(result.preview);
                            return false;
                        }
                        return true;
                    },
                }
            );
            // 一般来说所有结果都是通过关键字匹配出来的，就是不可能存在上面的关键字匹配不到的情况
            // 但考虑到后面可能会改匹配的正则，所以这里加个最多保留 5 个没有关键字的结果
            const allResult = [...resultWithKeyword.values(), ...resultWithoutKeyword.slice(0, 5)];
            context.snippet.push(...allResult.map(v => v.trim()));
        }
        catch (e) {
            error('AddInlineLog UpdateSnippet Error: ', (e as Error).message);
        }
        finally {
            context.isSnippetUpdating = false;
            // 强制刷新一下的话，让 CodeLens 尽快出来
            this._onDidChangeCodeLenses.fire();
        }

        // 记录下时间，后面看下大概多少
        this.performanceLog.log({
            plugin: 'comate',
            skill: 'addInlineLog',
            duration: performance.now() - start,
            uuid: '',
            type: 'inlineLog-snippet',
        });
    }

    /**
     * 异步更新对应目录下所使用的日志模块信息，通过读配置文件内容，然后让后端去判断用了什么日志库，结果记录到 context.logModules 里
     *
     * @param key 文件目录作为key
     */
    private async doExtractLogModule(key: string) {
        const context = this.contextMap.get(key);
        if (!context) {
            return;
        }
        const start = performance.now();
        context.isLogModuleUpdating = true;
        try {
            const content = await readFileContent(context.configUri);
            const res = await apiGetLogModule({
                content,
                filepath: context.configUri.fsPath,
            });

            const {dependencies = []} = res;
            context.logModules = dependencies;
        }
        catch (e) {
            error('AddInlineLog UpdateLogModule Error: ', (e as Error).message);
        }
        finally {
            context.isLogModuleUpdating = false;
            // 强制刷新一下的话，让 CodeLens 尽快出来
            this._onDidChangeCodeLenses.fire();
        }
        // 记录下更新module的时间
        this.performanceLog.log({
            plugin: 'comate',
            skill: 'addInlineLog',
            duration: performance.now() - start,
            uuid: '',
            type: 'inlineLog-module',
        });
    }

    private checkInitialize() {
        if (this.initialized) {
            return true;
        }
        this.init();
        this.initialized = true;
        return false;
    }

    /**
     * 初始化方法，会去请求接口获取配置信息和找当前工作区下的特定配置文件（用这些配置文件来判断日志库）
     */
    private async init() {
        await this.loadDependencyConfig();
        await this.initContextMap();

        const onDidConfigFileChange = debounce(
            (document: vscode.TextDocument) => {
                const uri = document.uri;
                const folder = dirname(uri.fsPath);
                const context = this.contextMap.get(folder) ?? this.createEmptyContext(uri);
                context.needUpdate = true;
                this.contextMap.set(folder, context);
            },
            // 随便设置的一个值
            2 * 1000
        );
        this.disposables.push(
            vscode.workspace.onDidChangeWorkspaceFolders(() => {
                this.initContextMap();
            }),
            vscode.workspace.onDidChangeTextDocument(edit => {
                const document = edit.document;
                if (
                    InlineLogProvider.supportedLanguages.includes(document.languageId)
                    && LOGGER_CONFIG_FILES.includes(basename(document.uri.fsPath))
                ) {
                    onDidConfigFileChange(document);
                }
            })
        );

        // 计算了context后触发一次更新
        this._onDidChangeCodeLenses.fire();
    }

    private async loadDependencyConfig() {
        try {
            const dependencyConfig = await apiGetDependencyConfig();
            if (dependencyConfig.length === 0) {
                warning('Unexpected empty dependencies during AddInlineLog initialization');
            }
            for (const item of dependencyConfig) {
                this.dependencyConfig.set(item.lang, item);
            }
        }
        catch (e) {
            error('AddInlineLog Init Dependency Error: ', (e as Error).message);
        }
    }

    private async initContextMap() {
        this.contextMap.clear();
        const uris = await vscode.workspace.findFiles(
            '**/{pom.xml,build.gradle,build.gradle.kts,go.mod,requirements.txt}',
            '**​/(node_modules|site_packages)/**',
            5
        );
        for (const uri of uris) {
            const folder = dirname(uri.fsPath);
            this.contextMap.set(folder, this.createEmptyContext(uri));
        }
    }

    private createEmptyContext(uri: vscode.Uri) {
        return {
            snippet: [],
            logModules: [],
            needUpdate: true,
            configUri: uri,
            isLogModuleUpdating: false,
            isSnippetUpdating: false,
        };
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
