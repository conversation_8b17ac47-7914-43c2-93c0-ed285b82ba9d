import {IncomingMessage} from 'http';
import * as vscode from 'vscode';
import {CancelToken} from 'axios';
import {info} from '@/common/outputChannel';
import {Position} from '@/common/types';
import {hideSensitive} from '@/utils/common';
import {createAxios} from '../../api/common';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://cs.baidu-int.com',
    'internal-test': 'https://sourcegraphtest.baidu-int.com',
    saas: 'https://comate.baidu.com/api',
    'saas-test': 'https://comate-cop.now.baidu-int.com/api',
    poc: 'https://comate.baidu.com/api',
    'poc-test': 'https://comate.baidu.com/api',
};

const feature = $features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '');
const axiosInstance = createAxios({
    baseURL: BASE_URL_MAPPING[feature],
});

info(`InlineLog api baseURL: ${axiosInstance.defaults.baseURL}`);

export interface DependencyConfigItem {
    lang: string;
    dependency_path: string[];
    log_regexp: string[];
    log_keyword: string[];
}

const DEPENDENCY_CONFIG_PREFIX = $features.PLATFORM === 'internal' ? '/nlcodesearch/v2' : '/autowork/retrieval/v2';
export async function apiGetDependencyConfig(): Promise<DependencyConfigItem[]> {
    const res = await axiosInstance.get(`${DEPENDENCY_CONFIG_PREFIX}/dependency/config`);
    if (res.status === 200) {
        return res.data ?? [];
    }
    return [];
}

interface ParamsGetLogModule {
    filepath: string;
    content: string;
}

export interface LogModuleItem {
    module: string;
    lang: string;
}

export interface LogModuleResponse {
    dependencies?: LogModuleItem[];
}
const LOG_MODULE_PREFIX = $features.PLATFORM === 'internal' ? '/embeddingindex/v2' : '/autowork/index/v2';
export async function apiGetLogModule(params: ParamsGetLogModule): Promise<LogModuleResponse> {
    const data = JSON.stringify({
        scene: 'log',
        filepath: params.filepath,
        content: params.content,
    });
    const res = await axiosInstance.post(`${LOG_MODULE_PREFIX}/dependency/info`, data);
    if (res.status === 200) {
        return res.data ?? {};
    }
    return {};
}

interface FileInfo {
    path: string;
    name: string;
    start: Position;
    end: Position;
    content: string;
    functionCode: string;
    repo?: string;
}

export interface ParamsStreamInlineLog {
    file: FileInfo;
    logModules: Array<{name: string}>;
    logCodes: string[];
    username: string;
    device?: string;
    ide: string;
    compiler?: string;
}

const INLINE_LOG_BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu-int.com/api/aidevops/autocomate',
    'internal-test': 'https://comate-test.now.baidu-int.com/api/aidevops/autocomate',
    saas: 'https://comate.baidu.com/api/aidevops/autocomate',
    'saas-test': 'https://comate-cop.now.baidu-int.com/api/aidevops/autocomate',
    poc: 'https://comate.baidu.com/api/aidevops/autocomate',
};

const inlineLogAxiosInstance = createAxios({
    baseURL: INLINE_LOG_BASE_URL_MAPPING[feature],
});
info(`InlineLog stream api baseURL: ${inlineLogAxiosInstance.defaults.baseURL}`);

export async function apiStreamInlineLog(params: ParamsStreamInlineLog, cancelToken?: CancelToken) {
    const ideVersion = vscode.version;
    const deductedFile = hideSensitive(params.file, ['content']);
    const query = JSON.stringify({...params, ideVersion, file: deductedFile});
    const result = await inlineLogAxiosInstance.post<IncomingMessage>(
        '/rest/v1/shortcut/log/stream',
        query,
        {
            headers: {
                'Content-Type': 'application/json',
                'login-name': params.username ? encodeURIComponent(params.username) : undefined,
            },
            responseType: 'stream',
            cancelToken,
        }
    );
    return result;
}
