import {basename} from 'node:path';
import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {EventMessage} from '@shared/protocols';
import {debounce} from 'lodash';
import {RegisteredCommand} from '@/constants';
import {vscodeCommands} from '@/utils/vscodeComands';
import {ChatViewProvider} from '../ChatViewProvider';

@injectable()
export class EditorWatcherProvider implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    constructor(
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider
    ) {
        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.EditorChangedEvent,
            listener: this.watchActiveTextEditor.bind(this),
        });
        this.disposables.push(
            vscode.commands.registerCommand(RegisteredCommand.addFileContextToChat, () => {
                if (vscode.window.activeTextEditor) {
                    vscodeCommands.openChatPanel({
                        activeTabKey: 'CHAT',
                        source: 'fileContextCommand',
                    });
                    this.sendEditorPathAndSelection(vscode.window.activeTextEditor, {fromCommand: true});
                }
            }),
            vscode.window.onDidChangeTextEditorSelection(
                debounce(event => {
                    if (event.textEditor.document.uri.scheme === 'file') {
                        const editor = event.textEditor;
                        this.sendEditorPathAndSelection(editor);
                    }
                }, 1000)
            ),
            vscode.window.onDidChangeVisibleTextEditors(debounce(this.watchActiveTextEditor, 300).bind(this))
        );
    }

    private watchActiveTextEditor() {
        const editor = vscode.window.activeTextEditor;
        if (editor && editor.document.uri.scheme === 'file') {
            this.sendEditorPathAndSelection(editor);
        }
        else {
            this.chatViewProvider.sendDataToWebview(EventMessage.EditorChangedEvent, {
                activeEditor: null,
            });
        }
    }

    private sendEditorPathAndSelection(editor: vscode.TextEditor, opts?: {fromCommand?: boolean}) {
        const noSelection = editor.selection.start.isEqual(editor.selection.end);
        this.chatViewProvider.sendDataToWebview(EventMessage.EditorChangedEvent, {
            fromCommand: !!opts?.fromCommand,
            activeEditor: {
                relativePath: vscode.workspace.asRelativePath(editor.document.uri),
                name: basename(editor.document.uri.fsPath),
                selection: noSelection ? null : [
                    editor.selection.start.line + 1,
                    editor.selection.end.line + 1,
                ],
            },
        });
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
    }
}
