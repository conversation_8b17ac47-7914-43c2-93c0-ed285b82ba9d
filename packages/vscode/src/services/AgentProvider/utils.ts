import {WebviewAgentConversationType, AgentConfig} from '@shared/protocols';

export const getAgentConfig = (
    currentConfig: AgentConfig,
    type: WebviewAgentConversationType,
    enable: boolean
) => {
    switch (type) {
        case WebviewAgentConversationType.CompletionBotConversation:
            return {...currentConfig, enableCompletionIntelligence: enable};
        case WebviewAgentConversationType.E2EBotConversation:
            return {...currentConfig, enableFullStackIntelligence: enable};
        case WebviewAgentConversationType.DebugBotConversation:
            return {...currentConfig, enableDebugIntelligence: enable};
        case WebviewAgentConversationType.SecuBotConversation:
            return {...currentConfig, enableSecurityIntelligence: enable};
        case WebviewAgentConversationType.TestBotConversation:
            return {...currentConfig, enableUTChatIntelligence: enable, enableUTEditorIntelligence: enable};
        default:
            return currentConfig;
    }
};
