import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {
    AGENT_DEBUG_CUSTOM_ACTION,
    // ACTION_COMATE_GET_AGENT_TASKS,
    // ACTION_COMATE_ADD_AGENT_TASK,
    // ACTION_COMATE_SET_FOREGROUND_TASK,
    // ACTION_COMATE_UPDATE_AGENT_TASK_MESSAGES,
    AgentPayload,
    // AgentMessage,
    // Task,
} from '@comate/plugin-shared-internals';
import {EventMessage, AgentConfig, WebviewAgentConversationType} from '@shared/protocols';
import {error} from '@/common/outputChannel';
import {getUserConfig} from '@/api/index';
import {isSaasOrPoc} from '@/utils/features';
import {KernelProvider} from '../KernelProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {VSCodeConfigProvider, Config<PERSON>ey} from '../ConfigProvider';
import {UserService} from '../UserService';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {TerminalManager} from '../TerminalLink/TerminalManager';
import {TerminalLinkProvider} from '../TerminalLink/TerminalLinkProvider';
import {
    CMD_GET_AGENT_CONVERSATIONS,
    CMD_ADD_AGENT_TASK_CONVERSATION,
    CMD_ADD_SET_FOREGROUND,
    CMD_ADD_NEW_MESSAGE,
    CMD_ADD_UPDATE_AGENT_STATUS,
    CMD_ADD_GET_AGENT_STATUS,
} from './constants';
import {getAgentConfig} from './utils';
import {handleDebugAgentAction} from './debugAgent';

export const CMD_MESSAGE_GENERATE = 'baidu.comate.sourceControlGenerateMessage';

@injectable()
export class AgentProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(UserService) private readonly userService: UserService,
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(TerminalManager) private readonly terminalManager: TerminalManager,
        @inject(TerminalLinkProvider) private readonly terminalLinkProvider: TerminalLinkProvider
    ) {
        this.disposables.push(
            vscode.commands.registerCommand(CMD_GET_AGENT_CONVERSATIONS, this.getAgentConversations.bind(this)),
            vscode.commands.registerCommand(CMD_ADD_AGENT_TASK_CONVERSATION, this.addAgentConversation.bind(this)),
            vscode.commands.registerCommand(CMD_ADD_SET_FOREGROUND, this.setForeground.bind(this)),
            vscode.commands.registerCommand(CMD_ADD_NEW_MESSAGE, this.newMessage.bind(this)),
            vscode.commands.registerCommand(CMD_ADD_UPDATE_AGENT_STATUS, this.updateAgentConfigByWebview.bind(this)),
            vscode.commands.registerCommand(CMD_ADD_GET_AGENT_STATUS, this.getAgentConfig.bind(this)),
            vscode.workspace.onDidChangeConfiguration(async e => {
                if (e.affectsConfiguration('baidu.comate.license')) {
                    this.updateAgentApplyStatusAndConfig();
                }
                if (e.affectsConfiguration('baidu.comate.beta')) {
                    this.updateAgentConfigByIDE();
                }
            })
        );
        this.listenConversationMessagesUpdate();
        this.listenToastMessageChange();
        this.listenDebugAgentActions();
    }

    private async updateAgentApplyStatusAndConfig() {
        const [username] = await this.userService.getCurrentUser();
        const license = this.configProvider.getLicense();
        const remoteConfig = await getUserConfig(isSaasOrPoc ? license : username);
        const enableIntelligenceAgent = this.configProvider.getConfig<AgentConfig>(ConfigKey.Beta);
        const agentConfig = {enableIntelligenceAgent, applyStatus: remoteConfig?.applyStatus};
        this.chatViewProvider.sendDataToWebview(EventMessage.AgentSwitchChangeFromIdeEvent, agentConfig);
    }

    private async updateAgentConfigByIDE() {
        const agentConfig = await this.configProvider.getAgentConfig();
        this.chatViewProvider.sendDataToWebview(EventMessage.AgentSwitchChangeFromIdeEvent, agentConfig);
    }

    private async getAgentConfig() {
        const [username] = await this.userService.getCurrentUser();
        const license = this.configProvider.getLicense();
        const remoteConfig = await getUserConfig(isSaasOrPoc ? license : username);
        const enableIntelligenceAgent = this.configProvider.getConfig<AgentConfig>(ConfigKey.Beta);
        const agentConfig = {
            enableIntelligenceAgent,
            applyStatus: remoteConfig?.applyStatus,
        };
        return agentConfig;
    }

    private async updateAgentConfigByWebview({type, enable}: {type: WebviewAgentConversationType, enable: boolean}) {
        const agentConfig = await this.configProvider.getAgentConfig();

        if (agentConfig.enableIntelligenceAgent) {
            const updateConfig = getAgentConfig(agentConfig.enableIntelligenceAgent, type, enable);
            this.configProvider.updateAgentConfig(updateConfig);
        }
    }

    async getAgentConversations() {
        try {
            const agentConversations = await this.kernelProvider.client?.sendRequest(
                'COMATE_AGENT_GET_CONVERSATIONS',
                {}
            );

            return agentConversations;
        }
        catch (ex) {
            error('getAgentConversations error:', (ex as Error).message);
            return [];
        }
    }

    async addAgentConversation(agentPayload: {agentPayload: AgentPayload}) {
        try {
            const res = await this.kernelProvider.client?.sendRequest('COMATE_AGENT_START_NEW_CHAT', agentPayload);
            return res;
        }
        catch (ex) {
            error('addAgentConversation error:', (ex as Error).message);
            return null;
        }
    }

    setForeground(agentPayload: {agentPayload: AgentPayload}) {
        try {
            const messages = this.kernelProvider.client?.sendRequest(
                'COMATE_AGENT_SET_FOREGROUND_CONVERSATION',
                agentPayload
            );
            return messages;
        }
        catch (ex) {
            error('setForeground error:', (ex as Error).message);
            return null;
        }
    }

    newMessage(agentPayload: {agentPayload: AgentPayload}) {
        try {
            this.kernelProvider.client?.sendRequest('COMATE_AGENT_NEW_MESSAGE', agentPayload);
        }
        catch (ex) {
            error('stopGeneration error:', (ex as Error).message);
        }
    }

    listenConversationMessagesUpdate() {
        this.kernelProvider.client?.onRequest(
            'COMATE_AGENT_UPDATE_MESSAGE',
            (res: any) => {
                this.chatViewProvider.sendDataToWebview(
                    EventMessage.AgentConversationMessageUpdateEvent,
                    res.data
                );
            }
        );
    }
    listenToastMessageChange() {
        this.kernelProvider.client?.onRequest(
            'TOAST_MESSAGE_CHANGE_MESSAGE',
            (res: any) => {
                this.chatViewProvider.sendDataToWebview(
                    EventMessage.ToastMessageChangeEvent,
                    res.data
                );
            }
        );
    }

    /**
     * Debug智能体自定义IDE事件
     */
    listenDebugAgentActions() {
        this.kernelProvider.client?.onRequest(
            AGENT_DEBUG_CUSTOM_ACTION,
            (payload: any) => {
                return handleDebugAgentAction(
                    payload,
                    this.treeSitterProvider,
                    this.terminalManager,
                    this.terminalLinkProvider
                );
            }
        );
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
