import {debounce} from 'lodash';
import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import 'reflect-metadata';
import {checkIfLicenseFromOfficialSourceMemoized} from '@/api/license';
import {L10n} from '@/common/L10nProvider/L10n';
import {ExtensionText} from '@/common/L10nProvider/constants';
import {showUserAgreementMessage} from '@/utils/login';
import {COMMAND_LOGOUT} from '@/constants';
import {getLicenseDetail, licenseValidate} from '../../api';
import {VSCodeConfigProvider} from '../ConfigProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {CMD_LICENSE_EXPIRED} from '../ComatePlusChatSession/constants';
import {ILicenseController} from './types';

const COMMAND_RENEW_LICENSE = 'baidu.comate.renewLicense';
const CONTEXT_VALID_DAYS = 'baidu.comate.context.validDays';
const CONTEXT_LOGOUT = 'baidu.comate.context.isLogin';
const CONTEXT_PLUGIN_CONFIG_PANEL_ENABLED = 'baidu.comate.pluginConfigPanelEnabled';

const getLicenseRenewUrl = async (license: string) => {
    if (!license) {
        return 'https://comate.baidu.com/user';
    }
    const isFromOfficialSource = await checkIfLicenseFromOfficialSourceMemoized(license)
        .catch(() => true); // 报错时也默认是来自官方源
    if (isFromOfficialSource) {
        return 'https://comate.baidu.com/user';
    }
    return 'https://console.bce.baidu.com/comate';
};

@injectable()
export class BasicLicenseController implements ILicenseController {
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider
    ) {
        const debounceCheckExpire = debounce(() => this.checkExpire(), 300);
        let prevLicense = this.configProvider.getLicense();
        vscode.commands.executeCommand('setContext', CONTEXT_LOGOUT, !!prevLicense);
        if (prevLicense) {
            showUserAgreementMessage();
        }

        this.disposables.push(
            vscode.commands.registerCommand(COMMAND_RENEW_LICENSE, async () => {
                const renewUrl = await getLicenseRenewUrl(this.configProvider.getLicense());
                vscode.env.openExternal(vscode.Uri.parse(renewUrl));
            }),
            vscode.commands.registerCommand(COMMAND_LOGOUT, async () => {
                this.configProvider.unsetLicenseKey();
            }),
            vscode.commands.registerCommand(CMD_LICENSE_EXPIRED, this.licenseValidity.bind(this)),
            this.configProvider.onDidChange(() => {
                if (prevLicense !== this.configProvider.getLicense()) {
                    debounceCheckExpire();
                    prevLicense = this.configProvider.getLicense();

                    const hasLicense = !!this.configProvider.getLicense();
                    this.chatViewProvider.updateLoginStatus(hasLicense);
                    vscode.commands.executeCommand('setContext', CONTEXT_LOGOUT, hasLicense);
                    // 登录后提示用户协议
                    if (hasLicense) {
                        showUserAgreementMessage();
                    }
                }
            })
        );
    }

    async isActive() {
        const key = this.configProvider.getLicense();
        if (key) {
            const res = await licenseValidate(key);
            return res.data ?? false;
        }
        return false;
    }

    async licenseValidity() {
        const license = await this.configProvider.getLicenseInfo();

        return {
            isActive: await this.isActive(),
            keyType: license?.keyType,
        };
    }

    async checkExpire() {
        const key = this.configProvider.getLicense();
        if (!key) {
            vscode.commands.executeCommand('setContext', CONTEXT_VALID_DAYS, undefined);
            vscode.commands.executeCommand('setContext', CONTEXT_PLUGIN_CONFIG_PANEL_ENABLED, false);
            return;
        }
        const licenseValidity = await this.licenseValidity();
        vscode.commands.executeCommand(
            'setContext',
            CONTEXT_PLUGIN_CONFIG_PANEL_ENABLED,
            licenseValidity.isActive && licenseValidity?.keyType !== 'NONE'
        );
        this.chatViewProvider.handleLicenseChange({
            isActive: licenseValidity.isActive,
            keyType: licenseValidity?.keyType,
        });
        try {
            const res = await getLicenseDetail(key);
            if (res.data?.validDays && typeof res.data.validDays === 'number') {
                vscode.commands.executeCommand('setContext', CONTEXT_VALID_DAYS, res.data.validDays);
            }
            else {
                vscode.commands.executeCommand('setContext', CONTEXT_VALID_DAYS, undefined);
            }
            // NOTE: message 是过期提醒文案，当 license 过期时，该接口在一天内只返回一次 message 表示过期，之后都是返回空
            if (res.data?.message) {
                const action = L10n.t(ExtensionText.RENEW_ACTION);
                const choice = await vscode.window.showInformationMessage(
                    res.data.message,
                    action
                );
                if (choice === action) {
                    vscode.commands.executeCommand(COMMAND_RENEW_LICENSE);
                }
            }
        }
        catch {
            //
        }
    }

    get hasLicense() {
        const license = this.configProvider.getLicense();
        return !!license;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
