import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {EventMessage, Feature} from '@shared/protocols';
import {debounce, isEqual, throttle} from 'lodash';
import {
    ACTION_COMATE_PLUS_QUERY_SELECTOR,
    ACTION_QUERY_SELECTOR,
    ProviderCapabilityInfo,
    QuerySelectorPayload,
} from '@comate/plugin-shared-internals';
import {activeFileContext} from '@/utils/document';
import {KernelProvider} from '../KernelProvider';
import {FeatureFlags} from '../FeatureFlags';
import {ChatViewProvider} from '../ChatViewProvider';
import {LogCategory, LogUploaderProvider} from '../LogUploaderProvider';

let previousSendActionTime: number = 0;

@injectable()
export class QueryVisibilitySelectorProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private querySelectorPayload?: QuerySelectorPayload;

    constructor(
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(FeatureFlags) private readonly featureFlags: FeatureFlags,
        @inject(LogUploaderProvider) private readonly logProvider: LogUploaderProvider
    ) {
        this.start();
    }

    start() {
        this.activate();
    }

    private async activate(): Promise<void> {
        const enable = await this.featureFlags.hasAccessTo(Feature.ComatePlus);

        if (!enable) {
            return;
        }

        this.kernelProvider.client?.onRequest(
            ACTION_COMATE_PLUS_QUERY_SELECTOR,
            (capabilities: ProviderCapabilityInfo[] = []) => {
                if (!capabilities) {
                    return;
                }
                const topCapability = capabilities
                    .filter(v => v.queryScore && v.queryScore > 0)
                    .sort((a, b) => b.queryScore! - a.queryScore!)[0];

                if (topCapability) {
                    // 推荐出来的记录
                    this.logProvider.logUserAction({
                        category: LogCategory.queryVisibility,
                        content: {
                            suggestionCommand: `${topCapability.name}@${topCapability.owner.name}`,
                            queryScore: topCapability.queryScore,
                            type: 'suggestion',
                        },
                    });
                }
                this.chatViewProvider.sendDataToWebview(EventMessage.QueryVisibilitySelector, topCapability);
            }
        );

        // 保存文件
        const saveFileEvent = vscode.workspace.onDidSaveTextDocument(
            debounce(this.onDidSaveDocument.bind(this), 1000, {leading: true})
        );

        /**
         * 打开文件
         * onDidOpenTextDocument 不太合适 vscode后台加载一个文件进来，虽然用户实际没有打开一个文件，onDidOpenTextDocument也会触发
         */
        const openFileEvent = vscode.window.onDidChangeActiveTextEditor(
            debounce(this.onDidChangeActiveEditor.bind(this), 1000, {leading: true})
        );
        // 编辑文件
        const changeFileEvent = vscode.workspace.onDidChangeTextDocument(
            debounce(
                throttle(this.onDidChangeDocument.bind(this), 1000, {leading: true}),
                1000
            )
        );

        this.disposables.push(saveFileEvent, openFileEvent, changeFileEvent);
    }

    private onDidSaveDocument(document: vscode.TextDocument) {
        this.sendActionQuerySelectorRequest(document);
    }

    private onDidChangeActiveEditor(editor?: vscode.TextEditor) {
        if (!editor || editor.document.uri.scheme !== 'file') {
            return;
        }
        this.sendActionQuerySelectorRequest(editor.document);
    }

    private onDidChangeDocument(edit: vscode.TextDocumentChangeEvent) {
        if (edit.document.uri.scheme !== 'file') {
            return;
        }

        this.sendActionQuerySelectorRequest(edit.document);
    }

    sendActionQuerySelectorRequest(document?: vscode.TextDocument) {
        const lessThan5seconds = Date.now() - previousSendActionTime < 2000;

        if (lessThan5seconds) {
            return;
        }

        previousSendActionTime = Date.now();
        const fileInfo = activeFileContext(document);

        const querySelectorPayload: QuerySelectorPayload = {
            context: {
                query: '',
                ...fileInfo,
            },
            systemInfo: {
                cwd: vscode.workspace.workspaceFolders?.[0].uri.fsPath ?? '',
            } as QuerySelectorPayload['systemInfo'],
        };

        // 重复的上下文不再重新计算
        if (isEqual(this.querySelectorPayload, querySelectorPayload)) {
            return;
        }

        this.querySelectorPayload = querySelectorPayload;
        this.kernelProvider.client?.sendRequest(ACTION_QUERY_SELECTOR, querySelectorPayload);
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
