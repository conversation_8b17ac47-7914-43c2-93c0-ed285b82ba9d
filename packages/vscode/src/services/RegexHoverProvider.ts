import {injectable} from 'inversify';
import * as vscode from 'vscode';
import {L10n} from '@/common/L10nProvider/L10n';
import {RegexHoverText} from '@/common/L10nProvider/constants';

export function getWordRangeAndTextAtPosition(
    document: vscode.TextDocument,
    position: vscode.Position,
    pattern: RegExp
): [vscode.Range, string] | undefined {
    const range = document.getWordRangeAtPosition(position, pattern);
    if (range) {
        const text = document.getText(range);
        return [range, text];
    }
    return undefined;
}

interface RegexMatcher {
    pattern: RegExp;
    extractRegex?: (text: string) => string;
}

// 这段正则来自：https://github.com/tjx666/vscode-regexp-tools/blob/20dfff4c515d384de69d452cc6c01a157aaaebd5/src/regexpHoverTip.ts#L21C13-L21C96
const JS_REGEX_LITERAL = /(^|\s|[()={},:?;])(\/(?:\\\/|\[[^\]]*\]|[^/])+\/[gmdsuyi]*)(\s|[()={},:?;.]|$)/;
const JS_REGEX_OBJECT = /(RegExp\(.*?\))(\s|[;,.]|$)/;

const JS_REGEX_MATCHERS: RegexMatcher[] = [
    {
        pattern: JS_REGEX_LITERAL,
        extractRegex: (text: string) => {
            const match = JS_REGEX_LITERAL.exec(text);
            return match?.at(2) || text;
        },
    },
    {
        pattern: JS_REGEX_OBJECT,
        extractRegex: (text: string) => {
            const match = JS_REGEX_OBJECT.exec(text);
            return match?.at(1) || text;
        },
    },
];

export function getRegexRangeAtPosition(
    document: vscode.TextDocument,
    position: vscode.Position,
    regexMatchers: RegexMatcher[]
): [vscode.Range, string] | undefined {
    for (const {pattern, extractRegex} of regexMatchers) {
        const result = getWordRangeAndTextAtPosition(document, position, pattern);
        if (result) {
            const [range, text] = result;
            const regex = extractRegex ? extractRegex(text) : text;
            return [range, regex];
        }
    }
    return undefined;
}

@injectable()
export class RegexHoverProvider implements vscode.HoverProvider, vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    static readonly supportedLanguages = [
        'javascript',
        'javascriptreact',
        'typescript',
        'typescriptreact',
        'vue',
    ];

    constructor() {
        const selector = RegexHoverProvider.supportedLanguages.map(item => ({
            language: item,
            scheme: 'file',
        }));
        this.disposables.push(
            vscode.languages.registerHoverProvider(selector, this)
        );
    }

    async provideHover(
        document: vscode.TextDocument,
        position: vscode.Position,
        token: vscode.CancellationToken
    ): Promise<vscode.Hover | undefined> {
        const regex = getRegexRangeAtPosition(document, position, JS_REGEX_MATCHERS);
        if (token.isCancellationRequested) {
            return undefined;
        }
        if (!regex) {
            return undefined;
        }
        const [range, expression] = regex;
        if (expression && range) {
            const args = [
                `${L10n.t(RegexHoverText.PROMPT)}${expression}`,
                {
                    openChatPanel: true,
                    ignoreSelection: true,
                },
            ];

            const commandUri = vscode
                .Uri
                .parse('command:baidu.comate.invokeNL2Code')
                .with({query: JSON.stringify(args)});
            const message = new vscode.MarkdownString(
                `[$(comate-logo) ${L10n.t(RegexHoverText.TOOLTIP)}](${commandUri}) \`${expression}\``
            );
            message.isTrusted = true;
            message.supportThemeIcons = true;
            return new vscode.Hover(message, range);
        }
        return undefined;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
