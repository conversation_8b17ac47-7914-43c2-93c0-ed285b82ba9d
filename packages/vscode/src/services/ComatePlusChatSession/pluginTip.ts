import {AgentListWithId} from '@shared/protocols';
import {activeFileContext, getCurrentSelection} from '@/utils/document';

export interface PluginTipConfig {
    tip?: string;
    tipCodeBlock?: {
        type: string;
        showTipWhileEmpty: boolean;
    };
}

export const ifShowTip = (tipConfig: PluginTipConfig) => {
    if (tipConfig?.tip || tipConfig?.tipCodeBlock?.type !== 'none') {
        return true;
    }
    return false;
};

// eslint-disable-next-line complexity
export const getPluginTipContent = (tipConfig: PluginTipConfig) => {
    if (!ifShowTip(tipConfig)) {
        return undefined;
    }
    const [selectCode, selectLanguage] = getCurrentSelection();
    const {activeFileContent, activeFileLanguage} = activeFileContext();
    // 根据type类型展示不同的content
    let code = '';
    let lang = '';
    if (tipConfig.tipCodeBlock?.type === 'activateFile') {
        code = activeFileContent;
        lang = activeFileLanguage;
    }
    if (tipConfig.tipCodeBlock?.type === 'selected') {
        code = selectCode || '';
        lang = selectLanguage || '';
    }
    // 如果选中代码为空 需要判断是否还展示tip
    return (code || tipConfig.tipCodeBlock?.showTipWhileEmpty)
        ? `${tipConfig.tip || ''} \n\`\`\`${lang || ''}\n${code || ''}\n\`\`\`\n`
        : undefined;
};

export const getPluginTipConfig = (
    pluginName: string,
    capability: string,
    agent: AgentListWithId[]
): PluginTipConfig => {
    return agent
        .find(item => item?.name === pluginName)
        ?.capabilities
        ?.find(item => item.name === (capability || 'fallback'))
        ?.tipConfig || {
        tipCodeBlock: {
            type: 'none',
            showTipWhileEmpty: false,
        },
        tip: '',
    };
};
