import {ContextType, KnowledgeList} from '@shared/protocols';
import {DrawElement} from '@comate/plugin-shared-internals';
import {compact} from 'lodash';
import {getCodeChunkFromFilePath, getCurrentFileRelativePath, partitionPathsByType} from '@/utils/files';
import {Position} from '@/common/types';
export interface SelectedKnowledge {
    id: string;
    name: string;
    type: ContextType;
    content?: string;
    contentStart?: Position;
    contentEnd?: Position;
    path?: string;
}
// 获取文件和文件夹的上下文 文件获取详细内容，文件夹仅仅获取相对路径
const getFileAndFolderContext = async (paths: string[]): Promise<SelectedKnowledge[]> => {
    const [folders, files] = partitionPathsByType(paths);
    const folderContexts: SelectedKnowledge[] = folders.map(path => ({
        id: path,
        name: path,
        type: ContextType.FOLDER,
        path,
    }));
    const fileContexts: SelectedKnowledge[] = await Promise.all(files.map(async path => {
        const {content, contentStart, contentEnd} = await getCodeChunkFromFilePath(path);
        return {
            id: path,
            name: path,
            type: ContextType.FILE,
            content,
            contentStart,
            contentEnd,
            path,
        };
    }));
    if (paths.includes('currentFile')) {
        const currentFile = getCurrentFileRelativePath();
        if (currentFile) {
            const {content, contentStart, contentEnd} = await getCodeChunkFromFilePath(currentFile);
            fileContexts.push({
                id: currentFile,
                name: currentFile,
                type: ContextType.CURRENT_FILE,
                content: content,
                contentStart,
                contentEnd,
                path: currentFile,
            });
        }
    }
    if (paths.includes('repo')) {
        folderContexts.push({
            id: 'repo',
            name: '当前代码库',
            type: ContextType.REPO,
        });
    }

    return [
        ...folderContexts,
        ...fileContexts,
    ];
};

const getOthersContext = (knowledges: KnowledgeList[]): SelectedKnowledge[] => {
    return knowledges.map(knowledge => ({
        id: knowledge.id,
        name: knowledge.name,
        type: knowledge.type,
    }));
};
// 为插件侧提供选中知识的上下文 目前支持当前代码库、当前文件、文件、目录、知识集
export const preProcessKnowledge = async (knowledgelist: KnowledgeList[]): Promise<SelectedKnowledge[]> => {
    const paths = knowledgelist.filter(item => item.type === 'FILE').map(item => item.id);
    const knowledges = knowledgelist.filter(item => !['FILE', 'WEB', 'CODE', 'TERMINAL'].includes(item.type));
    const contexts = compact([
        ...(await getFileAndFolderContext(paths)),
        ...getOthersContext(knowledges),
    ]);
    return contexts;
};

export const extractJsxGeneratedContent = (data: DrawElement[]): string => {
    const result = data.map(item => {
        if (item.type === 'code-block') {
            const newTexts: string[] = [];
            if (item.insertToFileData) {
                newTexts.push(item.insertToFileData.newText);
            }
            if (item.replaceToFileData) {
                newTexts.push(item.replaceToFileData.to);
            }
            if (!item.insertToFileData && !item.replaceToFileData) {
                newTexts.push(item.children.toString());
            }
            return `\`\`\`${newTexts.join('\n')}\n\`\`\``;
        }
        else {
            return item;
        }
    });
    return JSON.stringify(result).replace(/\\n/g, '\n');
};
