export const CMD_WEBVIEW_INIT = 'baidu.comate.webviewInit';
export const CMD_LICENSE_EXPIRED = 'baidu.comate.licenseExpired';
export const CMD_BUILTIN_COMMAND = 'baidu.comate.builtinCommand';
export const CMD_COMATE_PLUS_START_SESSION = 'baidu.comate.startComatePlusSession';
export const SHOW_CHAT_PANEL_AND_CMD_COMATE_PLUS_START = 'baidu.comate.showChatPanelAndStartComatePlusSession';
export const CMD_COMATE_PLUS_START_COMMAND = 'baidu.comate.startComatePlusCommand';
export const CMD_COMATE_PLUS_UPDATE_KNOWLEDGE = 'baidu.comate.updateComatePlusKowledge';
export const CMD_PADDLE_CONVERT = 'baidu.comate.paddleCovert';
export const CMD_ADD_CACHE = 'baidu.comate.addCache';
export const CMD_GET_CACHE = 'baidu.comate.getCache';

export const CMD_COMATE_PLUS_CREATE_PROMPTTEMPLATE = 'baidu.comate.promptTemplate.create';
export const CMD_COMATE_PLUS_EDIT_PROMPTTEMPLATE = 'baidu.comate.promptTemplate.edit';
export const CMD_COMATE_PLUS_DELETE_PROMPTTEMPLATE = 'baidu.comate.promptTemplate.delete';
