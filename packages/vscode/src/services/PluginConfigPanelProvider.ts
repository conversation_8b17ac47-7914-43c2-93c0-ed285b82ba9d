/* eslint-disable complexity */
/* eslint-disable max-len */
import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import axios from 'axios';
import {EventMessage} from '@shared/protocols';
import {FeatureName, isFeatureVisible} from '@shared/utils/features';
import {VPC_PLUGIN_CONFIG_LINK} from '@shared/protocols';
import {isSaaS} from '@/utils/features';
import {L10n} from '@/common/L10nProvider/L10n';
import {OpenPlatformText} from '@/common/L10nProvider/constants';
import {BASE_WEB_HOST} from '@/constants';
import {iocContainer} from '@/iocContainer';
import {getHtmlForWebview} from '../utils/getHtmlForWebview';
import {normalizeColorTheme} from '../utils/normalizeColorTheme';
import {getExtensionContextAsync} from '../utils/extensionContext';
import {ChatViewProvider} from './ChatViewProvider';
import {UserService} from './UserService';
import {ComatePlusChatSession} from './ComatePlusChatSession';
import {ConfigKey, VSCodeConfigProvider} from './ConfigProvider';
import {CustomizeProvider} from './CustomizeProvider';
import {PartialPrivatizationProvider} from './PartialPrivatizationProvider';

interface MessageFromWebview {
    name: string;
    data: unknown;
    messageId?: string;
}

@injectable()
export class PluginConfigPanelProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private panel: vscode.WebviewPanel | undefined;

    constructor(
        @inject(UserService) private readonly userService: UserService,
        @inject(ComatePlusChatSession) private readonly chatSession: ComatePlusChatSession,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider
    ) {
        if (isFeatureVisible(FeatureName.EXTENSION_PLATFORM)) {
            this.disposables.push(
                vscode.commands.registerCommand(
                    'baidu.comate.openPluginConfigPanel',
                    this.reveal,
                    this
                )
            );
        }
    }

    async reveal(toKnowledge?: boolean) {
        this.chatViewProvider.sendDataToWebview(EventMessage.OpenPluginConfigPanelEvent);
        if (isSaaS) {
            const partialPrivatizationProvider = iocContainer.get(PartialPrivatizationProvider);
            if (partialPrivatizationProvider.isEnable) {
                vscode.env.openExternal(
                    vscode.Uri.parse(partialPrivatizationProvider.privateHost + VPC_PLUGIN_CONFIG_LINK)
                );
                return;
            }
        }
        if (!this.panel) {
            this.panel = vscode.window.createWebviewPanel(
                'baidu.comate.pluginConfigPanel',
                L10n.t(OpenPlatformText.TITLE),
                vscode.ViewColumn.One,
                {
                    enableScripts: true,
                    retainContextWhenHidden: true,
                }
            );
            this.disposables.push(
                this.panel,
                this.panel.onDidDispose(() => {
                    this.panel = undefined;
                })
            );
            const [name] = await this.userService.getCurrentUser();
            const license = isSaaS ? this.configProvider.getConfig(ConfigKey.Key) : '';
            this.disposables.push(
                vscode.window.onDidChangeActiveColorTheme(theme => {
                    const normalizedTheme = normalizeColorTheme(theme.kind);
                    this.panel?.webview.postMessage({event: 'changeTheme', data: normalizedTheme});
                })
            );
            const initialTheme = normalizeColorTheme(vscode.window.activeColorTheme.kind);
            const customizeProvider = iocContainer.get(CustomizeProvider);
            const isCustomizeUser = customizeProvider.isCustomizeUser();
            const customizeService = isCustomizeUser ? customizeProvider.getCustomizeServiceConfig() : undefined;
            const host = isSaaS
                ? `${BASE_WEB_HOST}/cop`
                : 'https://comate.baidu-int.com/cop';
            const username = isSaaS ? license : name;
            const tab = toKnowledge || customizeService ? 'knowledge' : 'configSet';
            const url =
                `${host}/${tab}/list?client=vscode&license=${license}&username=${username}&cloudUsername=${name}&theme=${initialTheme}${
                    customizeService ? `&mixed=true&customizeService=${customizeService}` : ''
                }`;
            const extra = `
                <iframe
                    id="embedded-plugin-config"
                    src="${url}"
                    style="width: 100%; height: 100%; border: none;"
                    sandbox="allow-same-origin allow-pointer-lock allow-scripts allow-downloads allow-forms"
                    allow="clipboard-read; clipboard-write"
                ></iframe>
                <script>
                    const vscode = acquireVsCodeApi();
                    window.addEventListener('message', (e) => {
                        const frame = document.getElementById('embedded-plugin-config');
                        if (e.data.event === 'changeTheme') {
                            frame.contentWindow.postMessage({'name': 'changeTheme', 'data': e.data.data}, '*');
                        }
                        else if (e.data.name === 'keydown') {
                            window.dispatchEvent(new KeyboardEvent('keydown', e.data.data));
                        }
                        else if (e.data.name === 'proxyRequestResponse') {
                            frame.contentWindow.postMessage(e.data, '*');
                        }
                        else {
                            vscode.postMessage(e.data);
                        }
                    });
                    function execCommand(data) {
                        const frame = document.getElementById('embedded-plugin-config');
                        frame.contentWindow.postMessage({'name': 'execCommand', 'data': data}, '*');
                    }
                    for (const command of ['copy', 'paste', 'cut']) {
                        document.addEventListener(command, (e) => {
                            execCommand(command);
                        });
                    }
                    document.addEventListener('selectstart', (e) => {
                        execCommand('selectAll');
                        e.preventDefault();
                    });
                    window.addEventListener('keydown', (e) => {
                        if (e.key === 'z' && e.metaKey) {
                            execCommand(e.shiftKey ? 'redo' : 'undo');
                        }
                    });
                </script>
            `;
            this.panel.webview.html = getHtmlForWebview(undefined, L10n.t(OpenPlatformText.WEBVIEW_TITLE), extra);
            const context = await getExtensionContextAsync();
            this.panel.iconPath = vscode.Uri.joinPath(
                context.extensionUri,
                'assets',
                'icon.png'
            );
            this.panel.webview.onDidReceiveMessage(async (message: MessageFromWebview) => {
                if (message.name === 'copy') {
                    await vscode.env.clipboard.writeText(String(message.data));
                }
                else if (message.name === 'updateConfigSet') {
                    this.chatSession.updateWebviewData();
                }
                else if (message.name === 'openExternal') {
                    if (typeof message.data === 'string') {
                        vscode.env.openExternal(vscode.Uri.parse(message.data));
                    }
                }
                else if (message.name === 'proxyRequest') {
                    let formData = null;
                    const {file, ...other} = message.data as any;
                    if (file) {
                        const {name, type, data} = file;
                        formData = new FormData();
                        formData.append('file', new Blob([new Uint8Array(data)], {type}), name);
                        formData.append('knowledgeName', name);
                    }
                    axios
                        .request({...other, data: formData || other.data})
                        .then(
                            res => {
                                this.panel?.webview.postMessage({
                                    name: 'proxyRequestResponse',
                                    messageId: message.messageId,
                                    data: res.data,
                                    status: 'success',
                                });
                            }
                        )
                        .catch((e: any) => {
                            this.panel?.webview.postMessage({
                                name: 'proxyRequestResponse',
                                messageId: message.messageId,
                                error: e?.message,
                                status: 'error',
                            });
                        });
                }
            });
        }
        this.panel.reveal();
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
