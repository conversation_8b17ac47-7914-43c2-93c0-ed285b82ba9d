import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import {stubTrue} from 'lodash';
import {TYPES} from '@/inversify.config';
import {RegisteredCommand} from '@/constants';
import {L10n} from '@/common/L10nProvider/L10n';
import {InlineChatTrialProviderText} from '@/common/L10nProvider/constants';
import {isPoc} from '@/utils/features';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {CodeLensDisplayMode, CodelensConfig, ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {DiffProvider} from '../DiffProvider';
import {ILicenseController} from '../LicenseController/types';
import {CodeLensProvider} from '../FoldedCodeLensProvider';
import {ChatBaseProvider} from '../ChatBaseProvider';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';

/**
 * CodeLens里增加开始对话的引导，提示对话的打开频次
 */
@injectable()
export class ChatTrialProvider extends ChatBaseProvider implements CodeLensProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'javascript',
        'typescript',
        'javascriptreact',
        'jsx',
        'typescriptreact',
        'cpp',
        'c',
    ];
    private disposables: vscode.Disposable[] = [];
    onDidChangeCodeLenses?: vscode.Event<void> | undefined;
    _onDidChange = new vscode.EventEmitter<vscode.Uri>();
    onDidChange: vscode.Event<vscode.Uri> = this._onDidChange.event;

    constructor(
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(TYPES.ILicenseController) private readonly licenseController: ILicenseController,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider
    ) {
        super(diffProvider);
        this.disposables.push(
            vscode.languages.registerCodeLensProvider(
                ChatTrialProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this
            )
        );

        if (!isPoc) {
            this.disposables.push(
                vscode.commands.registerCommand(
                    RegisteredCommand.showInlineChatAndSelectCode,
                    (document: vscode.TextDocument, range: vscode.Range, ...args) => {
                        const editor = vscode.window.activeTextEditor;
                        if (editor?.document === document) {
                            editor.selection = new vscode.Selection(range.start, range.end);
                            vscode.commands.executeCommand(RegisteredCommand.showInlineChat, ...args);
                        }
                    }
                )
            );
        }
    }

    async provideCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();
        const isInDIff = this.diffProvider.isInDiff(document.uri.fsPath);
        if (codelensDisplayMode !== CodeLensDisplayMode.TextTitle) {
            return [];
        }
        if (isInDIff) {
            return [];
        }
        const codeLens = await this.computeCodeLenses(document);
        return this.tmpCodeLensProvider.filterOverlapCodeLenses(document, codeLens);
    }

    async computeCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensConfig = this.configProvider.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        if (codelensConfig?.enableChatPanelShortcut !== true || !this.licenseController.hasLicense && isPoc) {
            return [];
        }
        const codeLens = this.treeSitterProvider.getFunctionCodeLens(
            document,
            stubTrue,
            (document: vscode.TextDocument, range: vscode.Range) => {
                return {
                    title: L10n.t(InlineChatTrialProviderText.CODELENS_TITLE),
                    tooltip: '使用 Comate 进行编辑',
                    command: RegisteredCommand.showInlineChatAndSelectCode,
                    arguments: [document, range],
                };
            }
        );
        return codeLens;
    }

    dispose() {
        // 清理所有事件监听器
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];

        // 清理EventEmitter
        this._onDidChange.dispose();

    }
}
