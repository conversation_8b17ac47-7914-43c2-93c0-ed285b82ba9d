import path from 'path';
import os from 'os';
import {spawnSync} from 'child_process';
import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import {
    LanguageClient,
    LanguageClientOptions,
    ServerOptions,
} from 'vscode-languageclient/node';
import axios, {AxiosResponse} from 'axios';
import {compareVersions} from 'compare-versions';
import {ensureFilePathExists, isFileExist} from '@/utils/fs';
import {registerOutputChannel} from '../outputChannelRegistry';
import {UserService} from '../UserService';
interface checkUser {
    exists: boolean;
}

interface getVersion {
    version: string;
}

@injectable()
export class CustomLspProvider implements vscode.Disposable {
    client: LanguageClient | undefined;
    // eslint-disable-next-line
    private readonly comateGoLanguageBinPath = path.join(os.homedir(), '.comate-engine/bin/comate-go-language-server');

    constructor(@inject(UserService) private readonly userService: UserService) {
    }

    async requestIsWhiteListUser(): Promise<boolean> {
        const [userName] = await this.userService.getCurrentUser();
        const payload = {
            userName: userName,
        };

        try {
            const res = await axios.post<typeof payload, AxiosResponse<checkUser>>(
                'http://10.143.161.42:8001/check_user',
                payload,
                {timeout: 2 * 1000}
            );

            if (res.status !== 200) {
                return false;
            }
            return res.data.exists;
        }
        catch (e) {
            return false;
        }
    }

    async downloadComateLanBinPath() {
        const platform = os.platform();
        const arch = os.arch();
        const comateLanguageBinName = `comate-go-language-server-${platform}-${arch}`;
        const cmd =
            `rm -rf ${this.comateGoLanguageBinPath} && wget http://10.108.67.29:8119/home/<USER>/wbox/comate/output/${comateLanguageBinName} -O ${this.comateGoLanguageBinPath} && chmod +x ${this.comateGoLanguageBinPath}`;
        const downloadSpawn = await spawnSync(cmd, {shell: true});
        if (downloadSpawn.status) {
            // eslint-disable-next-line
            const errorInfo = downloadSpawn.stderr.toString();
            // eslint-disable-next-line
            console.log(errorInfo);
            return false;
        }
        return true;
    }

    async checkVersion() {
        const [userName] = await this.userService.getCurrentUser();
        const payload = {
            userName: userName,
        };

        try {
            const res = await axios.post<typeof payload, AxiosResponse<getVersion>>(
                'http://10.143.161.42:8001/get_version',
                {timeout: 2 * 1000}
            );

            if (res.status !== 200) {
                return false;
            }

            const spawnRes = await spawnSync(this.comateGoLanguageBinPath, ['version'], {shell: true});
            if (!spawnRes.status) {
                const versionInfo = spawnRes.stdout.toString().trim().split(' ');

                const localVersion = versionInfo[versionInfo.length - 1].trim();
                const latestVersion = res.data.version;
                return (compareVersions(localVersion, latestVersion) < 0);
            }
        }
        catch (e) {
            return true;
        }
        return false;
    }

    async start() {
        // 白名单鉴权
        if (!(await this.requestIsWhiteListUser())) {
            return;
        }
        // 确保路径存在
        if (!(await ensureFilePathExists(this.comateGoLanguageBinPath))) {
            return;
        }

        const fileExist = await isFileExist(this.comateGoLanguageBinPath);
        const needUpgrade = await this.checkVersion();

        // bin存在且版本落后或者bin不存在就更新
        // 更新失败退出
        if ((fileExist && needUpgrade) || !fileExist) {
            if (!(await this.downloadComateLanBinPath())) {
                return;
            }
        }

        this.client = new LanguageClient(
            'go',
            'comate-go-language-server',
            {
                command: this.comateGoLanguageBinPath,
                args: ['-mode=stdio'],
                options: {env: process.env},
            } as ServerOptions,
            {
                documentSelector: [{
                    language: 'go',
                    scheme: 'file',
                }],
                uriConverters: {
                    // Apply file:/// scheme to all file paths.
                    code2Protocol: (uri: vscode.Uri): string =>
                        (uri.scheme ? uri : uri.with({scheme: 'file'})).toString(),
                    protocol2Code: (uri: string) => vscode.Uri.parse(uri),
                },
            } as LanguageClientOptions
        );

        registerOutputChannel(this.client.outputChannel);
        this.client.start();
    }
    dispose() {
        this.client?.stop();
    }
}
