import {inject, injectable} from 'inversify';
import {Feature} from '@shared/protocols';
import {getUserConfig} from '@/api';
import {isInternal, isSaaS} from '@/utils/features';
import {iocContainer} from '@/iocContainer';
import {VSCodeConfigProvider} from './ConfigProvider';
import {PartialPrivatizationProvider} from './PartialPrivatizationProvider';

// TODO 这应该是比较root位置的一个class，可能需要多个其他实例计算出结果
@injectable()
export class FeatureFlags {
    private readonly cache = new Map<Feature, boolean>();
    private readonly listeners: any[] = [];
    constructor(@inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider) {
    }

    start() {
        if (isSaaS) {
            const partialPrivatizationProvider = iocContainer.get(PartialPrivatizationProvider);
            partialPrivatizationProvider.onConfigUpdated(e => {
                this.cache.set(Feature.EnableIAPI, !e?.status);
                this.emit();
            });
        }
    }

    async hasAccessTo(feature: Feature, ignoreCache = false) {
        // 厂内开关默认先开全部
        if (isInternal && feature !== Feature.ComatePair) {
            return true;
        }

        if (!ignoreCache && this.cache.has(feature)) {
            return this.cache.get(feature);
        }
        const access = await this.checkAccess(feature).catch(() => undefined);
        if (typeof access === 'boolean') {
            this.cache.set(feature, access);
            // 当缓存更新时，通知其他模块
            this.emit();
        }
        return access;
    }

    on(cb: any) {
        this.listeners.push(cb);
    }

    emit() {
        this.listeners.forEach(cb => cb(Object.fromEntries(this.cache) as Record<Feature, boolean>));
    }

    private async checkAccess(feature: Feature) {
        switch (feature) {
            case Feature.ComatePair: {
                return false;
            }
            case Feature.SaaSV0301: {
                if ($features.PLATFORM === 'poc') {
                    return false;
                }
                const license = this.configProvider.getLicense();
                const remoteConfig = await getUserConfig(license);
                return remoteConfig?.isEnableAutoworkAndPlus ?? false;
            }
            case Feature.EnableIAPI: {
                if ($features.PLATFORM === 'saas') {
                    const provider = iocContainer.get(PartialPrivatizationProvider);
                    return !provider.isEnable;
                }
                return true;
            }
            case Feature.ComatePlus: {
                return true;
            }
            default: {
                return false;
            }
        }
    }
}
