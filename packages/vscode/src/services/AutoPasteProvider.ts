import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import {error, info, trace} from '@/common/outputChannel';
import {stripMarkdownCodeBlock} from '@/utils/common';
import {acceptCode, showCode} from '@/api';
import {throttle} from '@/utils/throttle';
import {buildParams} from '@/common/Fetcher';
import {getDeviceUUIDThrottled} from '@/utils/deviceUUID';
import {DynamicWordDiffProvider} from './DynamicWordDiffProvider';
import {UserService} from './UserService';
import {requestAutoPasteStream} from './AutoComateChatSession/api';
import {VSCodeConfigProvider} from './ConfigProvider';
import {LogCategory, LogUploaderProvider} from './LogUploaderProvider';

const BUILTIN_PASTE_COMMAND = 'editor.action.clipboardPasteAction';

const MAX_PREFIX_LINES = 100;
const MAX_SUFFIX_LINES = 100;

const MIN_PREFIX_LINES = 10;
const MIN_SUFFIX_LINES = 10;

// 一期只支持粘贴的代码小于 5 行
const MAX_LINES_TRIGGER_AUTO_PASTE = 5;

function isValidPasteText(text: string) {
    if (text.length < 20) {
        return false;
    }
    // 如果只一个简单的变量，就别自动粘贴，太频繁了
    if (/^[a-zA-Z0-9_]+$/.test(text)) {
        return false;
    }
    return true;
}

function checkPasteContext(document: vscode.TextDocument, range: vscode.Range) {
    const text = document.getText(range);
    return text.trim() !== ''
        && range.end.line - range.start.line < MAX_LINES_TRIGGER_AUTO_PASTE
        && range.start.line >= MIN_PREFIX_LINES
        && range.end.line <= document.lineCount - MIN_SUFFIX_LINES;
}

async function getClipboardText() {
    const clipboardText = await vscode.env.clipboard.readText();
    return clipboardText;
}

const getClipboardTextThrottled = throttle(getClipboardText, 200);

const stripTextIndent = (text: string) => text.split('\n').map(v => v.trim()).join('\n');

@injectable()
export class AutoPasteProvider {

    constructor(
        @inject(DynamicWordDiffProvider) private readonly dynamicWordDiffProvider: DynamicWordDiffProvider,
        @inject(UserService) private readonly userService: UserService,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(LogUploaderProvider) private readonly loggerProvider: LogUploaderProvider
    ) {

    }

    start(context: vscode.ExtensionContext) {
        // 没开启就不劫持粘贴命令
        if (this.configProvider.getEnableAutoPaste() !== true) {
            return;
        }

        this.loggerProvider.logUserAction({
            category: LogCategory.AutoPaste,
            action: 'init',
        });

        // 低版本的 vscode 似乎劫持不到粘贴事件
        // 先在 iCoding 上实验下通过监听文档内容修改来判断粘贴事件是否可行
        // 目前看影响比较大的可能是频繁地触发获取粘贴内容
        if (vscode.env.appName === 'iCoding') {
            context.subscriptions.push(
                vscode.workspace.onDidChangeTextDocument(e => {
                    this.onPasteChangeTrigger(e);
                })
            );
            return;
        }

        this.overrideBuiltinPasteCommand(context);
    }

    private overrideBuiltinPasteCommand(context: vscode.ExtensionContext) {
        // eslint-disable-next-line @typescript-eslint/init-declarations
        let clipboardDisposable: vscode.Disposable | undefined;

        const onClipboardCopy = async () => {
            clipboardDisposable?.dispose();
            const editor = vscode.window.activeTextEditor;
            const selectionBeforePaste = editor?.selection;

            await vscode.commands.executeCommand(BUILTIN_PASTE_COMMAND);
            clipboardDisposable = vscode.commands.registerCommand(BUILTIN_PASTE_COMMAND, onClipboardCopy);
            context.subscriptions.push(clipboardDisposable);

            this.onPasteCommandTrigger(selectionBeforePaste);
        };
        clipboardDisposable = vscode.commands.registerCommand(BUILTIN_PASTE_COMMAND, onClipboardCopy);
    }

    // 通过监听内容变化来判断是否粘贴了代码，可能不准
    private async onPasteChangeTrigger(e: vscode.TextDocumentChangeEvent) {
        if (this.configProvider.getEnableAutoPaste() !== true) {
            return;
        }
        const editor = vscode.window.activeTextEditor;
        if (!editor || editor.document.uri.toString() !== e.document.uri.toString()) {
            return;
        }
        if (e.contentChanges.length !== 1) {
            return;
        }
        const change = e.contentChanges[0];
        if (!isValidPasteText(change.text.trim())) {
            return;
        }
        // 减少触发次数，因为每次触发都需要获取 clipboard 内容
        if (!change.range.isEmpty) {
            return;
        }

        const {start} = change.range;
        const lineRangeBeforeStart = new vscode.Range(start.line, 0, start.line, start.character);
        const offset = e.document.offsetAt(start);
        const end = e.document.positionAt(offset + change.text.length);
        // 判断开头是否为空
        if (e.document.getText(lineRangeBeforeStart).trim() !== '') {
            return;
        }
        this.checkAndRunAutoPaste(editor, new vscode.Range(start, end));
    }

    // 通过劫持粘贴命令来触发
    private async onPasteCommandTrigger(selectionBeforePaste?: vscode.Selection) {
        if (this.configProvider.getEnableAutoPaste() !== true) {
            return;
        }
        const editor = vscode.window.activeTextEditor;
        const selectionAfterPaste = editor?.selection;
        // 粘贴完光标应该在最后，就是selection为空，要不然可能是粘贴到搜索框里，然后选中代码了
        if (!editor || !selectionBeforePaste || !selectionAfterPaste || !selectionAfterPaste.isEmpty) {
            return;
        }
        // 粘贴完光标还在原位置也不对
        if (selectionBeforePaste.isEmpty && selectionAfterPaste.contains(selectionBeforePaste)) {
            return;
        }

        this.checkAndRunAutoPaste(
            editor,
            new vscode.Range(selectionBeforePaste.start, selectionAfterPaste.end)
        );
    }

    private async checkAndRunAutoPaste(
        editor: vscode.TextEditor,
        pasteRange: vscode.Range
    ) {
        const startTime = performance.now();
        if (!checkPasteContext(editor.document, pasteRange)) {
            return;
        }

        // 如果上一个还是 active 就不触发下一个，减少点打扰
        if (this.dynamicWordDiffProvider.conflictWithCurrentActiveDiff()) {
            return;
        }

        const clipboardText = await getClipboardTextThrottled() ?? '';
        // 如果只一个简单的变量，就别自动粘贴，太频繁了
        if (!isValidPasteText(clipboardText)) {
            return;
        }

        // 触发自动粘贴给模型时只取整行不区分字符位置
        const range = new vscode.Range(
            new vscode.Position(pasteRange.start.line, 0),
            editor.document.lineAt(pasteRange.end.line).range.end
        );

        // 重新拿一下，经过了异步操作，可能已经变了
        const currentText = editor.document.getText(range);
        if (stripTextIndent(clipboardText) !== stripTextIndent(currentText)) {
            return;
        }
        this.autoPaste(editor, range, startTime);
        this.onTrigger(clipboardText, startTime);
    }

    private async autoPaste(
        editor: vscode.TextEditor,
        range: vscode.Range,
        startTime: number
    ) {
        const options = {onCancel: (reason: string) => this.onCancel(reason)};
        const {showDiff, cancel} = this.dynamicWordDiffProvider.openWordDiff(editor, range, options);
        try {
            // 15s 后接口还没回来就取消
            const timer = setTimeout(() => cancel('request timeout'), 15 * 1000);
            const params = await this.buildParams(editor.document, range);
            const res = await requestAutoPasteStream(params);
            if (res.code !== 200) {
                throw new Error(res.message);
            }
            const autoPasteRes = stripMarkdownCodeBlock(res.data.content, editor.document.languageId);
            clearTimeout(timer);
            this.dynamicWordDiffProvider.onAccept(
                () => this.onAcceptAutoPaste(res.data.uuid, autoPasteRes)
            );
            trace('AutoPaste show diff: ', autoPasteRes);
            const success = await showDiff(autoPasteRes);
            success && this.onShowDiff(autoPasteRes, res.data.uuid, startTime);
        }
        catch (e: any) {
            cancel('request error: ' + e.message);
        }
    }

    private async buildParams(
        document: vscode.TextDocument,
        range: vscode.Range
    ) {
        const paramsRes = await buildParams(document, range.start, this.userService);
        if (paramsRes.type !== 'success') {
            throw new Error(paramsRes.reason);
        }
        const device = await getDeviceUUIDThrottled() ?? '';
        const {repo, username, path, pluginVersion} = paramsRes.value;

        return {
            device,
            repoName: repo,
            ide: 'vscode' as const,
            loginName: username,
            path,
            pluginVersion,
            ideVersion: vscode.version,
            ...this.buildCodeContext(document, range),
        };
    }

    private buildCodeContext(
        document: vscode.TextDocument,
        range: vscode.Range
    ) {
        const fullText = document.getText();
        const lines = fullText.split('\n');
        const codeStartLine = range.start.line > MAX_PREFIX_LINES
            ? range.start.line - MAX_PREFIX_LINES
            : 0;
        const code = lines
            .slice(codeStartLine, range.end.line + MAX_SUFFIX_LINES + 1)
            .join('\n');
        return {
            code,
            codeStartLine: codeStartLine + 1,
            pasteStartLine: range.start.line + 1,
            pasteEndLine: range.end.line + 1,
        };
    }

    private onTrigger(pasteText: string, startTime: number) {
        this.loggerProvider.logUserAction({
            category: LogCategory.AutoPaste,
            content: pasteText,
            action: 'trigger',
            label: (performance.now() - startTime).toString(),
        });
    }

    private onCancel(reason: string) {
        if (reason.includes('error')) {
            error('AutoPaste Error: ', reason);
        }
        else {
            info('AutoPaste Canceled: ', reason);
        }
        this.loggerProvider.logUserAction({
            category: LogCategory.AutoPaste,
            content: reason,
            action: 'cancel',
        });
    }

    private onAcceptAutoPaste(uuid: string, res: string) {
        acceptCode({
            uuid,
            accepted: true,
            content: res,
            generatedContent: res,
        });
        this.loggerProvider.logUserAction({
            category: LogCategory.AutoPaste,
            content: res,
            action: 'accept',
        });
    }

    private onShowDiff(res: string, uuid: string, startTIme: number) {
        showCode(uuid);
        this.loggerProvider.logUserAction({
            category: LogCategory.AutoPaste,
            label: (performance.now() - startTIme).toString(),
            content: res,
            action: 'showDiff',
        });
    }
}
