import {DocumentUri, Location, Position, Range, SymbolKind} from 'vscode-languageserver-types';

export interface SymbolLocationLink {
    name: string;
    // symbol 使用位置
    originRange: Range;
    originUri: DocumentUri;
    originStartOffset: number;
    originEndOffset: number;
    targetRange: Range;
    targetUri: DocumentUri;
}

export interface ILocationLinkResolver {
    resolveDefinition(file: string, range: Range): Promise<Location | undefined>;
    resolveTypeDefinition(file: string, range: Range): Promise<Location | undefined>;
}

export interface SymbolUsage {
    language?: undefined;
    name: string;
    range: Range;
    startOffset: number;
    endOffset: number;
}

export interface GoSymbolUsage {
    language: 'go';
    // go 包名称
    packageIdentifier?: SymbolUsage;
    // go 包中使用的类型
    typeIdentifier: SymbolUsage;
    kind?:
        // go 方法的接收者
        | 'receiverType'
        // 函数参数类型
        | 'parameterType'
        // 函数返回值类型
        | 'returnType';
}

export type SymbolUsageByLanguage = GoSymbolUsage | SymbolUsage;

export interface ISymbolUsageFinder {
    findImportedSymbols(file: string): Promise<SymbolUsage[]>;
    findAssignedVariables(file: string): Promise<SymbolUsage[]>;
    findOnFunction(file: string, position: Position): Promise<SymbolUsageByLanguage[]>;
}

export interface ISymbolLinkResolver {
    resolveImports(file: DocumentUri, maxCount: number): Promise<SymbolLocationLink[]>;
    resolveAssignments(file: DocumentUri, maxCount: number): Promise<SymbolLocationLink[]>;
}

export interface StructuredSignature {
    // 符号名称
    name: string;
    // 符号类型
    kind: SymbolKind | null;
    // 符号签名
    content: string;
    children: Array<Omit<StructuredSignature, 'children'>>;
}

export interface ISymbolSignatureReader {
    checkSupport(file: string): boolean;
    getSignature(file: string, symbolRange: Range, identifier?: string): Promise<StructuredSignature | undefined>;
}
