import * as vscode from 'vscode';
import {get} from 'lodash';
import {StructuredSignature} from './types';

interface SignatureStyle {
    // 一个如 interface/class/struct 等这样的复杂类型的签名，把属性包起来的 block 的开始和结束符号
    blockStart: string;
    blockEnd: string;
}

const LANGUAGE_SIGNATURE_STYLE: Record<string, SignatureStyle> = {
    python: {
        blockStart: '',
        blockEnd: '',
    },
    cpp: {
        blockStart: '{',
        blockEnd: '}',
    },
    default: {
        blockStart: '',
        blockEnd: '',
    },
};

function getIndent(language: string | undefined) {
    if (language === 'python') {
        // NOTE: 注意由于 python 的签名现在是从 lsp 的 hover 信息获取的，会是 (class) AAA, (method) bbb 这样的结构，本身就不是合法的 python，缩进也无所谓了。
        // 因此在设计上，考虑是否重构成让每个 signature reader 提供字符串化签名的方式，而不是下方的 stringifySignature 里统一处理？
        return '';
    }
    const editorOptions: vscode.TextEditorOptions = vscode.window.activeTextEditor?.options ?? {
        tabSize: 4,
        insertSpaces: true,
    };
    const indent = (editorOptions.insertSpaces && editorOptions.tabSize !== undefined)
        ? ' '.repeat(editorOptions.tabSize as number)
        : '\t';
    return indent;
}

export function stringifySignature(language: string | undefined, signature: StructuredSignature): string {
    if (!signature.children.length) {
        return signature.content;
    }
    const {blockStart, blockEnd} = get(
        LANGUAGE_SIGNATURE_STYLE,
        language ?? 'default',
        LANGUAGE_SIGNATURE_STYLE.default
    );
    const indent = getIndent(language);
    const signatureStart = signature.content + (blockStart ? ` ${blockStart}` : '');
    const children = signature.children.map(item => {
        const lines = item.content.split('\n');
        const indentLines = lines.map(line => `${indent}${line}`);
        return indentLines.join('\n');
    });
    const signatureEnd = blockEnd;
    const segments = [
        signatureStart,
        ...children,
        signatureEnd,
    ];
    return segments.join('\n');
}
