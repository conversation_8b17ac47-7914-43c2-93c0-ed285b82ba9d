import {timeoutSafe} from '@/utils/promise';
import {inferLanguage} from '@/utils/languages';
import logger from '@/common/consoleLogger';
import {ILocationLinkResolver, ISymbolUsageFinder, SymbolLocationLink} from './types';
import {ISymbolLinkResolver} from './types';

const THREE_SECONDS = 1000 * 3;

export class SymbolLinkResolver implements ISymbolLinkResolver {
    constructor(
        private readonly symbolUsageResolver: ISymbolUsageFinder,
        private readonly locationLinkResolver: ILocationLinkResolver
    ) {}

    async resolveImports(file: string, maxCount: number): Promise<SymbolLocationLink[]> {
        const t0 = performance.now();
        const importedSymbols = await this.symbolUsageResolver.findImportedSymbols(file);
        const t1 = performance.now();
        logger.debug(`(context): ${importedSymbols.length} imported symbols found in ${(t1 - t0).toFixed(2)}ms`);
        const links: SymbolLocationLink[] = [];
        const language = inferLanguage(file);
        for (const symbol of importedSymbols.slice(0, maxCount)) {
            const locationPromise = language === 'js'
                ? this.locationLinkResolver.resolveDefinition(file, symbol.range)
                : this.locationLinkResolver.resolveTypeDefinition(file, symbol.range);
            const location = await timeoutSafe(
                locationPromise,
                THREE_SECONDS,
                undefined
            );
            if (location) {
                const locationLink: SymbolLocationLink = {
                    name: symbol.name,
                    originUri: file,
                    originRange: symbol.range,
                    originStartOffset: symbol.startOffset,
                    originEndOffset: symbol.endOffset,
                    targetUri: location.uri,
                    targetRange: location.range,
                };
                links.push(locationLink);
            }
        }
        return links;
    }

    async resolveAssignments(file: string, maxCount: number): Promise<SymbolLocationLink[]> {
        const t0 = performance.now();
        const assignedVariables = await this.symbolUsageResolver.findAssignedVariables(file);
        const t1 = performance.now();
        logger.debug(`(context): ${assignedVariables.length} assigned variables found in ${(t1 - t0).toFixed(2)}ms`);
        const links: SymbolLocationLink[] = [];
        for (const symbol of assignedVariables.slice(0, maxCount)) {
            const location = await timeoutSafe(
                this.locationLinkResolver.resolveTypeDefinition(file, symbol.range),
                THREE_SECONDS,
                undefined
            );
            if (location) {
                const locationLink: SymbolLocationLink = {
                    name: symbol.name,
                    originUri: file,
                    originRange: symbol.range,
                    originStartOffset: symbol.startOffset,
                    originEndOffset: symbol.endOffset,
                    targetUri: location.uri,
                    targetRange: location.range,
                };
                links.push(locationLink);
            }
        }
        return links;
    }
}
