import {noop, orderBy, reverse, uniqBy} from 'lodash';
import {Range} from 'vscode-languageserver-types';
import {LRUCache} from 'vscode-languageclient';
import {inferLanguage} from '@/utils/languages';
import {WithRequired} from '@/utils/typeUtils';
import logger from '@/common/consoleLogger';
import {IWorkspaceProvider} from '@/common/lsp/types';
import {serializePosition} from './utils';
import {ISymbolLinkResolver, ISymbolSignatureReader, SymbolLocationLink, StructuredSignature} from './types';
import {getEditDistanceScore, getSymbolOffsetScore, getSnippetLengthScore} from './utils';
import {stringifySignature} from './stringifySignature';

interface CrossFileSnippet {
    relevantFile: string;
    snippet: string;
}

interface SymbolDefinition {
    uri: string;
    range: Range;
    signature: StructuredSignature;
}

interface AnalyzedSymbol extends SymbolLocationLink {
    targetDefinition?: SymbolDefinition;
}

interface CacheStructure {
    timestamp: number;
    inFlight: boolean;
    symbols: AnalyzedSymbol[];
}

const SUPPORTED_LANGUAGES = new Set(['python', 'cpp', 'js', 'go']);

export class CrossFileContextAnalyzer {
    private readonly referencedSymbolsCache = new LRUCache<string, CacheStructure>(50);

    constructor(
        private readonly symbolLinkResolver: ISymbolLinkResolver,
        private readonly symbolSignatureReader: ISymbolSignatureReader,
        private readonly analyzerThrottleTime: number,
        private readonly workspaceProvider: IWorkspaceProvider
    ) {}

    async getReferencedSymbolsContextFromCache(file: string, cursorOffset: number): Promise<CrossFileSnippet[]> {
        const referencedSymbols = this.staleWhileRevalidateSymbols(file);
        const results = await this.getRankedSnippets(referencedSymbols, file, cursorOffset);
        return results;
    }

    prepare(file: string) {
        this.staleWhileRevalidateSymbols(file);
    }

    checkSupport(file: string) {
        const language = inferLanguage(file);
        if (!language) {
            return false;
        }
        if (!SUPPORTED_LANGUAGES.has(language)) {
            return false;
        }
        if (language === 'js' && file.includes('node_modules')) {
            return false;
        }
        const isInWorkspace = this.workspaceProvider.workspaceFolders?.some(item => file.startsWith(item.uri));
        return isInWorkspace;
    }

    private async getRankedSnippets(
        symbols: AnalyzedSymbol[],
        filePath: string,
        cursorOffset: number
    ): Promise<CrossFileSnippet[]> {
        const doc = await this.workspaceProvider.openTextDocument(filePath);
        // TODO 这里获取 near cursor text 时读 100 个字符，从后往前取 20 个非空字符
        const startPosition = doc.positionAt(Math.max(cursorOffset - 50, 0));
        const endPosition = doc.positionAt(cursorOffset);
        const nearCursorText = doc.getText({start: startPosition, end: endPosition});

        // 保留光标前 symbols 并去重，重复时保留距离 cursorOffset 最近的那个
        const dedupedSymbols = uniqBy(
            reverse(
                symbols.filter(
                    (item): item is WithRequired<AnalyzedSymbol, 'targetDefinition'> => {
                        return !!item.targetDefinition && item.originEndOffset <= cursorOffset;
                    }
                )
            ),
            item => `${item.targetUri}-${serializePosition(item.targetRange.start)}`
        );

        const language = inferLanguage(filePath);
        const signatureTexts = dedupedSymbols.map(item => {
            const signature = item.targetDefinition.signature;
            const original = stringifySignature(language, signature);
            if (original.length <= 2000) {
                return original;
            }
            const streamlined = this.createStreamlinedSignatureText(
                item.targetDefinition.signature,
                {
                    nearCursorText,
                    language,
                }
            );
            return streamlined;
        });

        const maxSignatureLength = Math.max(...signatureTexts.map(item => item.length));

        const scoredLinks = dedupedSymbols.map((item, idx) => {
            const signatureText = signatureTexts[idx];
            const score = this.scoreReferencedSymbol(
                {...item, signatureText},
                cursorOffset,
                maxSignatureLength
            );
            return {
                ...item,
                score,
                signatureText,
            };
        });
        const sortedLinks = orderBy(scoredLinks, 'score', 'desc');

        const results = sortedLinks.map(item => ({
            snippet: item.signatureText,
            relevantFile: item.targetUri,
        }));
        return results;
    }

    private staleWhileRevalidateSymbols(file: string): AnalyzedSymbol[] {
        const cachedSymbols = this.referencedSymbolsCache.get(file);
        if (cachedSymbols) {
            if (!cachedSymbols.inFlight && Date.now() - cachedSymbols.timestamp > this.analyzerThrottleTime) {
                cachedSymbols.timestamp = Date.now();
                cachedSymbols.inFlight = true;
                this
                    .analyzeReferencedSymbols(file)
                    .then(result => {
                        cachedSymbols.symbols = result;
                    })
                    .catch(noop)
                    .finally(() => {
                        cachedSymbols.inFlight = false;
                    });
            }
            return cachedSymbols.symbols;
        }
        const initial: CacheStructure = {
            timestamp: Date.now(),
            inFlight: true,
            symbols: [],
        };
        this.referencedSymbolsCache.set(file, initial);
        this
            .analyzeReferencedSymbols(file)
            .then(result => {
                initial.symbols = result;
            })
            .catch(noop)
            .finally(() => {
                initial.inFlight = false;
            });
        return initial.symbols;
    }

    private async analyzeReferencedSymbols(file: string): Promise<AnalyzedSymbol[]> {
        const t0 = performance.now();
        const [imports, assignments] = await Promise.all([
            this.symbolLinkResolver.resolveImports(file, 30),
            this.symbolLinkResolver.resolveAssignments(file, 70),
        ]);
        const links = [
            ...imports,
            ...assignments,
        ];
        const targetsToResolve = await this.getLinksToResolve(links);
        const t1 = performance.now();
        logger.debug(`(context): ${(t1 - t0).toFixed(2)}ms to resolve symbol links`);

        const fulfilled: SymbolDefinition[] = [];
        for (const item of targetsToResolve) {
            try {
                const signature = await this.symbolSignatureReader.getSignature(
                    item.targetUri,
                    item.targetRange,
                    item.name
                );
                if (signature) {
                    fulfilled.push({
                        uri: item.targetUri,
                        range: item.targetRange,
                        signature,
                    } as SymbolDefinition);
                }
            }
            catch (e) {
                continue;
            }
        }

        const t2 = performance.now();
        logger.debug(`(context): ${(t2 - t1).toFixed(2)}ms to resolve signatures`);

        const targetDefinitionsMap = new Map(fulfilled.map(item => {
            const key = `${item.uri}-${serializePosition(item.range.start)}`;
            return [key, item];
        }));

        const results = links.map(item => {
            const targetDefinition = targetDefinitionsMap.get(
                `${item.targetUri}-${serializePosition(item.targetRange.start)}`
            );
            return {
                ...item,
                targetDefinition,
            };
        });
        return results;
    }

    private createStreamlinedSignatureText(signature: StructuredSignature, {
        nearCursorText,
        language,
    }: {
        nearCursorText: string;
        language?: string;
    }): string {
        const scoredChildren = signature.children.map((item, index) => {
            const score = getEditDistanceScore(item.name, nearCursorText);
            return {score, index};
        });
        const relevantChildren = orderBy(scoredChildren, 'score', 'desc').slice(0, 6);
        const streamlinedSignature = {
            ...signature,
            children: relevantChildren.map(item => signature.children[item.index]),
        };
        const signatureText = stringifySignature(language, streamlinedSignature);
        return signatureText;
    }

    private async getLinksToResolve(locationLinks: SymbolLocationLink[]) {
        const filtered: SymbolLocationLink[] = [];
        for (const item of locationLinks) {
            if (await this.shouldKeepLocationLink(item)) {
                filtered.push(item);
            }
        }
        const dedupedLinks = uniqBy(filtered, item => {
            return `${item.targetUri}-${serializePosition(item.targetRange.start)}`;
        });
        return dedupedLinks;
    }

    private scoreReferencedSymbol(
        link: Required<AnalyzedSymbol> & {signatureText: string},
        cursorOffset: number,
        maxSnippetLength: number
    ) {
        // 引用的文件路径和当前文件路经的编辑距离分数
        const editDistanceScore = getEditDistanceScore(link.originUri, link.targetUri);
        const symbolOffsetScore = getSymbolOffsetScore(link.originStartOffset, cursorOffset);
        const snippetLengthScore = getSnippetLengthScore(link.signatureText, maxSnippetLength);

        return (
            5 * editDistanceScore
            + 3 * symbolOffsetScore
            + 2 * snippetLengthScore
        );
    }

    /**
     * 判断是否要保留一个 Symbol 使用/定义位置关系的链接
     * @param link Symbol 使用/定义的位置信息
     * @returns true 表示保留，false 表示不保留
     */
    // eslint-disable-next-line complexity
    private async shouldKeepLocationLink(link: SymbolLocationLink) {
        if (link.targetUri === link.originUri) {
            return false;
        }
        const language = inferLanguage(link.originUri);
        if (language === 'python') {
            if (link.targetUri.includes('builtins.pyi')) {
                return false;
            }
            if (link.targetUri.includes('typeshed-fallback')) {
                return false;
            }
            // 第三方库，保留
            if (link.targetUri.includes('site-packages')) {
                return true;
            }
            if (link.targetUri.includes('lib/python') || link.targetUri.includes('lib\\python')) {
                return false;
            }
            return true;
        }
        else if (language === 'cpp') {
            if (!link.targetUri.endsWith('.h')) {
                return false;
            }
            const isInWorkspace = this.workspaceProvider.workspaceFolders?.some(
                item => link.targetUri.startsWith(item.uri)
            );
            return isInWorkspace;
        }
        else if (language === 'js') {
            if (link.targetUri.includes('/@types/node/') || link.targetUri.includes('\\@types\\node\\')) {
                return false;
            }
            const isInWorkspace = this.workspaceProvider.workspaceFolders?.some(
                item => link.targetUri.startsWith(item.uri)
            );
            return isInWorkspace;
        }
        else if (language === 'go') {
            if (link.targetUri.includes('libexec')) {
                return false;
            }
            const goRoot = await this.workspaceProvider.getGoRoot(link.originUri);
            if (goRoot && link.targetUri.startsWith(goRoot)) {
                return false;
            }
            return true;
        }
        return true;
    }
}
