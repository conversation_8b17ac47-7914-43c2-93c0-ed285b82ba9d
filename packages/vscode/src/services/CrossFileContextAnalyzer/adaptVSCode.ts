import * as vscode from 'vscode';
import {SymbolKind} from 'vscode-languageserver-types';

const symbolKindMap = {
    [vscode.SymbolKind.File]: SymbolKind.File,
    [vscode.SymbolKind.Module]: SymbolKind.Module,
    [vscode.SymbolKind.Namespace]: SymbolKind.Namespace,
    [vscode.SymbolKind.Package]: SymbolKind.Package,
    [vscode.SymbolKind.Class]: SymbolKind.Class,
    [vscode.SymbolKind.Method]: SymbolKind.Method,
    [vscode.SymbolKind.Property]: SymbolKind.Property,
    [vscode.SymbolKind.Field]: SymbolKind.Field,
    [vscode.SymbolKind.Constructor]: SymbolKind.Constructor,
    [vscode.SymbolKind.Enum]: SymbolKind.Enum,
    [vscode.SymbolKind.Interface]: SymbolKind.Interface,
    [vscode.SymbolKind.Function]: SymbolKind.Function,
    [vscode.SymbolKind.Variable]: SymbolKind.Variable,
    [vscode.SymbolKind.Constant]: SymbolKind.Constant,
    [vscode.SymbolKind.String]: SymbolKind.String,
    [vscode.SymbolKind.Number]: SymbolKind.Number,
    [vscode.SymbolKind.Boolean]: SymbolKind.Boolean,
    [vscode.SymbolKind.Array]: SymbolKind.Array,
    [vscode.SymbolKind.Object]: SymbolKind.Object,
    [vscode.SymbolKind.Key]: SymbolKind.Key,
    [vscode.SymbolKind.Null]: SymbolKind.Null,
    [vscode.SymbolKind.EnumMember]: SymbolKind.EnumMember,
    [vscode.SymbolKind.Struct]: SymbolKind.Struct,
    [vscode.SymbolKind.Event]: SymbolKind.Event,
    [vscode.SymbolKind.Operator]: SymbolKind.Operator,
    [vscode.SymbolKind.TypeParameter]: SymbolKind.TypeParameter,
};

export function adaptSymbolKind(symbolKind: vscode.SymbolKind): SymbolKind {
    return symbolKindMap[symbolKind] || null;
}
