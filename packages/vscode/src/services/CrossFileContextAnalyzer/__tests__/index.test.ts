import {it, describe, expect} from 'vitest';
import {getEditDistanceScore} from '../utils';

describe('getEditDistanceScore', () => {
    it('应符合用最优编辑距离计算的字符串相似度分数规律', () => {
        const score0 = getEditDistanceScore('hello', 'hello');
        const score1 = getEditDistanceScore('HELLO', 'hello');
        const score2 = getEditDistanceScore('hell', 'hello');
        const score3 = getEditDistanceScore('hi', 'hello');
        const score4 = getEditDistanceScore('', 'hello');
        expect(score0).toBe(score1);
        expect(score1).toBeGreaterThan(score2);
        expect(score2).toBeGreaterThan(score3);
        expect(score3).toBeGreaterThan(score4);
    });
});
