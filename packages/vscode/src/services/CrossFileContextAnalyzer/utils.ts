import {levenshteinEditDistance} from 'levenshtein-edit-distance';
import {Position, Range} from 'vscode-languageserver-types';
import {Point} from 'web-tree-sitter';

export function standarizeTreeSitterPosition(pos: Point): Position {
    return {
        line: pos.row,
        character: pos.column,
    };
}

export function standarizeTreeSitterRange(start: Point, end: Point): Range {
    return {
        start: standarizeTreeSitterPosition(start),
        end: standarizeTreeSitterPosition(end),
    };
}

export function getMidPosition(range: Range): Position | undefined {
    if (range.start.line !== range.end.line) {
        // 跨行的范围不支持获取中间位置
        return undefined;
    }
    return {
        line: Math.floor((range.start.line + range.end.line) / 2),
        character: Math.floor((range.start.character + range.end.character) / 2),
    };
}

export function serializePosition(position: Position): string {
    return `L${position.line}C${position.character}`;
}

export function deserializePosition(str: string): Position {
    const [line, character] = str.slice(1).split('C');
    return {
        line: parseInt(line, 10),
        character: parseInt(character, 10),
    };
}

export function serializeRange(range: Range): string {
    return `${serializePosition(range.start)}-${serializePosition(range.end)}`;
}

export function deserializeRange(str: string): Range {
    const [start, end] = str.split('-');
    return {
        start: deserializePosition(start),
        end: deserializePosition(end),
    };
}

/**
 * 通过编辑距离计算两个字符串之间的相似度，编辑距离越小分数则越高。
 *
 * @param source - 源字符串
 * @param target - 目标字符串
 * @returns 相似度分数 0 - 1
 */
export const getEditDistanceScore = (source: string, target: string) => {
    const distance = levenshteinEditDistance(source, target, true);
    const totalLength = source.length + target.length;
    return (totalLength - distance) / totalLength;
};

export const getSymbolOffsetScore = (symbolOffset: number, cursorOffset: number) => {
    return symbolOffset / cursorOffset;
};

export const getSnippetLengthScore = (snippet: string, maxSnippetLength: number) => {
    return (maxSnippetLength - snippet.length) / maxSnippetLength;
};
