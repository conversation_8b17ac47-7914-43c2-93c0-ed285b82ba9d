import * as vscode from 'vscode';
import {Range, Location, Position} from 'vscode-languageserver-types';
import {injectable} from 'inversify';
import {ILocationLinkResolver} from './types';
import {getMidPosition} from './utils';

@injectable()
export class VSCodeLocationLinkResolver implements ILocationLinkResolver {
    async resolveDefinition(file: string, range: Range): Promise<Location | undefined> {
        const uri = vscode.Uri.file(file);
        const midPoint = getMidPosition(range) ?? range.start;
        const location = await this.invokeDefinitionProvider(
            uri,
            'vscode.executeDefinitionProvider',
            midPoint
        );
        return location;
    }

    async resolveTypeDefinition(file: string, range: Range): Promise<Location | undefined> {
        const uri = vscode.Uri.file(file);
        const midPoint = getMidPosition(range) ?? range.start;
        const location = await this.invokeDefinitionProvider(
            uri,
            'vscode.executeTypeDefinitionProvider',
            midPoint
        );
        return location;
    }

    private async invokeDefinitionProvider(uri: vscode.Uri, command: string, position: Position) {
        const location = await vscode.commands.executeCommand<vscode.Location[] | vscode.LocationLink[]>(
            command,
            uri,
            position
        );
        const topLocation = location[0];
        if (!topLocation) {
            return undefined;
        }
        return this.standarizeLocation(topLocation);
    }

    private standarizeLocation(location: vscode.Location | vscode.LocationLink): Location {
        if (location instanceof vscode.Location) {
            return {
                uri: location.uri.fsPath,
                range: location.range,
            };
        }
        return {
            uri: location.targetUri.fsPath,
            range: location.targetRange,
        };
    }
}
