import {Range} from 'vscode-languageserver-types';
import {injectable} from 'inversify';
import {ISymbolSignatureReader, StructuredSignature} from '../types';

@injectable()
export class SymbolSignatureController implements ISymbolSignatureReader {
    private readonly readers: ISymbolSignatureReader[] = [];

    addReader(...reader: ISymbolSignatureReader[]) {
        this.readers.push(...reader);
    }

    checkSupport(file: string): boolean {
        return this.readers.some(reader => reader.checkSupport(file));
    }

    async getSignature(file: string, symbolRange: Range, identifer?: string): Promise<StructuredSignature | undefined> {
        const reader = this.readers.find(reader => reader.checkSupport(file));
        return reader?.getSignature(file, symbolRange, identifer);
    }
}
