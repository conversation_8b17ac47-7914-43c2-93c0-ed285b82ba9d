import {SymbolKind} from 'vscode-languageserver-types';
import Parser, {SyntaxNode} from 'web-tree-sitter';
import {inferLanguage} from '@/utils/languages';
import {queryPatternMatches} from '@/utils/treeSitterUtils';
import {ISymbolSignatureReader, StructuredSignature} from '../../types';
import {AbstractSignatureReader} from './AbstractSignatureReader';

export class CppSymbolSignatureReader extends AbstractSignatureReader implements ISymbolSignatureReader {
    checkSupport(file: string): boolean {
        const language = inferLanguage(file);
        return language === 'cpp';
    }

    protected async getSignatureHelper(
        rootNode: Parser.SyntaxNode,
        startPoint: Parser.Point
    ): Promise<StructuredSignature | undefined> {
        const identifierNode = rootNode.descendantForPosition(startPoint);
        if (identifierNode.type !== 'identifier' && identifierNode.type !== 'type_identifier') {
            return undefined;
        }
        const identifierParent = identifierNode.parent;
        if (!identifierParent) {
            return undefined;
        }
        const kind = this.getSymbolKind(identifierParent);
        if (!kind) {
            return undefined;
        }
        if (kind === SymbolKind.Class) {
            const matches = queryPatternMatches(identifierParent, [
                `(function_definition
                    type: (_) @return_type
                    declarator: (
                        function_declarator
                        declarator: (field_identifier) @name
                    ) @name_and_params
                )
                (function_definition
                    type: (_) @return_type
                    declarator: (
                        reference_declarator
                            (function_declarator
                                declarator: (field_identifier)) @name
                    ) @name_and_params
                )
                (function_definition
                    type: (_) @return_type
                    declarator: (
                        pointer_declarator
                            (function_declarator
                                declarator: (field_identifier)) @name
                    ) @name_and_params
                )
                (declaration
                    type: (_) @return_type
                    declarator: (
                        function_declarator
                            declarator: (_) @name
                    ) @name_and_params
                )
                (field_declaration
                    type: (_) @return_type
                    declarator: (
                        function_declarator
                            declarator: (_) @name
                    ) @name_and_params
                )`,
            ]);
            const children: StructuredSignature[] = matches.map(match => {
                const returnType = match.captures.find(capture => capture.name === 'return_type')!.node.text;
                const nameAndParams = match.captures.find(capture => capture.name === 'name_and_params')!.node.text;
                const name = match.captures.find(capture => capture.name === 'name')!.node.text;
                const signatureText = `${returnType} ${nameAndParams}`;
                return {
                    name,
                    kind: SymbolKind.Method,
                    content: signatureText,
                    children: [],
                };
            });
            return {
                name: identifierNode.text,
                kind,
                content: identifierParent.text.slice(0, identifierParent.text.indexOf('{')).trim(),
                children,
            };
        }
        return undefined;
    }

    private getSymbolKind(node: SyntaxNode): SymbolKind | undefined {
        if (node.text.startsWith('class')) {
            return SymbolKind.Class;
        }
        switch (node.type) {
            case 'class_specifier':
                return SymbolKind.Class;
            default:
                return undefined;
        }
    }
}
