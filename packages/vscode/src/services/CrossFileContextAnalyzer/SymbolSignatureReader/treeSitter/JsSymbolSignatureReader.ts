import {Range, SymbolKind} from 'vscode-languageserver-types';
import Parser, {Point} from 'web-tree-sitter';
import {inferLanguage} from '@/utils/languages';
import {getDeclarationText} from '@/common/prompt/extractLocalImportContext';
import {ISymbolSignatureReader, StructuredSignature} from '../../types';
import {AbstractSignatureReader} from './AbstractSignatureReader';

const DECLARATION_NODE_TYPES = [
    'ambient_declaration',
    'interface_declaration',
    'enum_declaration',
    'type_alias_declaration',
    'function_declaration',
    'function_signature',
    'lexical_declaration',
    'class_declaration',
    'abstract_class_declaration',
];

function getTreeSitterNodeSignature(declarationNode: Parser.SyntaxNode): StructuredSignature | undefined {
    const declaration = getDeclarationText(declarationNode.tree.rootNode.text, declarationNode);
    if (declaration.name && declaration.decl) {
        return {
            name: declaration.name,
            kind: declaration.kind,
            content: declaration.decl,
            children: [],
        };
    }
    return undefined;
}

export class JsSymbolSignatureReader extends AbstractSignatureReader implements ISymbolSignatureReader {
    checkSupport(file: string): boolean {
        const language = inferLanguage(file);
        return language === 'js';
    }

    // eslint-disable-next-line complexity
    async getSignature(
        file: string,
        symbolRange: Range,
        identifier?: string
    ): Promise<StructuredSignature | undefined> {
        const tree = await this.parseFile(file);
        if (!tree) {
            return undefined;
        }
        const adjustedStartPoint: Point = {
            row: symbolRange.start.line,
            column: 0,
        };
        const adjustedEndPoint: Point = {
            row: symbolRange.end.line,
            column: symbolRange.end.character,
        };
        const declarations = tree.rootNode.descendantsOfType(
            DECLARATION_NODE_TYPES,
            adjustedStartPoint,
            adjustedEndPoint
        );
        const declarationNode = declarations[0];
        if (declarationNode) {
            const signature = getTreeSitterNodeSignature(declarationNode);
            if (signature) {
                return signature;
            }
        }
        return this.handleExportDefaultNode(tree, adjustedStartPoint, adjustedEndPoint, identifier);
    }

    private handleExportDefaultNode(tree: Parser.Tree, start: Point, end: Point, identifier?: string) {
        const exportStatement = tree
            .rootNode
            .descendantsOfType(
                'export_statement',
                start,
                end
            )[0];
        if (exportStatement && exportStatement.text.startsWith('export default')) {
            const exportedElement = exportStatement.firstNamedChild;
            if (exportedElement) {
                if (exportedElement.type === 'arrow_function' && identifier) {
                    // 对应这类代码：export default (xxx) => {xxx}
                    const parameters = exportedElement.childForFieldName('parameters')
                        || exportedElement.childForFieldName('parameter');
                    // eslint-disable-next-line max-depth
                    if (!parameters) {
                        return undefined;
                    }
                    const returnType = exportedElement.childForFieldName('return_type')?.firstNamedChild?.text;
                    const content = `declare const ${identifier}: ${parameters.text} => ${returnType ?? 'any'}`;
                    return {name: identifier, content, kind: SymbolKind.Function, children: []};
                }
                if (DECLARATION_NODE_TYPES.includes(exportedElement.type)) {
                    // 对应这类代码：export default <declarationNode>
                    const signature = getTreeSitterNodeSignature(exportedElement);
                    return signature;
                }
            }
        }
        return undefined;
    }
}
