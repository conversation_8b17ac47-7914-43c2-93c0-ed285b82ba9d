import * as vscode from 'vscode';
import {Range} from 'vscode-languageserver-types';
import Parser, {Point, SyntaxNode} from 'web-tree-sitter';
import {TreeSitterProvider, isFileTooLarge} from '@/services/TreeSitterProvider';
import {ISymbolSignatureReader, StructuredSignature} from '../../types';

export abstract class AbstractSignatureReader implements ISymbolSignatureReader {
    constructor(protected readonly treeSitterProvider: TreeSitterProvider) {}

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    checkSupport(_file: string): boolean {
        return false;
    }

    protected async parseFile(file: string): Promise<Parser.Tree | undefined> {
        const document = await vscode.workspace.openTextDocument(file);
        if (isFileTooLarge(document)) {
            return undefined;
        }
        const tree = this.treeSitterProvider.getDocumentTree(document);
        return tree;
    }

    async getSignature(file: string, symbolRange: Range): Promise<StructuredSignature | undefined> {
        const tree = await this.parseFile(file);
        if (!tree) {
            return undefined;
        }
        const startPoint = {
            row: symbolRange.start.line,
            column: symbolRange.start.character,
        };
        const signature = await this.getSignatureHelper(tree.rootNode, startPoint);
        return signature;
    }

    protected async getSignatureHelper(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _rootNode: SyntaxNode,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _startPoint: Point
    ): Promise<StructuredSignature | undefined> {
        throw new Error('Method not implemented.');
    }
}
