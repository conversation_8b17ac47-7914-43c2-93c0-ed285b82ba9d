import {SymbolKind} from 'vscode-languageserver-types';
import {Point, SyntaxNode} from 'web-tree-sitter';
import {inferLanguage} from '@/utils/languages';
import {queryPatternMatches} from '@/utils/treeSitterUtils';
import {StructuredSignature} from '../../types';
import {AbstractSignatureReader} from './AbstractSignatureReader';

// TODO: python 中缩进是语法的一部分，因此要尽量保障缩进的正确。
// 现在用 tree-sitter 获取签名是直接 slice 原文的内容，尤其是多行的签名缩进肯定会有问题。
// 因此暂时先没用到这个 PythonSignatureReader，还是走的 LspSymbolSignatureReader。后面继续把这个实现改对 @tianzerun
export class PythonSignatureReader extends AbstractSignatureReader {
    checkSupport(file: string): boolean {
        return inferLanguage(file) === 'python';
    }

    protected async getSignatureHelper(
        rootNode: SyntaxNode,
        startPoint: Point
    ): Promise<StructuredSignature | undefined> {
        const identifierNode = rootNode.descendantForPosition(startPoint);
        if (identifierNode.type !== 'identifier') {
            return undefined;
        }
        const identifierParent = identifierNode?.parent;
        if (!identifierParent) {
            return undefined;
        }
        const kind = this.getSymbolKind(identifierParent);
        if (!kind) {
            return undefined;
        }
        const blockNode = identifierParent.children.find(item => item.type === 'block');
        if (!blockNode) {
            return undefined;
        }
        const bodyLength = blockNode.endIndex - blockNode.startIndex;
        const signatureText = identifierParent.text.slice(0, identifierParent.text.length - bodyLength).trim();
        if (!kind) {
            return undefined;
        }
        const children: StructuredSignature[] = [];
        if (kind === SymbolKind.Class) {
            const matches = queryPatternMatches(blockNode, ['(function_definition name: (identifier) @identifier)']);
            const childrenIdentifiers = matches.flatMap(match => {
                return match.captures.flatMap(capture => {
                    if (capture.name === 'identifier') {
                        return [capture.node];
                    }
                    return [];
                });
            });
            for (const child of childrenIdentifiers) {
                const childSignature = await this.getSignatureHelper(rootNode, child.startPosition);
                // eslint-disable-next-line max-depth
                if (childSignature) {
                    children.push(childSignature);
                }
            }
        }
        return {
            name: identifierNode.text,
            kind,
            content: signatureText,
            children,
        };
    }

    private getSymbolKind(node: SyntaxNode): SymbolKind | undefined {
        switch (node.type) {
            case 'class_definition':
                return SymbolKind.Class;
            case 'function_definition':
                return SymbolKind.Function;
            default:
                return undefined;
        }
    }
}
