import * as vscode from 'vscode';
import {Position, Range} from 'vscode-languageserver-types';
import {LRUCache} from 'vscode-languageclient';
import {extractMarkdownCodeBlockContent} from '@/utils/common';
import {inferLanguage} from '@/utils/languages';
import {timeoutSafe} from '@/utils/promise';
import {isInRange} from '@/utils/lsp';
import {getMidPosition, serializePosition} from '../utils';
import {ISymbolSignatureReader, StructuredSignature} from '../types';
import {adaptSymbolKind} from '../adaptVSCode';

function isDocumentSymbol(value: vscode.DocumentSymbol | vscode.SymbolInformation): value is vscode.DocumentSymbol {
    return (value as any).children !== undefined;
}

function getContentFromRichString(value: vscode.MarkdownString | vscode.MarkedString | undefined): string | undefined {
    if (!value) {
        return undefined;
    }
    if (typeof value === 'string') {
        return value;
    }
    return value.value;
}

function findSymbolAtPosition(
    symbols: Array<vscode.DocumentSymbol | vscode.SymbolInformation>,
    position: Position
): vscode.DocumentSymbol | vscode.SymbolInformation | undefined {
    for (const symbol of symbols) {
        if (isDocumentSymbol(symbol)) {
            const inRange = isInRange(position, symbol.range);
            if (inRange) {
                const childMatch = findSymbolAtPosition(symbol.children, position);
                return childMatch || symbol;
            }
        }
        else {
            // 先不支持 vscode.SymbolInformation 类型，应该是平铺且 range 会有重叠，不适合递归的去找
            return undefined;
        }
    }
    return undefined;
}

function shouldIncludeSymbol(symbol: vscode.DocumentSymbol, language: string | undefined): boolean {
    if (language === 'python') {
        if (symbol.name === '__init__') {
            return true;
        }
        if (symbol.name.startsWith('_')) {
            return false;
        }
        return true;
    }
    return true;
}

const THREE_SECONDS = 1000 * 3;

interface CacheConfig {
    maxFileLimit: number;
    maxSymbolsLimit: number;
}

export class LspSymbolSignatureReader implements ISymbolSignatureReader, vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    private readonly documentSymbolsCache: LRUCache<
        string, // file uri path
        Array<vscode.DocumentSymbol | vscode.SymbolInformation>
    >;
    private readonly signaturesCache: LRUCache<
        string, // file uri path
        LRUCache<
            string, // symbol range
            StructuredSignature | undefined
        >
    >;

    constructor(protected cacheConfig: CacheConfig) {
        this.documentSymbolsCache = new LRUCache(cacheConfig.maxFileLimit);
        this.signaturesCache = new LRUCache(cacheConfig.maxFileLimit);
        this.disposables.push(
            vscode.workspace.onDidChangeTextDocument(e => {
                if (e.contentChanges.length > 0) {
                    const cacheKey = e.document.uri.path;
                    if (this.documentSymbolsCache.has(cacheKey)) {
                        this.documentSymbolsCache.delete(cacheKey);
                    }
                    if (this.signaturesCache.has(cacheKey)) {
                        this.signaturesCache.delete(cacheKey);
                    }
                }
            })
        );
    }

    checkSupport(file: string): boolean {
        const language = inferLanguage(file);
        return language === 'python' || language === 'go';
    }

    async getSignature(file: string, symbolRange: Range): Promise<StructuredSignature | undefined> {
        const uri = vscode.Uri.file(file);
        const symbolLocationCacheKey = serializePosition(symbolRange.start);
        const cacheHit = this.signaturesCache.get(uri.path)?.get(symbolLocationCacheKey);
        if (cacheHit) {
            return cacheHit;
        }
        const symbols = await timeoutSafe(this.getDocumentSymbols(uri), THREE_SECONDS, []);
        const midPoint = getMidPosition(symbolRange) ?? symbolRange.start;
        const target = findSymbolAtPosition(symbols, midPoint);
        const language = inferLanguage(file);
        const structuredSignature = target
            ? await this.getStructuredSignatureHelper(uri, target, language)
            : undefined;
        if (!this.signaturesCache.has(uri.path)) {
            this.signaturesCache.set(
                uri.path,
                new LRUCache<string, StructuredSignature>(this.cacheConfig.maxSymbolsLimit)
            );
        }
        this.signaturesCache.get(uri.path)?.set(symbolLocationCacheKey, structuredSignature);
        return structuredSignature;
    }

    private async getStructuredSignatureHelper(
        uri: vscode.Uri,
        symbol: vscode.DocumentSymbol | vscode.SymbolInformation,
        language: string | undefined
    ) {
        const content = await this.getSymbolSignature(uri, symbol);
        if (!content) {
            return undefined;
        }
        const node: StructuredSignature = {
            name: symbol.name,
            kind: adaptSymbolKind(symbol.kind),
            content,
            children: [],
        };
        if (isDocumentSymbol(symbol)) {
            if (symbol.kind === vscode.SymbolKind.Class) {
                const innerSymbols = symbol.children.filter(node => shouldIncludeSymbol(node, language));
                for (const innerSymbol of innerSymbols) {
                    const innerContent = await this.getSymbolSignature(uri, innerSymbol);
                    // eslint-disable-next-line max-depth
                    if (innerContent) {
                        node.children.push({
                            name: innerSymbol.name,
                            kind: adaptSymbolKind(innerSymbol.kind),
                            content: innerContent || '',
                        });
                    }
                }
            }
        }
        return node;
    }

    private async getSymbolSignature(uri: vscode.Uri, symbol: vscode.DocumentSymbol | vscode.SymbolInformation) {
        const symbolPosition = isDocumentSymbol(symbol)
            ? symbol.selectionRange.start
            : symbol.location.range.start;
        const hovers = await timeoutSafe(this.getHovers(uri, symbolPosition), THREE_SECONDS, []);
        const topHover = hovers[0];
        if (!topHover) {
            return undefined;
        }
        const topHoverContent = getContentFromRichString(topHover.contents[0]);
        if (!topHoverContent) {
            return undefined;
        }
        const codeBlocks = extractMarkdownCodeBlockContent(topHoverContent);
        // 只要第一个 code block 信息
        return codeBlocks[0];
    }

    private async getDocumentSymbols(uri: vscode.Uri) {
        const cacheHit = this.documentSymbolsCache.get(uri.path);
        if (cacheHit) {
            return cacheHit;
        }
        const symbols = await vscode.commands.executeCommand<Array<vscode.DocumentSymbol | vscode.SymbolInformation>>(
            'vscode.executeDocumentSymbolProvider',
            uri
        );
        this.documentSymbolsCache.set(uri.path, symbols);
        return symbols;
    }

    private async getHovers(uri: vscode.Uri, position: Position) {
        const hovers = await vscode.commands.executeCommand<vscode.Hover[]>(
            'vscode.executeHoverProvider',
            uri,
            position
        );
        return hovers;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables.length = 0;
    }
}
