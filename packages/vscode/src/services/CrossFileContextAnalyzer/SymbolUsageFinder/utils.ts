import Parser from 'web-tree-sitter';
import {SymbolUsage} from '../types';
import {standarizeTreeSitterRange} from '../utils';

export function syntaxNode2SymbolUsage(node: Parser.SyntaxNode): SymbolUsage {
    return {
        name: node.text,
        range: standarizeTreeSitterRange(
            node.startPosition,
            node.endPosition
        ),
        startOffset: node.startIndex,
        endOffset: node.endIndex,
    };
}
