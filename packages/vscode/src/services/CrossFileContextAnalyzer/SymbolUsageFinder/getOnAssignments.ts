import Parser from 'web-tree-sitter';
import {queryPatternMatches} from '@/utils/treeSitterUtils';
import {inferLanguage} from '@/utils/languages';

const QUERY_FOR_ASSIGNED_VARIABLES: Record<string, string> = {
    cpp: `
    (declaration
        type: (qualified_identifier)
        declarator: (init_declarator
                        declarator: (identifier) @left
                        value: (call_expression)))
    (declaration
        type: (type_identifier)
        declarator: (init_declarator
                        (pointer_declarator
                            declarator: (identifier) @left)))
    (declaration
        type: [(type_identifier)(qualified_identifier)]
        declarator: (identifier) @left)`,
    python: `
    (assignment
        left: (identifier) @left
        right: (call))`,
    go: `
    (short_var_declaration
        left: (expression_list
            (identifier) @left))`,
};

export function getOnAssignments(file: string, tree: Parser.Tree) {
    const language = inferLanguage(file);
    const query = language
        ? QUERY_FOR_ASSIGNED_VARIABLES[language]
        : undefined;
    if (query) {
        const matches = queryPatternMatches(tree.rootNode, [query]);
        const allAssignmentNodes = matches.flatMap(match => {
            return match.captures.flatMap(capture => {
                if (capture.name === 'left') {
                    return [capture.node];
                }
                return [];
            });
        });
        return allAssignmentNodes;
    }
    return [];
}
