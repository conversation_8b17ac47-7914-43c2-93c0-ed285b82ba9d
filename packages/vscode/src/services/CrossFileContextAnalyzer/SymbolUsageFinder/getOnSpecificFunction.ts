import Parser from 'web-tree-sitter';
import {inferLanguage} from '@/utils/languages';
import {GoSymbolUsage} from '../types';
import {syntaxNode2SymbolUsage} from './utils';

function getGoPackageIdentifier(typeIdentifier: Parser.SyntaxNode) {
    const tmp = typeIdentifier.previousNamedSibling;
    return tmp?.type === 'package_identifier' ? tmp : undefined;
}

// eslint-disable-next-line complexity
function getOnGoFunction(funcNode: Parser.SyntaxNode): GoSymbolUsage[] {
    if (funcNode.type !== 'function_declaration' && funcNode.type !== 'method_declaration') {
        return [];
    }
    const receiverNode = funcNode.childForFieldName('receiver');
    const paramNode = funcNode.childForFieldName('parameters');
    const resultNode = funcNode.childForFieldName('result');
    const receiverTypeNodes = receiverNode
        ? receiverNode.descendantsOfType('type_identifier')
        : [];
    const paramTypeNodes = paramNode
        ? paramNode.descendantsOfType('type_identifier')
        : [];
    const resultTypeNodes = resultNode
        ? (
            resultNode.type === 'type_identifier'
                ? [resultNode]
                : resultNode.descendantsOfType('type_identifier')
        )
        : [];
    const result: GoSymbolUsage[] = [];
    if (receiverTypeNodes.length > 0) {
        const receiverNode = receiverTypeNodes[0];
        const packageIdentifier = getGoPackageIdentifier(receiverNode);
        result.push({
            language: 'go',
            typeIdentifier: syntaxNode2SymbolUsage(receiverNode),
            packageIdentifier: packageIdentifier ? syntaxNode2SymbolUsage(packageIdentifier) : undefined,
            kind: 'receiverType',
        });
    }
    for (const paramNode of paramTypeNodes) {
        const packageIdentifier = getGoPackageIdentifier(paramNode);
        result.push({
            language: 'go',
            typeIdentifier: syntaxNode2SymbolUsage(paramNode),
            packageIdentifier: packageIdentifier ? syntaxNode2SymbolUsage(packageIdentifier) : undefined,
            kind: 'parameterType',
        });
    }
    for (const resultNode of resultTypeNodes) {
        const packageIdentifier = getGoPackageIdentifier(resultNode);
        result.push({
            language: 'go',
            typeIdentifier: syntaxNode2SymbolUsage(resultNode),
            packageIdentifier: packageIdentifier ? syntaxNode2SymbolUsage(packageIdentifier) : undefined,
            kind: 'returnType',
        });
    }
    return result;
}

export async function getOnSpecificFunction(file: string, funcNode: Parser.SyntaxNode) {
    const language = inferLanguage(file);
    switch (language) {
        case 'go':
            return getOnGoFunction(funcNode);
        default:
            return [];
    }
}
