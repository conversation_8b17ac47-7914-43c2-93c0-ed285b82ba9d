import * as vscode from 'vscode';
import {Position} from 'vscode-languageserver-types';
import {inject, injectable} from 'inversify';
import {adaptToVSCodePosition} from '@/common/lsp/adapters/utils';
import {TreeSitterProvider} from '../../TreeSitterProvider';
import {ISymbolUsageFinder, SymbolUsage, SymbolUsageByLanguage} from '../types';
import {getOnImports} from './getOnImports';
import {getOnAssignments} from './getOnAssignments';
import {syntaxNode2SymbolUsage} from './utils';
import {getOnSpecificFunction} from './getOnSpecificFunction';

@injectable()
export class TreeSitterBasedSymbolUsageFinder implements ISymbolUsageFinder {
    constructor(@inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider) {}

    async findImportedSymbols(file: string): Promise<SymbolUsage[]> {
        const document = await vscode.workspace.openTextDocument(file);
        const tree = this.treeSitterProvider.getDocumentTree(document);
        if (!tree) {
            return [];
        }
        const symbolsNodes = await getOnImports(file, tree);
        const results = symbolsNodes.map(syntaxNode2SymbolUsage);
        return results;
    }

    async findAssignedVariables(file: string): Promise<SymbolUsage[]> {
        const document = await vscode.workspace.openTextDocument(file);
        const tree = this.treeSitterProvider.getDocumentTree(document);
        if (!tree) {
            return [];
        }
        const allAssignmentNodes = getOnAssignments(file, tree);
        const results = allAssignmentNodes.map(syntaxNode2SymbolUsage);
        return results;
    }

    async findOnFunction(file: string, position: Position): Promise<SymbolUsageByLanguage[]> {
        const document = await vscode.workspace.openTextDocument(file);
        const tree = this.treeSitterProvider.getDocumentTree(document);
        if (!tree) {
            return [];
        }
        const vscPosition = adaptToVSCodePosition(position);
        const funcNode = this
            .treeSitterProvider
            .functionNodeOfPosition(
                document,
                vscPosition
            )[0];
        const result: SymbolUsageByLanguage[] = [];
        if (!funcNode) {
            return result;
        }
        return getOnSpecificFunction(file, funcNode);
    }
}
