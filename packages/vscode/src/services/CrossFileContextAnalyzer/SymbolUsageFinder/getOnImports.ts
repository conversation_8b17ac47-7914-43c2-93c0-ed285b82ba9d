import Parser from 'web-tree-sitter';
import {inferLanguage} from '@/utils/languages';
import {queryPatternMatches} from '@/utils/treeSitterUtils';

function getOnPythonImport(tree: Parser.Tree) {
    const importFroms = tree.rootNode.descendantsOfType('import_from_statement');
    const symbolsNodes = importFroms.flatMap(item => {
        const idx = item.children.findIndex(item => item.type === 'import');
        // from a import b 格式中，import 不会出现在最首的位置，因此可以 > 0
        if (idx > 0) {
            return item.children.slice(idx + 1).filter(item => item.type === 'dotted_name');
        }
        return [];
    });
    return symbolsNodes;
}

const QUERY_FOR_IMPORTED_SYMBOLS: Record<string, string> = {
    js: `
    (import_statement
        (import_clause
            (identifier) @imported_symbol
        )
        source: (string)
    )
    (import_statement
        (import_clause
            (named_imports
                (import_specifier
                    name: (identifier) @imported_symbol
                )
            )
        )
        source: (string)
    )
    `,
};

function matchImportedSymbols(tree: Parser.Tree, language: string) {
    const query = QUERY_FOR_IMPORTED_SYMBOLS[language];
    if (query) {
        const matches = queryPatternMatches(tree.rootNode, [query]);
        const allAssignmentNodes = matches.flatMap(match => {
            return match.captures.flatMap(capture => {
                if (capture.name === 'imported_symbol') {
                    return [capture.node];
                }
                return [];
            });
        });
        return allAssignmentNodes;
    }
    return [];
}

export async function getOnImports(file: string, tree: Parser.Tree) {
    const language = inferLanguage(file);
    switch (language) {
        case 'python':
            return getOnPythonImport(tree);
        case 'js':
            return matchImportedSymbols(tree, language);
        default:
            return [];
    }
}
