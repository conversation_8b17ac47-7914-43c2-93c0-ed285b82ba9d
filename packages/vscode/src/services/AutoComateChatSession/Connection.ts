// import WebSocket from 'ws';
// import * as vscode from 'vscode';
// import {info} from '@/common/outputChannel';
// import {VSCodeConfigProvider} from '@/services/ConfigProvider';
// import {iocContainer} from '@/iocContainer';
// import {isSaaS} from '@/utils/features';
// import {L10n} from '@/common/L10nProvider/L10n';
// import {UserService} from '../UserService';
// import consoleLogger from '../../common/consoleLogger';
// import {
//     AutoComateRequestPayload,
//     AutoComateResponse,
//     HeartbeatPayload,
// } from './types';

// // dev 环境
// // const INTERNAL_SOCKET_LOCATION = 'ws://10.11.135.212:8011';
// // saas版本 dev 环境
// // const SAAS_SOCKET_LOCATION = 'wss://comate-external-test.now.baidu-int.com/api/aidevops/autocomate';
// // qa 环境
// // const INTERNAL_SOCKET_LOCATION = 'ws://10.11.135.212:8031';
// // 生产环境
// const INTERNAL_SOCKET_LOCATION = 'wss://newicafe.baidu.com/aidevops/autocomate';
// // saas版本 生产环境
// const SAAS_SOCKET_LOCATION = 'ws://comate.baidu.com/api/aidevops/autocomate';

// const SOCKET_LOCATION_MAPPING: Record<string, string> = {
//     'internal': INTERNAL_SOCKET_LOCATION,
//     'internal-test': 'ws://10.11.135.212:8011',
//     'saas': SAAS_SOCKET_LOCATION,
//     'saas-test': 'ws://10.11.135.212:8061',
// };

// eslint-disable-next-line max-len
// const SOCKET_LOCATION = SOCKET_LOCATION_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')];

// info(`AutoWork socket location: ${SOCKET_LOCATION}`);

// export class AutoComateConnection implements vscode.Disposable {
//     private connection: WebSocket | undefined;
//     private disposables: vscode.Disposable[] = [];

//     constructor(
//         private readonly userService: UserService,
//         private readonly onClose: () => void,
//         private readonly onMessage: (data: AutoComateResponse) => void
//     ) {}

//     private async establish() {
//         // eslint-disable-next-line @typescript-eslint/init-declarations
//         let heartbeatTimeout: NodeJS.Timer | undefined;
//         const ping = (connection: WebSocket, interval = 1000 * 10) => {
//             const timerRef = setInterval(() => {
//                 const data: HeartbeatPayload = {
//                     content: {
//                         type: 'HEARTBEAT',
//                     },
//                 };
//                 connection.send(JSON.stringify(data));
//                 heartbeatTimeout = setTimeout(() => {
//                     connection.terminate();
//                 }, 1000 * 3);
//                 connection.on('close', () => {
//                     clearInterval(timerRef);
//                 });
//             }, interval);
//         };
//         const [username] = await this.userService.getCurrentUser();
//         const configProvider = iocContainer.get(VSCodeConfigProvider);
//         const license = configProvider.getLicense();
//         const userName = isSaaS ? license : username;
//         const lang = L10n.currentLanguage === 'en' ? 'en-US' : '';
//         const ws = new WebSocket(
//             `${SOCKET_LOCATION}/ws/chat/conversation?userName=${userName}&lang=${lang}`
//         );
//         ws.on('error', consoleLogger.error);
//         ws.on('close', (code, reason) => {
//             consoleLogger.debug('AC(closed):', code, reason.toString());
//             this.connection = undefined;
//             this.onClose();
//         });
//         ws.on('message', async (data: WebSocket.RawData) => {
//             try {
//                 const event = JSON.parse(data.toString()) as AutoComateResponse;
//                 if (event.content.type === 'HEARTBEAT') {
//                     clearTimeout(heartbeatTimeout);
//                     return;
//                 }
//                 consoleLogger.debug('AC(incoming):', event);
//                 this.onMessage(event);
//             }
//             catch (e) {
//                 consoleLogger.error((e as Error).message);
//             }
//         });
//         this.disposables.push({
//             dispose: () => {
//                 ws.close();
//             },
//         });
//         return new Promise<WebSocket>(resolve => {
//             ws.on('open', () => {
//                 consoleLogger.debug('AC(connected)');
//                 ping(ws);
//                 resolve(ws);
//             });
//         });
//     }

//     async send(data: AutoComateRequestPayload) {
//         if (!this.connection) {
//             this.connection = await this.establish();
//         }
//         this.connection.send(JSON.stringify(data));
//         consoleLogger.debug('AC(outgoing):', data);
//     }

//     close() {
//         if (this.connection) {
//             this.connection.close();
//             this.connection = undefined;
//         }
//     }

//     dispose() {
//         this.disposables.forEach(disposable => disposable.dispose());
//         this.disposables = [];
//     }
// }
