import {KnowledgeQueryWorkspace} from '@comate/plugin-shared-internals';
import {TaskStatus} from '@shared/protocols';
import {CodeChunk, KnowledgeChunk, WebChunk} from '../../common/types';

export interface AutoComateTask {
    id: number;
    messageId: number;
    // taskType 决定当前思考过程的 tag 的显示
    taskType: 'SEARCH' | 'PLAN' | 'REASONING' | 'THOUGHT' | 'CODE_GENERATE' | 'ANSWER' | 'ANALYZE';
    desc: string; // 任务描述
    status: TaskStatus;
    detail?: string[]; // 子任务描述
}

interface BaseEvent {
    id: number;
    messageId: number;
    append: boolean;
    senderSessionId: string; // 由插件端生成，服务端透传回来的
}

// 通知 IDE 检索最终结果
export interface CodeSearchResultEvent extends BaseEvent {
    type: 'CODE_SEARCH_RESULT';
    detail: {
        exceptionMsg?: string;
        data: Array<Omit<CodeChunk, 'distance'>>;
        /** 知识集检索结果 */
        knowledge: KnowledgeChunk[];
        /** 网络检索结果 */
        web: WebChunk[];
        /** 代码检索总结的markdown文本 */
        summary: string;
        /** 符合开放平台JSX格式的 JSON 字符串 */
        summaryJSXFormat: string;
        /** summary 是否完整输出 */
        end: boolean;
        /** 检索文件列表 */
        files: string[];
        /** 检索目录列表 */
        folders: string[];
        /** 用于统计的uuid */
        adoptionUuid: string;
    };
}

// 通知 IDE 发起本地检索
export interface IDESearchEvent extends BaseEvent {
    type: 'IDE_SEARCH';
    detail: {
        /** 如果语言设置为英文的话，会翻译成中文 */
        queryTransToZH: string;
        query: string;
        queries: string[]; // query改写列表
        taskId: number; // 给此次 IDE 检索任务分配的 id
        files: string[]; // 检索文件列表
        folders: string[]; // 检索目录列表
        knowledge: KnowledgeQueryWorkspace[]; // 检索知识集列表
        needSearch?: boolean; // 是否需要执行query改写
        currentRepo?: boolean; // 是否择了当前代码库,需要进行全库检索
        currentFile?: string; // 选择的当前文件
    };
}

// 通知 IDE 目前思考过程的状态更新
interface ThoughtEvent extends BaseEvent {
    type: 'THOUGHT';
    detail?: {
        data?: Array<Omit<CodeChunk, 'distance'>>;
        summary?: string; // 代码检索总结的markdown文本
        files: string[]; // 检索文件列表
        folders: string[]; // 检索目录列表
    };
}

interface ExceptionEvent extends BaseEvent {
    type: 'EXCEPTION';
    detail: {
        exceptionMsg: string;
    };
}

interface HeartbeatEvent {
    type: 'HEARTBEAT';
    text: 'pong';
    append: boolean;
}

export interface AutoComateResponse {
    messageId: number;
    conversationId: number;
    workflowBuildId: number;
    jobBuildId: number;
    tasks: AutoComateTask[]; // 对应思考过程
    content: CodeSearchResultEvent | IDESearchEvent | ThoughtEvent | ExceptionEvent | HeartbeatEvent;
    timestamp: number;
}

export interface InitialConnectionPayload {
    agentId: number;
    knowledge: string;
    workspace: string;
    initType: 'CODE';
    slash: string;
    content: {
        type: 'QUERY';
        senderSessionId: string;
        detail: {
            query: string;
            folders: string[];
            files: string[];
            currentFile?: string; // 当前打开文件
            contents?: string[]; // 当前打开文件内容
            cursorLine?: number; // 光标所在行
            needSearch?: boolean; // 是否需要执行query改写
            currentRepo?: boolean; // 是否择了当前代码库,需要进行全库检索
            ide?: string; // 用于记录来源
            device?: string; // 用于saas版本数据上报
        };
    };
}

export interface FollowUpPayload {
    agentId: number;
    workflowBuildId: number;
    conversationId: number;
    content: {
        type: 'QUERY';
        senderSessionId: string;
        detail: {
            query: string;
            folders: string[];
            files: string[];
            currentFile?: string; // 当前打开文件
            contents?: string[]; // 当前打开文件内容
            cursorLine?: number; // 光标所在行
            needSearch?: boolean; // 是否需要执行query改写
            currentRepo?: boolean; // 是否择了当前代码库,需要进行全库检索
        };
    };
}

export interface SearchResultsPayload {
    agentId: number;
    conversationId: number;
    workflowBuildId: number;
    messageTaskId: number;
    content: {
        type: 'IDE_SEARCH_RESULT';
        senderSessionId: string;
        detail: {
            data?: CodeChunk[]; // 代码块检索结果
            knowledge?: KnowledgeChunk[]; // 知识集检索结果
            web?: WebChunk[]; // 网络检索结果
            taskId: number;
            query: string;
            queryType: string;
            error?: string;
            success: boolean;
        };
    };
}

export interface CancelConnectionPayload {
    agentId: number;
    conversationId: number;
    workflowBuildId: number;
    jobBuildId: number;
    content: {
        type: 'CANCEL';
        senderSessionId: string;
    };
}

export interface RegeneratePayload {
    agentId: number;
    conversationId: number;
    workflowBuildId: number;
    jobBuildId: number;
    content: {
        type: 'REGENERATE';
        senderSessionId: string;
        detail: {
            query: string;
        };
    };
}

export interface HeartbeatPayload {
    content: {
        type: 'HEARTBEAT';
    };
}

export type AutoComateRequestPayload =
    | InitialConnectionPayload
    | FollowUpPayload
    | SearchResultsPayload
    | CancelConnectionPayload
    | RegeneratePayload
    | HeartbeatPayload;
