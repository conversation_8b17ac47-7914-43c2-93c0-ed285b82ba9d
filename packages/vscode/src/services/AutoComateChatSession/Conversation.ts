import {AssistantMessage, BuiltinAgent, Message, Metadata, UserMessage} from '@shared/protocols';
import * as vscode from 'vscode';
import {ChatModel} from '../ChatViewProvider/ChatModel';
import {COMMAND_DEBUG} from './constants';

export class AutoComateConversation {
    private readonly _onDidChange = new vscode.EventEmitter<void>();
    onDidChange = this._onDidChange.event;
    private readonly _onDidCancelResponse = new vscode.EventEmitter<number>();
    onDidCancelResponse = this._onDidCancelResponse.event;

    constructor(private readonly chatModel: ChatModel, private readonly sessionUuid: string) {}

    addQuery(input: Pick<UserMessage, 'content' | 'code' | 'slash' | 'knowledgeList'>) {
        this.chatModel.cancelAllMessages(this.sessionUuid);
        const message = this.chatModel.addMessage(
            this.sessionUuid,
            {
                ...input,
                role: 'user',
                status: 'success',
                stream: false,
                type: 'thirdParty',
                agent: BuiltinAgent.Comate,
            } as UserMessage
        );
        this._onDidChange.fire();
        return message.id;
    }

    addResponse(
        replyTo: number,
        actions: Record<string, (data: any) => void>,
        agent: string,
        slash?: string,
        metadata?: Metadata
    ) {
        this.chatModel.cancelAllMessages(this.sessionUuid);
        const cancelTokenSource = new vscode.CancellationTokenSource();
        const isDebugSlash = slash === COMMAND_DEBUG;
        const message = this.chatModel.addMessage(
            this.sessionUuid,
            {
                role: 'assistant',
                content: '',
                status: 'inProgress',
                replyTo,
                type: 'thirdParty',
                cancelTokenSource,
                stream: true,
                agent,
                metadata,
                extra: isDebugSlash ? {capability: COMMAND_DEBUG} : undefined,
                actions,
            } as AssistantMessage
        );
        cancelTokenSource.token.onCancellationRequested(() => {
            this._onDidCancelResponse.fire(message.id);
        });
        this._onDidChange.fire();
        return message;
    }

    getMessageById(messageId: number) {
        return this.chatModel.getMessageById(this.sessionUuid, messageId);
    }

    updateResponse(id: number, updates: Partial<Message>) {
        this.chatModel.updateMessage(this.sessionUuid, id, updates);
        this._onDidChange.fire();
    }

    getReplyToWhom(messageId: number) {
        const message = this.chatModel.getMessageById(this.sessionUuid, messageId);
        return message?.replyTo;
    }
}
