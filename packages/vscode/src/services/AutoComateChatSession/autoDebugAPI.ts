import {mapValues, compact} from 'lodash';
import {axiosInstance as axios} from '@comate/plugin-shared-internals';
import {AutoDebugContext, uuapHeader} from './api';

export interface ErrorRegexRule {
    reg: string;
    /** 向前匹配行数 */
    forwardRangeNum: number;
    /** 向后匹配行数 */
    backRangeNum: number;
    /** 正则名称，用于统计数据  */
    regName: string;
    /** 预检索规则 */
    contexts?: any[];
}

type Lang = 'python' | 'go';

const debugRule2AutoworkRule = {
    file: 'FILE',
    directoryTree: 'DIRECTORY_TREE',
    metadata: 'METADATA',
};

const createAutoDebugAPIs = () => {
    return {
        reportTerminalLogLines: async ({
            userName,
            ...params
        }: {platform: 'VSCODE', errorLog: string, repo: string, userName: string}) => {
            const response = await axios.post(
                '/api/autodebug/api/v1/errorfix/record',
                params,
                {headers: await uuapHeader(userName), proxy: false}
            );
            return response.data;
        },
        getTerminalErrorOutputSingleLineRegexp: async ({userName}: {userName: string}) => {
            const response = await axios.get<{data: Record<Lang, ErrorRegexRule[]>}>(
                '/api/autodebug/api/v1/errorfix/regexp?platform=vscode',
                {headers: await uuapHeader(userName), proxy: false}
            );
            const rulesByLang = mapValues(response.data.data, rules => {
                return rules.map(rule => {
                    return {
                        ...rule,
                        contexts: compact(
                            rule.contexts?.map(context => {
                                const customRule = debugRule2AutoworkRule[
                                    context.rule as keyof typeof debugRule2AutoworkRule
                                ];
                                if (customRule) {
                                    return {
                                        params: {
                                            customRule,
                                            ...context,
                                        },
                                    } as any as AutoDebugContext;
                                }
                                return undefined;
                            })
                        ),
                    };
                });
            });
            return rulesByLang;
        },
    };
};

export const autoDebugAPIs = createAutoDebugAPIs();
