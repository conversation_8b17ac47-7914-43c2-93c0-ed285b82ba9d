import * as vscode from 'vscode';
import {flatten, uniq} from 'lodash';
import {getSymbolSearchParams} from '../../api/codeSearch';
import {EmbeddingsService} from '../EmbeddingsService';
import {provideSymbolContext} from '../localSymbolContext';
import {provideKeywordContext} from '../localKeywordContext';
import consoleLogger from '../../common/consoleLogger';
import {IGNORED_KEYWORDS} from './constants';

export async function searchEmbeddings(embeddingsService: EmbeddingsService, query: string[], path?: string[]) {
    const worksapce = vscode.workspace.workspaceFolders;
    if (worksapce && worksapce.length > 0) {
        try {
            const rootPath = worksapce[0].uri.fsPath;
            const results = await embeddingsService.search(query, rootPath, path);
            consoleLogger.debug('embeddings search results:', results);
            return results;
        }
        catch (e) {
            consoleLogger.error('Failed to search embeddings:', (e as Error).message);
            return {data: [], queryType: 'other'};
        }
    }
    return {data: [], queryType: 'other'};
}

export async function searchSymbols(query: string) {
    try {
        const searchParams = await getSymbolSearchParams(query);
        consoleLogger.debug('symbol search params:', searchParams);
        const contents = await Promise.all(searchParams.map(item => provideSymbolContext(item.params)));
        consoleLogger.debug('symbol search contents:', contents);
        const nonEmptyContents = contents.flatMap(item => (item.success ? (item.data ?? []) : []));
        return nonEmptyContents;
    }
    catch (e) {
        consoleLogger.error('Failed to seach local symbol:', (e as Error).message);
        return [];
    }
}

export const abstractKeywordFromQueries = (queries: string[]) => {
    const matches = queries.map(query => Array.from(query.matchAll(/([a-zA-Z][-_]*){3,}\w*/g)));
    const keywords = [...flatten(matches)].map(match => match[0]);
    // 最多取六个keywords
    const candidates = uniq(keywords)
        .filter(item => !IGNORED_KEYWORDS.has(item));
    return candidates;
};

export async function searchKeywords(
    queries: string[],
    searchPath: string = '',
    opts?: Parameters<typeof provideKeywordContext>[2]
) {
    // 最多取六个keywords
    const candidates = opts?.useRegexp ? queries : abstractKeywordFromQueries(queries).slice(0, 6);
    const results = await Promise.all(candidates.map(async keyword => {
        const codeChunks = await provideKeywordContext(keyword, searchPath, opts);
        return {keyword, codeChunks};
    }));
    return results;
}
