import {TreeSitterProvider} from '../TreeSitterProvider';
import {
    AutoDebugClosestFunctionContext,
    AutoDebugContext,
    AutoDebugDirectoryTreeContext,
    AutoDebugFileContext,
    AutoDebugFunctionDefinitionContext,
    AutoDebugMetadataContext,
    AutoDebugStructDefinitionContext,
    AutoDebugSymbolTypeDefinitionContext,
} from './api';
import {AutoDebugSearchMetadataTask} from './searchCodeTask/AutoDebugMetadataTask';
import {AutoDebugReadDirectoryTask} from './searchCodeTask/AutoDebugReadDirectoryTask';
import {AutoDebugSearchClosestFunctionTask} from './searchCodeTask/AutoDebugSearchClosestFunctionTask';
import {AutoDebugSearchDefinitionTask} from './searchCodeTask/AutoDebugSearchDefinitionTask';
import {AutoDebugSearchFileTask} from './searchCodeTask/AutoDebugSearchFileTask';
import {AutoDebugSearchStructDefinitionTask} from './searchCodeTask/AutoDebugSearchStructDefinitionTask';
import {AutoDebugSearchTypeDefinitionTask} from './searchCodeTask/AutoDebugSearchTypeDefinitionTask';

export const autoDebugSearchTask = async (
    repoId: string,
    treeSitterProvider: TreeSitterProvider,
    strategy: AutoDebugContext
) => {
    switch (strategy.params.customRule) {
        case 'CLOSEST_FUNCTION': {
            return new AutoDebugSearchClosestFunctionTask(
                strategy as AutoDebugClosestFunctionContext,
                repoId,
                treeSitterProvider
            )
                .search();
        }
        case 'DEFINITION': {
            return new AutoDebugSearchDefinitionTask(
                strategy as AutoDebugFunctionDefinitionContext,
                repoId,
                treeSitterProvider
            )
                .search();
        }
        case 'FILE': {
            return new AutoDebugSearchFileTask(
                strategy as AutoDebugFileContext,
                repoId,
                treeSitterProvider
            )
                .search();
        }
        case 'DIRECTORY_TREE': {
            return new AutoDebugReadDirectoryTask(
                strategy as AutoDebugDirectoryTreeContext,
                repoId,
                treeSitterProvider
            )
                .search();
        }
        case 'SYMBOLS_IN_STRUCT_OR_PACKAGE': {
            return new AutoDebugSearchStructDefinitionTask(
                strategy as AutoDebugStructDefinitionContext,
                repoId,
                treeSitterProvider
            )
                .search();
        }
        case 'METADATA': {
            return new AutoDebugSearchMetadataTask(
                strategy as AutoDebugMetadataContext,
                repoId,
                treeSitterProvider
            )
                .search();
        }
        case 'SYMBOL_TYPE_DEFINITION': {
            return new AutoDebugSearchTypeDefinitionTask(
                strategy as AutoDebugSymbolTypeDefinitionContext,
                repoId,
                treeSitterProvider
            )
                .search();
        }
        default:
            return null;
    }
};
