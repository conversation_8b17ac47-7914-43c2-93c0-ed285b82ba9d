import {L10n} from '@/common/L10nProvider/L10n';
import {AutoWorkText} from '@/common/L10nProvider/constants';
import {AutoComateTask} from './types';

export const IGNORED_KEYWORDS = new Set([
    'interface',
    'interfaces',
    'class',
    'classes',
    'method',
    'methods',
    'function',
    'functions',
    'constructor',
    'constructors',
    'property',
    'properties',
    'field',
    'fields',
    'definition',
    'definitions',
    'reference',
    'references',
    'keyword',
    'keywords',
    'instance',
    'instances',
    'const',
    'for',
    'while',
    'if',
    'else',
    'then',
    'switch',
    'case',
    'default',
    'extends',
    'implements',
    'new',
    'await',
    'then',
]);

export const TASK_TYPE_TO_DESC_MAPPING = {
    THOUGHT: L10n.t(AutoWorkText.TASK_THOUGHT),
    ANSWER: L10n.t(AutoWorkText.TASK_ANSWER),
} as Record<AutoComateTask['taskType'], string>;

export const CMD_START_AUTO_WORK_SESSION = 'baidu.comate.startAutoWorkSession';
export const CMD_CHAT_WITH_TERMINAL_SELECTION_CONTEXT = 'baidu.comate.chatWithTerminalSelectionContext';

export const COMMAND_ASK = '智能问答-V2';
export const COMMAND_AUTO_TEST = '智能测试';
export const COMMAND_IAPI = '生成API调用代码';
export const COMMAND_DEBUG = 'Auto Debug';
