import {partition} from 'lodash';
import {CodeChunk} from '@/common/types';
import {getCodeChunkFromFilePath, getFilesFromDir} from '@/utils/files';
import {
    CurrentFileReadIntentStrategy,
    FileReadIntentStrategy,
    FolderKeyIntentStrategy,
    FolderReadIntentStrategy,
    FolderRegexIntentStrategy,
    IntentStrategy,
    IntentStrategyRule,
    RepoKeyIntentStrategy,
    RepoRegexIntentStrategy,
} from '../../types/indentStrategy';
import {searchKeywords} from '../../tools';

abstract class IntentSearchTask {
    abstract readonly strategy: IntentStrategy;
    abstract search(): Promise<CodeChunk[]>;
}

export class RepoKeySearchTask implements IntentSearchTask {
    readonly strategy: RepoKeyIntentStrategy | FolderKeyIntentStrategy;
    private readonly searchPath: string;
    constructor(strategy: RepoKeyIntentStrategy | FolderKeyIntentStrategy) {
        this.strategy = strategy;
        this.searchPath = strategy.rule === IntentStrategyRule.REPO_KEY ? '' : strategy.context.id;
    }

    async search() {
        const codeChunks = await searchKeywords([this.strategy.query], this.searchPath);
        return codeChunks.map(item => item.codeChunks).flat();
    }
}

export class RepoRegexSearchTask implements IntentSearchTask {
    strategy: RepoRegexIntentStrategy | FolderRegexIntentStrategy;
    private readonly searchPath: string;
    constructor(strategy: RepoRegexIntentStrategy | FolderRegexIntentStrategy) {
        this.strategy = strategy;
        this.searchPath = strategy.rule === IntentStrategyRule.REPO_REGEX ? '' : strategy.context.id;
    }

    async search() {
        if (this.strategy.pattern) {
            const codeChunks = await searchKeywords(
                this.strategy.pattern.file_content,
                this.searchPath,
                {useRegexp: true}
            );
            return codeChunks.map(item => item.codeChunks).flat();
        }
        return [];
    }
}

export class FileReadSearchTask implements IntentSearchTask {
    strategy: FileReadIntentStrategy | CurrentFileReadIntentStrategy;
    isCurrentFile: boolean = false;
    constructor(strategy: FileReadIntentStrategy | CurrentFileReadIntentStrategy) {
        this.strategy = strategy;
        this.isCurrentFile = strategy.rule === IntentStrategyRule.CURRENT_FILE_READ;
    }

    async search() {
        const codeChunk = await getCodeChunkFromFilePath(this.strategy.context.id);
        if (codeChunk.path) {
            if (this.isCurrentFile) {
                return [{...codeChunk, type: 'CURRENT_FILE' as const}];
            }
            return [codeChunk];
        }
        return [];
    }
}

export class FolderReadSearchTask implements IntentSearchTask {
    strategy: FolderReadIntentStrategy;
    constructor(strategy: FolderReadIntentStrategy) {
        this.strategy = strategy;
    }

    async search() {
        const files = getFilesFromDir(this.strategy.context.id);
        return Promise.all(files.map(file => getCodeChunkFromFilePath(file)));
    }
}

/** 未实现，需要走服务端搜索的，例如向量检索 */
export class NOT_IMPLEMENTED_SEARCH_TASK implements IntentSearchTask {
    strategy: any;
    constructor(strategy: any) {
        this.strategy = strategy;
    }

    async search() {
        return [];
    }
}

export const searchTaskMapping: Record<IntentStrategyRule, new(strategy: any) => IntentSearchTask> = {
    [IntentStrategyRule.REPO_KEY]: RepoKeySearchTask,
    [IntentStrategyRule.REPO_REGEX]: RepoRegexSearchTask,
    [IntentStrategyRule.FOLDER_KEY]: RepoKeySearchTask,
    [IntentStrategyRule.FOLDER_REGEX]: RepoRegexSearchTask,
    [IntentStrategyRule.FOLDER_READ]: FolderReadSearchTask,
    [IntentStrategyRule.FILE_READ]: FileReadSearchTask,
    [IntentStrategyRule.CURRENT_FILE_READ]: FileReadSearchTask,
    [IntentStrategyRule.REPO_VECTOR]: NOT_IMPLEMENTED_SEARCH_TASK,
    [IntentStrategyRule.FOLDER_VECTOR]: NOT_IMPLEMENTED_SEARCH_TASK,
};

export const intentStrategy2SearchTask = (strategy: IntentStrategy) => {
    const SearchTask = searchTaskMapping[strategy.rule];
    if (!SearchTask) {
        return [] as CodeChunk[];
    }
    return new SearchTask(strategy).search();
};

export const isKeywordIntentStrategySearch = (strategy: IntentStrategy) => {
    return strategy.rule === IntentStrategyRule.REPO_KEY
        || strategy.rule === IntentStrategyRule.FOLDER_KEY;
};

export const isCurrentFileStrategy = (strategy: IntentStrategy) => {
    return strategy.rule === IntentStrategyRule.CURRENT_FILE_READ;
};

// 过滤掉一些{}空对象
const isValidCodeChunk = (chunk: CodeChunk): chunk is CodeChunk => Object.keys(chunk).length !== 0;
/**
 * 这是一段dirty的代码，主要是把意图识别的搜索策略和原来的#选择进行适配，关键字检索会返回包含原关键字的对象，用于排序
 */
export const adaptIntentStrategySearch2Original = async (strategies: IntentStrategy[] | undefined) => {
    if (!strategies?.length) {
        return {keywordSearchResult: [], otherSearchResult: []};
    }
    const [intentKeywordSearch, othersIntentSearch] = partition(strategies, isKeywordIntentStrategySearch);
    const keywordSearchPromise = new Promise<Array<{keyword: string, codeChunks: CodeChunk[]}>>(resolve => {
        Promise
            .all(intentKeywordSearch.map(async strategy => {
                const codeChunks = await intentStrategy2SearchTask(strategy);
                return {
                    keyword: strategy.query,
                    codeChunks: codeChunks?.filter(isValidCodeChunk) || [],
                };
            }))
            .then(resolve);
    });
    const otherSearchPromise = Promise.all(
        // 防止后端返回未实现的rule，需要过滤
        othersIntentSearch.filter(intent => !!searchTaskMapping[intent.rule]).map(intentStrategy2SearchTask)
    );
    const [keywordSearchResult, otherSearchResult] = await Promise.all([keywordSearchPromise, otherSearchPromise]);
    return {
        keywordSearchResult,
        otherSearchResult: otherSearchResult.flat().filter(isValidCodeChunk),
    };
};
