import {isAbsolute, join} from 'path';
import * as vscode from 'vscode';
import {error} from '@/common/outputChannel';
import {getCodeChunkFromFilePath} from '@/utils/files';
import {CodeChunk, CodeChunkType} from '@/common/types';
import {isFileExist} from '@/utils/fs';
import {AutoDebugFileContext} from '../api';
import {SearchTask} from './SearchTask';

export class AutoDebugSearchFileTask extends SearchTask<AutoDebugFileContext['params']> {
    async search() {
        try {
            const {filePath, retrieval} = this.context.params;
            const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!rootPath) {
                return null;
            }

            const absolutePath = isAbsolute(filePath) ? filePath : join(rootPath, filePath);
            const existed = await isFileExist(absolutePath);
            const codeChunkType: CodeChunkType = 'file';
            if (existed) {
                const relativePath = vscode.workspace.asRelativePath(filePath);
                const sourceDocument = await getCodeChunkFromFilePath(relativePath);
                return {...sourceDocument, type: codeChunkType};
            }
            else if (retrieval) {
                const uris = await vscode.workspace.findFiles(
                    '**/' + filePath,
                    '**​/(node_modules|site_packages)/**',
                    1
                );
                if (uris.length) {
                    const relativePath = vscode.workspace.asRelativePath(uris[0]);
                    const sourceDocument = await getCodeChunkFromFilePath(relativePath);
                    return {...sourceDocument, type: codeChunkType};
                }
            }
            // 如果文件不存在，则以没有contentStart, contentEnd的方式返回
            return {
                repo: this.repoId,
                type: codeChunkType,
                path: absolutePath,
                content: '',
            } as any as CodeChunk;
        }
        catch (ex) {
            error('AutoDebugSearchFileTask execute failed, reason: ', (ex as Error).message);
            return null;
        }
    }
}
