import {platform, arch} from 'node:os';
import * as vscode from 'vscode';
import {zipObject} from 'lodash';
import {error} from '@/common/outputChannel';
import {execCommand} from '@/utils/cp';
import {CodeChunk} from '@/common/types';
import {AutoDebugMetadataContext, AutoDedugMetadataKey} from '../api';
import {SearchTask} from './SearchTask';

const safeExecCommand = async (command: string, cwd: string) => {
    try {
        return await execCommand(command, cwd);
    }
    catch (ex) {
        return 'unknown';
    }
};

const metadataGetters: Record<AutoDedugMetadataKey, (cwd: string) => Promise<string>> = {
    nodejsVersion: async (cwd: string) => safeExecCommand('node -v', cwd),
    javaVersion: async (cwd: string) => safeExecCommand('java -version', cwd),
    goVersion: async (cwd: string) => safeExecCommand('go version', cwd),
    pythonVersion: async (cwd: string) => safeExecCommand('python --version', cwd),
    gccVersion: async (cwd: string) => safeExecCommand('gcc -v', cwd),
    platform: async () => platform(),
    arch: async () => arch(),
};

export class AutoDebugSearchMetadataTask extends SearchTask<AutoDebugMetadataContext['params']> {
    async search() {
        try {
            const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!rootPath) {
                return null;
            }

            const {metadata} = this.context.params;
            const result = await Promise.all(
                metadata.filter(key => metadataGetters[key]).map(key => metadataGetters[key](rootPath))
            );
            return {
                repo: this.repoId,
                type: 'metadata',
                path: '',
                content: JSON.stringify(zipObject(metadata, result)),
            } as any as CodeChunk;
        }
        catch (ex) {
            error('AutoDebugSearchMetadataTask execute failed, reason: ', (ex as Error).message);
            return null;
        }
    }
}
