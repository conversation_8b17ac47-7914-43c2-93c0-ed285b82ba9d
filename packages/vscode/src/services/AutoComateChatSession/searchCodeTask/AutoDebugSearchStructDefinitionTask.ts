import {isAbsolute, join} from 'path';
import * as vscode from 'vscode';
import {pick} from 'lodash';
import {error} from '@/common/outputChannel';
import {vscodeCommands} from '@/utils/vscodeComands';
import {CodeChunk, CodeChunkType} from '@/common/types';
import {AutoDebugStructDefinitionContext} from '../api';
import {SearchTask} from './SearchTask';

export class AutoDebugSearchStructDefinitionTask extends SearchTask<AutoDebugStructDefinitionContext['params']> {
    async searchStructureDefinition(rootPath: string) {
        const {filePath, symbolName} = this.context.params;
        const fileRelativePath = isAbsolute(filePath) ? filePath : join(rootPath, filePath);
        const document = await vscode.workspace.openTextDocument(fileRelativePath);
        const definitionExpression = this.getShortDefintions(document);

        const matched = definitionExpression.find(definition => {
            // task := &bs.CompileTask{
            //    Repo:    "baidu/bcloud/auto-debug-test",
            //    Version: "master@HEAD",
            //    User:    "zhangsan",
            //    Command: "mkdir output && cp ci.yml output",
            //    running: false,
            // }
            // qualifiedType 匹配 bs.CompileTask
            const qualifiedType = definition.captures[1].node.text;
            return qualifiedType.endsWith(symbolName);
        });

        if (!matched) {
            return null;
        }

        const {row} = matched.captures[1].node.startPosition;
        const {column} = matched.captures[1].node.endPosition;
        const definitions = await vscodeCommands.findDefinitions(
            vscode.workspace.asRelativePath(fileRelativePath),
            new vscode.Position(row, column)
        );

        if (definitions[0]) {
            const definitionUri = definitions[0].uri;
            const document = await vscode.workspace.openTextDocument(definitionUri);
            const structs = this.getStructs(document);
            const matchedStruct = structs.find(struct => {
                const structStartLine = struct.captures[0].node.startPosition.row;
                const definitionStartLine = definitions[0].range.start.line;
                return structStartLine === definitionStartLine;
            });

            if (!matchedStruct) {
                return null;
            }

            const {startPosition, endPosition, text} = matchedStruct.captures[0].node;
            const codeChunkType: CodeChunkType = 'symbolsInStructOrPackage';
            return {
                type: codeChunkType,
                repo: this.repoId,
                contentStart: {
                    line: startPosition.row,
                    column: startPosition.column,
                },
                contentEnd: {
                    line: endPosition.row,
                    column: endPosition.column,
                },
                content: text,
                path: definitionUri.fsPath,
            };
        }

        return null;
    }

    async searchDotProperties(rootPath: string) {
        const {filePath, lineNum, symbolName} = this.context.params;
        const fileRelativePath = isAbsolute(filePath) ? filePath : join(rootPath, filePath);
        const document = await vscode.workspace.openTextDocument(fileRelativePath);

        const lineText = document.lineAt(lineNum - 1);
        const position = lineText.text.indexOf(symbolName);
        const dotPosition = position + symbolName.length + 1;

        const completions = await vscodeCommands.getCompletionItems(
            document,
            new vscode.Position(lineNum - 1, dotPosition)
        );

        const codeChunkType: CodeChunkType = 'symbolsInStructOrPackage';
        const items = completions
            .slice(0, 20)
            .map(item => pick(item, ['label', 'detail', 'kind']));

        if (items.length) {
            return {
                type: codeChunkType,
                repo: this.repoId,
                symbolName,
                content: JSON.stringify(items),
                path: document.uri.fsPath,
            } as any as CodeChunk;
        }
        else {
            return null;
        }
    }

    async search() {
        try {
            const {searchType} = this.context.params;
            const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!rootPath) {
                return null;
            }

            if (searchType === 'MULTILINE') {
                const result = await this.searchStructureDefinition(rootPath);
                return result;
            }
            else if (searchType === 'SINGLELINE') {
                const result = await this.searchDotProperties(rootPath);
                return result;
            }
            return null;
        }
        catch (ex) {
            error('AutoDebugSearchStructDefinitionTask execute failed, reason: ', (ex as Error).message);
            return null;
        }
    }
}
