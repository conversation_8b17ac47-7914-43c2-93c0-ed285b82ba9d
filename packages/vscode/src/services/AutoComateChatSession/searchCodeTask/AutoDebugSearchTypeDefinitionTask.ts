import {isAbsolute, join} from 'path';
import * as vscode from 'vscode';
import Parser from 'web-tree-sitter';
import {error} from '@/common/outputChannel';
import {vscodeCommands} from '@/utils/vscodeComands';
import {checkTreeSitterSupport} from '@/utils/treeSitterUtils';
import {AutoDebugSymbolTypeDefinitionContext} from '../api';
import {SearchTask} from './SearchTask';

/**
 * 在文本中查找关键词的位置，并返回这些位置的数组。
 *
 * @param text 搜索的文本字符串。
 * @param keyword 要查找的关键词。
 * @returns 包含所有找到的关键词位置的数组。如果未找到任何关键词，则返回空数组。
 */
function findKeywordPositions(text: string, keyword: string): number[] {
    const positions: number[] = [];
    let index = text.indexOf(keyword);

    while (index !== -1) {
        positions.push(index);
        index = text.indexOf(keyword, index + keyword.length);
    }

    return positions;
}

/**
 * 通过原生的 executeTypeDefinitionProvider 获取一个变量的类型定义
 *
 * @param document vscode.TextDocument 文档对象
 * @param lineNum number 当前行数
 * @param symbolName string 符号名称
 * @param offset number 符号在行中的偏移量，可选参数。如果未提供，则在整个行中查找所有匹配的关键词位置。
 * @returns Promise<vscode.Location[]> 有效的类型定义位置列表
 */
async function getValidTypeDefinitions(
    document: vscode.TextDocument,
    lineNum: number,
    symbolName: string,
    offset?: number
) {
    const lineText = document.lineAt(lineNum - 1);
    const positions = typeof offset === 'number'
        ? [offset]
        : findKeywordPositions(lineText.text, symbolName);
    const definitions = await Promise.all(positions.map(async index => (
        vscodeCommands.findTypeDefinitions(document.uri, new vscode.Position(lineNum - 1, index))
    )));
    return definitions.flat().filter(definition => definition && definition.uri);
}

export class AutoDebugSearchTypeDefinitionTask extends SearchTask<AutoDebugSymbolTypeDefinitionContext['params']> {
    async search() {
        try {
            const {filePath, lineNum, symbolName, offset} = this.context.params;
            const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!rootPath) {
                return null;
            }
            const fileRelativePath = isAbsolute(filePath) ? filePath : join(rootPath, filePath);
            const document = await vscode.workspace.openTextDocument(fileRelativePath);
            const definitions = await getValidTypeDefinitions(document, lineNum, symbolName, offset);
            if (definitions.length === 0) {
                return null;
            }
            const definition = definitions[0];
            return this.getDefinitionCodeChunk(definition);
        }
        catch (ex) {
            error('AutoDebugSearchTypeDefinitionTask execute failed, reason: ', (ex as Error).message);
            return null;
        }
    }

    private async getDefinitionCodeChunk(definition: vscode.Location) {
        const {range, uri} = definition;
        const definitionDocument = await vscode.workspace.openTextDocument(uri);
        const tree = this.getDocumentTree(definitionDocument);
        const language = checkTreeSitterSupport(definitionDocument.languageId);
        if (!tree || !language) {
            return null;
        }
        const pos = {row: range.start.line, column: range.start.character};
        const identifierNode = tree.rootNode.descendantForPosition(pos);
        if (!identifierNode) {
            return null;
        }
        const codeBlockNode = this.retrieveDeclarationNode(identifierNode);
        const codeChunk = this.treeSitterFunctionToCodeChunk(
            'definition',
            vscode.workspace.asRelativePath(definition.uri.fsPath),
            codeBlockNode
        );
        return {
            ...codeChunk,
            symbolName: this.context.params.symbolName,
        };
    }

    /**
     * 拿到 Definition 节点后往上找函数声明/类声明/类型声明等
     *
     * @param node - 解析器语法节点。
     * @returns 返回类型声明节点作为上下文
     */
    private retrieveDeclarationNode(node: Parser.SyntaxNode) {
        const parent = node.parent;
        if (!parent) {
            return node;
        }

        // python
        if (parent.type === 'class_declaration'
            || parent.type === 'function_declaration'
        ) {
            return parent;
        }
        // go
        if (parent.type === 'type_spec'
            && parent.parent
            && parent.parent.type === 'type_declaration'
        ) {
            return parent.parent;
        }

        // TODO：其他语言还没测试过
        return parent;
    }
}
