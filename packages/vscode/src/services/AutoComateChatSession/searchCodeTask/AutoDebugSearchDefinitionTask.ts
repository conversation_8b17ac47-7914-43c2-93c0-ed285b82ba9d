import * as vscode from 'vscode';
import {checkTreeSitterSupport, extractFunctionNodes} from '@/utils/treeSitterUtils';
import {error} from '@/common/outputChannel';
import {vscodeCommands} from '@/utils/vscodeComands';
import {getCodeChunkFromFilePath} from '@/utils/files';
import {AutoDebugFunctionDefinitionContext} from '../api';
import {SearchTask} from './SearchTask';

export class AutoDebugSearchDefinitionTask extends SearchTask<AutoDebugFunctionDefinitionContext['params']> {
    async search() {
        try {
            const {filePath, lineNum, symbolName} = this.context.params;
            const relativePath = vscode.workspace.asRelativePath(filePath);
            const sourceDocument = await getCodeChunkFromFilePath(
                relativePath,
                new vscode.Range(
                    new vscode.Position(lineNum - 1, 0),
                    new vscode.Position(lineNum - 1, 999)
                )
            );
            const index = sourceDocument.content.indexOf(symbolName);

            if (index === -1) {
                return null;
            }

            const defintions = await vscodeCommands.findDefinitions(
                relativePath,
                new vscode.Position(lineNum - 1, index)
            );
            const definition = defintions[0];
            if (!definition) {
                return null;
            }

            const document = await vscode.workspace.openTextDocument(definition.uri);
            const tree = this.getDocumentTree(document);
            const language = checkTreeSitterSupport(document.languageId);
            if (tree && language) {
                const nodes = extractFunctionNodes(language, tree.rootNode);
                const functionNode = nodes.find(node => node.startPosition.row === definition.range.start.line);
                if (functionNode) {
                    const codeChunk = this.treeSitterFunctionToCodeChunk(
                        'definition',
                        vscode.workspace.asRelativePath(definition.uri.fsPath),
                        functionNode
                    );
                    return {
                        ...codeChunk,
                        symbolName: this.context.params.symbolName,
                        symbolType: 'function',
                    };
                }
            }
            return null;
        }
        catch (ex) {
            error('AutoDebugSearchDefinitionTask execute failed, reason: ', (ex as Error).message);
            return null;
        }
    }
}
