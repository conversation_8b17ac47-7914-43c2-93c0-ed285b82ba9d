import path from 'node:path';
import {existsSync} from 'node:fs';
import {readdirSync} from 'fs';
import * as vscode from 'vscode';
import {isNotJunk} from 'junk';
import {error} from '@/common/outputChannel';
import {CodeChunk, CodeChunkType} from '@/common/types';
import {AutoDebugDirectoryTreeContext} from '../api';
import {SearchTask} from './SearchTask';

const excludeFolderNames = ['node_modules', 'dist', 'site-packages'];

function readdirRecursive(dir: string, recursive: boolean, depth: number = Infinity) {
    const paths = readdirSync(dir, {withFileTypes: true});
    const result = paths.reduce<{directories: string[], files: string[]}>(
        (result, dirent) => {
            const filePath = path.join(dir, dirent.name);
            const relativePath = vscode.workspace.asRelativePath(filePath);
            if (dirent.isDirectory()) {
                if (recursive) {
                    if (excludeFolderNames.includes(dirent.name)) {
                        return result;
                    }
                    if (depth === 1) {
                        return result;
                    }
                    const {directories, files} = readdirRecursive(path.join(dir, dirent.name), recursive, depth - 1);
                    result.directories.push(relativePath, ...directories);
                    result.files.push(...files);
                }
                else {
                    result.directories.push(relativePath);
                }
            }
            else if (isNotJunk(dirent.name)) {
                result.files.push(relativePath);
            }
            return result;
        },
        {directories: [], files: []}
    );
    return result;
}

export class AutoDebugReadDirectoryTask extends SearchTask<AutoDebugDirectoryTreeContext['params']> {
    async search() {
        try {
            const {filePath, depth, recursive} = this.context.params;
            const relativePath = vscode.workspace.asRelativePath(filePath);
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri;
            if (!workspaceRoot) {
                throw new Error('workspace not found');
            }

            const codeChunkType: CodeChunkType = 'directoryTree';
            const absoultePath = vscode.Uri.joinPath(workspaceRoot, relativePath);
            if (!existsSync(absoultePath.fsPath)) {
                return {
                    repo: this.repoId,
                    type: codeChunkType,
                    path: absoultePath.fsPath,
                    content: JSON.stringify({
                        workspace: workspaceRoot.fsPath,
                        filePath: absoultePath.fsPath,
                        exist: false,
                    }),
                } as any as CodeChunk;
            }
            const result = readdirRecursive(absoultePath.fsPath, recursive, depth);
            return {
                repo: this.repoId,
                type: codeChunkType,
                path: absoultePath.fsPath,
                content: JSON.stringify({
                    workspace: workspaceRoot.fsPath,
                    filePath: absoultePath.fsPath,
                    exist: true,
                    ...result,
                }),
            } as any as CodeChunk;
        }
        catch (ex) {
            error('AutoDebugReadDirectoryTask execute failed, reason: ', (ex as Error).message);
            return null;
        }
    }
}
