import {stat, readFile} from 'node:fs/promises';
import {isAbsolute, join} from 'node:path';
import * as vscode from 'vscode';
import {SyntaxNode} from 'web-tree-sitter';
import {CodeChunk} from '@/common/types';
import {checkTreeSitterSupport, extractFunctionNodes} from '@/utils/treeSitterUtils';
import {error} from '@/common/outputChannel';
import {AutoDebugClosestFunctionContext} from '../api';
import {SearchTask} from './SearchTask';

const isLineInNodeRange = (node: SyntaxNode, lineNum: number) => {
    return (node.startPosition.row + 1) <= lineNum && (node.endPosition.row + 1) >= lineNum;
};

export class AutoDebugSearchClosestFunctionTask extends SearchTask<AutoDebugClosestFunctionContext['params']> {
    private async getCodeInRange(fileRelativePath: string) {
        const {filePath, lineNum, forwardRangeNum, backRangeNum} = this.context.params;

        try {
            // 获取文件大小，如果超过所设置的最大值，打印警告并返回空字符串
            const fileStats = await stat(fileRelativePath);
            const maxFileSize = 1024 * 1024; // Set the limit to 1MB
            if (fileStats.size > maxFileSize) {
                // eslint-disable-next-line max-len
                console.warn(
                    `File ${filePath} size (${fileStats.size} bytes) exceeds the maximum size of ${maxFileSize} bytes. Skipping file read.`
                );
                return '';
            }
            const fileContent = await readFile(fileRelativePath, 'utf8');
            const lines = fileContent.split('\n');
            const startLine = Math.max(0, lineNum - forwardRangeNum - 1);
            const endLine = Math.min(lineNum + backRangeNum, lines.length);
            return lines.slice(startLine, endLine).join('\n');
        }
        catch (error) {
            console.error(`Error reading file or exceed range: ${error}`);
            return '';
        }
    }

    async search() {
        try {
            const {filePath, lineNum, forwardRangeNum, maxNum, backRangeNum} = this.context.params;
            const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
            const fileRelativePath = isAbsolute(filePath) ? filePath : join(rootPath, filePath);

            const document = await vscode.workspace.openTextDocument(fileRelativePath);
            const tree = this.getDocumentTree(document);
            const language = checkTreeSitterSupport(document.languageId);

            if (tree && language) {
                const nodes = extractFunctionNodes(language, tree.rootNode);
                const functionNode = nodes.find(node => isLineInNodeRange(node, lineNum));
                if (functionNode && maxNum && functionNode.endPosition.row - functionNode.startPosition.row > maxNum) {
                    const startLine = Math.max(0, lineNum - forwardRangeNum - 1);
                    const endLine = lineNum + backRangeNum;
                    return {
                        type: 'fullContent',
                        repo: this.repoId,
                        path: vscode.workspace.asRelativePath(filePath),
                        content: document.getText(new vscode.Range(startLine, 0, endLine, 0)),
                        contentStart: {line: startLine, column: 0},
                        contentEnd: {line: endLine - 1, column: 0},
                    } as CodeChunk;
                }
                else if (functionNode) {
                    return this.treeSitterFunctionToCodeChunk('fullContent', filePath, functionNode);
                }
            }

            // 获取代码段内容
            const content = await this.getCodeInRange(fileRelativePath);
            // 创建一个 CodeChunk 对象
            const codeChunk: CodeChunk = {
                type: 'fullContent',
                repo: this.repoId,
                path: filePath,
                content: content,
                contentStart: {line: Math.max(0, lineNum - forwardRangeNum - 1), column: 0},
                contentEnd: {line: lineNum + backRangeNum, column: 0},
            };
            return codeChunk;
        }
        catch (ex) {
            error('AutoDebugSearchClosestFunctionTask execute failed, reason: ', (ex as Error).message);
            return null;
        }
    }
}
