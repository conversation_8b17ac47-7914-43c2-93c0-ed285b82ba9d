import * as vscode from 'vscode';
import {SyntaxNode} from 'web-tree-sitter';
import {CodeChunk} from '@/common/types';
import {TreeSitterProvider} from '@/services/TreeSitterProvider';
import {FileContext} from '../api';

export class SearchTask<Params> {
    private readonly _context: FileContext<Params>;
    private readonly _treeSitter: TreeSitterProvider;
    private readonly _repoId: string;
    constructor(context: FileContext<Params>, repoId: string, treeSitter: TreeSitterProvider) {
        this._repoId = repoId;
        this._context = context;
        this._treeSitter = treeSitter;
    }

    get context() {
        return this._context;
    }

    get repoId() {
        return this._repoId;
    }

    get repoPath() {
        return vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '';
    }

    protected treeSitterFunctionToCodeChunk(
        type: 'fullContent' | 'definition',
        filePath: string,
        functionNode: SyntaxNode
    ) {
        return {
            type,
            repo: this._repoId,
            path: vscode.workspace.asRelativePath(filePath),
            content: functionNode.text,
            contentStart: {line: functionNode.startPosition.row, column: 0},
            contentEnd: {line: functionNode.endPosition.row, column: 0},
        } as CodeChunk;
    }

    getStructs(document: vscode.TextDocument) {
        return this._treeSitter.getMatchedNodes(
            document,
            '(type_declaration) @type_decl'
        );
    }

    getShortDefintions(document: vscode.TextDocument) {
        return this._treeSitter.getMatchedNodes(
            document,
            String.raw`
                (short_var_declaration
                    left: (expression_list) @left
                    right: (expression_list
                        (unary_expression
                            operand:(composite_literal
                                type:[(qualified_type)(type_identifier)]@name
                            )
                        )
                    )
                )
            `
        );
    }

    getDocumentTree(document: vscode.TextDocument) {
        return this._treeSitter.getDocumentTree(document);
    }

    search(): Promise<CodeChunk | null> {
        throw new Error('Method not implemented.');
    }
}
