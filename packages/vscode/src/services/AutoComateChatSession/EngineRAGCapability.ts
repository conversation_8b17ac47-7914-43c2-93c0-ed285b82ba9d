import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {ACTION_ASK_RAG} from '@comate/plugin-shared-internals';
import {noop} from 'lodash';
import {KnowledgeList, TaskStatus} from '@shared/protocols';
import {SlashType} from '@shared/constants';
import {iocContainer} from '@/iocContainer';
import {sleep} from '@/utils/common';
import {isInternal} from '@/utils/features';
import {KernelProvider} from '../KernelProvider';
import {UserService} from '../UserService';
import {VSCodeConfigProvider} from '../ConfigProvider';
import {computeRepoId} from '../EmbeddingsService/embeddingUtils';
import {NewAutoComateChatSession} from '.';
import AutoWorkConversationContext from './ConversationContext';
import {getAutoWorkChatCurrentRepoContext, getNeedQueryRewrite} from './utils';
import {AutoComateTask, CodeSearchResultEvent} from './types';

@injectable()
export class EngineRAGCapability implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    constructor(
        @inject(UserService) private readonly userService: UserService,
        @inject(VSCodeConfigProvider) private readonly vsCodeConfig: VSCodeConfigProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider
    ) {
        if (this.kernelProvider.client) {
            this.disposables.push(
                this.kernelProvider.client.onRequest(ACTION_ASK_RAG, async message => {
                    const {prompt, retrieveFrom} = message.data.payload.payload;
                    const knowledgeList = [];
                    if (retrieveFrom?.workspace) {
                        const repoId = await this.computeRepoId();
                        if (repoId) {
                            knowledgeList.push(getAutoWorkChatCurrentRepoContext(repoId));
                        }
                    }
                    return this.askRAG({query: prompt, knowledgeList});
                })
            );
        }
    }

    private async getUserName() {
        const [userName] = await this.userService.getCurrentUser();
        const license = this.vsCodeConfig.getLicense();
        return isInternal ? userName : license;
    }

    private async computeRepoId() {
        const workspaces = vscode.workspace.workspaceFolders;
        const rootPath = workspaces?.[0]?.uri.fsPath;
        if (!rootPath) {
            return undefined;
        }
        const repoId = await computeRepoId(rootPath);
        return repoId;
    }

    async askRAG({query, knowledgeList}: {query: string, knowledgeList: KnowledgeList[]}) {
        const provider = iocContainer.get(NewAutoComateChatSession);
        const loginName = await this.getUserName();
        const repoId = await this.computeRepoId();
        const conversationContext = new AutoWorkConversationContext(iocContainer)
            .setLoginName(loginName)
            .setRepoId(repoId)
            .setQuery(query)
            .setSlash(SlashType.ASK_V2)
            .setChatIntentRecognition(true)
            .setContexts(knowledgeList);
        const contexts = provider.serializeContext(conversationContext);
        let output = '';
        const stage: {taskType: AutoComateTask['taskType'], status: AutoComateTask['status']} = {
            taskType: 'ANALYZE',
            status: TaskStatus.INIT,
        };
        const conversationEvent = {
            onConversationStageChange: (taskType: AutoComateTask['taskType'], status: AutoComateTask['status']) => {
                stage.taskType = taskType;
                stage.status = status;
            },
            onConversationFileChange: noop,
            onConversationContentChange: noop,
            onConversationAnswerChange: (
                _conversationContext: AutoWorkConversationContext,
                data: Partial<CodeSearchResultEvent['detail']>
            ) => {
                if (data.end && data.summary) {
                    output = data.summary;
                    stage.taskType = 'ANSWER';
                    stage.status = TaskStatus.SUCCEED;
                }
            },
        };
        const needQueryRewrite = await getNeedQueryRewrite(conversationContext);
        const analyzeResult = await provider.autoWorkAnalyze(
            conversationContext,
            needQueryRewrite,
            contexts,
            conversationEvent
        );
        const searchResult = await provider.autoWorkSearch(
            conversationContext,
            analyzeResult,
            contexts,
            conversationEvent
        );
        await provider.autoWorkChat(
            conversationContext,
            analyzeResult,
            searchResult,
            contexts,
            conversationEvent
        );

        // eslint-disable-next-line no-constant-condition
        while (true) {
            if (stage.status === TaskStatus.FAIL) {
                return {status: 'failed', output};
            }
            if (stage.taskType === 'ANSWER' && stage.status === TaskStatus.SUCCEED) {
                return {status: 'success', output};
            }
            await sleep(500);
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
    }
}
