import * as vscode from 'vscode';
import {injectable} from 'inversify';
import {ConfigItem, KnowledgeTypeExampleItem} from '@shared/protocols';
import {error} from '@/common/outputChannel';
import {knowledgeTypeGuideConfig} from '@/api/codeSearch/knowledgeGuide';
import {throttle} from '@/utils/throttle';
import {userGuideConfig, userGuideConfigWithExclude} from '../../api/userGuide';
import {computeRepoId} from '../EmbeddingsService/embeddingUtils';

export const CMD_USER_GUIDE_FETCH = 'baidu.comate.userGuideFetch';
export const CMD_KNOWLEDGE_TYPE_GUIDE_FETCH = 'baidu.comate.repoGuideFetch';
export const CMD_USER_GUIDE_CHANGE = 'baidu.comate.userGuideChange';

function hasFileAndFolderExamples(KnowledgeTypeExampleItem: KnowledgeTypeExampleItem[]) {
    const fileType = KnowledgeTypeExampleItem.find(item => item.type === 'file');
    const folderType = KnowledgeTypeExampleItem.find(item => item.type === 'folder');
    return fileType
        && folderType
        && fileType.suggested_quires?.length
        && folderType.suggested_quires?.length;
}

const getKnowledgeTypeGuideThrottle = throttle(knowledgeTypeGuideConfig, 10 * 1000);

@injectable()
export class UserGuideProvider {
    private disposables: vscode.Disposable[] = [];
    private userGuides: ConfigItem[] = [];
    /** 这是 userGuides 的子集， 与当前打开的代码库绑定 */
    private knowledgeTypeGuides: KnowledgeTypeExampleItem[] = [];
    private currentRepoId = '';

    constructor() {
        this.initializeRepoGuide();
        this.disposables.push(
            vscode.commands.registerCommand(CMD_USER_GUIDE_FETCH, this.userGuide.bind(this)),
            vscode.commands.registerCommand(CMD_KNOWLEDGE_TYPE_GUIDE_FETCH, this.getKnowledgeTypeGuides.bind(this)),
            vscode.commands.registerCommand(CMD_USER_GUIDE_CHANGE, this.userGuideChange.bind(this)),
            vscode.workspace.onDidChangeWorkspaceFolders(() => {
                this.initializeRepoGuide();
            })
        );
    }

    private async userGuide() {
        try {
            if (this.userGuides.length) {
                return this.userGuides;
            }
            if (!this.currentRepoId) {
                await this.initializeRepoGuide();
            }
            const userGuideConfigs = await userGuideConfig(this.currentRepoId);
            const data = userGuideConfigs.data.data;
            this.userGuides = data;
            return Array.isArray(data) ? data.slice(0, 3) : [];
        }
        catch (ex) {
            error('error:', (ex as Error).message);
            return [];
        }
    }

    private async getKnowledgeTypeGuides() {
        if (
            !this.currentRepoId
            || hasFileAndFolderExamples(this.knowledgeTypeGuides)
        ) {
            return this.knowledgeTypeGuides;
        }
        try {
            const repoGuides = await getKnowledgeTypeGuideThrottle(this.currentRepoId);
            this.knowledgeTypeGuides = repoGuides ?? [];
        }
        catch (ex) {
            error('Get RepoGuide Error: ', (ex as Error).message);
        }
        return this.knowledgeTypeGuides;
    }

    private async userGuideChange() {
        try {
            const lastUserGuideIds = this.userGuides.map(item => item.uuid);
            const userGuideConfigs = await userGuideConfigWithExclude(this.currentRepoId, lastUserGuideIds);
            const data = userGuideConfigs.data.data;
            this.userGuides = data;
            return Array.isArray(data) ? data.slice(0, 3) : [];
        }
        catch (ex) {
            error('error:', (ex as Error).message);
            return [];
        }
    }

    /**
     * 计算 workspace 的 repoId 然后通过接口获取对应的使用引导
     */
    private async initializeRepoGuide() {
        try {
            // 建立索引的逻辑相同，只看第一个 workspace
            const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            const repoId = await computeRepoId(rootPath);
            const repoGuides = await knowledgeTypeGuideConfig(repoId);
            this.knowledgeTypeGuides = repoGuides;
            this.currentRepoId = repoId ?? '';
        }
        catch (ex) {
            error('Initialize RepoGuide Error: ', (ex as Error).message);
            this.knowledgeTypeGuides = [];
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
