/* eslint-disable complexity */
import path from 'node:path';
import * as vscode from 'vscode';
import axios from 'axios';
import {injectable, inject} from 'inversify';
import Parser from 'web-tree-sitter';
import {
    localPluginConfig,
    mergePluginConfig,
} from '@comate/plugin-shared-internals';
import {TYPES} from '@/inversify.config';
import {CMD_GENERATE_DOCSTRING, SUFFIX_LANG_MAP} from '@/constants';
import {L10n, getGenerationFailureText, getRequestFailureText} from '@/common/L10nProvider/L10n';
import {DocstringProviderText} from '@/common/L10nProvider/constants';
import {findDocstringInsertionLocation} from '../../utils/docString';
import {docStringDefaultConfig as defaultConfig} from '../../utils/defaultConfig';
import {getCompleteFirstLine} from '../../utils/document';
import {stripExtraIndent, stripMinIndent} from '../../utils/indent';
import {GenerateCodeOptions} from '../../api';
import {TreeSitterLanguage} from '../../utils/treeSitterUtils';
import {
    addEolNewline,
    addMarkdownCodeBlock,
    extractMarkdownCodeBlocks,
    stripMarkdownCodeBlock,
} from '../../utils/common';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {iocContainer} from '../../iocContainer';
import {UserService} from '../UserService';
import {buildParams, fetchAndStreamCode} from '../../common/Fetcher';
import {ChatViewProvider} from '../ChatViewProvider';
import {CodeLensDisplayMode, CodelensConfig, ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {DiffProvider} from '../DiffProvider';
import {Conversation} from '../ChatViewProvider/Conversation';
import {ILicenseController} from '../LicenseController/types';
import {CodeLensProvider} from '../FoldedCodeLensProvider';
import {ChatBaseProvider} from '../ChatBaseProvider';
import {TextResponse} from '../ChatViewProvider/TextResponse';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';
import {InlineChatProvider} from '../InlineChatProvider';
import {formatGeneratedDocstring} from './utils';

const commentRegex = (lang: string) => {
    const reg = {
        java: /\/\*\*.*?\*\//gs,
        python: /""".*?"""/gs,
        go: /\/\/.*?\n/g,
        javascript: /\/\*\*.*?\*\//gs,
        typescript: /\/\*\*.*?\*\//gs,
        cpp: /\/\*\*.*?\*\//gs,
    }[lang];
    return reg;
};

export function buildInsertionText(content: string, padding: string) {
    const lines = addEolNewline(content).split('\n');
    const indentedLine = [
        ...lines.slice(0, lines.length - 1).map(line => `${padding}${line}`),
        ...lines.slice(lines.length - 1),
    ];
    return indentedLine.join('\n');
}

export function filterDocstringFunctionNode(node: Parser.SyntaxNode, languageId: TreeSitterLanguage) {
    const isMultiLine = node.startPosition.row !== node.endPosition.row;
    if (
        languageId === TreeSitterLanguage.JavaScript
        || languageId === TreeSitterLanguage.TypeScript
        || languageId === TreeSitterLanguage.Tsx
    ) {
        return isMultiLine;
    }
    if (languageId === TreeSitterLanguage.Python) {
        // 由于 python 函数的 docstring 要加在签名后一行，所以要确保有函数签名
        const signatureNode = node.childForFieldName('return_type') ?? node.childForFieldName('parameters');
        return isMultiLine && signatureNode !== null;
    }
    return isMultiLine;
}

@injectable()
export class DocstringCodeLensesProvider extends ChatBaseProvider implements CodeLensProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'javascript',
        'typescript',
        'javascriptreact',
        'jsx',
        'typescriptreact',
        'cpp',
        'c',
        'vue',
    ];
    private disposables: vscode.Disposable[] = [];
    onDidChangeCodeLenses?: vscode.Event<void> | undefined;
    _onDidChange = new vscode.EventEmitter<vscode.Uri>();
    onDidChange: vscode.Event<vscode.Uri> = this._onDidChange.event;

    constructor(
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(TYPES.ILicenseController) private readonly licenseController: ILicenseController,
        @inject(PerformanceLogProvider) private readonly performanceLog: PerformanceLogProvider,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider,
        @inject(InlineChatProvider) private readonly inlineChatProvider: InlineChatProvider
    ) {
        super(diffProvider);
        this.disposables.push(
            vscode.languages.registerCodeLensProvider(
                DocstringCodeLensesProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this
            ),
            vscode.commands.registerCommand(
                CMD_GENERATE_DOCSTRING,
                async (
                    document: vscode.TextDocument,
                    selection: vscode.Range,
                    insertPosition: vscode.Position,
                    padding: string
                ) => {
                    const startTime = performance.now();
                    // 英文版本屏蔽开放平台能力
                    if (L10n.isEnglish) {
                        return this.handleGenerateDocstringCommandFallback(
                            document,
                            selection,
                            insertPosition,
                            padding,
                            startTime
                        );
                    }
                    else {
                        return this.handleGenerateDocstringCommand(
                            document,
                            selection,
                            insertPosition,
                            padding,
                            false,
                            startTime
                        );
                    }
                }
            )
        );
    }

    async handleGenerateDocstringCommandFallback(
        document: vscode.TextDocument,
        selection: vscode.Range,
        insertPosition: vscode.Position,
        padding: string,
        startTime: number = performance.now()
    ) {
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_GENERATE_DOCSTRING});
        const firstLine = getCompleteFirstLine(document, selection);
        const functionContent = stripExtraIndent(document.getText(selection), true, firstLine);
        const conversation = this.chatViewProvider.createConversation(
            L10n.t(DocstringProviderText.PROMPT),
            'docstring',
            addMarkdownCodeBlock(functionContent, document.languageId)
        );
        const res = await this.generateFallback(document, functionContent, conversation, insertPosition, padding);
        if (res?.uuid) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'docstring',
                duration: performance.now() - startTime,
                uuid: res.uuid,
                type: 'all',
            });
        }
    }

    async generateFallback(
        document: vscode.TextDocument,
        functionContent: string,
        conversation: Conversation,
        insertPosition: vscode.Position,
        padding: string
    ) {
        const streamMode = this.configProvider.getConfig(ConfigKey.EnableStreamingSession);
        const reply = conversation.addResponse(streamMode ? 'stream' : 'text', '', 'inProgress', {
            regenerate: async () => {
                this.generateFallback(document, functionContent, conversation, insertPosition, padding);
            },
            // 打开全文复制按钮
            copyAll: () => {},
        });
        const paramsRes = await buildParams(
            document,
            new vscode.Position(0, 0),
            iocContainer.get(UserService)
        );
        if (paramsRes.type !== 'success') {
            reply.fail(getGenerationFailureText(paramsRes.reason));
            return {uuid: ''};
        }

        const params = {...paramsRes.value, content: functionContent, model: 'ERNIE_BOT'};
        try {
            return this.fetchAndStreamFallback(document, insertPosition, padding, reply, params);
        }
        catch (e: any) {
            reply.fail(getGenerationFailureText(e.message));
            return {uuid: ''};
        }
    }

    private async fetchAndStreamFallback(
        document: vscode.TextDocument,
        insertPosition: vscode.Position,
        padding: string,
        reply: TextResponse,
        params: GenerateCodeOptions
    ) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        const {content, uuid, processor, chatId} = await fetchAndStreamCode(
            params,
            (content, uuid, chatId) => {
                if (content) {
                    const formattedDocstring = formatGeneratedDocstring(document.languageId, content);
                    reply.update(
                        formattedDocstring,
                        this.getSuccessActions(
                            reply,
                            document,
                            insertPosition,
                            padding,
                            uuid,
                            chatId,
                            params
                        )
                    );
                }
            },
            reply.message.cancelTokenSource?.token,
            axiosTokenSource.token
        );
        if (processor.error) {
            const msg = processor.errorMsg;
            reply.fail(getRequestFailureText(msg));
            return;
        }
        if (!content) {
            reply.fail(L10n.t(DocstringProviderText.GENERATE_ERROR));
            return;
        }
        const formattedDocstring = formatGeneratedDocstring(document.languageId, content);
        reply.success(
            formattedDocstring,
            this.getSuccessActions(
                reply,
                document,
                insertPosition,
                padding,
                uuid,
                chatId,
                params
            ),
            uuid
        );

        return {uuid};
    }

    async handleGenerateDocstringCommand(
        document: vscode.TextDocument,
        selection: vscode.Range,
        insertPosition: vscode.Position,
        padding: string,
        multipleFunctionsSelected: boolean = false,
        startTime: number = performance.now()
    ) {
        const codelensDisplayMode = this.configProvider.getDocstringDisplayMode();
        const pluginConfig = await this.pluginConfig();
        if (codelensDisplayMode === 'editor' && this.isDefaultConfig(pluginConfig)) {
            this.inlineChatProvider.generateInlineChatContent(document, selection, 'CODE_TO_COMMENT', insertPosition);
            return;
        }
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_GENERATE_DOCSTRING});
        const firstLine = getCompleteFirstLine(document, selection);
        const functionContent = stripExtraIndent(document.getText(selection), true, firstLine);
        const conversation = this.chatViewProvider.createConversation(
            this.displayPrompt(pluginConfig),
            'docstring',
            addMarkdownCodeBlock(functionContent, document.languageId)
        );
        const res = await this.generate(
            document,
            functionContent,
            conversation,
            insertPosition,
            padding,
            pluginConfig,
            multipleFunctionsSelected,
            startTime
        );
        if (res?.uuid) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'docstring',
                duration: performance.now() - startTime,
                uuid: res.uuid,
                type: 'all',
            });
        }
    }

    async generate(
        document: vscode.TextDocument,
        functionContent: string,
        conversation: Conversation,
        insertPosition: vscode.Position,
        padding: string,
        pluginConfig: {functionCommentLanguage: string, functionCommentIncludeFunctionParameters: string},
        multipleFunctionsSelected: boolean,
        startTime: number
    ) {
        const streamMode = this.configProvider.getConfig(ConfigKey.EnableStreamingSession);
        const reply = conversation.addResponse(streamMode ? 'stream' : 'text', '', 'inProgress', {
            regenerate: async () => {
                this.generate(
                    document,
                    functionContent,
                    conversation,
                    insertPosition,
                    padding,
                    pluginConfig,
                    multipleFunctionsSelected,
                    performance.now()
                );
            },
            // 打开全文复制按钮
            copyAll: () => {},
        });
        const paramsRes = await buildParams(
            document,
            new vscode.Position(0, 0),
            iocContainer.get(UserService)
        );
        if (paramsRes.type !== 'success') {
            reply.fail(getGenerationFailureText(paramsRes.reason));
            return {uuid: ''};
        }
        const params = {
            ...paramsRes.value,
            content: functionContent,
            userInput: functionContent,
            model: 'ERNIE_BOT',
            function: 'CODE_TO_COMMENT',
        };
        if (!this.isDefaultConfig(pluginConfig)) {
            params.function = 'COMMENT_TO_CODE';
            params.content = this.realPrompt(pluginConfig, paramsRes.value.path, functionContent);
        }
        try {
            return this.fetchAndStream(document, insertPosition, padding, reply, params, startTime);
        }
        catch (e: any) {
            reply.fail(getGenerationFailureText(e.message));
            return {uuid: ''};
        }
    }

    async provideCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();
        if (codelensDisplayMode !== CodeLensDisplayMode.TextTitle) {
            return [];
        }
        const codeLenses = await this.computeCodeLenses(document);
        return this.tmpCodeLensProvider.filterOverlapCodeLenses(document, codeLenses);
    }

    async computeCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensConfig = this.configProvider.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        if (codelensConfig?.enableInlineDocstring !== true || !this.licenseController.hasLicense) {
            return [];
        }
        if (this.diffProvider.isInDiff(document.uri.fsPath)) {
            return [];
        }
        return this.treeSitterProvider.getFunctionCodeLens(
            document,
            filterDocstringFunctionNode,
            (document: vscode.TextDocument, range: vscode.Range, node: Parser.SyntaxNode) => {
                const location = findDocstringInsertionLocation(document, node);
                const {position, padding} = location;
                return {
                    title: L10n.t(DocstringProviderText.CODELENS_TITLE),
                    tooltip: L10n.t(DocstringProviderText.CODELENS_TOOLTIP),
                    command: CMD_GENERATE_DOCSTRING,
                    arguments: [document, range, position, padding],
                };
            }
        );
    }

    private async fetchAndStream(
        document: vscode.TextDocument,
        insertPosition: vscode.Position,
        padding: string,
        reply: TextResponse,
        params: GenerateCodeOptions,
        startTime: number
    ) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        const language = SUFFIX_LANG_MAP[path.extname(params.path).slice(1)];
        const reg = commentRegex(language);
        let firstToken = true;
        const resCode = await fetchAndStreamCode(
            params,
            (content: string, uuid: string, chatId: string) => {
                // 只返回代码部分
                const startIndex = content.indexOf('```');
                const endIndex = content.lastIndexOf('```');

                if (content && firstToken) {
                    this.performanceLog.log({
                        plugin: 'comate',
                        skill: 'docstring',
                        duration: performance.now() - startTime,
                        uuid,
                        type: 'first-token',
                    });
                    firstToken = false;
                }
                const captureComment = (code: string, matches: RegExpMatchArray) => {
                    const matchIndex = code.indexOf(matches[0]);
                    const matchEnd = matchIndex + matches.map(v => v.length).reduce((a, b) => a + b);

                    const formattedDocstring = formatGeneratedDocstring(
                        document.languageId,
                        code.slice(0, matchEnd) + '\n```'
                    );
                    return reply[endIndex > matchEnd ? 'success' : 'update'](
                        formattedDocstring,
                        this.getSuccessActions(
                            reply,
                            document,
                            insertPosition,
                            padding,
                            uuid,
                            chatId,
                            params
                        )
                    );
                };
                if (startIndex === -1 || endIndex === -1) {
                    return reply.update('');
                }
                // 代码块没结束
                else if (startIndex === endIndex) {
                    const code = this.modifyComment(language, content.slice(startIndex));
                    if (reg && code) {
                        const matches = code.match(reg);
                        if (matches) {
                            return captureComment(code, matches);
                        }
                    }
                    return reply.update(
                        code,
                        this.getSuccessActions(
                            reply,
                            document,
                            insertPosition,
                            padding,
                            uuid,
                            chatId,
                            params
                        )
                    );
                }
                // 代码块结束了
                else {
                    const code = this.modifyComment(language, content.slice(startIndex, endIndex));
                    if (reg && code) {
                        const matches = code.match(reg);
                        if (matches) {
                            return captureComment(code, matches);
                        }
                    }
                    return reply.update(
                        code,
                        this.getSuccessActions(
                            reply,
                            document,
                            insertPosition,
                            padding,
                            uuid,
                            chatId,
                            params
                        )
                    );
                }
            },
            reply.message.cancelTokenSource?.token,
            axiosTokenSource.token
        );
        const {uuid, processor, chatId} = resCode;
        let {content} = resCode;
        if (processor.error) {
            const msg = processor.errorMsg;
            reply.fail(getRequestFailureText(msg));
            return;
        }
        if (!content || !content.includes('```')) {
            reply.fail(L10n.t(DocstringProviderText.GENERATE_ERROR));
            return;
        }
        content = this.modifyComment(language, extractMarkdownCodeBlocks(content)[0]);
        if (reg && content) {
            const matches = content.match(reg);
            if (matches) {
                const matchIndex = content.indexOf(matches[0]);
                const matchEnd = matchIndex + matches.map(v => v.length).reduce((a, b) => a + b);
                content = content.slice(0, matchEnd) + '\n```';
            }
        }
        const formattedDocstring = formatGeneratedDocstring(document.languageId, content);
        reply.success(
            formattedDocstring,
            this.getSuccessActions(
                reply,
                document,
                insertPosition,
                padding,
                uuid,
                chatId,
                params
            ),
            uuid
        );

        return {uuid};
    }

    private getSuccessActions(
        reply: TextResponse,
        document: vscode.TextDocument,
        insertPosition: vscode.Position,
        padding: string,
        uuid?: string,
        chatId?: string,
        params?: GenerateCodeOptions
    ) {
        const {diff, accept} = this.diffProvider.createInsertionDiffHandler(
            document,
            insertPosition,
            content => {
                // 这里的padding是完整的，也就是说 formattedDocstring 不应该有缩进了
                // 非流式时后端会处理去掉缩进，流式时没有处理是带上缩进的，所以这里得先去掉缩进
                return buildInsertionText(
                    stripMinIndent(stripMarkdownCodeBlock(content, document.languageId).split('\n')),
                    padding
                );
            },
            uuid,
            'docstring',
            params
        );
        const chatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getChatId: () => chatId,
            getMessageContent: () => reply.message.content,
            getTrackUuid: () => uuid || reply.message.extra?.uuid,
        };
        return {
            ...this.defaultActions(chatResponseProxy, 'docstring', params, {ignoreSmartApplyFeature: true}),
            diff,
            accept,
        };
    }

    modifyComment(language: string, code: string) {
        // 特殊处理python
        if (language === 'python') {
            const lines = code.split('\n');
            const firstCommentIndex = lines.findIndex(l =>
                l.includes('"""') || l.includes('\'\'\'') || l.includes('#')
            );
            if (firstCommentIndex > -1) {
                // eslint-disable-next-line no-param-reassign
                code = lines[0] + '\n' + lines.slice(firstCommentIndex).join('\n');
            }
            else {
                // eslint-disable-next-line no-param-reassign
                code = lines[0] + '\n';
            }
        }
        return code;
    }

    async pluginConfig() {
        const workspace = vscode.workspace.workspaceFolders;
        const pluginConfigs = this.chatViewProvider.pluginConfigs;
        // @ts-ignore
        const localConfig = await localPluginConfig(workspace);
        const {config} = await mergePluginConfig(pluginConfigs['comate'], localConfig);
        if (!config.functionCommentLanguage) {
            config.functionCommentLanguage = defaultConfig.functionCommentLanguage;
        }
        if (!config.functionCommentIncludeFunctionParameters) {
            config.functionCommentIncludeFunctionParameters = defaultConfig.functionCommentIncludeFunctionParameters;
        }
        return config || {};
    }

    isDefaultConfig(config: Record<string, any>) {
        if (
            config.functionCommentLanguage === defaultConfig.functionCommentLanguage
            && config.functionCommentIncludeFunctionParameters
                === defaultConfig.functionCommentIncludeFunctionParameters
        ) {
            return true;
        }
        return false;
    }

    realPrompt(
        {
            functionCommentLanguage,
            functionCommentIncludeFunctionParameters,
        }: {
            functionCommentLanguage: string;
            functionCommentIncludeFunctionParameters: string;
        },
        filePath: string,
        code: string
    ) {
        const language = SUFFIX_LANG_MAP[path.extname(filePath).slice(1)];
        return `给下面这段代码加上${functionCommentLanguage}的文档注释，${
            functionCommentIncludeFunctionParameters === '否' ? '不' : ''
        }包括函数参数：
\`\`\`${language}
${code}
\`\`\``;
    }

    displayPrompt({
        functionCommentLanguage,
        functionCommentIncludeFunctionParameters,
    }: {
        functionCommentLanguage: string;
        functionCommentIncludeFunctionParameters: string;
    }) {
        return `给下面这段代码加上${functionCommentLanguage}的文档注释，${
            functionCommentIncludeFunctionParameters === '否' ? '不' : ''
        }包括函数参数：`;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
