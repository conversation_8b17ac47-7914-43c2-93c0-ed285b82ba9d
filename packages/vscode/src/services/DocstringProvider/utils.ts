import * as vscode from 'vscode';
import {findLeadingNonWhitespaceIndex} from '@/utils/common';
import {COMATE_DOCSTRING_SCHEME} from '../../constants';

interface PanelUriQuery {
    sourceFileUri: string;
    startLine: number;
    startCharacter: number;
    endLine: number;
    endCharacter: number;
    inserationLine: number;
    inserationCharacter: number;
    padding: string;
}

let id = 0;

export const buildPanelUri = (
    sourceFileUri: vscode.Uri,
    selection: vscode.Range,
    inserationPosition: vscode.Position,
    padding: string
) => {
    const query = JSON.stringify({
        sourceFileUri: sourceFileUri.toString(),
        startLine: selection.start.line,
        startCharacter: selection.start.character,
        endLine: selection.end.line,
        endCharacter: selection.end.character,
        inserationLine: inserationPosition.line,
        inserationCharacter: inserationPosition.character,
        padding,
    });
    const uri = vscode.Uri.from({
        scheme: COMATE_DOCSTRING_SCHEME,
        path: '【实验室】Baidu Comate注释生成面板',
        query,
        fragment: String(id++),
    });
    return uri;
};

export const resolvePanelUri = (uri: vscode.Uri) => {
    const query = JSON.parse(uri.query) as PanelUriQuery;
    const {
        sourceFileUri,
        inserationLine,
        startLine,
        startCharacter,
        endLine,
        endCharacter,
        inserationCharacter,
        ...rest
    } = query;
    return {
        sourceFileUri: vscode.Uri.parse(sourceFileUri),
        inserationPosition: new vscode.Position(inserationLine, inserationCharacter),
        selection: new vscode.Range(startLine, startCharacter, endLine, endCharacter),
        ...rest,
    };
};

function trimLeadingWhitespaces(content: string) {
    const lines = content.split('\n');
    // count the number of whitespaces at the beginning of the first line
    const whitespaceLength = findLeadingNonWhitespaceIndex(lines[0]);
    if (whitespaceLength < 0) {
        return content;
    }
    return lines.map(item => item.slice(whitespaceLength)).join('\n');
}

export function formatGeneratedDocstring(languageId: string, content: string) {
    if (languageId === 'python') {
        return trimLeadingWhitespaces(content);
    }
    return content;
}
