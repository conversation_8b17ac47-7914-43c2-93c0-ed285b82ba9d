/* eslint-disable max-depth */
/* eslint-disable camelcase */
import {readFileSync} from 'node:fs';
import {join} from 'node:path';
import {chunk} from 'lodash';
import {CancelTokenSource} from 'axios';
import {getAxios, getVersionPrefix} from '@/api/codeSearch/common';
import {PatchFileIndex, postRemoteIndexMetadata} from '@/api/codeSearch';
import {isInternal} from '@/utils/features';
import {debug} from '@/common/outputChannel';

export interface FileMeta {
    sha1: string;
    path: string;
    action: PatchFileIndex['action'];
}

export async function* apiPostFileForIndex({repo_id, device, repo, branch, files, limit, cwd, version, scene}: {
    repo_id: string;
    device: string;
    branch?: string;
    repo?: string;
    limit: number;
    cwd: string;
    version: 'v1' | 'v2';
    scene: 'firstBuild' | 'reBuild' | 'common';
    cancelToken: CancelTokenSource;
    files: FileMeta[];
}) {
    const chunks = chunk(files, limit);
    try {
        for await (const chunk of chunks) {
            const files = chunk.map(file => ({
                path: file.path,
                sha1: file.sha1,
                action: file.action,
                content: file.action === 'delete' ? '' : readFileSync(join(cwd, file.path), 'utf-8'),
            }));
            const meta = {device, repo_id, repo, branch, scene};
            try {
                const versionPrefix = getVersionPrefix(version);
                const path = isInternal
                    ? `/embeddingindex${versionPrefix}/batch-remote-index-file/json`
                    : `/index${versionPrefix}/batch-remote-index-file/json`;
                await getAxios().post(path, {...meta, files}, {timeout: 5 * 1000});
            }
            catch (ex) {
                console.error(ex);
            }
            finally {
                yield {uploaded: chunks.indexOf(chunk) * limit + chunk.length};
            }
        }
    }
    catch (e) {
        console.error(e);
    }
}

export async function apiPostDeletedFileAndMarkedJobAsFinish(
    params: Parameters<typeof postRemoteIndexMetadata>[0] & Parameters<typeof apiPostFileForIndex>[0]
) {
    const repoInfo = {
        // eslint-disable-next-line camelcase
        repo_id: params.repo_id,
        repo: params.repo,
        branch: params.branch,
        device: params.device,
    };
    debug(`embedding(debug): start delete ${params.files.length} files, repoId=${repoInfo.repo_id}`);
    const deleteRemoteIndexFileTask = apiPostFileForIndex({
        ...repoInfo,
        cwd: params.cwd,
        files: params.files,
        version: params.version,
        cancelToken: params.cancelToken,
        scene: params.scene,
        limit: params.limit,
    });
    // eslint-disable-next-line no-constant-condition
    while (true) {
        const {done} = await deleteRemoteIndexFileTask.next();
        if (done) {
            break;
        }
    }
    debug(`embedding(debug): finish delete ${params.files.length} files, files=${params.files.length}`);
    postRemoteIndexMetadata({
        user: params.user,
        ...repoInfo,
        action: 'finish',
    }, params.version);
}
