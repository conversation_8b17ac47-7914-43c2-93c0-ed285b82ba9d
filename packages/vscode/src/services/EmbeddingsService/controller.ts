/* eslint-disable max-statements */
import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {getIndexerConfig, getRemoteIndexInfo, getRemoteIndexVersion} from '@/api/codeSearch';
import {error, info} from '@/common/outputChannel';
import {iocContainer} from '@/iocContainer';
import {CountUp} from '@/utils/countUp';
import {L10n} from '@/common/L10nProvider/L10n';
import {EmbeddingsServiceText} from '@/common/L10nProvider/constants';
import {getRepoHostAndNameMemoized} from '@/utils/git';
import {isSaaS} from '@/utils/features';
import {ComateStatusBar} from '../StatusBar';
import {PartialPrivatizationProvider} from '../PartialPrivatizationProvider';
import {EmbeddingsIndexProgressStore} from './IndexProgressStore';
import {
    EmbeddingFileExceedError,
    EmbeddingJob,
    EmbeddingJobDescription,
    FirstBuildEmbeddingJob,
    Internval24hEmbeddingJob,
    RebuildEmbeddingJob,
} from './EmbeddingJob';
import {computeRepoId} from './embeddingUtils';
import {EmbeddingJobManager} from './JobManager';

export const CMD_RECREATE_INDEX = 'baidu.comate.recreateIndex';

@injectable()
export class EmbeddingsController implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    private readonly embeddingJobManager: EmbeddingJobManager = new EmbeddingJobManager(
        iocContainer,
        'existSingleRunningJob'
    );
    private uploadFilesBranchChangeMonitoringInterval: number = 10;

    constructor(
        @inject(EmbeddingsIndexProgressStore) private readonly indexProgressStore: EmbeddingsIndexProgressStore,
        @inject(ComateStatusBar) private readonly statusBar: ComateStatusBar
    ) {
        this.disposables.push(
            vscode.commands.registerCommand(
                CMD_RECREATE_INDEX,
                async (opts: {command: 'recreate' | 'inquire'}) => {
                    const command = opts?.command || 'recreate';
                    const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                    if (!rootPath) {
                        return;
                    }

                    const isValidGitRepo = await getRepoHostAndNameMemoized(rootPath);
                    if (!isValidGitRepo) {
                        vscode.window.showWarningMessage(
                            '抱歉，当前目录不是一个有效的 Git 仓库，暂不支持更新代码索引!'
                        );
                        return {progress: -1};
                    }

                    const repoId = await computeRepoId(rootPath);
                    if (!repoId) {
                        info('embeddings(info): skip for failing to compute repoId');
                        return {progress: 100};
                    }
                    const existRepoJob = await this.embeddingJobManager.getExistRunningJob(repoId);
                    info(`embeddings(info): recreateWorkspaceIndex ${existRepoJob ? 'failed' : 'success'}`);
                    if (command === 'recreate') {
                        if (existRepoJob) {
                            return undefined;
                        }
                        else {
                            const job = this.embeddingJobManager.createJob(
                                RebuildEmbeddingJob,
                                rootPath,
                                repoId
                            );
                            job.addListener(this.updateIndexBuildProgress.bind(this));
                            await job.execute();
                        }
                    }
                    else if (command === 'inquire') {
                        const {progress} = await this.getProgress(rootPath);
                        return {progress};
                    }
                    return {progress: 100};
                }
            )
        );
    }

    private readonly intervals: Record<string, NodeJS.Timer> = {};
    async start() {
        const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        try {
            const isValidGitRepo = await getRepoHostAndNameMemoized(rootPath!);
            if (!isValidGitRepo) {
                info('embeddings(info): skip for invalid git repo');
                return;
            }
            const repoId = await computeRepoId(rootPath);
            if (!repoId || !rootPath) {
                info('embeddings(info): skip for failing to compute repoId');
                return;
            }
            const version = await getRemoteIndexVersion(repoId);
            let job: EmbeddingJob | null = null;
            if (version === 'v1') {
                const {data} = await getRemoteIndexInfo(repoId, version);
                if (!data?.length) {
                    info(`embeddings(info): create FirstBuildEmbeddingJob success repoId=${repoId}`);
                    job = this.embeddingJobManager.createJob(
                        FirstBuildEmbeddingJob,
                        rootPath,
                        repoId
                    );
                    job.addListener(this.updateIndexBuildProgress.bind(this));
                    await job.execute();
                    return;
                }
            }

            if (await this.satisfyIndexUpdateInterval(repoId)) {
                info(`embeddings(info): create Internval24hEmbeddingJob success repoId=${repoId}`);
                job = this.embeddingJobManager.createJob(
                    Internval24hEmbeddingJob,
                    rootPath,
                    repoId,
                    version
                );
                job.addListener(this.updateIndexBuildProgress.bind(this));
                await job.execute();
            }
            await this.updateConfig();
            this.intervals.repoIdInterval = setInterval(
                async () => {
                    const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                    const newRepoId = await computeRepoId(rootPath);
                    if (newRepoId && newRepoId !== repoId) {
                        job?.cancel();
                        clearInterval(this.intervals.repoIdInterval);
                        clearInterval(this.intervals.generalInterval);
                        this.start();
                    }
                },
                1000 * this.uploadFilesBranchChangeMonitoringInterval // 默认每 10s 检查一次
            );

            // 更新索引定时任务
            this.intervals.generalInterval = setInterval(
                async () => {
                    if (job?.description.running) {
                        return;
                    }
                    if (await this.satisfyIndexUpdateInterval(repoId)) {
                        job = this.embeddingJobManager.createJob(
                            Internval24hEmbeddingJob,
                            rootPath,
                            repoId,
                            version
                        );
                        job.addListener(this.updateIndexBuildProgress.bind(this));
                        job.execute();
                    }
                },
                1000 * 60 * 60 // 每 1 小时执行一次
            );
        }
        catch (ex) {
            if (ex instanceof EmbeddingFileExceedError) {
                vscode.window.showWarningMessage(ex.message);
                return;
            }
            error(`embeddings(error): init embedding job failed rootPath=${rootPath}`);
        }
    }

    async getProgress(rootPath: string) {
        const job = this.embeddingJobManager.runningJobs.find(job => job.description.workspacePath === rootPath);
        if (job) {
            return {progress: job.description.progress};
        }

        try {
            const isValidGitRepo = await getRepoHostAndNameMemoized(rootPath);
            const repoId = await computeRepoId(rootPath);
            if (!repoId || !isValidGitRepo) {
                throw new Error('Unable to compute repoId for the give root path');
            }
            const version = await getRemoteIndexVersion(repoId);
            const {progress} = await getRemoteIndexInfo(repoId, version);
            return {progress};
        }
        catch (ex) {
            return {progress: 100};
        }
    }

    private async satisfyIndexUpdateInterval(repoId: string) {
        const lastUploadTime = await this.indexProgressStore.getLastUploadTime(repoId) ?? 0;
        info('embeddings(info): last update time:', new Date(lastUploadTime).toString());
        const indexerConfig = await getIndexerConfig();
        const minUploadInterval = 1000 * 60 * Math.max(indexerConfig.config.index_interval_minute || 5, 5);
        const timeSinceLastUpdate = Date.now() - lastUploadTime;
        if (timeSinceLastUpdate <= minUploadInterval) {
            const wait = Math.ceil((minUploadInterval - timeSinceLastUpdate) / (1000 * 60));
            info(`(embeddings) skip updating for not reaching the minimum update interval, ${wait} minutes to go`);
            return false;
        }
        return true;
    }

    private readonly countUp = new CountUp({
        decimal: 1,
        frameElapsedTime: 2000,
        onUpdate: value => {
            const progress = Number(value);
            if (progress <= this.previousProgress) {
                return;
            }
            this.previousProgress = progress;
            this.statusBar.setText(
                `${L10n.t(EmbeddingsServiceText.PROGRESS)}...(${progress.toFixed(1)}%)`,
                L10n.t(EmbeddingsServiceText.PROGRESS_TOOLTIP)
            );

            if (progress === 100) {
                this.countUp.reset(0);
                this.statusBar.forceNormal();
                this.previousProgress = 0;
            }
        },
    });
    private previousProgress = 0;
    private updateIndexBuildProgress(ev: EmbeddingJobDescription) {
        this.countUp.update(ev.progress);
    }

    async updateConfig() {
        if (isSaaS) {
            const partialPrivatizationProvider = iocContainer.get(PartialPrivatizationProvider);
            this.uploadFilesBranchChangeMonitoringInterval = (await partialPrivatizationProvider.getCommonConfig(
                'uploadFilesBranchChangeMonitoringInterval'
            )) ?? 10;
        }
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
    }
}
