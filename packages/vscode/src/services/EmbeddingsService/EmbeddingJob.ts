/* eslint-disable max-statements */
/* eslint-disable complexity */
import {join} from 'node:path';
import EventEmitter from 'node:events';
import * as vscode from 'vscode';
import {Container} from 'inversify';
import {globby} from 'globby';
import axios, {CancelTokenSource} from 'axios';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {
    IndexerConfig,
    getIndexerConfig,
    getRemoteIndexInfo,
    postRemoteIndexMetadata,
} from '@/api/codeSearch';
import {isFileExist} from '@/utils/fs';
import {L10n} from '@/common/L10nProvider/L10n';
import {EmbeddingsServiceText} from '@/common/L10nProvider/constants';
import {sleep} from '@/utils/common';
import {UserService} from '../UserService';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {FileMeta, apiPostDeletedFileAndMarkedJobAsFinish, apiPostFileForIndex} from './api';
import {EmbeddingsIndexProgressStore} from './IndexProgressStore';
import {EmbeddingsWorkspaceMetadata, createFileIndexPatchAction, getMetadata} from './embeddingUtils';

export interface Description {
    createdTime: number;
}

abstract class Job<T extends Description> {
    abstract get description(): T;
    abstract get progress(): number;
    abstract get running(): boolean;
    abstract execute(): void;
    abstract cancel(): void;
    abstract addListener(listener: (ev: T) => void): this;
}

enum JobStatus {
    READY,
    RUNNING,
    CANCELLED,
    DONE,
}
export interface EmbeddingJobDescription extends Description {
    repoId: string;
    workspacePath: string;
    running: boolean;
    progress: number;
    scene: 'common' | 'firstBuild' | 'reBuild';
    version: 'v1' | 'v2';
}

export class EmbeddingFileExceedError extends Error {
    threshold: number;
    constructor(message: string, threshold: number) {
        super(message);
        this.threshold = threshold;
    }
}

export class EmbeddingJob implements Job<EmbeddingJobDescription> {
    private readonly createdTime: number = Date.now();
    private readonly repoId: string;
    private readonly workspacePath: string;
    private readonly cancelToken: vscode.CancellationTokenSource;
    private readonly axiosCancelTokenSource: CancelTokenSource;
    private readonly ctx: Container;
    private readonly eventEmitter = new EventEmitter();
    private internalProgress: number = 0;
    private status: JobStatus = JobStatus.READY;

    private readonly opts: Pick<EmbeddingJobDescription, 'scene' | 'version'>;
    constructor(
        ctx: Container,
        workspacePath: string,
        repoId: string,
        opts: Pick<EmbeddingJobDescription, 'scene' | 'version'>
    ) {
        this.workspacePath = workspacePath;
        this.repoId = repoId;
        this.opts = opts;
        this.ctx = ctx;
        this.axiosCancelTokenSource = axios.CancelToken.source();
        this.cancelToken = new vscode.CancellationTokenSource();
    }

    // static async getExistJob(repoId: string) {
    //     const store = await getExtensionContextAsync();
    //     const jobs = store.globalState.get<Record<string, EmbeddingJobDescription>>('embeddingJobs');
    //     return jobs?.[repoId];
    // }

    async setEmbeddingJobDescriptionToGlobal() {
        // const store = await getExtensionContextAsync();
        // const jobs = store.globalState.get<Record<string, EmbeddingJobDescription>>('embeddingJobs');
        // store.globalState.update('embeddingJobs', {...jobs, [this.repoId]: this.description});
    }

    get running(): boolean {
        return this.status === JobStatus.RUNNING;
    }

    get progress(): number {
        return Math.min(this.internalProgress, 100);
    }

    get description(): EmbeddingJobDescription {
        return {
            createdTime: this.createdTime,
            progress: Math.floor(this.progress),
            workspacePath: this.workspacePath,
            running: this.running,
            repoId: this.repoId,
            scene: this.opts.scene,
            version: this.opts.version,
        };
    }

    /**
     * 每个Job有两个定时器
     * 一个定时器是任务开始执行后，定时向外广播progress
     * 一个定时器是任务执行过程中，轮训服务端进度，往job里写progress
     */
    private readonly intervals: NodeJS.Timer[] = [];

    get emitProgressInterval() {
        return this.intervals[0];
    }

    set emitProgressInterval(interval: NodeJS.Timer) {
        this.intervals[0] = interval;
    }

    addListener(listener: (ev: EmbeddingJobDescription) => void) {
        this.eventEmitter.addListener('progress', listener);
        return this;
    }

    private async buildIndex(files: string[], metadata: EmbeddingsWorkspaceMetadata, config: IndexerConfig) {
        const [username] = await this.ctx.get<UserService>(UserService).getCurrentUser();

        const repoInfo = {
            user: username,
            // eslint-disable-next-line camelcase
            repo_id: this.repoId,
            repo: metadata.project!,
            branch: metadata.branch,
            device: metadata.device!,
            cwd: this.description.workspacePath,
            cancelToken: this.axiosCancelTokenSource,
            version: this.description.version,
            scene: this.description.scene,
            limit: config.config.concurrent_file_count || 10,
        };
        await postRemoteIndexMetadata(repoInfo, this.description.version);
        const indexInfo = await getRemoteIndexInfo(this.repoId, this.description.version);
        const previousIndexData = indexInfo.data ?? [];
        const indexMap = new Map(previousIndexData.map(item => [item.filepath, item.sha1]));
        // 启动后就设置上传时间，IDE重启后不支持续传，可通过系统命令，重建索引
        // 若后续要支持续传，增加description里的`uploadedFiles/files`两个字段，启动时可读取上一次的description队列进行续传
        this.ctx.get<EmbeddingsIndexProgressStore>(EmbeddingsIndexProgressStore).updateUploadTime(
            this.description.repoId
        );

        const fileInfos = [];
        for await (const relativePath of files) {
            if (this.cancelToken.token.isCancellationRequested) {
                return;
            }
            const oldFileHash = indexMap.get(relativePath);
            const absolutePath = join(this.description.workspacePath, relativePath);
            const action = await createFileIndexPatchAction(absolutePath, oldFileHash, config);
            if (action) {
                fileInfos.push({path: relativePath, sha1: action.sha1, action: action.action});
            }
        }

        const batchUpdate = apiPostFileForIndex({...repoInfo, files: fileInfos});
        for await (const progress of batchUpdate) {
            // @进度计算规则1：前50%由上传文件决定，已上传数/总文件数
            this.internalProgress = 50 * (progress.uploaded / fileInfos.length);
        }

        const removedFiles: FileMeta[] = [];
        for await (const {filepath, sha1} of previousIndexData) {
            const existed = await isFileExist(join(this.description.workspacePath, filepath));
            if (!existed) {
                removedFiles.push({sha1, path: filepath, action: 'delete'});
            }
        }

        // @进度计算规则3：删除部分不考虑进度, 但是finish需要等待删除处理完
        apiPostDeletedFileAndMarkedJobAsFinish({...repoInfo, files: removedFiles});

        while (
            !this.cancelToken.token.isCancellationRequested
            && fileInfos.length > 0
            && this.internalProgress < 95
        ) {
            const {progress} = await getRemoteIndexInfo(this.repoId, this.description.version);
            this.internalProgress = 50 + progress * 0.5;
            await sleep(1000);
        }
        this.internalProgress = 100;
        this.status = JobStatus.DONE;
        this.ctx.get<PerformanceLogProvider>(PerformanceLogProvider).log({
            plugin: 'comate',
            skill: 'embdedding-build',
            duration: Date.now() - this.description.createdTime,
            type: this.description.scene,
        });
        // 保证定时器把最后一帧消息发出去
        await sleep(1000);
        this.cancel();
    }

    async execute() {
        try {
            this.status = JobStatus.RUNNING;
            this.emitProgressInterval = setInterval(
                () => {
                    this.setEmbeddingJobDescriptionToGlobal();
                    const progress = this.description.progress;
                    this.eventEmitter.emit('progress', {progress});
                    if (progress === 100) {
                        // 保证只发送一次100的进度事件，防止界面上从100重置到0后又变成100
                        clearInterval(this.emitProgressInterval);
                    }
                },
                1000
            );

            const config = await getIndexerConfig();
            const metadata = await getMetadata(this.description.workspacePath);
            if (!metadata.project || !metadata.device) {
                throw new Error('Unable to update: missing project or device');
            }

            if (this.cancelToken.token.isCancellationRequested) {
                return;
            }

            const files = await globby(config.white_pattern, {
                gitignore: true,
                cwd: this.description.workspacePath,
                ignore: config.black_pattern,
            });

            const maxFiles = config.config.max_file_count;
            if (files.length > maxFiles) {
                throw new EmbeddingFileExceedError(
                    L10n.t(EmbeddingsServiceText.MAXIMUM_FILES, maxFiles),
                    maxFiles
                );
            }

            this.buildIndex(files, metadata, config);
        }
        catch (ex) {
            this.cancel();
            throw ex;
        }
    }

    cancel(): void {
        clearInterval(this.emitProgressInterval);
        this.cancelToken.cancel();
        this.axiosCancelTokenSource.cancel();
        this.internalProgress = 100;
        this.status = JobStatus.CANCELLED;
        this.eventEmitter.emit('progress', this.description);
    }
}

/** 首次构建的任务，上线后默认是v2版本 */
export class FirstBuildEmbeddingJob extends EmbeddingJob {
    constructor(ctx: Container, rootPath: string, repoId: string) {
        super(ctx, rootPath, repoId, {scene: 'firstBuild', version: 'v2'});
    }
}

/** 24h定时任务 */
export class Internval24hEmbeddingJob extends EmbeddingJob {
    constructor(ctx: Container, rootPath: string, repoId: string, version: 'v1' | 'v2') {
        super(ctx, rootPath, repoId, {scene: 'common', version});
    }
}

/** 手动触发的重建任务，默认是v2版本 */
export class RebuildEmbeddingJob extends EmbeddingJob {
    constructor(ctx: Container, rootPath: string, repoId: string) {
        super(ctx, rootPath, repoId, {scene: 'reBuild', version: 'v2'});
    }

    static async getRebuildJobProgress(repoId: string) {
        const store = await getExtensionContextAsync();
        const jobs = store.globalState.get<Record<string, EmbeddingJobDescription>>('embeddingJobs');
        if (jobs?.[repoId].running) {
            return jobs[repoId].progress;
        }

        return 100;
    }
}
