import * as path from 'path';
import * as vscode from 'vscode';
import {CodeChunk} from '../../common/types';

export async function getResultWithContent(data: CodeChunk[], basePath?: string) {
    const resultWithContent = await Promise.all(data.map(async item => {
        if (item.content) {
            return item;
        }
        try {
            if (!basePath) {
                return undefined;
            }
            const filePath = path.join(basePath, item.path);
            const doc = await vscode.workspace.openTextDocument(filePath);
            const content = doc.getText(
                new vscode.Range(
                    item.contentStart.line,
                    0,
                    item.contentEnd.line,
                    Number.MAX_SAFE_INTEGER
                )
            );
            return {
                ...item,
                content,
            };
        }
        catch (e) {
            return undefined;
        }
    }));
    return resultWithContent.filter(item => item !== undefined) as CodeChunk[];
}
