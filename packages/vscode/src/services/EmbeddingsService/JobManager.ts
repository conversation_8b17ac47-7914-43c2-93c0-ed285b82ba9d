import {Container} from 'inversify';
import {EmbeddingJob, FirstBuildEmbeddingJob, RebuildEmbeddingJob, Internval24hEmbeddingJob} from './EmbeddingJob';

export class EmbeddingJobManager {
    private readonly jobs: EmbeddingJob[] = [];
    constructor(
        private readonly ctx: Container,
        /** 创建任务时的策略，`existSingleRunningJob` 表示只允许单个任务存在，`everyRepoIdExistedSingleJob` 表示允许多个任务存在 */
        private readonly createJobStrategy: 'existSingleRunningJob' | 'everyRepoIdExistedSingleJob'
    ) {
    }

    get runningJobs() {
        return this.jobs.filter(job => job.running);
    }

    getExistRunningJob(repoId: string) {
        return this.runningJobs.find(job => job.description.repoId === repoId);
    }

    createJob(job: typeof FirstBuildEmbeddingJob, rootPath: string, repoId: string): FirstBuildEmbeddingJob;
    createJob(job: typeof RebuildEmbeddingJob, rootPath: string, repoId: string): RebuildEmbeddingJob;
    createJob(
        job: typeof Internval24hEmbeddingJob,
        rootPath: string,
        repoId: string,
        version: 'v1' | 'v2'
    ): Internval24hEmbeddingJob;
    createJob(
        factory: typeof FirstBuildEmbeddingJob | typeof RebuildEmbeddingJob | typeof Internval24hEmbeddingJob,
        rootPath: string,
        repoId: string,
        version?: 'v1' | 'v2'
    ) {
        // 取消所有正在运行的job
        if (this.createJobStrategy === 'existSingleRunningJob') {
            for (const job of this.runningJobs) {
                job.cancel();
            }
        }
        // 取消所有正在运行的job中repoId和创建任务相同的job
        else if (this.createJobStrategy === 'everyRepoIdExistedSingleJob') {
            for (const job of this.runningJobs) {
                if (job.description.repoId === repoId) {
                    job.cancel();
                }
            }
        }

        if (factory === FirstBuildEmbeddingJob) {
            const job = new FirstBuildEmbeddingJob(this.ctx, rootPath, repoId);
            this.jobs.push(job);
            return job;
        }
        else if (factory === RebuildEmbeddingJob) {
            const job = new RebuildEmbeddingJob(this.ctx, rootPath, repoId);
            this.jobs.push(job);
            return job;
        }
        else {
            const job = new Internval24hEmbeddingJob(this.ctx, rootPath, repoId, version!);
            this.jobs.push(job);
            return job;
        }
    }
}
