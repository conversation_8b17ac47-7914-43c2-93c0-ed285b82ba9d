import * as path from 'path';
import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {CodeChunk} from '../../common/types';
import {getEmbeddingMatches} from '../../api/codeSearch';
import {UserService} from '../UserService';
import {computeRepoIdByMetadata, getMetadata} from './embeddingUtils';

@injectable()
export class EmbeddingsService {
    constructor(@inject(UserService) private readonly userService: UserService) {}

    private async getRepoConfig(workspacePath: string) {
        const metadata = await getMetadata(workspacePath);
        const repoId = computeRepoIdByMetadata(metadata);
        return {metadata, repoId};
    }

    async search(
        query: string[],
        workspacePath: string,
        searchPath?: string[]
    ): Promise<{data: CodeChunk[], queryType: string}> {
        const {metadata, repoId} = await this.getRepoConfig(workspacePath);
        if (!repoId) {
            throw new Error('unable to compute repoId');
        }
        const [user] = await this.userService.getCurrentUser();
        const params = {
            query,
            path: searchPath,
            // 当前场景repo仅为当前代码库
            repo: [repoId],
            needRank: true,
            user,
        };
        const {data, queryType} = await getEmbeddingMatches(params);
        const resultWithContent = await Promise.all(data.map(async item => {
            const filePath = path.join(metadata.uploadBasePath, item.path);
            try {
                const doc = await vscode.workspace.openTextDocument(filePath);
                const content = doc.getText(
                    new vscode.Range(
                        item.contentStart.line,
                        0,
                        item.contentEnd.line,
                        Number.MAX_SAFE_INTEGER
                    )
                );
                return {
                    ...item,
                    content,
                };
            }
            catch (e) {
                return undefined;
            }
        }));
        return {data: resultWithContent.filter(item => item !== undefined) as CodeChunk[], queryType};
    }
}
