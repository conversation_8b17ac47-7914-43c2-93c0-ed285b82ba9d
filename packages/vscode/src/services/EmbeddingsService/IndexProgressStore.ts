import {injectable} from 'inversify';
import {getExtensionContextAsync} from '@/utils/extensionContext';

@injectable()
export class EmbeddingsIndexProgressStore {
    async getLastUploadTime(repoId: string) {
        const context = await getExtensionContextAsync();
        const records = context.globalState.get<Record<string, number | undefined>>(
            'embeddings_upload_records',
            {}
        );
        return records[repoId];
    }

    async updateUploadTime(repoId: string) {
        const context = await getExtensionContextAsync();
        const records = context.globalState.get<Record<string, number | undefined>>(
            'embeddings_upload_records',
            {}
        );
        context.globalState.update('embeddings_upload_records', {
            ...records,
            [repoId]: Date.now(),
        });
    }
}
