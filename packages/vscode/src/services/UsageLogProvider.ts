/* eslint-disable max-len */
import os from 'node:os';
import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import * as si from 'systeminformation';
import axios from 'axios';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {getEnginePid} from '@/utils/engineExec';
import {getIdeName} from '@/common/Fetcher';
import {debug} from '@/common/outputChannel';
import {ConfigKey, VSCodeConfigProvider} from './ConfigProvider';
import {UserActivityMonitor} from './UserActivityMonitor';
import {KernelProvider} from './KernelProvider';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu-int.com',
    saas: 'https://comate.baidu.com',
    poc: 'https://comate.baidu.com',
};

const RECORD_INTERVAL = 30 * 1000; // 30 seconds
const SEND_INTERVAL = 60 * 60 * 1000; // 1 hour

interface Measurement {
    mainCPU: number;
    mainMEM: number;
    engineCPU: number;
    engineMEM: number;
    timestamp: number;
    userActivity: boolean;
    securityCPU: number;
    securityMEM: number;
    clusterCPU: number;
    clusterMEM: number;
}

const getCPUMetrics = (measurements_: number[]) => {
    const measurements = measurements_.filter(m => m >= 0);
    const sum = measurements.reduce((acc, cur) => acc + cur, 0);
    const count = measurements.length;

    return {
        avarage: sum / count,
        max: Math.max(...measurements),
        min: Math.min(...measurements),
        lte5: measurements.filter(m => m <= 5).length / count,
        gt5: measurements.filter(m => m > 5).length / count,
        gt10: measurements.filter(m => m > 10).length / count,
        gt20: measurements.filter(m => m > 20).length / count,
        gt30: measurements.filter(m => m > 30).length / count,
        gt40: measurements.filter(m => m > 40).length / count,
        gt50: measurements.filter(m => m > 50).length / count,
        gt60: measurements.filter(m => m > 60).length / count,
        gt70: measurements.filter(m => m > 70).length / count,
        gt80: measurements.filter(m => m > 80).length / count,
        gt90: measurements.filter(m => m > 90).length / count,
        gt100: measurements.filter(m => m > 100).length / count,
        count,
    };
};

const getMEMMetrics = (measurements_: number[]) => {
    const measurements = measurements_.filter(m => m >= 0);
    const sum = measurements.reduce((acc, cur) => acc + cur, 0);
    const count = measurements.length;

    return {
        avarage: sum / count,
        max: Math.max(...measurements),
        min: Math.min(...measurements),
        lte200: measurements.filter(m => m <= 200).length / count,
        gt200: measurements.filter(m => m > 200).length / count,
        gt300: measurements.filter(m => m > 300).length / count,
        gt400: measurements.filter(m => m > 400).length / count,
        gt500: measurements.filter(m => m > 500).length / count,
        gt750: measurements.filter(m => m > 750).length / count,
        gt1000: measurements.filter(m => m > 1000).length / count,
        gt1250: measurements.filter(m => m > 1250).length / count,
        gt1500: measurements.filter(m => m > 1500).length / count,
        gt2000: measurements.filter(m => m > 2000).length / count,
        gt3000: measurements.filter(m => m > 3000).length / count,
        gt4000: measurements.filter(m => m > 4000).length / count,
        count,
    };
};

@injectable()
export class UsageLogProvider {
    startTime = Date.now();
    recordIntervalId: NodeJS.Timeout | null = null;
    sendIntervalId: NodeJS.Timeout | null = null;
    measurements: Measurement[] = [];
    userActivityMonitor: UserActivityMonitor | null = null;

    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider
    ) {
        getExtensionContextAsync().then(context => {
            this.userActivityMonitor = new UserActivityMonitor(context);
            this.startRecording();
        });
    }

    dispose() {
        if (this.recordIntervalId) {
            clearInterval(this.recordIntervalId);
        }
        if (this.sendIntervalId) {
            clearInterval(this.sendIntervalId);
        }
    }

    private startRecording() {
        // Record measurements every 30 seconds
        this.recordIntervalId = setInterval(
            async () => {
                const info = await this.getAllProcessInfo();
                if (info) {
                    const measurement = {
                        ...info,
                        timestamp: Date.now(),
                        userActivity: !!this.userActivityMonitor?.isUserActive(),
                    };
                    debug(`usage log: ${JSON.stringify(measurement)}`);
                    this.measurements.push(measurement);
                }
            },
            RECORD_INTERVAL
        );

        // Send average measurements every hour
        this.sendIntervalId = setInterval(
            () => {
                this.sendMetrics();
            },
            SEND_INTERVAL
        );
    }

    private getMetrics(filter: (item: Measurement) => boolean = () => true) {
        const measurements = this.measurements.filter(filter);

        if (measurements.length === 0) {
            return {};
        }

        return {
            totalCPU: getCPUMetrics(measurements.map(m => {
                if (m.mainCPU < 0 || m.engineCPU < 0) {
                    return -1;
                }

                return m.mainCPU + m.engineCPU;
            })),
            totalMEM: getMEMMetrics(measurements.map(m => {
                if (m.mainMEM < 0 || m.engineMEM < 0) {
                    return -1;
                }

                return m.mainMEM + m.engineMEM;
            })),
            mainCPU: getCPUMetrics(measurements.map(m => m.mainCPU)),
            mainMEM: getMEMMetrics(measurements.map(m => m.mainMEM)),
            engineCPU: getCPUMetrics(measurements.map(m => m.engineCPU)),
            engineMEM: getMEMMetrics(measurements.map(m => m.engineMEM)),
            securityCPU: getCPUMetrics(measurements.map(m => m.securityCPU)),
            securityMEM: getMEMMetrics(measurements.map(m => m.securityMEM)),
            clusterCPU: getCPUMetrics(measurements.map(m => m.clusterCPU)),
            clusterMEM: getMEMMetrics(measurements.map(m => m.clusterMEM)),
            duration: (measurements[measurements.length - 1]?.timestamp || 0) - (measurements[0]?.timestamp || 0),
        };
    }

    private async sendMetrics() {
        const metrics = this.getMetrics();
        await this.log({
            all: metrics,
            active: this.getMetrics(m => m.userActivity),
            inactive: this.getMetrics(m => !m.userActivity),
        });
        // Clear measurements after sending
        this.measurements = [];
    }

    async getProcessInfoByPid(pid?: number) {
        try {
            if (!pid) {
                return {
                    cpu: -1,
                    mem: -1,
                };
            }

            const [proce, memtotal] = await Promise.all([si.processes(), si.mem()]);
            const stats = proce.list.find(p => p.pid === pid);
            if (!stats) {
                return {
                    cpu: -1,
                    mem: -1,
                };
            }
            const cpu = stats.cpu;
            const mem = stats.mem * memtotal.total / 1024 / 1024 / 100;

            return {
                cpu,
                mem,
            };
        }
        catch (e) {
            console.error(e);
        }

        return {
            cpu: -1,
            mem: -1,
        };
    }

    async getAllProcessInfo() {
        const pid = process.pid;
        const enginePid = getEnginePid();
        const securityPid = this.kernelProvider.pluginsPid.security;
        const clusterPid = this.kernelProvider.pluginsPid.cluster;
        try {
            const [main, engine, security, cluster] = await Promise.all([
                this.getProcessInfoByPid(pid),
                this.getProcessInfoByPid(enginePid),
                this.getProcessInfoByPid(securityPid),
                this.getProcessInfoByPid(clusterPid),
            ]);

            return {
                mainCPU: main.cpu,
                mainMEM: main.mem,
                engineCPU: engine.cpu,
                engineMEM: engine.mem,
                securityCPU: security.cpu,
                securityMEM: security.mem,
                clusterCPU: cluster.cpu,
                clusterMEM: cluster.mem,
            };
        }
        catch (e) {
            console.error(e);
        }

        return {
            mainCPU: -1,
            mainMEM: -1,
            engineCPU: -1,
            engineMEM: -1,
            securityCPU: -1,
            securityMEM: -1,
            clusterCPU: -1,
            clusterMEM: -1,
        };
    }

    async log(logContent: any) {
        try {
            const context = await getExtensionContextAsync();
            const logHost = BASE_URL_MAPPING[$features.PLATFORM];

            debug(`sending usage log: ${Date.now() - this.startTime}, ${JSON.stringify(logContent)}`);

            await axios
                .post(
                    `${logHost}/logger/usage.log`,
                    {
                        machineId: vscode.env.machineId,
                        osPlatform: os.platform(),
                        osArch: os.arch(),
                        osRelease: os.release(),
                        ide: getIdeName(),
                        ideVersion: vscode.version,
                        pluginVersion: context?.extension.packageJSON.version,
                        pluginConfig: {
                            inlineSuggestionMode: this.configProvider.getConfig<string>(ConfigKey.InlineSuggestionMode)
                                ?? '',
                        },
                        username: this.configProvider.getConfig<string>(ConfigKey.Username) ?? '',
                        license: this.configProvider.getConfig<string>(ConfigKey.Key) ?? '',
                        timestamp: Date.now(),
                        scope: $features.PLATFORM,
                        performanceNow: Date.now() - this.startTime,
                        ...logContent,
                    },
                    {
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }
                );
        }
        catch (e) {
            console.error(e);
        }
    }
}
