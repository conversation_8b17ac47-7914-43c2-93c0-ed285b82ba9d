import * as vscode from 'vscode';
import Parser from 'web-tree-sitter';
import {extractMarkdownCodeBlocks} from '../../utils/common';
import {
    getAncestor,
    getFirstPrecedingComment,
    isTopLevelNode,
    TreeSitterLanguage,
} from '../../utils/treeSitterUtils';
import {MAX_CODE_CONTEXT_LENGTH} from './constant';

export function extractLastCodeBlock(content: string) {
    const blocks = extractMarkdownCodeBlocks(content);
    const last = blocks.pop();
    return last ?? content;
}

export function getFunctionCallContext(node: Parser.SyntaxNode, languageId: TreeSitterLanguage) {
    const ancestor = getAncestor(languageId, node);
    const insertIndex = ancestor && isTopLevelNode(languageId, ancestor.parent)
        ? (getFirstPrecedingComment(ancestor) ?? ancestor)?.startIndex
        : undefined;
    if (ancestor && ancestor.text.length < MAX_CODE_CONTEXT_LENGTH) {
        return {code: ancestor.text, insertIndex};
    }

    const startIndex = node.startIndex;
    const fullText = node.tree.rootNode.text;
    const suffix = fullText.slice(startIndex, startIndex + MAX_CODE_CONTEXT_LENGTH / 2);
    const remainingLength = MAX_CODE_CONTEXT_LENGTH - suffix.length;
    const prefix = fullText.slice(Math.max(startIndex - remainingLength, 0), startIndex);
    return {code: prefix + suffix, insertIndex};
}

export function findMaxContextParent(node: Parser.SyntaxNode, maxLength: number) {
    let current = node;
    while (current.parent && current.parent.text.length < maxLength) {
        current = current.parent;
    }
    return current;
}
export function getDiagnosticsMergedRange(document: vscode.TextDocument, diagnostics: readonly vscode.Diagnostic[]) {
    const ranges = diagnostics.map(v => {
        const startIndex = document.offsetAt(v.range.start);
        const endIndex = document.offsetAt(v.range.end);
        return [startIndex, endIndex];
    });
    // eslint-disable-next-line no-undef-init
    let start: number | undefined = undefined;
    // eslint-disable-next-line no-undef-init
    let end: number | undefined = undefined;
    ranges.forEach(range => {
        if (start === undefined || start > range[0]) {
            start = range[0];
        }
        if (end === undefined || end < range[1]) {
            end = range[1];
        }
    });
    const startPos = document.positionAt(start ?? 0);
    const endPos = document.positionAt(end ?? 0);
    return [startPos, endPos];
}

export function getNodeByRange(start: number, end: number, node: Parser.SyntaxNode) {
    let current = node.descendantForIndex(start);
    while (current && current.parent && current.endIndex < end) {
        current = current.parent;
    }
    return current.endIndex >= end ? current : undefined;
}

export function isUndefinedDiagnostic(diagnostic: vscode.Diagnostic) {
    return (diagnostic.source === 'ts' && diagnostic.message.startsWith('Cannot find name '))
        || (diagnostic.source === 'Pylance' && diagnostic.message.endsWith('is not defined'))
        || (diagnostic.source === 'compiler' && diagnostic.message.startsWith('undefined: '));
}
