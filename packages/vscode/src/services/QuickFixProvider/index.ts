import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import {
    ApplyStatus,
    BuiltinAgent,
    ContextType,
    EventMessage,
    KnowledgeList,
    WebviewAgentConversationType,
} from '@shared/protocols';
import {SlashType} from '@shared/constants';
import {L10n} from '@/common/L10nProvider/L10n';
import {QuickFixText} from '@/common/L10nProvider/constants';
import {vscodeCommands} from '@/utils/vscodeComands';
import {stripExtraIndent} from '@/utils/indent';
import {isInternal} from '@/utils/features';
import {addMarkdownCodeBlock} from '../../utils/common';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {ChatBaseProvider} from '../ChatBaseProvider';
import {DiffProvider} from '../DiffProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {promptForFixAll} from './prompts';
import {CMD_FIX_ALL} from './constant';
import {getDiagnosticsMergedRange} from './utils';

@injectable()
export class QuickFixProvider extends ChatBaseProvider implements vscode.CodeActionProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'cpp',
        'javascript',
        'typescript',
        'javascriptreact',
        'jsx',
        'typescriptreact',
    ];
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider
    ) {
        super(diffProvider);
        this.disposables.push(
            vscode.languages.registerCodeActionsProvider(
                QuickFixProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this
            ),
            vscode.commands.registerCommand(
                CMD_FIX_ALL,
                this.fixAll.bind(this)
            )
        );
    }

    async provideCodeActions(
        document: vscode.TextDocument,
        _range: vscode.Range | vscode.Selection,
        context: vscode.CodeActionContext
    ) {
        const enableQuickFix = this.configProvider.getConfig<boolean>(ConfigKey.EnableQuickFix);
        if (enableQuickFix !== true) {
            return [];
        }

        const warningOrErrorDiagnostics = context.diagnostics.filter(v => (
            v.severity <= vscode.DiagnosticSeverity.Warning
            // @ts-expect-error 排除掉来自engine的修复
            && !v?.code?.fromEngine
        ));

        if (warningOrErrorDiagnostics.length > 0) {
            return this.getActions(document, warningOrErrorDiagnostics);
        }
        return [];
    }

    async getActions(document: vscode.TextDocument, allDiagnostics: readonly vscode.Diagnostic[]) {
        // @ts-expect-error 排除掉来自engine的修复
        const diagnostics = allDiagnostics.filter(diagnostic => !diagnostic?.code?.fromEngine);
        if (diagnostics.length === 0) {
            return [];
        }
        const actionForFixAll = this.getActionForFixAll(document, diagnostics);
        return actionForFixAll ? [actionForFixAll] : [];
    }

    getActionForFixAll(document: vscode.TextDocument, diagnostics: readonly vscode.Diagnostic[]) {
        const quickFixAction = new vscode.CodeAction(
            L10n.t(QuickFixText.ACTION),
            vscode.CodeActionKind.QuickFix
        );
        quickFixAction.isPreferred = false;
        quickFixAction.command = {
            title: quickFixAction.title,
            command: CMD_FIX_ALL,
            arguments: [document, diagnostics],
        };
        return quickFixAction;
    }

    async fixAll(document: vscode.TextDocument, diagnostics: readonly vscode.Diagnostic[]) {
        const message = promptForFixAll(diagnostics);
        const [start, end] = getDiagnosticsMergedRange(document, diagnostics);
        const firstLine = document.lineAt(start.line).text;
        const referCode = addMarkdownCodeBlock(
            stripExtraIndent(document.getText(new vscode.Range(start, end)), true, firstLine),
            document.languageId
        );
        const upperTenLineRange = new vscode.Range(
            Math.max(start.line - 10, 0),
            0,
            end.line + 1,
            Number.MAX_SAFE_INTEGER
        );
        const upperTenLine = addMarkdownCodeBlock(document.getText(upperTenLineRange));

        const editor = vscode.window.activeTextEditor;
        const diagnosticsFileDescription = editor
            ? `报错文件：\n${editor.document.uri.fsPath}:${diagnostics[0].range.start.line + 1}\n\n`
            : '';
        const prompt = `${message}\n\n${diagnosticsFileDescription}报错行代码：\n${referCode}\n\n报错行所在代码块：\n${upperTenLine}`;

        const debugAgentEnable = await this.isDebugAgentEnable();
        if (debugAgentEnable) {
            const success = await this.askDebugAgent(message, referCode, prompt);
            // 调用不成功的话回到 AutoWork，可能是 Webview 刚初始话，处理 Agent 相应的事件丢了
            if (!success) {
                this.askAutoWork(message, referCode, prompt);
            }
        }
        else {
            this.askAutoWork(message, referCode, prompt);
        }
    }


    private askAutoWork(
        message: string,
        referCode: string,
        prompt: string
    ) {
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_FIX_ALL});
        const knowledgeList: KnowledgeList[] = [
            {
                content: referCode,
                type: ContextType.CODE,
                id: '',
                name: '',
            },
            {
                path: '',
                type: ContextType.TERMINAL,
                id: '',
                name: '',
                content: '{"triggerType": "WAVE_LINE"}',
            },
        ];
        vscodeCommands.askAutoWork({
            agent: BuiltinAgent.Comate,
            query: `${message}\n报错行代码：`,
            prompt,
            slash: SlashType.AUTO_DEBUG,
            knowledgeList,
        });
    }

    private askDebugAgent(
        message: string,
        referCode: string,
        prompt: string
    ) {
        vscodeCommands.openChatPanel({source: 'debug', activeTabKey: 'AGENT'});
        return this.chatViewProvider.sendDataToWebview(
            EventMessage.AgentConversationAddFromIdeEvent,
            {
                conversationId: '',
                payload: {
                    query: `${message}\n\n报错行代码：`,
                    code: referCode,
                    customPrompt: prompt,
                    needsValidation: false,
                    platform: 'VSCODE',
                    contexts: {triggerType: 'WAVE_LINE', ideVersion: vscode.version},
                },
                messageType: 'add-conversation',
                conversationType: WebviewAgentConversationType.DebugBotConversation,
            }
        );
    }


    private async isDebugAgentEnable() {
        if (!isInternal) {
            return false;
        }
        if (!this.chatViewProvider.engineInitialized) {
            return false;
        }
        const agentConfigs = await this.configProvider.getAgentConfig();
        const approved = agentConfigs.applyStatus?.debugIntelligenceApplyStatus === ApplyStatus.Approved;
        return approved && agentConfigs.enableIntelligenceAgent?.enableDebugIntelligence;
    }


    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
