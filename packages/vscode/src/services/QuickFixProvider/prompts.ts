import * as vscode from 'vscode';

export function promptForUndefinedFunctionCall(name: string) {
    return `请根据代码猜测 ${name} 的作用并提供一个可能的实现\n`
        + `1. 只告诉我 ${name} 的实现，不需要解释，不需要其他内容\n`
        + '2. 返回结果需要符合调用逻辑\n'
        + `3. ${name}方法请带上函数注释\n`;
}

export function getDiagnosticCodeValue(diagnostic: vscode.Diagnostic) {
    if (!diagnostic.code) {
        return '';
    }
    if (typeof diagnostic.code === 'number' || typeof diagnostic.code === 'string') {
        return diagnostic.code;
    }
    return diagnostic.code.value;
}

export function buildDisplayMessage(diagnostic: vscode.Diagnostic) {
    const codeValue = getDiagnosticCodeValue(diagnostic);
    const code = codeValue ? `(${codeValue})` : '';
    const from = diagnostic.source ? `${diagnostic.source}${code}: ` : '';
    return from + diagnostic.message;
}

export function promptForFixAll(diagnostics: readonly vscode.Diagnostic[]) {
    const messages = diagnostics.map((v, index) => {
        return `${index + 1}. ${buildDisplayMessage(v)}`;
    });
    return `请分析以下代码问题并进行修复：\n${messages.join('\n')}`;
}

export function promptForFixMaxLen(diagnostic: vscode.Diagnostic) {
    const text = diagnostic.message.split('Maximum allowed is ')[1] ?? '';
    const max = text.replace(/\.$/, '') ?? 120;
    return `下面这段代码存在 \`${buildDisplayMessage(diagnostic)}\` 错误，请根据提示重构这段代码\n`
        + `1. 只需要修复 eslint(max-len) 问题，使得每行的代码字符长度小于 ${max}， 不需要其他优化\n`
        + '2. 不要改变原有代码的逻辑和功能\n'
        + '3. 告诉我修复后的代码，不需要行间的解释\n'
        + '4. 请返回 markdown 形式的结果\n';
}
