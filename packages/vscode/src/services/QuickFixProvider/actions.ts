import * as vscode from 'vscode';
import Parser from 'web-tree-sitter';
import {isFunctionCall, TreeSitterLanguage} from '../../utils/treeSitterUtils';
import {CMD_FIX_MAX_LEN, CMD_FIX_UNDEFINED_FUNCTION_CALL, FixableDiagnosticType} from './constant';
import {promptForFixMaxLen} from './prompts';
import {getFunctionCallContext, getNodeByRange, isUndefinedDiagnostic} from './utils';

function fixUndefinedFunctionCallAction(
    document: vscode.TextDocument,
    diagnostic: vscode.Diagnostic,
    root: Parser.SyntaxNode,
    languageId: TreeSitterLanguage
) {
    const node = root.descendantForPosition({
        row: diagnostic.range.start.line,
        column: diagnostic.range.start.character,
    });
    if (node && isFunctionCall(languageId, node)) {
        const name = node.text;
        const {code, insertIndex} = getFunctionCallContext(node, languageId);
        const quickFixAction = new vscode.CodeAction(
            `Comate 生成 '${name}'`,
            vscode.CodeActionKind.QuickFix
        );
        quickFixAction.isPreferred = false;

        quickFixAction.command = {
            title: quickFixAction.title,
            command: CMD_FIX_UNDEFINED_FUNCTION_CALL,
            arguments: [
                document,
                name,
                code,
                insertIndex,
            ],
        };
        return quickFixAction;
    }
    return undefined;
}

function fixMaxLenAction(document: vscode.TextDocument, diagnostic: vscode.Diagnostic, root: Parser.SyntaxNode) {
    if (diagnostic.range.start.line !== diagnostic.range.end.line) {
        return undefined;
    }
    const line = document.getText(diagnostic.range);
    const startOffset = document.offsetAt(diagnostic.range.start);
    const endOffset = document.offsetAt(diagnostic.range.end);
    const node = getNodeByRange(
        startOffset + line.length - line.trimStart().length,
        endOffset,
        root
    );
    if (!node) {
        return undefined;
    }
    const message = promptForFixMaxLen(diagnostic);
    const quickFixAction = new vscode.CodeAction(
        'Comate 修复 max-len 问题',
        vscode.CodeActionKind.QuickFix
    );
    quickFixAction.isPreferred = false;
    quickFixAction.command = {
        title: quickFixAction.title,
        command: CMD_FIX_MAX_LEN,
        arguments: [
            document,
            node,
            message,
        ],
    };
    return quickFixAction;
}

export function getDiagnosticsGroupActions(
    document: vscode.TextDocument,
    diagnostics: vscode.Diagnostic[],
    root: Parser.SyntaxNode,
    languageId: TreeSitterLanguage,
    type: string
) {
    if (type === FixableDiagnosticType.UndefinedFunctionCall) {
        const actions = [];
        const remaining = [];
        for (const item of diagnostics) {
            const action = fixUndefinedFunctionCallAction(
                document,
                item,
                root,
                languageId
            );
            if (action) {
                actions.push(action);
            }
            else {
                remaining.push(item);
            }
        }
        return {actions, remaining: remaining};
    }
    else if (type === FixableDiagnosticType.MaxLen) {
        if (diagnostics.length === 1) {
            const action = fixMaxLenAction(document, diagnostics[0], root);
            if (action) {
                return {actions: [action], remaining: []};
            }
        }
        // TODO: 需要考虑合并之类的，先不管了
        return {actions: [], remaining: diagnostics};
    }
    else {
        return {actions: [], remaining: diagnostics};
    }
}

export function groupDiagnosticsByType(diagnostics: readonly vscode.Diagnostic[]) {
    const res = new Map<FixableDiagnosticType, vscode.Diagnostic[]>();
    const others = [];
    for (const diagnostic of diagnostics) {
        if (isUndefinedDiagnostic(diagnostic)) {
            const pre = res.get(FixableDiagnosticType.UndefinedFunctionCall) ?? [];
            res.set(
                FixableDiagnosticType.UndefinedFunctionCall,
                [...pre, diagnostic]
            );
        }
        else if (
            diagnostic.source === 'eslint'
            && typeof diagnostic.code === 'object'
            && diagnostic.code.value === 'max-len'
        ) {
            const pre = res.get(FixableDiagnosticType.MaxLen) ?? [];
            res.set(
                FixableDiagnosticType.MaxLen,
                [...pre, diagnostic]
            );
        }
        else {
            others.push(diagnostic);
        }
    }
    res.set(FixableDiagnosticType.Others, others);
    return res;
}
