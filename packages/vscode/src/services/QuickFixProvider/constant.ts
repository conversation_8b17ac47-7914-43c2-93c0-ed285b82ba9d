export const CMD_FIX_MAX_LEN = 'baidu.comate.fixMaxLen';

export const CMD_FIX_UNDEFINED_FUNCTION_CALL = 'baidu.comate.fixUndefinedFunctionCall';

export const CMD_FIX_ALL = 'baidu.comate.fixAll';

export const MAX_CODE_CONTEXT_LENGTH = 1600;

export const MAX_USER_INPUT_LENGTH = 1750;

export enum FixableDiagnosticType {
    UndefinedFunctionCall = 'undefinedFunctionCall',
    MaxLen = 'maxLen',
    Others = 'others',
}
