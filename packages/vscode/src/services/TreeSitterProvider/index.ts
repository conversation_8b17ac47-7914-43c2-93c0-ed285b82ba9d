import {extname} from 'path';
import * as vscode from 'vscode';
import {injectable} from 'inversify';
import Parser from 'web-tree-sitter';
import 'reflect-metadata';
import {
    TreeSitterLanguage,
    getTreeSitterParser,
    checkTreeSitterSupport,
    extractFunctionNodes,
    getVueScriptRange,
} from '../../utils/treeSitterUtils';

export const MAX_DOCUMENT_LENGTH = 400000;

export const MAX_DOCUMENT_LINE = 8000;

export function asPoint(pos: vscode.Position): Parser.Point {
    return {row: pos.line, column: pos.character};
}

export function getFileExtname(document: vscode.TextDocument) {
    if (document.isUntitled) {
        return undefined;
    }
    return extname(document.fileName);
}

export function isFileTooLarge(document: vscode.TextDocument) {
    const text = document.getText();
    return document.lineCount > MAX_DOCUMENT_LINE || text.length > MAX_DOCUMENT_LENGTH;
}

export function getEndPoint(startPoint: Parser.Point, content: string) {
    const lines = content.split('\n');
    if (lines.length > 1) {
        return {
            row: startPoint.row + lines.length - 1,
            column: lines[lines.length - 1].length,
        };
    }
    return {row: startPoint.row, column: startPoint.column + content.length};
}

const EMPTY_RANGE: Parser.Range = {
    startPosition: {row: 0, column: 0},
    endPosition: {row: 0, column: 0},
    startIndex: 0,
    endIndex: 0,
};

/**
 * 解析文档内容, 根据语言判断要解析的文本范围，正常是解析传进来的整个文档内容
 * 如果是 vue 语言文件，则只解析 js 部分
 *
 * @param parser 解析器
 * @param content 文档内容
 * @param languageId vscode的语言标识
 * @param previousTree 之前解析的树
 */
function parseText(parser: Parser, content: string, languageId: string, previousTree?: Parser.Tree) {
    if (languageId === TreeSitterLanguage.Vue) {
        const ranges = getVueScriptRange(content);
        // 如果拿不到 js 代码范围，则给一个空的范围，防止解析器直接解析整个文档内容
        // 直接解析整个文档很容易出现一些函数可以一些函数不行，还不如先不支持
        const includedRanges = ranges.length > 0 ? ranges : [EMPTY_RANGE];
        return parser.parse(content, previousTree, {includedRanges});
    }
    return parser.parse(content, previousTree);
}

@injectable()
export class TreeSitterProvider implements vscode.Disposable {
    private readonly parsers = new Map<TreeSitterLanguage, Parser>();
    private disposables: vscode.Disposable[] = [];
    // Note: 每在这里加一个语言，都要确保在static目录有对应语言的 tree-sitter
    readonly enabledWasmLanguageIds: TreeSitterLanguage[] = [
        TreeSitterLanguage.Java,
        TreeSitterLanguage.Go,
        TreeSitterLanguage.Python,
        TreeSitterLanguage.Cpp,
        TreeSitterLanguage.Markdown,
        TreeSitterLanguage.JavaScript,
        TreeSitterLanguage.TypeScript,
        TreeSitterLanguage.Tsx,
        TreeSitterLanguage.C,
    ];
    trees = new Map<string, Parser.Tree>();

    constructor() {
        this.initParser();
        this.disposables.push(
            vscode.window.onDidChangeVisibleTextEditors(() => {
                this.updateVisibleDocumentTree();
            }),
            vscode.workspace.onDidChangeTextDocument(edit => {
                if (edit.contentChanges.length === 0) {
                    return;
                }
                const language = checkTreeSitterSupport(edit.document.languageId);
                if (!language) {
                    return;
                }
                if (isFileTooLarge(edit.document)) {
                    return;
                }
                this.handleDocumentChange(edit);
            })
        );
    }

    private async initParser() {
        // NOTE: 这里必须是一个结束后再初始化另一个，不能同步进行
        for (const languageId of this.enabledWasmLanguageIds) {
            await this.initLanguageParser(languageId);
        }
        this.updateVisibleDocumentTree();
    }

    private async initLanguageParser(languageId: TreeSitterLanguage) {
        try {
            const parser = await getTreeSitterParser(languageId);
            this.parsers.set(languageId, parser);
        }
        catch (e) {
            // noop
        }
    }

    private getParser(document: vscode.TextDocument) {
        const language = checkTreeSitterSupport(document.languageId);
        // 特殊处理下 vue 语言，现在没找到合适的 tree-sitter-vue.wasm，找到的也不适配
        // .vue 文件涉及 CodeLens 的应该都在 <script> 标签中，可以找到这个范围然后尝试用 ts 解析器解析
        if (language === 'vue') {
            // 理论上可以通过script标签的 lang="ts" 这个属性判断是ts还是js，但这样太复杂了
            // 动态地修改 parser 可能会影响语法树的增量解析，先试下直接用 ts 解析器看会不会有问题
            return this.parsers.get(TreeSitterLanguage.TypeScript);
        }
        else if (language && this.enabledWasmLanguageIds.includes(language)) {
            return this.parsers.get(language);
        }
        return undefined;
    }

    private async updateVisibleDocumentTree() {
        for (const editor of vscode.window.visibleTextEditors) {
            const parser = this.getParser(editor.document);
            if (!parser) {
                continue;
            }

            if (isFileTooLarge(editor.document)) {
                continue;
            }

            const uri = editor.document.uri.toString();
            if (this.trees.has(uri)) {
                continue;
            }

            const tree = parseText(
                parser,
                editor.document.getText(),
                editor.document.languageId
            );
            this.trees.set(uri, tree);
        }

        // 清空掉已关闭的文档的 tree 缓存
        const uris = vscode.window.visibleTextEditors.map(v => v.document.uri.toString());
        for (const key of this.trees.keys()) {
            if (!uris.includes(key)) {
                const tree = this.trees.get(key);
                tree?.delete();
                this.trees.delete(key);
            }
        }
    }

    private async handleDocumentChange(edit: vscode.TextDocumentChangeEvent) {
        const parser = this.getParser(edit.document);
        if (!parser) {
            return;
        }

        const uri = edit.document.uri.toString();
        const previousTree = this.trees.get(uri);
        if (previousTree) {
            for (const change of edit.contentChanges) {
                const startIndex = change.rangeOffset;
                const oldEndIndex = change.rangeOffset + change.rangeLength;
                const newEndIndex = change.rangeOffset + change.text.length;
                const startPos = edit.document.positionAt(startIndex);
                const oldEndPos = edit.document.positionAt(oldEndIndex);
                const newEndPos = edit.document.positionAt(newEndIndex);
                const startPosition = asPoint(startPos);
                const oldEndPosition = asPoint(oldEndPos);
                const newEndPosition = asPoint(newEndPos);
                const delta = {startIndex, oldEndIndex, newEndIndex, startPosition, oldEndPosition, newEndPosition};
                previousTree.edit(delta);
            }
            try {
                const newTree = parseText(
                    parser,
                    edit.document.getText(),
                    edit.document.languageId,
                    previousTree
                );
                if (newTree !== previousTree) {
                    previousTree.delete();
                    this.trees.set(uri, newTree);
                }
            }
            catch {
                // If parsing fails, clean up the previous tree
                previousTree.delete();
                this.trees.delete(uri);
            }
        } else {
            try {
                const newTree = parseText(
                    parser,
                    edit.document.getText(),
                    edit.document.languageId
                );
                this.trees.set(uri, newTree);
            }
            catch {
                // noop
            }
        }
    }

    getDocumentTree(document: vscode.TextDocument) {
        const parser = this.getParser(document);
        if (!parser) {
            return;
        }
        if (isFileTooLarge(document)) {
            return;
        }
        const uri = document.uri.toString();
        const previousTree = this.trees.get(uri);
        const tree = parseText(parser, document.getText(), document.languageId, previousTree);
        if (tree !== previousTree) {
            previousTree?.delete();
            this.trees.set(uri, tree);
        }
        return tree;
    }

    getMatchedNodes(document: vscode.TextDocument, query: string) {
        const parser = this.getParser(document);
        const node = this.getDocumentTree(document)?.rootNode;
        if (!parser || !node) {
            return [];
        }
        return parser.getLanguage().query(query).matches(node);
    }

    parseDocumentAfterReplaceNode(document: vscode.TextDocument, content: string, node: Parser.SyntaxNode) {
        const parser = this.getParser(document);
        if (!parser) {
            return;
        }
        const uri = document.uri.toString();
        const previousTree = this.trees.get(uri);
        if (previousTree) {
            const startIndex = node.startIndex;
            const oldEndIndex = node.endIndex;
            const newEndIndex = node.startIndex + content.length;
            const startPos = document.positionAt(startIndex);
            const oldEndPos = document.positionAt(oldEndIndex);
            const startPosition = asPoint(startPos);
            const oldEndPosition = asPoint(oldEndPos);
            const newEndPosition = getEndPoint(startPosition, content);
            const delta = {startIndex, oldEndIndex, newEndIndex, startPosition, oldEndPosition, newEndPosition};
            previousTree.edit(delta);
            const fullText = document.getText();

            const tree = parseText(
                parser,
                fullText.slice(0, node.startIndex) + content + fullText.slice(node.endIndex),
                document.languageId,
                previousTree
            );
            if (tree !== previousTree) {
                previousTree.delete();
                this.trees.set(uri, tree);
            }
            return tree;
        }
        return parseText(parser, content, document.languageId);
    }

    getFunctionCodeLens(
        document: vscode.TextDocument,
        filterFunctionNode: (node: Parser.SyntaxNode, languageId: TreeSitterLanguage) => boolean,
        getCommand: (document: vscode.TextDocument, range: vscode.Range, node: Parser.SyntaxNode) => vscode.Command
    ) {
        if (isFileTooLarge(document)) {
            return [];
        }
        const treeSitterLanguage = checkTreeSitterSupport(document.languageId);
        if (!treeSitterLanguage) {
            return [];
        }
        const tree = this.getDocumentTree(document);
        if (!tree) {
            return [];
        }
        const nodes = extractFunctionNodes(treeSitterLanguage, tree.rootNode);
        const codeLenses = nodes
            .filter(node => filterFunctionNode(node, treeSitterLanguage))
            .map(node => {
                const {startPosition, endPosition} = node;
                const start = new vscode.Position(startPosition.row, startPosition.column);
                const end = new vscode.Position(endPosition.row, endPosition.column);
                const range = new vscode.Range(start, end);
                const command = getCommand(document, range, node);
                const codeLens = new vscode.CodeLens(
                    new vscode.Range(
                        new vscode.Position(startPosition.row, 0),
                        new vscode.Position(endPosition.row, 0)
                    ),
                    command
                );
                return codeLens;
            });
        return codeLenses;
    }

    findRange(ranges: vscode.Range[], position: vscode.Position) {
        for (const range of ranges) {
            // 只需比较行数是否在范围内，避免 treesitter 将 export 解析在 function 外的问题
            if (range.start.line <= position.line && position.line <= range.end.line) {
                return range;
            }
        }
        return null;
    }

    getFunctionRange(
        document: vscode.TextDocument,
        filterFunctionNode: (node: Parser.SyntaxNode, languageId: TreeSitterLanguage) => boolean,
        selectStartPosition: vscode.Position
    ) {
        if (isFileTooLarge(document)) {
            return null;
        }
        const treeSitterLanguage = checkTreeSitterSupport(document.languageId);
        if (!treeSitterLanguage) {
            return null;
        }
        const tree = this.getDocumentTree(document);
        if (!tree) {
            return null;
        }
        const nodes = extractFunctionNodes(treeSitterLanguage, tree.rootNode);
        const ranges = nodes
            .filter(node => filterFunctionNode(node, treeSitterLanguage))
            .map(node => {
                const {startPosition, endPosition} = node;
                const start = new vscode.Position(startPosition.row, startPosition.column);
                const end = new vscode.Position(endPosition.row, endPosition.column);
                const range = new vscode.Range(start, end);
                return range;
            });
        const range = this.findRange(ranges, selectStartPosition);
        return range;
    }

    functionNodeOfRange(document: vscode.TextDocument, range: vscode.Range) {
        if (isFileTooLarge(document)) {
            return [];
        }
        const treeSitterLanguage = checkTreeSitterSupport(document.languageId);
        if (!treeSitterLanguage) {
            return [];
        }
        const tree = this.getDocumentTree(document);
        if (!tree) {
            return [];
        }

        const nodes = extractFunctionNodes(treeSitterLanguage, tree.rootNode);
        const selectedNodes = nodes.filter(node => {
            const {startPosition, endPosition} = node;
            const start = new vscode.Position(startPosition.row, startPosition.column);
            const end = new vscode.Position(endPosition.row, endPosition.column);
            return range.contains(start) && range.contains(end);
        });
        return selectedNodes;
    }

    functionNodeOfSelection(document: vscode.TextDocument) {
        const selection = vscode.window.activeTextEditor?.selection;
        if (!selection) {
            return [];
        }

        return this.functionNodeOfRange(document, selection);
    }

    functionNodeOfPosition(document: vscode.TextDocument, position: vscode.Position) {
        if (isFileTooLarge(document)) {
            return [];
        }
        const treeSitterLanguage = checkTreeSitterSupport(document.languageId);
        if (!treeSitterLanguage) {
            return [];
        }
        const tree = this.getDocumentTree(document);
        if (!tree) {
            return [];
        }
        const nodes = extractFunctionNodes(treeSitterLanguage, tree.rootNode);
        const selectedNodes = nodes.filter(node => {
            const {startPosition, endPosition} = node;
            const start = new vscode.Position(startPosition.row, startPosition.column);
            const end = new vscode.Position(endPosition.row, endPosition.column);
            return position.isAfterOrEqual(start) && position.isBeforeOrEqual(end);
        });
        return selectedNodes;
    }

    dispose() {
        // 清理所有的Parser.Tree对象
        for (const [key, tree] of this.trees.entries()) {
            tree.delete();
            this.trees.delete(key);
        }

        // 清理所有的Parser对象
        for (const parser of this.parsers.values()) {
            parser.delete();
        }
        this.parsers.clear();

        // 清理所有事件监听器
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
