import * as vscode from 'vscode';
import {Position, Diagnostic} from 'vscode-languageserver-types';
import {URI} from 'vscode-uri';
import {adaptToLspDiagnostic} from '@/common/lsp/adapters/utils';
import {IDiagnosticTracker} from './types';

export class VSCodeDiagnosticTracker implements IDiagnosticTracker {
    private static _instance: VSCodeDiagnosticTracker | undefined;
    private constructor() {}

    static getInstance() {
        if (this._instance) {
            return this._instance;
        }
        this._instance = new VSCodeDiagnosticTracker();
        return this._instance;
    }

    /**
     * 获取指定资源在指定位置附近的诊断信息。
     *
     * @param resource URI 资源对象，表示需要获取诊断信息的文件路径。
     * @param position Position 对象，表示需要获取诊断信息的位置。
     * @returns Diagnostic 数组，包含指定位置附近的诊断信息。
     */
    getDiagnostics(resource: URI, position: Position): Diagnostic[] {
        const diagnostics = vscode.languages.getDiagnostics(resource);
        const startLine = position.line - 1;
        const endLine = position.line + 2;
        const targetRange = new vscode.Range(startLine, 0, endLine + 1, 0);
        const result: Diagnostic[] = diagnostics
            .filter(item => item.range.intersection(targetRange) !== undefined)
            .map(adaptToLspDiagnostic);
        return result;
    }
}
