/**
 * @file 根据当前用户行为以及输入的特征，对于用户采纳推荐结果的概率进行预测
 */

import {Container, injectable} from 'inversify';
import 'reflect-metadata';
import {TelemetryData} from '@/common/telemetry/index';
import {PromptInfo} from '../../../common/prompt/extractPrompt';
import {
    contextualFilterIntercept,
    contextualFilterWeights,
    contextualFilterCharacterMap,
    contextualFilterLanguageMap,
} from './config';
import {treeScore} from './treeScore';

@injectable()
export class ContextualFilterManager {
    previousLabel: number = 0;
    previousLabelTimestamp: number = Date.now() - 3600;
    probabilityAccept: number = 0;
    probabilityAcceptByDecisionTree: number = 0;
    probabilityAcceptByLinearRegression: number = 0;

    dispose() {
    }
}

export function getLastLineLength(str: string) {
    const lines = str.split('\n');
    return lines[lines.length - 1].length;
}

/**
 * 根据当前文档上下文情况进行打分，估计用户采纳返回结果的概率
 *
 * @param globalContext 依赖注入 context
 * @param telemetry 监控 log，用于提取特征指标
 * @param prompt prompt，用于提取特征指标
 * @returns
 */
// eslint-disable-next-line complexity, max-statements
export function contextualFilterScore(globalContext: Container, telemetry: TelemetryData, prompt: PromptInfo) {
    const contextualFilterManager = globalContext.get(ContextualFilterManager);
    const previousLabel = contextualFilterManager.previousLabel;

    // 光标后是否还有 whitespace，0 或 1
    let afterCursorWhitespace = 0;
    if (
        'afterCursorWhitespace' in telemetry.properties
        && telemetry.properties.afterCursorWhitespace === 'true'
    ) {
        afterCursorWhitespace = 1;
    }

    // 用户上一次触发补全距现在的时间，单位，秒，加1取 log，保证是一个大于 0 的值
    const duration = (Date.now() - contextualFilterManager.previousLabelTimestamp) / 1000;
    const duratonLog = Math.log(1 + duration);

    // 光标前缀最后一行的长度，加1取 log
    let prefixLastLineLengthFactor = 0;
    // 前缀的最后一个字符，根据字符不同，在特征向量中采取不同的 index，0 或 1
    let lastCharaterOfPrefix = 0;
    const prefix = prompt.prefix;
    if (prefix) {
        prefixLastLineLengthFactor = Math.log(1 + getLastLineLength(prefix));
        const lastChar = prefix.slice(-1);
        if (contextualFilterCharacterMap[lastChar] !== undefined) {
            lastCharaterOfPrefix = contextualFilterCharacterMap[lastChar];
        }
    }

    // 和前面一样，只是 prefix 多了一个 trimEnd 的处理
    let prefixLastLineLengthWithoutWsFactor = 0;
    let lastCharaterOfTrimPrefix = 0;
    const prefixWithoutWs = prefix.trimEnd();
    if (prefixWithoutWs) {
        prefixLastLineLengthWithoutWsFactor = Math.log(1 + getLastLineLength(prefixWithoutWs));
        const lastChar = prefixWithoutWs.slice(-1);
        if (contextualFilterCharacterMap[lastChar] !== undefined) {
            lastCharaterOfTrimPrefix = contextualFilterCharacterMap[lastChar];
        }
    }

    // 当前文件整体的长度，加1取 log
    let documentLength = 0;
    if ('documentLength' in telemetry.measurements) {
        const measurements = telemetry.measurements.documentLength;
        documentLength = Math.log(1 + measurements);
    }

    // 当前光标位置在整个文档中的 offset，加1取 log
    let promptEndPos = 0;
    if ('promptEndPos' in telemetry.measurements) {
        const measurements = telemetry.measurements.promptEndPos;
        promptEndPos = Math.log(1 + measurements);
    }

    // 光标位置在文档中的百分比
    let documentLengthAndPromptEndPos = 0;
    if (
        'promptEndPos' in telemetry.measurements
        && 'documentLength' in telemetry.measurements
    ) {
        const documentLength = telemetry.measurements.documentLength;
        documentLengthAndPromptEndPos = (telemetry.measurements.promptEndPos + 0.5) / (1 + documentLength);
    }

    // 语言权重，根据语言种类，在 feature 向量对应位置置 1
    let languageWeight = 0;
    if (contextualFilterLanguageMap[telemetry.properties.languageId] !== undefined) {
        languageWeight = contextualFilterLanguageMap[telemetry.properties.languageId];
    }
    telemetry.measurements.contextualFilter = {
        previousLabel: previousLabel,
        afterCursorWhitespace: afterCursorWhitespace,
        duratonLog: duratonLog,
        prefixLastLineLengthFactor: prefixLastLineLengthFactor,
        prefixLastLineLengthWithoutWsFactor: prefixLastLineLengthWithoutWsFactor,
        documentLength: documentLength,
        promptEndPos: promptEndPos,
        documentLengthAndPromptEndPos: documentLengthAndPromptEndPos,
        languageWeight: languageWeight,
        lastCharaterOfPrefix: lastCharaterOfPrefix,
        lastCharaterOfTrimPrefix: lastCharaterOfTrimPrefix,
    };

    // 用户采纳概率
    let probabilityAccept = 0;
    // 下面是用户采纳概率的两种计算方式

    // 决策树方法：
    // 将上面计算出的诸多特征，转换成一个 221 维的向量
    const vector = new Array(221).fill(0);
    vector[0] = previousLabel;
    vector[1] = afterCursorWhitespace;
    vector[2] = duratonLog;
    vector[3] = prefixLastLineLengthFactor;
    vector[4] = prefixLastLineLengthWithoutWsFactor;
    vector[5] = documentLength;
    vector[6] = promptEndPos;
    vector[7] = documentLengthAndPromptEndPos;
    vector[8 + languageWeight] = 1;
    vector[29 + lastCharaterOfPrefix] = 1;
    vector[125 + lastCharaterOfTrimPrefix] = 1;
    // 将向量丢给一个决策树模型，最终给出一个归一化的结果
    const probabilityAcceptByDecisionTree = treeScore(vector)[1];

    // 线性规划方法：
    // 让各个指标 * 固定的权重
    let intercept = contextualFilterIntercept;
    intercept += contextualFilterWeights[0] * previousLabel;
    intercept += contextualFilterWeights[1] * afterCursorWhitespace;
    intercept += contextualFilterWeights[2] * duratonLog;
    intercept += contextualFilterWeights[3] * prefixLastLineLengthFactor;
    intercept += contextualFilterWeights[4] * prefixLastLineLengthWithoutWsFactor;
    intercept += contextualFilterWeights[5] * documentLength;
    intercept += contextualFilterWeights[6] * promptEndPos;
    intercept += contextualFilterWeights[7] * documentLengthAndPromptEndPos;
    intercept += contextualFilterWeights[8 + languageWeight];
    intercept += contextualFilterWeights[29 + lastCharaterOfPrefix];
    intercept += contextualFilterWeights[125 + lastCharaterOfTrimPrefix];
    const probabilityAcceptByLinearRegression = 1 / (1 + Math.exp(-intercept));

    probabilityAccept = probabilityAcceptByDecisionTree;

    globalContext.get(ContextualFilterManager).probabilityAccept = probabilityAccept;
    globalContext.get(ContextualFilterManager).probabilityAcceptByDecisionTree = probabilityAcceptByDecisionTree;
    globalContext
        .get(ContextualFilterManager)
        .probabilityAcceptByLinearRegression = probabilityAcceptByLinearRegression;
    return [probabilityAccept, probabilityAcceptByDecisionTree, probabilityAcceptByLinearRegression] as const;
}
