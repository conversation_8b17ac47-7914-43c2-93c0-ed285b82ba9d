// @ts-nocheck
import TextDocumentManager from './TextDocumentManager';

function wrapDoc(ctx, doc) {
    let language = ctx.get(LanguageDetection).detectLanguage(
        TextDocument.create(doc.uri, doc.languageId, doc.version, doc.getText())
    );
    return TextDocument.create(doc.uri, language.languageId, doc.version, doc.getText());
}

export default class ExtensionTextDocumentManager extends TextDocumentManager {
    constructor(ctx) {
        super(ctx);
        this.onDidFocusTextDocument = uf.window.onDidChangeActiveTextEditor;
        this.onDidChangeTextDocument = (listener, thisArgs, disposables) =>
            uf.workspace.onDidChangeTextDocument(
                e => listener({
                    document: wrapDoc(this.ctx, e.document),
                    contentChanges: e.contentChanges,
                }),
                thisArgs,
                disposables
            );
        this.onDidChangeCursor = (listener, thisArgs, disposables) =>
            uf.window.onDidChangeTextEditorSelection(
                e => listener({
                    textEditor: {
                        document: wrapDoc(this.ctx, e.textEditor.document),
                    },
                    selections: e.selections,
                }),
                thisArgs,
                disposables
            );
    }
    async getOpenTextDocuments() {
        return uf.workspace.textDocuments.map(d => wrapDoc(this.ctx, d));
    }
    async openTextDocument(uri) {
        let doc = await uf.workspace.openTextDocument(uri);
        return wrapDoc(this.ctx, doc);
    }
    findNotebook(doc) {
        for (let notebook of uf.workspace.notebookDocuments) {
            if (notebook.getCells().some(cell => cell.document.uri.toString() === doc.uri.toString())) {
                return {
                    getCells: () => notebook.getCells().map(cell => this.wrapCell(cell)),
                    getCellFor: cellDocument => {
                        let cell = notebook.getCells().find(cell =>
                            cell.document.uri.toString() === cellDocument.uri.toString()
                        );
                        return cell ? this.wrapCell(cell) : void 0;
                    },
                };
            }
        }
    }
    wrapCell(cell) {
        return {
            ...cell,
            get document() {
                return TextDocument.create(
                    cell.document.uri,
                    cell.document.languageId,
                    cell.document.version,
                    cell.document.getText()
                );
            },
        };
    }
    getWorkspaceFolders() {
        var _a, _b;
        return (_b = (_a = uf.workspace.workspaceFolders) == null ? void 0 : _a.map(f => f.uri)) != null ? _b : [];
    }
}
