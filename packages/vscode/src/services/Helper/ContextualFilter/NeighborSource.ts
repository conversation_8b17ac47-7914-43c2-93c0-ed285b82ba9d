// @ts-nocheck
import {sortByAccessTimes} from '@/common/prompt/fileTracker';
import * as vscode from 'vscode';

const MAX_NEIGHBOR_AGGREGATE_LENGTH = 2e5;
const MAX_NEIGHBOR_FILES = 20;
const EXCLUDED_NEIGHBORS = ['node_modules', 'dist', 'site-packages'];

// TODO 可能不太对，先这么写着
function considerNeighborFile(languageId, neighborLanguageId) {
    return languageId === neighborLanguageId;
    // return (0, lO.normalizeLanguageId)(languageId) === (0, lO.normalizeLanguageId)(neighborLanguageId);
}

class OpenTabFiles {
    docManager;
    constructor(docManager) {
        this.docManager = docManager;
    }
    async truncateDocs(docs, fileURI, languageId, maxNumNeighborFiles) {
        let openFiles = new Map(),
            totalLen = 0;
        for (let doc of docs) {
            if (
                !(totalLen + doc.getText().length > MAX_NEIGHBOR_AGGREGATE_LENGTH)
                && (doc.vscodeUri.scheme === 'file' && fileURI.scheme === 'file' && doc
                            .vscodeUri
                            .fsPath !== fileURI.fsPath
                    && considerNeighborFile(
                        languageId,
                        doc.languageId
                    ) && (openFiles.set(doc.uri.toString(), {
                        uri: doc.uri.toString(),
                        relativePath: await this.docManager.getRelativePath(doc),
                        languageId: doc.languageId,
                        source: doc.getText(),
                    }),
                        totalLen += doc
                            .getText()
                            .length),
                    openFiles.size >= maxNumNeighborFiles)
            ) {
                break;
            }
        }
        return openFiles;
    }
    async getNeighborFiles(uri, languageId, maxNumNeighborFiles) {
        let neighborFiles = new Map(),
            neighborSource = new Map();
        return neighborFiles = await this.truncateDocs(
            sortByAccessTimes(await this.docManager.textDocuments()),
            uri,
            languageId,
            maxNumNeighborFiles
        ),
            neighborSource.set('opentabs', Array.from(neighborFiles.keys()).map(uri => uri.toString())),
            {
                docs: neighborFiles,
                neighborSource: neighborSource,
            };
    }
}

export default class NeighborSource {
    static reset() {
        NeighborSource.instance = void 0;
    }
    static async getNeighborFiles(ctx, uri, featuresFilterArgs, telemetryData) {
        let docManager = ctx.get(TextDocumentManager);
        NeighborSource.instance === void 0 && (NeighborSource.instance = new OpenTabFiles(docManager));
        let result = await NeighborSource.instance.getNeighborFiles(
                uri,
                featuresFilterArgs.fileType,
                MAX_NEIGHBOR_FILES
            ),
            doc = await docManager.getTextDocument(uri);
        if (!doc) {
            return relatedFilesLogger.debug(ctx, 'neighborFiles.getNeighborFiles', 'Failed to get the document'),
                result;
        }
        let wksFolder = await docManager.getWorkspaceFolder(doc),
            folder = wksFolder == null ? void 0 : wksFolder.toString();
        if (wksFolder && folder) {
            let docInfo = {
                    relativePath: (0, uO.relative)(folder, doc.uri),
                    uri: doc.uri,
                    languageId: doc.languageId,
                    source: doc.getText(),
                },
                relatedFiles = await getRelatedFilesList(ctx, docInfo, wksFolder, telemetryData);
            relatedFiles != null && relatedFiles.size && relatedFiles.forEach((uriToContentMap, type) => {
                let addedDocs = [];
                uriToContentMap.forEach((value, key) => {
                    if (result.docs.has(key)) {
                        return;
                    }
                    let relatedFileDocInfo = {
                        relativePath: (0, uO.relative)(folder, key),
                        uri: key,
                        languageId: docInfo.languageId,
                        source: value,
                    };
                    addedDocs.unshift(relatedFileDocInfo), result.docs.set(key, relatedFileDocInfo);
                }), addedDocs.length > 0 && result.neighborSource.set(type, addedDocs.map(doc => doc.uri.toString()));
            });
        }
        else {
            relatedFilesLogger.debug(ctx, 'neighborFiles.getNeighborFiles', 'Failed to get the workspace folder');
        }
        return result;
    }
}
