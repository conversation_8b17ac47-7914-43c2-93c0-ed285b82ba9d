import * as path from 'path';
import * as vscode from 'vscode';
import {injectable} from 'inversify';
import 'reflect-metadata';

export function getRelativePath(uris: vscode.Uri[], to: string) {
    for (const uri of uris) {
        const from = uri.fsPath;
        if (to.startsWith(from + path.sep)) {
            return path.relative(from, to);
        }
    }
    return undefined;
}

export async function tryGetTextDocument(textDocumentManager: TextDocumentManager, uri: string) {
    try {
        const document = await textDocumentManager.getTextDocument(uri);
        return document;
    }
    catch (e) {
        return undefined;
    }
}

@injectable()
export class TextDocumentManager {
    constructor() {
        this.onDidFocusTextDocument = vscode.window.onDidChangeActiveTextEditor;
        this.onDidChangeTextDocument = vscode.workspace.onDidChangeTextDocument;
        this.onDidChangeCursor = vscode.window.onDidChangeTextEditorSelection;
    }

    get textDocuments() {
        return vscode.workspace.textDocuments;
    }

    async getTextDocument(uri: string) {
        return vscode.workspace.openTextDocument(vscode.Uri.parse(uri));
    }

    getRelativePath(doc: vscode.TextDocument) {
        const document = doc;
        if (document) {
            if (document.isUntitled) {
                return;
            }
            const workspaceFolders = vscode.workspace.workspaceFolders?.map(folder => folder.uri) ?? [];
            return getRelativePath(workspaceFolders, document.fileName) ?? path.basename(document.fileName);
        }
        return undefined;
    }

    findNotebook(doc: vscode.TextDocument) {
        const document = doc;
        return vscode.workspace.notebookDocuments.find(notebook => {
            return notebook.getCells().some(cell => {
                return cell.document === document;
            });
        });
    }

    onDidFocusTextDocument: vscode.Event<vscode.TextEditor | undefined>;
    onDidChangeCursor: vscode.Event<vscode.TextEditorSelectionChangeEvent>;
    onDidChangeTextDocument: vscode.Event<vscode.TextDocumentChangeEvent>;
}
