import {injectable} from 'inversify';
import 'reflect-metadata';
import * as vscode from 'vscode';

@injectable()
export class LocationFactory {
    range(startLine: vscode.Position, startCharacter: vscode.Position): vscode.Range;
    range(startLine: number, startCharacter: number, endLine?: number, endCharacter?: number): vscode.Range;
    range(startLine: any, startCharacter: any, endLine?: any, endCharacter?: any) {
        return undefined !== endLine && undefined !== endCharacter
            ? new vscode.Range(startLine, startCharacter, endLine, endCharacter)
            : new vscode.Range(startLine, startCharacter);
    }

    position(line: number, character: number) {
        return new vscode.Position(line, character);
    }
}
