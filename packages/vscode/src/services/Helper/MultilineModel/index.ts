// @ts-nocheck
/* eslint-disable complexity */
import * as vscode from 'vscode';
import MultilineModelFeatures from './MultilineModelFeatures';
import {multilineModelPredict} from './MultilineModelWeights';
import {getBlockParser, isSupportedLanguageId} from './elidableText';
import {Container} from 'inversify';
import {PromptContext} from '@/common/prompt/extractPrompt';

function isNewLine(selectionPosition, doc) {
    return doc.lineAt(selectionPosition).text.trim().length === 0;
}

async function isEmptyBlockStart2(languageId, text, offset) {
    return isSupportedLanguageId(languageId) ? getBlockParser(languageId).isEmptyBlockStart(text, offset) : !1;
}

function isEmptyBlockStart(doc, position) {
    return isEmptyBlockStart2(doc.languageId, doc.getText(), doc.offsetAt(position));
}

function constructMultilineFeatures(prompt, language) {
    return new MultilineModelFeatures(prompt.prefix, prompt.suffix, language);
}

function requestMultilineScore(prompt, language) {
    const features = constructMultilineFeatures(prompt, language).constructFeatures();
    return multilineModelPredict(features)[1];
}

function exploreMultilineRandom() {
    return Math.random() > 0.5;
}

async function requestMultilineExperiment(
    enableMultilineExploration,
    enableMultiModel,
    multiModelThreshold,
    document,
    prompt
) {
    let score = 0;
    // 初始化一个变量来存储是否应该请求多行实验
    let shouldRequestMultiline = false;

    // 如果启用了多行探索，使用随机探索函数来确定是否请求多行实验
    if (enableMultilineExploration) {
        shouldRequestMultiline = exploreMultilineRandom();
    }
    // 如果未启用多行探索，但启用了多模型，并且文档语言是JavaScript或Python，
    // 使用评分函数来确定是否基于提示和多模型阈值请求多行实验
    else if (enableMultiModel && ['javascript', 'javascriptreact', 'python'].includes(document.languageId)) {
        score = requestMultilineScore(prompt.prompt, document.languageId);
        shouldRequestMultiline = score > multiModelThreshold;
    }

    // 返回结果
    return {shouldRequestMultiline, score};
}

async function shouldRequestMultiline(
    ctx,
    document,
    position,
    inlineSuggestion,
    prompt,
    requestMultilineExploration,
    requestMultilineOnNewLine,
    requestMultiModel,
    requestMultiModelThreshold
) {
    let score = 0;
    // TODO 如果有强制多行请求的覆盖，则返回true
    // if (ctx.get(ForceMultiLine).requestMultilineOverride) {
    //     return true;
    // }

    // 如果请求多行探索，检查文档中的位置是否为空块的开始，并将结果存储在预发布的遥测数据中
    // if (requestMultilineExploration) {
    //     const isEmptyBlockStartDocumentPosition = await isEmptyBlockStart(document, position);
    //     const isEmptyBlockStartDocumentPositionRangeEnd = await isEmptyBlockStart(
    //         document,
    //         document.lineAt(position).range.end
    //     );
    //     preIssuedTelemetryData.properties.isEmptyBlockStartDocumentPosition = isEmptyBlockStartDocumentPosition
    //         .toString();
    //     preIssuedTelemetryData.properties.isEmptyBlockStartDocumentPositionRangeEnd =
    //         isEmptyBlockStartDocumentPositionRangeEnd.toString();
    //     preIssuedTelemetryData.properties.inlineSuggestion = inlineSuggestion.toString();
    //     preIssuedTelemetryData.measurements.documentLineCount = document.lineCount;
    //     preIssuedTelemetryData.measurements.positionLine = position.line;
    // }

    // 如果文档的行数超过8000，发出一个遥测数据，表明由于文件太长而跳过了多行请求
    if (document.lineCount >= 8e3) {
        // telemetry(
        //     ctx,
        //     'ghostText.longFileMultilineSkip',
        //     TelemetryData.createAndMarkAsIssued({
        //         languageId: document.languageId,
        //         lineCount: String(document.lineCount),
        //         currentLine: String(position.line),
        //     })
        // );
    }
    else {
        if (
            requestMultilineOnNewLine
            && ['typescript', 'typescriptreact'].includes(document.languageId)
            && isNewLine(position, document)
        ) {
            return {requestMultiline: true, score};
        }

        let requestMultiline = false;

        if (isSupportedLanguageId(document.languageId)) {
            if (!inlineSuggestion) {
                requestMultiline = await isEmptyBlockStart(document, position);
            }
            else {
                const isStartEmpty = await isEmptyBlockStart(document, position);
                const isEndEmpty = await isEmptyBlockStart(document, document.lineAt(position).range.end);
                requestMultiline = isStartEmpty || isEndEmpty;
            }
        }

        // 如果上述条件都不满足，调用requestMultilineExperiment函数来判断是否应该请求多行
        if (!requestMultiline) {
            const reqMultiline = await requestMultilineExperiment(
                requestMultilineExploration,
                requestMultiModel,
                requestMultiModelThreshold,
                document,
                prompt
            );
            requestMultiline = reqMultiline.shouldRequestMultiline;
            score = reqMultiline.score;
        }

        return {requestMultiline, score};
    }

    return {requestMultiline: false, score};
}

async function isBlockBodyFinished(languageId, prefix, completion, offset) {
    if (isSupportedLanguageId(languageId)) {
        return getBlockParser(languageId).isBlockBodyFinished(prefix, completion, offset);
    }
}

function parsingBlockFinished(ctx, doc, position) {
    const prefix = doc.getText(new vscode.Range(new vscode.Position(0, 0), position));
    const offset = doc.offsetAt(position);
    const languageId = doc.languageId;
    return completion => isBlockBodyFinished(languageId, prefix, completion, offset);
}

function toApplicableBlockMode(blockMode, languageId) {
    switch (blockMode) {
        case 'parsing':
            return isSupportedLanguageId(languageId) ? 'parsing' : 'server';
        case 'server':
            return 'server';
        case 'parsingandserver':
        default:
            return isSupportedLanguageId(languageId) ? 'parsingandserver' : 'server';
    }
}

function forLanguage(languageId) {
    let overrideBlockMode = false;
    return overrideBlockMode
        ? toApplicableBlockMode(overrideBlockMode, languageId)
        : languageId == 'ruby'
        ? 'parsing'
        : isSupportedLanguageId(languageId)
        ? 'parsingandserver'
        : 'server';
}

// 这两行估计也是做实验的
var workerFuns = [
    'getFunctionPositions',
    'isEmptyBlockStart',
    'isBlockBodyFinished',
    'getNodeStart',
    'parsesWithoutError',
];
var directFuns = ['isSupportedLanguageId', 'getBlockCloseToken', 'getPrompt'];

export async function getGhostTextStrategy(
    ctx: Container,
    document: vscode.TextDocument,
    position: vscode.Position,
    prompt: PromptContext,
    inlineSuggestion: boolean | undefined,
    requestMultilineExploration = false, // 采集的策略不要
    requestMultilineOnNewLine = false, // 新行触发太激进
    requestMultiModel = true,
    requestMultiModelThreshold = 0.5
) {
    const blockMode = forLanguage(document.languageId);
    const shouldReq = await shouldRequestMultiline(
        ctx,
        document,
        position,
        inlineSuggestion,
        prompt,
        requestMultilineExploration,
        requestMultilineOnNewLine,
        requestMultiModel,
        requestMultiModelThreshold
    );
    if (shouldReq.requestMultiline) {
        let adjustedPosition;
        const promptInfo = prompt.prompt;
        // 如果提示的尾部空白长度大于 0，并且提示的前缀不以尾部空白结尾，则调整位置
        if (promptInfo.trailingWs.length > 0 && !promptInfo.prefix.endsWith(promptInfo.trailingWs)) {
            adjustedPosition = new vscode.Position(
                position.line,
                Math.max(position.character - promptInfo.trailingWs.length, 0)
            );
        }
        else {
            adjustedPosition = position;
        }

        return {
            blockMode: blockMode,
            requestMultiline: true,
            isCyclingRequest: false,
            score: shouldReq.score,
            // TODO 这块可能有个后处理逻辑，还没找到
            // finishedCb: parsingBlockFinished(ctx, document, adjustedPosition),
        };
    }
    return {
        blockMode: blockMode,
        requestMultiline: false,
        isCyclingRequest: false,
        score: 0,
        // finishedCb: async () => {},
    };
}
