// @ts-nocheck
import {contextualFilterCharacterMap} from '../ContextualFilter/config';

const commentMap = {
    javascript: ['//'],
    typescript: ['//'],
    typescriptreact: ['//'],
    javascriptreact: ['//'],
    vue: ['//', '-->'],
    php: ['//', '#'],
    dart: ['//'],
    go: ['//'],
    cpp: ['//'],
    scss: ['//'],
    csharp: ['//'],
    java: ['//'],
    c: ['//'],
    rust: ['//'],
    python: ['#'],
    markdown: ['#', '-->'],
    css: ['*/'],
};

const languageMap = {
    javascript: 1,
    javascriptreact: 2,
    typescript: 3,
    typescriptreact: 4,
    python: 5,
    go: 6,
    ruby: 7,
};

function hasComment(text, lineNumber, language, ignoreEmptyLines = true) {
    // 将文本按行分割
    let lines = text.split('\n');

    // 如果忽略空行，则过滤掉空行
    if (ignoreEmptyLines) {
        lines = lines.filter(line => line.trim().length > 0);
    }

    // 如果行号的绝对值大于行数，或者行号大于等于行数，则返回 false
    if (Math.abs(lineNumber) > lines.length || lineNumber >= lines.length) {
        return false;
    }

    // 如果行号小于 0，则将行号设置为行数加上行号
    if (lineNumber < 0) {
        lineNumber = lines.length + lineNumber;
    }

    // 获取指定行号的行
    const line = lines[lineNumber];

    // 获取语言对应的注释字符列表
    const commentChars = commentMap[language] ?? [];

    // 检查行中是否包含注释字符
    return commentChars.some(commentChar => line.includes(commentChar));
}

class PromptFeatures {
    // 构造函数，接受文本组件和语言作为参数
    constructor(promptComponentText, language) {
        // 获取文本的第一行和最后一行
        const [firstLine, lastLine] = this.firstAndLast(promptComponentText);
        // 获取去除尾部空白字符后的文本的第一行和最后一行
        const [firstLineTrimEnd, lastLineTrimEnd] = this.firstAndLast(promptComponentText.trimEnd());

        // 初始化文本特征属性
        this.language = language;
        this.length = promptComponentText.length;
        this.firstLineLength = firstLine.length;
        this.lastLineLength = lastLine.length;
        this.lastLineRstripLength = lastLine.trimEnd().length;
        this.lastLineStripLength = lastLine.trim().length;
        this.rstripLength = promptComponentText.trimEnd().length;
        this.stripLength = promptComponentText.trim().length;
        this.rstripLastLineLength = lastLineTrimEnd.length;
        this.rstripLastLineStripLength = lastLineTrimEnd.trim().length;
        this.secondToLastLineHasComment = hasComment(promptComponentText, -2, language);
        this.rstripSecondToLastLineHasComment = hasComment(promptComponentText.trimEnd(), -2, language);
        this.prefixEndsWithNewline = promptComponentText.endsWith('\n');
        this.lastChar = promptComponentText.slice(-1);
        this.rstripLastChar = promptComponentText.trimEnd().slice(-1);
        this.firstChar = promptComponentText[0];
        this.lstripFirstChar = promptComponentText.trimStart().slice(0, 1);
    }

    // 获取文本的第一行和最后一行的方法
    firstAndLast(text) {
        const lines = text.split('\n');
        const numLines = lines.length;
        const firstLine = lines[0];
        let lastLine = lines[numLines - 1];

        // 如果最后一行为空，并且文本有多行，则返回倒数第二行作为最后一行
        if (lastLine === '' && numLines > 1) {
            lastLine = lines[numLines - 2];
        }

        return [firstLine, lastLine];
    }
}

// 定义 MultilineModelFeatures 类
export default class MultilineModelFeatures {
    prefixFeatures;
    suffixFeatures;
    // 构造函数，接受前缀、后缀和语言作为参数
    constructor(prefix, suffix, language) {
        this.language = language;
        this.prefixFeatures = new PromptFeatures(prefix, language);
        this.suffixFeatures = new PromptFeatures(suffix, language);
    }
    // 构建特征向量的方法
    constructFeatures() {
        // 构建数值特征向量
        const numFeatures = [
            this.prefixFeatures.length,
            this.prefixFeatures.firstLineLength,
            this.prefixFeatures.lastLineLength,
            this.prefixFeatures.lastLineRstripLength,
            this.prefixFeatures.lastLineStripLength,
            this.prefixFeatures.rstripLength, // 去除尾部空白符后的长度
            this.prefixFeatures.rstripLastLineLength,
            this.prefixFeatures.rstripLastLineStripLength,
            this.suffixFeatures.length,
            this.suffixFeatures.firstLineLength,
            this.suffixFeatures.lastLineLength,
            this.prefixFeatures.secondToLastLineHasComment ? 1 : 0,
            this.prefixFeatures.rstripSecondToLastLineHasComment ? 1 : 0,
            this.prefixFeatures.prefixEndsWithNewline ? 1 : 0,
        ];

        // 构建语言特征向量
        const langFeatures = Array(Object.keys(languageMap).length + 1).fill(0);
        langFeatures[languageMap[this.language] ?? 0] = 1;

        // 构建前缀最后一个字符特征向量
        const prefixLastCharFeatures = Array(Object.keys(contextualFilterCharacterMap).length + 1).fill(0);
        prefixLastCharFeatures[contextualFilterCharacterMap[this.prefixFeatures.lastChar] ?? 0] = 1;

        // 构建前缀去除尾部空白后的最后一个字符特征向量
        const prefixRstripLastCharFeatures = Array(Object.keys(contextualFilterCharacterMap).length + 1).fill(0);
        prefixRstripLastCharFeatures[contextualFilterCharacterMap[this.prefixFeatures.rstripLastChar] ?? 0] = 1;

        // 构建后缀第一个字符特征向量
        const suffixFirstCharFeatures = Array(Object.keys(contextualFilterCharacterMap).length + 1).fill(0);
        suffixFirstCharFeatures[contextualFilterCharacterMap[this.suffixFeatures.firstChar] ?? 0] = 1;

        // 构建后缀去除首部空白后的第一个字符特征向量
        const suffixLstripFirstCharFeatures = Array(Object.keys(contextualFilterCharacterMap).length + 1).fill(0);
        suffixLstripFirstCharFeatures[contextualFilterCharacterMap[this.suffixFeatures.lstripFirstChar] ?? 0] = 1;

        // 将所有特征向量连接起来，形成最终的特征向量
        return numFeatures.concat(
            langFeatures,
            prefixLastCharFeatures,
            prefixRstripLastCharFeatures,
            suffixFirstCharFeatures,
            suffixLstripFirstCharFeatures
        );
    }
}
