// @ts-nocheck
import {parseTreeSitter} from '@/utils/treeSitterUtils';
function indent(nd, source) {
    const startIndex = nd.startIndex;
    const lineStart = nd.startIndex - nd.startPosition.column;
    const prefix = source.substring(lineStart, startIndex);
    if (/^\s*$/.test(prefix)) {
        return prefix;
    }
}
function getLineAtOffset(text, offset) {
    const prevNewline = text.lastIndexOf('\n', offset - 1);
    let nextNewline = text.indexOf('\n', offset);
    return nextNewline < 0 && (nextNewline = text.length), text.slice(prevNewline + 1, nextNewline);
}

function outdented(fst, snd, source) {
    if (snd.startPosition.row <= fst.startPosition.row) {
        return !1;
    }
    const fstIndent = indent(fst, source);
    const sndIndent = indent(snd, source);
    return fstIndent !== void 0 && sndIndent !== void 0 && fstIndent.startsWith(sndIndent);
}

function rewindToNearestNonWs(text, offset) {
    let result = offset;
    for (; result > 0 && /\s/.test(text.charAt(result - 1));) {
        result--;
    }
    return result;
}

const docstringQuery = [`[
    (class_definition (block (expression_statement (string))))
    (function_definition (block (expression_statement (string))))
]`];

function innerQuery(queries, root) {
    const matches = [];
    for (const query of queries) {
        if (!query[1]) {
            const lang = root.tree.getLanguage();
            query[1] = lang.query(query[0]);
        }
        matches.push(...query[1].matches(root));
    }
    return matches;
}

function queryPythonIsDocstring(blockNode) {
    return innerQuery([docstringQuery], blockNode).length == 1;
}

/* eslint-disable */
class BaseBlockParser {
    constructor(languageId, nodeMatch, nodeTypesWithBlockOrStmtChild) {
        this.languageId = languageId;
        this.nodeMatch = nodeMatch;
        this.nodeTypesWithBlockOrStmtChild = nodeTypesWithBlockOrStmtChild;
    }
    async getNodeMatchAtPosition(text, offset, cb) {
        const tree = await parseTreeSitter(this.languageId, text);
        try {
            let nodeToComplete = tree.rootNode.descendantForIndex(offset);
            for (; nodeToComplete;) {
                const blockNodeType = this.nodeMatch[nodeToComplete.type];
                if (blockNodeType) {
                    if (!this.nodeTypesWithBlockOrStmtChild.has(nodeToComplete.type)) {
                        break;
                    }
                    const fieldLabel = this.nodeTypesWithBlockOrStmtChild.get(nodeToComplete.type);
                    const childToCheck = fieldLabel == ''
                        ? nodeToComplete.namedChildren[0]
                        : nodeToComplete.childForFieldName(fieldLabel);
                    if ((childToCheck == null ? void 0 : childToCheck.type) == blockNodeType) {
                        break;
                    }
                }
                nodeToComplete = nodeToComplete.parent;
            }
            return nodeToComplete ? cb(nodeToComplete) : void 0;
        }
        finally {
            tree.delete();
        }
    }
    getNextBlockAtPosition(text, offset, cb) {
        return this.getNodeMatchAtPosition(text, offset, nodeToComplete => {
            let block = nodeToComplete.children.reverse().find(x => x.type == this.nodeMatch[nodeToComplete.type]);
            if (block) {
                if (this.languageId == 'python' && block.parent) {
                    const parent = block.parent.type == ':' ? block.parent.parent : block.parent;
                    let nextComment = parent == null ? void 0 : parent.nextSibling;
                    for (; nextComment && nextComment.type == 'comment';) {
                        const commentInline = nextComment.startPosition.row == block.endPosition.row
                            && nextComment.startPosition.column >= block.endPosition.column;
                        const commentAtEnd = nextComment.startPosition.row > parent.endPosition.row
                            && nextComment.startPosition.column > parent.startPosition.column;
                        if (commentInline || commentAtEnd) {
                            block = nextComment, nextComment = nextComment.nextSibling;
                        }
                        else {
                            break;
                        }
                    }
                }
                if (
                    !(block.endIndex >= block.tree.rootNode.endIndex - 1
                        && (block.hasError() || block.parent.hasError()))
                ) {
                    return cb(block);
                }
            }
        });
    }
    async isBlockBodyFinished(prefix, completion, offset) {
        const solution = (prefix + completion).trimEnd();
        const endIndex = await this.getNextBlockAtPosition(solution, offset, block => block.endIndex);
        if (endIndex !== void 0 && endIndex < solution.length) {
            const lengthOfBlock = endIndex - prefix.length;
            return lengthOfBlock > 0 ? lengthOfBlock : void 0;
        }
    }
    getNodeStart(text, offset) {
        const solution = text.trimEnd();
        return this.getNodeMatchAtPosition(solution, offset, block => block.startIndex);
    }
}

export class TreeSitterBasedBlockParser extends BaseBlockParser {
    constructor(
        languageId,
        nodeMatch,
        nodeTypesWithBlockOrStmtChild,
        startKeywords,
        blockNodeType,
        emptyStatementType,
        curlyBraceLanguage
    ) {
        super(languageId, nodeMatch, nodeTypesWithBlockOrStmtChild);
        this.startKeywords = startKeywords;
        this.blockNodeType = blockNodeType;
        this.emptyStatementType = emptyStatementType;
        this.curlyBraceLanguage = curlyBraceLanguage;
    }
    isBlockEmpty(block, offset) {
        let _a;
        let _b;
        let trimmed = block.text.trim();
        return this.curlyBraceLanguage
            && (trimmed.startsWith('{') && (trimmed = trimmed.slice(1)),
                trimmed.endsWith('}') && (trimmed = trimmed.slice(0, -1)),
                trimmed = trimmed.trim()),
            !!(trimmed.length == 0
                || this.languageId == 'python' && (((_a = block
                                    .parent) == null
                                ? void 0
                                : _a.type) == 'class_definition'
                        || ((_b = block.parent) == null ? void 0 : _b.type) == 'function_definition')
                    && block.children.length == 1 && queryPythonIsDocstring(block.parent));
    }
    async isEmptyBlockStart(text, offset) {
        let _a;
        let _b;
        let _c;
        let _d;
        if (offset > text.length) {
            throw new RangeError('Invalid offset');
        }
        for (
            let i = offset;
            i < text.length && text.charAt(i) != `
`;
            i++
        ) {
            if (/\S/.test(text.charAt(i))) {
                return !1;
            }
        }
        offset = rewindToNearestNonWs(text, offset);
        const tree = await parseTreeSitter(this.languageId, text);
        try {
            const nodeAtPos = tree.rootNode.descendantForIndex(offset - 1);
            if (nodeAtPos == null || this.curlyBraceLanguage && nodeAtPos.type == '}') {
                return !1;
            }
            if (
                (this.languageId == 'javascript' || this.languageId == 'typescript') && nodeAtPos.parent && nodeAtPos
                        .parent
                        .type == 'object'
                && nodeAtPos.parent.text.trim() == '{'
            ) {
                return !0;
            }
            if (this.languageId == 'typescript') {
                let currNode = nodeAtPos;
                for (; currNode.parent;) {
                    if (currNode.type == 'function_signature' || currNode.type == 'method_signature') {
                        const next = nodeAtPos.nextSibling;
                        return next && currNode.hasError() && outdented(currNode, next, text)
                            ? !0
                            : !currNode.children.find(c => c.type == ';') && currNode.endIndex <= offset;
                    }
                    currNode = currNode.parent;
                }
            }
            let errorNode = null;
            let blockNode = null;
            let blockParentNode = null;
            let currNode = nodeAtPos;
            for (; currNode != null;) {
                if (currNode.type == this.blockNodeType) {
                    blockNode = currNode;
                    break;
                }
                if (this.nodeMatch[currNode.type]) {
                    blockParentNode = currNode;
                    break;
                }
                if (currNode.type == 'ERROR') {
                    errorNode = currNode;
                    break;
                }
                currNode = currNode.parent;
            }
            if (blockNode != null) {
                if (!blockNode.parent || !this.nodeMatch[blockNode.parent.type]) {
                    return !1;
                }
                if (this.languageId == 'python') {
                    const prevSibling = blockNode.previousSibling;
                    if (
                        prevSibling != null && prevSibling.hasError()
                        && (prevSibling.text.startsWith('"""') || prevSibling.text.startsWith('\'\'\''))
                    ) {
                        return !0;
                    }
                }
                return this.isBlockEmpty(blockNode, offset);
            }
            if (errorNode != null) {
                if (
                    ((_a = errorNode.previousSibling) == null ? void 0 : _a.type) == 'module' || ((_b = errorNode
                                .previousSibling) == null
                            ? void 0
                            : _b.type) == 'internal_module'
                    || ((_c = errorNode.previousSibling) == null ? void 0 : _c.type) == 'def'
                ) {
                    return !0;
                }
                const children = [...errorNode.children].reverse();
                const keyword = children.find(child => this.startKeywords.includes(child.type));
                let block = children.find(child => child.type == this.blockNodeType);
                if (keyword) {
                    switch (this.languageId) {
                        case 'python': {
                            keyword.type == 'try' && nodeAtPos.type == 'identifier' && nodeAtPos.text.length > 4
                                && (block = (_d = children.find(child => child.hasError())) == null
                                    ? void 0
                                    : _d.children.find(child => child.type == 'block'));
                            let colonNode;
                            let parenCount = 0;
                            for (const child of errorNode.children) {
                                if (child.type == ':' && parenCount == 0) {
                                    colonNode = child;
                                    break;
                                }
                                child.type == '(' && (parenCount += 1), child.type == ')' && (parenCount -= 1);
                            }
                            if (colonNode && keyword.endIndex <= colonNode.startIndex && colonNode.nextSibling) {
                                if (keyword.type == 'def') {
                                    const sibling = colonNode.nextSibling;
                                    if (
                                        sibling.type == '"' || sibling.type == '\''
                                        || sibling.type == 'ERROR' && (sibling
                                                        .text == '"""' || sibling.text == '\'\'\'')
                                    ) {
                                        return !0;
                                    }
                                }
                                return !1;
                            }
                            break;
                        }
                        case 'javascript': {
                            const formalParameters = children.find(child => child.type == 'formal_parameters');
                            if (keyword.type == 'class' && formalParameters) {
                                return !0;
                            }
                            const leftCurlyBrace = children.find(child => child.type == '{');
                            if (
                                leftCurlyBrace && leftCurlyBrace.startIndex > keyword.endIndex
                                    && leftCurlyBrace.nextSibling != null
                                || children.find(child => child.type == 'do') && keyword.type == 'while' || keyword
                                                .type == '=>' && keyword.nextSibling && keyword.nextSibling.type != '{'
                            ) {
                                return !1;
                            }
                            break;
                        }
                        case 'typescript': {
                            const leftCurlyBrace = children.find(child => child.type == '{');
                            if (
                                leftCurlyBrace && leftCurlyBrace.startIndex > keyword.endIndex
                                    && leftCurlyBrace.nextSibling != null
                                || children.find(child => child.type == 'do') && keyword.type == 'while' || keyword
                                                .type == '=>' && keyword.nextSibling && keyword.nextSibling.type != '{'
                            ) {
                                return !1;
                            }
                            break;
                        }
                    }
                    return block && block.startIndex > keyword.endIndex ? this.isBlockEmpty(block, offset) : !0;
                }
            }
            if (blockParentNode != null) {
                const expectedType = this.nodeMatch[blockParentNode.type];
                const block = blockParentNode.children.slice().reverse().find(x => x.type == expectedType);
                if (block) {
                    return this.isBlockEmpty(block, offset);
                }
                if (this.nodeTypesWithBlockOrStmtChild.has(blockParentNode.type)) {
                    const fieldLabel = this.nodeTypesWithBlockOrStmtChild.get(blockParentNode.type);
                    const child = fieldLabel == ''
                        ? blockParentNode.children[0]
                        : blockParentNode.childForFieldName(fieldLabel);
                    if (child && child.type != this.blockNodeType && child.type != this.emptyStatementType) {
                        return !1;
                    }
                }
                return !0;
            }
            return !1;
        }
        finally {
            tree.delete();
        }
    }
}

class RegexBasedBlockParser extends BaseBlockParser {
    constructor(languageId, blockEmptyMatch, lineMatch, nodeMatch, nodeTypesWithBlockOrStmtChild) {
        super(languageId, nodeMatch, nodeTypesWithBlockOrStmtChild);
        this.blockEmptyMatch = blockEmptyMatch;
        this.lineMatch = lineMatch;
    }
    isBlockStart(line) {
        return this.lineMatch.test(line.trimStart());
    }
    async isBlockBodyEmpty(text, offset) {
        let res = await this.getNextBlockAtPosition(text, offset, block => {
            block.startIndex < offset && (offset = block.startIndex);
            let blockText = text.substring(offset, block.endIndex).trim();
            return blockText == '' || blockText.replace(/\s/g, '') == this.blockEmptyMatch;
        });
        return res === void 0 || res;
    }
    async isEmptyBlockStart(text, offset) {
        return offset = rewindToNearestNonWs(text, offset),
            this.isBlockStart(getLineAtOffset(text, offset)) && this.isBlockBodyEmpty(text, offset);
    }
}

// TODO 我们没有ruby的
const languageIdToWasmLanguageMapping = {
    python: 'python',
    javascript: 'javascript',
    javascriptreact: 'javascript',
    jsx: 'javascript',
    typescript: 'typescript',
    typescriptreact: 'tsx',
    go: 'go',
    ruby: 'ruby',
};
export function isSupportedLanguageId(languageId) {
    return languageId in languageIdToWasmLanguageMapping;
}
function languageIdToWasmLanguage(languageId) {
    if (!(languageId in languageIdToWasmLanguageMapping)) {
        throw new Error(`Unrecognized language: ${languageId}`);
    }
    return languageIdToWasmLanguageMapping[languageId];
}

const wasmLanguageToBlockParser = {
    python: new TreeSitterBasedBlockParser(
        'python',
        {
            class_definition: 'block',
            elif_clause: 'block',
            else_clause: 'block',
            except_clause: 'block',
            finally_clause: 'block',
            for_statement: 'block',
            function_definition: 'block',
            if_statement: 'block',
            try_statement: 'block',
            while_statement: 'block',
            with_statement: 'block',
        },
        new Map(),
        ['def', 'class', 'if', 'elif', 'else', 'for', 'while', 'try', 'except', 'finally', 'with'],
        'block',
        null,
        !1
    ),
    javascript: new TreeSitterBasedBlockParser(
        'javascript',
        {
            arrow_function: 'statement_block',
            catch_clause: 'statement_block',
            do_statement: 'statement_block',
            else_clause: 'statement_block',
            finally_clause: 'statement_block',
            for_in_statement: 'statement_block',
            for_statement: 'statement_block',
            function: 'statement_block',
            function_declaration: 'statement_block',
            generator_function: 'statement_block',
            generator_function_declaration: 'statement_block',
            if_statement: 'statement_block',
            method_definition: 'statement_block',
            try_statement: 'statement_block',
            while_statement: 'statement_block',
            with_statement: 'statement_block',
            class: 'class_body',
            class_declaration: 'class_body',
        },
        new Map([
            ['arrow_function', 'body'],
            ['do_statement', 'body'],
            ['else_clause', ''],
            ['for_in_statement', 'body'],
            ['for_statement', 'body'],
            ['if_statement', 'consequence'],
            ['while_statement', 'body'],
            ['with_statement', 'body'],
        ]),
        ['=>', 'try', 'catch', 'finally', 'do', 'for', 'if', 'else', 'while', 'with', 'function', 'function*', 'class'],
        'statement_block',
        'empty_statement',
        !0
    ),
    typescript: new TreeSitterBasedBlockParser(
        'typescript',
        {
            ambient_declaration: 'statement_block',
            arrow_function: 'statement_block',
            catch_clause: 'statement_block',
            do_statement: 'statement_block',
            else_clause: 'statement_block',
            finally_clause: 'statement_block',
            for_in_statement: 'statement_block',
            for_statement: 'statement_block',
            function: 'statement_block',
            function_declaration: 'statement_block',
            generator_function: 'statement_block',
            generator_function_declaration: 'statement_block',
            if_statement: 'statement_block',
            internal_module: 'statement_block',
            method_definition: 'statement_block',
            module: 'statement_block',
            try_statement: 'statement_block',
            while_statement: 'statement_block',
            abstract_class_declaration: 'class_body',
            class: 'class_body',
            class_declaration: 'class_body',
        },
        new Map([
            ['arrow_function', 'body'],
            ['do_statement', 'body'],
            ['else_clause', ''],
            ['for_in_statement', 'body'],
            ['for_statement', 'body'],
            ['if_statement', 'consequence'],
            ['while_statement', 'body'],
            ['with_statement', 'body'],
        ]),
        [
            'declare',
            '=>',
            'try',
            'catch',
            'finally',
            'do',
            'for',
            'if',
            'else',
            'while',
            'with',
            'function',
            'function*',
            'class',
        ],
        'statement_block',
        'empty_statement',
        !0
    ),
    tsx: new TreeSitterBasedBlockParser(
        'typescriptreact',
        {
            ambient_declaration: 'statement_block',
            arrow_function: 'statement_block',
            catch_clause: 'statement_block',
            do_statement: 'statement_block',
            else_clause: 'statement_block',
            finally_clause: 'statement_block',
            for_in_statement: 'statement_block',
            for_statement: 'statement_block',
            function: 'statement_block',
            function_declaration: 'statement_block',
            generator_function: 'statement_block',
            generator_function_declaration: 'statement_block',
            if_statement: 'statement_block',
            internal_module: 'statement_block',
            method_definition: 'statement_block',
            module: 'statement_block',
            try_statement: 'statement_block',
            while_statement: 'statement_block',
            abstract_class_declaration: 'class_body',
            class: 'class_body',
            class_declaration: 'class_body',
        },
        new Map([
            ['arrow_function', 'body'],
            ['do_statement', 'body'],
            ['else_clause', ''],
            ['for_in_statement', 'body'],
            ['for_statement', 'body'],
            ['if_statement', 'consequence'],
            ['while_statement', 'body'],
            ['with_statement', 'body'],
        ]),
        [
            'declare',
            '=>',
            'try',
            'catch',
            'finally',
            'do',
            'for',
            'if',
            'else',
            'while',
            'with',
            'function',
            'function*',
            'class',
        ],
        'statement_block',
        'empty_statement',
        !0
    ),
    go: new RegexBasedBlockParser('go', '{}', /\b(func|if|else|for)\b/, {
        communication_case: 'block',
        default_case: 'block',
        expression_case: 'block',
        for_statement: 'block',
        func_literal: 'block',
        function_declaration: 'block',
        if_statement: 'block',
        labeled_statement: 'block',
        method_declaration: 'block',
        type_case: 'block',
    }, new Map()),
    ruby: new RegexBasedBlockParser(
        'ruby',
        'end',
        /\b(BEGIN|END|case|class|def|do|else|elsif|for|if|module|unless|until|while)\b|->/,
        {
            begin_block: '}',
            block: '}',
            end_block: '}',
            lambda: 'block',
            for: 'do',
            until: 'do',
            while: 'do',
            case: 'end',
            do: 'end',
            if: 'end',
            method: 'end',
            module: 'end',
            unless: 'end',
            do_block: 'end',
        },
        new Map()
    ),
};
export function getBlockParser(e) {
    return wasmLanguageToBlockParser[languageIdToWasmLanguage(e)];
}
