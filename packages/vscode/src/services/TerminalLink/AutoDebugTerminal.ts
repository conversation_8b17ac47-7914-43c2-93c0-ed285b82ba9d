import {spawn} from 'node:child_process';
import pWaitFor from 'p-wait-for';
import * as vscode from 'vscode';
import {arePathsEqual} from './TerminalManager';

interface Options {
    onDidCloseTerminal: () => void;
}

export class AutoDebugTerminal implements vscode.Pseudoterminal {
    private readonly _onDidWriteEmitter = new vscode.EventEmitter<string>();
    onDidWrite = this._onDidWriteEmitter.event;

    private readonly _onDidCloseEmitter = new vscode.EventEmitter<void>();
    onDidClose = this._onDidCloseEmitter.event;

    constructor(
        private options?: Options
    ) {

    }

    open() {

    }

    close() {
        this._onDidCloseEmitter.fire();
        this.options?.onDidCloseTerminal();
    }

    write(data: string) {
        const lines = data.split(/\r\n?/);
        const text = lines.join('\r\n');
        this._onDidWriteEmitter.fire(text);
    }

    handleInput(data: string) {
        if (data === '\u0003') {
            this.close();
            this.options?.onDidCloseTerminal();
        }
        this.write(data);
    }

    setOptions(options: Options) {
        this.options = options;
    }
}

export interface AutoDebugTerminalInfo {
    terminal: vscode.Terminal;
    busy: boolean;
    cwd: string;
    lastCommand: string;
    pty: AutoDebugTerminal;
}

// 自定义 pty 的 terminal，只有输出，不能手动执行命令
export class AutoDebugTerminalManager {
    private terminals: AutoDebugTerminalInfo[] = [];

    /**
     * 起一个进程执行传入的命令，然后将结果输出到自定义的terminal中
     *
     * @param commandLine 执行的命令
     * @param cwd 执行命令的目录
     */
    async runCommandInAutoDebugTerminal(
        commandLine: string,
        cwd: string
    ) {
        const process = spawn(commandLine, {cwd, shell: true});
        let killed = false;
        const terminalInfo = this.getOrCreateAutoDebugTerminal(cwd);
        terminalInfo.busy = true;
        terminalInfo.pty.setOptions({
            onDidCloseTerminal: () => {
                if (!killed) {
                    process.kill();
                    killed = true;
                }
                this.removeAutoDebugTerminal(terminalInfo);
            },
        });

        const terminal = terminalInfo.terminal;
        terminal.show();
        terminal.sendText(`> ${commandLine}\n\n`);

        let result = '';
        process.stdout.on('data', data => {
            result += data.toString();
            terminal.sendText(data.toString());
        });

        process.stderr.on('data', data => {
            result += data.toString();
            terminal.sendText(result);
        });
        let completed = false;
        process.on('close', () => {
            completed = true;
        });
        process.on('exit', () => {
            completed = true;
        });
        process.on('error', error => {
            completed = true;
            terminal.sendText(error.toString());
        });

        await pWaitFor(() => completed === true, {timeout: 5 * 60 * 1000});

        terminal.sendText('\n* Task Completed.\n');
        terminal.sendText('* Terminal will be reused by tasks, press <Ctrl+C> to close it.\n\n');
        terminalInfo.busy = false;
        return {
            completed,
            result,
            exitCode: process.exitCode,
            killed,
        };
    }

    /**
     * 获取或创建一个 AutoDebug 的调试终端
     * 如果当前工作目录已经存在一个空闲的终端，则返回该终端，否则创建一个新的 AutoDebug 调试终端
     *
     * @param cwd 当前工作目录
     * @returns 返回找到的终端信息对象，或创建的新终端信息对象
     */
    private getOrCreateAutoDebugTerminal(cwd: string) {
        const terminalInfo = this.terminals.find(t => {
            if (t.busy) {
                return false;
            }
            if (!t.cwd) {
                return false;
            }
            return arePathsEqual(cwd, t.cwd);
        });
        if (terminalInfo) {
            return terminalInfo;
        }

        const pty = new AutoDebugTerminal();
        const newTerminal = vscode.window.createTerminal({
            name: 'Comate Debug 智能体运行验证',
            pty,
            iconPath: new vscode.ThemeIcon('debug-console'),
        });

        const newTerminalInfo = {
            terminal: newTerminal,
            busy: false,
            cwd,
            lastCommand: '',
            pty,
        };
        this.terminals.push(newTerminalInfo);
        return newTerminalInfo;
    }

    private removeAutoDebugTerminal(terminal: AutoDebugTerminalInfo) {
        this.terminals = this.terminals.filter(t => t !== terminal);
    }
}
