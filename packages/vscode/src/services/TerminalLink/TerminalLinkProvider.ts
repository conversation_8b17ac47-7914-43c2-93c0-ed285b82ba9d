import {basename} from 'node:path';
import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {
    ContextType,
    EventMessage,
    BuiltinAgent,
    KnowledgeList,
    WebviewAgentConversationType,
    ApplyStatus,
} from '@shared/protocols';
import {SlashType} from '@shared/constants';
import {compact, groupBy} from 'lodash';
import pWaitFor from 'p-wait-for';
import {error, info} from '@/common/outputChannel';
import {isInternal} from '@/utils/features';
import {addMarkdownCodeBlock} from '@/utils/common';
import {vscodeCommands} from '@/utils/vscodeComands';
import {
    extractCommandFromTerminalLog,
    getTerminalProcessIdPWD,
    matchFilePath,
    matchFilePathPositionToTerminalLinkRange,
    removeTrailingEOLChar,
    splitTerminalOutputToCommandChunks,
} from '@/utils/terminal';
import {getFirstWorkspaceRepoName} from '@/utils/workspace';
import {L10n} from '@/common/L10nProvider/L10n';
import {AutoWorkText} from '@/common/L10nProvider/constants';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {
    CMD_CHAT_WITH_TERMINAL_SELECTION_CONTEXT,
} from '../AutoComateChatSession/constants';
import {ErrorRegexRule, autoDebugAPIs} from '../AutoComateChatSession/autoDebugAPI';
import {LogCategory, LogUploaderProvider} from '../LogUploaderProvider';
import {autoDebugSearchTask} from '../AutoComateChatSession/autoDebugSearchTask';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {computeRepoId} from '../EmbeddingsService/embeddingUtils';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';
import {ChatViewProvider} from '../ChatViewProvider';

type ExecutableTerminalLink = vscode.TerminalLink & {data: string};

const CMD_AUTO_DEBUG_ACTION = 'baidu.comate.autoDebugAction';

const SHORT_DETAIL_MAX_LENGTH = 52;

const HOVER_VALID_MAX_DURATION = 3 * 1000;

interface ExecutableHoveredLink {
    timestamp: number;
    data: string;
}

interface AutoDebugContext {
    errorText: string;
    matchRule: ErrorRegexRule;
    chunk: string[];
}

const getFisrtNotSpaceCharPosition = (input: string) => {
    return input.trimStart() === input ? 0 : input.length - input.trimStart().length;
};

const getTermialLinkRange = (input: string) => {
    return {
        startIndex: getFisrtNotSpaceCharPosition(input),
        length: input.trim().length,
    };
};

const formatJsFamilyLanguage = (language?: string) => {
    if (language === 'typescript' || language === 'tsx') {
        return 'javascript';
    }
    return language;
};

const extractErrorDisplayText = (errorText: string) => {
    const ranges = matchFilePathPositionToTerminalLinkRange(errorText);
    if (ranges.length) {
        const texts = ranges.map(range => errorText.slice(range.startIndex, range.startIndex + range.length));
        return (texts.pop() ?? '').trim();
    }
    else {
        return errorText.trim();
    }
};

const sliceLogRangeWithConfig = (lines: string[], data: string, forwardRangeNum: number, backRangeNum: number) => {
    const index = lines.lastIndexOf(removeTrailingEOLChar(data));
    return lines.slice(
        Math.max(index - forwardRangeNum, 0),
        index + backRangeNum + 1
    );
};

interface RangeInfo {
    regName: string;
    ranges: Array<ReturnType<typeof getTermialLinkRange>>;
}

@injectable()
export class TerminalLinkProvider implements vscode.TerminalLinkProvider<ExecutableTerminalLink> {
    executableHoveredLink: ExecutableHoveredLink | undefined = undefined;
    disposables: vscode.Disposable[] = [];

    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(LogUploaderProvider) private readonly loggerProvider: LogUploaderProvider,
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(TreeSitterProvider) readonly treeSitterProvider: TreeSitterProvider,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider
    ) {
        this.disposables.push(vscode.window.onDidChangeActiveTerminal(this.initTerminalMatchRegex.bind(this)));
        this.disposables.push(vscode.window.onDidOpenTerminal(this.initTerminalMatchRegex.bind(this)));
    }

    start() {
        this.disposables.push(vscode.window.registerTerminalLinkProvider(this));
        this.disposables.push(vscode.commands.registerCommand(
            CMD_CHAT_WITH_TERMINAL_SELECTION_CONTEXT,
            this.chatWithTerminalSelectionContext.bind(this)
        ));
        this.disposables.push(vscode.commands.registerCommand(
            CMD_AUTO_DEBUG_ACTION,
            (data: AutoDebugContext) => {
                this.chatWithTerminalSelectionContext(data.chunk, data.matchRule, 'CODE_BUTTON');
                this.loggerProvider.logUserAction({
                    category: LogCategory.AutoDebug,
                    label: 'AutoDebugCodeLens',
                    content: data,
                    action: 'click',
                });
            }
        ));
        this.disposables.push(vscode.window.onDidChangeTextEditorSelection(async e => {
            const link = this.getDocumentRelatedTerminalLink(e);
            const isSelectionChangeDueToTerminalLink = !!link;
            if (!isSelectionChangeDueToTerminalLink) {
                return;
            }
            const autoDebugContext = await this.matchAutoDebugError(
                link.data,
                e.textEditor.document.languageId
            );
            if (autoDebugContext) {
                this.displayTmpCodelens(e.textEditor, autoDebugContext);
                // 防止多次触发，但是文档内容变了
                this.executableHoveredLink = undefined;
                this.loggerProvider.logUserAction({
                    category: LogCategory.AutoDebug,
                    label: 'AutoDebugCodeLens',
                    content: autoDebugContext.errorText,
                    action: 'recognized',
                });
            }
        }));
        this.initTerminalMatchRegex();
    }

    private errorOutputSingleLineRegexps: Array<ErrorRegexRule & {language: string}> = [];
    private lastUpdateTime: number = 0;
    private async initTerminalMatchRegex() {
        if ((Date.now() - this.lastUpdateTime) < (60 * 60 * 1000)) {
            return;
        }

        const userName = this.configProvider.getConfig<string>(ConfigKey.Username);
        if (userName) {
            this.lastUpdateTime = Date.now();
            try {
                info(`TerminalLinkProvider: regex rule updated at ${this.lastUpdateTime}`);
                const response = await autoDebugAPIs.getTerminalErrorOutputSingleLineRegexp({userName});
                this.errorOutputSingleLineRegexps = Object.entries(response).reduce<
                    Array<ErrorRegexRule & {language: string}>
                >(
                    (result, [language, rules]) => {
                        result.push(...rules.map(rule => ({...rule, language})));
                        return result;
                    },
                    []
                );
            }
            catch (e) {
                error('TerminalLinkProvider: fetch regex failed');
            }
        }
    }

    async chatWithTerminalSelectionContext(
        logs: string[] | vscode.Terminal,
        rule: ErrorRegexRule,
        customizedTriggerType?: string
    ) {
        const terminal = vscode.window.activeTerminal as vscode.Terminal & {selection: string};
        if (!terminal) {
            return;
        }
        const logCodeBlock = Array.isArray(logs) ? logs.join('\n') : terminal.selection;
        const processId = await terminal.processId;
        if (logCodeBlock) {
            const pwd = await getTerminalProcessIdPWD(processId, {defaultRoot: vscode.env.appRoot});
            const triggerType = customizedTriggerType || (Array.isArray(logs) ? 'CONSOLE_HOVER' : 'CONSOLE_SELECT');
            const debugAgentEnable = await this.isDebugAgentEnable();
            if (debugAgentEnable) {
                const success = await this.askDebugAgent(terminal, logCodeBlock, pwd, triggerType);
                // 调用不成功的话回到 AutoWork，可能是 Webview 初始话超时了，处理 Agent 相应的事件丢了
                if (!success) {
                    this.askAutoWork(logCodeBlock, rule, pwd, triggerType);
                }
            }
            else {
                this.askAutoWork(logCodeBlock, rule, pwd, triggerType);
            }
        }

        if (!Array.isArray(logs)) {
            this.loggerProvider.logUserAction({
                category: LogCategory.AutoDebug,
                label: 'chunk',
                content: terminal.selection.slice(0, 1000),
                action: 'selection',
            });
        }
    }

    async askDebugAgent(
        terminal: vscode.Terminal,
        logCodeBlock: string,
        pwd: string,
        triggerType: string
    ) {
        vscodeCommands.openChatPanel({source: 'debug', activeTabKey: 'AGENT'});
        const shellPath = (terminal.creationOptions as vscode.TerminalOptions).shellPath;
        const commandLine = await extractCommandFromTerminalLog(logCodeBlock, shellPath);
        // TODO: 后面把这个逻辑干掉，触发智能体不要走 webview，直接请求 engine 触发
        await this.waitForWebviewReady();
        const success = await this.chatViewProvider.sendDataToWebview(
            EventMessage.AgentConversationAddFromIdeEvent,
            {
                conversationId: '',
                payload: {
                    query: '请帮我分析终端的报错日志，并提供解决方案。',
                    code: addMarkdownCodeBlock(logCodeBlock, 'text'),
                    command: {pwd, commandLine},
                    platform: 'VSCODE',
                    contexts: {triggerType, ideVersion: vscode.version},
                    cwd: pwd,
                },
                messageType: 'add-conversation',
                conversationType: WebviewAgentConversationType.DebugBotConversation,
            }
        );
        if (success) {
            this.loggerProvider.logUserAction({
                category: LogCategory.AutoDebug,
                label: 'DebugAgent',
                content: commandLine,
            });
        }
        return success;
    }

    async askAutoWork(
        logCodeBlock: string,
        rule: ErrorRegexRule,
        pwd: string,
        triggerType: string
    ) {
        let contexts = {};
        if (rule.contexts?.length) {
            const repoId = await computeRepoId(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath);
            const chunks = await Promise.all(rule.contexts.map(
                strategy => autoDebugSearchTask(repoId!, this.treeSitterProvider, strategy)
            ));
            contexts = groupBy(compact(chunks), 'type');
        }
        const knowledgeList: KnowledgeList[] = [
            {
                content: addMarkdownCodeBlock(logCodeBlock, 'text'),
                type: ContextType.CODE,
                id: '',
                name: '',
            },
            {
                path: pwd,
                type: ContextType.TERMINAL,
                content: JSON.stringify({triggerType, ...contexts}),
                id: '',
                name: '',
            },
        ];
        vscodeCommands.openChatPanel({source: 'debug'});
        vscodeCommands.askAutoWork(
            {
                query: '请帮我分析终端的报错日志，并提供解决方案。只解决第一个报错',
                agent: BuiltinAgent.Comate,
                slash: SlashType.AUTO_DEBUG,
                knowledgeList,
            }
        );
    }

    matchRule(content: string) {
        const lines = content.split('\n');
        for (const line of lines) {
            const matchRule = this.executeRegexMatch(line);
            if (matchRule) {
                const backRangeNum = matchRule?.backRangeNum ?? 10;
                const forwardRangeNum = matchRule?.forwardRangeNum ?? 10;
                const logLines = lines.length >= 40
                    ? sliceLogRangeWithConfig(lines, line, forwardRangeNum, backRangeNum)
                    : lines;
                return {matchRule, chunk: logLines};
            }
        }
        return undefined;
    }

    async handleTerminalLink(link: ExecutableTerminalLink): Promise<void> {
        return this.doAutoDebugTerminalLink(link);
    }

    async doAutoDebugTerminalLink(link: {data: string, triggerType?: string}) {
        const terminalOutput = await vscodeCommands.exposeTerminalOutput();
        const chunks = splitTerminalOutputToCommandChunks(terminalOutput);
        const regexpRule = this.executeRegexMatch(link.data)!;
        const backRangeNum = regexpRule?.backRangeNum ?? 10;
        const forwardRangeNum = regexpRule?.forwardRangeNum ?? 10;
        const chunk = chunks.find(chunk => chunk.some(line => line === removeTrailingEOLChar(link.data)));
        if (chunk && chunk.length !== 1 && chunk.length < 40) {
            this.chatWithTerminalSelectionContext(chunk, regexpRule, link.triggerType);

            this.loggerProvider.logUserAction({
                category: LogCategory.AutoDebug,
                label: regexpRule?.regName || '',
                content: chunk.slice(0, 10),
                action: 'click',
            });
        }
        else {
            const logLines = (chunk && chunk.length > 1) ? chunk : terminalOutput.split('\n');
            const slicedRange = sliceLogRangeWithConfig(logLines, link.data, forwardRangeNum, backRangeNum);
            this.chatWithTerminalSelectionContext(slicedRange, regexpRule);

            this.loggerProvider.logUserAction({
                category: LogCategory.AutoDebug,
                label: regexpRule?.regName || '',
                content: slicedRange,
                action: 'click',
            });
        }
    }

    executeRegexMatch(line: string, language?: string) {
        const errorRegexps = language
            ? this.errorOutputSingleLineRegexps.filter(v => v.language === language)
            : this.errorOutputSingleLineRegexps;
        return errorRegexps.find(({reg}) => {
            try {
                const result = new RegExp(reg).test(line);
                return result;
            }
            catch (ex) {
                error('TerminalLinkProvider: reg initialized failed, reg: ', reg);
                this.errorOutputSingleLineRegexps = this.errorOutputSingleLineRegexps.filter(i => reg === i.reg);
                return false;
            }
        });
    }

    private getDocumentRelatedTerminalLink(e: vscode.TextEditorSelectionChangeEvent) {
        if (!this.executableHoveredLink) {
            return undefined;
        }
        if (Date.now() - this.executableHoveredLink.timestamp > HOVER_VALID_MAX_DURATION) {
            return undefined;
        }
        const {document, selection} = e.textEditor;
        const filename = basename(document.uri.fsPath);
        const line = selection.start.line + 1;
        const character = selection.start.character + 1;
        const matchText = `${filename}:${line}`;
        const {data: text} = this.executableHoveredLink;
        // TODO：这里后面可以翻一下terminal跳转文档是怎么实现的
        if (
            text.includes(matchText)
            || new RegExp(`${filename}(.*)line ${line}`).exec(text)
            || text.includes(`${filename}(${line},${character})`)
            || new RegExp(`${filename}", line ${line}`).exec(text)
        ) {
            return this.executableHoveredLink;
        }
        return undefined;
    }

    private async matchAutoDebugError(link: string, language: string) {
        const terminalOutput = await vscodeCommands.exposeTerminalOutput();
        const chunks = splitTerminalOutputToCommandChunks(terminalOutput);
        const chunk = chunks.find(chunk => chunk.some(line => line === link));
        if (!chunk) {
            return undefined;
        }
        const languageId = formatJsFamilyLanguage(language);
        for (const line of chunk) {
            // 这个python的通用错误信息没什么用，先手动过滤掉，后面看下这里是不是规则上去掉
            if (line.includes('Traceback (most recent call last):')) {
                continue;
            }
            const matchRule = this.executeRegexMatch(line, languageId);
            if (matchRule) {
                return {errorText: line, matchRule, chunk};
            }
        }
        return undefined;
    }

    private displayTmpCodelens(textEditor: vscode.TextEditor, context: AutoDebugContext) {
        const errorDetail = extractErrorDisplayText(context.errorText);
        const displayText = errorDetail ? `: ${errorDetail}` : '';
        const shortDetail = displayText.length > SHORT_DETAIL_MAX_LENGTH
            ? displayText.slice(0, SHORT_DETAIL_MAX_LENGTH) + '...'
            : displayText;
        this.tmpCodeLensProvider.triggerDisplayTmpCodeLens({
            document: textEditor.document,
            range: textEditor.selection,
            title: `$(debug) ${L10n.t(AutoWorkText.AUTO_DEBUG)}${shortDetail}`,
            tooltip: L10n.t(AutoWorkText.AUTO_DEBUG) + displayText,
            command: CMD_AUTO_DEBUG_ACTION,
            data: context,
        });
    }

    /** 日志缓存，存储的是每一行的日志字符串，去重，其它程序通过序号来还原内容 */
    private readonly logLineIndexes: string[] = [];
    /** 活跃日志的顺序队列，以缓存中的序号来存储 */
    private readonly logIndexQueue: number[] = [];
    /** 日志正则匹配的结果，其它程序通过日志在`logLineIndexes`里的序号来获取结果 */
    private readonly regexMatchedResult: Array<boolean | RangeInfo> = [];

    private matchRegex(sequence: number, line: string): boolean | RangeInfo {
        if (this.errorOutputSingleLineRegexps.length) {
            const alreadyExecutedMatch = typeof this.regexMatchedResult[sequence] === 'boolean';
            if (alreadyExecutedMatch) {
                return this.regexMatchedResult[sequence];
            }
            else {
                const result = this.executeRegexMatch(line);
                if (result && isInternal) {
                    this.loggerProvider.logUserAction({
                        category: LogCategory.AutoDebug,
                        label: result.regName,
                        content: line,
                        action: 'recognized',
                    });
                }

                if (result) {
                    const ranges = matchFilePathPositionToTerminalLinkRange(line);
                    const rangeInfo: RangeInfo = {
                        ranges: ranges.length ? ranges : [getTermialLinkRange(line)],
                        regName: result.regName,
                    };
                    this.regexMatchedResult[sequence] = rangeInfo;
                    return rangeInfo;
                }
                this.regexMatchedResult[sequence] = !!result;
                return this.regexMatchedResult[sequence];
            }
        }
        return false;
    }

    provideTerminalLinks(context: vscode.TerminalLinkContext): vscode.ProviderResult<ExecutableTerminalLink[]> {
        const pretteriedLine = removeTrailingEOLChar(context.line);
        this.onDidTerminalLinkHookActive(pretteriedLine);
        const insertQueueIndex = this.updateLogCacheAndReturnInsertIndex(pretteriedLine);
        const sequence = this.logIndexQueue[insertQueueIndex];
        const matched = this.matchRegex(sequence, pretteriedLine);

        if (matched) {
            const ranges = typeof matched === 'boolean'
                ? [getTermialLinkRange(pretteriedLine)]
                : matched.ranges;
            const tooltip = typeof matched !== 'boolean' && matched.regName === 'JS_WEBPACK_ERROR'
                ? '分析与修复webpack构建问题'
                : 'Comate分析与修复';
            return ranges.map(range => ({
                ...range,
                tooltip,
                data: pretteriedLine,
            }));
        }
        // const links: ExecutableTerminalLink[] = [];
        // const uriRegex = new RegExp(`${ExecutableLinkScheme}://[^\\s]+`, 'g');
        // todo: 实现匹配链接的逻辑
        return [];
    }

    private updateLogCacheAndReturnInsertIndex(line: string) {
        const prevLogIndex = this.logLineIndexes.indexOf(line);
        if (prevLogIndex === -1) {
            this.logLineIndexes.push(line);
            this.logIndexQueue.push(this.logLineIndexes.length - 1);
        }
        else {
            this.logIndexQueue.push(prevLogIndex);
        }
        return this.logIndexQueue.length - 1;
    }

    private firstActiveTime: number | undefined;
    private async onDidTerminalLinkHookActive(link: string) {
        if (matchFilePath(link).length > 0) {
            this.executableHoveredLink = {
                data: link,
                timestamp: Date.now(),
            };
        }
        if (!this.firstActiveTime || !isInternal) {
            this.firstActiveTime = Date.now();
            return;
        }

        // 首次激活后，过10分钟触发进行一次上报
        if ((Date.now() - this.firstActiveTime) > 10 * 60 * 1000) {
            const output = await vscodeCommands.exposeTerminalOutput();
            const errorLogLines = output.split('\n');
            if (errorLogLines.length > 10) {
                const takeLatestLines = errorLogLines.slice(Math.max(errorLogLines.length - 100, 0), Infinity);
                this.reportLogLines(takeLatestLines.join('\n'));
            }
            this.firstActiveTime = Date.now();
        }
    }

    private async reportLogLines(errorLog: string) {
        if (!isInternal) {
            return;
        }

        try {
            const [userName, repo] = await Promise.all([
                this.configProvider.getConfig(ConfigKey.Username),
                getFirstWorkspaceRepoName().catch(() => ''),
            ]);

            autoDebugAPIs.reportTerminalLogLines({
                userName: String(userName),
                repo,
                errorLog,
                platform: 'VSCODE',
            });
        }
        catch (ex) {
            error('reportLogLines failed, reason: ', (ex as Error).message);
        }
    }

    private async isDebugAgentEnable() {
        if (!isInternal) {
            // vscode 目前只在厂内开启
            return false;
        }
        if (!this.chatViewProvider.engineInitialized) {
            return false;
        }
        const agentConfigs = await this.configProvider.getAgentConfig();
        const approved = agentConfigs.applyStatus?.debugIntelligenceApplyStatus === ApplyStatus.Approved;
        return approved && agentConfigs.enableIntelligenceAgent?.enableDebugIntelligence;
    }

    /**
     * 因为 debug 智能体是通过发事件给 webview 再到 engine 触发的
     * 所以这里需要先等待 WebView 准备就绪，第一次打开时可能会有点慢，等个 2s
     */
    private async waitForWebviewReady() {
        if (this.chatViewProvider.webviewInitialized) {
            return;
        }
        return pWaitFor(() => this.chatViewProvider.webviewInitialized, {timeout: 2000});
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
    }
}
