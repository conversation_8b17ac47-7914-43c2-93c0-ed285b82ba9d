import * as vscode from 'vscode';
import {InlineCompletionItem} from '../types';

export class InlineState {
    #prefix: string | null = null;
    #suffix: string | null = null;
    #cacheKey: string | null = null;
    #previousHiddenKey: string | null = null;

    assign(prefix: string, suffix: string, cacheKey: string) {
        this.#prefix = prefix;
        this.#suffix = suffix;
        this.#cacheKey = cacheKey;
    }

    setHiddenKey(key?: string) {
        this.#previousHiddenKey = key ?? null;
    }

    get prefix() {
        return this.#prefix;
    }

    get suffix() {
        return this.#suffix;
    }

    get cacheKey() {
        return this.#cacheKey;
    }

    get previousHiddenKey() {
        return this.#previousHiddenKey;
    }
}

export class DisplayState {
    #displayedCompletions: InlineCompletionItem[] = [];
    #selectedCompletion: string | undefined = undefined;
    #lastPosition: vscode.Position | undefined = undefined;
    #lastUri: vscode.Uri | undefined = undefined;

    setSelected(id: string) {
        this.#selectedCompletion = id;
    }

    addShownCompletion(item: InlineCompletionItem) {
        this.#displayedCompletions.push(item);
    }

    reset(position: vscode.Position, uri: vscode.Uri) {
        this.#lastPosition = position;
        this.#lastUri = uri;
        this.#displayedCompletions = [];
    }

    unset() {
        this.#lastPosition = undefined;
        this.#lastUri = undefined;
        this.#displayedCompletions = [];
    }

    get lastPosition() {
        return this.#lastPosition;
    }

    get lastUri() {
        return this.#lastUri;
    }

    get selectedCompletion() {
        return this.#selectedCompletion;
    }

    get displayedCompletions() {
        return this.#displayedCompletions;
    }
}
