/* eslint-disable @typescript-eslint/no-unused-vars */
import {injectable} from 'inversify';
import * as vscode from 'vscode';
import {ICompletionSuccessRateTracker} from './types';

@injectable()
export class EmptyCompletionSuccessRateTracker implements ICompletionSuccessRateTracker {
    trackSuccess(_uri?: vscode.Uri, _line?: number) {
        // do nothing
    }

    trackFailure(_uri?: vscode.Uri, _line?: number) {
        // do nothing
    }

    dispose() {
        // do nothing
    }
}
