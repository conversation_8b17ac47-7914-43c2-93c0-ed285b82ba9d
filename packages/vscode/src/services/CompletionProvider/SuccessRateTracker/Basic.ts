import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {track} from '../../../api';
import {UserService} from '../../UserService';
import {ICompletionSuccessRateTracker} from './types';

@injectable()
export class BasicCompletionSuccessRateTracker implements ICompletionSuccessRateTracker {
    private disposables: vscode.Disposable[] = [];
    private readonly reportPeriod = 100;
    private successCount = 0;
    private failureCount = 0;

    private lastUri: vscode.Uri | undefined;
    private lastLine: number | undefined;
    private lastCompletionStatus: string | undefined;

    constructor(@inject(UserService) private readonly userService: UserService) {}

    private async track() {
        const [username] = await this.userService.getCurrentUser().catch(() => ['unknown']);
        if (this.successCount >= this.reportPeriod) {
            this.successCount -= this.reportPeriod;
            track({
                username: username,
                category: 'completion',
                action: 'success',
                operation: {count: this.reportPeriod},
            });
        }
        else if (this.failureCount >= this.reportPeriod) {
            this.failureCount -= this.reportPeriod;
            track({
                username: username,
                category: 'completion',
                action: 'fail',
                operation: {count: this.reportPeriod},
            });
        }
    }

    trackSuccess(uri?: vscode.Uri, line?: number) {
        if (uri && uri.fsPath === this.lastUri?.fsPath && line === this.lastLine) {
            this.lastCompletionStatus = 'success';
        }
        else {
            if (this.lastCompletionStatus === 'success') {
                this.successCount += 1;
            }
            else if (this.lastCompletionStatus === 'fail') {
                this.failureCount += 1;
            }
            this.track();

            this.lastUri = uri;
            this.lastLine = line;
            this.lastCompletionStatus = 'success';
        }
    }

    trackFailure(uri?: vscode.Uri, line?: number) {
        if (uri && uri.fsPath !== this.lastUri?.fsPath || line !== this.lastLine) {
            if (this.lastCompletionStatus === 'success') {
                this.successCount += 1;
            }
            else if (this.lastCompletionStatus === 'fail') {
                this.failureCount += 1;
            }
            this.track();

            this.lastUri = uri;
            this.lastLine = line;
            this.lastCompletionStatus = 'fail';
        }
    }

    async dispose() {
        const [username] = await this.userService.getCurrentUser().catch(() => ['unknown']);

        if (this.successCount > 0) {
            track({
                username: username,
                category: 'completion',
                action: 'success',
                operation: {count: this.successCount},
            });
        }
        if (this.failureCount > 0) {
            track({
                username: username,
                category: 'completion',
                action: 'fail',
                operation: {count: this.failureCount},
            });
        }
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
