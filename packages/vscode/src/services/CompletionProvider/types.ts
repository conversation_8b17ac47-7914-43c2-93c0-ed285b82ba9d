import {URI} from 'vscode-uri';
import {RewriteBaseResult} from '@/api/smartTab';
import {IProgrammingContextTracker} from '../ProgrammingContextTracker/types';

export interface RewriteHandler {
    contextTracker: IProgrammingContextTracker;
    showRewrite: (uri: URI, value: Omit<RewriteBaseResult, 'path' | 'score'>) => void;
    getEnabled: () => boolean;
    shouldSkip: (uri: URI, startLine: number, endLine: number) => boolean;
}
