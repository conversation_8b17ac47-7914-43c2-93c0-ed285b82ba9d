/* eslint-disable complexity, max-statements, max-lines */
import * as vscode from 'vscode';
import {compact} from 'lodash';
import {Container} from 'inversify';
import {
    checkTreeSitterSupport,
    cursorAtEmptyLine,
    isBlockComment,
} from '@/utils/treeSitterUtils';
import logger from '@/common/consoleLogger';
import captureActualCodeAfterGeneration from '@/utils/captureActualCodeAfterGeneration';
import {sleep} from '@/utils/common';
import {TelemetryData, telemetrizePromptLength} from '@/common/telemetry';
import {isInternal} from '@/utils/features';
import {Debouncer} from '../../utils/Debouncer';
import {keyForPrompt, LRUCache} from '../../utils/cache';
import {RawGenerateCode} from '../../api';
import {
    ResultType,
    CacheCompletionItem,
    CompletionChoice,
    CompletionResult,
    UnsuccessReturn,
    SuccessReturn,
    GhostTextReturn,
    CompletionSource,
} from '../types';
import {buildParams, fetchInlineEdit} from '../../common/Fetcher';
import {UserService} from '../UserService';
import {ComateStatusBar} from '../StatusBar';
import {extractPrompt, PromptInfo, PromptContext} from '../../common/prompt/extractPrompt';
import {DEBOUNCE_LIMIT} from '../../constants';
import {normalizeIndentCharacter} from '../../utils/normalizeIndentCharacter';
import {isLastLineEmpty} from '../../utils/indent';
import {VSCodeConfigProvider} from '../ConfigProvider';
import {CompletionContextProvider, CompletionContextResolver} from '../CompletionContextProvider';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {LocationFactory} from '../Helper/LocationFactory';
import {contextualFilterScore} from '../Helper/ContextualFilter';
import {InlineState} from './State';
import {checkShouldCoverSuffix, postProcessCompletion} from './postProcess';
import {COMPLETION_ITEM_KEYWORD_BLACKLIST} from './dict';
import {LoadingDecorations} from './LoadingDecorations';
import {LineCommentDecorations} from './LineCommentDecorations';
import {EnhanceCompletionProvider} from './EnhanceCompletionProvider';
import CompletionPositionChecker from './CompletionPositionChecker';
import {RewriteHandler} from './types';

const debouncer = new Debouncer();

export const inlineState = new InlineState();

export const completionCache = new LRUCache<CacheCompletionItem>(100);

function getDebounceLimit(configProvider: VSCodeConfigProvider, prompt: PromptInfo) {
    // TODO copilot
    const normalLimit = configProvider.getRequestDelay() ?? DEBOUNCE_LIMIT;
    if (
        prompt.prefix.endsWith('.')
        || prompt.prefix.endsWith('::')
        || prompt.prefix.endsWith('->')
    ) {
        return configProvider.getRequestDelay('extremeAccurate') ?? 125;
    }
    return normalLimit;
}

function getEditorOptions(document: vscode.TextDocument) {
    const editor = vscode.window.visibleTextEditors.find(editor => {
        return editor.document.uri.toString() === document.uri.toString();
    });
    return editor?.options;
}

function createCompletionChoice(
    document: vscode.TextDocument,
    prompt: PromptInfo,
    data: RawGenerateCode,
    originalText?: string,
    source?: CompletionSource
): CompletionChoice {
    const [startLine, startCharacter, endLine, endCharacter] = data.range;
    const expectCoverText = document.getText(
        new vscode.Range(startLine - 1, startCharacter - 1, endLine - 1, endCharacter - 1)
    );
    const options = getEditorOptions(document);
    const isEmptyOrWhitespace = isLastLineEmpty(prompt.prefix);
    // NOTE: 根据当前编辑器设置，处理 \t 和空格
    const rawCompletion = isEmptyOrWhitespace && options
        ? normalizeIndentCharacter(data.content, options)
        : data.content;
    return {
        rawData: data,
        expectCoverText,
        completionText: prompt.trailingWs + rawCompletion,
        originalText: originalText ?? data.content,
        source: source ?? CompletionSource.Normal,
    };
}

/**
 * 尝试为候选项填充内容，但是api限制在显示候选弹窗时，ghosttext内容只能必须以候选词为前缀
 * 举例,^表示光标位置
 * <div i^/> 候选词 id，对外显示是id，但其实补全之后是 id=""，如下
 * <div id=""/>
 * 所以如果在选中候选词 id 时，同时显示 ghost text，那么 ghost text 内容必须 id="" 开头，后续内容无所谓
 * 这就导致无法在 "" 中间填充
 */
// function getInsertPositionFromCompletionText(selectedCompletionInfo: vscode.SelectedCompletionInfo) {
//     const {range, text} = selectedCompletionInfo;
//     const insertFlags = ['()', '""', '\'\'', '{}'];
//     const flag = insertFlags.find(v => text.endsWith(v));
//     if (flag) {
//         return range.start.character + text.length;
//     }
//     return range.start.character + text.length + 1;
// }

function getCompletionWidgetAgumentedContext(
    document: vscode.TextDocument,
    position: vscode.Position,
    completionContext: vscode.InlineCompletionContext
) {
    if (!completionContext.selectedCompletionInfo) {
        return undefined;
    }
    const textBeforeCursor = document.getText(
        new vscode.Range(new vscode.Position(position.line, 0), position)
    );
    if (COMPLETION_ITEM_KEYWORD_BLACKLIST.some(v => textBeforeCursor.endsWith(v))) {
        return undefined;
    }
    const {
        text: selectedCompletionText,
        range: selectedCompletionRange,
    } = completionContext.selectedCompletionInfo;
    // 需要构建候选项被选中后的文件内容，prefix以及suffix均以候选项覆盖范围的前后位置为准
    const prefix = document.getText(
        new vscode.Range(new vscode.Position(0, 0), selectedCompletionRange.start)
    );
    const suffix = document.getText(
        new vscode.Range(selectedCompletionRange.end, new vscode.Position(document.lineCount, 0))
    );
    const content = prefix + selectedCompletionText + suffix;
    const insertRow = selectedCompletionRange.end.line + 1;
    const insertCol = selectedCompletionRange.start.character + selectedCompletionText.length + 1;
    return {
        content,
        row: insertRow,
        col: insertCol,
    };
}

// 在 debounce 之后请求服务之前有一段预处理逻辑，这里有个预估的耗时
const ESTIMATED_PREPROCESSING_COMPUTE_TIME = 30;

async function getNetworkCompletion(
    globalContext: Container,
    document: vscode.TextDocument,
    position: vscode.Position,
    promptContext: PromptContext,
    cancelToken: vscode.CancellationToken,
    completionContext: vscode.InlineCompletionContext,
    completionContextProvider?: CompletionContextProvider,
    // 触发续写时 document 的版本号
    docVersionOnCall?: number,
    isCommentToCodeMode?: boolean,
    showLoading?: (condition?: () => boolean) => Promise<boolean>,
    rewriteHandler?: RewriteHandler
): Promise<SuccessReturn<CompletionResult> | UnsuccessReturn> {
    const configProvider = globalContext.get<VSCodeConfigProvider>(VSCodeConfigProvider);
    const enhanceCompletionProvider = globalContext.get<EnhanceCompletionProvider>(EnhanceCompletionProvider);
    // 续写上下文是非必要数据，如果有异常不希望影响续写逻辑，因此这里保守一些
    // eslint-disable-next-line @typescript-eslint/init-declarations
    let completionContextResolver: CompletionContextResolver | undefined;
    try {
        completionContextResolver = completionContextProvider?.provideCompletionContextResolver(document, position);
    }
    catch (e) {
        console.error(`Unable to get completion context resolver: ${e}`);
    }
    const prompt = promptContext.prompt;
    const statusBar = globalContext.get(ComateStatusBar);
    // 实际 debounce 的时间应当减去 debounce 后预处理花费的时间
    const requiredDebounceLimit = getDebounceLimit(configProvider, prompt);
    const debounceDuration = completionContext.triggerKind === vscode.InlineCompletionTriggerKind.Invoke
        ? 0
        : Math.max(requiredDebounceLimit - ESTIMATED_PREPROCESSING_COMPUTE_TIME, 0);
    const debounceStartTime = performance.now();

    if (debounceDuration > 0) {
        try {
            await debouncer.debounce(debounceDuration);
        }
        catch {
            return {
                type: 'cancelled',
                reason: 'by debouncer',
            };
        }
        if (cancelToken.isCancellationRequested) {
            return {
                type: 'cancelled',
                reason: 'during debounce',
            };
        }
    }

    const t0 = performance.now();
    const neighborSnippetList = await completionContextResolver?.resolve();
    const t1 = performance.now();
    if (neighborSnippetList) {
        logger.debug(`(context): added ${(t1 - t0).toFixed(2)}ms prior to completion request`, neighborSnippetList);
        logger.debug(`(context): ${neighborSnippetList.length} snippets attached`);
    }

    const params = await buildParams(document, position, globalContext.get(UserService));

    if (cancelToken.isCancellationRequested) {
        return {
            type: 'cancelled',
            reason: 'during build params',
        };
    }

    if (params.type !== 'success') {
        return params;
    }

    if (isCommentToCodeMode) {
        params.value.manualTriggerType = 'commentEnhancement';
    }

    const agumentedContext = getCompletionWidgetAgumentedContext(document, position, completionContext);

    if (agumentedContext) {
        params.value.content = agumentedContext.content;
        params.value.row = String(agumentedContext.row);
        params.value.col = String(agumentedContext.col);
    }

    if (neighborSnippetList) {
        params.value.neighborSnippetList = JSON.stringify(neighborSnippetList);
    }

    // NOTE: 这里检查了 document 版本是否和触发时一致，若不一致说明用户从触发到运行至此处代码之间又修改了内容。
    // 因为 position 仍是触发时的位置，但文件内容已是最新，可能会遇到不合法的行中间触发，模型输出异样的情况：
    // e.x.
    // 触发：console.l|
    // 执行至此处：console.lo| <-- 虽然光标后移了，但 position 仍是触发时的，相当于触发了不合法的行中间续写
    // 模型输出：og('hello');o <-- 注意模型会在末尾补了一个 o
    if (docVersionOnCall !== undefined && document.version !== docVersionOnCall) {
        return {
            type: 'aborted',
            reason: 'document was changed when preparing the completion request',
        };
    }
    if (cancelToken.isCancellationRequested) {
        return {
            type: 'cancelled',
            reason: 'vscode cancelled',
        };
    }
    try {
        // 请求前改一下行位置和全文内容
        if (promptContext.type === 'notebookContext') {
            params.value.content = promptContext.fullText;
            params.value.row = `${promptContext.position.line + 1}`;
        }

        // 判断是否为 api 补全
        const isInRequestFunction = enhanceCompletionProvider.isInRequestContext(document, position);
        const isInsider = isInRequestFunction && await enhanceCompletionProvider.getAccessTo(params.value);
        const isRealApiCompleteSubmit = isInRequestFunction
            && await enhanceCompletionProvider.isRealSubmit(document, position);
        const isAPICompletionCondition = isInRequestFunction && isInternal && isInsider
            && isRealApiCompleteSubmit
            && !agumentedContext;

        // 当实际预处理时间小于预估，之前的 debounce 可能少了，这里再补上
        const timeElapsedBeforeFetch = performance.now() - debounceStartTime;
        const extraWaitTime = Math.floor(requiredDebounceLimit - timeElapsedBeforeFetch);
        if (extraWaitTime > 0) {
            await sleep(extraWaitTime);
            if (cancelToken.isCancellationRequested) {
                return {
                    type: 'cancelled',
                    reason: 'during the extra wait time before fetching',
                };
            }
        }
        let isContinuingComment = false;

        if (showLoading) {
            showLoading(isAPICompletionCondition ? () => true : undefined).then(result => {
                isContinuingComment = !!result;
            });
        }
        const tFetchStart = performance.now();
        const completion = await (async () => {
            if (isAPICompletionCondition) {
                const apiCompletion = await enhanceCompletionProvider.getCodeCompletion(
                    document,
                    position,
                    params.value
                );
                if (apiCompletion) {
                    return apiCompletion;
                }
            }
            const result = await fetchInlineEdit(
                document,
                position,
                agumentedContext ? completionContext.selectedCompletionInfo : undefined,
                params.value,
                rewriteHandler,
                cancelToken
            );
            if (result.kind === 'rewrite') {
                rewriteHandler?.showRewrite(document.uri, result.value);
                throw new Error('stop the inline completion process, let rewriteHandler take over');
            }
            captureActualCodeAfterGeneration(
                document.uri,
                result.value.uuid
            );
            return result.value;
        })();
        const tFetchEnd = performance.now();
        const totalTime = tFetchEnd - debounceStartTime;
        if (totalTime > 10000 && !isContinuingComment && !cancelToken.isCancellationRequested) {
            statusBar.setNoSuggestions();
            return {
                type: 'cancelled',
                reason: 'timeout',
            };
        }
        logger.debug('(completion)(time) fetch:', tFetchEnd - tFetchStart);
        const originalText = completion.content;
        const choice = createCompletionChoice(
            document,
            prompt,
            completion,
            originalText,
            isAPICompletionCondition ? CompletionSource.API : CompletionSource.Normal
        );
        choice.isExtensionToSelectedCompletionInfo = Boolean(agumentedContext);
        const cacheKey = keyForPrompt(prompt);
        const choices: CompletionChoice[] = [];
        const cached = completionCache.get(cacheKey);
        cached && choices.push(...cached.choices);
        if (isCommentToCodeMode || isAPICompletionCondition) {
            // 注释生成代码或者API续写时，优先展示最新结果
            choices.unshift(choice);
        }
        else {
            choices.push(choice);
        }
        completionCache.put(cacheKey, {choices});
        return {
            type: 'success',
            value: {resultType: ResultType.Network, data: choices},
        };
    }
    catch (e: any) {
        return {
            type: 'failed',
            reason: e.message ?? 'unknown error on ghost text request',
        };
    }
}

function checkInlineStateAsTyping(prompt: PromptInfo) {
    const {prefix} = prompt;
    const isPrefixMatch = !!inlineState.prefix && prefix.startsWith(inlineState.prefix);
    const isSuffixMatch = inlineState.suffix != null && prompt.suffix === inlineState.suffix;

    if (!(inlineState.prefix && inlineState.cacheKey && isPrefixMatch && isSuffixMatch)) {
        return undefined;
    }

    const cachedCompletion = completionCache.get(inlineState.cacheKey);

    if (!cachedCompletion || !cachedCompletion.choices) {
        return undefined;
    }

    const userInput = prefix.substring(inlineState.prefix.length);

    const result: CompletionChoice[] = [];
    for (const choice of cachedCompletion.choices) {
        const text = choice.completionText.trimEnd();
        if (text.startsWith(userInput)) {
            result.push({
                ...choice,
                completionText: choice.completionText.substring(userInput.length),
            });
        }
    }
    return result;
}

function checkCache(prompt: PromptInfo) {
    const cacheKey = keyForPrompt(prompt);
    const cachedCompletion = completionCache.get(cacheKey);
    if (cachedCompletion && cachedCompletion.choices) {
        const choices = cachedCompletion.choices.filter(item => item.completionText);
        if (choices.length > 0) {
            inlineState.assign(prompt.prefix, prompt.suffix, cacheKey);
        }
        return choices;
    }
    return undefined;
}

function getCompletionFromCache(prompt: PromptInfo): CompletionResult | undefined {
    const typingAsSuggested = checkInlineStateAsTyping(prompt);
    if (typingAsSuggested && typingAsSuggested.length > 0) {
        return {
            resultType: ResultType.TypingAsSuggested,
            data: typingAsSuggested,
        };
    }
    const cachedChoices = checkCache(prompt);
    if (cachedChoices && cachedChoices.length > 0) {
        return {
            resultType: ResultType.Cache,
            data: cachedChoices,
        };
    }

    return undefined;
}

async function getCompletions(
    globalContext: Container,
    document: vscode.TextDocument,
    position: vscode.Position,
    promptContext: PromptContext,
    cancelToken: vscode.CancellationToken,
    completionContext: vscode.InlineCompletionContext,
    completionContextProvider?: CompletionContextProvider,
    docVersionOnCall?: number,
    isCommentToCodeMode?: boolean,
    showLoading?: () => Promise<boolean>,
    rewriteHandler?: RewriteHandler
): Promise<SuccessReturn<CompletionResult> | UnsuccessReturn> {
    const prompt = promptContext.prompt;
    const statusBar = globalContext.get(ComateStatusBar);
    const cacheResult = getCompletionFromCache(prompt);

    const useCacheResult = cacheResult
        && !isCommentToCodeMode
        && completionContext.triggerKind === vscode.InlineCompletionTriggerKind.Automatic
        && !completionContext.selectedCompletionInfo;

    if (useCacheResult) {
        return {
            type: 'success',
            value: cacheResult,
        };
    }

    statusBar.setProgress();

    const networkCompletion = await getNetworkCompletion(
        globalContext,
        document,
        position,
        promptContext,
        cancelToken,
        completionContext,
        completionContextProvider,
        docVersionOnCall,
        isCommentToCodeMode,
        showLoading,
        rewriteHandler
    );

    statusBar.removeProgress();

    const cacheKey = keyForPrompt(prompt);
    if (networkCompletion.type === 'success') {
        inlineState.assign(prompt.prefix, prompt.suffix, cacheKey);
    }

    return networkCompletion;
}

function createCompletion(trailingWs: string, choice: CompletionChoice) {
    // NOTE 这里的 completionText 已经先加上前面的 trailingWs 空格了
    const {completionText} = choice;
    if (trailingWs.length > 0) {
        // 正常情况是下面这种，前面加上 trailingWs 这里又去掉，不影响
        if (completionText.startsWith(trailingWs)) {
            return {
                choice,
                displayText: completionText.slice(trailingWs.length),
                displayNeedsWsOffset: false,
            };
        }

        const whitespaceAtStart = completionText.slice(
            0,
            completionText.length - completionText.trimStart().length
        );
        // 如果 trailingWs 比补全前面的空格多，就通过 displayNeedsWsOffset 计算
        return trailingWs.startsWith(whitespaceAtStart)
            ? {
                choice,
                displayText: completionText.trimStart(),
                displayNeedsWsOffset: true,
            }
            // 如果 \t 和空格混合，有可能会走这个逻辑
            : {
                choice,
                displayText: completionText,
                displayNeedsWsOffset: false,
            };
    }

    return {
        choice,
        displayText: completionText,
        displayNeedsWsOffset: false,
    };
}

function hideCurrentCompletion(prompt: PromptInfo) {
    const key = keyForPrompt(prompt);
    return inlineState.previousHiddenKey && key === inlineState.previousHiddenKey;
}

function getTelemetryBlob(
    globalContext: Container,
    document: vscode.TextDocument,
    position: vscode.Position,
    prompt: PromptContext,
    telemetryData: TelemetryData
) {
    const locationFactory = globalContext.get(LocationFactory);
    const textLine = document.lineAt(position.line);
    const beforeRange = document.getText(locationFactory.range(textLine.range.start, position));
    const afterRange = document.getText(locationFactory.range(position, textLine.range.end));
    const textProperties = {
        languageId: document.languageId,
        beforeCursorWhitespace: JSON.stringify(beforeRange.trim() === ''),
        afterCursorWhitespace: JSON.stringify(afterRange.trim() === ''),
    };

    const promptMeasurements = {
        ...telemetrizePromptLength(prompt.prompt),
        promptEndPos: document.offsetAt(position),
        documentLength: document.getText().length,
        delayMs: 0, // telemetryWithExp.filtersAndExp.exp.variables.copilotlms 目前没有设置，默认为0
    };

    const telemetryBlob = telemetryData.extendedBy(textProperties, promptMeasurements);
    telemetryBlob.measurements.contextualFilterScore = contextualFilterScore(
        globalContext,
        telemetryBlob,
        prompt.prompt
    );
    return telemetryBlob;
}

export async function getGhostText(
    globalContext: Container,
    document: vscode.TextDocument,
    position: vscode.Position,
    cancelToken: vscode.CancellationToken,
    completionContext: vscode.InlineCompletionContext,
    telemetryData: TelemetryData,
    completionContextProvider?: CompletionContextProvider,
    docVersionOnCall?: number,
    treeSitterProvider?: TreeSitterProvider,
    loadingDecorations?: LoadingDecorations,
    lineCommentDecorations?: LineCommentDecorations,
    isCommentToCodeMode?: boolean,
    rewriteHandler?: RewriteHandler
): Promise<GhostTextReturn> {
    const statusBar = globalContext.get(ComateStatusBar);
    const prompt = extractPrompt(document, position);
    if (completionContext.triggerKind === vscode.InlineCompletionTriggerKind.Automatic) {
        // 只有自动触发续写时做策略的限制
        if (prompt.type === 'contextTooShort') {
            return {
                type: 'aborted',
                reason: 'not enough context',
            };
        }
        const isValidLineMiddle = globalContext.get(CompletionPositionChecker).isValidMiddleOfTheLine(
            document,
            position
        );
        if (!isValidLineMiddle) {
            return {
                type: 'aborted',
                reason: 'invalid middle of the line',
            };
        }
        if (document.lineCount >= 8000) {
            return {
                type: 'aborted',
                reason: 'skip long file',
            };
        }
    }

    if (hideCurrentCompletion(prompt.prompt)) {
        return {
            type: 'aborted',
            reason: 'auto hide current completion',
        };
    }

    // 服务下发的变量，全部可变
    // const delayMs = 0;
    // const multiLogitBias = '';
    // const debouncePredict = false;
    // const contextualFilterEnable = true;
    // const contextualFilterEnableTree = true;
    // const computeContextualFilterScore = false;

    const telemetryBlob = getTelemetryBlob(
        globalContext,
        document,
        position,
        prompt,
        telemetryData
    );

    // 如果分数太低直接不显示
    // if (
    //     contextualFilterEnable
    //     && telemetryData.measurements.contextualFilterScore
    //     && telemetryData.measurements.contextualFilterScore < contextualFilterAcceptThreshold / 100
    //     && Math.random() < 1 - contextualFilterExplorationTraffic / 100
    // ) {
    //     console.log('contextualFilterScore below threshold');
    // }

    // 在一些场景下（块级续写、用户快捷键主动触发生成等）通常较慢，或希望用户能感知到插件正在工作
    // 因此，会有个 loading 效果提示用户等一等
    const showLoading = (condition?: () => boolean) => {
        return new Promise<boolean>(resolve => {
            setTimeout(
                () => {
                    if (!cancelToken.isCancellationRequested) {
                        const line = position.line;
                        if (treeSitterProvider) {
                            const tree = treeSitterProvider.getDocumentTree(document);
                            const treeSitterLanguage = checkTreeSitterSupport(document.languageId);
                            if (condition?.()) {
                                loadingDecorations?.show(line, 1000);
                                resolve(true);
                                return;
                            }
                            if (
                                tree
                                && treeSitterLanguage
                                && !cancelToken.isCancellationRequested
                                && isBlockComment(tree.rootNode, line)
                                && cursorAtEmptyLine(tree.rootNode, line, document)
                            ) {
                                loadingDecorations?.show(line);
                                resolve(true);
                                return;
                            }
                        }
                        if (isCommentToCodeMode) {
                            lineCommentDecorations?.hide();
                            loadingDecorations?.show(line);
                            resolve(true);
                            return;
                        }
                    }
                    resolve(false);
                },
                0
            );
        });
    };

    const result = await getCompletions(
        globalContext,
        document,
        position,
        prompt,
        cancelToken,
        completionContext,
        completionContextProvider,
        docVersionOnCall,
        isCommentToCodeMode,
        showLoading,
        rewriteHandler
    );

    if (result.type !== 'success') {
        statusBar.setNoSuggestions();
        return result;
    }
    if (cancelToken.isCancellationRequested) {
        return {
            type: 'cancelled',
            reason: 'cancelled before postProcess',
        };
    }

    const {resultType, data} = result.value;

    const filteredCompletionResult = compact(data.map(
        item => postProcessCompletion(document, position, item)
    ));

    const ghostTextResult = filteredCompletionResult.map(
        item => ({
            ...createCompletion(prompt.prompt.trailingWs, item),
            coverSuffix: checkShouldCoverSuffix(document, position, item),
        })
    );
    if (ghostTextResult.length === 0) {
        statusBar.setNoSuggestions();
    }
    else {
        statusBar.removeProgress();
    }

    return {
        type: 'success',
        value: {
            resultType,
            data: ghostTextResult,
            telemetryBlob: telemetryBlob.extendedBy({}, {}),
        },
    };
}
