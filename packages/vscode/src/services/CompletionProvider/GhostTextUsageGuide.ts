import {compare} from 'compare-versions';
import * as vscode from 'vscode';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {L10n} from '@/common/L10nProvider/L10n';
import {CompletionText} from '@/common/L10nProvider/constants';
import {findNotebook} from '@/common/prompt/extractPrompt';
import {isPoc} from '@/utils/features';
import {VSCodeConfigProvider} from '../ConfigProvider';

const ACCEPT_GUIDE_KEY = 'baidu.comate.didShowGuide';

const ACCEPT_WITH_COMPLETION_WIDGET_GUIDE_KEY = 'baidu.comate.didShownLSPGuide';

const metaKey = process.platform === 'win32' ? 'Ctrl' : '⌘';

const baseStyling: vscode.DecorationRenderOptions = {
    after: {
        color: new vscode.ThemeColor('editorCodeLens.foreground'),
        margin: '0 0 0 2rem',
    },
};

export class GhostTextUsageGuide implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    private lastShownEditor: vscode.TextEditor | undefined;
    private lastShownLine: number | undefined;
    private lastShownFile: string | undefined;
    private lastMultiLineShownFile: string | undefined;
    private decoration: vscode.TextEditorDecorationType | undefined;
    private showGuideDebouncedTimer: NodeJS.Timeout | undefined;

    constructor(protected configProvider: VSCodeConfigProvider) {
        this.disposables.push(
            vscode.window.onDidChangeTextEditorSelection(e => {
                // 光标位置一旦变化即刻隐藏guide
                if (e.textEditor.document.uri.scheme === 'file') {
                    this.hide();
                    return;
                }
                const notebook = findNotebook(e.textEditor.document);
                if (notebook) {
                    this.hide();
                }
            }),
            vscode.window.onDidChangeWindowState(() => {
                // 窗口focus状态变化即刻隐藏guide
                this.hide();
            }),
            vscode.window.onDidChangeActiveTextEditor(() => {
                // 活跃文件变化时重新初始化state并隐藏guide
                this.hide();
                this.lastShownFile = undefined;
                this.lastShownLine = undefined;
                this.lastMultiLineShownFile = undefined;
            })
        );
    }

    // eslint-disable-next-line complexity
    async _show(line: number, isMultiLine: boolean, withCompletionWidget: boolean) {
        const activeFilePath = vscode.window.activeTextEditor?.document.uri.fsPath;
        if (!activeFilePath) {
            this.lastShownFile = undefined;
            this.lastShownLine = undefined;
            this.lastMultiLineShownFile = undefined;
            return;
        }
        // 如果上次是在该文件和行展示了guide，这次不重复展示
        if (line === this.lastShownLine && activeFilePath === this.lastShownFile) {
            return;
        }
        // 如果是多行，并且在当前文件展示过多行的guide，这次不再展示
        if (isMultiLine && activeFilePath === this.lastMultiLineShownFile) {
            return;
        }
        const context = await getExtensionContextAsync();
        const sawAcceptGuide = context.globalState.get<boolean>(
            ACCEPT_GUIDE_KEY
        );
        const sawAcceptWithCompletionWidgetGuide = context.globalState.get<boolean>(
            ACCEPT_WITH_COMPLETION_WIDGET_GUIDE_KEY
        );
        if (!sawAcceptGuide) {
            this.hide();
            this.decoration = this.createDecoration(L10n.t(CompletionText.DECORATION_BASIC));
        }
        else if (!sawAcceptWithCompletionWidgetGuide && withCompletionWidget) {
            this.hide();
            this.decoration = this.createDecoration(L10n.t(CompletionText.DECORATION_WITH_WIDGET));
        }
        else if (isMultiLine && compare(vscode.version, '1.78', '>=') && !isPoc) {
            this.hide();
            this.decoration = this.createDecoration(
                L10n.t(CompletionText.DECORATION_ACCEPT_LINE_WITH_INLINE_CHAT_TEXT, metaKey)
            );
            this.lastMultiLineShownFile = activeFilePath;
        }
        else {
            this.hide();
            return;
        }
        vscode.window.activeTextEditor?.setDecorations(
            this.decoration,
            [new vscode.Range(line, Number.MAX_SAFE_INTEGER, line, Number.MAX_SAFE_INTEGER)]
        );
        this.lastShownLine = line;
        this.lastShownFile = activeFilePath;
        this.lastShownEditor = vscode.window.activeTextEditor;
    }

    async show(line: number, isMultiLine: boolean, withCompletionWidget: boolean) {
        this.showGuideDebouncedTimer = setTimeout(() => {
            this._show(line, isMultiLine, withCompletionWidget);
        }, 500);
    }

    async hide() {
        if (this.showGuideDebouncedTimer) {
            clearTimeout(this.showGuideDebouncedTimer);
            this.showGuideDebouncedTimer = undefined;
        }
        if (this.decoration) {
            this.lastShownEditor?.setDecorations(
                this.decoration,
                []
            );
        }
    }

    async handleAccepted(withCompletionWidget: boolean | undefined) {
        const context = await getExtensionContextAsync();
        const sawAcceptGuide = context.globalState.get<boolean>(ACCEPT_GUIDE_KEY);
        if (sawAcceptGuide) {
            if (withCompletionWidget) {
                context.globalState.update(ACCEPT_WITH_COMPLETION_WIDGET_GUIDE_KEY, true);
            }
        }
        else {
            context.globalState.update(ACCEPT_GUIDE_KEY, true);
        }
    }

    private createDecoration(contentText: string) {
        return vscode.window.createTextEditorDecorationType({
            ...baseStyling,
            after: {
                ...baseStyling.after,
                contentText,
            },
        });
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
    }
}
