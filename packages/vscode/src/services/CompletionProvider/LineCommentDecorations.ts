import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {L10n} from '@/common/L10nProvider/L10n';
import {DecorationsText} from '@/common/L10nProvider/constants';
import {isBlockComment, isLineCommentAndCursorAtEndOfCommentOrNextNewLine} from '@/utils/treeSitterUtils';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {VSCodeConfigProvider} from '../ConfigProvider';
import {LoadingDecorations} from './LoadingDecorations';

const metaKey = process.platform === 'win32' ? 'Alt' : 'Option';

@injectable()
export class LineCommentDecorations implements vscode.Disposable {
    private readonly decoration: vscode.TextEditorDecorationType;
    private disposables: vscode.Disposable[] = [];
    private lastShownEditor: vscode.TextEditor | undefined;

    constructor(
        @inject(LoadingDecorations) private readonly loadingDecorations: LoadingDecorations,
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider
    ) {
        this.disposables.push(
            vscode.window.onDidChangeTextEditorSelection(e => {
                const document = e.textEditor.document;
                if (document.uri.scheme !== 'file') {
                    return;
                }
                // 光标位置一旦变化即刻隐藏快捷键提示
                this.hide();
                const tree = this.treeSitterProvider?.getDocumentTree(document);
                const position = vscode.window.activeTextEditor?.selection.active;
                const line = position?.line ?? -1;
                const character = position?.character ?? -1;
                const enableCommentEnhancement = this.configProvider?.getEnableCommentEnhancement();
                if (
                    tree
                    && isLineCommentAndCursorAtEndOfCommentOrNextNewLine(tree.rootNode, line, character, document)
                    && !isBlockComment(tree.rootNode, line)
                    && enableCommentEnhancement
                ) {
                    // 先确保其他情况的提示已被清除
                    this.loadingDecorations?.hide();
                    this.show(line);
                }
            }),
            vscode.window.onDidChangeWindowState(() => {
                // 窗口focus状态变化即刻隐藏快捷键提示
                this.hide();
            })
        );

        this.decoration = vscode.window.createTextEditorDecorationType({
            after: {
                contentText: L10n.t(DecorationsText.LINE_COMMENT_CODE_TRIGGER_TITLE, metaKey),
                color: new vscode.ThemeColor('editorCodeLens.foreground'),
                margin: '0 0 0 1.5rem',
                textDecoration: ';font-size: 10px;',
            },
        });
    }

    async hide() {
        if (this.decoration) {
            this.lastShownEditor?.setDecorations(
                this.decoration,
                []
            );
        }
    }

    show(line: number) {
        this.hide();

        const range = new vscode.Range(
            line,
            Number.MAX_SAFE_INTEGER,
            line,
            Number.MAX_SAFE_INTEGER
        );
        vscode.window.activeTextEditor?.setDecorations(this.decoration, [range]);
        this.lastShownEditor = vscode.window.activeTextEditor;
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
