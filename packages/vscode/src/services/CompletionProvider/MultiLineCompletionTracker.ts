import {acceptCode} from '../../api';
import {LRUCache} from '../../utils/cache';

interface AcceptanceRecord {
    fullText: string;
    numOfLines: number;
}

export class MultiLineCompletionTracker {
    private _uuid: string | undefined;
    private readonly cache = new LRUCache<AcceptanceRecord>(100);

    record(uuid: string, completionText: string) {
        this._uuid = uuid;
        // 只处理多行的情况
        if (!completionText.includes('\n')) {
            return;
        }
        if (!this.cache.has(uuid)) {
            this.cache.put(uuid, {
                fullText: completionText,
                numOfLines: 0,
            });
        }
    }

    acceptNextLine() {
        if (!this._uuid) {
            return;
        }
        const record = this.cache.get(this._uuid);
        if (!record) {
            return;
        }
        record.numOfLines++;
        this.report();
    }

    private report() {
        if (this._uuid) {
            const record = this.cache.get(this._uuid);
            if (record && record.numOfLines > 0) {
                const acceptedText = record
                    .fullText
                    .split('\n')
                    .slice(0, record.numOfLines)
                    .join('\n');
                acceptCode({
                    uuid: this._uuid,
                    accepted: true,
                    content: '',
                    generatedContent: acceptedText,
                });
            }
        }
    }
}
