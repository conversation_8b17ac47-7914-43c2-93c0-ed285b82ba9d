import path, {extname} from 'node:path';
import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import Parser, {type SyntaxNode} from 'web-tree-sitter';
import {
    checkTreeSitterSupport,
    extractFunctionNodes,
    findMatchedNodes,
    findMatchedParent,
    functionStatementPatterns,
    getImportStatements,
    queryPatternMatches,
    TreeSitterLanguage,
} from '@/utils/treeSitterUtils';
import {RipgrepProvider} from '@/common/RipgrepProvider';
import {
    codeCompletionV2,
    getCodeRegExp,
    memorizedGetApiNameKeyword,
    RepoCodeCompletionType,
    type RepoFindCodeRegular,
    sendEvent,
    startGenerateRegular,
} from '@/api/apiCodeCompletion';
import {type GenerateCodeOptions, generateTrackUuid, memoizedGetUserConfig, type RawGenerateCode} from '@/api';
import {TYPES} from '@/inversify.config';
import {timeout} from '@/utils/promise';
import {getRepoNameMemoized} from '@/utils/git';
import {isInternal} from '@/utils/features';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {ILocationLinkResolver} from '../CrossFileContextAnalyzer/types';
import {SymbolSignatureController} from '../CrossFileContextAnalyzer/SymbolSignatureReader';
import {UserService} from '../UserService';

function isRequestFunctionName(functionName: string) {
    return functionName === 'fetch'
        || functionName.startsWith('axios')
        || functionName.startsWith('request')
        || functionName.startsWith('superagent')
        || functionName.startsWith('XMLHttpRequest')
        || functionName.startsWith('$.ajax');
}

/**
 * call express 名称限定
 *
 * @example
 * function apiXXX() {
 *   // ...
 *    ${functionName}();
 *    ^^^^^^^^^^^^
 * }
 */
function isAliasRequestFunctionCallNode(node: Parser.SyntaxNode, aliasPatterns?: RegExp[]) {
    const functionNode = node.childForFieldName('function');
    if (!functionNode) {
        return false;
    }
    const functionName = functionNode.type === 'await_expression'
        ? functionNode.namedChild(0)?.text
        : functionNode.text;

    if (!functionName) {
        return false;
    }

    return isRequestFunctionName(functionName)
        || (aliasPatterns && aliasPatterns.some(pattern => pattern.test(node.text)));
}

function getFunctionDeclartionNameNode(node: Parser.SyntaxNode) {
    let target: Parser.SyntaxNode | null = node;
    /*
     * export const name
     * ^^^^^^
     */
    if (node.type === 'export_statement') {
        target = node.firstNamedChild;
    }
    /*
     * const name
     *       ^^^^
     */
    if (target?.type === 'lexical_declaration') {
        target = target.firstNamedChild;
    }

    if (target?.type === 'function_declaration') {
        return target.firstNamedChild;
    }
    else if (
        target?.firstNamedChild?.type === 'variable_declarator'
        && target.firstNamedChild.childForFieldName('value')?.type === 'arrow_function'
    ) {
        return target.firstNamedChild.childForFieldName('name');
    }
    return target?.childForFieldName('name') ?? node;
}

// 找到同文件夹中的文件
function getCurrentFolder(uri: vscode.Uri) {
    const filePath = uri.fsPath;
    return path.dirname(filePath);
}

function isRelativeApiString(str: string, keywords: string[]) {
    return keywords.some(keyword => str.includes(keyword));
}

// 文件中是否有 import api 语句
async function hasApiImportText(root: Parser.SyntaxNode) {
    const importNodes = getImportStatements(root);
    const apiFileKeywords = await memorizedGetApiNameKeyword();
    return importNodes.some(node => {
        // const sourceNode = node.childForFieldName('source');
        // if (!sourceNode) {
        //     return false;
        // }
        // const sourceName = sourceNode.text.slice(1, -1).toLocaleLowerCase();
        // if (isRelativeApiString(sourceName)) {
        //     return true;
        // }

        const importClauseNode = (() => {
            if (node.firstNamedChild?.type === 'import_clause') {
                return node.firstNamedChild;
            }
            return node;
        })();
        const importClauseNodeName = importClauseNode?.text.toLocaleLowerCase();
        if (
            importClauseNodeName
            && isRelativeApiString(importClauseNodeName, apiFileKeywords ?? ['axios', 'api', 'fetch', 'request'])
        ) {
            return true;
        }
        return false;
    });
}

async function getRepo() {
    const firstWorkspace = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    const repoNamePromise = firstWorkspace ? getRepoNameMemoized(firstWorkspace) : '';
    try {
        return await repoNamePromise;
    }
    catch {
        return '';
    }
}

function isAvailablePattern(node: Parser.SyntaxNode, patterns: RepoFindCodeRegular[]): boolean {
    return patterns.some(cur => {
        if (
            // 完整文本匹配
            new RegExp(cur.styleRegular).test(node.text)
            || new RegExp(cur.requestRegular).test(node.text)
        ) {
            return true;
        }
        return false;
    });
}

function getAvailablePattern(tree: Parser.Tree | undefined, patterns: RepoFindCodeRegular[]): RepoFindCodeRegular[] {
    const pattern: RepoFindCodeRegular[] = [];
    if (tree) {
        const fileFunctionExpressionNodes = extractFunctionNodes(TreeSitterLanguage.TypeScript, tree.rootNode);
        fileFunctionExpressionNodes.some((child: SyntaxNode) => {
            return patterns.some(cur => {
                if (isAvailablePattern(child, [cur])) {
                    pattern.push(cur);
                    return true;
                }
                return false;
            });
        });
    }
    return pattern;
}

type UnknownFunction = (value: unknown) => void;

function isBooleanProcessing(delay: number) {
    let lastExec = 0;
    let timer: NodeJS.Timeout | null = null;
    let promsieResolve: UnknownFunction | null = null;

    return () => {
        const now = Date.now();
        if (now - lastExec >= delay) {
            lastExec = now;

            return Promise.resolve(false);
        }

        promsieResolve?.(true);

        timer !== null && clearTimeout(timer);

        // gc
        promsieResolve = null;

        return new Promise(resolve => {
            promsieResolve = resolve;
            timer = setTimeout(() => {
                resolve(false);
            }, delay);
        });
    };
}

const isProcessing = isBooleanProcessing(1000);

@injectable()
export class EnhanceCompletionProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    static readonly supportedLanguages = [
        'typescript',
        /* javascript, 因为需要 interface 所以 javascript 暂时不处理 */
    ];

    // 上次提交的代码内容
    private previousSubmitLine: string | null = null;
    private condition: RepoFindCodeRegular[] | null = null;
    // 生成正则的次数
    private generateRuleCount = 0;

    constructor(
        @inject(UserService) private readonly userService: UserService,
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(TYPES.ILocationLinkResolver) private readonly locationLinkResolver: ILocationLinkResolver,
        @inject(SymbolSignatureController) private readonly symbolSignatureController: SymbolSignatureController,
        @inject(RipgrepProvider) private readonly ripgrepProvider: RipgrepProvider
    ) {
        this.disposables.push(
            vscode.window.onDidChangeActiveTextEditor((editor?: vscode.TextEditor) => {
                const isRegistered = editor && isInternal && this.isRequestFile(editor.document);
                isRegistered && this.registerCompletionCondition(editor.document);
            })
        );
    }

    async registerCompletionCondition(document: vscode.TextDocument) {
        // 对文件做限制
        // if (!isPathContainsApiKeyword(document.uri)) {
        //     return;
        // }
        if (!EnhanceCompletionProvider.supportedLanguages.includes(document.languageId) || this.generateRuleCount > 5) {
            return;
        }

        const tree = this.treeSitterProvider.getDocumentTree(document);
        if (!(await this.isRequestFile(document))) {
            return;
        }
        const repo = await getRepo();
        try {
            const patterns = await getCodeRegExp({
                repo,
                parseType: RepoCodeCompletionType.Request,
            });
            const availablePatterns = getAvailablePattern(tree, patterns);
            if (!availablePatterns.length) {
                const generatePattern = await startGenerateRegular({
                    repo,
                    content: document.getText(),
                    path: document.uri.fsPath,
                    username: (await this.userService.getCurrentUser())[0],
                });
                // 增加一个锁，防止频繁生成
                this.generateRuleCount += 1;
                generatePattern && getAvailablePattern(tree, [generatePattern]).length
                    && availablePatterns.push(generatePattern);
            }

            // 单例，当次运行均只使用这一个条件
            this.condition = availablePatterns;
        }
        catch (e: any) {
            console.error('get api code regexp error', e.message);
        }
    }

    isInRequestContext(document: vscode.TextDocument, position: vscode.Position) {
        if (!this.condition || !this.condition.length) {
            return false;
        }
        if (!EnhanceCompletionProvider.supportedLanguages.includes(document.languageId)) {
            return false;
        }
        const treeSitterLanguage = checkTreeSitterSupport(document.languageId);
        if (!treeSitterLanguage) {
            return false;
        }
        const tree = this.treeSitterProvider.getDocumentTree(document);
        if (!tree) {
            return false;
        }
        const closestNode = tree.rootNode.descendantForPosition({
            row: position.line,
            column: position.character,
        });
        // const closestFunctionDeclarationNode = this.getCurrentPositionFunctionNode(tree, position);

        // 这个来自于早期需求，期望在整个函数体内补全
        // 通用规则会让生成的位置不准确，所以需要限定在函数内
        // if (
        //     // !isPathContainsApiKeyword(document.uri)
        //     closestFunctionDeclarationNode && isAvailablePattern(closestFunctionDeclarationNode, this.condition)
        // ) {
        //     return true;
        // }

        /**
         * 限定在 method, url, params, body 等位置的续写
         *
         * @example
         * function apiXXX() {
         *    return request(
         *       '|<<< 这里
         *    );
         * }
         */
        const closestFunctionCallNode = findMatchedParent(
            closestNode,
            node => node.type === 'call_expression'
        );

        // 光标位置在函数参数位置
        function isArgumentContainsPosition(
            document: vscode.TextDocument,
            position: vscode.Position,
            node: Parser.SyntaxNode
        ) {
            const argumentNode = node.childForFieldName('arguments');
            if (!argumentNode) {
                return false;
            }
            const currentIndex = document.offsetAt(position);
            return argumentNode.startIndex <= currentIndex && argumentNode.endIndex >= currentIndex;
        }

        const isRequestContext = closestFunctionCallNode
            && isAliasRequestFunctionCallNode(
                closestFunctionCallNode,
                this.condition.map(cur => new RegExp(cur.requestRegular))
            )
            && isArgumentContainsPosition(document, position, closestFunctionCallNode);

        return !!isRequestContext;
    }

    async getAccessTo(params: {username: string}) {
        const response = await memoizedGetUserConfig(params.username);
        return response?.enableGenAPIInvokeCompletion ?? false;
    }

    // 是否是合理的提交
    async isRealSubmit(document: vscode.TextDocument, position: vscode.Position) {
        // 增加个节流
        if (await isProcessing()) {
            return false;
        }

        const previousSubmitLine = this.previousSubmitLine;
        const lineTextOnCursor = document.getText(
            new vscode.Range(
                position.line,
                0,
                position.line,
                position.character
            )
        );
        // 如果是删除字符，则不提交 =>
        if (lineTextOnCursor === previousSubmitLine?.substring(0, previousSubmitLine.length - 1)) {
            return false;
        }
        return true;
    }

    async getCodeCompletion(
        document: vscode.TextDocument,
        position: vscode.Position,
        params: GenerateCodeOptions
    ): Promise<RawGenerateCode | undefined> {
        try {
            const snippets = await this.getCodeSnippet(document);
            const paramsTypeDefinition = await this.getParamsTypeDefinition(document, position);

            const functionNamedNode = this.getClosestFunctionDeclarationNode(document, position);
            if (!functionNamedNode) {
                return;
            }

            const functionName = getFunctionDeclartionNameNode(functionNamedNode)?.text;
            const closestFunctionTextOnCursor = document.getText(
                new vscode.Range(
                    functionNamedNode
                        .startPosition
                        .row,
                    functionNamedNode.startPosition.column,
                    position.line,
                    position.character
                )
            );
            // 缓存本行
            this.previousSubmitLine = document.getText(
                new vscode.Range(
                    position.line,
                    0,
                    position.line,
                    position.character
                )
            );
            const {code, generateId} = await timeout(
                new Promise<Record<string, string>>(async (resolve, reject) => {
                    const {content: code, generateId} = await codeCompletionV2({
                        sampleCode: snippets,
                        dataModel: paramsTypeDefinition ? [paramsTypeDefinition] : [],
                        content: functionNamedNode.text,
                        cursorIndexFunction: closestFunctionTextOnCursor.length - 1,
                        username: params.username,
                        repoName: params.repo,
                        queryList: functionName ?? '',
                    });
                    if (!generateId) {
                        return reject(Error('Generate ID failed'));
                    }

                    // TODO: 是否有竟态可能？输入过程中，触发了原续写，同时又返回了结果
                    resolve({code, generateId});
                }),
                // 3s 超时
                3000
            );

            const uuid = await (async () => {
                try {
                    return (await generateTrackUuid({
                        username: params.username,
                        content: code || 'UNDEFINED',
                        generatedContent: code || 'UNDEFINED',
                        model: 'GenAPI',
                        function: 'API_CALL_CODE_COMPLETION',
                        path: params.path,
                        col: '1',
                        row: '1',
                        ide: params.ide,
                        repo: params.repo,
                        shown: true,
                        multiline: true,
                        pluginVersion: params.pluginVersion,
                    }))
                        .data
                        .data
                        ?.uuid ?? '';
                }
                catch {
                    return '';
                }
            })();

            // 关联埋点
            this.trackEvent({uuid, generateId});

            if (!code) {
                throw Error('Enhance code completion failed');
            }

            const startRow = position.line;
            const startCol = position.character;
            const codeSplitArray = code?.split('\n');
            // mock RawGenerateCode
            return {
                uuid,
                content: code,
                range: [
                    startRow,
                    startCol,
                    startRow + codeSplitArray.length - 1,
                    Number(startCol + codeSplitArray[codeSplitArray.length - 1].length),
                ] as [number, number, number, number],
                score: 0,
            };
        }
        catch (e: any) {
            console.error('Enhance code completion error happen: ', e.message);
            return undefined;
        }
    }

    trackEvent(params: {uuid: string, isAdopt?: boolean, generateId?: string}) {
        sendEvent(params);
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }

    private async isRequestFile(document: vscode.TextDocument) {
        const tree = this.treeSitterProvider.getDocumentTree(document);
        return !!(tree && await hasApiImportText(tree.rootNode));
    }

    private async getParamsTypeDefinition(document: vscode.TextDocument, position: vscode.Position) {
        const closestFunctionDeclarationNode = this.getClosestFunctionDeclarationNode(document, position);
        if (!closestFunctionDeclarationNode) {
            return;
        }
        const currentFilePath = document.uri.fsPath;
        /**
         * type annotation 类型注解
         * interface_declaration 接口声明
         * object_type 对象类型
         */
        let typeAnnotationNode = findMatchedNodes(
            closestFunctionDeclarationNode,
            node => node.type === 'type_annotation'
        )[0];
        if (!typeAnnotationNode) {
            return;
        }
        if (typeAnnotationNode.firstNamedChild?.type === 'objec_type') {
            return typeAnnotationNode.text;
        }
        if (typeAnnotationNode.firstNamedChild?.type === 'type_identifier') {
            typeAnnotationNode = typeAnnotationNode.firstNamedChild!;
        }
        const typeIdentifierDefLocation = await this.locationLinkResolver.resolveTypeDefinition(
            currentFilePath,
            {
                start: {
                    line: typeAnnotationNode.startPosition.row,
                    character: typeAnnotationNode.startPosition.column,
                },
                end: {
                    line: typeAnnotationNode.startPosition.row,
                    character: typeAnnotationNode.startPosition.column + typeAnnotationNode.text.length,
                },
            }
        );
        if (!typeIdentifierDefLocation) {
            return;
        }
        const typeSiganture = await this.symbolSignatureController.getSignature(
            typeIdentifierDefLocation.uri,
            typeIdentifierDefLocation.range
        );
        return typeSiganture?.content;
    }

    private async getCodeSnippet(document: vscode.TextDocument) {
        if (this.condition?.length && this.condition[0].sampleMethod) {
            return [this.condition[0].sampleMethod];
        }
        const tree = this.treeSitterProvider.getDocumentTree(document);
        const currentFileSnippets = this.getDocumentRequestSnippets(tree);
        if (currentFileSnippets.length > 0) {
            return currentFileSnippets;
        }

        // 这里可能会有点 hardcode，需要优化
        const currentFolder = getCurrentFolder(document.uri);
        if (!currentFolder) {
            return [];
        }

        const apiFiles: vscode.Uri[] = [];
        const currentExtname = extname(document.uri.fsPath);

        const apiSnippetRegexp = this.condition?.[0].styleRegular;
        if (!apiSnippetRegexp) {
            return [];
        }

        await this.ripgrepProvider.searchText(
            {pattern: apiSnippetRegexp, isCaseSensitive: true, isRegExp: true},
            {folderUri: vscode.Uri.file(currentFolder)},
            {
                onResult: result => {
                    if (result.uri.fsPath === document.uri.fsPath || currentExtname !== extname(result.uri.fsPath)) {
                        return false;
                    }
                    apiFiles.push(result.uri);
                    return !!apiFiles.length;
                },
            }
        );
        const relativeApiSnippets = await this.getRequestSnippetsFromFile(apiFiles);
        if (relativeApiSnippets.length > 0) {
            return relativeApiSnippets.filter(code => code.split('\n').length < 40);
        }
        return [];
    }

    private getDocumentRequestSnippets(tree: Parser.Tree | undefined) {
        const snippets: string[] = [];
        if (tree && this.condition?.length) {
            const fileFunctionExpressionNodes = extractFunctionNodes(TreeSitterLanguage.TypeScript, tree.rootNode);
            fileFunctionExpressionNodes.some((child: SyntaxNode) => {
                if (isAvailablePattern(child, this.condition!)) {
                    snippets.push(child.text);
                    return true;
                }
                return false;
            });
        }
        return snippets;
    }

    private getCurrentPositionFunctionNode(tree: Parser.Tree, position: vscode.Position) {
        const closestNode = tree.rootNode.descendantForPosition({
            row: position.line,
            column: position.character,
        });
        return findMatchedParent(closestNode, node => {
            return !!queryPatternMatches(node, functionStatementPatterns['typescript']).length;
        });
    }

    private async getRequestSnippetsFromFile(apiFiles: vscode.Uri[]) {
        // tree-sitter 解析是同步的，这里先不异步处理了
        for (const uri of apiFiles) {
            const document = await vscode.workspace.openTextDocument(uri);
            const tree = this.treeSitterProvider.getDocumentTree(document);
            const snippets = this.getDocumentRequestSnippets(tree);
            if (snippets.length > 0) {
                return snippets;
            }
        }
        return [];
    }

    private getClosestFunctionDeclarationNode(document: vscode.TextDocument, position: vscode.Position) {
        const treeSitterLanguage = checkTreeSitterSupport(document.languageId);
        const tree = this.treeSitterProvider.getDocumentTree(document);
        if (tree && treeSitterLanguage) {
            return this.getCurrentPositionFunctionNode(tree, position) ?? undefined;
        }

        return undefined;
    }
}
