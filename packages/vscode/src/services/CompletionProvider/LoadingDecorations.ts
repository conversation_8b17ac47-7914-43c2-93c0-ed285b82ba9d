import * as vscode from 'vscode';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {DecorationsText} from '@/common/L10nProvider/constants';
import {L10n} from '@/common/L10nProvider/L10n';

export class LoadingDecorations implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private decoration: vscode.TextEditorDecorationType | undefined;
    private lastShownEditor: vscode.TextEditor | undefined;
    private debouncedShowHandler: NodeJS.Timeout | undefined;

    constructor() {
        this.disposables.push(
            vscode.window.onDidChangeTextEditorSelection(e => {
                if (e.textEditor.document.uri.scheme !== 'file') {
                    return;
                }
                // 光标位置一旦变化即刻隐藏 loading 提示
                this.hide();
            }),
            vscode.window.onDidChangeWindowState(() => {
                // 窗口focus状态变化即刻隐藏 loading 提示
                this.hide();
            })
        );
    }

    createDecoration(context: any, text?: string): vscode.TextEditorDecorationType {
        const iconPath = vscode.Uri.joinPath(
            context.extensionUri,
            'assets',
            'loading.gif'
        );
        return vscode.window.createTextEditorDecorationType({
            before: {
                contentIconPath: iconPath,
                width: '20px',
                textDecoration: 'none; display: inline-block; transform: translateY(2px); text-align: center;',
            },
            after: {
                contentText: text,
                color: new vscode.ThemeColor('editorCodeLens.foreground'),
            },
        });
    }

    async hide() {
        clearTimeout(this.debouncedShowHandler);
        if (this.decoration) {
            this.lastShownEditor?.setDecorations(
                this.decoration,
                []
            );
        }
    }

    async show(line: number, delay = 0) {
        const addDecoration = async () => {
            const context = await getExtensionContextAsync();
            this.hide();
            const text = L10n.t(DecorationsText.GENERATING_TITLE);
            this.decoration = this.createDecoration(context, text);
            vscode.window.activeTextEditor?.setDecorations(
                this.decoration,
                [new vscode.Range(line, Number.MAX_SAFE_INTEGER, line, Number.MAX_SAFE_INTEGER)]
            );
            this.lastShownEditor = vscode.window.activeTextEditor;
        };
        if (delay > 0) {
            this.debouncedShowHandler = setTimeout(addDecoration, delay);
        }
        else {
            addDecoration();
        }
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
