/* eslint-disable max-depth */
import * as vscode from 'vscode';
import {checkSubsequence} from '@/utils/common';
import {getBlockCloseToken} from '@/utils/treeSitterUtils';
import {CompletionChoice} from '../types';

// eslint-disable-next-line complexity
function stripAfterBlockCloseToken(document: vscode.TextDocument, position: vscode.Position, completionText: string) {
    if (completionText === '') {
        return completionText;
    }
    let blockCloseToken = '}';
    try {
        blockCloseToken = getBlockCloseToken(document.languageId) ?? '}';
    }
    catch (e) {
        // ignore
    }
    let length = completionText.length;
    do {
        const lastLineBreakIndex = completionText.lastIndexOf('\n', length - 2) + 1;
        // 最后一行独立为`}`一个字符
        const lastLineHasBlockClose = completionText.substring(lastLineBreakIndex, length);
        if (lastLineHasBlockClose.trim() === blockCloseToken) {
            for (let current = position.line; current < document.lineCount; current++) {
                let lineText = document.lineAt(current).text;
                if (current === position.line) {
                    lineText = lineText.slice(position.character);
                }
                if (lineText.startsWith(lastLineHasBlockClose.trimEnd())) {
                    // 这里减掉的一个字符大概率是 \n
                    // lastLineBreakIndex - 1 < 0 指的是第一行只有一个 }
                    // 而且能与用户的后缀匹配上，这种情况直接丢弃吧
                    return completionText.substring(0, Math.max(0, lastLineBreakIndex - 1));
                }
                // 如果是多行匹配，又在行中的话，跳过所在行的后缀
                if (current !== position.line && lineText.trim() !== '') {
                    break;
                }
            }
        }
        if (length === lastLineBreakIndex) {
            return '';
        }
        length = lastLineBreakIndex;
    }
    while (length > 1);
    return completionText;
}

export function postProcessCompletion(
    document: vscode.TextDocument,
    position: vscode.Position,
    completionChoice: CompletionChoice
): CompletionChoice | undefined {
    // typing 的时候会裁掉前面用户输入的字符，裁掉后可能会为空
    if (!completionChoice.completionText) {
        return undefined;
    }

    const clonedCompletionChoice: CompletionChoice = {...completionChoice};

    clonedCompletionChoice.completionText = stripAfterBlockCloseToken(
        document,
        position,
        clonedCompletionChoice.completionText
    );
    return clonedCompletionChoice.completionText ? clonedCompletionChoice : undefined;
}

// eslint-disable-next-line complexity
function checkParentheseBalanced(text: string) {
    const stack = [];
    for (let i = 0; i < text.length; i++) {
        const c = text.charAt(i);
        if (c === '(' || c === '[') {
            stack.push(c);
        }
        else if (c === ')' || c === ']') {
            if (stack.length === 0) {
                return false;
            }
            const last = stack[stack.length - 1];
            if ((c === ')' && last === '(') || (c === ']' && last === '[')) {
                stack.pop();
            }
        }
    }
    return stack.length === 0;
}

/**
 * 判断补全代码是否包含了光标所在行的后缀内容
 *
 * @param document 当前打开的文档对象
 * @param position 光标所在位置
 * @param choice 补全选项
 * @returns 如果补全代码包含了光标所在行的后缀内容则返回true，否则返回false
 */
function checkSuffixInclusion(document: vscode.TextDocument, position: vscode.Position, choice: CompletionChoice) {
    const lineText = document.lineAt(position).text;
    const cursorLineSuffix = lineText.slice(position.character);
    if (cursorLineSuffix.length > 0) {
        // 补全的代码包含了光标位置后面的内容，
        // 比如当前行后面是`) {`，补全的是`checkCurrentState(state) === State.OK) {`
        if (choice.completionText.includes(cursorLineSuffix)) {
            return true;
        }

        // 判断补全内容是否包含当前光标所在行的后缀
        return checkSubsequence(choice.completionText, cursorLineSuffix);
    }
    return false;
}

/**
 * 检查是否应该用续写覆盖光标行后缀的内容
 *
 * @param document 当前文档
 * @param position 当前光标位置
 * @param choice 补全选项
 * @returns 如果应该覆盖后缀，则返回 true；否则返回 false
 */
export function checkShouldCoverSuffix(
    document: vscode.TextDocument,
    position: vscode.Position,
    choice: CompletionChoice
) {
    const suffixIncluded = checkSuffixInclusion(document, position, choice);
    if (!suffixIncluded) {
        // 当续写无法完全包含光标行的后缀时，则不覆盖
        return false;
    }
    const oldLineText = document.lineAt(position).text;
    const oldParensBalanced = checkParentheseBalanced(oldLineText);
    if (oldParensBalanced) {
        const newText = oldLineText.slice(0, position.character) + choice.completionText;
        const newParensBalanced = checkParentheseBalanced(newText.split(/\r?\n/)[0]);
        if (!newParensBalanced) {
            // 原文括号平衡，用续写覆盖后缀后括号不平衡，则不覆盖
            return false;
        }
    }
    return true;
}
