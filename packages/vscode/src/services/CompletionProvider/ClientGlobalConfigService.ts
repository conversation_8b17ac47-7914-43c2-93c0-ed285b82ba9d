import EventEmitter from 'node:events';
import {injectable} from 'inversify';
import {cloneDeep} from 'lodash';
import stableStringify from 'json-stable-stringify';
import {Disposable} from 'vscode-languageserver-protocol';
import {getClientGlobalConfig, ClientGlobalConfig} from '@/api';

@injectable()
export class ClientGlobalConfigService implements Disposable {
    private readonly disposables: Disposable[] = [];
    private _config: ClientGlobalConfig | null = null;
    private readonly _onDidChange = new EventEmitter();
    static changeEventName = 'change';

    constructor() {
        this.refresh();
    }

    get config() {
        return cloneDeep(this._config);
    }

    onDidChange(callback: () => void): Disposable {
        this._onDidChange.on(ClientGlobalConfigService.changeEventName, callback);
        return {
            dispose: () => {
                this._onDidChange.removeListener(ClientGlobalConfigService.changeEventName, callback);
            },
        };
    }

    private async refresh() {
        const result = await getClientGlobalConfig();
        if (result.status === 'OK' && result.data) {
            if (stableStringify(this._config) !== stableStringify(result.data)) {
                this._config = result.data;
                this._onDidChange.emit(ClientGlobalConfigService.changeEventName);
            }
        }
        const timeoutId = setTimeout(
            () => {
                this.refresh();
            },
            1000 * 60 * 60 // 每小时更新一次
        );
        this.disposables.push({
            dispose: () => {
                clearTimeout(timeoutId);
            },
        });
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables.length = 0;
    }
}
