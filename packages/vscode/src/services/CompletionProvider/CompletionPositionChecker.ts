import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import {ClientGlobalConfigService} from './ClientGlobalConfigService';

@injectable()
export default class CompletionPositionChecker implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];

    languageToRegexMap: Map<string, {
        prefixRegexStr: string;
        suffixRegexStr: string;
        /**
         * 两位二进制数，分别代表前缀和后缀的模式
         * 00 不匹配
         * 01 后缀匹配
         * 10 前缀匹配
         * 11 前后缀都匹配
         */
        mode: number;
    }> = new Map();
    defaultPrefixRegexStr: string;
    defaultSuffixRegexStr: string;
    constructor(@inject(ClientGlobalConfigService) private readonly configService: ClientGlobalConfigService) {
        this.defaultPrefixRegexStr = '.*';
        this.defaultSuffixRegexStr = '^\\s*[)}\\]"\'`]*\\s*[:{;,]?\\s*$';
        this.update();
        this.disposables.push(
            this.configService.onDidChange(this.update.bind(this))
        );
    }

    async update() {
        if (this.configService.config) {
            this.configService.config?.inlineCodeCompleteConfig?.forEach(item => {
                this.languageToRegexMap.set(item.languageId, {
                    prefixRegexStr: item.prefixRegexStr,
                    suffixRegexStr: item.suffixRegexStr,
                    mode: item.mode,
                });
            });
        }
    }

    /**
     * 判断光标所在位置是否处于有效的续写触发位置
     * e.g.
     * 有效位置: ab|;
     * 无效位置: a|b;
     * @param doc vscode.TextDocument 文档对象
     * @param cursor vscode.Position 光标位置
     * @param regexStr 正则表达式，默认为 /^\s*[)}\]"'`]*\s*[:{;,]?\s*$/
     * @returns boolean 返回布尔值，表示光标是否位于行尾有效范围内
     */
    isValidMiddleOfTheLine(doc: vscode.TextDocument, cursor: vscode.Position) {
        const prefix = doc.lineAt(cursor).text.slice(0, cursor.character);
        const post = doc.lineAt(cursor).text.slice(cursor.character);
        const config = this.languageToRegexMap.get(doc.languageId);
        if (config) {
            // 末尾一定触发
            if (post === '') {
                return true;
            }
            if (config.mode === 3) {
                return (RegExp(config.prefixRegexStr).test(prefix)) && (RegExp(config.suffixRegexStr).test(post));
            }
            else if (config.mode === 2) {
                return RegExp(config.prefixRegexStr).test(prefix);
            }
            else if (config.mode === 1) {
                return RegExp(config.suffixRegexStr).test(post);
            }
            else {
                return false;
            }
        }
        else {
            return RegExp(this.defaultSuffixRegexStr).test(doc.lineAt(cursor).text.slice(cursor.character).trim());
        }
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables.length = 0;
    }
}
