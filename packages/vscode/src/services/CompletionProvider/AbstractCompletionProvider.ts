import * as vscode from 'vscode';
import {Container} from 'inversify';
import {compare} from 'compare-versions';
import {maxBy} from 'lodash';
import {isInternal} from '@/utils/features';
import {trace} from '@/common/outputChannel';
import {InlineCompletionItem} from '../types';
import {CompletionContextProvider} from '../CompletionContextProvider';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {MultiLineCompletionTracker} from './MultiLineCompletionTracker';
import {GhostTextUsageGuide} from './GhostTextUsageGuide';
import {
    handleGhostTextPostInsert,
    handleCompletionHide,
    getInlineCompletions,
    handleGhostTextResult,
    handleGhostTextShown,
} from './handlers';
import {LoadingDecorations} from './LoadingDecorations';
import {LineCommentDecorations} from './LineCommentDecorations';
import {RewriteHandler} from './types';

export interface InterceptorMatch {
    score: number;
    execute: () => void;
}

export interface InlineCompletionInterceptor {
    match(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.InlineCompletionContext
    ): InterceptorMatch | Promise<InterceptorMatch>;
}

export abstract class AbstractCompletionProvider implements vscode.InlineCompletionItemProvider, vscode.Disposable {
    protected disposables: vscode.Disposable[] = [];
    protected isCommentToCodeMode = false;
    protected _onDidProvideInlineCompletionItems = new vscode.EventEmitter<InlineCompletionItem[]>();
    readonly onDidProvideInlineCompletionItems = this._onDidProvideInlineCompletionItems.event;
    protected _onDidAcceptInlineCompletionItem = new vscode.EventEmitter<InlineCompletionItem>();
    readonly onDidAcceptInlineCompletionItem = this._onDidAcceptInlineCompletionItem.event;
    protected interceptors = new Map<string, InlineCompletionInterceptor>();
    private isFrozen = false;
    private freezeTimeout: NodeJS.Timeout | null = null;

    constructor(
        protected ctx: Container,
        protected ghostTextUsageGuide?: GhostTextUsageGuide,
        protected multiLineCompletionTracker?: MultiLineCompletionTracker,
        protected completionContextProvider?: CompletionContextProvider,
        protected treeSitterProvider?: TreeSitterProvider,
        protected loadingDecorations?: LoadingDecorations,
        protected lineCommentDecorations?: LineCommentDecorations,
        protected readonly configProvider?: VSCodeConfigProvider,
        protected readonly rewriteHandler?: RewriteHandler
    ) {
        if (this.ghostTextUsageGuide) {
            this.disposables.push(this.ghostTextUsageGuide);
        }
        this.disposables.push(
            vscode.languages.registerInlineCompletionItemProvider(
                [
                    {scheme: 'file'},
                    {notebookType: '*'},
                ],
                this
            ),
            vscode.commands.registerCommand(
                'baidu.comate._ghostTextPostInsert',
                (inlineCompletion: InlineCompletionItem) => {
                    this.ghostTextUsageGuide?.hide();
                    this.ghostTextUsageGuide?.handleAccepted(inlineCompletion.selectedCompletionInfo !== undefined);
                    this._onDidAcceptInlineCompletionItem.fire(inlineCompletion);
                    return handleGhostTextPostInsert(this.ctx, inlineCompletion);
                }
            ),
            vscode.commands.registerCommand(
                'baidu.comate.cancel',
                () => {
                    vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
                    this.ghostTextUsageGuide?.hide();
                    handleCompletionHide();
                }
            ),
            vscode.commands.registerCommand(
                'baidu.comate.acceptNextLine',
                () => {
                    if (compare(vscode.version, '1.78', '>=')) {
                        // only supported after vscode 1.78: https://github.com/microsoft/vscode/issues/173209
                        vscode.commands.executeCommand('editor.action.inlineSuggest.acceptNextLine');
                        this.ghostTextUsageGuide?.hide();
                        this.multiLineCompletionTracker?.acceptNextLine();
                    }
                }
            ),
            vscode.commands.registerCommand(
                'baidu.comate.triggerCodeBasedOnLineComment',
                () => {
                    if (this.configProvider?.getEnableCommentEnhancement()) {
                        this.isCommentToCodeMode = true;
                        vscode.commands.executeCommand('editor.action.inlineSuggest.trigger');
                    }
                }
            )
        );
    }

    // eslint-disable-next-line complexity, max-statements
    async provideInlineCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.InlineCompletionContext,
        cancelToken: vscode.CancellationToken
    ): Promise<InlineCompletionItem[]> {
        if (!isInternal && this.configProvider && !this.configProvider.getConfig(ConfigKey.EnableInlineSuggestion)) {
            // 未开启续写总开关
            trace('Inline completion is disabled globally');
        }
        if (this.isFrozen) {
            return [];
        }
        const interceptors = [...this.interceptors.values()];

        const matches = await Promise.all(interceptors.map(item => item.match(document, position, context)));

        const winner = maxBy(matches, 'score');

        if (winner && winner.score > 0) {
            winner.execute();
            return [];
        }

        const activeEditor = vscode.window.activeTextEditor;
        if (
            activeEditor
            && activeEditor.document.uri.fsPath === document.uri.fsPath
            && activeEditor.selections.length > 1
        ) {
            // VS Code 目前的设计无法实现给多光标处提供不同的补全内容，因此当有多光标时放弃补全。
            return [];
        }

        const result = await getInlineCompletions(
            this.ctx,
            document,
            position,
            context,
            cancelToken,
            this.completionContextProvider,
            this.treeSitterProvider,
            this.loadingDecorations,
            this.lineCommentDecorations,
            this.isCommentToCodeMode,
            this.rewriteHandler
        );

        const inlineCompletions = handleGhostTextResult(result);

        for (const item of inlineCompletions) {
            item.selectedCompletionInfo = context.selectedCompletionInfo;
        }

        // NOTE: 当补全列表出现时，续写需要以候选项的文本为前缀，并确保替换的范围一致才会展示 https://code.visualstudio.com/api/references/vscode-api#InlineCompletionContext。
        // 当用户输入的字符非完全匹配候选前缀时，候选列表也会显示，然而现有的处理逻辑有 bug - 在拼接 insertText 时使用了原文该范围的文本开头，而非候选对应的字符，未能符合 api 要求。
        // 目前已尝试按要求修改，但仍未渲染，即便设置了 filterText。
        // TODO: 下一步可以做个最小 repro 插件进行测试，明确是 api 使用错误还是 vscode 的 bug。@tianzerun
        // EDIT: 用改写视图来解决 vscode 无法渲染的情况
        if (context.selectedCompletionInfo) {
            const textToReplace = document.getText(context.selectedCompletionInfo.range);
            if (!context.selectedCompletionInfo.text.startsWith(textToReplace)) {
                // 无法用 vscode 的续写进行展示，换用改写视图
                const first = inlineCompletions[0];
                if (
                    this.rewriteHandler
                    && first?.range
                    && first?.isExtensionToSelectedCompletionInfo
                    && typeof first?.insertText === 'string'
                ) {
                    const originalLine = document.lineAt(position.line).text;
                    const generatedContent = originalLine.slice(0, first.range.start.character)
                        + first.insertText
                        + originalLine.slice(first.range.end.character);
                    this.rewriteHandler.showRewrite(document.uri, {
                        uuid: first.uuid,
                        startRow: position.line + 1,
                        endRow: position.line + 1,
                        generatedContent,
                    });
                }
                return [];
            }
        }

        // 当得到续写结果，就将 loading 效果及快捷键触发提示隐藏
        this.loadingDecorations?.hide();
        inlineCompletions.length > 0 && this.isCommentToCodeMode && this.lineCommentDecorations?.hide();

        this.isCommentToCodeMode = false;

        this._onDidProvideInlineCompletionItems.fire(inlineCompletions);

        return inlineCompletions;
    }

    protected _handleDidShowCompletionItem(
        ctx: Container,
        item: InlineCompletionItem | undefined,
        document?: vscode.TextDocument,
        position?: vscode.Position
    ) {
        if (item && item.range) {
            handleGhostTextShown(ctx, item, document, position);
            this.multiLineCompletionTracker?.record(
                item.uuid,
                item.originalText
            );
            this.ghostTextUsageGuide?.show(
                item.range.start.line,
                item.displayText.includes('\n'),
                item.selectedCompletionInfo !== undefined
            );
        }
    }

    registerInterceptor(name: string, interceptor: InlineCompletionInterceptor) {
        this.interceptors.set(name, interceptor);
    }

    freeze(duration: number) {
        if (this.freezeTimeout) {
            clearTimeout(this.freezeTimeout);
        }
        this.isFrozen = true;
        this.freezeTimeout = setTimeout(() => {
            this.isFrozen = false;
            this.freezeTimeout = null;
        }, duration);
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
