import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {getUserConfig, modelListApi, ModelRes, updateUserConfig} from '@/api';
import {isInternal} from '@/utils/features';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {UserService} from '../UserService';
import {VSCodeConfigProvider} from '../ConfigProvider';

export const CHAT_MODEL_ID = 'CHAT_MODEL_ID';
export const CMD_UPDATE_DEFAULT_MODEL = 'baidu.comate.updateDefaultModel';
export const CMD_DEFAULT_MODEL = 'baidu.comate.defaultModel';

@injectable()
export default class ModelSelector implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private key?: string;
    private firstSelect = true;
    modelList: ModelRes[] = [];
    context!: vscode.ExtensionContext;

    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(UserService) private readonly userService: UserService
    ) {
        this.disposables.push(
            vscode.commands.registerCommand(CMD_UPDATE_DEFAULT_MODEL, this.changeSelect.bind(this)),
            vscode.commands.registerCommand(CMD_DEFAULT_MODEL, this.getLastSelect.bind(this))
        );
        this.init();
    }

    async init() {
        const context = await getExtensionContextAsync();
        this.context = context;
        const license = this.configProvider.getLicense();
        const [username] = await this.userService.getCurrentUser();
        this.key = isInternal ? username : license;
        try {
            const res = await modelListApi({
                key: isInternal ? username : license,
                type: 'CHAT',
            });
            res.data && (this.modelList = res.data);
            const last = this.context.globalState.get<string>(CHAT_MODEL_ID);
            if (!last && this.modelList.length > 0 && this.modelList.some(v => v.isDefault)) {
                this.context.globalState.update(CHAT_MODEL_ID, this.modelList.find(v => v.isDefault)!.modelId);
            }
        }
        catch (error) {
            console.log(error);
        }
    }

    async changeSelect({id, type}: {id: string, type: 'CHAT' | 'COMPLETION'}) {
        if (type === 'CHAT') {
            this.context.globalState.update(CHAT_MODEL_ID, id);
            if (this.key) {
                await updateUserConfig(this.key, {
                    modelId: id,
                });
            }
        }
    }

    async getLastSelect(modelList: ModelRes[]) {
        if (this.key && this.firstSelect) {
            this.firstSelect = false;
            const res = await getUserConfig(this.key);
            if (res?.modelId) {
                this.context.globalState.update(CHAT_MODEL_ID, res.modelId);
                return {
                    chat: res.modelId,
                    completion: undefined,
                };
            }
        }
        const last = this.context.globalState.get<string>(CHAT_MODEL_ID);
        if (last && modelList?.find(v => v.modelId === last)) {
            return {
                chat: last,
                completion: undefined,
            };
        }
        return {
            chat: this.modelList.find(v => v.isDefault)?.modelId ?? undefined,
            completion: undefined,
        };
    }

    getModelList() {
        return this.modelList;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
