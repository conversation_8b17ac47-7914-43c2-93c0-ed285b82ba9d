import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import axios from 'axios';
import {isEmpty} from 'lodash';
import {Message, Metadata} from '@shared/protocols';
import {TYPES} from '@/inversify.config';
import {L10n, getGenerationFailureText, getRequestFailureText} from '@/common/L10nProvider/L10n';
import {Nl2codeProviderText} from '@/common/L10nProvider/constants';
import {getCurrentSelection, getCompleteFirstLine} from '../../utils/document';
import {addMarkdownCodeBlock, hideSensitiveContent} from '../../utils/common';
import {GenerateCodeOptions, generateCode} from '../../api';
import {stripExtraIndent} from '../../utils/indent';
import {ChatViewProvider} from '../ChatViewProvider';
import {UserService} from '../UserService';
import {buildParams, fetchAndStreamCode} from '../../common/Fetcher';
import {Conversation} from '../ChatViewProvider/Conversation';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {ITimeTracker} from '../TimeTracker/types';
import {SuccessReturn} from '../types';
import {ChatBaseProvider} from '../ChatBaseProvider';
import {DiffProvider} from '../DiffProvider';
import {TextResponse} from '../ChatViewProvider/TextResponse';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {CMD_INVOKE_NL2CODE} from './constants';

interface HandleOptions {
    needContext?: boolean;
    openChatPanel?: boolean;
    messageOrder?: Record<number, number>;
    ignoreSelection?: boolean;
    metadata?: Metadata;
}

function filterEmptyUserAndAssistant(data: Message[]): Message[] {
    const skipIndices: number[] = [];

    data.forEach((item, index) => {
        if (item.role === 'user') {
            // If role is user and content is empty, add indices of user and corresponding assistant to skipIndices.
            const assistantIndex = data.findIndex(
                (assistant, assistantIndex) => assistant.role === 'assistant' && assistantIndex > index
            );
            if (!item.content) {
                skipIndices.push(index);
                if (assistantIndex > -1) {
                    skipIndices.push(assistantIndex);
                }
            }
            // 内容非字符串的也排除
            if (typeof data[assistantIndex].content !== 'string') {
                skipIndices.push(index);
                skipIndices.push(assistantIndex);
            }
        }
    });

    return data.filter((_, index) => !skipIndices.includes(index));
}

@injectable()
export class NL2CodeProvider extends ChatBaseProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(UserService) private readonly userService: UserService,
        @inject(TYPES.ITimeTracker) private readonly timeTracker: ITimeTracker,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(PerformanceLogProvider) private readonly performanceLog: PerformanceLogProvider
    ) {
        super(diffProvider);
        this.disposables.push(
            vscode.commands.registerCommand(CMD_INVOKE_NL2CODE, this.handleNL2Code.bind(this))
        );
    }

    private async buildRequestParams(query: string, chatHisotries: Message[], context?: string) {
        const document = vscode.window.activeTextEditor?.document;
        const params = await buildParams(document, new vscode.Position(0, 0), this.userService);
        if (params.type !== 'success') {
            return params;
        }

        const result = {
            ...params.value,
            content: context ?? '',
            userInput: query,
            model: 'ERNIE_BOT',
            function: 'COMMENT_TO_CODE',
            userInputHistory: JSON.stringify(chatHisotries.map(v => ({
                role: v.role === 'user' ? 'user' : 'assistant',
                content: v.content.trim() + (v.code ? `\n${hideSensitiveContent(v.code)}` : ''),
            }))),
        };
        return {type: 'success', value: result} as SuccessReturn<GenerateCodeOptions>;
    }

    async generate(
        query: string,
        conversation: Conversation,
        chatHistories: Message[],
        startTime: number,
        context?: string,
        metadata?: Metadata
    ) {
        const streamMode = this.configProvider.getConfig(ConfigKey.EnableStreamingSession);
        const reply = conversation.addResponse(
            streamMode ? 'stream' : 'text',
            '',
            'inProgress',
            {
                regenerate: () => {
                    this.generate(query, conversation, chatHistories, performance.now(), context, metadata);
                },
                // 打开全文复制按钮
                copyAll: () => {},
            },
            metadata
        );
        // 把 role = user 的 content 为空的问答对删掉，不然接口报错
        const filteredChatHistories = filterEmptyUserAndAssistant(chatHistories);
        const params = await this.buildRequestParams(query, filteredChatHistories, context);

        if (params.type !== 'success') {
            reply.fail(getGenerationFailureText(params.reason));
            return {uuid: ''};
        }

        try {
            if (streamMode) {
                return this.fetchAndStream(reply, params.value, startTime);
            }
            else {
                return this.fetchWithParams(reply, params.value);
            }
        }
        catch (e: any) {
            reply.fail(getGenerationFailureText(e.message));
            return {uuid: ''};
        }
    }

    private async fetchWithParams(reply: TextResponse, params: GenerateCodeOptions) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        this.timeTracker.recordChatStart(reply.message.replyTo ?? -1);
        const result = await generateCode(params, axiosTokenSource.token);
        this.timeTracker.bindChatId(result.data.data?.uuid ?? '', reply.message.replyTo ?? -1);

        if (result.data.status !== 'OK') {
            const msg = result.data.message;
            reply.fail(getRequestFailureText(msg));
            return;
        }
        const generatedContent = result.data.data?.content;
        const uuid = result.data.data?.uuid;
        if (!generatedContent) {
            reply.fail(L10n.t(Nl2codeProviderText.EMPTY_TEXT));
            return;
        }
        const chatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getChatId: () => result.data.data?.chatId,
            getMessageContent: () => reply.message.content,
            getTrackUuid: () => uuid || reply.message.extra?.uuid,
        };
        const actions = {
            ...this.defaultActions(chatResponseProxy),
            ...this.extraActions(chatResponseProxy),
        };
        return reply.success(generatedContent, actions, uuid);
    }

    private async fetchAndStream(reply: TextResponse, params: GenerateCodeOptions, startTime: number) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        const baseChatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getMessageContent: () => reply.message.content,
        };
        let firstToken = true;
        const {content, uuid, processor, chatId} = await fetchAndStreamCode(
            params,
            // reply.updateWithId.bind(reply),
            (content: string, uuid: string, chatId: string) => {
                const chatResponseProxy = {
                    ...baseChatResponseProxy,
                    getChatId: () => chatId,
                    getTrackUuid: () => uuid || reply.message.extra?.uuid,
                };
                if (content && firstToken) {
                    this.performanceLog.log({
                        plugin: 'comate',
                        skill: 'NL2CodeProvider',
                        duration: performance.now() - startTime,
                        uuid,
                        type: 'first-token',
                    });
                    firstToken = false;
                }
                reply.update(
                    content,
                    {...this.defaultActions(chatResponseProxy), ...this.extraActions(chatResponseProxy)}
                );
            },
            reply.message.cancelTokenSource?.token,
            axiosTokenSource.token
        );
        if (processor.error) {
            const msg = processor.errorMsg;
            reply.fail(getRequestFailureText(msg));
            return;
        }
        if (!content) {
            reply.fail(L10n.t(Nl2codeProviderText.EMPTY_TEXT));
            return;
        }
        const chatResponseProxy = {
            ...baseChatResponseProxy,
            getChatId: () => chatId,
            getTrackUuid: () => uuid || reply.message.extra?.uuid,
        };
        reply.success(
            content,
            {...this.defaultActions(chatResponseProxy), ...this.extraActions(chatResponseProxy)},
            uuid
        );
        return {uuid};
    }

    private async handleNL2Code(query: string, options?: HandleOptions, startTime: number = performance.now()) {
        if (options && options.openChatPanel) {
            vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_INVOKE_NL2CODE});
        }
        const [code, languageId] = getCurrentSelection();
        const editor = vscode.window.activeTextEditor;
        const firstLine = editor ? getCompleteFirstLine(editor.document, editor.selection) : undefined;
        const chatHisotries = options?.messageOrder
            ? this.chatViewProvider.getChatHistory(10, options.messageOrder)
            : [];
        const context = (code && !options?.ignoreSelection)
            ? addMarkdownCodeBlock(stripExtraIndent(code, false, firstLine), languageId)
            : undefined;
        const conversation = this.chatViewProvider.createConversation(
            query,
            'nl2Code',
            context
        );

        if (options?.needContext && isEmpty(code)) {
            conversation.addResponse('text', L10n.t(Nl2codeProviderText.NO_SELECTION), 'failed');
            return;
        }
        conversation.updateView();
        const res = await this.generate(query, conversation, chatHisotries, startTime, code, options?.metadata);
        if (res?.uuid) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'NL2CodeProvider',
                duration: performance.now() - startTime,
                uuid: res.uuid,
                type: 'all',
            });
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
