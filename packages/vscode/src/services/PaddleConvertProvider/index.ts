/* eslint-disable max-depth */
/* eslint-disable max-len */
import * as path from 'path';
import * as fs from 'fs';
import * as vscode from 'vscode';
import {injectable, inject, LazyServiceIdentifer} from 'inversify';
import {AgentListWithId, KnowledgeList} from '@shared/protocols';
import {TYPES} from '@/inversify.config';
import {activeFileContext, getCurrentSelection} from '@/utils/document';
import {checkTreeSitterSupport, extractTorchImportNodes} from '../../utils/treeSitterUtils';
import {TreeSitterProvider, isFileTooLarge} from '../TreeSitterProvider';
import {CodeLensDisplayMode, CodelensConfig, ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {ILicenseController} from '../LicenseController/types';
import {CodeLensProvider} from '../FoldedCodeLensProvider';
import {ChatBaseProvider} from '../ChatBaseProvider';
import {DiffProvider} from '../DiffProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {ComatePlusChatSession} from '../ComatePlusChatSession';
import {CMD_PADDLE_CONVERT} from '../ComatePlusChatSession/constants';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';

@injectable()
export class PaddleConvertProvider extends ChatBaseProvider implements CodeLensProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
    ];
    private disposables: vscode.Disposable[] = [];
    private readonly pluginName: string = 'paddle';
    private readonly config: AgentListWithId | undefined;

    constructor(
        @inject(ComatePlusChatSession) private readonly comatePlusChatSession: ComatePlusChatSession,
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(TYPES.ILicenseController) private readonly licenseController: ILicenseController,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider
    ) {
        super(diffProvider);
        this.config = this.chatViewProvider.chatCompletionList[0].find(item => item?.name === this.pluginName);
        this.disposables.push(
            vscode.languages.registerCodeLensProvider(
                PaddleConvertProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this
            ),
            vscode.commands.registerCommand(CMD_PADDLE_CONVERT, this.handlePaddleConvert.bind(this))
        );
    }

    async handlePaddleConvert(
        query: string,
        pluginName: string,
        capability?: string,
        knowledgeList?: KnowledgeList[],
        data?: unknown
    ) {
        const [code] = getCurrentSelection();
        const conversation = this.chatViewProvider.createConversation(
            '',
            'comatePlus',
            this.getTipContent(query || '', knowledgeList || [])
        );
        conversation.updateView();
        await this.comatePlusChatSession.generate(query, conversation, [], code, {
            query,
            data,
            pluginName,
            capability,
            name: pluginName,
            icon: this.config?.icon ?? '',
            extra: {
                capability,
                knowledgeList,
                renderJSX: true,
            },
        });
    }

    // eslint-disable-next-line complexity, max-statements
    getTipContent(query: string, knowledgeList: KnowledgeList[]) {
        const [selectCode, selectLanguage] = getCurrentSelection();
        const {activeFileContent, activeFileLanguage} = activeFileContext();
        // 优先级1: 用户选择的文件/目录
        if (knowledgeList.length) {
            if (knowledgeList[0].name === 'repo' || knowledgeList[0].id === 'repo') {
                const workspace = vscode.workspace.workspaceFolders;
                return `将当前整个代码库转换为飞桨\n\`\`\`\n${
                    workspace ? workspace[0].uri.path.split('/').pop() : ''
                }\n\`\`\`\n`;
            }
            if (knowledgeList[0].name === 'currentFile' && activeFileContent) {
                return `将当前文件中代码转换为飞桨\n\`\`\`${activeFileLanguage || ''}\n${
                    activeFileContent || ''
                }\n\`\`\`\n`;
            }
            if (knowledgeList[0].type === 'FILE') {
                const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                if (!rootPath) {
                    return '';
                }
                const pathToFile = path.join(rootPath, knowledgeList[0].id);
                try {
                    const stats = fs.statSync(pathToFile);
                    if (stats.isDirectory()) {
                        return `将下面整个目录中代码转换为飞桨\n\`\`\`\n${knowledgeList[0].id}\n\`\`\`\n`;
                    }
                    if (stats.isFile()) {
                        return `将下面文件中代码转换为飞桨\n\`\`\`\n${knowledgeList[0].id}\n\`\`\`\n`;
                    }
                }
                catch (e) {
                    return '文件不存在或不可读，请检查文件路径。';
                }
            }
        }

        // 优先级2: query为相对路径
        if (query.trim().length > 0) {
            const rootPath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            const queryTrim = query.trim();
            if (rootPath) {
                const pathToFile = path.join(rootPath, queryTrim);
                try {
                    const stats = fs.statSync(pathToFile);
                    if (stats.isDirectory()) {
                        return `将下面整个目录中代码转换为飞桨\n\`\`\`\n${queryTrim}\n\`\`\`\n`;
                    }
                    if (stats.isFile()) {
                        return `将下面文件中代码转换为飞桨\n\`\`\`\n${queryTrim}\n\`\`\`\n`;
                    }
                }
                catch (e) {
                    // do nothing
                }
            }
        }

        // 优先级3: 选中代码
        if (selectCode) {
            return `将当前选中代码转换为飞桨\n\`\`\`${selectLanguage}\n${selectCode}\n\`\`\`\n`;
        }

        // 优先级4: 当前文件
        if (activeFileContent) {
            return `将当前文件中代码转换为飞桨\n\`\`\`${activeFileLanguage}\n${activeFileContent}\n\`\`\`\n`;
        }
        return '';
    }

    async provideCodeLenses(document: vscode.TextDocument) {
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();
        if (codelensDisplayMode !== CodeLensDisplayMode.TextTitle) {
            return [];
        }
        if (this.diffProvider.isInDiff(document.uri.fsPath)) {
            return [];
        }
        const codeLenses = await this.computeCodeLenses(document);
        return this.tmpCodeLensProvider.filterOverlapCodeLenses(document, codeLenses);
    }

    async computeCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensConfig = this.configProvider.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        if (codelensConfig?.enableInlineExplain !== true || !this.licenseController.hasLicense) {
            return [];
        }
        if (isFileTooLarge(document)) {
            return [];
        }
        const language = checkTreeSitterSupport(document.languageId);
        if (!language) {
            return [];
        }
        const tree = this.treeSitterProvider.getDocumentTree(document);
        if (!tree) {
            return [];
        }
        const nodes = extractTorchImportNodes(language, tree.rootNode, document);
        const codeLenses = nodes
            .map(node => {
                const {startPosition, endPosition} = node;
                const codeLens = new vscode.CodeLens(
                    new vscode.Range(
                        new vscode.Position(startPosition.row, 0),
                        new vscode.Position(endPosition.row, 0)
                    ),
                    {
                        title: '代码转换',
                        tooltip: '飞桨代码转换',
                        command: 'baidu.comate.paddleCovert',
                        arguments: ['', 'paddle', 'code-convert', []],
                    }
                );
                return codeLens;
            });
        return codeLenses;
    }
    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
