import * as vscode from 'vscode';
import {injectable} from 'inversify';
import {L10n} from '@/common/L10nProvider/L10n';
import {InlineChatText} from '@/common/L10nProvider/constants';
import {belowIndexDecorationType, indexDecorationType} from './InlineChatProvider/types';
/**
 * 统一管理编辑区的codelens loading状态、流式背景状态，防止抢占问题
 */
@injectable()
export class VisualStateManagerProvider implements vscode.CodeLensProvider, vscode.Disposable {
    private readonly loadingRange: Map<string, vscode.Range> = new Map();
    private readonly onCancelCallbacks: Map<string, (source?: any) => void> = new Map();
    private readonly _onDidChangeCodeLenses: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();
    private disposables: vscode.Disposable[] = [];
    onDidChangeCodeLenses = this._onDidChangeCodeLenses.event;

    constructor() {
        this.disposables.push(
            vscode.languages.registerCodeLensProvider({scheme: 'file'}, this)
        );
    }

    addLoadingRange(filePath: string, range: vscode.Range) {
        this.loadingRange.set(filePath, range);
        this._onDidChangeCodeLenses.fire();
        vscode.commands.executeCommand('setContext', 'baidu.comate.editor.isLoading', true);
    }

    addOnCancelCallback(filePath: string, callback: (source?: any) => void) {
        this.onCancelCallbacks.set(filePath, callback);
    }

    getLoadingRange(path: string) {
        // Don't fire event when just checking loading range
        return this.loadingRange.get(path);
    }

    async clearLoadingRange(path: string, source?: any) {
        this.loadingRange.delete(path);
        this._onDidChangeCodeLenses.fire();
        this.onCancelCallbacks.get(path)?.(source);
        await vscode.commands.executeCommand('setContext', 'baidu.comate.editor.isLoading', false);
    }

    async updateIndexLineDecorations(
        filePath: string,
        range: vscode.Range
    ) {
        const uri = vscode.Uri.file(filePath);
        const document = await vscode.workspace.openTextDocument(uri);
        const editor = await vscode.window.showTextDocument(document);
        if (range.start.line === range.end.line) {
            editor.setDecorations(indexDecorationType, []);
            editor.setDecorations(belowIndexDecorationType, []);
        }
        else {
            const start = new vscode.Position(range.start.line, 0);
            editor.setDecorations(indexDecorationType, [
                new vscode.Range(
                    start,
                    new vscode.Position(start.line, Number.MAX_SAFE_INTEGER)
                ),
            ]);
            const end = new vscode.Position(range.end.line, 0);
            editor.setDecorations(belowIndexDecorationType, [
                new vscode.Range(start.translate(1), end),
            ]);
        }
    }

    provideCodeLenses(document: vscode.TextDocument): vscode.CodeLens[] | Thenable<vscode.CodeLens[]> {
        const codeLenses: vscode.CodeLens[] = [];
        const loadingRange = this.getLoadingRange(document.uri.fsPath);

        if (loadingRange) {
            const generatedCodeLens = new vscode.CodeLens(
                loadingRange,
                {
                    title: L10n.t(InlineChatText.INLINECHAT_TITLE, '$(loading~spin)', '(Esc取消)'),
                    command: '',
                    arguments: [document.uri],
                }
            );
            return [generatedCodeLens];
        }

        return codeLenses;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
