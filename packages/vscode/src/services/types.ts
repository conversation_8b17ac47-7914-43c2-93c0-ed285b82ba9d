import * as vscode from 'vscode';
import {TelemetryData} from '@/common/telemetry';
import {RawGenerateCode} from '../api';

export enum CompletionSource {
    Normal = 'normal',
    API = 'api',
}

export interface CompletionChoice {
    rawData: RawGenerateCode;
    // 后面要覆盖的内容
    expectCoverText: string;
    // 修正前缀空格后的补全内容
    completionText: string;
    // 服务端初始生成的内容
    originalText: string;
    // 补全来源
    source: CompletionSource;
    // 是否基于候选项扩充的续写结果
    isExtensionToSelectedCompletionInfo?: boolean;
}

export type FetchingChoice = Promise<CompletionChoice | undefined>;

export interface CacheCompletionItem {
    choices: CompletionChoice[];
}

export enum ResultType {
    // 新的推荐走网络只会有一个结果，后面的放到缓存
    Network = 0,
    Cache = 1,
    TypingAsSuggested = 2,
    // toolbar 走这个模式
    Cycling = 3,
}

export interface CompletionResult {
    resultType: ResultType;
    data: CompletionChoice[];
}

export interface UnsuccessReturn {
    type: 'failed' | 'aborted' | 'cancelled';
    reason: string;
}

export interface SuccessReturn<T> {
    type: 'success';
    value: T;
}

export interface InlineCompletionItem extends vscode.InlineCompletionItem {
    uuid: string;
    displayText: string;
    telemetry: TelemetryData;
    uri: vscode.Uri;
    position: vscode.Position;
    // 从文件初始位置到该 position 的偏移，可以利用 document.offsetAt(position) 来计算
    insertOffset: number;
    // 触发续写时的补全列表选中项
    selectedCompletionInfo?: vscode.SelectedCompletionInfo;
    // 续写结果是否基于候选项扩充的
    isExtensionToSelectedCompletionInfo?: boolean;
    // NOTE: 如果用户的输入与之前续写结果的 prefix 完全匹配，会复用之前的结果展示，但 displayText 以及 insertText 会是截取掉用户输入的部分，因此保留服务端返回的原始补全内容便于上报。
    originalText: string;
    duration?: number;
    cache?: boolean;
    source: CompletionSource;
    // 是否用续写结果覆盖光标后当前行的内容
    coverSuffix: boolean;
}

export type InlineCompletionResult =
    | SuccessReturn<InlineCompletionItem[]>
    | UnsuccessReturn;

export interface Completion {
    choice: CompletionChoice;
    displayText: string;
    displayNeedsWsOffset: boolean;
    coverSuffix: boolean;
}

export interface GhostTextResult {
    resultType: ResultType;
    data: Completion[];
    telemetryBlob: TelemetryData;
}

export type GhostTextReturn = SuccessReturn<GhostTextResult> | UnsuccessReturn;

export interface SelectedLocation {
    uri: vscode.Uri;
    selection: vscode.Selection;
    kind: vscode.TextEditorSelectionChangeKind | undefined;
}
