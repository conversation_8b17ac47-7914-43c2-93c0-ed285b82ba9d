/* eslint-disable complexity */
import {extname} from 'node:path';
import {memoize} from 'lodash';
import {axiosInstance} from '@comate/plugin-shared-internals';
import {IllegalTextExtDefinition} from '@shared/composer';
import {iocContainer} from '@/iocContainer';
import {isInternal} from '@/utils/features';
import {uuapHeader} from '../AutoComateChatSession/api';
import {UserService} from '../UserService';
import {VSCodeConfigProvider} from '../ConfigProvider';

const getIllegalTextExtname = memoize(async (userName: string) => {
    try {
        const data = await axiosInstance.get<{data: IllegalTextExtDefinition[]}>(
            '/api/aidevops/autocomate/rest/autowork/v1/reference-file-extension-whitelist',
            {
                headers: await uuapHeader(userName),
            }
        );
        return data.data.data;
    }
    catch (ex) {
        return [];
    }
});

// 根据文件路径判断文件是否是音频、视频、图标、文档等
export async function illegalTextFile(filePath: string) {
    try {
        const userName = isInternal
            ? (await iocContainer.get(UserService).getCurrentUser())[0]
            : await iocContainer.get(VSCodeConfigProvider).getLicense();

        const defintions = await getIllegalTextExtname(userName);
        const ext = extname(filePath);
        const def = defintions.find(def => {
            return def.extensions.includes(ext.replace(/^./, ''));
        });
        return def ? {illegal: true, type: def.type} as const : {illegal: false, type: ''} as const;
    }
    catch (ex) {
        return {illegal: false, type: ''} as const;
    }
}
