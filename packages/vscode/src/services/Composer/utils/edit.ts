import * as vscode from 'vscode';

export const overwriteDocument = async (uri: vscode.Uri, content: string | null) => {
    const workspaceEdit = new vscode.WorkspaceEdit();
    workspaceEdit.replace(
        uri,
        new vscode.Range(
            new vscode.Position(0, 0),
            new vscode.Position(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER)
        ),
        content || ''
    );
    await vscode.workspace.applyEdit(workspaceEdit);
};
