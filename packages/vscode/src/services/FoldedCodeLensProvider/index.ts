/* eslint-disable max-len */
import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {flatten, groupBy} from 'lodash';
import platform from '@shared/platform';
import {RegisteredCommand} from '@/constants';
import {L10n} from '@/common/L10nProvider/L10n';
import {ExtensionText, OptimizeProviderText} from '@/common/L10nProvider/constants';
import {isPoc} from '@/utils/features';
import {CodeLensDisplayMode, VSCodeConfigProvider} from '../ConfigProvider';
import {DiffProvider} from '../DiffProvider';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';

export interface CodeLensProvider extends vscode.CodeLensProvider {
    computeCodeLenses(document: vscode.TextDocument, token?: vscode.CancellationToken): Promise<vscode.CodeLens[]>;
}

@injectable()
export class FoldedCodeLensProvider implements vscode.CodeLensProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'cpp',
        'c',
        'javascript',
        'typescript',
        'javascriptreact',
        'jsx',
        'typescriptreact',
        'vue',
    ];
    private disposables: vscode.Disposable[] = [];

    private readonly codeLensProviders: CodeLensProvider[] = [];

    quickPickItemsMap: Map<vscode.Range, vscode.QuickPickItem[]> = new Map();

    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider
    ) {
        this.disposables.push(
            vscode.languages.registerCodeLensProvider(
                FoldedCodeLensProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this
            ),
            vscode.commands.registerCommand(
                RegisteredCommand.showCodelensDetailQuickPick,
                async (
                    codeLensGroup: Array<{label: string, command: string, description: string, arguments: any[]}>
                ) => {
                    const optimizeCodelensName = L10n.t(OptimizeProviderText.CODELENS_TITLE);
                    const sortedCodeLensGroup = codeLensGroup[0].label === optimizeCodelensName
                        ? codeLensGroup.reverse()
                        : codeLensGroup;
                    const items = sortedCodeLensGroup
                        .map(item => {
                            return {label: item.label};
                        });
                    const selected = await vscode.window.showQuickPick(items, {
                        title: `${platform.resolve('brand')}`,
                        placeHolder: L10n.t(ExtensionText.QUICK_PICK_PLACEHOLDER),
                    });

                    if (selected) {
                        const result = sortedCodeLensGroup.find(
                            quickPickItem => quickPickItem.label === selected.label
                        );
                        result?.command && vscode.commands.executeCommand(result?.command, ...result.arguments);
                    }
                }
            )
        );
    }

    addCodeLensProvider(provider: CodeLensProvider) {
        this.codeLensProviders.push(provider);
    }

    async provideCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();

        if (this.diffProvider.isInDiff(document.uri.fsPath)) {
            return [];
        }

        const codeLenses = await Promise.all(
            [
                ...this
                    .codeLensProviders,
            ]
                .reverse()
                .map(item => item.computeCodeLenses(document))
        );
        const flattenedCodeLenses = flatten(codeLenses);
        const newCodeLensArray: vscode.CodeLens[] = [];
        this.quickPickItemsMap = new Map();
        const groupedCodeLenses = groupBy(flattenedCodeLenses, codeLens => codeLens.range.start.line);

        Object.values(groupedCodeLenses).forEach(codeLensGroup => {
            if (codeLensGroup.length === 0) {
                return;
            }
            const curRange = codeLensGroup[0].range;
            const range = new vscode.Range(
                curRange.start.line,
                curRange.start.character,
                curRange.end.line,
                document.lineAt(curRange.end.line).range.end.character
            );

            const quickPickItems = codeLensGroup
                .filter(codeLens => codeLens.command)
                .map(codeLens => {
                    const result = {
                        label: '/' + codeLens.command!.title,
                        command: codeLens.command!.command,
                        description: codeLens.command!.tooltip,
                        arguments: codeLens.command!.arguments,
                    };
                    return result;
                });

            this.quickPickItemsMap.set(range, quickPickItems);

            if (codelensDisplayMode === CodeLensDisplayMode.MinimizedIcon && !isPoc) {
                newCodeLensArray.push(
                    new vscode.CodeLens(
                        range,
                        {
                            title: '$(comate-logo-mini)$(chevron-down)',
                            command: RegisteredCommand.showInlineChatAndSelectCode,
                            arguments: [document, range, quickPickItems],
                        }
                    )
                );
            }
        });
        return this.tmpCodeLensProvider.filterOverlapCodeLenses(document, newCodeLensArray);
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
