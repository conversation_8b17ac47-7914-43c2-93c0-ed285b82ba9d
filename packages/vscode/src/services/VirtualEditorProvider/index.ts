/* eslint-disable max-statements */
/* eslint-disable complexity */
import {basename} from 'path';
import {identity} from 'lodash';
import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {
    ACTION_MOCK_VIRTUAL_EDITOR_EVENT,
    VirtualEditor,
    VirtualEditorMethodCall,
} from '@comate/plugin-shared-internals';
import {EventMessage} from '@shared/protocols';
import {isFileExist} from '@/utils/fs';
import {vscodeCommands} from '@/utils/vscodeComands';
import {splitTerminalOutputToCommandChunks} from '@/utils/terminal';
import {iocContainer} from '@/iocContainer';
import {sleep} from '@/utils/common';
import {KernelProvider} from '../KernelProvider';
import {overwriteDocument} from '../Composer/utils/edit';
import {
    COMATE_COMPOSRER_DIFF_SCHEME_1,
    COMATE_COMPOSRER_DIFF_SCHEME_2,
    createModifedUri,
    createOriginalUri,
    virtualFileSystemModifiedImplenment,
    virtualFileSystemOriginalImplenment,
} from '../Composer/VirtualDiffFile';
import {ChatViewProvider} from '../ChatViewProvider';
import {TerminalManager} from '../TerminalLink/TerminalManager';

async function closeTab(equals: (tab: vscode.Tab) => boolean) {
    const tabGroups = vscode.window.tabGroups.all[0];
    const createdEditors = tabGroups.tabs.filter(equals);
    for (const createdEditor of createdEditors) {
        await vscode.window.tabGroups.close(createdEditor, true);
    }
}

async function replaceDocument(absolutePath: string, content: string) {
    const uri = vscode.Uri.file(absolutePath);
    if (await isFileExist(absolutePath)) {
        const workspaceEdit = new vscode.WorkspaceEdit();
        await overwriteDocument(uri, content);
        await vscode.workspace.applyEdit(workspaceEdit);
    }
    else {
        await vscode.workspace.fs.writeFile(uri, Buffer.from(content || ''));
    }
}

@injectable()
export class VirtualEditorProvider implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    constructor(
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider
    ) {
        this.kernelProvider.client?.onRequest(
            VirtualEditor.event,
            this.returnValueBy(res => {
                this.kernelProvider.sendRequest(VirtualEditor.event, res);
            })
        );
        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.VirtualEditorEvent,
            listener: this.returnValueBy(identity),
        });
        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.MockVirtualEditorEventFromEngine,
            listener: async payload => {
                const res = await this.kernelProvider.sendRequest(ACTION_MOCK_VIRTUAL_EDITOR_EVENT, payload);
                return res;
            },
        });
        // 清除历史
        closeTab(tab => {
            return tab.input instanceof vscode.TabInputTextDiff
                && tab.input.modified.scheme === COMATE_COMPOSRER_DIFF_SCHEME_2;
        });
        this.disposables.push(
            vscode.workspace.registerFileSystemProvider(
                COMATE_COMPOSRER_DIFF_SCHEME_1,
                virtualFileSystemOriginalImplenment
            ),
            vscode.workspace.registerFileSystemProvider(
                COMATE_COMPOSRER_DIFF_SCHEME_2,
                virtualFileSystemModifiedImplenment
            )
        );
    }

    returnValueBy(pipe: (res?: any) => void) {
        return async (event: VirtualEditorMethodCall) => {
            const action = event.action;
            const {uuid} = event.payload;
            switch (action) {
                case 'getActiveDocument': {
                    const activeEditor = vscode.window.activeTextEditor;
                    if (!activeEditor) {
                        return {uuid, existed: false, absolutePath: '', content: ''};
                    }
                    const document = activeEditor.document;
                    const scheme = document.uri.scheme === COMATE_COMPOSRER_DIFF_SCHEME_2
                        ? 'diff'
                        : document.uri.scheme;

                    return pipe({
                        uuid,
                        existed: true,
                        scheme,
                        isDirty: document.isDirty,
                        absolutePath: document.uri.fsPath,
                        content: document.getText(),
                    });
                }
                case 'openDocument': {
                    const {selection} = event.payload;
                    const document = await vscode.workspace.openTextDocument(event.payload.absolutePath);
                    const opts = selection
                        ? {
                            selection: new vscode.Range(
                                new vscode.Position(selection.start.line, selection.start.column),
                                new vscode.Position(selection.end.line, selection.end.column)
                            ),
                        } as vscode.TextDocumentShowOptions
                        : undefined;
                    await vscode.window.showTextDocument(document, opts);
                    return pipe({uuid});
                }
                case 'getDocument': {
                    try {
                        const document = await vscode.workspace.openTextDocument(event.payload.absolutePath);
                        return pipe({existed: true, uuid, content: document.getText(), isDirty: document.isDirty});
                    }
                    catch {
                        return pipe({existed: false, uuid, content: ''});
                    }
                }
                case 'closeDocument': {
                    await closeTab(tab => {
                        return tab.input instanceof vscode.TabInputText
                            && tab.input.uri.fsPath === event.payload.absolutePath;
                    });
                    return pipe({uuid});
                }
                case 'saveDocumentWithReplaceContent': {
                    const {absolutePath, content} = event.payload;
                    await replaceDocument(absolutePath, content);
                    const document = await vscode.workspace.openTextDocument(absolutePath);
                    await document.save();
                    return pipe({uuid});
                }
                case 'closeVirtualDiffDocument': {
                    const {absolutePath} = event.payload;
                    const diffModifiedUri = createModifedUri(absolutePath);
                    const document = await vscode.workspace.openTextDocument(diffModifiedUri);
                    await document.save();
                    await closeTab(tab => {
                        return tab.input instanceof vscode.TabInputTextDiff
                            && tab.input.modified.fsPath === diffModifiedUri.fsPath;
                    });
                    return pipe({uuid});
                }
                case 'openVirtualDiffDocument': {
                    const {content, modified, absolutePath, stream} = event.payload;
                    const diffOriginalUri = createOriginalUri(absolutePath);
                    const diffModifiedUri = createModifedUri(absolutePath);
                    if (typeof content === 'string') {
                        virtualFileSystemOriginalImplenment.updateContent(diffOriginalUri, content, false);
                    }

                    if (typeof modified === 'string') {
                        virtualFileSystemModifiedImplenment.updateContent(diffModifiedUri, modified, !!stream);
                    }

                    await closeTab(tab => {
                        return tab.input instanceof vscode.TabInputTextDiff
                            && tab.input.modified.fsPath === diffModifiedUri.fsPath;
                    });
                    // const activeTab = vscode.window.activeTextEditor;
                    // // 如果用户的焦点切换到了文本编辑器，那么不在做diff的跳转
                    // const preventFocus = activeTab?.document.uri.scheme === 'file';
                    // const opts: vscode.TextDocumentShowOptions = {
                    //     preserveFocus: preventFocus,
                    // };
                    await vscode.commands.executeCommand(
                        'vscode.diff',
                        diffOriginalUri,
                        diffModifiedUri,
                        basename(diffOriginalUri.fsPath)
                    );
                    return pipe({uuid});
                }
                case 'getVirtualDiffDocument': {
                    const {absolutePath} = event.payload;
                    const diffModifiedUri = createModifedUri(absolutePath);
                    try {
                        const document = await vscode.workspace.openTextDocument(diffModifiedUri);
                        return pipe({uuid, existed: true, content: document.getText()});
                    }
                    catch (ex) {
                        return pipe({uuid, existed: false, content: ''});
                    }
                }
                case 'replaceVirtualDiffModifiedDocument': {
                    // const useAutoScrollAsSelectionStayAtEnd =
                    // editor.selection.start.line === editor.document.lineCount;
                    const {absolutePath, content, scrollToLine} = event.payload;
                    const diffModifiedUri = createModifedUri(absolutePath);
                    virtualFileSystemModifiedImplenment.updateContent(diffModifiedUri, content, true);

                    // const isActiveModifiedDiff = isModifiedComposerDiff(editor.document.uri, absolutePath);
                    // if (!useAutoScrollAsSelectionStayAtEnd && !isActiveModifiedDiff) {
                    //     return;
                    // }
                    const editor = vscode.window.activeTextEditor;
                    if (editor && typeof scrollToLine === 'number') {
                        // 保证编辑器最新的内容已经更新进去
                        await sleep(300);
                        if (editor === vscode.window.activeTextEditor) {
                            const range = new vscode.Range(
                                new vscode.Position(scrollToLine, 0),
                                new vscode.Position(scrollToLine, Number.MAX_SAFE_INTEGER)
                            );
                            // 滚动到指定的行位置
                            editor.revealRange(range, vscode.TextEditorRevealType.InCenter);
                        }
                    }
                    return pipe({uuid});
                }
                case 'executeTerminalShell': {
                    const rootPath = vscode.workspace.workspaceFolders![0].uri.fsPath;
                    const {cmd, duration = 1000, cwd = rootPath, run} = event.payload;
                    if (!run) {
                        const terminal = vscode.window.activeTerminal ?? vscode.window.createTerminal();
                        vscode.env.clipboard.writeText(cmd);
                        terminal.show();
                        await vscode.commands.executeCommand('workbench.action.terminal.focus');
                        await vscode.commands.executeCommand('workbench.action.terminal.paste');
                        return;
                    }
                    const manager = iocContainer.get(TerminalManager);
                    const terminalInfo = await manager.getOrCreateTerminal(vscode.Uri.file(cwd));
                    terminalInfo.terminal.show();
                    const process = manager.runCommand(terminalInfo, cmd);
                    let result = '';
                    process.on('line', line => {
                        result += line + '\n';
                    });

                    let completed = false;
                    process.once('completed', () => {
                        completed = true;
                    });

                    // virtualEditor 设置在 duration 时间内返回，既然命令没有结束
                    await Promise.race([
                        process,
                        sleep(duration - 1000),
                    ]);
                    // Wait for a short delay to ensure all messages are sent to the webview
                    // This delay allows time for non-awaited promises to be created and
                    // for their associated messages to be sent to the webview, maintaining
                    // the correct order of messages (although the webview is smart about
                    // grouping command_output messages despite any gaps anyways)
                    await sleep(50);
                    return pipe({uuid, completed, output: result.trim()});
                }
                case 'getLatestOutputFromActiveTerminal': {
                    const rawTerminalOutput = await vscodeCommands.exposeTerminalOutput();
                    const terminalAndOutputs = splitTerminalOutputToCommandChunks(rawTerminalOutput);
                    const output = terminalAndOutputs.map(lines => lines.join('\n')).join('\n');
                    return pipe({output, uuid});
                }
                case 'openUrlInEditorWebview': {
                    const {url, title} = event.payload;
                    const safeUrl = /https?:\/\//.test(url) ? url : `http://${url}`;
                    await vscodeCommands.openUrlInEditorWebview({url: safeUrl, title});
                    return pipe({uuid});
                }
                case 'saveDocumentWithReplaceContentAndOpen': {
                    const {absolutePath, content} = event.payload;
                    await replaceDocument(absolutePath, content);
                    const document = await vscode.workspace.openTextDocument(absolutePath);
                    await document.save();
                    await vscode.window.showTextDocument(document);
                    return pipe({uuid});
                }
                case 'deleteDocument': {
                    const {absolutePath} = event.payload;
                    // windows 系统使用vscode.Uri.parse转的路径无法删除文件报错
                    // Unable to resolve filesystem provider with relative file path
                    // 'c:\Users\<USER>\Desktop\auto-debug-test\baidu\bcloud\auto-debug-test\divide.js'
                    vscode.workspace.fs.delete(vscode.Uri.file(absolutePath.replace(/\\/g, '/')));
                    return pipe({uuid});
                }
                case 'refreshProjectTree': {
                    // 可以但没必要 writeFile和delete会自动刷新文件树
                    // vscode.commands.executeCommand('workbench.files.action.refreshFilesExplorer');
                    return pipe({uuid});
                }
                default: {
                    const exhaustiveCheck: never = action;
                    throw new Error(exhaustiveCheck);
                }
            }
        };
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
    }
}
