import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {uniq} from 'lodash';
import {EventMessage} from '@shared/protocols';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {RECENT_FILES_STORAGE_KEY} from '../utils/files';
import {ChatViewProvider} from './ChatViewProvider';

@injectable()
export class FileProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private lastFileOpenedState = false;
    constructor(
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider
    ) {
        this.recordRecentFiles();
        this.listenWorkspaceStatus();
        this.initialCheckFilesAndWorkspaces();
    }
    // 在插件载入时检查工作区和文件的状态
    private async initialCheckFilesAndWorkspaces() {
        const workspaceOpened = vscode.workspace.workspaceFolders !== undefined;
        const fileOpened = vscode.window.activeTextEditor !== undefined;
        this.chatViewProvider.sendDataToWebview(EventMessage.WorkspaceStatusEvent, {workspaceOpened, fileOpened});
    }
    private async recordRecentFiles() {
        const context = await getExtensionContextAsync();
        const folders = vscode.workspace.workspaceFolders;
        if (!folders || folders.length === 0) {
            return;
        }
        const disposable = vscode.window.onDidChangeActiveTextEditor((editor?: vscode.TextEditor) => {
            const recent = context.globalState.get(RECENT_FILES_STORAGE_KEY, {}) as Record<string, string[]>;
            if (editor) {
                const workspace = vscode.workspace.getWorkspaceFolder(editor.document.uri);
                if (workspace) {
                    const repo = workspace.uri.fsPath;
                    const repoFileName = workspace.uri.fsPath.slice(repo.length + 1);
                    const updatedFiles = {
                        ...recent,
                        [repo]: recent?.[repo] ? uniq([repoFileName, ...recent?.[repo]]).slice(0, 20) : [repoFileName],
                    };
                    context.globalState.update(RECENT_FILES_STORAGE_KEY, updatedFiles);
                }
            }
        });
        this.disposables.push(disposable);
    }
    private async listenWorkspaceStatus() {
        const folders = vscode.workspace.workspaceFolders;
        if (!folders || folders.length === 0) {
            return;
        }
        const textEditorsDisposable = vscode.window.onDidChangeVisibleTextEditors(editors => {
            const fileEditors = editors.filter(editor => editor.document.uri.scheme === 'file');
            const fileOpened = fileEditors.length !== 0;

            if (fileOpened !== this.lastFileOpenedState) {
                this.lastFileOpenedState = fileOpened;
                this.chatViewProvider.sendDataToWebview(EventMessage.WorkspaceStatusEvent, {
                    fileOpened: fileOpened,
                    workspaceOpened: true,
                });
            }
        });
        const workspaceChangeDisposable = vscode.workspace.onDidChangeWorkspaceFolders(() => {
            this.chatViewProvider.sendDataToWebview(EventMessage.WorkspaceStatusEvent, {workspaceOpened: true});
        });
        this.disposables.push(textEditorsDisposable, workspaceChangeDisposable);
    }
    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
