import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import type {
    EDITION,
    ExtensionConfig,
    KernelConfig,
    PassthroughMessageParams,
    ResponseBase,
} from '@comate/kernel-shared';
import {
    KERNEL_CONFIG_FETCH_REQUEST,
    KERNEL_EXTENSION_CONFIG_FETCH_REQUEST,
    KERNEL_PASSTHROUGH_MESSAGE_TO_IDE_REQUEST,
    IDE_NAME,
    ENVIRONMENT,
    IDE_PASSTHROUGH_MESSAGE_TO_KERNEL_REQUEST,
    IDE_PASSTHROUGH_MESSAGE_TO_WEBVIEW_REQUEST,
} from '@comate/kernel-shared';
import {
    LanguageClient,
    LanguageClientOptions,
    ServerOptions,
    State,
} from 'vscode-languageclient/node';
import {
    ACTION_USER_DETAIL,
    ACTION_COMATE_LSP_WORKSPACE_FOLDERS,
    ACTION_UPDATE_ENGINE_CONFIG,
    ACTION_ENGINE_PROCESS_START_SUCCESS,
} from '@comate/plugin-shared-internals';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {log, LogLevel} from '@/common/outputChannel';
import {L10n} from '@/common/L10nProvider/L10n';
import {isInternal, isPoc, isSaaS} from '@/utils/features';
import {iocContainer} from '@/iocContainer';
import {getDeviceUUIDThrottled} from '@/utils/deviceUUID';
import {engineExec} from '@/utils/engineExec';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {LogCategory, LogUploaderProvider} from '../LogUploaderProvider';
import {CustomizeProvider} from '../CustomizeProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {registerOutputChannel} from '../outputChannelRegistry';
interface PluginsPid {
    [key: string]: number | undefined;
}

@injectable()
export class KernelProvider implements vscode.Disposable {
    client: LanguageClient | undefined;
    pluginsPid: PluginsPid = {};
    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(LogUploaderProvider) private readonly loggerProvider: LogUploaderProvider
    ) {
        vscode.workspace.onDidChangeConfiguration(async e => {
            if (e.affectsConfiguration('baidu.comate.privateService')) {
                this.updatePrivateService();
            }
        });
    }
    get chatViewProvider() {
        return iocContainer.get<ChatViewProvider>(ChatViewProvider);
    }

    async start() {
        if (
            isPoc
            && (!this.configProvider.getConfig<string>(ConfigKey.Key)
                || !this.configProvider.getPrivateService())
        ) {
            return;
        }
        const serverOptions: ServerOptions = () => engineExec(this.loggerProvider);
        const clientOptions: LanguageClientOptions = {
            documentSelector: [{scheme: 'file'}],
            synchronize: {
                // Notify the server about file changes to '.clientrc files contained in the workspace
                fileEvents: vscode.workspace.createFileSystemWatcher('**/.clientrc'),
            },
        };
        // Create the language client and start the client.
        this.client = new LanguageClient(
            'baiduComateKernel',
            'Baidu Comate Kernel',
            serverOptions,
            clientOptions,
            true
        );
        registerOutputChannel(this.client.outputChannel);
        // kernelProvider 的初始化相对独立，user_detail是 engine 必要信息，所以在这里处理
        this.client?.onRequest(ACTION_USER_DETAIL, async () => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_USER_DETAIL);
            const customizeProvider = iocContainer.get(CustomizeProvider);
            const isCustomizeUser = customizeProvider.isCustomizeUser();
            const customizeService = isCustomizeUser ? customizeProvider.getCustomizeServiceConfig() : undefined;
            const context = await getExtensionContextAsync();
            return {
                status: 'success',
                payload: {
                    name: this.configProvider.getConfig<string>(ConfigKey.Username),
                    license: isInternal ? '' : this.configProvider.getConfig<string>(ConfigKey.Key),
                    ideVersion: vscode.version,
                    platform: $features.PLATFORM,
                    version: context.extension.packageJSON.version,
                    language: L10n.currentLanguage,
                    customizeService,
                    device: isInternal ? '' : await getDeviceUUIDThrottled(),
                },
            };
        });
        this.client.onDidChangeState(event => {
            if (event.newState === State.Stopped) {
                this.loggerProvider.logUserAction({
                    category: LogCategory.onDidChangeState,
                    source: 'client',
                    content: `COMATE PLUSE CLIENT newState:${event.newState} oldState:${event.oldState}`,
                });
            }
        });

        this.client.onRequest(ACTION_COMATE_LSP_WORKSPACE_FOLDERS, () => {
            log(LogLevel.Debug, 'COMATE PLUSE CLIENT REC <--------', ACTION_COMATE_LSP_WORKSPACE_FOLDERS);
            return vscode.workspace.workspaceFolders?.map(v => ({uri: v.uri.toString(), name: v.name})) ?? [];
        });

        this.client.onNotification(ACTION_ENGINE_PROCESS_START_SUCCESS, () => {
            this.loggerProvider.logUserAction({
                category: LogCategory.EngineInnerInit,
                content: 'IDE channel initialized',
            });
        });

        this.client.onRequest(KERNEL_CONFIG_FETCH_REQUEST, async (): Promise<ResponseBase<KernelConfig>> => {
            const context = await getExtensionContextAsync();
            const config: KernelConfig = {
                /** IDE 应用本身所在的目录 */
                appRoot: vscode.workspace.workspaceFolders?.[0].uri?.fsPath ?? '',
                /** IDE 模式 */
                appHost: 'desktop',
                /** IDE 使用的语言 IETF 标准 */
                language: L10n.currentLanguage ?? 'zh',
                /** 设备 ID */
                deviceId: await getDeviceUUIDThrottled() ?? '',
                /** IDE 名称 */
                ideName: IDE_NAME.VSCode,
                ideSeries: vscode.env.appName.toLowerCase(),
                /** IDE 版本 */
                ideVersion: vscode.version,
                /** 执行环境 */
                environment: process.env.NODE_ENV === 'development' ? ENVIRONMENT.DEVELOPMENT : ENVIRONMENT.PRODUCTION,
                /** Comate 版本 */
                extensionVersion: context.extension.packageJSON.version,
                /** 厂内外等版本标识，同 platform */
                edition: $features.PLATFORM as EDITION,
                /** 主题色 */
                themeColor: vscode.window.activeColorTheme.kind === vscode.ColorThemeKind.Dark ? 'dark' : 'light',
            };
            log(LogLevel.Debug, 'KERNEL CLIENT REC <--------', KERNEL_CONFIG_FETCH_REQUEST);
            return {
                code: 0,
                message: 'success',
                data: config,
            };
        });

        this.client.onRequest(KERNEL_EXTENSION_CONFIG_FETCH_REQUEST, (): ResponseBase<ExtensionConfig> => {
            log(LogLevel.Debug, 'KERNEL CLIENT REC <--------', KERNEL_EXTENSION_CONFIG_FETCH_REQUEST);
            return {
                code: 0,
                message: 'success',
                data: this.configProvider.dumpConfig(),
            };
        });

        this.client.onRequest(
            KERNEL_PASSTHROUGH_MESSAGE_TO_IDE_REQUEST,
            (params: PassthroughMessageParams<any>) => {
                this.chatViewProvider.sendDataToWebview(IDE_PASSTHROUGH_MESSAGE_TO_WEBVIEW_REQUEST, params);
            }
        );

        this.client.onDidChangeState(event => {
            const states = {
                [State.Running]: 'Running',
                [State.Starting]: 'Starting',
                [State.Stopped]: 'Stopped',
            };

            log(
                LogLevel.Info,
                `Language Server State Changed - From: ${states[event.oldState]} To: ${states[event.newState]}`
            );

            // 如果进入 Running 状态，说明启动完成
            if (event.newState === State.Running) {
                log(LogLevel.Info, '✅ Language Server started successfully');
                this.loggerProvider.logUserAction({
                    category: LogCategory.onDidChangeState,
                    source: 'client',
                    content: `Language Server Started - ${new Date().toLocaleString()}`,
                });
            }
        });

        this.client?.onRequest('PLUGINS_PID_CHANGE_MESSAGE', (res: any) => {
            if (res.data) {
                this.pluginsPid[res.data.name] = res.data.pid;
            }
        });

        this.client.start();
    }

    async sendRequest<T>(method: string, params: any): Promise<T> {
        log(LogLevel.Debug, 'COMATE PLUSE CLIENT send -------->', method, JSON.stringify(params));
        return this.client!.sendRequest(method, params);
    }

    updatePrivateService() {
        const apiHost = this.configProvider.getPrivateService();
        if (apiHost && isSaaS) {
            this.client?.sendRequest(ACTION_UPDATE_ENGINE_CONFIG, {apiHost});
        }
    }

    ptSend(params: PassthroughMessageParams<unknown>) {
        this.client?.sendRequest(IDE_PASSTHROUGH_MESSAGE_TO_KERNEL_REQUEST, params);
        // 返回一个成功，表示收到了，并不是该条消息的response
        return Promise.resolve();
    }

    dispose() {
        this.client?.stop();
    }
}
