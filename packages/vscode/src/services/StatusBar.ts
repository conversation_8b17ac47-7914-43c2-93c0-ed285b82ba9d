/**
 * @file 底部状态栏
 */

import pDebounce from 'p-debounce';
import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import 'reflect-metadata';
import {iocContainer} from '@/iocContainer';
import {isPoc, isSaaS} from '@/utils/features';
import {L10n} from '@/common/L10nProvider/L10n';
import {StatusBarText} from '@/common/L10nProvider/constants';
import {loginByPassport} from '@/utils/login';
import {SWAN_APP_NAME} from '../constants';
import {ConfigKey, VSCodeConfigProvider} from './ConfigProvider';
const CMDToggleStatusBar = 'baidu.comate.toggleStatusBar';
export const CMDUpdateStatusBar = 'baidu.comate.updateStatusBar';

const getNormalLogoText = (enabled: boolean | string) => {
    const title = L10n.t(StatusBarText.TITLE);
    if (vscode.env.appName === SWAN_APP_NAME) {
        return enabled ? `$(comate-logo) ${title}` : '$(comate-disabled)';
    }
    return enabled ? `$(comate-logo) ${title}` : '$(comate-logo-disabled)';
};

@injectable()
export class ComateStatusBar implements vscode.Disposable {
    private enabled: string | boolean = true;
    private status: string = 'Normal';
    private readonly item: vscode.StatusBarItem;

    private readonly disabledColor: vscode.ThemeColor = new vscode.ThemeColor('statusBarItem.warningBackground');
    private readonly delayedUpdateDisplay: () => Promise<void>;

    private disposables: vscode.Disposable[] = [];

    constructor(@inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider) {
        this.delayedUpdateDisplay = pDebounce(() => {
            this.updateDisplay();
        }, 100);
        this.item = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 0);
        this.item.show();
        this.disposables.push(
            this.item,
            vscode.window.onDidChangeActiveTextEditor(() => {
                this.updateStatusBarIndicator();
            }),
            vscode.workspace.onDidCloseTextDocument(() => {
                this.updateStatusBarIndicator();
            }),
            vscode.workspace.onDidOpenTextDocument(() => {
                this.updateStatusBarIndicator();
            }),
            vscode.commands.registerCommand(CMDToggleStatusBar, () => {
                this.toggleStatusBar();
            }),
            vscode.commands.registerCommand(
                CMDUpdateStatusBar,
                (enabled: boolean) => this.updateEnableStatus(enabled)
            ),
            vscode.workspace.onDidChangeConfiguration(e => {
                if (isPoc && e.affectsConfiguration('baidu.comate.enableInlineSuggestion')) {
                    this.updateEnableInlineSuggestionStatus();
                }
            })
        );
        this.prepareConfig();
    }

    /**
     * 用户手动手动更新配置后会打开文档会触发 onDidChangeActiveTextEditor 事件，如果是插件里边更新的话
     * 需要调用这个方法更新状态
     */
    updateEnableStatus(enable: boolean) {
        this.enabled = enable;
        this.updateDisplay();
    }

    async prepareConfig() {
        this.setProgress();
        await this.configProvider.initSuffixMap();
        this.removeProgress();
        this.updateStatusBarIndicator();
    }

    updateStatusBarIndicator() {
        this.enabled = this.checkEnabledForLanguage();
        this.updateDisplay();
    }

    checkEnabledForLanguage() {
        const inlineSuggestionEnabled = this.configProvider.getConfig<boolean>(
            ConfigKey.EnableInlineSuggestion,
            true
        );
        if (!inlineSuggestionEnabled) {
            return false; // 当关闭内联建议时，始终返回禁用状态
        }
        else {
            if (!vscode.window.activeTextEditor) {
                return true;
            }
            const languageId = this.configProvider.getLanguageIdByFilePath(
                vscode.window.activeTextEditor.document.uri.fsPath
            );
            if (!languageId) {
                return false;
            }
            return this.configProvider.getEnabledConfig(languageId);
        }
    }

    getTooltip() {
        if (!vscode.window.activeTextEditor) {
            return 'Comate';
        }
        const languageId = this.configProvider.getLanguageIdByFilePath(
            vscode.window.activeTextEditor.document.uri.fsPath
        );
        if (!languageId) {
            return 'Comate';
        }
        return languageId === 'others'
            ? L10n.t(StatusBarText.NOT_SUPPORT)
            : (this.enabled ? 'Comate' : `${L10n.t(StatusBarText.ENABLE)} Comate`);
    }

    async updateDisplay() {
        const noSuggestionMessage = L10n.t(StatusBarText.NO_SUGGESTIONS);
        const title = L10n.t(StatusBarText.TITLE);
        switch (this.status) {
            case 'InProgress':
                this.item.text = `$(loading~spin) ${title}`;
                break;
            case 'Normal':
                this.item.text = getNormalLogoText(this.enabled);
                this.item.command = CMDToggleStatusBar;
                this.item.tooltip = this.getTooltip();
                this.item.backgroundColor = this.enabled ? undefined : this.disabledColor;
                break;
            case 'NoSuggestions':
                this.item.text = `$(comate-logo) ${noSuggestionMessage}`;
        }
    }

    getStatusBarItem() {
        return this.item;
    }

    setProgress() {
        if (this.status === 'Normal' || this.status === 'NoSuggestions') {
            this.status = 'InProgress';
            this.delayedUpdateDisplay();
        }
    }

    removeProgress() {
        if (this.status === 'InProgress' || this.status === 'NoSuggestions') {
            this.status = 'Normal';
            this.delayedUpdateDisplay();
        }
    }

    forceNormal() {
        this.status = 'Normal';
        this.delayedUpdateDisplay();
    }

    setNoSuggestions() {
        if (this.status === 'InProgress' || this.status === 'Normal') {
            this.status = 'NoSuggestions';
            this.delayedUpdateDisplay();
        }
    }

    get text() {
        return this.item.text;
    }

    setText(text: string, tooltip?: string) {
        const previousLogo = /^\$\(([^)]+)\)/.exec(this.item.text);
        const nextLogo = /^\$\(([^)]+)\)/.exec(text);
        this.item.text = !nextLogo && previousLogo ? `${previousLogo[0]} ${text}` : text;
        if (tooltip) {
            this.item.tooltip = tooltip;
        }
    }

    async toggleStatusBar() {
        if (isSaaS) {
            const license = this.configProvider.getLicense();
            if (!license) {
                loginByPassport(iocContainer);
                return;
            }
        }
        const configProvider = this.configProvider;
        const path = vscode.window.activeTextEditor?.document.uri.fsPath;
        const languageId = path && this.configProvider.getLanguageIdByFilePath(path);
        const allLanguageConfig = this.configProvider.getConfig<Record<string, boolean>>(ConfigKey.LangSuggestion, {});
        if (
            !languageId
            || languageId === 'others'
            || !(languageId in allLanguageConfig)
        ) {
            vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: 'statusBar'});
            return;
        }
        // 如果是全局开关关掉了，在这里打开，全局开关是个独立配置，不影响语言。可能会出现全局开关打开之后，该语言还是禁用的情况
        if (!this.configProvider.getConfig<boolean>(ConfigKey.EnableInlineSuggestion)) {
            configProvider.updateEnableInlineSuggestion(true);
        }
        else if (this.configProvider.getEnabledConfig(languageId)) {
            vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: 'statusBar'});
        }
        else {
            configProvider.updateEnabledConfig(languageId, true).then(() => {
                this.enabled = true;
                this.updateDisplay();
            });
        }
    }

    updateEnableInlineSuggestionStatus() {
        const inlineSuggestionEnabled = this.configProvider.getConfig<boolean>(
            ConfigKey.EnableInlineSuggestion,
            true
        );
        if (inlineSuggestionEnabled) {
            // 重新检查基于语言的启用状态
            this.updateStatusBarIndicator();
        }
        else {
            this.enabled = false;
            this.updateDisplay();
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
