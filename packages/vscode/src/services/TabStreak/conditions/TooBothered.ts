import {
    DocumentUri,
    DidChangeTextDocumentParams,
    Disposable,
    TextDocumentContentChangeEvent,
} from 'vscode-languageserver-protocol';
import {URI} from 'vscode-uri';
import {IWorkspaceProvider} from '@/common/lsp/types';
import {EscapedRewrite, ICondition} from '../types';

type LineRange = Pick<EscapedRewrite, 'startLine' | 'endLine'>;

function getOverlappingRange(a: LineRange, b: LineRange) {
    const largerStart = Math.max(a.startLine, b.startLine);
    const smallerEnd = Math.min(a.endLine, b.endLine);

    if (largerStart > smallerEnd) {
        // 没有重叠区域
        return null;
    }

    return {
        startLine: largerStart,
        endLine: smallerEnd,
    };
}

function getMergedLineRange(a: LineRange, b: LineRange) {
    const smallerStart = Math.min(a.startLine, b.startLine);
    const largerEnd = Math.max(a.endLine, b.endLine);
    const overlap = getOverlappingRange(a, b);

    if (!overlap) {
        // 没有重叠区域，不合并
        return null;
    }

    return {
        startLine: smallerStart,
        endLine: largerEnd,
    };
}

export class TooBotheredHeuristic implements ICondition, Disposable {
    private readonly disposables: Disposable[] = [];
    // Fixed size with only 2 items
    private readonly escapedRewrites: EscapedRewrite[] = [];

    constructor(workspaceProvider: IWorkspaceProvider) {
        this.disposables.push(workspaceProvider.onDidChangeTextDocument(this.handleTextDocumentChange.bind(this)));
    }

    addEscapedRewrite(item: EscapedRewrite) {
        // if (item.shownDuration < 500) {
        //     // 如果 shown 时长小于500ms，认为用户没有仔细看这个提示，则不记录。
        //     return;
        // }
        if (this.escapedRewrites.length === 2) {
            this.escapedRewrites.shift();
        }
        this.escapedRewrites.push(item);
    }

    match(uri: DocumentUri, rewriteStartLine: number, rewriteEndLine: number) {
        if (this.escapedRewrites.length < 2) {
            return false;
        }
        const [first, second] = this.escapedRewrites;
        if (first.uri !== second.uri || second.uri !== uri) {
            return false;
        }
        const merged = getMergedLineRange(first, second);
        if (!merged) {
            return false;
        }
        const overlap = getOverlappingRange(
            {startLine: rewriteStartLine, endLine: rewriteEndLine},
            merged
        );
        return Boolean(overlap);
    }

    private handleTextDocumentChange(event: DidChangeTextDocumentParams) {
        if (!event.contentChanges.length) {
            return;
        }
        const lastEscapedRewrite = this.escapedRewrites.at(-1);
        if (!lastEscapedRewrite) {
            return;
        }
        if (URI.parse(lastEscapedRewrite.uri).fsPath !== event.textDocument.uri) {
            // 当最近编辑文件和放弃的改写文件不一致时，重置纪录。
            this.escapedRewrites.length = 0;
            return;
        }
        for (const change of event.contentChanges) {
            if (TextDocumentContentChangeEvent.isIncremental(change)) {
                const changeLineRange = {startLine: change.range.start.line, endLine: change.range.end.line};
                const overlap = getOverlappingRange(changeLineRange, lastEscapedRewrite);
                if (!overlap) {
                    // 当变更的范围和放弃的改写没有重叠范围时，重置纪录。
                    this.escapedRewrites.length = 0;
                    break;
                }
            }
        }
    }

    dispose(): void {
        this.disposables.forEach(d => d.dispose());
        this.disposables.length = 0;
        this.escapedRewrites.length = 0;
    }
}
