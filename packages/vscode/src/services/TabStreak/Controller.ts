import * as vscode from 'vscode';
import {CancellationToken, Position, Range} from 'vscode-languageclient';
import {noop} from 'lodash';
import {URI} from 'vscode-uri';
import {LRUCache} from 'vscode-languageserver-protocol';
import crypto from 'crypto-js';
import {VSCodeTextDocument} from '@/common/lsp/adapters/VSCodeTextDocument';
import {adaptToVSCodeRange} from '@/common/lsp/adapters/utils';
import {createEndOfLineWhitespace, createTabToJumpGuide} from '@/common/decorations';
import {TriggerSource} from '@/api/smartTab';
import {ITextDocument} from '@/common/lsp/types';
import {acceptCode, showCode} from '@/api';
import captureActualCodeAfterGeneration from '@/utils/captureActualCodeAfterGeneration';
import {isInRange, isWithinRange} from '@/utils/lsp';
import consoleLogger from '@/common/consoleLogger';
import {WithRequired} from '@/utils/typeUtils';
import {
    BlockRewrite,
    CursorPrediction,
    EditContext,
    IBlockRewriteProvider,
    ICursorPredictionProvider,
} from '../EditPredictionProvider';
import {IDiffViewer} from '../DiffViewer/types';
import {AgumentedDiagnosticInfo, ProgrammingAction} from '../ProgrammingContextTracker/types';
import {adjustRewriteRange} from '../EditPredictionProvider/utils';
import {EscapedRewrite, ICondition} from './types';

interface ExtendedContext extends EditContext {
    refreshContext: (uri?: URI, cursor?: Position) => Promise<ProgrammingAction[]>;
}

interface TabAction {
    accept(): void;
    reject(): void;
}

export class TabStreakController implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private readonly tabActions: TabAction[] = [];
    private internalCancelTokenSource: vscode.CancellationTokenSource | null = null;
    /** cursor 可以自由划选的区域，不会退出 tab streak 模式 */
    private cursorFreeZone: Range | null = null; // TODO: @tianzerun 这里应该也记录一下文件 uri
    /** 是否豁免光标移动/文件变更，true 时不会退出 tab streak 模式 */
    private isPermissive = false;
    private cooldownTimer: NodeJS.Timeout | null = null;
    /** 当前是否有待采纳的预测 */
    private _isDeciding: boolean = false;
    /** 缓存改写结果，避免重复请求同一个范围的改写 */
    private readonly rewriteCache = new LRUCache<string, BlockRewrite>(50);
    /** 已被取消的改写，避免用户取消了又展示一遍，key 为 cache key */
    private readonly rewriteEscaped = new LRUCache<string, boolean>(50);
    private readonly _onDidAcceptRewrite = new vscode.EventEmitter<void>();
    readonly onDidAcceptRewrite = this._onDidAcceptRewrite.event;
    private readonly _onDidEscapeRewrite = new vscode.EventEmitter<EscapedRewrite>();
    readonly onDidEscapeRewrite = this._onDidEscapeRewrite.event;
    /** 跳过本次改写的条件，满足任何一个条件即可 */
    private readonly skipRewriteConditions: ICondition[] = [];

    constructor(
        private readonly diffViewer: IDiffViewer,
        private readonly similarLinePrediction: ICursorPredictionProvider,
        private readonly llmBlockRewrite: IBlockRewriteProvider
    ) {
        this.disposables.push(
            vscode.commands.registerCommand('baidu.comate.tabStreak.tab', this.tab.bind(this)),
            vscode.commands.registerCommand('baidu.comate.tabStreak.escape', this.escape.bind(this)),
            vscode.window.onDidChangeTextEditorSelection(this.handleTextEditorSelectionChange.bind(this)),
            vscode.workspace.onDidChangeTextDocument(this.handleTextDocumentChange.bind(this)),
            vscode.window.onDidChangeActiveTextEditor(this.handleActiveTextEditorChange.bind(this))
        );
    }

    get isDeciding() {
        return this._isDeciding;
    }

    async run(editor: vscode.TextEditor, cursor: vscode.Position, context: ExtendedContext, token?: CancellationToken) {
        // clean up before predicting
        this.escape();
        const document = new VSCodeTextDocument(editor.document);
        this.predictSimilarLineEdit(editor, document, cursor, context, token);
        token?.onCancellationRequested(() => {
            this.escape();
        });
    }

    async runDiagnosticFix(
        editor: vscode.TextEditor,
        position: vscode.Position,
        context: WithRequired<ExtendedContext, 'triggerSource'>,
        token?: CancellationToken
    ) {
        this.escape();
        token?.onCancellationRequested(() => {
            this.escape();
        });
        const internalCancelToken = this.internalCancelTokenSource?.token;
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const diagnostics = context.programmingContext.filter(item =>
            item.name === 'diagnostic_info'
        ) as AgumentedDiagnosticInfo[];
        if (diagnostics.length <= 0) {
            return;
        }
        const vscDocument = new VSCodeTextDocument(editor.document);
        const diagnosticStart = Math.min(
            ...diagnostics.map(item => item.raw.range.start.line),
            Math.max(position.line - 1, 0)
        );
        const diagnosticEnd = Math.max(
            ...diagnostics.map(item => item.raw.range.end.line),
            Math.min(position.line + 2, editor.document.lineCount - 1)
        );
        const [rewriteStart, rewriteEnd] = adjustRewriteRange(
            editor.document,
            position,
            diagnosticStart,
            diagnosticEnd
        );
        if (this.shouldSkipRewrite(editor.document.uri, rewriteStart, rewriteEnd)) {
            consoleLogger.debug('tab streak: skip rewrite');
            return;
        }
        const cacheKey = this.computeRewriteCacheKey(vscDocument, rewriteStart, rewriteEnd);
        if (this.rewriteEscaped.get(cacheKey)) {
            consoleLogger.debug('tab streak: skip rewrite because it was canceled before.');
            // 曾经在同一个触发位置取消过改写，则不再展示
            return;
        }
        this.cursorFreeZone = {
            start: {line: rewriteStart, character: 0},
            end: {line: rewriteEnd, character: Number.MAX_SAFE_INTEGER},
        };
        let rewrite = this.rewriteCache.get(cacheKey);
        if (!rewrite) {
            rewrite = await this.llmBlockRewrite.provideRewrite(
                vscDocument,
                rewriteStart,
                rewriteEnd,
                context,
                token
            );
            rewrite && this.rewriteCache.set(cacheKey, rewrite);
        }
        if (!rewrite || token?.isCancellationRequested || internalCancelToken?.isCancellationRequested) {
            this.cursorFreeZone = null;
            return;
        }
        const rewriteHandler = await this.showRewrite(editor, rewrite);
        if (!rewriteHandler) {
            this.cursorFreeZone = null;
            return;
        }
        this.tabActions.push({
            accept: () => {
                rewriteHandler.accept();
            },
            reject: () => {
                this.rewriteEscaped.set(cacheKey, true);
                rewriteHandler.reject();
            },
        });
    }

    async showInlineEdit(editor: vscode.TextEditor, rewrite: BlockRewrite) {
        this.escape();
        const rewriteHandler = await this.showRewrite(editor, rewrite);
        if (!rewriteHandler) {
            return;
        }
        this.tabActions.push(rewriteHandler);
    }

    /**
     * 检查给定的位置是否有正在触发的改写。
     *
     * @param cursor - 当前光标的位置。
     * @returns true 表示 cursor 附近有正在触发的改写，false 表示没有。
     */
    isEnclosedByPendingRewrite(cursor: vscode.Position) {
        if (!this.cursorFreeZone) {
            return false;
        }
        return isInRange(cursor, this.cursorFreeZone);
    }

    addSkipRewriteCondition(heuristic: ICondition) {
        this.skipRewriteConditions.push(heuristic);
    }

    private async predictSimilarLineEdit(
        editor: vscode.TextEditor,
        document: ITextDocument,
        position: vscode.Position,
        context: ExtendedContext,
        token?: CancellationToken
    ) {
        // NOTE: 因为连续触发时还是基于初始触发位置，预测的位置可能会重复，所以最好加一个判断要求预测位置大于**当前**光标位置。
        // 同一位置反复改写是不合理的效果，因此要求预测的行号必须要大于当前光标位置。
        // 后期交给模型来做预测就不管了 @tianzerun。
        const internalCancelToken = this.internalCancelTokenSource?.token;
        const cursor = editor.selection.active;
        const nextEditLocation = await this.similarLinePrediction.provideCursorPrediction(
            document,
            position,
            cursor.line + 1,
            position.line + 20,
            context,
            token
        );
        if (token?.isCancellationRequested || internalCancelToken?.isCancellationRequested) {
            return;
        }
        if (nextEditLocation && nextEditLocation.position.line > cursor.line) {
            this.enabledTabToAccept();
            const hideJumpGuide = this.showJumpGuide(editor, nextEditLocation);
            const showStartTime = Date.now();
            this._isDeciding = true;
            this.cursorFreeZone = document.lineAt(nextEditLocation.position.line).range;
            const [rewriteStart, rewriteEnd] = adjustRewriteRange(
                document,
                nextEditLocation.position,
                Math.max(0, nextEditLocation.position.line - 1),
                Math.min(nextEditLocation.position.line + 2, document.lineCount - 1)
            );
            // 在有预测位置的同时直接触发改写，不用等用户tab确认
            const rewritePromise = this.llmBlockRewrite.provideRewrite(
                document,
                rewriteStart,
                rewriteEnd,
                {
                    ...context,
                    triggerSource: TriggerSource.SimilarCodeRewrite,
                }
            );
            const reportShown = (shownEndTime: number) => {
                nextEditLocation.uuidPromise.then(uuid => {
                    const shownMilliSeconds = shownEndTime - showStartTime;
                    showCode(uuid, {shownMilliSeconds});
                });
            };
            const tabToJumpAction: TabAction = {
                accept: async () => {
                    reportShown(Date.now());
                    this._isDeciding = false;
                    this.cooldown();
                    this.disableTabToAccept();
                    hideJumpGuide();
                    nextEditLocation.uuidPromise.then(uuid => {
                        acceptCode({
                            uuid,
                            accepted: true,
                            content: '',
                            generatedContent: String(nextEditLocation.position.line + 1),
                        });
                    });
                    const cursor = editor.selection.active;
                    const lineOffset = nextEditLocation.position.line - cursor.line;
                    if (lineOffset !== 0) {
                        const direction = lineOffset > 0 ? 'down' : 'up';
                        await vscode.commands.executeCommand('cursorMove', {
                            to: direction,
                            by: 'line',
                            value: Math.abs(lineOffset),
                        });
                    }
                    vscode.commands.executeCommand('cursorMove', {
                        to: 'wrappedLineFirstNonWhitespaceCharacter',
                    });
                    const rewrite = await rewritePromise;
                    if (!rewrite) {
                        return;
                    }
                    if (
                        token?.isCancellationRequested
                        || internalCancelToken?.isCancellationRequested
                    ) {
                        return;
                    }
                    const uri = vscode.Uri.parse(nextEditLocation.uri);
                    const rewriteHandler = await this.showRewrite(editor, rewrite);
                    if (!rewriteHandler) {
                        return;
                    }
                    const tabToRewriteAction: TabAction = {
                        accept: async () => {
                            await rewriteHandler.accept();
                            const programmingContext = await context.refreshContext(uri, editor.selection.active);
                            this.predictSimilarLineEdit(
                                editor,
                                document,
                                position,
                                {...context, programmingContext},
                                token
                            );
                        },
                        reject: () => {
                            rewriteHandler.reject();
                        },
                    };
                    this.tabActions.push(tabToRewriteAction);
                },
                reject: () => {
                    reportShown(Date.now());
                    this._isDeciding = false;
                    hideJumpGuide();
                },
            };
            this.tabActions.push(tabToJumpAction);
        }
    }

    private async showRewrite(editor: vscode.TextEditor, rewrite: BlockRewrite) {
        const uri = editor.document.uri;
        if (vscode.window.activeTextEditor?.document.uri.toString() !== uri.toString()) {
            // Not the active editor already, do not show the diff.
            return;
        }
        let hideDiff = noop;
        this.enabledTabToAccept();
        let shownStartTime = Date.now();
        try {
            const handler = await this.diffViewer.showDiff(
                editor,
                rewrite.range.start.line,
                rewrite.range.end.line,
                rewrite.content
            );
            shownStartTime = Date.now();
            this._isDeciding = true;
            hideDiff = handler.hide;
        }
        catch (e) {
            this._isDeciding = false;
            this.disableTabToAccept();
            return;
        }
        const reportShown = (duration: number) => {
            rewrite.uuidPromise.then(uuid => {
                showCode(uuid, {shownMilliSeconds: duration});
                captureActualCodeAfterGeneration(uri, uuid, 1);
            });
        };
        return {
            accept: async () => {
                const shownDuration = Date.now() - shownStartTime;
                reportShown(shownDuration);
                this._isDeciding = false;
                this.cooldown();
                this.disableTabToAccept();
                hideDiff();
                rewrite.uuidPromise.then(uuid => {
                    acceptCode({
                        uuid,
                        accepted: true,
                        content: '',
                    });
                });
                const edit = new vscode.WorkspaceEdit();
                edit.replace(
                    uri,
                    adaptToVSCodeRange(rewrite.range),
                    rewrite.content
                );
                const applied = await vscode.workspace.applyEdit(edit);
                if (!applied) {
                    return;
                }
                // NOTE: 这个移动是为了避免触发续写，略微 hack 了一些，但完美实现想要的效果了。
                vscode.commands.executeCommand('cursorMove', {
                    to: 'wrappedLineEnd',
                });
                this._onDidAcceptRewrite.fire();
            },
            reject: () => {
                const shownDuration = Date.now() - shownStartTime;
                reportShown(shownDuration);
                this._onDidEscapeRewrite.fire({
                    uri: uri.toString(),
                    startLine: rewrite.range.start.line,
                    endLine: rewrite.range.end.line,
                    shownDuration,
                });
                this._isDeciding = false;
                hideDiff();
            },
        };
    }

    private tab() {
        if (this.tabActions.length) {
            const action = this.tabActions.shift();
            action?.accept();
        }
        else {
            this.escape();
        }
    }

    private escape() {
        this.disableTabToAccept();
        this.cursorFreeZone = null;
        this.internalCancelTokenSource?.cancel();
        this.internalCancelTokenSource = new vscode.CancellationTokenSource();
        if (this.tabActions.length) {
            for (const action of this.tabActions) {
                action.reject();
            }
            this.tabActions.length = 0;
        }
    }

    private showJumpGuide(editor: vscode.TextEditor, nextEditLocation: CursorPrediction) {
        const whitespace = createEndOfLineWhitespace();
        const tabToJump = createTabToJumpGuide();
        const line = editor.document.lineAt(nextEditLocation.position.line);
        editor.setDecorations(whitespace, [new vscode.Range(line.range.end, line.range.end)]);
        editor.setDecorations(tabToJump, [new vscode.Range(line.range.end, line.range.end)]);
        return () => {
            whitespace.dispose();
            tabToJump.dispose();
        };
    }

    private handleTextEditorSelectionChange(e: vscode.TextEditorSelectionChangeEvent) {
        const document = e.textEditor.document;
        if (document.uri.scheme !== 'file') {
            return;
        }
        if (this.isPermissive) {
            return;
        }
        if (this.cursorFreeZone) {
            const inFreeZone = isWithinRange(this.cursorFreeZone, e.textEditor.selection);
            if (inFreeZone) {
                return;
            }
        }
        this.escape();
    }

    private handleActiveTextEditorChange() {
        if (this.cursorFreeZone) {
            this.escape();
            consoleLogger.debug('tab streak: escape because of active text editor change');
        }
    }

    private handleTextDocumentChange(e: vscode.TextDocumentChangeEvent) {
        if (e.document.uri.scheme !== 'file') {
            return;
        }
        if (this.isPermissive) {
            return;
        }
        if (e.contentChanges.length === 0) {
            return;
        }
        this.escape();
    }

    private enabledTabToAccept() {
        vscode.commands.executeCommand('setContext', 'baidu.comate.context.tabStreak', true);
    }

    private disableTabToAccept() {
        vscode.commands.executeCommand('setContext', 'baidu.comate.context.tabStreak', false);
    }

    private computeRewriteCacheKey(document: VSCodeTextDocument, rewriteStartLine: number, rewriteEndLine: number) {
        // eslint-disable-next-line new-cap
        return crypto.SHA256(`${document.getText()}-${rewriteStartLine}-${rewriteEndLine}`).toString();
    }

    private cooldown(duration: number = 100) {
        if (this.cooldownTimer) {
            clearTimeout(this.cooldownTimer);
        }
        this.isPermissive = true;
        this.cooldownTimer = setTimeout(() => {
            this.isPermissive = false;
            this.cooldownTimer = null;
        }, duration);
    }

    private shouldSkipRewrite(uri: vscode.Uri, rewriteStartLine: number, rewriteEndLine: number) {
        return this.skipRewriteConditions.some(cond => cond.match(uri.toString(), rewriteStartLine, rewriteEndLine));
    }

    dispose() {
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }
}
