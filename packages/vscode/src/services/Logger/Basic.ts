import os from 'node:os';
import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {UserService} from '../UserService';
import {ILogger, LogItem, LogMessageStatus, LogMessageType, LogParams, TelemtryEnvInfo} from './types';

@injectable()
export class BasicLogger implements ILogger {
    private disposables: vscode.Disposable[] = [];
    private envInfo: TelemtryEnvInfo = {
        machineId: '',
        version: '',
        ideVersion: '',
        osPlatform: '',
        osArch: '',
        osRelease: '',
    };
    private readonly logs: LogItem[] = [];
    private id = 1;

    constructor(@inject(UserService) private readonly userService: UserService) {
        this.initEnvInfo();
    }

    async initEnvInfo() {
        const context = await getExtensionContextAsync();
        this.envInfo = {
            machineId: vscode.env.machineId,
            version: context.extension.packageJSON.version,
            ideVersion: vscode.version,
            osPlatform: os.platform(),
            osArch: os.arch(),
            osRelease: os.release(),
        };
    }

    formatTime(time?: Date) {
        const date = (time && time instanceof Date) ? time : new Date();

        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const day = ('0' + date.getDate()).slice(-2);
        const hour = ('0' + date.getHours()).slice(-2);
        const minute = ('0' + date.getMinutes()).slice(-2);
        const second = ('0' + date.getSeconds()).slice(-2);

        const formattedDateTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
        return formattedDateTime;
    }

    async addLog(messageStatus: LogMessageStatus, messageType: LogMessageType, params: LogParams) {
        const {event, time, uuid, extra} = params;
        const [username] = await this.userService.getCurrentUser().catch(() => ['unknown']);
        this.logs.push({
            id: this.id,
            time: this.formatTime(time),
            username: username,
            event: event,
            uuid: uuid,
            status: messageStatus,
            type: messageType,
            extra: extra,
        });
        // 只保留最近的500条日志
        if (this.logs.length > 500) {
            this.logs.shift();
        }

        this.id = this.id + 1;
    }

    startDebugLog(params: LogParams) {
        this.addLog('START', 'DEBUG', params);
    }

    stopDebugLog(params: LogParams) {
        this.addLog('FINISHED', 'DEBUG', params);
    }

    triggerCancelLog(params: LogParams) {
        this.addLog('CANCELD', 'DEBUG', params);
    }

    triggerErrorLog(params: LogParams) {
        this.addLog('START', 'ERROR', params);
    }

    triggerInfoLog(params: LogParams) {
        this.addLog('LOADING', 'INFO', params);
    }

    reportLogHistoryByUuid(uuid?: string) {
        // 如果传入合法的 uuid，则返回该 uuid 及之前对应的 100 条日志
        // 如果 uuid 不合法或为空，则返回最后的 100 条日志
        // 如果不满 100 条历史日志，则返回全部
        let extractedLogs = this.logs.slice(-100);
        if (uuid && uuid !== '') {
            const index = this.logs.findIndex(log => log.uuid === uuid);
            if (index !== -1 && index - 100 >= 0) {
                extractedLogs = this.logs.slice(index - 100 + 1, index + 1);
            }
            else if (index !== -1) {
                extractedLogs = this.logs.slice(0, index + 1);
            }
        }
        const res = JSON.stringify(this.envInfo) + JSON.stringify(extractedLogs);
        vscode.env.clipboard.writeText(res);
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
