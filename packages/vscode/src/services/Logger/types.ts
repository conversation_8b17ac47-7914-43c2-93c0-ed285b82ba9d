import * as vscode from 'vscode';

export type LogMessageStatus =
    | 'START'
    | 'LOADING'
    | 'ERROR'
    | 'FINISHED'
    | 'CANCELD';

export type LogMessageType =
    | 'INFO'
    | 'WARN'
    | 'ERROR'
    | 'DEBUG';

export interface LogItem {
    id: number;
    time: string;
    username: string;
    event: any;
    uuid?: string;
    status: LogMessageStatus;
    type: LogMessageType;
    extra?: any;
}

export interface TelemtryEnvInfo {
    machineId: string;
    version: string;
    ideVersion: string;
    osPlatform: string;
    osArch: string;
    osRelease: string;
}

export interface LogParams {
    event: any;
    time?: Date;
    extra?: any;
    uuid?: string;
}

export interface ILogger extends vscode.Disposable {
    startDebugLog(params: LogParams): void;
    stopDebugLog(params: LogParams): void;
    triggerCancelLog(params: LogParams): void;
    triggerErrorLog(params: LogParams): void;
    triggerInfoLog(params: LogParams): void;
    reportLogHistoryByUuid(uuid?: string): void;
}
