/* eslint-disable @typescript-eslint/no-unused-vars */
import {injectable} from 'inversify';
import {ILogger, LogParams} from './types';

@injectable()
export class EmptyLogger implements ILogger {
    startDebugLog(_params: LogParams): void {
        // do nothing
    }

    stopDebugLog(_params: LogParams): void {
        // do nothing
    }

    triggerCancelLog(_params: LogParams): void {
        // do nothing
    }

    triggerErrorLog(_params: LogParams): void {
        // do nothing
    }

    triggerInfoLog(_params: LogParams): void {
        // do nothing
    }

    reportLogHistoryByUuid(_uuid: string): void {
        // do nothing
    }

    dispose() {
        // do nothing
    }
}
