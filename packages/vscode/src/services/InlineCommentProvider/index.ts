import path from 'node:path';
import * as vscode from 'vscode';
import axios from 'axios';
import {injectable, inject} from 'inversify';
import Parser from 'web-tree-sitter';
import {localPluginConfig, mergePluginConfig} from '@comate/plugin-shared-internals';
import {TYPES} from '@/inversify.config';
import {L10n, getGenerationFailureText} from '@/common/L10nProvider/L10n';
import {CommentProviderText} from '@/common/L10nProvider/constants';
import {CMD_ADD_COMMENT, SUFFIX_LANG_MAP} from '@/constants';
import {inlineCommentDefaultConfig as defaultConfig} from '../../utils/defaultConfig';
import {getCompleteFirstLine} from '../../utils/document';
import {stripExtraIndent} from '../../utils/indent';
import {GenerateCodeOptions, generateCode} from '../../api';
import {
    addMarkdownCodeBlock,
    extractMarkdownCodeBlocks,
    findLeadingNonWhitespaceIndex,
    stripMarkdownCodeBlock,
} from '../../utils/common';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {iocContainer} from '../../iocContainer';
import {UserService} from '../UserService';
import {buildParams, fetchAndStreamCode} from '../../common/Fetcher';
import {ChatViewProvider} from '../ChatViewProvider';
import {CodeLensDisplayMode, CodelensConfig, ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {DiffProvider} from '../DiffProvider';
import {Conversation} from '../ChatViewProvider/Conversation';
import {ITimeTracker} from '../TimeTracker/types';
import {inlineCommentDefaultConfig} from '../../utils/defaultConfig';
import {ILicenseController} from '../LicenseController/types';
import {CodeLensProvider} from '../FoldedCodeLensProvider';
import {ChatBaseProvider} from '../ChatBaseProvider';
import {TextResponse} from '../ChatViewProvider/TextResponse';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';
import {InlineChatProvider} from '../InlineChatProvider';

const MAX_INPUT_LENGTH = 9000;

export function filterInlineCommentFunctionNode(node: Parser.SyntaxNode) {
    if (node.hasError()) {
        return false;
    }
    const lineCount = node.endPosition.row - node.startPosition.row;
    return lineCount > 0 && node.text.length <= MAX_INPUT_LENGTH;
}

// 目前只考虑 # 和 // 两种
function getInlineCommentMarker(languageId: string) {
    if (languageId === 'python') {
        return '#';
    }
    else {
        return '//';
    }
}

function isInlineCommentEqual(a: string, b: string, marker: string) {
    const text1 = a.trim();
    const text2 = b.trim();
    if (text1.startsWith(marker) && text2.startsWith(marker)) {
        const content1 = text1.slice(marker.length);
        const content2 = text2.slice(marker.length);
        return content1.trim() === content2.trim();
    }
    return false;
}

/**
 * 过滤掉注释内容连着两行一模一样的行间注释
 * @param languageId document 的 languageId
 * @param lines 分行后的生成内容
 * @returns
 */
function filterConsecutiveInlineComments(languageId: string, lines: string[]) {
    const commentMarker = getInlineCommentMarker(languageId);
    return lines.filter((line, index) => {
        if (index === 0) {
            return true;
        }
        return !isInlineCommentEqual(lines[index - 1], line, commentMarker);
    });
}

export function buildReplacement(document: vscode.TextDocument, content: string, padding: string) {
    const lines = stripMarkdownCodeBlock(content, document.languageId).split('\n');
    const paddingLines = lines.map((line, index) => (line.trim() && index !== 0
        ? `${padding}${line}`
        : line)
    );
    return filterConsecutiveInlineComments(document.languageId, paddingLines).join('\n').trimEnd();
}

@injectable()
export class InlineCommentProvider extends ChatBaseProvider implements CodeLensProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'javascript',
        'typescript',
        'cpp',
        'c',
        'vue',
    ];
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(TYPES.ITimeTracker) private readonly timeTracker: ITimeTracker,
        @inject(TYPES.ILicenseController) private readonly licenseController: ILicenseController,
        @inject(PerformanceLogProvider) private readonly performanceLog: PerformanceLogProvider,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider,
        @inject(InlineChatProvider) private readonly inlineChatProvider: InlineChatProvider
    ) {
        super(diffProvider);
        this.disposables.push(
            vscode.languages.registerCodeLensProvider(
                InlineCommentProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this
            ),
            vscode.commands.registerCommand(
                CMD_ADD_COMMENT,
                async (document: vscode.TextDocument, range: vscode.Range) => {
                    const startTime = performance.now();
                    if (!L10n.isEnglish) {
                        this.handleInlineCommentCommand(document, range, startTime);
                    }
                    else {
                        this.handleInlineCommentFallback(document, range, startTime);
                    }
                }
            )
        );
    }

    async resolveFallback(
        document: vscode.TextDocument,
        range: vscode.Range,
        conversation: Conversation,
        functionContent: string,
        padding: string,
        startTime: number
    ) {
        const streamMode = this.configProvider.getConfig(ConfigKey.EnableStreamingSession);
        const reply = conversation.addResponse(streamMode ? 'stream' : 'text', '', 'inProgress', {
            regenerate: async () => {
                this.resolveFallback(document, range, conversation, functionContent, padding, startTime);
            },
            // 打开全文复制按钮
            copyAll: () => {},
        });
        const paramsRes = await buildParams(
            document,
            range.start,
            iocContainer.get(UserService)
        );
        if (paramsRes.type !== 'success') {
            reply.fail(getGenerationFailureText(paramsRes.reason));
            return {uuid: ''};
        }
        const params = {
            ...paramsRes.value,
            content: functionContent,
            model: 'ERNIE_BOT',
            function: 'CODE_INLINE_COMMENT',
        };
        try {
            if (streamMode) {
                return this.fetchAndStream(document, range, padding, reply, params, startTime);
            }
            else {
                return this.fetchWithParams(document, range, padding, reply, params);
            }
        }
        catch (e: any) {
            reply.fail(getGenerationFailureText(e.message));
            return {uuid: ''};
        }
    }
    async handleInlineCommentFallback(
        document: vscode.TextDocument,
        range: vscode.Range,
        startTime: number = performance.now()
    ) {
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_ADD_COMMENT});
        const firstLine = getCompleteFirstLine(document, range);
        const functionContent = stripExtraIndent(document.getText(range), true, firstLine);
        const conversation = this.chatViewProvider.createConversation(
            L10n.t(CommentProviderText.PROMPT),
            'inlineComment',
            addMarkdownCodeBlock(functionContent, document.languageId)
        );
        const definitionLine = document.lineAt(range.start).text;
        const padding = definitionLine.slice(0, findLeadingNonWhitespaceIndex(definitionLine));
        const res = await this.resolveFallback(document, range, conversation, functionContent, padding, startTime);
        if (res?.uuid) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'inlineComment',
                duration: performance.now() - startTime,
                uuid: res.uuid,
                type: 'all',
            });
        }
    }

    async handleInlineCommentCommand(
        document: vscode.TextDocument,
        range: vscode.Range,
        startTime: number = performance.now()
    ) {
        const codelensDisplayMode = this.configProvider.getDocstringDisplayMode();
        const pluginConfig = await this.pluginConfig();
        if (codelensDisplayMode === 'editor' && this.isDefaultConfig(pluginConfig)) {
            this.inlineChatProvider.generateInlineChatContent(
                document,
                range,
                'CODE_INLINE_COMMENT',
                new vscode.Position(0, 0)
            );
            return;
        }
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_ADD_COMMENT});
        const firstLine = getCompleteFirstLine(document, range);
        const functionContent = stripExtraIndent(document.getText(range), true, firstLine);
        const conversation = this.chatViewProvider.createConversation(
            this.displayPrompt(pluginConfig),
            'inlineComment',
            addMarkdownCodeBlock(functionContent, document.languageId)
        );
        const definitionLine = document.lineAt(range.start).text;
        const padding = definitionLine.slice(0, findLeadingNonWhitespaceIndex(definitionLine));
        const res = await this.resolve(
            document,
            range,
            conversation,
            functionContent,
            padding,
            pluginConfig,
            startTime
        );
        if (res?.uuid) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'inlineComment',
                duration: performance.now() - startTime,
                uuid: res.uuid,
                type: 'all',
            });
        }
    }

    async resolve(
        document: vscode.TextDocument,
        range: vscode.Range,
        conversation: Conversation,
        functionContent: string,
        padding: string,
        pluginConfig: {inlineCommentLanguage: string, inlineCommentMode: string},
        startTime: number
    ) {
        const streamMode = this.configProvider.getConfig(ConfigKey.EnableStreamingSession);
        const reply = conversation.addResponse(streamMode ? 'stream' : 'text', '', 'inProgress', {
            regenerate: async () => {
                this.resolve(document, range, conversation, functionContent, padding, pluginConfig, performance.now());
            },
            // 打开全文复制按钮
            copyAll: () => {},
        });
        const paramsRes = await buildParams(
            document,
            range.start,
            iocContainer.get(UserService)
        );
        if (paramsRes.type !== 'success') {
            reply.fail(getGenerationFailureText(paramsRes.reason));
            return {uuid: ''};
        }
        const params = {
            ...paramsRes.value,
            content: functionContent,
            userInput: functionContent,
            model: 'ERNIE_BOT',
            function: 'CODE_INLINE_COMMENT',
        };
        if (!this.isDefaultConfig(pluginConfig)) {
            params.content = this.realPrompt(pluginConfig, paramsRes.value.path, functionContent);
            params.userInput = '。';
            params.function = 'COMMENT_TO_CODE';
        }
        try {
            if (streamMode) {
                return this.fetchAndStream(document, range, padding, reply, params, startTime);
            }
            else {
                return this.fetchWithParams(document, range, padding, reply, params);
            }
        }
        catch (e: any) {
            reply.fail(getGenerationFailureText(e.message));
            return {uuid: ''};
        }
    }

    async provideCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();
        if (codelensDisplayMode !== CodeLensDisplayMode.TextTitle) {
            return [];
        }
        if (this.diffProvider.isInDiff(document.uri.fsPath)) {
            return [];
        }
        const codeLenses = await this.computeCodeLenses(document);
        return this.tmpCodeLensProvider.filterOverlapCodeLenses(document, codeLenses);
    }

    async computeCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensConfig = this.configProvider.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        if (codelensConfig?.enableInlineComment !== true || !this.licenseController.hasLicense) {
            return [];
        }
        return this.treeSitterProvider.getFunctionCodeLens(
            document,
            filterInlineCommentFunctionNode,
            (document: vscode.TextDocument, range: vscode.Range, node: Parser.SyntaxNode) => {
                const parent = node.parent;
                const editRange = parent && parent.type === 'export_statement'
                    ? new vscode.Range(
                        new vscode.Position(parent.startPosition.row, parent.startPosition.column),
                        range.end
                    )
                    : range;
                return {
                    title: L10n.t(CommentProviderText.CODELENS_TITLE),
                    tooltip: L10n.t(CommentProviderText.CODELENS_TOOLTIP),
                    command: CMD_ADD_COMMENT,
                    arguments: [document, editRange],
                };
            }
        );
    }

    private async fetchWithParams(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        padding: string,
        reply: TextResponse,
        params: GenerateCodeOptions
    ) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        this.timeTracker.recordChatStart(reply.message.replyTo ?? -1);
        const result = await generateCode(params, axiosTokenSource.token);
        this.timeTracker.bindChatId(result.data.data?.uuid ?? '', reply.message.replyTo ?? -1);

        if (result.data.status !== 'OK') {
            reply.fail(getGenerationFailureText(result.data.message));
            return;
        }
        const generatedContent = result.data.data?.content;
        if (!generatedContent) {
            reply.fail(L10n.t(CommentProviderText.GENERATE_ERROR));
            return;
        }
        const uuid = result.data.data?.uuid;
        reply.success(
            generatedContent,
            this.getSuccessActions(
                reply,
                document,
                functionRange,
                buildReplacement(document, generatedContent, padding),
                uuid,
                result.data.data?.chatId
            ),
            uuid
        );
        return {uuid};
    }

    private async fetchAndStream(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        padding: string,
        reply: TextResponse,
        params: GenerateCodeOptions,
        startTime: number
    ) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        let firstToken = true;
        const resCode = await fetchAndStreamCode(
            params,
            (content: string, uuid: string, chatId: string) => {
                // 只返回代码部分
                const startIndex = content.indexOf('```');
                const endIndex = content.lastIndexOf('```');
                if (content && firstToken) {
                    this.performanceLog.log({
                        plugin: 'comate',
                        skill: 'inlineComment',
                        duration: performance.now() - startTime,
                        uuid,
                        type: 'first-token',
                    });
                    firstToken = false;
                }
                if (startIndex === -1 || endIndex === -1) {
                    return reply.update('');
                }
                else if (startIndex === endIndex) {
                    return reply.update(
                        content.slice(startIndex),
                        this.getSuccessActions(
                            reply,
                            document,
                            functionRange,
                            buildReplacement(document, content.slice(startIndex), padding),
                            uuid,
                            chatId
                        )
                    );
                }
                else {
                    const length = content.match(/```/g)?.length;
                    if (length && length > 2) {
                        const codeBlock = extractMarkdownCodeBlocks(content)[0];
                        return reply.success(
                            codeBlock,
                            this.getSuccessActions(
                                reply,
                                document,
                                functionRange,
                                buildReplacement(document, codeBlock, padding),
                                uuid,
                                chatId
                            ),
                            uuid
                        );
                    }
                    return reply.success(
                        content.slice(startIndex, endIndex + 3),
                        this.getSuccessActions(
                            reply,
                            document,
                            functionRange,
                            buildReplacement(document, content.slice(startIndex, endIndex + 3), padding),
                            uuid,
                            chatId
                        ),
                        uuid
                    );
                }
            },
            reply.message.cancelTokenSource?.token,
            axiosTokenSource.token
        );
        const {uuid, processor, chatId} = resCode;
        let {content} = resCode;
        if (processor.error) {
            reply.fail(getGenerationFailureText(processor.errorMsg));
            return;
        }
        if (!content || !content.includes('```')) {
            reply.fail(L10n.t(CommentProviderText.GENERATE_ERROR));
            return;
        }
        content = extractMarkdownCodeBlocks(content)[0];
        reply.success(
            content,
            this.getSuccessActions(
                reply,
                document,
                functionRange,
                buildReplacement(document, content, padding),
                uuid,
                chatId
            ),
            uuid
        );
        return {uuid};
    }

    private getSuccessActions(
        reply: TextResponse,
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        content: string,
        uuid?: string,
        chatId?: string
    ) {
        const {diff, accept} = this.diffProvider.createReplacementDiffHandler(
            document,
            functionRange,
            () => content,
            uuid
        );
        const chatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getChatId: () => chatId,
            getMessageContent: () => reply.message.content,
            getTrackUuid: () => uuid || reply.message.extra?.uuid,
        };
        return {
            ...this.defaultActions(chatResponseProxy, undefined, undefined, {ignoreSmartApplyFeature: true}),
            diff,
            accept,
        };
    }

    async pluginConfig() {
        const workspace = vscode.workspace.workspaceFolders;
        const pluginConfigs = this.chatViewProvider.pluginConfigs;
        // @ts-ignore
        const localConfig = await localPluginConfig(workspace);
        const {config} = await mergePluginConfig(pluginConfigs['comate'], localConfig);
        if (!config.inlineCommentLanguage) {
            config.inlineCommentLanguage = inlineCommentDefaultConfig.inlineCommentLanguage;
        }
        if (!config.inlineCommentMode) {
            config.inlineCommentMode = inlineCommentDefaultConfig.inlineCommentMode;
        }
        return config || {};
    }

    isDefaultConfig(config: Record<string, any>) {
        if (
            config.inlineCommentLanguage === defaultConfig.inlineCommentLanguage
            && config.inlineCommentMode
                === defaultConfig.inlineCommentMode
        ) {
            return true;
        }
        return false;
    }

    realPrompt(
        {inlineCommentLanguage, inlineCommentMode}: {
            inlineCommentLanguage: string;
            inlineCommentMode: string;
        },
        filePath: string,
        code: string
    ) {
        const fileExtension = path.extname(filePath).slice(1);
        if (inlineCommentLanguage === '英文') {
            return `Add English line comments in code blocks in code${
                inlineCommentMode === '完整' ? '' : ', up to 5 words'
            }
\`\`\`${SUFFIX_LANG_MAP[fileExtension] ?? fileExtension}
${code}
\`\`\``;
        }
        return `按代码逻辑块在代码中添加${inlineCommentLanguage}的行注释${
            inlineCommentMode === '完整' ? '' : '，最多5个字'
        }
\`\`\`${SUFFIX_LANG_MAP[fileExtension] ?? fileExtension}
${code}
\`\`\``;
    }

    displayPrompt(
        {inlineCommentLanguage, inlineCommentMode}: {inlineCommentLanguage: string, inlineCommentMode: string}
    ) {
        return `请为下面的函数增加${inlineCommentMode}的${inlineCommentLanguage}行间注释：`;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
