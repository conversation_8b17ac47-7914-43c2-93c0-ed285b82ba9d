import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import {L10n} from '@/common/L10nProvider/L10n';
import {DiffProviderText} from '@/common/L10nProvider/constants';
import {LogUploaderProvider, LogCategory} from '../LogUploaderProvider';
import {VisualStateManagerProvider} from '../VisualStateManagerProvider';
import {COMATE_DIFF_ACCEPT, COMATE_DIFF_REJECT, COMATE_DIFF_SCHEME} from './constant';

interface DiffInfo {
    originalFilepath?: string;
    newFilepath?: string;
    editor?: vscode.TextEditor;
    range: vscode.Range;
    content?: string;
    accept?: (text: string) => Promise<void>;
    reject?: () => Promise<void>;
}

const isWin = process.platform === 'win32';

/**
 * 区别于单屏 diff 展示，这里是双屏（利用 vscode 的 diff 编辑器）展示
 */
@injectable()
export class DualScreenDiffCodeLensProvider implements vscode.CodeLensProvider, vscode.Disposable {
    private readonly diffs: Map<string, DiffInfo> = new Map();
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(LogUploaderProvider) private readonly logUploadProvider: LogUploaderProvider,
        @inject(VisualStateManagerProvider) readonly visualStateManagerProvider: VisualStateManagerProvider
    ) {
        this.disposables.push(
            vscode.languages.registerCodeLensProvider({scheme: COMATE_DIFF_SCHEME}, this),
            vscode.commands.registerCommand(
                COMATE_DIFF_ACCEPT,
                async (uri?: string, content?: string) => {
                    const filepath = uri || vscode.window.activeTextEditor?.document?.uri?.fsPath || '';
                    const diffInfo = this.diffs.get(filepath);
                    if (diffInfo && diffInfo.content && diffInfo.accept) {
                        await diffInfo.accept(content || diffInfo.content);
                        this.clearDiffInfo(filepath);
                        this.logUploadProvider.logUserAction({
                            category: LogCategory.Diff,
                            content: uri ? 'codelensAccept' : 'keyboardAccept',
                        });
                    }
                }
            ),
            vscode.commands.registerCommand(COMATE_DIFF_REJECT, (uri?: string) => {
                const filepath = uri || vscode.window.activeTextEditor?.document?.uri?.fsPath || '';
                const diffInfo = this.diffs.get(filepath);
                if (diffInfo && diffInfo.reject) {
                    diffInfo.reject();
                }
                this.clearDiffInfo(filepath);
                this.closeDiffEditor();
            })
        );
    }

    async closeDiffEditor() {
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor && activeEditor.document.uri.scheme === COMATE_DIFF_SCHEME) {
            if (activeEditor.document.isDirty) {
                await activeEditor.document.save();
            }
            vscode.commands.executeCommand('workbench.action.closeActiveEditor');
        }
    }

    setDiffInfo(path: string, diffInfo: DiffInfo) {
        this.diffs.set(path, diffInfo);
    }

    getDiffInfo(path: string) {
        return this.diffs.get(path);
    }

    clearDiffInfo(path: string) {
        this.diffs.delete(path);
    }

    isInDiff(path: string): boolean {
        return this.diffs.has(path);
    }

    provideCodeLenses(document: vscode.TextDocument): vscode.CodeLens[] | Thenable<vscode.CodeLens[]> {
        vscode.commands.executeCommand('setContext', 'baidu.comate.inlinechat.isDiffCodelensVisible', false);
        if (document.uri.scheme === COMATE_DIFF_SCHEME) {
            const codeLenses: vscode.CodeLens[] = [];
            const loadingRange = this.visualStateManagerProvider.getLoadingRange(document.uri.fsPath);

            if (loadingRange) {
                return [];
            }
            const diffInfo = this.diffs.get(document.uri.fsPath);
            if (diffInfo) {
                vscode.commands.executeCommand('setContext', 'baidu.comate.inlinechat.isDiffCodelensVisible', true);
                codeLenses.push(
                    new vscode.CodeLens(diffInfo.range, {
                        title: '$(comate-logo-mini)'
                            + L10n.t(DiffProviderText.ACCEPT, (isWin ? '(Ctrl+' : '(⌘') + 'S)'),
                        command: COMATE_DIFF_ACCEPT,
                        arguments: [document.uri.fsPath, diffInfo?.content],
                    }),
                    new vscode.CodeLens(diffInfo.range, {
                        title: L10n.t(DiffProviderText.REJECT, (isWin ? '(Ctrl+' : '(⌘') + 'Z)'),
                        command: COMATE_DIFF_REJECT,
                        arguments: [document.uri.fsPath],
                    })
                );
            }
            return codeLenses;
        }
        else {
            return [];
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
