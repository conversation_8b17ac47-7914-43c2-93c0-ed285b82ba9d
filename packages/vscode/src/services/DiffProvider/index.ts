/* eslint-disable @typescript-eslint/init-declarations */
import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {MessageType} from '@shared/protocols';
import {WATI_FOR_ACCEPT_CODE_FROM_CHAT} from '@shared/constants';
import {TYPES} from '@/inversify.config';
import {L10n} from '@/common/L10nProvider/L10n';
import {DiffProviderText} from '@/common/L10nProvider/constants';
import {FileDocstringData, generateFileComment} from '@/api/codeSearch';
import {isInternal} from '@/utils/features';
import {postInsertionTasks} from '@/common/telemetry/postTasks';
import {iocContainer} from '@/iocContainer';
import {getExtensionContext} from '@/utils/extensionContext';
import {TelemetryData} from '@/common/telemetry';
import {ACCEPTANCE_TYPE, GenerateCodeOptions, acceptCode} from '../../api';
import {chatActionDelayedReporter} from '../ChatViewProvider/chatActionDelayedReporter';
import {ITimeTracker} from '../TimeTracker/types';
import {ChatViewProvider} from '../ChatViewProvider';
import {LogUploaderProvider, LogCategory} from '../LogUploaderProvider';
import {VisualStateManagerProvider} from '../VisualStateManagerProvider';
import {
    COMATE_DIFF_ALL_REJECT,
    COMATE_DIFF_DUAL_SCREEN_OPEN,
    COMATE_DIFF_SCHEME,
} from './constant';
import {DualScreenDiffCodeLensProvider} from './DiffCodeLensProvider';
import {DiffHighlightProvider, DiffRange} from './DiffHighlightProvider';

export class File implements vscode.FileStat {
    type: vscode.FileType;
    ctime: number;
    mtime: number;
    size: number;
    name: string;
    data: Uint8Array;

    constructor() {
        this.type = vscode.FileType.File;
        this.ctime = Date.now();
        this.mtime = Date.now();
        this.size = 0;
        this.name = 'comate-diff';
        this.data = new Uint8Array(0);
    }
}

let id = 0;

interface CachedFileItem {
    file: File;
    id: number;
    hasChange: boolean;
}

@injectable()
export class DiffProvider implements vscode.FileSystemProvider, vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private readonly _emitter = new vscode.EventEmitter<vscode.FileChangeEvent[]>();
    readonly onDidChangeFile: vscode.Event<vscode.FileChangeEvent[]> = this._emitter.event;
    entries: Map<string, CachedFileItem> = new Map();
    private context?: vscode.ExtensionContext;

    constructor(
        @inject(TYPES.ITimeTracker) private readonly timeTracker: ITimeTracker,
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(DualScreenDiffCodeLensProvider) private readonly codeLensProvider: DualScreenDiffCodeLensProvider,
        @inject(DiffHighlightProvider) private readonly highlightProvider: DiffHighlightProvider,
        @inject(LogUploaderProvider) private readonly logUploadProvider: LogUploaderProvider,
        @inject(VisualStateManagerProvider) readonly visualStateManagerProvider: VisualStateManagerProvider
    ) {
        this.disposables.push(
            vscode.workspace.registerFileSystemProvider(
                COMATE_DIFF_SCHEME,
                this
            ),
            vscode.workspace.onDidCloseTextDocument(doc => {
                this.entries.delete(doc.uri.toString());
            }),
            vscode.commands.registerCommand(COMATE_DIFF_DUAL_SCREEN_OPEN, this.openDiff, this)
        );
        this.start();
    }
    async start() {
        this.context = await getExtensionContext();
    }
    /**
     * 生成会话代码区域顶部展示的 diff 和 accept 函数，用于替换代码的采纳
     *
     * @param document 文档对象
     * @param range 范围对象
     * @param wrapContent 采纳时可以对内容进行修改，比如增加padding
     * @param uuid 可选的唯一标识符
     * @returns 返回一个包含diff和accept方法的对象
     */
    createReplacementDiffHandler(
        document: vscode.TextDocument,
        range: vscode.Range,
        wrapContent: (content: string) => string,
        uuid?: string,
        skipCodeRetention?: boolean,
        onAccept?: (content: string) => any,
        onReject?: () => Promise<void>
    ) {
        let id: number | undefined;
        const accept = async (text: string) => {
            if (onAccept) {
                onAccept(text);
            }
            const content = wrapContent(text);
            const replaceAll = id === undefined
                ? false
                : await this.checkAndUpdateWithDiffContent(document, id);
            if (!replaceAll) {
                await this.acceptReplacement(document, range, content);
            }
            this.closeDiffEditor();
            const documentText = document.getText() ?? '';
            if (uuid) {
                chatActionDelayedReporter.add(uuid, () => {
                    acceptCode({
                        accepted: true,
                        content: '',
                        uuid,
                        acceptanceInfo: {
                            originContent: documentText,
                            row: range.start?.line ?? 0,
                            col: range.start?.character ?? 0,
                            acceptanceContent: content,
                            acceptanceType: ACCEPTANCE_TYPE.REPLACE,
                        },
                    });
                });
                this.timeTracker.recordChatEnd(uuid, content);
                this.timeTracker.isCodeByComateNonSerial = true;
                const offset = document.offsetAt(range.start);
                await postInsertionTasks(
                    iocContainer,
                    {
                        offset,
                        uri: document.uri,
                        displayText: content,
                        uuid: uuid,
                        telemetry: {} as TelemetryData,
                    },
                    skipCodeRetention ? undefined : 'chat'
                );
            }
        };
        return {
            diff: async (text: string) => {
                const content = wrapContent(text);
                id = await this.showReplacementDiff(document, range, content, accept, onReject);
            },
            accept,
        };
    }

    createInsertionDiffHandler(
        document: vscode.TextDocument,
        insertPosition: vscode.Position,
        wrapContent: (content: string) => string,
        uuid?: string,
        sourceProvider?: MessageType,
        params?: GenerateCodeOptions
    ) {
        let id: number | undefined;
        const accept = async (content: string) => {
            const replaceAll = id === undefined
                ? false
                : await this.checkAndUpdateWithDiffContent(document, id);
            if (!replaceAll) {
                await this.acceptInsertion(document, insertPosition, wrapContent(content));
            }
            this.closeDiffEditor();
            const documentText = document.getText() ?? '';
            if (uuid) {
                chatActionDelayedReporter.add(uuid, () => {
                    acceptCode({
                        accepted: true,
                        content: '',
                        uuid,
                        acceptanceInfo: {
                            originContent: documentText,
                            row: insertPosition?.line ?? 0,
                            col: insertPosition?.character ?? 0,
                            acceptanceContent: content,
                            acceptanceType: ACCEPTANCE_TYPE.INSERT,
                        },
                    });
                });
                this.timeTracker.recordChatEnd(uuid, content);
                this.timeTracker.isCodeByComateNonSerial = true;
                const offset = document.offsetAt(insertPosition);
                await postInsertionTasks(
                    iocContainer,
                    {
                        offset,
                        uri: document.uri,
                        displayText: content,
                        uuid: uuid,
                        telemetry: {} as TelemetryData,
                    }
                );
            }
            if (sourceProvider === 'docstring' && isInternal) {
                setTimeout(() => {
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => {
                            reject(new Error('Generate Whole File Docstring Timeout After 5s'));
                        }, 5000);
                    });

                    const fileDocstringPromise = generateFileComment({
                        content: document.getText(),
                        repo: params?.repo ?? '',
                        path: params?.path ?? '',
                        username: params?.username ?? '',
                    });

                    // 如果服务端 5s 内没有返回结果，则舍弃此次请求
                    Promise
                        .race([timeoutPromise, fileDocstringPromise])
                        .then((fileDocstringRes: any) => {
                            if (
                                fileDocstringRes.success
                                && fileDocstringRes.data.content
                                && fileDocstringRes.data.generate_count > 0
                            ) {
                                this.generateFileDocstring(fileDocstringRes.data, document);
                            }
                        })
                        .catch(error => {
                            console.error(error);
                        });
                }, 100);
            }
        };
        return {
            diff: async (content: string) => {
                id = await this.showInsertionDiff(document, insertPosition, wrapContent(content), accept);
            },
            accept,
        };
    }

    async generateFileDocstring(fileDocstringData: FileDocstringData, document: vscode.TextDocument) {
        const conversation = this.chatViewProvider.createConversation('', 'docstring');
        const path = fileDocstringData.path;
        const resContent: string = `已为您在 ${path} 文件中批量生成注释。`;
        const reply = conversation.addResponse('text', '', 'success');
        const range = new vscode.Range(
            document.positionAt(0),
            document.positionAt(document.getText().length)
        );
        const {diff, accept} = this.createReplacementDiffHandler(
            document,
            range,
            () => fileDocstringData.content,
            fileDocstringData.uuid
        );

        const intro =
            `\n\`\`\`\n检测到当前文件还有${fileDocstringData.generate_count}个函数没有注释，已为您批量生成注释，请查看或采纳。\n\`\`\``;
        reply.success(resContent + intro, {diff, accept}, fileDocstringData.uuid);
    }

    updateDiffInfo(
        document: vscode.TextDocument,
        selection: vscode.Range,
        content: string,
        newText: string,
        accept?: (content: string) => Promise<void>,
        reject?: () => Promise<void>
    ): void {
        const diffUri = this.getDiffFileUri(document);
        const diffId = this.createDiffFile(diffUri, new TextEncoder().encode(newText));
        const diffInfo = this.codeLensProvider.getDiffInfo(diffUri.fsPath);
        const updatedDiffInfo = {
            newFilepath: diffUri.fsPath,
            range: selection,
            content,
            diffId,
            accept: accept || diffInfo?.accept,
            reject: reject || diffInfo?.reject,
        };
        const mergedDiffInfo = diffInfo
            ? {
                ...diffInfo,
                ...updatedDiffInfo,
            }
            : {
                originalFilepath: document.uri.fsPath,
                ...updatedDiffInfo,
            };
        this.codeLensProvider.setDiffInfo(diffUri.fsPath, mergedDiffInfo);
    }

    async showInsertionDiff(
        document: vscode.TextDocument,
        insertPosition: vscode.Position,
        content: string,
        accept?: (content: string) => Promise<any>
    ): Promise<number> {
        const fullText = document.getText();
        const offset = document.offsetAt(insertPosition);
        const newText = fullText.slice(0, offset) + content + fullText.slice(offset);
        const selection = new vscode.Range(insertPosition, insertPosition);

        this.updateDiffInfo(document, selection, content, newText, accept);

        return this.showDiff(
            document,
            selection,
            newText
        );
    }

    async showReplacementDiff(
        document: vscode.TextDocument,
        range: vscode.Range,
        content: string,
        accept?: (content: string) => Promise<void>,
        reject?: () => Promise<void>
    ): Promise<number> {
        const fullText = document.getText();
        const startOffset = document.offsetAt(range.start);
        const endOffset = document.offsetAt(range.end);
        const newText = fullText.slice(0, startOffset) + content + fullText.slice(endOffset);

        this.updateDiffInfo(document, range, content, newText, accept, reject);

        return this.showDiff(
            document,
            new vscode.Range(range.start, range.start),
            newText
        );
    }

    async showDiff(document: vscode.TextDocument, selection: vscode.Range, newText: string): Promise<number> {
        const diffUri = this.getDiffFileUri(document);
        // 避免用户再次保存时冲突
        await this.savePreviousDiffDocument(diffUri);
        const diffId = this.createDiffFile(diffUri, new TextEncoder().encode(newText));
        await vscode.commands.executeCommand(
            'vscode.diff',
            document.uri,
            diffUri,
            `Comate ${L10n.t(DiffProviderText.TITLE)}`,
            {selection} as vscode.TextDocumentShowOptions
        );
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            vscode
                .workspace
                .getConfiguration('diffEditor', editor.document.uri)
                .update('codeLens', true, vscode.ConfigurationTarget.Global);
        }
        this._emitter.fire([{type: vscode.FileChangeType.Changed, uri: diffUri}]);
        this.logUploadProvider.logUserAction({
            category: LogCategory.Diff,
            content: 'showDiff',
        });
        return diffId;
    }

    async checkAndUpdateWithDiffContent(target: vscode.TextDocument, id: number) {
        const uri = this.getDiffFileUri(target);
        const entry = this.entries.get(uri.toString());
        if (!entry || entry.id !== id) {
            return false;
        }
        await this.savePreviousDiffDocument(uri);
        if (!entry.hasChange) {
            return false;
        }
        const fullText = new TextDecoder().decode(entry.file.data);
        const edit = new vscode.WorkspaceEdit();
        edit.replace(target.uri, new vscode.Range(0, 0, target.lineCount, 0), fullText);
        return vscode.workspace.applyEdit(edit);
    }

    async acceptReplacement(document: vscode.TextDocument, range: vscode.Range, content: string) {
        const edit = new vscode.WorkspaceEdit();
        edit.replace(document.uri, range, content);
        this.context?.workspaceState.update(WATI_FOR_ACCEPT_CODE_FROM_CHAT, content);
        await vscode.workspace.applyEdit(edit);
    }

    async acceptInsertion(document: vscode.TextDocument, insertPosition: vscode.Position, content: string) {
        const edit = new vscode.WorkspaceEdit();
        edit.insert(document.uri, insertPosition, content);
        this.context?.workspaceState.update(WATI_FOR_ACCEPT_CODE_FROM_CHAT, content);
        await vscode.workspace.applyEdit(edit);
    }

    async closeDiffEditor() {
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor && activeEditor.document.uri.scheme === COMATE_DIFF_SCHEME) {
            if (activeEditor.document.isDirty) {
                await activeEditor.document.save();
            }
            vscode.commands.executeCommand('workbench.action.closeActiveEditor');
        }
    }

    stat(uri: vscode.Uri): vscode.FileStat {
        const entry = this.getDiffFile(uri);
        return entry.file;
    }

    async readFile(uri: vscode.Uri): Promise<Uint8Array> {
        const entry = this.getDiffFile(uri);
        if (entry.id === -1) {
            try {
                const originalFileUri = uri.with({scheme: 'file'});
                const content = await vscode.workspace.fs.readFile(originalFileUri);
                this.updateFileContent(entry, content);
                entry.id = id++;
            }
            catch {
                // do nothing
            }
        }
        return entry.file.data;
    }

    /**
     * 对 vscode 暴露的更新文件内容方法
     *
     * @param uri 文件路径
     * @param content 文件内容
     */
    writeFile(uri: vscode.Uri, content: Uint8Array) {
        const entry = this.getDiffFile(uri);
        if (entry.id === -1) {
            entry.id = id++;
        }
        else {
            entry.hasChange = true;
        }
        this.updateFileContent(entry, content);
    }

    delete(): void {
        // do nothing
    }

    rename(): void {
        // do nothing
    }

    createDirectory(): void {
        // do nothing
    }

    readDirectory() {
        // do nothing
        return [];
    }

    watch(): vscode.Disposable {
        return new vscode.Disposable(() => {});
    }

    /** .diff 被用户点击revert后，需要保存下 */
    private async savePreviousDiffDocument(uri: vscode.Uri) {
        const documents = vscode.workspace.textDocuments.filter(doc => {
            return doc.uri.toString() === uri.toString();
        });
        for (const doc of documents) {
            if (doc.isDirty) {
                await doc.save();
            }
        }
    }

    private getDiffFileUri(document: vscode.TextDocument) {
        return document.uri.with({scheme: COMATE_DIFF_SCHEME});
    }

    private getDiffFile(uri: vscode.Uri): CachedFileItem {
        const target = this.entries.get(uri.toString());
        if (!target) {
            const fileContent = new File();
            const item = {file: fileContent, id: -1, hasChange: false};
            this.entries.set(uri.toString(), item);
            return item;
        }
        return target;
    }

    private updateFileContent(entry: CachedFileItem, content: Uint8Array) {
        entry.file.data = content;
        entry.file.mtime = Date.now();
        entry.file.size = content.byteLength;
    }

    private createDiffFile(uri: vscode.Uri, content: Uint8Array) {
        const entry = this.getDiffFile(uri);
        this.updateFileContent(entry, content);
        entry.id = id++;
        return entry.id;
    }

    isInDiff(path: string): boolean {
        const isDiffLoading = this.visualStateManagerProvider.getLoadingRange(path);
        if (isDiffLoading) {
            return true;
        }

        const isInDiff = this.codeLensProvider.isInDiff(path);
        const isInHighlightDiff = this.highlightProvider.isInDiff(path);
        const activeEditor = vscode.window.activeTextEditor;
        if (isInDiff && activeEditor && activeEditor.document.uri.scheme === COMATE_DIFF_SCHEME) {
            return true;
        }
        else if (isInHighlightDiff && activeEditor) {
            return true;
        }
        else {
            return false;
        }
    }

    async openDiff(document_: vscode.TextDocument, diffInfo_: DiffRange) {
        const document = document_ || vscode.window?.activeTextEditor?.document;
        const diffUri = this.getDiffFileUri(document);
        const diffInfo = diffInfo_ || this.highlightProvider.getDiffRange(diffUri.fsPath);
        const {range, uuid, modifiedContent} = diffInfo;

        const {diff} = this.createReplacementDiffHandler(
            document,
            range,
            () => modifiedContent,
            uuid
        );
        this.highlightProvider.setDualScreenDiff(diff);
        this.highlightProvider.setCurrentDiffInfoForDualScreen(diffInfo);
        await vscode.commands.executeCommand(
            COMATE_DIFF_ALL_REJECT,
            document.uri,
            range,
            modifiedContent
        );
    }

    dispose() {
        this.entries.clear();
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
