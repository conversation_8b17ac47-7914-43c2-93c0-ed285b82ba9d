export const COMATE_DIFF_SCHEME = 'comate-diff';
/** diff 视图内的采纳 */
export const COMATE_DIFF_ACCEPT = 'baidu.comate.diffAccept';
/** diff 视图内的放弃 */
export const COMATE_DIFF_REJECT = 'baidu.comate.diffReject';
/** 对块级与行级的 diff 进行采纳 */
export const COMATE_DIFF_ALL_ACCEPT = 'baidu.comate.diffAllAccept';
/** 对块级与行级的 diff 进行放弃 */
export const COMATE_DIFF_ALL_REJECT = 'baidu.comate.diffAllReject';
/** 打开 diff 双屏 */
export const COMATE_DIFF_DUAL_SCREEN_OPEN = 'baidu.comate.diffDualScreenOpen';
