/**
 * @file 混合云用户配置
 */
import * as vscode from 'vscode';
import {LazyServiceIdentifer, injectable, inject} from 'inversify';
import {EventMessage} from '@shared/protocols';
import {knowledgeServiceHealthy, LicenseFullDetail} from '@comate/plugin-shared-internals';
import {error} from '@/common/outputChannel';
import {isSaaS} from '@/utils/features';
import consoleLogger from '../../common/consoleLogger';
import {getLicenseFullDetail, serviceHealthy} from '../../api';
import {codeIndexServiceHealthy, codeSearchServiceHealthy} from '../../api/codeSearch';
import {
    toastServiceSettingDebounced,
    toastPrivateServiceSettingDebounced,
} from '../../api/codeSearch/toastServiceSetting';
import {VSCodeConfigProvider} from '../ConfigProvider';
import {EmbeddingsController} from '../EmbeddingsService/controller';
import {ChatViewProvider} from '../ChatViewProvider';

// import {CMD_COMATE_PLUS_UPDATE_KNOWLEDGE} from '../ComatePlusChatSession/constants';
@injectable()
export class CustomizeProvider {
    private disposables: vscode.Disposable[] = [];
    private config?: LicenseFullDetail;

    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(new LazyServiceIdentifer(() => EmbeddingsController)) private readonly embeddingsController:
            EmbeddingsController
    ) {
        this.disposables.push(
            vscode.workspace.onDidChangeConfiguration(async e => {
                if (e.affectsConfiguration('baidu.comate.license')) {
                    this.start();
                }
                if (e.affectsConfiguration('baidu.comate.privateService')) {
                    this.privateServiceHealthy();
                }
                // 混合云服务地址隐藏配置入口，只使用console 后端配置的地址
                // if (e.affectsConfiguration('baidu.comate.customizeService')) {
                //     this.serviceHealthy();
                //     this.embeddingStart();
                //     const isCustomizeUser = this.config?.customized;
                //     if (isCustomizeUser) {
                //         vscode.commands.executeCommand(CMD_COMATE_PLUS_UPDATE_KNOWLEDGE, {});
                //     }
                // }
            })
        );
    }

    async start() {
        await this.customizeConfig();
        this.serviceHealthy();
        this.embeddingStart();
    }

    private async customizeConfig() {
        try {
            const license = this.configProvider.getLicense();
            const data = await getLicenseFullDetail(license);
            this.config = data;
            // this.configProvider.updateCustomizeConfig(data);
            this.chatViewProvider.sendDataToWebview(EventMessage.CustomizeUserChangeEvent, data.customized || false);
            consoleLogger.debug('customizeConfig:', data);
        }
        catch (ex) {
            error('userCustomizeConfig error:', (ex as Error).message);
        }
    }

    private embeddingStart() {
        const isCustomizeUser = this.config?.customized;
        if (isCustomizeUser) {
            this.embeddingsController.start();
        }
    }

    private async serviceHealthy() {
        try {
            const isCustomizeUser = this.config?.customized;
            if (isCustomizeUser) {
                const customizeService = this.getCustomizeServiceConfig();
                await Promise.all([
                    codeIndexServiceHealthy(),
                    codeSearchServiceHealthy(),
                    knowledgeServiceHealthy(customizeService),
                ]);
            }
        }
        catch (ex) {
            error('serviceHealthy error:', (ex as Error).message);
            toastServiceSettingDebounced();
        }
    }

    private async privateServiceHealthy() {
        try {
            const privateService = this.configProvider.getPrivateService();
            if (privateService && isSaaS) {
                await serviceHealthy();
            }
        }
        catch (ex) {
            error('privateServiceHealthy error:', (ex as Error).message);
            toastPrivateServiceSettingDebounced();
        }
    }

    getConfig() {
        return this.config;
    }

    isCustomizeUser() {
        return this.config?.customized || false;
    }

    getCustomizeServiceConfig() {
        return this.config?.customizedUrl;
    }

    getComputedHost() {
        if (this.isCustomizeUser()) {
            return this.config?.customizedUrl;
        }
        return this.configProvider.getPrivateService();
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
