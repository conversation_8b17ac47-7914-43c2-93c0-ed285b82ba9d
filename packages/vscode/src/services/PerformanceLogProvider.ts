/* eslint-disable max-len */
import os from 'node:os';
import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import axios from 'axios';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {ConfigKey, VSCodeConfigProvider} from './ConfigProvider';
import {getIdeName} from '@/common/Fetcher';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu-int.com',
    saas: 'https://comate.baidu.com',
    poc: 'https://comate.baidu.com',
};

export interface LogUploaderEvent {
    plugin: string;
    skill: string;
    duration: number;
    type?: string;
    uuid?: string;
    cache?: boolean;
}

@injectable()
export class PerformanceLogProvider {
    constructor(@inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider) {}

    async log(event: LogUploaderEvent) {
        try {
            const context = await getExtensionContextAsync();

            const logHost = BASE_URL_MAPPING[$features.PLATFORM];

            if (event.duration < 0) {
                return;
            }

            // eslint-disable-next-line
            console.log(999, event);

            await axios
                .post(
                    `${logHost}/logger/performance.log`,
                    {
                        machineId: vscode.env.machineId,
                        osPlatform: os.platform(),
                        osArch: os.arch(),
                        osRelease: os.release(),
                        ide: getIdeName(),
                        ideVersion: vscode.version,
                        pluginVersion: context?.extension.packageJSON.version,
                        pluginConfig: {
                            inlineSuggestionMode: this.configProvider.getConfig<string>(ConfigKey.InlineSuggestionMode)
                                ?? '',
                        },
                        username: this.configProvider.getConfig<string>(ConfigKey.Username) ?? '',
                        timestamp: Date.now(),
                        scope: $features.PLATFORM,
                        type: 'all',
                        ...event,
                    },
                    {
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }
                );
        }
        catch (e) {
            console.error(e);
        }
    }
}
