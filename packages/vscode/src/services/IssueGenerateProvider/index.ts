/* eslint-disable max-len */
// TODO: 这部分逻辑等JetBrains介入开发后迁移至kernel

import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {ACTION_BRANCH_CHANGE} from '@comate/plugin-shared-internals';
import {error} from '@/common/outputChannel';
import {Repository} from '@/external/builtinGitExtension';
import {getIssuesByQuery, apiInferCardScoreByCommittedDiff} from '../../api/issueGenerate';
import {getStagedStat} from '../../utils/git';
import {sleep} from '../../utils/common';
import {CMD_COMATE_PLUS_START_SESSION} from '../ComatePlusChatSession/constants';
import {UserService} from '../UserService';
import {LogUploaderProvider, LogCategory} from '../LogUploaderProvider';
import {KernelProvider} from '../KernelProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {RepositoryStateProvider} from '../RepositoryStateProvider';
import {getCommittedDiff, getAuthorLog} from './utils';

export const CMD_ISSUE_GENERATE = 'baidu.comate.issueGenerate';

@injectable()
export class IssueGenerateProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private issues: any[] = [];
    private lastGitIndex: string | undefined;
    private currentBranch: string | undefined;
    constructor(
        @inject(UserService) private readonly userService: UserService,
        @inject(LogUploaderProvider) private readonly logUploadProvider: LogUploaderProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(RepositoryStateProvider) private readonly repositoryStateProvider: RepositoryStateProvider
    ) {
        this.start();
        this.disposables.push(
            vscode.commands.registerCommand(CMD_ISSUE_GENERATE, this.inferIssueScoreByCommittedDiff.bind(this))
        );
    }

    async start() {
        try {
            await this.chatViewProvider.waitingEngineInit();
            this.fetchIssuesByQuery();
            const unsubscribe = this.repositoryStateProvider.addRepositoryStateListener(repository => {
                this.onDidRepoChange(repository);
                this.onRepositoryBranchChange(repository);
            });
            this.disposables.push(
                new vscode.Disposable(() => {
                    unsubscribe();
                })
            );
        }
        catch (ex) {
            error('getAccess error:', (ex as Error).message);
            this.issues = [];
        }
    }

    async fetchIssuesByQuery() {
        try {
            const [username] = await this.userService.getCurrentUser();
            const workspace = vscode.workspace.workspaceFolders;
            const rootPath = workspace?.[0]?.uri.fsPath;
            const history = rootPath ? await getAuthorLog(rootPath) : undefined;
            const {content = [], contentByHisMsg = []} = await getIssuesByQuery({
                query: '最近14天我负责的未完成的卡片',
                // eslint-disable-next-line camelcase
                task_engine_userId: username,
                hisMsg: history,
                source: '1',
                from: 'vscode',
            });
            // 优先使用按历史提交过滤中类型+状态过滤的卡片列表
            const issues = contentByHisMsg.length === 0 ? content : contentByHisMsg;
            this.issues = issues;
        }
        catch (ex) {
            error('fetchIssuesByQuery error:', (ex as Error).message);
            this.issues = [];
        }
    }

    // eslint-disable-next-line complexity, max-statements
    async inferIssueScoreByCommittedDiff() {
        try {
            const titles = this.issues.map(item => item.cardIdAndTile);
            const workspace = vscode.workspace.workspaceFolders;
            if (workspace && workspace.length > 0) {
                const rootPath = workspace[0].uri.fsPath;
                const {diff, diffStatus, errorMessage} = await getCommittedDiff(rootPath);
                if (diffStatus !== 'success') {
                    throw new Error(errorMessage);
                }
                if (titles.length === 0) {
                    throw new Error('no issue found');
                }
                const response = await apiInferCardScoreByCommittedDiff({
                    diff,
                    titles,
                });
                this.logUploadProvider.logUserAction({
                    category: LogCategory.IssueGenerate,
                    label: 'issueGenerate',
                    content: {issues: titles, diff},
                });
                const generateIssue = this.issues[response.result.rank.findIndex(value => value === 0) || 0];
                if (!generateIssue) {
                    return;
                }
                const choice = await vscode.window.showInformationMessage(
                    `✨Comate为您推荐可能绑定的卡片：${generateIssue.cardIdAndTile}`,
                    '插入终端',
                    '复制卡片信息',
                    '查找更多卡片'
                );
                if (choice === '插入终端') {
                    const terminal = vscode.window.activeTerminal ?? vscode.window.createTerminal();
                    const command = `git commit -m "${generateIssue.cardIdAndTile}"`;
                    terminal.show();
                    vscode.env.clipboard.writeText(command);
                    await vscode.commands.executeCommand('workbench.action.terminal.focus');
                    await vscode.commands.executeCommand('workbench.action.terminal.paste');
                    this.logUploadProvider.logUserAction({
                        category: LogCategory.IssueGenerate,
                        label: 'acceptCurrentCard',
                        content: {issues: titles, diff, generateIssue},
                    });
                }
                else if (choice === '复制卡片信息') {
                    vscode.env.clipboard.writeText(generateIssue.cardIdAndTile);
                    this.logUploadProvider.logUserAction({
                        category: LogCategory.IssueGenerate,
                        label: 'copyCurrentCard',
                        content: {issues: titles, diff, generateIssue},
                    });
                }
                else if (choice === '查找更多卡片') {
                    vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: 'icafe'});
                    // 等待三方插件初始化完毕
                    sleep(5000);
                    vscode.commands.executeCommand(CMD_COMATE_PLUS_START_SESSION, '', 'icafe', 'searchBySelf', []);
                    this.logUploadProvider.logUserAction({
                        category: LogCategory.IssueGenerate,
                        label: 'findMore',
                    });
                }
            }
        }
        catch (ex) {
            error('inferIssueScoreByCommittedDiff error:', (ex as Error).message);
        }
    }

    async performGitChecks(path: string) {
        const diffStat = await getStagedStat(path);
        if (this.lastGitIndex === diffStat) {
            return;
        }
        this.lastGitIndex = diffStat;
        this.inferIssueScoreByCommittedDiff();
    }

    onDidRepoChange(repo: Repository) {
        try {
            const indexChanges = repo.state.indexChanges;
            const workingTreeChanges = repo.state.workingTreeChanges;

            if (indexChanges.length > 0 && workingTreeChanges.length === 0) {
                this.performGitChecks(repo.rootUri.fsPath);
            }
            if (this.lastGitIndex && workingTreeChanges.length > 0) {
                this.lastGitIndex = '';
            }
        }
        catch (ex) {
            error('onDidRepoChange error:', (ex as Error).message);
        }
    }

    onRepositoryBranchChange(repo: Repository) {
        try {
            const branch = repo.state.HEAD?.name;
            if (this.currentBranch !== branch) {
                this.currentBranch = branch;
                this.kernelProvider.client?.sendRequest(ACTION_BRANCH_CHANGE, {data: branch});
            }
        }
        catch (ex) {
            error('onRepositoryBranchChange error:', (ex as Error).message);
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
