import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
// import {TextModel} from '@comate/plugin-shared-internals';
import {
    ACTION_GENERATE_MESSAGE,
    ACTION_GENERATE_MESSAGE_REPORT,
    GenerateMessageResponse,
} from '@comate/plugin-shared-internals';
import {error} from '@/common/outputChannel';
import {Repository} from '@/external/builtinGitExtension';
import {CONTEXT_SHOW_GIT_MESSAGE} from '@/constants';
import {BuiltinGitProvider} from '../BuiltinGitProvider';
import {KernelProvider} from '../KernelProvider';

export const CMD_MESSAGE_GENERATE = 'baidu.comate.sourceControlGenerateMessage';

@injectable()
export class MessageGenerateProvider implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    constructor(@inject(KernelProvider) private readonly kernelProvider: KernelProvider) {
        this.watchGitCommit();
        vscode.commands.executeCommand('setContext', CONTEXT_SHOW_GIT_MESSAGE, true);
        this.disposables.push(
            vscode.commands.registerCommand(CMD_MESSAGE_GENERATE, this.generate.bind(this))
        );
    }

    async generate(repositories: Repository[] | Repository) {
        try {
            const repository = Array.isArray(repositories) ? repositories[0] : repositories;
            const cwd = repository.rootUri.fsPath;
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.SourceControl,
                cancellable: true,
            }, async progress => {
                progress.report({increment: 50});
                // eslint-disable-next-line max-len
                const generateMessageResponse = await this.kernelProvider.client?.sendRequest(
                    ACTION_GENERATE_MESSAGE,
                    {data: {cwd}}
                ) as GenerateMessageResponse;
                if (generateMessageResponse.status !== 'success' && generateMessageResponse.errorMessage) {
                    vscode.window.showInformationMessage(generateMessageResponse.errorMessage);
                    error('Failed to generate message');
                    return;
                }
                progress.report({increment: 100});
                if (repository) {
                    repository.inputBox.value = generateMessageResponse.message;
                }
            });
        }
        catch (ex) {
            error('generate error:', (ex as Error).message);
        }
    }

    onRepositoryCommit(repository: Repository) {
        const commitListener = repository.onDidCommit(async () => {
            const ref = repository.state.HEAD?.name;
            const commit = ref ? await repository.getCommit(ref) : undefined;
            if (commit) {
                const commitMessage = commit.message.split('\n\n')[0];
                this.kernelProvider.client?.sendRequest(
                    ACTION_GENERATE_MESSAGE_REPORT,
                    {data: {message: commitMessage, cwd: repository.rootUri.fsPath}}
                );
            }
        });
        this.disposables.push(commitListener);
    }

    private async watchGitCommit() {
        try {
            const gitProvider = BuiltinGitProvider.createProvider();
            if (!gitProvider) {
                error('No Git provider found.');
                return;
            }
            gitProvider.repositories.forEach(repository => {
                this.onRepositoryCommit(repository);
            });
            this.disposables.push(
                gitProvider.onDidOpenRepository(repository => {
                    this.onRepositoryCommit(repository);
                })
            );
        }
        catch (ex) {
            error('watchGitCommit error:', (ex as Error).message);
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
    }
}
