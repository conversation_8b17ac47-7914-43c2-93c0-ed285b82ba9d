import fs from 'node:fs/promises';
import * as path from 'path';
import {getStagedDiffContent, getCurrentGitAuthor, getLog, getLogByAuthor} from '../../utils/git';

export function formatPrompt(promptTemplate: string, args?: Record<string, unknown>) {
    if (args) {
        return promptTemplate.replace(
            /\{\{(\w+)\}\}/g,
            (match, key) => {
                const replacement = args[key];
                return replacement === undefined ? match : String(replacement);
            }
        );
    }

    return promptTemplate;
}

export const getReadmeContent = async (rootPath: string): Promise<string> => {
    try {
        const readmePath = path.join(rootPath, 'README.md');
        await fs.access(readmePath, fs.constants.F_OK);
        const stats = await fs.stat(readmePath);
        // 处理文件大小超过限制的情况
        const maxFileSize = 1024 * 1024; // 设置读取文件大小限制为1MB
        if (stats.size > maxFileSize) {
            console.warn(
                // eslint-disable-next-line max-len
                `File size of ${readmePath} (${stats.size} bytes) exceeds maximum size of ${maxFileSize} bytes. Skipping...`
            );
            return '';
        }
        const data = await fs.readFile(readmePath, 'utf8');
        return data;
    }
    catch (error) {
        console.error(`Error accessing file or directory: ${error}`);
        return '';
    }
};

const formatDiffBlock = (text: string) => {
    return text
        .split('\n')
        .filter(line => line.startsWith('+') || line.startsWith('-'))
        .map(line => line.replace(/^(---|\+\+\+) /, '').replace(/^[+-] */, ''));
};

const ignoreFiles = ['yarn.lock'];

export const getCommittedDiff = async (pwd: string) => {
    try {
        const stagedDiff = await getStagedDiffContent(pwd);
        if (!stagedDiff) {
            return {diffStatus: 'success', diff: ''};
        }
        const list = stagedDiff
            .split(/^diff --git /)
            .map(formatDiffBlock)
        // 过滤掉无用的文件，减少diff的长度，暂时只有 yarn.lock ，
            .filter(v => v.length && ignoreFiles.every(file => !v[0].endsWith(file)))
            .map(v => v.join(' '));
        const diffText = list.join(' ').slice(0, 10000);
        return {diffStatus: 'success', diff: diffText};
    }
    catch (ex) {
        return {diffStatus: 'fail', diff: '', errorMessage: (ex as Error).message};
    }
};

export const extractJsonFromString = (input: string): string | null => {
    const jsonBlockRegex = /```json\n([\s\S]*?)\n```/;
    const match = jsonBlockRegex.exec(input);
    return match ? match[1] : null;
};

export const formatIssueMessage = (result: string) => {
    const jsonString = extractJsonFromString(result);
    if (jsonString) {
        try {
            const value = JSON.parse(jsonString);
            return value?.message;
        }
        catch {
            throw new Error('format issue message error.');
        }
    }
    return null;
};

export const getAuthorLog = async (cwd: string): Promise<string> => {
    try {
        const author = await getCurrentGitAuthor(cwd);
        const currentUserHistory = await getLogByAuthor(cwd, author);
        if (!currentUserHistory || currentUserHistory.length === 0) {
            const history = await getLog(cwd);
            return history;
        }
        return currentUserHistory;
    }
    catch (e) {
        const history = await getLog(cwd);
        return history;
    }
};
