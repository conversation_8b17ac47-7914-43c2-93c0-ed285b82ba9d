import os from 'node:os';
import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import {uploadLog} from '@/api';
import {getExtensionContextAsync} from '@/utils/extensionContext';
// import {getDeviceUUIDThrottled} from '@/utils/deviceUUID';
import {createAxios} from '@/api/common';
import {getDeviceUUIDThrottled} from '@/utils/deviceUUID';
import {isPoc} from '@/utils/features';
import {ConfigKey, VSCodeConfigProvider} from './ConfigProvider';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu-int.com',
    saas: 'https://comate.baidu.com',
    poc: 'https://comate.baidu.com',
};

const axios = createAxios({
    baseURL: BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
});

const ERROR_WHITE_LIST = [
    'Importing JSON module',
    'Debugger listening',
    'SESSION_FINISH',
    'COMATE_PLUS_INITIALIZED',
];

// timeFrame 最多调用 limit 次
function throttle(func: (...args: any[]) => void, limit: number, timeFrame: number): (...args: any[]) => void {
    let lastFunc: NodeJS.Timeout | null = null;
    let lastRan: number = Date.now();
    let calledTimes = 0;
    return (...args: any[]) => {
        if (Date.now() - lastRan >= timeFrame) {
            lastRan = Date.now();
            calledTimes = 0;
        }

        if (calledTimes < limit) {
            if (lastFunc) {
                clearTimeout(lastFunc);
            }
            lastFunc = setTimeout(() => {
                func(...args);
                calledTimes++;
            }, 0);
        }
    };
}

export enum LogCategory {
    showChatPanel = 'showChatPanel',
    queryVisibility = 'queryVisibility',
    ChatVisibilityChange = 'ChatVisibilityChange',
    onDidChangeState = 'ondidchangestate',
    getEngineWebviewInitDataFailed = 'GetEngineWebviewInitDataFailed',
    comatePair = 'comate-pair',
    selectPluginOrCommand = 'selectPluginOrCommand',
    AutoDebug = 'autoDebug',
    TrackUUIDGenerateFailed = 'TrackUUIDGenerateFailed',
    Diff = 'diff',
    IssueGenerate = 'issueGenerate',
    OpenComatePlusFromUrlSchema = 'openComatePlusFromUrlSchema',
    Engine = 'engine',
    EngineInit = 'engineInit',
    EngineInnerInit = 'engineInnerInit',
    Kernel = 'kernel',
    PromptTemplate = 'promptTemplate',
    App = 'app',
    AutoPaste = 'autoPaste',
    createNewChat = 'createNewChat',
    chatCodeBlockAction = 'chatCodeBlockAction',
    BinTest = 'binTest',
    UserApiError = 'userApiError',
    Diagnostic = 'diagnostic',
}
export interface LogUploaderEvent {
    category: LogCategory;
    label?: string;
    content?: any;
    action?: string;
    source?: string;
}

type LogType = 'event' | 'error';

@injectable()
export class LogUploaderProvider {
    constructor(@inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider) {
    }
    /**
     * @deprecated 废弃，使用 logUserAction
     */
    upload = throttle(
        async (msg: string, type = 'event') => {
            if (ERROR_WHITE_LIST.some(e => msg.includes(e))) {
                return;
            }
            const context = await getExtensionContextAsync();
            uploadLog(
                {
                    type,
                    platform: $features.PLATFORM,
                    common: {
                        license: this.configProvider.getConfig<string>(ConfigKey.Key) ?? '',
                        username: this.configProvider.getConfig<string>(ConfigKey.Username) ?? '',
                        plugin: 'vscode',
                        version: context.extension.packageJSON.version,
                        ideVersion: vscode.version,
                        os: {platform: os.platform(), arch: os.arch(), release: os.release()},
                    },
                    event: {
                        content: msg,
                    },
                }
            );
        },
        3,
        10000
    );
    /**
     * @deprecated 废弃，使用 logProvider的 logUserAction
     */
    async internalLogUserAction(type: LogType = 'event', event: LogUploaderEvent) {
        const context = await getExtensionContextAsync();
        axios.post(
            'https://comate.baidu-int.com/logger/comate.log',
            {
                type,
                platform: $features.PLATFORM,
                common: {
                    username: this.configProvider.getConfig<string>(ConfigKey.Username) ?? '',
                    app: 'comate-vscode',
                    version: context.extension.packageJSON.version,
                    ideVersion: vscode.version,
                    os: {platform: os.platform(), arch: os.arch(), release: os.release()},
                },
                event,
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }

    async logError(category: LogCategory, content: string) {
        if (ERROR_WHITE_LIST.some(e => content.includes(e))) {
            return;
        }
        this.logUserAction({
            category,
            content,
        }, 'error');
    }

    async logUserAction(event: LogUploaderEvent, type: LogType = 'event') {
        if (
            process.env.NODE_ENV === 'development' && type === 'error'
            && ERROR_WHITE_LIST.some(e => event.content.includes(e))
        ) {
            return;
        }
        // poc 就不上报了，没有对应服务
        if (isPoc) {
            return;
        }
        try {
            const context = await getExtensionContextAsync();
            const clientId = await getDeviceUUIDThrottled();
            axios
                .post(
                    '/logger/comate.log',
                    {
                        type,
                        event,
                        platform: $features.PLATFORM,
                        common: {
                            app: 'comate-vscode',
                            version: context?.extension.packageJSON.version,
                            clientId: clientId ?? null,
                            license: this.configProvider.getConfig<string>(ConfigKey.Key) ?? '',
                            username: this.configProvider.getConfig<string>(ConfigKey.Username) ?? '',
                            ideVersion: vscode.version,
                            machineId: vscode.env.machineId,
                            from: vscode.env.appName,
                            language: vscode.env.language,
                            os: {platform: os.platform(), arch: os.arch(), release: os.release()},
                            timestamp: Date.now(),
                        },
                    },
                    {
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }
                )
                .catch(e => {
                    console.error(e);
                });
        }
        catch (e) {
            console.error(e);
        }
    }
}
