/* eslint-disable @typescript-eslint/init-declarations */
/* eslint-disable @typescript-eslint/prefer-string-starts-ends-with */
/* eslint-disable no-control-regex */
/* eslint-disable max-depth */
/* eslint-disable complexity */
/* eslint-disable max-len */
/* eslint-disable max-statements */
import stripAnsi from 'strip-ansi';

export function removeLastLineArtifacts(output: string, ignoreFullLine = false) {
    const lines = output.trimEnd().split('\n');
    if (lines.length > 0) {
        const lastLine = lines[lines.length - 1];
        // Remove prompt characters and trailing whitespace from the last line
        lines[lines.length - 1] = lastLine.replace(/[%$#>]\s*$/, '');
        // NOTE: 发现终端输出最后一行会带有 prompt character 和一段路径，这不是我们想要的，因此直接删除最后一行
        if (ignoreFullLine && /[%$#>]\s*/.test(lastLine)) {
            lines.pop();
        }
    }
    return lines.join('\n').trimEnd();
}

export function createTerminalOutputProcessor(command: string) {
    let isFirstChunk = true;
    let didOutputNonCommand = false;

    return (chunk: string) => {
        let data = chunk;
        // 1. Process chunk and remove artifacts
        if (isFirstChunk) {
            /*
             * The first chunk we get from this stream needs to be processed to be more human readable, ie remove vscode's custom escape sequences and identifiers, removing duplicate first char bug, etc.
             */

            // bug where sometimes the command output makes its way into vscode shell integration metadata
            /*
            ]633 is a custom sequence number used by VSCode shell integration:
            - OSC 633 ; A ST - Mark prompt start
            - OSC 633 ; B ST - Mark prompt end
            - OSC 633 ; C ST - Mark pre-execution (start of command output)
            - OSC 633 ; D [; <exitcode>] ST - Mark execution finished with optional exit code
            - OSC 633 ; E ; <commandline> [; <nonce>] ST - Explicitly set command line with optional nonce
            */
            // if you print this data you might see something like "eecho hello worldo hello world;5ba85d14-e92a-40c4-b2fd-71525581eeb0]633;C" but this is actually just a bunch of escape sequences, ignore up to the first ;C
            /* ddateb15026-6a64-40db-b21f-2a621a9830f0]633;CTue Sep 17 06:37:04 EDT 2024 % ]633;D;0]633;P;Cwd=/Users/<USER>/Repositories/test */
            // Gets output between ]633;C (command start) and ]633;D (command end)
            const outputBetweenSequences = removeLastLineArtifacts((/\]633;C([\s\S]*?)\]633;D/.exec(data))?.[1] || '')
                .trim();

            // Once we've retrieved any potential output between sequences, we can remove everything up to end of the last sequence
            // https://code.visualstudio.com/docs/terminal/shell-integration#_vs-code-custom-sequences-osc-633-st
            const vscodeSequenceRegex = /\x1b\]633;.[^\x07]*\x07/g;
            const lastMatch = [...data.matchAll(vscodeSequenceRegex)].pop();
            if (lastMatch && lastMatch.index !== undefined) {
                data = data.slice(lastMatch.index + lastMatch[0].length);
            }
            // Place output back after removing vscode sequences
            if (outputBetweenSequences) {
                data = outputBetweenSequences + '\n' + data;
            }
            // remove ansi
            data = stripAnsi(data);

            // NOTE: 如果最后一行是 artifacts，我们再 remove 一次
            data = removeLastLineArtifacts(data, true);

            // Split data by newlines
            const lines = data ? data.split('\n') : [];
            // Remove non-human readable characters from every line
            for (let i = 0; i < lines.length; i++) {
                lines[i] = lines[i].replace(/[^\x20-\x7E|\t]/g, '');
            }
            // Check if first two characters are the same, if so remove the first character
            if (lines.length > 0 && lines[0].length >= 2 && lines[0][0] === lines[0][1]) {
                lines[0] = lines[0].slice(1);
            }
            // NOTE: 不太懂为啥要这样做，我先注释了吧
            // // Remove everything up to the first alphanumeric character for first two lines
            // if (lines.length > 0) {
            //     lines[0] = lines[0].replace(/^[^a-zA-Z0-9]*/, '');
            // }
            // if (lines.length > 1) {
            //     lines[1] = lines[1].replace(/^[^a-zA-Z0-9]*/, '');
            // }
            // Join lines back
            data = lines.join('\n');
            isFirstChunk = false;
        }
        else {
            data = stripAnsi(data);
        }

        // first few chunks could be the command being echoed back, so we must ignore
        // note this means that 'echo' commands wont work
        if (!didOutputNonCommand) {
            const lines = data.split('\n');
            for (let i = 0; i < lines.length; i++) {
                // eslint-disable-next-line max-depth
                if (command.includes(lines[i].trim())) {
                    lines.splice(i, 1);
                    i--; // Adjust index after removal
                }
                else {
                    didOutputNonCommand = true;
                    break;
                }
            }
            data = lines.join('\n');
        }

        // FIXME: right now it seems that data chunks returned to us from the shell integration stream contains random commas, which from what I can tell is not the expected behavior. There has to be a better solution here than just removing all commas.
        data = data.replace(/,/g, '');

        return data;
    };
}
