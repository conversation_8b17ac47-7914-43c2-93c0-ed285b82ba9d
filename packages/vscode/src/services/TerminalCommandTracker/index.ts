import * as vscode from 'vscode';
import {compare} from 'compare-versions';
import {elideLongLine} from '@/utils/common';
import {createTerminalOutputProcessor, removeLastLineArtifacts} from './utils';
import {ITerminalCommandTracker, TerminalCommand} from './types';

interface CommandEntry {
    execution: vscode.TerminalShellExecution;
    fullOutput: string;
    startTime: number;
}

export interface Options {
    /** 最大保留记录的数量 */
    maxEntries?: number;
    /** 单个输出最大保留长度 */
    maxSingleOutput?: number;
}

export const defaultOptions: Required<Options> = {
    maxEntries: 50,
    maxSingleOutput: 300,
};

export class Terminal<PERSON>ommandTracker implements ITerminalCommandTracker, vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    private readonly entries = new Map<vscode.TerminalShellExecution, CommandEntry>();
    private readonly log: vscode.TerminalShellExecution[] = [];
    private readonly finalOptions: Required<Options>;
    private readonly _onDidStartTerminalShellExecution = new vscode.EventEmitter<void>();
    readonly onDidStartTerminalShellExecution: vscode.Event<void> = this._onDidStartTerminalShellExecution.event;

    constructor(readonly options: Options = defaultOptions) {
        this.finalOptions = {
            ...defaultOptions,
            ...options,
        };
        if (compare(vscode.version, '1.93.0', '>=')) {
            this.disposables.push(
                vscode.window.onDidStartTerminalShellExecution(async e => {
                    const outputStream = e.execution.read();
                    const command = e.execution.commandLine.value;
                    if (command.trim().length <= 0) {
                        // 忽略空命令
                        return;
                    }
                    this._onDidStartTerminalShellExecution.fire();
                    const removeCount = this.log.length - this.finalOptions.maxEntries + 1;
                    if (removeCount > 0) {
                        const toBeRemoved = this.log.splice(0, removeCount);
                        for (const item of toBeRemoved) {
                            this.entries.delete(item);
                        }
                    }
                    this.log.push(e.execution);
                    const entry: CommandEntry = {
                        execution: e.execution,
                        fullOutput: '',
                        startTime: Date.now(),
                    };
                    this.entries.set(e.execution, entry);
                    const processTerminalOutput = createTerminalOutputProcessor(command);
                    for await (const data of outputStream) {
                        entry.fullOutput += processTerminalOutput(data);
                        if (entry.fullOutput.length >= this.finalOptions.maxSingleOutput) {
                            // 我们只需要获取到需要的最大输出长度即可
                            break;
                        }
                    }
                    entry.fullOutput = elideLongLine(
                        removeLastLineArtifacts(entry.fullOutput, true),
                        this.finalOptions.maxSingleOutput
                    );
                })
            );
        }
    }

    getCommands(): TerminalCommand[] {
        const results: TerminalCommand[] = [];
        for (const execution of this.log) {
            const entry = this.entries.get(execution);
            if (entry) {
                results.push({
                    cwd: entry.execution.cwd?.fsPath,
                    command: entry.execution.commandLine.value,
                    output: entry.fullOutput,
                    startTime: entry.startTime,
                });
            }
        }
        return results;
    }

    dispose() {
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables.length = 0;
    }
}
