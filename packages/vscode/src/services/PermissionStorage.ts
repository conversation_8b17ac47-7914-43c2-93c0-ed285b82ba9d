import {PermissionType} from '@comate/plugin-shared-internals';
import * as vscode from 'vscode';
import {getExtensionContextAsync} from '@/utils/extensionContext';

export class PermissionStorage {
    context?: vscode.ExtensionContext;

    async get(pluginName: string, permissionType: PermissionType) {
        await this.init();
        // TODO 厂内默认有用户信息和workspace信息，saas要全部授权
        if (permissionType === PermissionType.UserDetail || permissionType === PermissionType.WorkspaceFileSystem) {
            return true;
        }
        return this.context?.globalState.get(`${pluginName}-${permissionType}`);
    }

    async clearAll() {
        await this.init();
        this.context?.globalState.keys().forEach(key => {
            this.context?.globalState.update(key, undefined);
        });
    }

    async update(pluginName: string, permissionType: string, isAccepted: boolean) {
        await this.init();
        return this.context?.globalState.update(`${pluginName}-${permissionType}`, isAccepted);
    }

    async init() {
        if (!this.context) {
            this.context = await getExtensionContextAsync();
        }
    }
}
