import * as vscode from 'vscode';
import {compact} from 'lodash';
import {getFirstWorkspaceRepoName} from '@/utils/workspace';
import consoleLogger from '../common/consoleLogger';
import {CodeChunk} from '../common/types';
import {runRg} from '../utils/rg';

export interface KeywordBlockMatches {
    filePath: string;
    lines: string[];
    startLine: number;
    endLine: number;
}

function processRgResult(result: string) {
    const files = result.split('\n\n');
    const blockMatches: KeywordBlockMatches[] = [];
    for (const file of files) {
        const [filePath, ...lines] = file.split(/\r?\n/);
        blockMatches.push({
            filePath,
            lines: [],
            startLine: -1,
            endLine: -1,
        });
        for (const line of lines) {
            const lastBlock = blockMatches[blockMatches.length - 1];
            if (line === '--') {
                blockMatches.push({
                    filePath,
                    lines: [],
                    startLine: -1,
                    endLine: -1,
                });
            }
            else {
                const match = /^([0-9]+)[:-]{1}/.exec(line);
                // eslint-disable-next-line max-depth
                if (match) {
                    const [prefix, lineNumber] = match;
                    const lineContent = line.slice(prefix.length);
                    // eslint-disable-next-line max-depth
                    if (lastBlock.startLine === -1) {
                        lastBlock.startLine = parseInt(lineNumber, 10);
                        lastBlock.endLine = lastBlock.startLine;
                    }
                    else {
                        lastBlock.endLine++;
                    }
                    lastBlock.lines.push(lineContent);
                }
            }
        }
    }
    return blockMatches;
}

interface RgSearchOpts {
    useRegexp?: boolean;
}

/**
 * 使用 ripgrep 搜索关键字，可以指定目录搜索
 */
export async function provideKeywordContext(
    keyword: string,
    searchPath: string = '',
    rgSearchOpts: RgSearchOpts = {useRegexp: false}
): Promise<CodeChunk[]> {
    const folders = vscode.workspace.workspaceFolders;
    if (!folders || folders.length === 0) {
        return [];
    }
    const rootFolder = folders[0].uri.fsPath;
    const args = compact([
        rgSearchOpts?.useRegexp ? undefined : '--fixed-strings',
        '--line-number',
        '--heading',
        '--crlf',
        '--word-regexp',
        '--context=10',
        '--max-columns=300',
        '--max-columns-preview',
        '--max-filesize=1M',
        `'${keyword}'`,
    ]);

    try {
        const result = await runRg(args, rootFolder, searchPath ? `**/${searchPath}/**` : '');
        const repo = await getFirstWorkspaceRepoName()
            .catch(() => rootFolder.slice(rootFolder.lastIndexOf('/') + 1));
        const blockMatches = processRgResult(result);
        const codeChunks: CodeChunk[] = blockMatches.map(item => ({
            repo,
            type: 'keyword',
            path: item.filePath,
            content: item.lines.join('\n'),
            contentStart: {
                line: item.startLine - 1,
                column: 0,
            },
            contentEnd: {
                line: item.endLine - 1,
                column: item.lines[item.lines.length - 1].length,
            },
        }));
        return codeChunks;
    }
    catch (e) {
        consoleLogger.error((e as Error).message);
        return [];
    }
}
