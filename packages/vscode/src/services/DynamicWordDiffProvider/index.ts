import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import {noop} from 'lodash';
import {CONTEXT_WORD_DIFF_ACTIVE, RegisteredCommand} from '@/constants';
import {L10n} from '@/common/L10nProvider/L10n';
import {DiffProviderText} from '@/common/L10nProvider/constants';
import {trace} from '@/common/outputChannel';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';
import {WordDiffWorker} from './WordDiffWorker';

function shouldAbortBeforeChange(e: vscode.TextDocumentChangeEvent, diffWorker: WordDiffWorker) {
    if (diffWorker.status === 'loading') {
        // loading 状态，除了新增空格/换行，其他修改都放弃
        return e.contentChanges.some(change => change.text.trim() !== '' || change.rangeLength !== 0);
    }
    const diffRange = diffWorker.diffRange;
    // 修改的范围跨 diff 的起始/结束位置，这种算破坏了 diff 的范围，也需要放弃
    // TODO 这种理论上是可以转成取消的，abort会保留中间状态，可以优化下
    return e.contentChanges.some(change => {
        if (change.range.contains(diffRange)) {
            return true;
        }
        const intersection = change.range.intersection(diffRange);
        if (!intersection || intersection.isEmpty) {
            return false;
        }
        const changeStart = change.range.start;
        const changeEnd = change.range.end;
        const modifyStart = changeStart.isBefore(diffRange.start) && changeEnd.isAfter(diffRange.start);
        const modifyEnd = changeStart.isBefore(diffRange.end) && changeEnd.isAfter(diffRange.end);
        return modifyStart || modifyEnd;
    });
}

function shouldAbortAfterChange(diffWorker: WordDiffWorker) {
    // 改完都没有 diff 了，没必要再展示 采纳/拒绝 操作了
    return diffWorker.status === 'diff' && !diffWorker.difference;
}

/**
 * 提供单词级别的 diff，因为是单词级别的 diff，与原本的 inline diff 不太适配，所以单独实现一个
 */
@injectable()
export class DynamicWordDiffProvider implements vscode.CodeLensProvider, vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    private readonly _onDidChangeCodeLenses = new vscode.EventEmitter<void>();
    onDidChangeCodeLenses?: vscode.Event<void> | undefined = this._onDidChangeCodeLenses.event;

    private activeWordDiffWorker: WordDiffWorker | undefined;

    private acceptCallback: () => void = noop;
    private rejectCallback: () => void = noop;

    constructor(
        @inject(TemporaryCodeLensProvider) private readonly temporaryCodeLensProvider: TemporaryCodeLensProvider
    ) {
        this.disposables.push(
            vscode.languages.registerCodeLensProvider({scheme: 'file'}, this),
            vscode.commands.registerCommand(RegisteredCommand.acceptWordDiffChange, this.accept, this),
            vscode.commands.registerCommand(RegisteredCommand.rejectWordDiffChange, this.reject, this),
            vscode.commands.registerCommand(RegisteredCommand.cancelWordDiff, () => {
                this.cancel('manually.');
            }),
            vscode.workspace.onDidChangeTextDocument(this.handleDocumentChange.bind(this)),
            vscode.window.onDidChangeActiveTextEditor((editor?: vscode.TextEditor) => {
                if (this.activeWordDiffWorker && editor !== this.activeWordDiffWorker.editor) {
                    this.cancel('editor changed');
                }
            })
        );
        this.temporaryCodeLensProvider.onFilterOverlapCodeLenses((document, codeLenses) => {
            if (
                this.activeWordDiffWorker
                && this.activeWordDiffWorker.editor.document.uri.toString() === document.uri.toString()
                && !this.activeWordDiffWorker.finished
            ) {
                const diffRange = this.activeWordDiffWorker.diffRange;
                return codeLenses.filter(codeLens => !diffRange.intersection(codeLens.range));
            }
            return codeLenses;
        });
    }

    provideCodeLenses(document: vscode.TextDocument): vscode.CodeLens[] {
        if (!this.activeWordDiffWorker || !this.activeWordDiffWorker.showActions(document)) {
            return [];
        }
        const {diffRange} = this.activeWordDiffWorker;
        const codelens = [
            new vscode.CodeLens(diffRange, {
                title: '$(comate-logo-mini)' + L10n.t(DiffProviderText.ACCEPT, '(Tab)'),
                command: RegisteredCommand.acceptWordDiffChange,
                arguments: [],
            }),
            new vscode.CodeLens(diffRange, {
                title: L10n.t(DiffProviderText.REJECT, '(Escape)'),
                command: RegisteredCommand.rejectWordDiffChange,
                arguments: [],
            }),
        ];
        return codelens;
    }

    /**
     * 单词级别的 inline diff，调用后先展示 loading 状态，执行 showDiff 后展示操作按钮
     *
     * @param editor vscode文本编辑器实例
     * @param replaceRange 要替换的文本范围
     * @param options 可选参数，包含取消时的回调函数
     * @returns
     */
    openWordDiff(
        editor: vscode.TextEditor,
        replaceRange: vscode.Range,
        options?: {onCancel?: (reason: string) => void}
    ) {
        this.cancel('next diff triggered');
        const diffWorker = this.createWorker(editor, replaceRange, options);
        return {
            showDiff: async (text: string | AsyncGenerator<string>) => {
                if (diffWorker.finished) {
                    return false;
                }
                const success = await diffWorker.run(text);
                if (success) {
                    if (!diffWorker.difference) {
                        diffWorker.cancel('no differences found');
                        return false;
                    }
                    this._onDidChangeCodeLenses.fire();
                }
                return success;
            },
            cancel: (msg: string) => {
                diffWorker.cancel(msg);
            },
        };
    }

    // 判断当前 diff 是否与正在编辑的冲突，比如在当前的diff区域中继续打开diff
    conflictWithCurrentActiveDiff() {
        return this.activeWordDiffWorker
            && this.activeWordDiffWorker.active !== false;
    }

    /** 采纳时的回调 */
    onAccept(callback: () => void) {
        this.acceptCallback = callback;
    }

    /** 拒绝采纳时的回调 */
    onReject(callback: () => void) {
        this.rejectCallback = callback;
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
    }

    private async accept() {
        if (!this.activeWordDiffWorker || this.activeWordDiffWorker.finished) {
            return;
        }
        await this.activeWordDiffWorker.acceptDiff();
        this.acceptCallback();
        this._onDidChangeCodeLenses.fire();
    }

    private async reject() {
        if (!this.activeWordDiffWorker || this.activeWordDiffWorker.finished) {
            return;
        }
        await this.activeWordDiffWorker.reject();
        this.rejectCallback();
        this._onDidChangeCodeLenses.fire();
    }

    private cancel(msg: string) {
        if (!this.activeWordDiffWorker || this.activeWordDiffWorker.finished) {
            return;
        }
        return this.activeWordDiffWorker?.cancel(msg);
    }

    /**
     * 监听文档变化，同步文档修改，并判断是否需要取消 diff
     * @param e document change event
     * @returns void
     */
    private handleDocumentChange(e: vscode.TextDocumentChangeEvent) {
        if (
            !this.activeWordDiffWorker
            || this.activeWordDiffWorker.finished
            || e.document.uri.toString() !== this.activeWordDiffWorker.editor.document.uri.toString()
            || e.contentChanges.length === 0
        ) {
            return;
        }
        if (shouldAbortBeforeChange(e, this.activeWordDiffWorker)) {
            trace('Abort word diff before document change');
            this.activeWordDiffWorker.abort();
            return;
        }

        // 同步修改，修正位置信息
        this.activeWordDiffWorker.onDocumentChange(e);

        if (shouldAbortAfterChange(this.activeWordDiffWorker)) {
            this.activeWordDiffWorker.abort();
        }

        this.postDocumentChange(e, this.activeWordDiffWorker);
    }

    /**
     * 同步文档修改，需要去检查下是否需要取消、是否需要重新设置定时等，因为取消操作涉及到删除代码，不能在同步前进行
     * @param e document change event
     * @returns void
     */
    private postDocumentChange(e: vscode.TextDocumentChangeEvent, worker: WordDiffWorker) {
        const diffRange = worker.diffRange;

        const beforeOrAfter = e.contentChanges.filter(change => {
            const intersection = change.range.intersection(diffRange);
            if (!intersection) {
                return true;
            }
            // 与 diff 区域相交，但相交于边界，而且 change 不是插入
            if (
                intersection.isEmpty
                && !change.range.isEmpty
                && (intersection.start.isEqual(diffRange.start) || intersection.end.isEqual(diffRange.end))
            ) {
                return true;
            }
            return false;
        });
        // 在 diff 范围之外的修改，如果不是空格换行，则取消 diff，loading状态前面已经处理过了
        // running 状态区分不了是diff加的还是用户加的，忽略，不取消就行
        if (
            worker.status !== 'loading' && worker.status !== 'running' && beforeOrAfter.some(
                change => change.text.trim() !== '' || change.rangeLength !== 0
            )
        ) {
            worker.cancel('other changes');
        }
    }

    private createWorker(
        editor: vscode.TextEditor,
        replaceRange: vscode.Range,
        options?: {onCancel?: (reason: string) => void}
    ) {
        const onInVisibleChange = () => {
            vscode.commands.executeCommand('setContext', CONTEXT_WORD_DIFF_ACTIVE, false);
        };
        const diffWorker = WordDiffWorker.create(
            editor,
            replaceRange,
            {...options, activeAnimation: false, onInVisibleChange}
        );
        this.activeWordDiffWorker = diffWorker;
        vscode.commands.executeCommand('setContext', CONTEXT_WORD_DIFF_ACTIVE, true);
        return diffWorker;
    }
}
