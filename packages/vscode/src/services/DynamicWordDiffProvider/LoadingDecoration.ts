import * as vscode from 'vscode';
import {DiffRangeManager} from './DiffRangeManager';

const loadingDecorationType = vscode.window.createTextEditorDecorationType({
    isWholeLine: true,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
});

/**
 * 这是一个会不断闪烁的 Decoration 主要用于首 token 回来前的 loading 状态展示
 */
export class LoadingDecoration {
    private loadingInterval: NodeJS.Timeout | null = null;

    constructor(
        private readonly editor: vscode.TextEditor,
        private readonly diffRangeManager: DiffRangeManager
    ) {

    }

    show() {
        if (this.loadingInterval) {
            return;
        }
        let show = true;
        this.loadingInterval = setInterval(() => {
            const startLine = this.diffRangeManager.startLine;
            const decorations = [new vscode.Range(startLine, 0, startLine, Number.MAX_SAFE_INTEGER)];
            this.editor.setDecorations(loadingDecorationType, show ? decorations : []);
            show = !show;
        }, 800);
    }

    hide() {
        if (this.loadingInterval) {
            clearInterval(this.loadingInterval);
            this.loadingInterval = null;
            this.clear();
        }
    }

    private clear() {
        this.editor.setDecorations(loadingDecorationType, []);
    }
}
