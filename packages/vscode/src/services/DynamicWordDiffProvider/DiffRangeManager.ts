import {uniq, uniqBy} from 'lodash';
import * as vscode from 'vscode';

/**
 * 管理 wordDiff 计算过程的所有位置相关的状态，包括临时增加的行、当前高亮的行，要替换的范围等
 */
export class DiffRangeManager {
    // 标识当前对比到了哪一行，这一行会高亮
    private _activeLine: number;

    private _replaceRange: vscode.Range;

    /** 下面涉及行相关的范围默认都是 从 0 到 MAX_SAFE_INTEGER，不涉及后面的换行符 */
    deleteLineRanges: vscode.Range[] = [];
    insertLineRanges: vscode.Range[] = [];

    insertWordRanges: vscode.Range[] = [];
    deleteWordRanges: vscode.Range[] = [];


    constructor(
        replaceRange: vscode.Range,
        readonly document: vscode.TextDocument
    ) {
        this._replaceRange = replaceRange;
        this._activeLine = this._replaceRange.start.line;
    }

    /** 根据文档变化调整位置信息 */
    onDidDocumentChange(changes: readonly vscode.TextDocumentContentChangeEvent[]) {
        for (const change of changes) {
            const linesAdded = change.text.split('\n').length - 1;
            const linesDeleted = change.range.end.line - change.range.start.line;
            const lineDelta = linesAdded - linesDeleted;
            this.insertWordRanges = this.adjustWordPosition(this.insertWordRanges, change, lineDelta);
            this.deleteWordRanges = this.adjustWordPosition(this.deleteWordRanges, change, lineDelta);
            this.insertLineRanges = this.adjustLineRanges(this.insertLineRanges, change, lineDelta);
            this.deleteLineRanges = this.adjustLineRanges(this.deleteLineRanges, change, lineDelta);
            this.adjustDiffRange(change, lineDelta);
        }
        this.uniqLines();
        this.filterOverlapWords();
    }

    addInsertLine(range: vscode.Range) {
        this.insertLineRanges.push(range);
    }

    addDeleteLine(range: vscode.Range) {
        this.deleteLineRanges.push(range);
    }

    addInsertWord(wordRange: vscode.Range) {
        this.insertWordRanges.push(wordRange);
    }

    addDeleteWord(wordRange: vscode.Range) {
        this.deleteWordRanges.push(wordRange);
    }

    clear() {
        this.insertWordRanges = [];
        this.deleteWordRanges = [];
        this.insertLineRanges = [];
        this.deleteLineRanges = [];
    }

    moveActiveLine(offset = 1) {
        this._activeLine += offset;
    }

    get activeLine() {
        return this._activeLine;
    }

    get startLine() {
        return this._replaceRange.start.line;
    }

    get endLine() {
        return this._replaceRange.end.line;
    }

    get replaceRange() {
        return this._replaceRange;
    }

    get difference() {
        return this.deleteLineRanges.length > 0 || this.insertLineRanges.length > 0 || this.modifyLineRanges.length > 0;
    }

    /**
     * 按行调整要删除行和要插入行的范围，默认所有 range 都是一整行
     */
    private adjustLineRanges(
        ranges: vscode.Range[],
        change: vscode.TextDocumentContentChangeEvent,
        lineDelta: number
    ) {
        const res = [];
        for (const range of ranges) {
            // 涉及第一个字符，直接不要了
            if (change.range.start.isBefore(range.start)
                && (change.range.end.isAfter(range.start) || change.range.end.isEqual(range.start))
            ) {
                continue;
            }
            const lineIndex = this.adjustPosition(range.start, change, lineDelta).line;
            // 永远都是当前行的所有字符
            const newRange = new vscode.Range(
                lineIndex,
                0,
                lineIndex,
                this.document.lineAt(lineIndex).range.end.character
            );
            res.push(newRange);
        }
        return res;
    }

    /**
     * 调整删除/插入单词位置，这里假设所有的单词都是一行内，如果跨行的话，计算会比较麻烦
     *
     * @param ranges 需要调整位置的单词范围数组
     * @param change 文本内容变更事件
     * @param lineDelta 行数变更量
     * @returns 调整后的单词范围数组
     */
    // eslint-disable-next-line complexity
    private adjustWordPosition(
        ranges: vscode.Range[],
        change: vscode.TextDocumentContentChangeEvent,
        lineDelta: number
    ) {
        const res = [];
        for (const range of ranges) {
            const intersection = change.range.intersection(range);
            // 有交集的都去掉
            if (intersection && !intersection.isEmpty) {
                continue;
            }
            // 其他情况就调整位置就行
            res.push(new vscode.Range(
                this.adjustPosition(range.start, change, lineDelta),
                this.adjustPosition(range.end, change, lineDelta)
            ));
        }
        return res;
    }

    private adjustPosition(
        position: vscode.Position,
        change: vscode.TextDocumentContentChangeEvent,
        lineDelta: number
    ) {
        const start = change.range.start;
        const end = change.range.end;
        if (position.isBefore(start) || position.isEqual(start)) {
            return position;
        }
        // 中间
        else if (position.isBefore(end) && position.isAfter(start)) {
            return start;
        }
        // 剩下的都是修改在 position 前面
        else {
            if (end.line < position.line) {
                return position.translate(lineDelta);
            }
            const addedLines = change.text.split('\n');
            const lastLineLength = (addedLines[addedLines.length - 1] ?? '').length;
            const characterDelta = addedLines.length > 1
                ? lastLineLength - change.range.end.character
                : change.range.start.character - change.range.end.character + lastLineLength;
            return position.translate(lineDelta, characterDelta);
        }
    }

    private adjustDiffRange(
        change: vscode.TextDocumentContentChangeEvent,
        lineDelta: number
    ) {
        const diffRange = this._replaceRange;
        this._replaceRange = new vscode.Range(
            this.adjustPosition(diffRange.start, change, lineDelta),
            this.adjustPosition(diffRange.end, change, lineDelta)
        );
    }

    private uniqLines() {
        this.insertLineRanges = uniqBy(this.insertLineRanges, v => v.start.line);
        this.deleteLineRanges = uniqBy(this.deleteLineRanges, v => v.start.line);
        // 删除优先
        this.insertLineRanges = this.insertLineRanges
            .filter(v => !this.deleteLineRanges.some(d => d.intersection(v)));
    }

    private filterOverlapWords() {
        const deleteOrAddLine = [...this.deleteLineRanges, ...this.insertLineRanges];
        this.insertWordRanges = this.insertWordRanges
            .filter(v => !deleteOrAddLine.some(d => d.intersection(v)));
        this.deleteWordRanges = this.deleteWordRanges
            .filter(v => !deleteOrAddLine.some(d => d.intersection(v)));
    }

    get modifyLineRanges() {
        return uniq([...this.insertWordRanges, ...this.deleteWordRanges].map(v => v.start.line))
            .map(line => new vscode.Range(line, 0, line, Number.MAX_SAFE_INTEGER));
    }
}
