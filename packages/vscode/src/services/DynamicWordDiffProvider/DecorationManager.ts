import * as vscode from 'vscode';
import {compare} from 'compare-versions';
import {
    greenDecorationType,
    indexDecorationType,
    redDecorationType,
} from '../InlineChatProvider/types';
import {LoadingDecoration} from './LoadingDecoration';
import {DiffRangeManager} from './DiffRangeManager';

export const addWordDecorationType = vscode.window.createTextEditorDecorationType({
    backgroundColor: {id: 'diffEditor.insertedLineBackground'},
    outlineWidth: '1px',
    outlineStyle: 'solid',
    outlineColor: {id: 'diffEditor.insertedLineBackground'},
    rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
});

export const deleteWordDecorationType = vscode.window.createTextEditorDecorationType({
    backgroundColor: {id: 'diffEditor.removedLineBackground'},
    textDecoration: 'line-through',
    outlineWidth: '1px',
    outlineStyle: 'solid',
    outlineColor: {id: 'diffEditor.removedLineBackground'},
    rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
});

export const modifyDecorationType = vscode.window.createTextEditorDecorationType({
    isWholeLine: true,
    backgroundColor: 'rgba(191, 136, 3, 0.1)',
    outlineWidth: '1px',
    outlineStyle: 'solid',
    outlineColor: 'rgba(191, 136, 3, 0.1)',
    rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
});

// TODO: 低于 1.80 的版本 没有 diffEditor 相关的颜色
const loadingBgColor = compare(vscode.version, '1.80', '<')
    ? new vscode.ThemeColor('dropdown.border')
    : new vscode.ThemeColor('diffEditor.unchangedCodeBackground');

export const bgDecorationType = vscode.window.createTextEditorDecorationType({
    isWholeLine: true,
    backgroundColor: loadingBgColor,
    rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
});

/**
 * 管理 decoration 相关的操作，只涉及展示与更新，不涉及任何位置计算
 * 位置信息都从 DiffRangeManager 获取，为什么不放在一起？看着不爽
 */
export class DecorationManager {
    private readonly loadingDecoration: LoadingDecoration;
    private disposed = false;

    constructor(
        private readonly editor: vscode.TextEditor,
        private readonly diffRangeManager: DiffRangeManager,
        // 流式更新时的动态动效
        private readonly active: boolean = false
    ) {
        this.loadingDecoration = new LoadingDecoration(editor, this.diffRangeManager);
    }

    /**
     * 更新 loading 动效
     */
    updateLoadingAnimation() {
        if (this.disposed) {
            return;
        }

        if (!this.active) {
            const startLine = this.diffRangeManager.startLine;
            const endLine = this.diffRangeManager.endLine;
            const range = new vscode.Range(startLine, 0, endLine, Number.MAX_SAFE_INTEGER);
            this.editor.setDecorations(bgDecorationType, [range]);
            return;
        }

        const activeLine = this.diffRangeManager.activeLine;
        const endLine = this.diffRangeManager.endLine;
        const activeLineRange = new vscode.Range(activeLine, 0, activeLine, Number.MAX_SAFE_INTEGER);

        // 初始时第一行改成闪烁的效果
        if (activeLine === this.diffRangeManager.startLine) {
            this.loadingDecoration.show();
        }
        else {
            this.loadingDecoration.hide();
            this.editor.setDecorations(indexDecorationType, [activeLineRange]);
        }

        const decorations = endLine > activeLine
            ? [new vscode.Range(activeLine, 0, endLine, Number.MAX_SAFE_INTEGER)]
            : [];
        this.editor.setDecorations(bgDecorationType, decorations);
    }

    updateWordDecorations() {
        if (this.disposed) {
            return;
        }
        const addedWordRanges = this.diffRangeManager.insertWordRanges;
        const deletedWordRanges = this.diffRangeManager.deleteWordRanges;
        this.editor.setDecorations(addWordDecorationType, addedWordRanges);
        this.editor.setDecorations(deleteWordDecorationType, deletedWordRanges);
    }

    updateInsertLineDecorations() {
        if (this.disposed) {
            return;
        }
        const addedLineRanges = this.diffRangeManager.insertLineRanges;
        this.editor.setDecorations(greenDecorationType, addedLineRanges);
    }

    updateDeleteLineDecorations() {
        if (this.disposed) {
            return;
        }
        const deletedLineRanges = this.diffRangeManager.deleteLineRanges;
        this.editor.setDecorations(redDecorationType, deletedLineRanges);
    }

    updateModifyLineDecorations() {
        if (this.disposed) {
            return;
        }
        this.updateWordDecorations();
        const modifiedLineRanges = this.diffRangeManager.modifyLineRanges;
        this.editor.setDecorations(modifyDecorationType, modifiedLineRanges);
    }

    updateDiffDecorations() {
        this.updateInsertLineDecorations();
        this.updateDeleteLineDecorations();
        this.updateModifyLineDecorations();
        this.updateWordDecorations();
    }

    destroy() {
        this.disposed = true;
        this.clearWordDecorations();
        this.clearLineDecorations();
        this.clearAnimation();
    }

    clearWordDecorations() {
        this.editor.setDecorations(addWordDecorationType, []);
        this.editor.setDecorations(deleteWordDecorationType, []);
    }

    clearLineDecorations() {
        this.editor.setDecorations(greenDecorationType, []);
        this.editor.setDecorations(redDecorationType, []);
        this.editor.setDecorations(modifyDecorationType, []);
    }

    clearAnimation() {
        this.loadingDecoration.hide();
        this.editor.setDecorations(bgDecorationType, []);
        this.editor.setDecorations(indexDecorationType, []);
    }
}
