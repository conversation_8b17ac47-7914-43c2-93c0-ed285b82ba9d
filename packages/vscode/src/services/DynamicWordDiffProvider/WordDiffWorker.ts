import * as vscode from 'vscode';
import {diffWordsWithSpace} from 'diff';
import {error} from '@/common/outputChannel';
import {matchLine} from '../InlineChatProvider/streamDiff';
import {deleteLines, deleteRanges, insertLine, insertTextBeforePosition} from './utils';
import {DiffRangeManager} from './DiffRangeManager';
import {DecorationManager} from './DecorationManager';

export interface WorkerOptions {
    activeAnimation?: boolean;
    onCancel?: (reason: string) => void;
    onInVisibleChange?: () => void;
}

// loading 表示采纳交互出来前，diff 表示采纳或拒绝前
// cancelled 表示需要把编辑恢复 aborted 表示用户主动编辑了不需要恢复
type WorkerStatus = 'loading' | 'running' | 'diff' | 'accepted' | 'rejected' | 'error' | 'cancelled' | 'aborted';

const VISIBLE_STATUS = ['loading', 'running', 'diff'];

/**
 * 在编辑器中提供行间对比和相似行单词高亮对比
 * 每个 editor 同一个时刻只能有一个实例运行，否则会影响交互
 */
export class WordDiffWorker {
    _status: WorkerStatus = 'loading';
    // 状态为 finished 时有可能有代码还没删除完，所以需要加一个状态标记
    active: boolean = true;

    // 需要对比的原始文本
    private originalLines: string[];
    // 表示当前与原始文本对比到了哪一行，后面生成的结果基于这一行开始 diff，这是个相对原始文本的 offset
    private relativeDiffLine: number = 0;

    private readonly diffRangeManager: DiffRangeManager;
    private readonly decorationManager: DecorationManager;

    static create(
        editor: vscode.TextEditor,
        replaceRange: vscode.Range,
        options?: WorkerOptions
    ) {
        return new WordDiffWorker(editor, replaceRange, options);
    }

    constructor(
        readonly editor: vscode.TextEditor,
        replaceRange: vscode.Range,
        readonly options?: WorkerOptions
    ) {
        const originalText = editor.document.getText(replaceRange);
        this.originalLines = originalText.split('\n');
        this.diffRangeManager = new DiffRangeManager(replaceRange, editor.document);
        this.decorationManager = new DecorationManager(
            editor,
            this.diffRangeManager,
            options?.activeAnimation
        );
        this.decorationManager.updateLoadingAnimation();
    }

    async run(lineGenerator: AsyncGenerator<string> | string) {
        this.status = 'running';
        // 开始对比的时候更新一下原始文本，可能会新增一些空格换行
        const originalText = this.editor.document.getText(this.diffRangeManager.replaceRange);
        this.originalLines = originalText.split('\n');

        this.decorationManager.updateLoadingAnimation();
        await this.diff(lineGenerator);
        if (this.status !== 'running') {
            return false;
        }
        if (this.relativeDiffLine < this.originalLines.length) {
            this.deleteLines(this.originalLines.length - this.relativeDiffLine);
        }
        this.decorationManager.clearAnimation();
        this.status = 'diff';
        return true;
    }

    /**
     * 按行进行 diff
     */
    private async diff(text: AsyncGenerator<string> | string) {
        if (typeof text === 'string') {
            const lines = text.split('\n');
            for (const line of lines) {
                if (this.status !== 'running') {
                    return;
                }
                await this.nextLineDiff(line);
            }
            return;
        }
        for await (const line of text) {
            if (this.status !== 'running') {
                return;
            }
            await this.nextLineDiff(line);
        }
    }

    /**
     * 按行处理生成结果，如果一开始某一行对比错了，后面所有行都有可能受影响
     */
    private async nextLineDiff(line: string) {
        // 对比完了要替换的所有行，后面的都是新增的
        if (this.isLastLine()) {
            await this.insertLineAfter(line);
            this.moveToNextLine();
            return;
        }
        const remainingLines = this.originalLines.slice(this.relativeDiffLine);
        const {isPerfectMatch, matchIndex} = matchLine(line, remainingLines);
        // 匹配不上原始文本，这一行直接插入
        if (matchIndex === -1) {
            await this.insertLineAbove(line);
            this.moveToNextLine();
        }
        else {
            this.deleteLines(matchIndex);
            if (isPerfectMatch === false) {
                await this.sequenceDiffWith(line);
            }
            this.relativeDiffLine += 1;
            if (!this.isLastLine()) {
                this.moveToNextLine();
            }
        }
    }

    async acceptDiff() {
        if (this.finished) {
            return;
        }
        this.status = 'accepted';
        this.decorationManager.destroy();
        await this.acceptLinesAndWords();
        this.active = false;
    }

    /** 清空高亮并且恢复原始文本 */
    async cancel(msg: string) {
        if (this.finished) {
            return;
        }
        this.status = 'cancelled';
        this.options?.onCancel?.(msg);
        await this.reset();
        this.active = false;
    }

    /** 清空高亮并且恢复原始文本 */
    async reject() {
        if (this.finished) {
            return;
        }
        this.status = 'rejected';
        await this.reset();
        this.active = false;
    }

    /** 只清空高亮 */
    abort() {
        this.status = 'aborted';
        this.active = false;
        return this.clearHighlight();
    }

    private async reset() {
        this.decorationManager.destroy();
        await this.removeTmpLinesAndWords();
    }

    private clearHighlight() {
        this.decorationManager.destroy();
        this.diffRangeManager.clear();
    }

    private async acceptLinesAndWords() {
        const {deleteLineRanges, deleteWordRanges} = this.diffRangeManager;
        this.diffRangeManager.clear();
        try {
            await deleteRanges(this.editor, deleteWordRanges);
            await deleteLines(this.editor, deleteLineRanges);
        }
        catch (e: any) {
            error('Error while accepting WordDiff temporary lines and words', e.message);
        }
    }

    private async removeTmpLinesAndWords() {
        const {insertWordRanges, insertLineRanges} = this.diffRangeManager;
        this.diffRangeManager.clear();
        try {
            await deleteRanges(this.editor, insertWordRanges);
            await deleteLines(this.editor, insertLineRanges);
        }
        catch (e: any) {
            error('Error while removing WordDiff temporary lines and words', e.message);
        }
    }

    /**
     * 与当前高亮行进行单词级别的 diff 并移动到下一行
     *
     * @param newLine 要进行比较的行内容
     * @returns
     */
    private async sequenceDiffWith(newLine: string) {
        const diffLine = this.originalLines[this.relativeDiffLine];
        const changes = diffWordsWithSpace(diffLine, newLine);

        let res = '';
        for (const change of changes) {
            const {added, value, removed} = change;
            const offsetStart = res.length;
            const offsetEnd = offsetStart + value.length;
            if (removed) {
                this.diffRangeManager.addDeleteWord(
                    new vscode.Range(this.activeLine, offsetStart, this.activeLine, offsetEnd)
                );
            }
            else if (added) {
                await insertTextBeforePosition(
                    this.editor,
                    new vscode.Position(this.activeLine, offsetStart),
                    value
                );
                this.diffRangeManager.addInsertWord(
                    new vscode.Range(this.activeLine, offsetStart, this.activeLine, offsetEnd)
                );
            }
            res += value;
        }
        this.decorationManager.updateModifyLineDecorations();
    }

    /**
     * 在当前高亮行上方插入一行文本，换行符写死了 \n，所以要插入的文本用 \n 切分，这样不影响原本的格式
     *
     * @param line 要插入的文本内容
     * @returns
     */
    private async insertLineAbove(line: string) {
        await insertLine(this.editor, this.activeLine, line);
        const textLine = this.editor.document.lineAt(this.activeLine);
        if (this.status === 'running') {
            this.diffRangeManager.addInsertLine(textLine.range);
        }
        // 写入后发现已经取消了，需要把新插入的行删除掉，因为 diffRangeManager 还没添加，cancel 的那一时刻是识别不出来新增的行的
        else if (this.status === 'cancelled') {
            await deleteRanges(this.editor, [new vscode.Range(this.activeLine, 0, this.activeLine + 1, 0)]);
        }
        this.decorationManager.updateInsertLineDecorations();
        this.decorationManager.updateLoadingAnimation();
    }

    /**
     * 在当前高亮行下方插入一行文本
     *
     * @param line 要插入的文本内容
     * @returns
     */
    private async insertLineAfter(line: string) {
        await insertLine(this.editor, this.activeLine + 1, line);
        const textLine = this.editor.document.lineAt(this.activeLine + 1);
        if (this.status === 'running') {
            this.diffRangeManager.addInsertLine(textLine.range);
        }
        // 写入后发现已经取消了，需要把新插入的行删除掉，因为 diffRangeManager 还没添加，cancel 的那一时刻是识别不出来新增的行的
        else if (this.status === 'cancelled') {
            await deleteRanges(this.editor, [new vscode.Range(this.activeLine + 1, 0, this.activeLine + 2, 0)]);
        }
        this.decorationManager.updateInsertLineDecorations();
        this.decorationManager.updateLoadingAnimation();
    }

    /**
     * 从高亮行往后删除n行（从当前高亮行开始）
     */
    private async deleteLines(num: number) {
        if (num <= 0) {
            return;
        }
        this.relativeDiffLine += num;
        for (let i = 0; i < num; i++) {
            const line = this.editor.document.lineAt(this.activeLine + i);
            this.diffRangeManager.addDeleteLine(line.range);
        }
        this.decorationManager.updateDeleteLineDecorations();
        this.moveLines(num);
    }

    /**
     * 将高亮效果移动到下一行
     */
    private async moveToNextLine() {
        this.diffRangeManager.moveActiveLine();
        this.decorationManager.updateLoadingAnimation();
    }

    /**
     * 将光标向下移动指定行数
     *
     * @param num 要移动的行数
     * @returns
     */
    private async moveLines(num: number) {
        for (let i = 0; i < num; i++) {
            this.moveToNextLine();
        }
    }

    /**
     * 判断是否已经对比完所有行
     */
    private isLastLine() {
        return this.relativeDiffLine >= this.originalLines.length;
    }

    private get activeLine() {
        return this.diffRangeManager.activeLine;
    }

    showActions(document: vscode.TextDocument) {
        if (this.editor.document.uri.toString() !== document.uri.toString()) {
            return false;
        }
        return this.status === 'diff';
    }

    get diffRange() {
        return this.diffRangeManager.replaceRange;
    }

    get finished() {
        return this.status !== 'diff' && this.status !== 'loading' && this.status !== 'running';
    }

    get difference() {
        return this.diffRangeManager.difference;
    }

    get status() {
        return this._status;
    }

    set status(value: WorkerStatus) {
        if (VISIBLE_STATUS.includes(this._status)
            && !VISIBLE_STATUS.includes(value)
            && this.options?.onInVisibleChange
        ) {
            this.options.onInVisibleChange();
        }
        this._status = value;
    }

    onDocumentChange(e: vscode.TextDocumentChangeEvent) {
        this.diffRangeManager.onDidDocumentChange(e.contentChanges);
        this.decorationManager.updateDiffDecorations();
    }
}
