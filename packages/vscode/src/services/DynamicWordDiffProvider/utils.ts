import * as vscode from 'vscode';

export function insertLine(editor: vscode.TextEditor, lineIndex: number, line: string) {
    return editor.edit(
        editBuilder => {
            const lineCount = editor.document.lineCount;
            if (lineIndex < lineCount) {
                editBuilder.insert(new vscode.Position(lineIndex, 0), `${line}\n`);
                return;
            }
            editBuilder.insert(editor.document.lineAt(lineCount - 1).range.end, `\n${line}`);
        },
        {
            undoStopAfter: false,
            undoStopBefore: false,
        }
    );
}

export function deleteRanges(editor: vscode.TextEditor, ranges: vscode.Range[]) {
    return editor.edit(
        editBuilder => {
            for (const range of ranges) {
                editBuilder.delete(range);
            }
        },
        {
            undoStopAfter: false,
            undoStopBefore: false,
        }
    );
}

/**
 * 将 range 范围内的行删除，包括后面的换行符
 */
export async function deleteLines(editor: vscode.TextEditor, ranges: vscode.Range[]) {
    const lineRanges = ranges.map(range => new vscode.Range(range.start.line, 0, range.end.line + 1, 0));
    // 尝试三次
    for (let i = 0; i < 3; i++) {
        const success = await deleteRanges(editor, lineRanges);
        // 如果删除失败，有可能是编辑冲突了，一个常见的例子是插件在粘贴代码后会自动增加缩进，很容易导致编辑冲突
        if (!success) {
            continue;
        }
        return success;
    }
    return false;
}

export function insertTextBeforePosition(editor: vscode.TextEditor, position: vscode.Position, text: string) {
    return editor.edit(
        editBuilder => editBuilder.replace(position, text),
        {
            undoStopAfter: false,
            undoStopBefore: false,
        }
    );
}
