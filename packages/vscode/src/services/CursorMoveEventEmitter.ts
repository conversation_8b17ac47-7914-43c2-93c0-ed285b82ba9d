import {injectable} from 'inversify';
import * as vscode from 'vscode';
import {SelectedLocation} from './types';

@injectable()
export class CursorMoveEventEmitter implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    private readonly listeners = new Map<number, vscode.EventEmitter<SelectedLocation>>();
    private documentChangeTime = 0;
    private readonly pendingHandlers: NodeJS.Timeout[] = [];

    constructor() {
        this.disposables.push(
            vscode.window.onDidChangeTextEditorSelection(this.handleTextEditorSelectionChange, this),
            vscode.workspace.onDidChangeTextDocument(this.handleTextDocumentChange, this),
            vscode.window.onDidChangeActiveTextEditor(this.clearPendingHandlers, this)
        );
    }

    /**
     * 注册一个回调函数，当光标被手动移动时调用。
     *
     * @param callback - 当光标移动时调用的函数。它接收一个 `SelectedLocation` 对象作为参数。
     * @param debounce - 可选。防抖时间，单位为毫秒。默认为 1000 毫秒。
     */
    onDidMoveCursorManually(callback: (location: SelectedLocation) => void, debounce = 1000): vscode.Disposable {
        const eventEmitter = this.listeners.get(debounce) ?? new vscode.EventEmitter<SelectedLocation>();
        this.listeners.set(debounce, eventEmitter);
        const disposable = eventEmitter.event(callback);
        this.disposables.push(disposable);
        return disposable;
    }

    private handleTextEditorSelectionChange(e: vscode.TextEditorSelectionChangeEvent) {
        this.clearPendingHandlers();
        const currentUri = e.textEditor.document.uri;
        if (e.kind === undefined) {
            // 忽略鼠标拖拽等非手动移动
            return;
        }
        if (e.selections.length > 1 || e.selections.some(item => !item.start.isEqual(item.end))) {
            // 忽略多光标或者划选的情况
            return;
        }
        if (Date.now() - this.documentChangeTime < 100) {
            // 当光标变更和文档变更的间隔小于100ms时，认为是编辑带来的移动，而非用户自主切换光标位置
            return;
        }
        for (const [debounce, emitter] of this.listeners.entries()) {
            const handler = setTimeout(
                () => {
                    emitter.fire({uri: currentUri, selection: e.textEditor.selection, kind: e.kind});
                },
                debounce
            );
            this.pendingHandlers.push(handler);
        }
    }

    private handleTextDocumentChange(e: vscode.TextDocumentChangeEvent) {
        if (e.document.uri.scheme !== 'file') {
            return;
        }
        this.documentChangeTime = Date.now();
        this.clearPendingHandlers();
    }

    private clearPendingHandlers() {
        this.pendingHandlers.forEach(clearTimeout);
        this.pendingHandlers.length = 0;
    }

    dispose() {
        this.clearPendingHandlers();
        this.disposables.forEach(d => d.dispose());
        this.disposables.length = 0;
    }
}
