import * as vscode from 'vscode';
import {RawGenerateCode} from '../../api';
import {iocContainer} from '../../iocContainer';
import {asyncIterableMapFilter} from '../../utils/asyncIterators';
import {ComateStatusBar} from '../StatusBar';
import {UserService} from '../UserService';
import {extractPrompt} from '../../common/prompt/extractPrompt';
import {fetchAndStreamCompletions} from '../../common/Fetcher';
import {ComateListDocument} from './ComateListDocument';
import {CompletionContext, CompletionType} from './CompletionContext';

export interface RawSolutionItem {
    displayText: string;
    rawData: RawGenerateCode;
    prependToCompletion: string;
}

export interface SuccessSolutionReturn {
    status: 'Solution';
    solutions: RawSolutionItem[];
    next: Promise<NextSolutionReturn>;
}

export type NextSolutionReturn =
    | {status: 'FinishedWithError', error: string}
    | {status: 'FinishedNormally'}
    | SuccessSolutionReturn;

async function next(
    statusBar: ComateStatusBar,
    token: vscode.CancellationToken,
    solutionResult: AsyncGenerator<RawSolutionItem[], void, void>
): Promise<NextSolutionReturn> {
    if (token.isCancellationRequested) {
        statusBar.removeProgress();
        return {
            status: 'FinishedWithError',
            error: 'Cancelled',
        };
    }

    const result = await solutionResult.next();

    if (result.done === true) {
        statusBar.removeProgress();
        return {
            status: 'FinishedNormally',
        };
    }

    return {
        status: 'Solution',
        solutions: result.value,
        next: next(statusBar, token, solutionResult),
    };
}

export async function launchSolutions(
    completionContext: CompletionContext,
    listDocument: ComateListDocument
): Promise<NextSolutionReturn> {
    const insertPosition = completionContext.insertPosition;
    const prependToCompletion = completionContext.prependToCompletion;
    const document = await listDocument.getDocument();
    const prompt = extractPrompt(document, insertPosition);
    if (prompt.type === 'contextTooShort') {
        return {
            status: 'FinishedWithError',
            error: 'Context too short',
        };
    }

    if (document.lineCount > 8000) {
        return {
            status: 'FinishedWithError',
            error: 'skip long file',
        };
    }

    const userService = iocContainer.get(UserService);
    const statusBar = iocContainer.get(ComateStatusBar);
    statusBar.setProgress();
    const processResult = await fetchAndStreamCompletions(
        document,
        insertPosition,
        userService,
        listDocument.solutionCountTarget
    );

    if (processResult.type !== 'success') {
        return {
            status: 'FinishedWithError',
            error: processResult.reason,
        };
    }

    const token = listDocument.getCancellationToken();

    const mapFilterResult = asyncIterableMapFilter(
        processResult.value,
        async batchChoices => {
            return batchChoices.map(data => {
                let displayText = data.content;
                if (completionContext.completionType === CompletionType.OPEN_COMATE) {
                    const lineText = document.lineAt(insertPosition);
                    const prefix = lineText.text.slice(0, insertPosition.character);
                    displayText = prefix + displayText;
                }
                return {
                    displayText,
                    rawData: data,
                    prependToCompletion: prependToCompletion,
                };
            });
        }
    );

    return next(statusBar, token, mapFilterResult);
}
