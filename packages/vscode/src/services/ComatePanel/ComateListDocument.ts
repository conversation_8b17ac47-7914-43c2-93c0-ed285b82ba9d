import * as vscode from 'vscode';
import {L10n} from '@/common/L10nProvider/L10n';
import {CompletionText} from '@/common/L10nProvider/constants';
import {TelemetryData} from '@/common/telemetry';
import {showCode} from '../../api';
import {debounce} from '../../utils/Debouncer';
import {info} from '../../common/outputChannel';
import {CompletionContext} from './CompletionContext';
import {launchSolutions, NextSolutionReturn, RawSolutionItem} from './launchSolutions';

function normalizeCompletionText(completionText: string) {
    return completionText.replace(/\s+/g, '');
}

interface SolutionItem {
    uuid: string;
    displayLines: string[];
    completionText: string;
    score: number;
}

type Handler = (uri: vscode.Uri) => void;

export class ComateListDocument {
    static separator: string;

    startPosition: vscode.Position;
    numberHeaderLines: number;
    private _wasCancelled: boolean = false;
    private _solutionCount: number = 0;
    private readonly _displaySolutions = new Set<string>();
    private readonly _solutions: SolutionItem[] = [];
    private readonly _updateHandlers = new Set<Handler>();
    savedTelemetryData = TelemetryData.createAndMarkAsIssued();
    onDidResultUpdated: (handler: Handler) => {dispose: () => void};
    debouncedEventFire: () => Promise<void>;

    constructor(
        private readonly _uri: vscode.Uri,
        private readonly targetDocument: vscode.TextDocument,
        readonly completionContext: CompletionContext,
        readonly solutionCountTarget: number,
        private readonly token: vscode.CancellationToken
    ) {
        this.debouncedEventFire = debounce(
            10,
            () => {
                this._updateHandlers.forEach(handler => {
                    handler(this._uri);
                });
            }
        );

        this.onDidResultUpdated = (handler: Handler) => {
            this._updateHandlers.add(handler);
            return {
                dispose: () => {
                    this._updateHandlers.delete(handler);
                },
            };
        };
        this.startPosition = this.completionContext.insertPosition;
        this.numberHeaderLines = Math.max(1, this.formatDisplayLines('').length - 1);
    }

    async getDocument() {
        return this.targetDocument;
    }

    get targetUri() {
        return this.targetDocument.uri;
    }

    header() {
        if (this._wasCancelled) {
            return L10n.t(CompletionText.PANEL_EMPTY);
        }
        const description = this._solutionCount - this._solutions.length > 0
            ? ` (${L10n.t(CompletionText.PANEL_HIDDEN)})`
            : '';
        const prefix = L10n.t(CompletionText.PANEL_SYNTHESIZING);
        return `${prefix} ${this._solutionCount}/${this.solutionCountTarget} ${description}`;
    }

    areSolutionsDuplicates(a: SolutionItem, b: SolutionItem) {
        return normalizeCompletionText(a.completionText) === normalizeCompletionText(b.completionText);
    }

    reportCancelled() {
        this._wasCancelled = true;
        this.debouncedEventFire();
    }

    getCancellationToken() {
        return this.token;
    }

    insertSorted(item: SolutionItem) {
        if (/^\s*$/.test(item.completionText)) {
            return;
        }
        for (let n = 0; n < this._solutions.length; n++) {
            const current = this._solutions[n];
            if (this.areSolutionsDuplicates(current, item)) {
                if (current.score > item.score) {
                    this._solutions.splice(n, 1);
                    break;
                }
                return;
            }
        }
        for (let n = 0; n < this._solutions.length; n++) {
            const current = this._solutions[n];
            if (current.score > item.score) {
                this._solutions.splice(n, 0, item);
                return;
            }
        }
        this._solutions.push(item);
    }

    insertSolution(data: RawSolutionItem[]) {
        data.forEach(item => {
            const solution = {
                uuid: item.rawData.uuid,
                displayLines: this.formatDisplayLines(item.displayText),
                completionText: item.rawData.content,
                score: Math.abs(item.rawData.score),
            };
            this.insertSorted(solution);
            this._solutionCount++;
        });
        this.debouncedEventFire();
    }

    formatDisplayLines(displayText: string) {
        return `${ComateListDocument.separator}\n\n${displayText}`.split('\n');
    }

    async runQuery() {
        const result = await launchSolutions(this.completionContext, this);
        this.processNextSolution(result);
    }

    async processNextSolution(resultSSE: NextSolutionReturn) {
        switch (resultSSE.status) {
            case 'FinishedNormally':
                info(
                    `all solution returned. success ${this._solutionCount}, `,
                    `fail ${this.solutionCountTarget - this._solutionCount}`
                );
                return;
            case 'FinishedWithError':
                return;
            case 'Solution':
                this.insertSolution(resultSSE.solutions);
                this.processNextSolution(await resultSSE.next);
        }
    }

    solutionsReceived() {
        return this._solutionCount;
    }

    solutions() {
        return this._solutions;
    }

    get value() {
        for (const solution of this._solutions) {
            if (!this._displaySolutions.has(solution.uuid)) {
                showCode(solution.uuid);
                this._displaySolutions.add(solution.uuid);
            }
        }
        const solutionLines = this._solutions.flatMap(solution => solution.displayLines);
        return [this.header(), ...solutionLines].join('\n');
    }
}
ComateListDocument.separator = '\n=======';
