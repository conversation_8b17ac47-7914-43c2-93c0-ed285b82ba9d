/**
 * @file 多条推荐显示面板
 */

import {Container} from 'inversify';
import * as vscode from 'vscode';
import {L10n} from '@/common/L10nProvider/L10n';
import {CompletionText} from '@/common/L10nProvider/constants';
import {postInsertionTasks} from '../../common/telemetry/postTasks';
import {acceptCode} from '../../api';
import {COMATE_COMPLETION_SCHEMA} from '../../constants';
import {ComateListDocument} from './ComateListDocument';
import {completionContextForEditor, decodeLocation, encodeLocation} from './CompletionContext';

const CMDAcceptPanelSolution = 'baidu.comate.acceptPanelSolution';

interface DocumentPair {
    model: ComateListDocument;
    cts: vscode.CancellationTokenSource;
}

export class ComatePanel implements vscode.TextDocumentContentProvider, vscode.CodeLensProvider, vscode.Disposable {
    private readonly _onDidChange = new vscode.EventEmitter<vscode.Uri>();
    private readonly _documents = new Map<string, DocumentPair>();
    private readonly _editorDecoration = vscode.window.createTextEditorDecorationType({
        textDecoration: 'underline',
    });
    private disposables: vscode.Disposable[] = [];

    constructor(private readonly _ctx: Container) {
        this.disposables.push(
            vscode.workspace.registerTextDocumentContentProvider(COMATE_COMPLETION_SCHEMA, this),
            vscode.languages.registerCodeLensProvider({scheme: COMATE_COMPLETION_SCHEMA}, this),
            vscode.commands.registerCommand('baidu.comate.generate', () => {
                vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
                this.openPanel();
            }),
            vscode.commands.registerCommand(
                CMDAcceptPanelSolution,
                async (uri: vscode.Uri, position: vscode.Position, text: string, callback: () => void) => {
                    const workspaceEdit = new vscode.WorkspaceEdit();
                    workspaceEdit.insert(uri, position, text);
                    await vscode.workspace.applyEdit(workspaceEdit);
                    callback();
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
            ),
            vscode.workspace.onDidCloseTextDocument(doc => {
                if (doc.isClosed && doc.uri.scheme === COMATE_COMPLETION_SCHEMA) {
                    this._documents.delete(doc.uri.toString());
                }
            }),
            this._onDidChange,
            this._editorDecoration
        );
    }

    get onDidChange() {
        return this._onDidChange.event;
    }

    async provideTextDocumentContent(uri: vscode.Uri) {
        let model = this._documents.get(uri.toString())?.model;
        if (model) {
            return model.value;
        }
        const cts = new vscode.CancellationTokenSource();
        const [newUri, completionContext] = decodeLocation(uri);
        const doc = await vscode.workspace.openTextDocument(newUri);
        model = new ComateListDocument(
            uri,
            doc,
            completionContext,
            10,
            cts.token
        );
        model.onDidResultUpdated(uri => {
            return this._onDidChange.fire(uri);
        });
        this._documents.set(uri.toString(), {
            model: model,
            cts: cts,
        });
        model.runQuery();
        return model.value;
    }

    provideCodeLenses(document: vscode.TextDocument) {
        const value = this._documents.get(document.uri.toString());
        if (value) {
            return this.getCodeLens(value);
        }
        return [];
    }

    private getCodeLens(documentPair: DocumentPair) {
        const modal = documentPair.model;
        let numberHeaderLines = modal.numberHeaderLines;
        const insertPosition = modal.completionContext.insertPosition;
        const separatorLength = ComateListDocument.separator.split('\n').length - 1;
        return modal.solutions().map((solution, index) => {
            const solutionStart = new vscode.Position(numberHeaderLines + separatorLength, 0);
            const solutionEnd = new vscode.Position(numberHeaderLines + solution.displayLines.length - 1, 0);
            const telemetry = modal.savedTelemetryData.extendedBy(
                {},
                {
                    compCharLen: solution.completionText.length,
                    meanProb: 0,
                    rank: index,
                }
            );
            const codeLens = new vscode.CodeLens(new vscode.Range(solutionStart, solutionEnd), {
                title: L10n.t(CompletionText.PANEL_ACCEPT),
                tooltip: L10n.t(CompletionText.PANEL_ACCEPT_TOOLTIP),
                command: CMDAcceptPanelSolution,
                arguments: [
                    modal.targetUri,
                    insertPosition,
                    solution.completionText,
                    async () => {
                        documentPair.cts.cancel();
                        acceptCode({
                            uuid: solution.uuid,
                            accepted: true,
                            content: '',
                        });
                        const offset = (await vscode.workspace.openTextDocument(modal.targetUri))
                            .offsetAt(insertPosition);
                        postInsertionTasks(
                            this._ctx,
                            {
                                uri: modal.targetUri,
                                offset,
                                displayText: solution.completionText,
                                telemetry,
                                uuid: solution.uuid,
                            }
                        );
                    },
                ],
            });
            numberHeaderLines += solution.displayLines.length;
            return codeLens;
        });
    }

    private openPanel() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }
        if (!vscode.workspace.getConfiguration('editor', editor.document.uri).get('codeLens')) {
            vscode
                .window
                .showInformationMessage(
                    L10n.t(CompletionText.PANEL_CODELENS_DISABLED),
                    'Open Settings'
                )
                .then(selection => {
                    if (selection === 'Open Settings') {
                        vscode.commands.executeCommand('workbench.action.openSettings', 'editor.codeLens');
                    }
                });
        }
        const completionContext = completionContextForEditor(editor);
        const uri = encodeLocation(editor.document.uri, completionContext);
        const languageId = editor.document.languageId;
        vscode.workspace.openTextDocument(uri).then(doc => {
            vscode.languages.setTextDocumentLanguage(doc, languageId);
            vscode.window.showTextDocument(doc, vscode.ViewColumn.Beside);
        });
    }

    dispose() {
        this._documents.clear();
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
