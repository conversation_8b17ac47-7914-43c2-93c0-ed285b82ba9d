/**
 * @file information encoded in panel document uri
 */

import {URI} from 'vscode-uri';
import * as vscode from 'vscode';
import {L10n} from '@/common/L10nProvider/L10n';
import {CompletionText} from '@/common/L10nProvider/constants';
import {LocationFactory} from '../Helper/LocationFactory';
import {COMATE_COMPLETION_SCHEMA} from '../../constants';
import {iocContainer} from '../../iocContainer';

export enum CompletionType {
    OPEN_COMATE = 2,
}

export function completionTypeToString(completionType: CompletionType) {
    return completionType === CompletionType.OPEN_COMATE ? 'open comate' : 'unknown';
}

interface CompletionContextData {
    prependToCompletion: string;
    appendToCompletion: string;
    indentation: null;
    completionType: CompletionType;
    insertPosition: vscode.Position;
}

/**
 * information encoded in comate panel uri
 */
export class CompletionContext {
    prependToCompletion: string;
    appendToCompletion: string;
    indentation: null;
    completionType: CompletionType;
    insertPosition: vscode.Position;

    constructor(activePos: vscode.Position, completionType: CompletionType) {
        this.prependToCompletion = '';
        this.appendToCompletion = '';
        this.indentation = null;
        this.completionType = CompletionType.OPEN_COMATE;
        this.insertPosition = iocContainer.get(LocationFactory).position(activePos.line, activePos.character);
        this.completionType = completionType;
    }

    static fromJSONParse(data: CompletionContextData) {
        const position = iocContainer
            .get(LocationFactory)
            .position(data.insertPosition.line, data.insertPosition.character);
        const completionContext = new CompletionContext(position, data.completionType);
        completionContext.prependToCompletion = data.prependToCompletion;
        completionContext.appendToCompletion = data.appendToCompletion;
        completionContext.indentation = data.indentation;
        return completionContext;
    }
}

export function completionContextForDocument(document: vscode.TextDocument, active: vscode.Position) {
    let activePos = active;
    const textLine = document.lineAt(active.line);
    if (!textLine.isEmptyOrWhitespace) {
        activePos = textLine.range.end;
    }
    return new CompletionContext(activePos, CompletionType.OPEN_COMATE);
}

export function completionContextForEditor(editor: vscode.TextEditor) {
    return completionContextForDocument(editor.document, editor.selection.active);
}

let id = 0;

export function encodeLocation(uri: vscode.Uri, completionContext: CompletionContext): vscode.Uri {
    const uriParts = uri.toString().split('#');
    const uriData = uriParts.length > 1 ? uriParts[1] : '';
    const data = JSON.stringify([uriParts[0], completionContext, uriData]);
    const name = L10n.t(CompletionText.PANEL_TITLE);
    // 不知道为什么这里用的 vscode-uri 库而不是 vscode.Uri.parse , 可能是为了适配吧
    return URI.parse(`${COMATE_COMPLETION_SCHEMA}:Baidu%20Comate%20${name}?${data}#${id++}`);
}

export function decodeLocation(uri: vscode.Uri) {
    const [uriString, completionContext, uriData] = JSON.parse(uri.query);
    return [
        URI.parse(uriData.length > 0 ? uriString + '#' + uriData : uriString),
        CompletionContext.fromJSONParse(completionContext),
    ] as const;
}
