import * as vscode from 'vscode';
import {SearchAPIParamType, TrackAPIParamType} from '@shared/protocols';
import {injectable, inject} from 'inversify';
import {error} from '@/common/outputChannel';
import {isInternal} from '@/utils/features';
import {VSCodeConfigProvider} from '@/services/ConfigProvider';
import {UserService} from '../UserService';
import {searchAPIByKeywordAPI, trackClickSearchedApiAPI} from '../../api/smartAPI';

export const CMD_SEARCH_API_BY_KEYWORD = 'baidu.comate.searchAPIByKeyword';
export const CMD_TRACK_CLICK_SEARCHED_API = 'baidu.comate.trackClickSearchedAPI';

@injectable()
export class SmartAPIProvider {
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(UserService) private readonly userService: UserService,
        @inject(VSCodeConfigProvider) private readonly vsCodeConfig: VSCodeConfigProvider
    ) {
        this.disposables.push(
            vscode.commands.registerCommand(CMD_SEARCH_API_BY_KEYWORD, this.searchAPIByKeyword.bind(this))
        );

        this.disposables.push(
            vscode.commands.registerCommand(CMD_TRACK_CLICK_SEARCHED_API, this.trackClickSearchedAPI.bind(this))
        );
    }

    private async getUserName() {
        const [userName] = await this.userService.getCurrentUser();
        const license = this.vsCodeConfig.getLicense();
        return isInternal ? userName : license;
    }

    async searchAPIByKeyword(params: SearchAPIParamType) {
        try {
            const userName = await this.getUserName();
            params.username = userName;
            const res = await searchAPIByKeywordAPI(params);
            return res || [];
        }
        catch (ex) {
            error('error:', (ex as Error).message);
            return [];
        }
    }

    async trackClickSearchedAPI(params: TrackAPIParamType) {
        try {
            const userName = await this.getUserName();
            params.username = userName;
            trackClickSearchedApiAPI(params);
        }
        catch (ex) {
            error('error:', (ex as Error).message);
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
