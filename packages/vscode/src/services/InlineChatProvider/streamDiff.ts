/* eslint-disable complexity */
import {distance} from 'fastest-levenshtein';
import {DiffLine, DiffLineType} from './types';

export type LineStream = AsyncGenerator<string>;

export interface MatchLineResult {
    /**
     * -1 if it's a new line, otherwise the index of the first match
     * in the old lines.
     */
    matchIndex: number;
    isPerfectMatch: boolean;
    newLine: string;
}

function linesMatchPerfectly(lineA: string = '', lineB: string = ''): boolean {
    return lineA === lineB && lineA !== '';
}

const END_BRACKETS = ['}', '});', '})'];

function linesMatch(lineA: string = '', lineB: string = '', linesBetween = 0): boolean {
    // Require a perfect (without padding) match for these lines
    // Otherwise they are edit distance 1 from empty lines and other single char lines (e.g. each other)
    if (['}', '*', '});', '})'].includes(lineA.trim())) {
        return lineA.trim() === lineB.trim();
    }

    const d = distance(lineA, lineB);

    return (
        // Should be more unlikely for lines to fuzzy match if they are further away
        (d / Math.max(lineA.length, lineB.length) < 0.5 - linesBetween * 0.05
            || lineA.trim() === lineB.trim())
        && lineA.trim() !== ''
    );
}

/**
 * Used to find a match for a new line in an array of old lines.
 *
 * Return the index of the first match and whether it is a perfect match
 * Also return a version of the line with correct indentation if needs fixing
 */
export function matchLine(
    newLine: string = '',
    oldLines: string[],
    permissiveAboutIndentation = false
): MatchLineResult {
    // Only match empty lines if it's the next one:
    if (newLine?.trim() === '' && oldLines[0]?.trim() === '') {
        return {
            matchIndex: 0,
            isPerfectMatch: true,
            newLine: newLine?.trim(),
        };
    }

    const isEndBracket = END_BRACKETS.includes(newLine?.trim());

    for (let i = 0; i < oldLines.length; i++) {
        if (i > 4 && isEndBracket) {
            return {matchIndex: -1, isPerfectMatch: false, newLine};
        }

        if (linesMatchPerfectly(newLine, oldLines[i])) {
            return {matchIndex: i, isPerfectMatch: true, newLine};
        }
        if (linesMatch(newLine, oldLines[i], i)) {
            if (
                newLine.trimStart() === oldLines[i].trimStart()
                && (permissiveAboutIndentation || newLine.trim().length > 8)
            ) {
                return {
                    matchIndex: i,
                    isPerfectMatch: true,
                    newLine: oldLines[i],
                };
            }
            return {matchIndex: i, isPerfectMatch: false, newLine};
        }
    }

    return {matchIndex: -1, isPerfectMatch: false, newLine};
}

/**
 * https://blog.jcoglan.com/2017/02/12/the-myers-diff-algorithm-part-1/
 * Invariants:
 * - new + same = newLines.length
 * - old + same = oldLinesCopy.length
 * ^ (above two guarantee that all lines get represented)
 * - Lines are always output in order, at least among old and new separately
 */
export async function* streamDiff(oldLines: string[], newLines: LineStream): AsyncGenerator<DiffLine> {
    const oldLinesCopy = [...oldLines];

    // If one indentation mistake is made, others are likely. So we are more permissive about matching
    let seenIndentationMistake = false;

    let newLineResult = await newLines.next();

    while (oldLinesCopy.length > 0 && !newLineResult.done) {
        const {matchIndex, isPerfectMatch, newLine} = matchLine(
            newLineResult.value,
            oldLinesCopy,
            seenIndentationMistake
        );

        if (!seenIndentationMistake && newLineResult.value !== newLine) {
            seenIndentationMistake = true;
        }

        let type: DiffLineType = 'new';

        let isLineRemoval = false;
        const isNewLine = matchIndex === -1;

        if (isNewLine) {
            type = 'new';
        }
        else {
            // Insert all deleted lines before match
            for (let i = 0; i < matchIndex; i++) {
                yield {type: 'old', line: oldLinesCopy.shift()!};
            }

            type = isPerfectMatch ? 'same' : 'old';
        }

        switch (type) {
            case 'new':
                yield {type, line: newLine};
                break;

            case 'same':
                yield {type, line: oldLinesCopy.shift()!};
                break;

            case 'old':
                yield {type, line: oldLinesCopy.shift()!};

                if (oldLinesCopy[0] === newLine) {
                    isLineRemoval = true;
                }
                else {
                    yield {type: 'new', line: newLine};
                }

                break;

            default:
                console.error(`Error streaming diff, type: ${type}`);
        }

        if (!isLineRemoval) {
            newLineResult = await newLines.next();
        }
    }

    // Once at the edge, only one choice
    if (newLineResult.done && oldLinesCopy.length > 0) {
        for (const oldLine of oldLinesCopy) {
            yield {type: 'old', line: oldLine};
        }
    }

    if (!newLineResult.done && oldLinesCopy.length === 0) {
        yield {type: 'new', line: newLineResult.value};
        for await (const newLine of newLines) {
            yield {type: 'new', line: newLine};
        }
    }
}
