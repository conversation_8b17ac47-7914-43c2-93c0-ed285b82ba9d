import * as vscode from 'vscode';

export function isSelected(activeEditor: vscode.TextEditor) {
    const selection = activeEditor.selection;
    const selectedText = activeEditor.document.getText(selection);
    if (selection.isEmpty || selectedText.trim() === '') {
        return false;
    }
    return true;
}

export function isMultiLine(text: string) {
    return text.includes('\n');
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function removeLanguageTagAndTrimNewline(content: string, addNewLine = true) {
    if (content.startsWith('```')) {
        if (content.length > 6 && content.endsWith('```')) {
            return content.split('\n').slice(1, -1).join('\n').trimEnd() + (addNewLine ? '\n' : '');
        } else {
            return content.split('\n').slice(1).join('\n');
        }
    }

    let content_ = (content || '').trimEnd();

    if (content_) {
        if (content_.startsWith('\n')) {
            content_ = content_.slice(1);
        }

        if (!content_.endsWith('\n') && addNewLine) {
            content_ = content_ + '\n';
        }

        return content_;
    }

    return content_;
}

/**
 * 将用户输入的自然语言作为对应的注释插入到代码中，因为要走后端的注释生成代码逻辑
 * 之后可能废弃
 */
export function addUserInputIntoComment(content: string, row: number, userInput: string, language: string) {
    const lines = content.split('\n');
    let commentSymbol = '';
    let colAdditionLength = 0;

    switch (language) {
        case 'python':
            commentSymbol = '#';
            colAdditionLength = 2;
            break;
        case 'html':
            commentSymbol = '<!--';
            // eslint-disable-next-line no-param-reassign
            userInput += ' -->';
            colAdditionLength = 9;
            break;
        case 'javascript':
        case 'typescript':
        case 'java':
        case 'c':
        case 'cpp':
        default:
            commentSymbol = '//';
            colAdditionLength = 3;
    }

    lines.splice(row, 0, commentSymbol + ' ' + userInput);
    const res = lines.join('\n');
    return {newContent: res, colAdditionLength};
}
