/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/init-declarations */
/* eslint-disable max-statements */
import * as vscode from 'vscode';
import {injectable} from 'inversify';
import {DynamicDiffProvider} from './DynamicDiffProvider';
import {streamDiff} from './streamDiff';

@injectable()
export class DynamicDiffManager {
    private readonly fileHandlerMap: Map<string, DynamicDiffProvider> = new Map();

    private userChangeListener: vscode.Disposable | undefined = undefined;

    createDynamicDiffProvider(filepath: string, startLine: number, endLine: number) {
        if (this.fileHandlerMap.has(filepath)) {
            this.fileHandlerMap.get(filepath)?.clear(false);
            this.fileHandlerMap.delete(filepath);
        }
        const editor = vscode.window.activeTextEditor;
        if (editor?.document.uri.fsPath === filepath) {
            const handler = new DynamicDiffProvider(
                startLine,
                endLine,
                editor,
                this.clearForFilepath.bind(this)
            );
            this.fileHandlerMap.set(filepath, handler);
            return handler;
        }
        else {
            return undefined;
        }
    }

    getHandlerForFile(filepath: string) {
        return this.fileHandlerMap.get(filepath);
    }

    // Creates a listener for document changes by user.
    private enableDocumentChangeListener(): vscode.Disposable | undefined | void {
        if (this.userChangeListener) {
            // Only create one listener per file
            return;
        }

        this.userChangeListener = vscode.workspace.onDidChangeTextDocument(
            event => {
                // Check if there is an active handler for the affected file
                const filepath = event.document.uri.fsPath;
                const handler = this.getHandlerForFile(filepath);
                if (handler) {
                    // If there is an active diff for that file, handle the document change
                    this.handleDocumentChange(event, handler);
                }
            }
        );
    }

    disableDocumentChangeListener() {
        if (this.userChangeListener) {
            this.userChangeListener.dispose();
            this.userChangeListener = undefined;
        }
    }

    private handleDocumentChange(event: vscode.TextDocumentChangeEvent, handler: DynamicDiffProvider) {
        // Loop through each change in the event
        event.contentChanges.forEach(change => {
            // Calculate the number of lines added or removed
            const linesAdded = change.text.split('\n').length - 1;
            const linesDeleted = change.range.end.line - change.range.start.line;
            const lineDelta = linesAdded - linesDeleted;

            // Update the diff handler with the new line delta
            handler.updateLineDelta(
                change.range.start.line,
                lineDelta
            );
        });
    }

    async clearForFilepath(filepath_: string | undefined, accept: boolean) {
        let filepath = filepath_;
        if (!filepath) {
            const activeEditor = vscode.window.activeTextEditor;
            if (!activeEditor) {
                return;
            }
            filepath = activeEditor.document.uri.fsPath;
        }

        const handler = this.fileHandlerMap.get(filepath);
        if (handler) {
            await handler.clear(accept);
            this.fileHandlerMap.delete(filepath);
        }

        this.disableDocumentChangeListener();
    }

    // eslint-disable-next-line complexity
    async streamOutput(oldContent: string, stream: AsyncGenerator<string>, range?: vscode.Range, eol: '\n' | '\r\n' = '\n', onClearRange?: () => void) {
        const editor = vscode.window.activeTextEditor;

        if (!editor) {
            return;
        }

        const filepath = editor.document.uri.fsPath;
        const startLine = range ? range.start.line : editor.selection.start.line;
        const endLine = range ? range.end.line : editor.selection.end.line;

        const diffHandler = this.createDynamicDiffProvider(
            filepath,
            startLine,
            endLine
        );

        diffHandler?.onClearRange(onClearRange);
        
        if (!diffHandler) {
            console.warn('Inline Chat Error: Could not create diff handler');
            return;
        }

        let selectedRange = diffHandler.range;

        if (selectedRange.isEmpty) {
            // If the selection is empty, select current line
            selectedRange = new vscode.Range(
                editor.selection.start.with(undefined, 0),
                editor.selection.end.with(undefined, Number.MAX_SAFE_INTEGER)
            );
        }

        if (editor.selection) {
            // Unselect the range
            editor.selection = new vscode.Selection(
                editor.selection.active,
                editor.selection.active
            );
        }

        try {
            await diffHandler.run(
                streamDiff(
                    oldContent ? oldContent.split(eol) : [],
                    stream
                )
            );

            this.enableDocumentChangeListener();
        }
        catch (e) {
            this.disableDocumentChangeListener();
            vscode.window.showErrorMessage(`Error streaming diff: ${e}`);
            throw e;
        }
    }
}
