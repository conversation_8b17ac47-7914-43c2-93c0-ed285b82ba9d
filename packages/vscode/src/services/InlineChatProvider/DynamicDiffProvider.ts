import * as vscode from 'vscode';
import {
    DecorationTypeRangeManager,
    belowIndexDecorationType,
    greenDecorationType,
    indexDecorationType,
    redDecorationType,
    DiffLine,
} from './types';

export class DynamicDiffProvider implements vscode.Disposable {
    private editor: vscode.TextEditor;
    private readonly startLine: number;
    private readonly endLine: number;
    private currentLineIndex: number;
    // 添加一个变量来存储回调函数
    private storedListener?: () => void;
    private cancelled = false;

    get range(): vscode.Range {
        const startLine = Math.min(this.startLine, this.endLine);
        const endLine = Math.max(this.startLine, this.endLine);
        return new vscode.Range(startLine, 0, endLine, Number.MAX_SAFE_INTEGER);
    }

    private newLinesAdded = 0;

    input?: string;

    constructor(
        startLine: number,
        endLine: number,
        editor: vscode.TextEditor,
        private readonly clearForFilepath: (filepath: string | undefined, accept: boolean) => void,
        input?: string
    ) {
        this.currentLineIndex = startLine;
        this.startLine = startLine;
        this.endLine = endLine;
        this.editor = editor;
        this.input = input;
        this.disposables = [];

        this.redDecorationManager = new DecorationTypeRangeManager(
            redDecorationType,
            this.editor
        );
        this.greenDecorationManager = new DecorationTypeRangeManager(
            greenDecorationType,
            this.editor
        );

        const disposable = vscode.window.onDidChangeActiveTextEditor(editor => {
            // When we switch away and back to this editor, need to re-draw decorations
            if (editor?.document.uri.fsPath === this.filepath) {
                this.editor = editor;
                this.redDecorationManager.applyToNewEditor(editor);
                this.greenDecorationManager.applyToNewEditor(editor);
                this.updateIndexLineDecorations();

                // Handle any lines received while editor was closed
                this.queueDiffLine(undefined);
            }
        });
        this.disposables.push(disposable);
    }

    private get filepath() {
        return this.editor.document.uri.fsPath;
    }

    private deletionBuffer: string[] = [];
    private readonly redDecorationManager: DecorationTypeRangeManager;
    insertedInCurrentBlock = 0;

    private async insertDeletionBuffer() {
        // Don't remove trailing whitespace line
        const totalDeletedContent = this.deletionBuffer.join('\n');
        if (
            totalDeletedContent === ''
            && this.currentLineIndex >= this.endLine + this.newLinesAdded
            && this.insertedInCurrentBlock === 0
        ) {
            return;
        }

        if (this.deletionBuffer.length === 0) {
            this.insertedInCurrentBlock = 0;
            return;
        }

        // Insert the block of deleted lines
        await this.insertTextAboveLine(
            this.currentLineIndex - this.insertedInCurrentBlock,
            totalDeletedContent
        );
        this.redDecorationManager.addLines(
            this.currentLineIndex - this.insertedInCurrentBlock,
            this.deletionBuffer.length
        );
        // Shift green decorations downward
        this.greenDecorationManager.shiftDownAfterLine(
            this.currentLineIndex - this.insertedInCurrentBlock,
            this.deletionBuffer.length
        );

        // Update line index, clear buffer
        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let i = 0; i < this.deletionBuffer.length; i++) {
            this.incrementCurrentLineIndex();
        }
        this.deletionBuffer = [];
        this.insertedInCurrentBlock = 0;
    }

    private incrementCurrentLineIndex() {
        this.currentLineIndex++;
        this.updateIndexLineDecorations();
    }

    private readonly greenDecorationManager: DecorationTypeRangeManager;

    private async insertTextAboveLine(index: number, text: string) {
        await this.editor.edit(
            editBuilder => {
                const lineCount = this.editor.document.lineCount;
                if (index >= lineCount) {
                    // Append to end of file
                    editBuilder.insert(
                        new vscode.Position(
                            lineCount,
                            this.editor.document.lineAt(lineCount - 1).text.length
                        ),
                        `\n${text}`
                    );
                }
                else {
                    editBuilder.insert(new vscode.Position(index, 0), `${text}\n`);
                }
            },
            {
                undoStopAfter: false,
                undoStopBefore: false,
            }
        );
    }

    private async insertLineAboveIndex(index: number, line: string) {
        await this.insertTextAboveLine(index, line);
        this.greenDecorationManager.addLine(index);
        this.newLinesAdded++;
    }

    private async deleteLinesAt(index: number, numLines = 1) {
        const startLine = new vscode.Position(index, 0);
        await this.editor.edit(
            editBuilder => {
                editBuilder.delete(
                    new vscode.Range(startLine, startLine.translate(numLines))
                );
            },
            {
                undoStopAfter: false,
                undoStopBefore: false,
            }
        );
    }

    private updateIndexLineDecorations() {
        if (this.currentLineIndex - this.newLinesAdded >= this.endLine) {
            this.editor.setDecorations(indexDecorationType, []);
            this.editor.setDecorations(belowIndexDecorationType, []);
        }
        else {
            const start = new vscode.Position(this.currentLineIndex, 0);
            this.editor.setDecorations(indexDecorationType, [
                new vscode.Range(
                    start,
                    new vscode.Position(start.line, Number.MAX_SAFE_INTEGER)
                ),
            ]);
            const end = new vscode.Position(this.endLine, 0);
            this.editor.setDecorations(belowIndexDecorationType, [
                new vscode.Range(start.translate(1), end.translate(this.newLinesAdded)),
            ]);
        }
    }

    private clearIndexLineDecorations() {
        this.editor.setDecorations(belowIndexDecorationType, []);
        this.editor.setDecorations(indexDecorationType, []);
    }

    getLineDeltaBeforeLine(line: number) {
        // Returns the number of lines removed from a file when the diff currently active is closed
        let totalLineDelta = 0;
        for (
            const range of this
                .greenDecorationManager
                .getRanges()
                .sort((a, b) => a.start.line - b.start.line)
        ) {
            if (range.start.line > line) {
                break;
            }

            totalLineDelta -= range.end.line - range.start.line + 1;
        }

        return totalLineDelta;
    }

    async clear(accept: boolean) {
        const rangesToDelete = accept
            ? this.redDecorationManager.getRanges()
            : this.greenDecorationManager.getRanges();

        this.redDecorationManager.clear();
        this.greenDecorationManager.clear();
        this.clearIndexLineDecorations();

        await this.editor.edit(
            editBuilder => {
                for (const range of rangesToDelete) {
                    editBuilder.delete(
                        new vscode.Range(
                            range.start,
                            new vscode.Position(range.end.line + 1, 0)
                        )
                    );
                }
            },
            {
                undoStopAfter: false,
                undoStopBefore: false,
            }
        );

        this.cancelled = true;
        this.dispose();
    }

    disposables: vscode.Disposable[] = [];

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
    }

    get isCancelled() {
        return this.cancelled;
    }

    private readonly _diffLinesQueue: DiffLine[] = [];
    private _queueLock = false;

    async queueDiffLine(diffLine: DiffLine | undefined) {
        if (diffLine) {
            this._diffLinesQueue.push(diffLine);
        }

        if (this._queueLock || this.editor !== vscode.window.activeTextEditor) {
            return;
        }

        this._queueLock = true;

        while (this._diffLinesQueue.length) {
            const line = this._diffLinesQueue.shift();
            if (!line) {
                break;
            }

            try {
                await this._handleDiffLine(line);
            }
            catch (e) {
                this._diffLinesQueue.push(line);
                break;
            }
        }

        this._queueLock = false;
    }

    private async _handleDiffLine(diffLine: DiffLine) {
        switch (diffLine.type) {
            case 'same':
                await this.insertDeletionBuffer();
                this.incrementCurrentLineIndex();
                break;
            case 'old':
                // Add to deletion buffer and delete the line for now
                this.deletionBuffer.push(diffLine.line);
                await this.deleteLinesAt(this.currentLineIndex);
                break;
            case 'new':
                await this.insertLineAboveIndex(this.currentLineIndex, diffLine.line);
                this.incrementCurrentLineIndex();
                this.insertedInCurrentBlock++;
                break;
        }
    }

    async run(diffLineGenerator: AsyncGenerator<DiffLine>) {
        try {
            this.updateIndexLineDecorations();

            for await (const diffLine of diffLineGenerator) {
                if (this.isCancelled) {
                    return;
                }
                await this.queueDiffLine(diffLine);
            }

            await this.insertDeletionBuffer();
            this.clearIndexLineDecorations();
        }
        catch (e) {
            this.clearForFilepath(this.filepath, false);
            throw e;
        }
    }

    updateLineDelta(startLine: number, lineDelta: number) {
        const redResult = this.redDecorationManager.shiftDownAfterLine(startLine, lineDelta);
		const greenResult = this.greenDecorationManager.shiftDownAfterLine(startLine, lineDelta);
        // 检查两个结果是否都是空数组或者每一项都只剩下一行光标
        const shouldClear = (result: any[]) => result.length === 0 ||
        (result.every(item => item.start.line === item.end.line) && result.length === 1)
        if (shouldClear(redResult) && shouldClear(greenResult)) {
            this.storedListener?.();
        }
    }
    onClearRange(listener?: () => void) {
        if (listener) {
            this.storedListener = listener;
        }
    }
}
