/* eslint-disable max-statements */
/* eslint-disable complexity */
/* eslint-disable max-depth */
/* eslint-disable max-len */
import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import platform from '@shared/platform';
import {SSEProcessor} from '@comate/plugin-shared-internals';
import axios, {CancelToken} from 'axios';
import {
    CMD_EXPLAIN_SELECTED_CODE,
    CMD_SHOW_INLINECHAT_QUICKPICK,
    RegisteredCommand,
    INLINECHAT_PROMPT_HISTORY_KEY,
} from '@/constants';
import {StreamResponse, buildParams} from '@/common/Fetcher';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {GenerateCodeOptions, RawGenerateCode, acceptCode, generateStreamCode} from '@/api';
import {error} from '@/common/outputChannel';
import {showForbiddenMessage} from '@/common/showForbiddenMessage';
import {stripExtraIndent} from '@/utils/indent';
import {getCompleteFirstLine} from '@/utils/document';
import {findDocstringInsertionLocation} from '@/utils/docString';
import {findLeadingNonWhitespaceIndex, stripMarkdownCodeBlock} from '@/utils/common';
import {L10n} from '@/common/L10nProvider/L10n';
import {
    CommentProviderText,
    DocstringProviderText,
    ExplainProviderText,
    InlineChatText,
} from '@/common/L10nProvider/constants';
import {isPoc} from '@/utils/features';
import {UserService} from '../UserService';
import {formatGeneratedDocstring} from '../DocstringProvider/utils';
import {buildInsertionText} from '../DocstringProvider';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {DiffHighlightProvider} from '../DiffProvider/DiffHighlightProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {ChatBaseProvider} from '../ChatBaseProvider';
import {DiffProvider} from '../DiffProvider';
import {Conversation} from '../ChatViewProvider/Conversation';
import {FoldedCodeLensProvider} from '../FoldedCodeLensProvider';
import {VisualStateManagerProvider} from '../VisualStateManagerProvider';
import {isSelected, removeLanguageTagAndTrimNewline} from './utils';
import {DynamicDiffManager} from './DynamicDiffManager';

interface QuickPickItem extends vscode.QuickPickItem {
    command?: ((input?: string) => void) | string;
}

type FunctionType = 'CODE_TO_COMMENT' | 'CODE_INLINE_COMMENT';

function buildReplacement(document: vscode.TextDocument, content: string, padding: string) {
    const lines = stripMarkdownCodeBlock(content, document.languageId).split('\n');
    const currentPadding = lines[0].slice(0, findLeadingNonWhitespaceIndex(lines[0]));
    if (currentPadding === padding) {
        return content;
    }
    const paddingLines = lines.map(line => (line.trim()
        ? `${padding}${line}`
        : line)
    );
    return paddingLines.join('\n');
}

function findFirstNonEmptyLineTextInRange(document: vscode.TextDocument, range: vscode.Range) {
    for (let i = range.start.line; i <= range.end.line; i++) {
        const lineText = document.lineAt(i).text;
        // 返回第一个非空行的文本
        if (lineText.trim() !== '') {
            return lineText;
        }
    }
    return document.lineAt(range.start).text;
}

const getEndOfLine = (document: vscode.TextDocument) => {
    return document.eol === vscode.EndOfLine.LF ? '\n' : '\r\n';
};

/**
 * 处理后端返回 content，提取出需要的内容
 *
 * 目前返回的 content 结构类似：
 * ```json\n{\n    \"isEdit\": true,\n    \"result\": \"具体代码\n```
 * 当 isEdit 为 true 时，为 InlineChat 方式在编辑区直接展示
 * 当 isEdit 为 false 时，返回的非代码，将引至侧边栏输出
 *
 * @param content 接口返回的 content 字段原始内容
 */
const formatResponse = (content_: string, json: boolean = true, addNewLine = true) => {
    const content = content_.trim();
    if (!json) {
        return {isEdit: true, content: removeLanguageTagAndTrimNewline(content, addNewLine), result: content};
    }

    try {
        const trimmedContent = JSON.parse(removeLanguageTagAndTrimNewline(content, false));
        const trimmedResult = removeLanguageTagAndTrimNewline(trimmedContent.result, addNewLine);
        return {isEdit: trimmedContent.isEdit, content: trimmedResult, result: trimmedContent.result as string};
    }
    catch (e) {
        let isEdit = true;
        let result = '';

        // 使用正则表达式逐步提取 isEdit 和 result 的值
        const isEditMatch = /"isEdit":\s*(true|false)/.exec(content);
        if (isEditMatch) {
            isEdit = isEditMatch[1] === 'true';
        }

        const resultMatch = /"result":\s*"([^"]*)"?/.exec(content);
        if (resultMatch?.[1]) {
            result = removeLanguageTagAndTrimNewline(resultMatch[1].split(/\r?\\n/).join('\n'), false);
        }
        return {isEdit, content: result, result: resultMatch?.[1].split(/\r?\\n/).join('\n') ?? ''};
    }
};

@injectable()
export class InlineChatProvider extends ChatBaseProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private currentQuickPickItems: QuickPickItem[] = [];
    private taskId = 0;
    private quickPick: vscode.QuickPick<vscode.QuickPickItem> | null = null;

    constructor(
        @inject(UserService) private readonly userService: UserService,
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(DiffHighlightProvider) private readonly diffHightlightProvider: DiffHighlightProvider,
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(DynamicDiffManager) private readonly diffManager: DynamicDiffManager,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(FoldedCodeLensProvider) readonly foldedCodeLensProvider: FoldedCodeLensProvider,
        @inject(VisualStateManagerProvider) readonly visualStateManagerProvider: VisualStateManagerProvider
    ) {
        super(diffProvider);
        if (!isPoc) {
            this.disposables.push(
                vscode.commands.registerCommand(
                    RegisteredCommand.showInlineChat,
                    // eslint-disable-next-line max-statements, complexity
                    async () => {
                        const activeEditor = vscode.window.activeTextEditor;
                        if (!activeEditor) {
                            return;
                        }

                        const editor = vscode.window.activeTextEditor;
                        const document = editor?.document;
                        const position = editor?.selection.active;
                        if (!position || !document) {
                            return;
                        }

                        const diffInfo = this.diffHightlightProvider.getDiffRange(document.uri.fsPath);
                        const loadingRange = this.visualStateManagerProvider.getLoadingRange(document.uri.fsPath);

                        const errorMessage = L10n.t(InlineChatText.INLINCHAT_ERROR);
                        const generateMessage = L10n.t(InlineChatText.INLINECHAT_GENERATE);
                        const docstringLabel = L10n.t(DocstringProviderText.INLINECHAT_TITLE);
                        const inlineCommentLabel = L10n.t(CommentProviderText.INLINECHAT_TITLE);

                        if (loadingRange || diffInfo) {
                            const textInputOptions: vscode.InputBoxOptions = {
                                title: `${platform.resolve('brand')}`,
                                placeHolder: errorMessage,
                                validateInput: () => errorMessage,
                            };
                            await vscode.window.showInputBox(textInputOptions);
                            return;
                        }

                        const quickItemsArr = [...this.foldedCodeLensProvider.quickPickItemsMap.keys()];
                        const quickItemsKey = quickItemsArr.find(i => editor.selection && i?.contains(editor.selection));
                        let quickPickItems: QuickPickItem[] = [];

                        if (quickItemsKey) {
                            const quickItems_ = this.foldedCodeLensProvider.quickPickItemsMap.get(quickItemsKey);
                            if (quickItems_) {
                                quickPickItems = quickItems_.map(
                                    i => {
                                        const functionNodes = this.treeSitterProvider.functionNodeOfRange(
                                            document,
                                            quickItemsKey
                                        );
                                        const insertPosition = functionNodes.length === 0
                                            ? position
                                            : findDocstringInsertionLocation(document, functionNodes[0]).position;

                                        if (i.label === docstringLabel) {
                                            return {
                                                label: docstringLabel,
                                                command: () =>
                                                    this.generateInlineChatContent(
                                                        document,
                                                        quickItemsKey,
                                                        'CODE_TO_COMMENT',
                                                        insertPosition
                                                    ),
                                            };
                                        }

                                        if (i.label === inlineCommentLabel) {
                                            return {
                                                label: inlineCommentLabel,
                                                command: () =>
                                                    this.generateInlineChatContent(
                                                        document,
                                                        quickItemsKey,
                                                        'CODE_INLINE_COMMENT',
                                                        insertPosition
                                                    ),
                                            };
                                        }

                                        return i;
                                    }
                                );
                            }
                        }
                        const selection = editor.selection;
                        const selectedText = editor.document.getText(selection);
                        if (selectedText.trim() !== '') {
                            if (
                                !quickPickItems.some(i => i.label === `/${L10n.t(ExplainProviderText.CODELENS_TITLE)}`)
                            ) {
                                quickPickItems.push({
                                    label: `/${L10n.t(ExplainProviderText.CODELENS_TITLE)}`,
                                    command: CMD_EXPLAIN_SELECTED_CODE,
                                });
                            }
                        }

                        const quickPickItemsLength = quickPickItems.length;

                        const context = await getExtensionContextAsync();
                        const inlineChatPromptHistory: string[] = context.globalState.get(INLINECHAT_PROMPT_HISTORY_KEY)
                        || [];

                        inlineChatPromptHistory.slice(0, 3).forEach(prompt => {
                            quickPickItems.push({
                                label: `${prompt}`,
                                command: ((currentPrompt: string) => () =>
                                    this
                                        .handleUserInput(
                                            currentPrompt,
                                            document,
                                            new vscode.Position(position.line, 0)
                                        ))(prompt), // 立即执行函数表达式，捕获当前的prompt值
                            });
                        });

                        const inlineChatItem = {
                            label: generateMessage,
                            command: (input: string) =>
                                this.handleUserInput(input, document, new vscode.Position(position.line, 0)),
                        };

                        const quickPickItemsWithoutInput = quickPickItems
                            .filter(item => item !== null)
                            .map((item: any) => {
                                return {
                                    label: item.label,
                                    command: item.command,
                                    alwaysShow: true,
                                    arguments: item.arguments,
                                } as QuickPickItem;
                            });
                        const quickPickItemsWithInput = [inlineChatItem]
                            .filter(item => item !== null)
                            .map((item: any) => {
                                return {
                                    label: item.label,
                                    command: item.command,
                                    alwaysShow: true,
                                    arguments: item.arguments,
                                } as QuickPickItem;
                            });

                        quickPickItemsLength && quickPickItemsWithoutInput.unshift({
                            kind: vscode.QuickPickItemKind.Separator,
                            label: '指令',
                        });

                        inlineChatPromptHistory.length
                        && quickPickItemsWithoutInput.splice(
                            quickPickItemsLength === 0 ? 0 : quickPickItemsLength + 1,
                            0,
                            {
                                kind: vscode.QuickPickItemKind.Separator,
                                label: '最近输入',
                            }
                        );

                        const quickPick = vscode.window.createQuickPick();
                        quickPick.title = `${platform.resolve('brand')}`;
                        quickPick.placeholder = selectedText.trim() === ''
                            ? L10n.t(InlineChatText.INLINECHAT_UNSELECTED)
                            : L10n.t(InlineChatText.INLINECHAT_SELECTED);
                        quickPick.items = quickPickItemsWithoutInput;
                        quickPick.ignoreFocusOut = true;
                        this.currentQuickPickItems = quickPickItemsWithoutInput;
                        quickPick.onDidChangeValue(value => {
                            if (value) {
                                quickPick.items = quickPickItemsWithInput;
                                this.currentQuickPickItems = quickPickItemsWithInput;
                            }
                            else {
                                quickPick.items = quickPickItemsWithoutInput;
                                this.currentQuickPickItems = quickPickItemsWithoutInput;
                            }
                        });

                        quickPick.onDidAccept(() => {
                        // 如果有活动项（即用户选择了一个下拉列表项），则执行与该项对应的命令
                            const result = this.currentQuickPickItems.find(
                                quickPickItem => quickPickItem.label === quickPick.activeItems[0].label
                            );
                            if (result?.command) {
                                if (typeof result.command === 'function') {
                                    result.command(quickPick.value);
                                }

                                if (typeof result.command === 'string') {
                                    vscode.commands.executeCommand(
                                        result.command,
                                        ...((result as any)?.arguments || [])
                                    );
                                }
                            }
                            quickPick.hide();
                        });
                        quickPick.onDidHide(() => {
                            this.quickPick = null;
                            vscode.commands.executeCommand('setContext', 'baidu.comate.inlinechat.quickPickVisible', false);
                        });
                        this.quickPick = quickPick;
                        vscode.commands.executeCommand('setContext', 'baidu.comate.inlinechat.quickPickVisible', true);
                        quickPick.show();
                    }
                ),
                vscode.commands.registerCommand(
                    RegisteredCommand.closeInlineChat,
                    async () => {
                    // 如果QuickPick已经显示，则隐藏它
                        if (this.quickPick) {
                            this.quickPick.hide();
                        }
                    }
                )
            );
        }
    }

    editContent(content: string, row: number, userInput: string, language: string) {
        const lines = content.split('\n');
        let commentSymbol = '';
        let colAdditionLength = 0;

        switch (language) {
            case 'python':
                commentSymbol = '#';
                colAdditionLength = 2;
                break;
            case 'html':
                commentSymbol = '<!--';
                // eslint-disable-next-line no-param-reassign
                userInput += ' -->';
                colAdditionLength = 9;
                break;
            case 'javascript':
            case 'typescript':
            case 'c':
            case 'cpp':
            default:
                commentSymbol = '//';
                colAdditionLength = 3;
        }

        lines.splice(row, 0, commentSymbol + ' ' + userInput);
        const res = lines.join('\n');
        return {newContent: res, colAdditionLength};
    }

    // eslint-disable-next-line max-statements, complexity
    async handleUserInput(input: string, document: vscode.TextDocument, position: vscode.Position) {
        if (input.trim() === '') {
            // 如果仅包含空格，则不执行后续操作
            return;
        }
        const params = await buildParams(document, position, this.userService);
        if (params.type !== 'success') {
            return;
        }
        params.value.triggerSource = 'INLINE_CHAT';

        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            return;
        }
        const hasSelectText = isSelected(activeEditor);
        try {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                return;
            }
            const selection = editor.selection;
            let range = new vscode.Range(selection.start, selection.end);
            // 包含选中内容的完整行，即如果是从中间选的这个会从开头开始
            if (!range.start.isEqual(range.end)) {
                range = new vscode.Range(
                    range.start.line,
                    0,
                    range.end.line,
                    document.lineAt(range.end.line).range.end.character
                );
            }
            const text = editor.document.getText(range);
            const languages = ['python', 'html', 'javascript', 'typescript', 'c', 'cpp'];
            const resIsJson = hasSelectText || !languages.includes(document.languageId);

            const context = await getExtensionContextAsync();
            const history: string[] = context.globalState.get(INLINECHAT_PROMPT_HISTORY_KEY) || [];
            const newHistory = [input, ...history];
            context.globalState.update(INLINECHAT_PROMPT_HISTORY_KEY, [...new Set(newHistory)].slice(0, 20));

            if (resIsJson) {
                params.value.content = text;
                params.value.userInput = input;
                params.value.function = 'CUSTOMER_QUERY';
                params.value.model = 'ERNIE_BOT';
            }
            else {
                const {newContent, colAdditionLength} = this.editContent(
                    params.value.content,
                    Number(params.value.row),
                    input,
                    document.languageId
                );
                params.value.content = newContent;
                params.value.row = String(Number(params.value.row) + 1);
                params.value.col = String(input.length + colAdditionLength + 1);
                params.value.manualTriggerType = 'commentEnhancement';
            }

            const filePath = document.uri.fsPath;
            this.visualStateManagerProvider.addLoadingRange(filePath, range);
            const currentTaskId = ++this.taskId;
            const definitionLine = findFirstNonEmptyLineTextInRange(document, range);
            const padding = definitionLine.slice(0, findLeadingNonWhitespaceIndex(definitionLine));

            const res = await this.fetchAndStreamCode(params.value, range, resIsJson);
            if (res.error) {
                throw new Error(res.error);
            }
            const isLoading = Boolean(this.visualStateManagerProvider.getLoadingRange(filePath));
            if (isLoading && currentTaskId === this.taskId) {
                const {isEdit, content} = formatResponse(res.content, resIsJson, true);
                if (!isEdit) {
                    // 激活下
                    await editor.edit(editBuilder => {
                        editBuilder.insert(new vscode.Position(editor.document.lineCount, 0), '');
                    });

                    // eslint-disable-next-line max-depth
                    if (!this.chatViewProvider.webViewVisible && !isPoc) {
                        vscode.commands.executeCommand('baidu.comate.showChatPanel', {
                            source: CMD_SHOW_INLINECHAT_QUICKPICK,
                        });
                    }

                    this.diffHightlightProvider.clearLoadingRange(document.uri.fsPath);
                    return;
                }

                this.diffHightlightProvider.clearLoadingRange(document.uri.fsPath, {finished: true});
                const replaceContent = buildReplacement(editor.document, content, padding);
                this.diffHightlightProvider.applyCustomDiff(
                    filePath,
                    range,
                    replaceContent,
                    hasSelectText ? 'line' : 'block',
                    this.getCustomHandlers(filePath, res.uuid)
                );

                if (hasSelectText) {
                    editor.selection = new vscode.Selection(range.start, range.start);
                }
            }
        }
        catch (e: any) {
            error('inline chat error:', e.message);
            this.diffHightlightProvider.clearLoadingRange(document.uri.fsPath, {finished: true});
        }
    }

    async generateInlineChatContent(
        document: vscode.TextDocument,
        range: vscode.Range,
        functionName: FunctionType,
        insertPosition: vscode.Position
    ) {
        const paramsRes = await buildParams(document, range.start, this.userService);
        if (paramsRes.type !== 'success') {
            return {uuid: ''};
        }

        const firstLine = getCompleteFirstLine(document, range);
        const functionContent = stripExtraIndent(document.getText(range), true, firstLine);

        const params = {...paramsRes.value, content: functionContent, model: 'ERNIE_BOT', function: functionName};

        try {
            this.visualStateManagerProvider.addLoadingRange(document.uri.fsPath, range);

            return this.fetchAndStreamFallback(document, params, functionName, range, insertPosition);
        }
        catch (e: any) {
            return {uuid: ''};
        }
    }

    async fetchAndStreamCode(
        params: GenerateCodeOptions,
        range: vscode.Range,
        json: boolean = true,
        cancelToken?: CancelToken,
        _conversation_: Conversation | null = null,
        addNewLine: boolean = false
    ) {
        params.triggerSource = 'INLINE_CHAT';
        const res = await generateStreamCode(params, cancelToken);
        const editor = vscode.window.activeTextEditor;

        const processor = new SSEProcessor<StreamResponse>(res.data);
        let uuid = '';
        let content = '';
        let chatId = '';
        const updateContent = (data: Partial<RawGenerateCode>) => {
            if (data.uuid && !uuid) {
                uuid = data.uuid;
            }
            if (data.chatId && !chatId) {
                chatId = data.chatId;
            }
            if (data.content) {
                content += data.content;
            }
        };
        let visibleContentArr: string[] = [];

        try {
            if (!editor) {
                return {content: '', uuid, processor};
            }

            const oldContent = editor.document.getText(
                new vscode.Range(range.start.line, 0, range.end.line, Number.MAX_SAFE_INTEGER)
            );
            const filePath = editor.document.uri.fsPath;
            this.diffHightlightProvider.addOnCancelCallback(
                filePath,
                ({finished}: {finished: boolean} = {finished: false}) => {
                    if (!finished) {
                        this.diffManager.clearForFilepath(filePath, false);
                    }
                }
            );
            const definitionLine = findFirstNonEmptyLineTextInRange(editor.document, range);
            const padding = definitionLine.slice(0, findLeadingNonWhitespaceIndex(definitionLine));
            // eslint-disable-next-line @typescript-eslint/no-this-alias
            const clearForFilepath = this.diffManager.clearForFilepath.bind(this.diffManager);
            const text = editor.document.getText(range);

            const handleUserQuery = this.chatViewProvider.handleUserQuery.bind(this.chatViewProvider);

            const clearLoadingRange = this.diffHightlightProvider.clearLoadingRange.bind(this.diffHightlightProvider);
            const acceptAllChanges = this.diffHightlightProvider.acceptAllChanges.bind(this.diffHightlightProvider);

            const asyncGenerator = async function* asyncGenerator() {
                for await (const chunk of processor.processSSE()) {
                    if (showForbiddenMessage(chunk as any) || chunk.message) {
                        processor.cancel();
                        throw new Error((chunk as any).message);
                    }
                    updateContent(chunk);
                    if (chunk.content) {
                        const {content: formattedContent_, isEdit} = formatResponse(content, json, false);
                        if (isEdit === false) {
                            clearLoadingRange();
                            // 激活下
                            await editor.edit(editBuilder => {
                                editBuilder.insert(new vscode.Position(editor.document.lineCount, 0), '');
                            });
                            editor.selection = new vscode.Selection(
                                range.start,
                                range.end
                            );
                            if (!isPoc) {
                                vscode.commands.executeCommand('baidu.comate.showChatPanel', {
                                    source: CMD_SHOW_INLINECHAT_QUICKPICK,
                                });
                            }

                            handleUserQuery({
                                prompt: params.userInput as string,
                                chatIntentRecognition: true,
                                supportAt: true,
                                messageOrder: {},
                                knowledgeList: [],
                            });

                            editor && clearForFilepath(editor.document.uri.fsPath, false);
                            return;
                        }
                        const formattedContent = buildReplacement(editor.document, formattedContent_, padding);
                        const formattedContentArr = formattedContent.split('\n');

                        if (formattedContentArr.length - visibleContentArr.length > 1 && isEdit) {
                            const streamContentArr = formattedContentArr.slice(visibleContentArr.length, -1);
                            visibleContentArr = [...visibleContentArr, ...streamContentArr];
                            for (const j of streamContentArr) {
                                yield j;
                            }
                        }
                    }
                }

                const {content: formattedContent_, isEdit} = formatResponse(content, json, addNewLine);
                const formattedContent = buildReplacement(editor.document, formattedContent_, padding);
                const formattedContentArr = formattedContent.split('\n');

                if (isEdit) {
                    const streamContentArr = formattedContentArr.slice(visibleContentArr.length);
                    visibleContentArr = [...visibleContentArr, ...streamContentArr];
                    for (const j of streamContentArr) {
                        yield j;
                    }
                }
            };

            if (text) {
                await this.diffManager.streamOutput(
                    oldContent,
                    asyncGenerator(),
                    range,
                    getEndOfLine(editor.document),
                    () => acceptAllChanges()
                );
            }
            else {
                await this.diffManager.streamOutput(
                    '',
                    asyncGenerator(),
                    range || undefined,
                    getEndOfLine(editor.document),
                    () => acceptAllChanges()
                );
            }

            return {content, uuid, processor};
        }
        catch (e: any) {
            const editor = vscode.window.activeTextEditor;
            if (editor?.document?.uri.fsPath) {
                this.diffHightlightProvider.clearLoadingRange(editor.document.uri.fsPath);
            }
            // 临时处理生成到一半然后异常的情况
            if (content) {
                error('request failed: ', e.message);
                return {content, uuid, processor, error: e.message};
            }
            throw e;
        }
    }

    getCustomHandlers(filepath: string, uuid: string) {
        return {
            accept: () => {
                acceptCode({uuid, content: '', accepted: true});
                this.diffManager.clearForFilepath(filepath, true);
            },
            reject: () => {
                this.diffManager.clearForFilepath(filepath, false);
            },
        };
    }

    private async fetchAndStreamFallback(
        document: vscode.TextDocument,
        params: GenerateCodeOptions,
        functionName: FunctionType,
        range: vscode.Range,
        insertPosition: vscode.Position
    ) {
        const axiosTokenSource = axios.CancelToken.source();
        const functionRange = functionName === 'CODE_TO_COMMENT'
            ? new vscode.Range(insertPosition.line, 0, insertPosition.line, 0)
            : range;
        const {content, uuid, processor, error} = await this.fetchAndStreamCode(
            params,
            functionRange,
            false,
            axiosTokenSource.token,
            null
        );
        if (error) {
            return {uuid};
        }
        const formattedDocstring = formatGeneratedDocstring(document.languageId, content);
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            return;
        }
        const isLoading = Boolean(this.visualStateManagerProvider.getLoadingRange(document.uri.fsPath));

        this.diffHightlightProvider.clearLoadingRange(document.uri.fsPath, {finished: true});
        if (isLoading) {
            const definitionLine = document.lineAt(range.start).text;
            const padding = definitionLine.slice(0, findLeadingNonWhitespaceIndex(definitionLine));
            const replaceContent = buildReplacement(
                editor.document,
                removeLanguageTagAndTrimNewline(content, false),
                padding
            );
            const insertContent = buildInsertionText(
                removeLanguageTagAndTrimNewline(content.trim()),
                padding
            );

            this.diffHightlightProvider.applyCustomDiff(
                document.uri.fsPath,
                functionRange,
                functionName === 'CODE_TO_COMMENT' ? insertContent : replaceContent,
                functionName === 'CODE_TO_COMMENT' ? 'block' : 'line',
                this.getCustomHandlers(document.uri.fsPath, uuid)
            );
        }

        return {formattedDocstring, processor, uuid};
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
