/* eslint-disable max-lines */
/* eslint-disable complexity */
/**
 * @file 配置中心，用于同步本地和远端的语言 enable 配置
 */

import {extname} from 'path';
import {defaultsDeep, isEqual, omit, pick, pickBy} from 'lodash';
import * as vscode from 'vscode';
import {injectable, inject} from 'inversify';
import {ExtensionDisplayLanguages, AgentConfig, AgentApplyStatus} from '@shared/protocols';
import 'reflect-metadata';
import {isPoc, isSaaS, isSaasOrPoc} from '@/utils/features';
import {
    InlineSuggestionModeItem,
    LinePreferMode,
    UserConfig,
    UserInfoWithLicense,
    getSuffixSupport,
    getUserConfig,
    getUserInfoWithLicense,
    updateUserConfig,
} from '../api';
import {
    BAIDU_CONFIG_NAMESPACE,
    COMATE_CONFIG_PREFIX,
    COMATE_CONFIG_SECTION,
    DEFAULT_SUGGESTION_MODE_MAP,
    SUPPORTED_SUFFIX_CONFIG,
} from '../constants';
import {UserService} from './UserService';

export enum ConfigKey {
    EnableInlineSuggestion = 'enableInlineSuggestion',
    LangSuggestion = 'langSuggestion',
    InlineSuggestionMode = 'inlineSuggestionMode',
    CodeLensDisplayMode = 'codelensDisplayMode',
    DocstringOutputPosition = 'docstringOutputPosition',
    EnableStreamingSession = 'enableStreamingSession',
    EnableCodelens = 'enableCodelens',
    EnableQuickFix = 'enableQuickFix',
    Username = 'username',
    UnitTestFrameworkForJava = 'unitTestFrameworkForJava',
    UnitTestMockForJava = 'unitTestMockForJava',
    UnitTestFrameworkForGo = 'unitTestFrameworkForGo',
    UnitTestMockForGo = 'unitTestMockForGo',
    UnitTestFrameworkForCpp = 'unitTestFrameworkForC/C++',
    UnitTestMockForCpp = 'unitTestMockForC/C++',
    UnitTestFrameworkForJs = 'unitTestFrameworkForJs/Ts',
    UnitTestMockForJs = 'unitTestMockForJs/Ts',
    UnitTestFrameworkForPython = 'unitTestFrameworkForPython',
    UnitTestMockForPython = 'unitTestMockForPython',
    Key = 'license',
    EnableSecurityScanBox = 'enableSecurityScanBox',
    // 这个配置目前只有saas有，不要乱用下面这个key，如果要拿language从 configProvider.getDisplayLanguage 拿
    DisplayLanguage = 'displayLanguage',
    EnableSecurityEnhancement = 'enableSecurityEnhancement',
    EnableCodebaseEnhancedContext = 'enableCodebaseEnhancedContext',
    LinePreferMode = 'linePreferMode',
    EnableCommentEnhancement = 'enableCommentEnhancement',
    // CustomizeService = 'customizeService',
    PrivateService = 'privateService',
    EnableAutoPaste = 'enableAutoPaste',
    Beta = 'beta',
}

export const SENSITIVE_CONFIGS = [ConfigKey.Key];

export interface CodelensConfig {
    enableInlineUnitTest: boolean;
    enableInlineExplain: boolean;
    enableInlineDocstring: boolean;
    enableInlineSplit: boolean;
    enableInlineComment: boolean;
    enableInlineOptimize: boolean;
    enableInlineLog: boolean;
    enableChatPanelShortcut: boolean;
}

export enum CodeLensDisplayMode {
    TextTitle = 'expand',
    MinimizedIcon = 'collapse',
}

export enum DocstringOutputPosition {
    Editor = 'editor',
    Sidebar = 'sidebar',
}

type LocalCodeLensDisplayMode = CodeLensDisplayMode | '文字平铺' | '最小化 ICON 展示';
type LocalDocstringOutputPosition = DocstringOutputPosition | '编辑区' | '侧边栏';

/**
 * 因为 InlineSuggestionMode 这个配置从中文变成了英文，需要兼容老版本
 *
 * @param value 本地读取到的 InlineSuggestionMode 配置
 * @returns 返回格式化后的字符串
 */
export function formatLocalInlineSuggestionMode(value: string) {
    switch (value) {
        case '极速模式':
            return 'extremeFast';
        case '速度优先':
            return 'fast';
        case '智能平衡':
            return 'balance';
        case '精准优先':
            return 'accurate';
        case '精准模式':
            return 'extremeAccurate';
        default:
            return value;
    }
}

export function formatLanguageId(languageId?: string) {
    // 以后端的语言名称为准
    if (languageId === 'shellscript') {
        return 'shell';
    }
    return languageId;
}

export function formatDisplayLanguage(language?: string) {
    switch (language) {
        case 'English':
        case 'en':
            return 'en';
        default:
            return 'zh';
    }
}

export function getBasicConfigSectionName() {
    if (vscode.env.language.startsWith('zh')) {
        return '基本配置';
    }
    return 'basic';
}

export function capitalize(str: string) {
    return str.slice(0, 1).toUpperCase() + str.slice(1);
}

@injectable()
export class VSCodeConfigProvider implements vscode.Disposable {
    private config: vscode.WorkspaceConfiguration;
    // 不放在config中，因为applyStatus只用于展示当前各个智能体的申请状态，用户无法修改
    private agentApplyStatus: AgentApplyStatus | undefined;
    private suffixMap: Map<string, string> | undefined;
    readonly suggestionModeMap: Map<string, InlineSuggestionModeItem>;
    isInsider = false;
    private readonly configChangeEventEmitter = new vscode.EventEmitter<void>();
    private licenseInfo?: UserInfoWithLicense;
    onDidChange = this.configChangeEventEmitter.event;
    private resolveInitialRemoteConfig?: (value: UserConfig) => void;
    readonly initialRemoteConfigPromise: Promise<UserConfig> = new Promise(resolve => {
        this.resolveInitialRemoteConfig = resolve;
    });

    private disposables: vscode.Disposable[] = [];

    constructor(@inject(UserService) private readonly userService: UserService) {
        this.config = vscode.workspace.getConfiguration(COMATE_CONFIG_PREFIX);
        this.disposables.push(
            vscode.workspace.onDidChangeConfiguration(e => {
                if (e.affectsConfiguration(COMATE_CONFIG_PREFIX)) {
                    this.config = vscode.workspace.getConfiguration(COMATE_CONFIG_PREFIX);
                    this.configChangeEventEmitter.fire();
                    this.sync();
                }
            })
        );
        this.suggestionModeMap = new Map<string, InlineSuggestionModeItem>(
            Object.entries(DEFAULT_SUGGESTION_MODE_MAP)
        );
        this.synchronizeRemoteConfig();
        this.initUsername();
    }

    dumpConfig() {
        const res = Object.keys(ConfigKey).reduce((acc: any, key: string) => {
            const keyname = ConfigKey[key as keyof typeof ConfigKey];
            acc[keyname] = this.config.get(keyname);
            return acc;
        }, {});
        return res;
    }

    async getLicenseInfo() {
        const license = this.getLicense();
        this.licenseInfo = await getUserInfoWithLicense(license);
        return {
            ...this.licenseInfo,
            license,
        };
    }

    private async synchronizeRemoteConfig() {
        await this.sync(true);
    }

    /**
     * 同步本地和远端的配置
     *
     * @TODO 这里是否需要 debounce，或者 sync 是否需要单例化
     * @param forceRemote 是否强制使用远端配置，否则将使用本地配置对远端进行更新
     * @returns
     */
    private async sync(forceRemote: boolean = false) {
        const license = this.getLicense();
        const [username] = await this.userService.getCurrentUser();
        const remoteConfig = await getUserConfig(isSaasOrPoc ? license : username);

        if (!remoteConfig) {
            return;
        }

        this.resolveInitialRemoteConfig?.(remoteConfig);

        this.agentApplyStatus = remoteConfig.applyStatus || {};

        this.updateMemoryConfig(remoteConfig);

        if (forceRemote) {
            return this.updateLocalConfig(remoteConfig);
        }
        else {
            return this.updateRemoteConfig(remoteConfig, isSaasOrPoc ? license : username);
        }
    }

    /**
     * 初始化一下用户名配置
     */
    private async initUsername() {
        const configUsername = this.getConfig<string>(ConfigKey.Username);
        if (!configUsername) {
            const [username] = await this.userService.getCurrentUser();
            if (username) {
                this.config.update(ConfigKey.Username, username, true);
            }
        }
    }

    /**
     * 更新本地配置
     *
     * @param remoteConfig 当前的远端配置
     * @returns
     */
    private async updateLocalConfig(remoteConfig: UserConfig) {
        const langSuggestion = this.getConfig<Record<string, boolean>>(ConfigKey.LangSuggestion);
        if (remoteConfig.langSuggestion && !isEqual(langSuggestion, remoteConfig.langSuggestion)) {
            this.config.update(ConfigKey.LangSuggestion, remoteConfig.langSuggestion, true);
        }

        if (remoteConfig.delayMilliSecondsModeMap) {
            Object.entries(remoteConfig.delayMilliSecondsModeMap).forEach(item => {
                const [key, value] = item;
                this.suggestionModeMap.set(key, value);
            });
        }

        const localSuggestionMode = this.getConfig<string>(ConfigKey.InlineSuggestionMode);

        if (remoteConfig.delayMilliSecondsMode !== localSuggestionMode) {
            this.config.update(ConfigKey.InlineSuggestionMode, remoteConfig.delayMilliSecondsMode, true);
        }

        const displayLanguage = this.getDisplayLanguage();
        const remoteDisplayLanguage = formatDisplayLanguage(remoteConfig.displayLanguage);
        if (remoteDisplayLanguage !== displayLanguage) {
            this.updateDisplayLanguage(remoteDisplayLanguage);
        }
        this.updateCodelensConfig(remoteConfig);
        this.updateUnitTestConfig(remoteConfig);
        this.updateSecurityConfig(remoteConfig.enableSecurityEnhance);
        this.updateLinePreferModeConfig(remoteConfig.linePreferMode);
        this.updateEnableCommentEnhancement(remoteConfig.enableCommentEnhancement);
        this.updateEnableAutoPaste(remoteConfig.enableAutoPaste);
        this.updateAgentConfig(remoteConfig.enableIntelligenceAgent);
        this.updateDocstringOutputPositionConfig(remoteConfig.docstringOutputPosition);
        this.forceUpdateDeprecatedConfig();
    }

    /**
     * 更新远端配置
     *
     * @param remoteConfig 当前的远端配置
     * @returns
     */
    private async updateRemoteConfig(remoteConfig: UserConfig, licenseOrUserName: string) {
        const localSuggestionMode = this.getConfig<string>(ConfigKey.InlineSuggestionMode);
        const langSuggestion = this.getConfig<Record<string, boolean>>(ConfigKey.LangSuggestion);
        const codelensConfig = this.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        const unitTestConfig = this.getUnitTestConfig();
        const securityConfig = this.getSecurityEnhancement();
        const linePreferMode = this.getLinePreferMode();
        const enableCommentEnhancement = this.getEnableCommentEnhancement();
        const displayLanguage = this.getDisplayLanguage();
        const docstringOutputPosition = this.getConfig<DocstringOutputPosition>(ConfigKey.DocstringOutputPosition);
        const agentConfig = this.getConfig<AgentConfig>(ConfigKey.Beta);

        // NOTE: 只有以下配置会同步到远端，因此注意当有新增这类配置时，要在这里加上。
        const localConfig: Partial<UserConfig> = {
            langSuggestion,
            delayMilliSecondsMode: localSuggestionMode,
            enableInlineUnitTest: codelensConfig?.enableInlineUnitTest,
            enableInlineCodeExplain: codelensConfig?.enableInlineExplain,
            enableInlineCodeToComment: codelensConfig?.enableInlineDocstring,
            enableInlineLog: codelensConfig?.enableInlineLog,
            unitTestConfig,
            enableSecurityEnhance: securityConfig,
            docstringOutputPosition,
            displayLanguage,
            linePreferMode,
            enableCommentEnhancement,
            ...(!isSaaS && ({
                enableChatPanelShortcut: codelensConfig?.enableChatPanelShortcut,
            })),
            ...(!isSaaS && ({
                enableAutoPaste: this.getEnableAutoPaste(),
            })),
            enableIntelligenceAgent: agentConfig,
        };

        // NOTE: WorkspaceConfiguration.get() api 可能返回 undefined，如果是 undefined 则用 remote 作为 default。
        const relevantRemoteConfig = pick(remoteConfig, Object.keys(localConfig));
        defaultsDeep(localConfig, relevantRemoteConfig);

        const changedConfigs = pickBy(localConfig, (value, key) => {
            return !isEqual(value, remoteConfig[key as keyof UserConfig]);
        });

        if (Object.keys(changedConfigs).length > 0) {
            await updateUserConfig(
                licenseOrUserName,
                changedConfigs
            );
        }
    }

    async initSuffixMap() {
        const config = await getSuffixSupport() ?? SUPPORTED_SUFFIX_CONFIG;
        const suffixMap = new Map<string, string>();
        for (const [languageId, suffixes] of Object.entries(config)) {
            for (const suffix of suffixes) {
                suffixMap.set(suffix, languageId);
            }
        }
        this.suffixMap = suffixMap;
    }

    /**
     * 请求前的等待
     *
     * @param
     * @returns
     */
    getRequestDelay(mode?: string) {
        if (mode) {
            return this.suggestionModeMap.get(mode)?.requestDelayMs;
        }

        const inlineSuggestionMode = this.getSuggestionMode();
        if (!inlineSuggestionMode) {
            return undefined;
        }
        return this.suggestionModeMap.get(inlineSuggestionMode)?.requestDelayMs;
    }

    /**
     * 获取行间推荐的推荐模式
     *
     * @returns 推荐模式
     */
    getSuggestionMode() {
        // 如果获取不到，默认精准模式
        const inlineSuggestionMode = this.config.get<string>(ConfigKey.InlineSuggestionMode, 'extremeAccurate');
        return formatLocalInlineSuggestionMode(inlineSuggestionMode);
    }

    /**
     * 获取行间按钮的展示模式
     *
     * @returns codeLens展示方式
     */
    getCodeLensDisplayMode() {
        const codelensDisplayMode = this.getConfig<LocalCodeLensDisplayMode>(ConfigKey.CodeLensDisplayMode);
        // 兼容旧版本配置
        if (codelensDisplayMode === '文字平铺') {
            return CodeLensDisplayMode.TextTitle;
        }
        else if (codelensDisplayMode === '最小化 ICON 展示') {
            return CodeLensDisplayMode.MinimizedIcon;
        }
        return codelensDisplayMode;
    }

    /**
     * 获取函数注释、行间注释等输出位置
     *
     * @returns 函数注释、行间注释等输出位置
     */
    getDocstringDisplayMode() {
        const docstringOutputPosition = this.getConfig<LocalDocstringOutputPosition>(ConfigKey.DocstringOutputPosition);
        if (docstringOutputPosition === '编辑区') {
            return DocstringOutputPosition.Editor;
        }
        else if (docstringOutputPosition === '侧边栏') {
            return DocstringOutputPosition.Sidebar;
        }
        return docstringOutputPosition;
    }

    /**
     * 获取配置
     * 避免外部直接使用 vscode.workspace.getConfiguration
     *
     * @param section
     * @returns
     */
    getConfig<T>(section: string): T | undefined;
    getConfig<T>(section: string, defaultValue: T): T;
    getConfig<T>(section: string, defaultValue?: T): T | undefined {
        // 流式会话默认开启
        if (section === ConfigKey.EnableStreamingSession) {
            return true as T;
        }
        if (defaultValue) {
            return this.config.get<T>(section, defaultValue);
        }
        return this.config.get<T>(section);
    }

    /**
     * 根据文件路径后缀获取 languageId ，不识别的后缀返回 others
     * @param Path
     * @returns languageId
     */
    getLanguageIdByFilePath(path: string) {
        if (this.suffixMap === undefined) {
            return undefined;
        }
        const suffix = extname(path).slice(1);
        if (!suffix) {
            return 'others';
        }
        return this.suffixMap.get(suffix) ?? 'others';
    }

    /**
     * 根据 languageId 获取语言配置
     * 这里的 languageId 是根据文件后缀和后端接口得出的
     * @param languageId
     * @returns
     */
    getEnabledConfig(languageId?: string) {
        const langSuggestion = this.getConfig<Record<string, boolean>>(ConfigKey.LangSuggestion);
        if (!langSuggestion) {
            return false;
        }
        if (languageId === undefined) {
            return langSuggestion.others;
        }
        // others 目前其实是不支持的语言
        // @TODO: 后续支持任意语言之后，可以包括到下边langSuggestion的判断中
        if (languageId === 'others') {
            return false;
        }

        if (languageId in langSuggestion) {
            return langSuggestion[languageId];
        }
        return false;
    }

    /**
     * 更新本地语言配置
     * 远端依赖 on change 事件进行更新
     *
     * @param languageId
     * @param enable
     * @returns
     */
    async updateEnabledConfig(languageId: string | undefined, enable: boolean) {
        if (languageId === undefined) {
            // eslint-disable-next-line no-unused-expressions
            languageId === 'others';
        }
        const langSuggestion = this.getConfig<Record<string, boolean>>(ConfigKey.LangSuggestion);
        if (!langSuggestion) {
            return;
        }
        langSuggestion[languageId!] = enable;
        return this.config.update(ConfigKey.LangSuggestion, langSuggestion, true);
    }

    /**
     * 更新配置启用所有语言
     * @returns
     */
    enableAllLanguages() {
        const langSuggestion = this.getConfig<Record<string, boolean>>(ConfigKey.LangSuggestion);
        if (!langSuggestion) {
            return;
        }
        for (const key of Object.keys(langSuggestion)) {
            langSuggestion[key] = true;
        }
        return this.config.update(ConfigKey.LangSuggestion, langSuggestion, true);
    }

    async updateKeyAndUserName(key: string, username: string) {
        await this.config.update(ConfigKey.Key, key, true);
        await this.config.update(ConfigKey.Username, username, true);
    }

    updateEnableInlineSuggestion(value: boolean) {
        this.config.update(ConfigKey.EnableInlineSuggestion, value, true);
    }

    unsetLicenseKey() {
        this.config.update(ConfigKey.Key, '', true);
    }

    getLicense() {
        const key = isSaasOrPoc ? (this.getConfig<string | undefined>(ConfigKey.Key) ?? '') : '';
        return key.trim();
    }

    /**
     * 获取当前配置的语言
     *
     * @returns 返回格式化后的显示语言：en | zh
     */
    getDisplayLanguage(): ExtensionDisplayLanguages {
        if (!isSaaS) {
            return 'zh';
        }
        const language = this.getConfig<string>(ConfigKey.DisplayLanguage);
        return formatDisplayLanguage(language);
    }

    /**
     * 是否开启智能粘贴功能
     */
    getEnableAutoPaste(): boolean {
        if (isSaaS) {
            return false;
        }
        return this.getConfig<boolean>(ConfigKey.EnableAutoPaste, false);
    }

    /**
     * 更新本地语言配置，非 SaaS 没有这个配置，所以更新不会生效
     *
     * @param language 'en' | 'zh'
     */
    updateDisplayLanguage(language: ExtensionDisplayLanguages) {
        if (!isSaaS) {
            return;
        }
        // 下面的 zh 映射成 zh-cn 主要是为了显示上一致，在配置中看到的是：中文 (Simplified Chinese)
        // 而且这个 key 在配置中还不能隐藏用户肯定会看到，所以改成 zh-cn
        // 在代码中都用 zh-cn 的话还得考虑cn的大小写，webview上同步不会不有问题什么的
        return this.config.update(ConfigKey.DisplayLanguage, language === 'zh' ? 'zh-cn' : 'en', true);
    }

    /**
     * 获取安全增强配置
     *
     * @returns 返回布尔值，表示是否启用安全增强功能
     */
    getSecurityEnhancement() {
        if (isPoc) {
            return false;
        }
        return this.getConfig<boolean>(ConfigKey.EnableSecurityEnhancement, true);
    }

    /**
     * 返回单行优先或多行优先的配置
     *
     * @returns 返回 'auto' | 'singleLine' | 'multiLine';
     */
    getLinePreferMode() {
        return this.getConfig<LinePreferMode>(ConfigKey.LinePreferMode);
    }

    /**
     * 获取快捷键注释生成代码开关情况
     *
     * @returns 返回布尔值，表示是否启用快捷键触发注释生成代码功能
     */
    getEnableCommentEnhancement() {
        return this.getConfig<boolean>(ConfigKey.EnableCommentEnhancement);
    }

    private getUnitTestConfig() {
        return {
            java: {
                mockFrame: this.getUnitTestConfigByKey(ConfigKey.UnitTestMockForJava),
                unitTestFrame: this.getUnitTestConfigByKey(ConfigKey.UnitTestFrameworkForJava),
            },
            go: {
                mockFrame: this.getUnitTestConfigByKey(ConfigKey.UnitTestMockForGo),
                unitTestFrame: this.getUnitTestConfigByKey(ConfigKey.UnitTestFrameworkForGo),
            },
            javascript: {
                mockFrame: this.getUnitTestConfigByKey(ConfigKey.UnitTestMockForJs),
                unitTestFrame: this.getUnitTestConfigByKey(ConfigKey.UnitTestFrameworkForJs),
            },
            cpp: {
                mockFrame: this.getUnitTestConfigByKey(ConfigKey.UnitTestMockForCpp),
                unitTestFrame: this.getUnitTestConfigByKey(ConfigKey.UnitTestFrameworkForCpp),
            },
            python: {
                mockFrame: this.getUnitTestConfigByKey(ConfigKey.UnitTestMockForPython),
                unitTestFrame: this.getUnitTestConfigByKey(ConfigKey.UnitTestFrameworkForPython),
            },
        };
    }

    private getUnitTestConfigByKey(key: string) {
        const value = this.config.get<string>(key, '');
        return value === 'auto' ? '' : value;
    }

    private updateCodelensConfig(remoteConfig: UserConfig) {
        const localConfig = this.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        const {
            enableInlineDocstring,
            enableInlineExplain,
            enableInlineUnitTest,
            enableInlineLog,
        } = localConfig ?? {};
        const needUpdate = (local: boolean | undefined, remote: boolean | undefined) => {
            return typeof remote === 'boolean' && local !== remote;
        };
        if (
            needUpdate(enableInlineUnitTest, remoteConfig.enableInlineUnitTest)
            || needUpdate(enableInlineDocstring, remoteConfig.enableInlineCodeToComment)
            || needUpdate(enableInlineExplain, remoteConfig.enableInlineCodeExplain)
            || needUpdate(enableInlineLog, remoteConfig.enableInlineLog)
        ) {
            const config = {
                // NOTE: 有必要把本地的其它值也带上，否则没有更新的本地值会被重写为默认值
                ...localConfig,
                enableInlineDocstring: remoteConfig.enableInlineCodeToComment ?? false,
                enableInlineExplain: remoteConfig.enableInlineCodeExplain ?? false,
                enableInlineUnitTest: remoteConfig.enableInlineUnitTest ?? false,
                enableInlineLog: remoteConfig.enableInlineLog ?? false,
            };
            this.config.update(ConfigKey.EnableCodelens, config, true);
        }
    }

    private updateUnitTestConfig(remoteConfig: UserConfig) {
        const unitTestConfig = remoteConfig.unitTestConfig;
        const allConfigMap = {
            [ConfigKey.UnitTestFrameworkForJava]: unitTestConfig?.java.unitTestFrame,
            [ConfigKey.UnitTestMockForJava]: unitTestConfig?.java.mockFrame,
            [ConfigKey.UnitTestFrameworkForGo]: unitTestConfig?.go.unitTestFrame,
            [ConfigKey.UnitTestMockForGo]: unitTestConfig?.go.mockFrame,
            [ConfigKey.UnitTestFrameworkForCpp]: unitTestConfig?.cpp.unitTestFrame,
            [ConfigKey.UnitTestMockForCpp]: unitTestConfig?.cpp.mockFrame,
            [ConfigKey.UnitTestFrameworkForJs]: unitTestConfig?.javascript.unitTestFrame,
            [ConfigKey.UnitTestMockForJs]: unitTestConfig?.javascript.mockFrame,
            [ConfigKey.UnitTestFrameworkForPython]: unitTestConfig?.python.unitTestFrame,
            [ConfigKey.UnitTestMockForPython]: unitTestConfig?.python.mockFrame,
        };

        for (const [key, value] of Object.entries(allConfigMap)) {
            this.updateUnitTestConfigByKey(key, value);
        }
    }

    private updateUnitTestConfigByKey(key: string, remoteValue?: string) {
        const value = this.getConfig<string>(key);
        const newValue = remoteValue ? remoteValue : 'auto';
        if (newValue !== value) {
            this.config.update(key, newValue, true);
        }
    }

    /**
     * 更新用户是否开启安全增强模式的配置
     * @param remoteSecurityConfig
     * @returns
     */
    private updateSecurityConfig(remoteSecurityConfig: boolean) {
        // poc 没有这个配置，不需要去更新
        if (isPoc) {
            return;
        }
        this.config.update(ConfigKey.EnableSecurityEnhancement, remoteSecurityConfig, true);
    }

    private updateDocstringOutputPositionConfig(remoteDocstringOutputPosition: string) {
        // poc 没有这个配置，不需要去更新
        if (isPoc || !remoteDocstringOutputPosition) {
            return;
        }
        this.config.update(ConfigKey.DocstringOutputPosition, remoteDocstringOutputPosition, true);
    }

    private updateLinePreferModeConfig(remoteLinePreferMode: LinePreferMode) {
        this.config.update(ConfigKey.LinePreferMode, remoteLinePreferMode, true);
    }

    private updateEnableCommentEnhancement(remoteEnableCommentEnhancement: boolean) {
        this.config.update(ConfigKey.EnableCommentEnhancement, remoteEnableCommentEnhancement, true);
    }

    private updateEnableAutoPaste(remoteEnableAutoPaste?: boolean) {
        if (isSaaS || typeof remoteEnableAutoPaste !== 'boolean') {
            return;
        }
        this.config.update(ConfigKey.EnableAutoPaste, remoteEnableAutoPaste, true);
    }

    private updateMemoryConfig(remoteConfig: UserConfig) {
        this.isInsider = remoteConfig.isInsider ?? false;
    }

    // updateCustomizeConfig(config: CustomizeConfig) {
    //     this.config.update(ConfigKey.CustomizeService, config.customizedUrl, true);
    // }

    // private getCustomizeService() {
    //     const customizeService = this.config.get<string>(ConfigKey.CustomizeService);
    //     return customizeService;
    // }

    updatePrivateConfig(url: string) {
        this.config.update(ConfigKey.PrivateService, url, true);
    }

    getPrivateService() {
        const privateService = this.config.get<string>(ConfigKey.PrivateService);
        return privateService;
    }

    /**
     * 强制更新已弃用的配置到最新的，目前主要是 CodeLensDisplayMode、DisplayLanguage
     *
     * @private
     */
    private forceUpdateDeprecatedConfig() {
        // 这里不用 getConfig 的原因是不想配置文件拿不到时从package拿，因为配置文件没有这个配置就不需要强制同步
        const codelensDisplayMode = this.config.get<string>(ConfigKey.CodeLensDisplayMode);
        if (codelensDisplayMode === '文字平铺') {
            this.config.update(ConfigKey.CodeLensDisplayMode, CodeLensDisplayMode.TextTitle, true);
        }
        else if (codelensDisplayMode === '最小化 ICON 展示') {
            this.config.update(ConfigKey.CodeLensDisplayMode, CodeLensDisplayMode.MinimizedIcon, true);
        }

        const language = this.config.get<string>(ConfigKey.DisplayLanguage);
        if (language === 'English') {
            this.updateDisplayLanguage('en');
        }
        else if (language === '中文') {
            this.updateDisplayLanguage('zh');
        }
    }

    /**
     * 更新智能体相关开关配置
     */
    updateAgentConfig(config?: AgentConfig) {
        // vscode暂时只开放了全栈和安全智能体 更新配置时先过滤掉其他
        const updateConfig = {
            enableFullStackIntelligence: config?.enableFullStackIntelligence,
            enableSecurityIntelligence: config?.enableSecurityIntelligence,
            enableCompletionIntelligence: config?.enableCompletionIntelligence,
            enableDebugIntelligence: config?.enableDebugIntelligence,
        };
        this.config.update(ConfigKey.Beta, updateConfig, true);
    }

    /**
     * 获取智能体相关开关配置
     */
    getAgentConfig(): Promise<{enableIntelligenceAgent?: AgentConfig, applyStatus?: AgentApplyStatus}> {
        return new Promise(resolve => {
            const checkInterval = 100;
            let elapsedTime = 0;

            const interval = setInterval(() => {
                // 如果agentApplyStatus不是undefined，或者已经过去了3秒 停止等待，直接返回开关和申请状态
                if (this.agentApplyStatus !== undefined || elapsedTime >= 3000) {
                    const enableIntelligenceAgent = this.getConfig<AgentConfig>(ConfigKey.Beta);
                    resolve({enableIntelligenceAgent, applyStatus: this.agentApplyStatus});
                    clearInterval(interval);
                }
                elapsedTime += checkInterval;
            }, checkInterval);
        });
    }

    getLocalConfigs(hideSensitive: boolean = true) {
        const configs = vscode.workspace.getConfiguration(BAIDU_CONFIG_NAMESPACE).get<Record<string, any>>(
            COMATE_CONFIG_SECTION
        );
        return omit(configs, hideSensitive ? SENSITIVE_CONFIGS : []);
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
