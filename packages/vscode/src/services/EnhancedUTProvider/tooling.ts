import {platform, arch} from 'node:process';
import {sep} from 'node:path';
import {chmod} from 'node:fs/promises';
import {ExecException} from 'node:child_process';
import * as vscode from 'vscode';
import axios from 'axios';
import {execCommand} from '@/utils/cp';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {getIdeName} from '../../common/Fetcher';
import {iocContainer} from '../../iocContainer';
import {download} from '../../utils/download';
import {isFileExist} from '../../utils/fs';
import {error, info} from '../../common/outputChannel';
import {reportTestMateExecution} from '../../api/smartUT';
import {UserService} from '../UserService';

const buildTestMateDownloadUrl = (): string | undefined => {
    if (platform === 'darwin') {
        if (arch === 'x64') {
            return 'https://baidu-coverage.bj.bcebos.com/ftp/install_tools/iUT/testMateCli-darwin-x86-latest';
        }
        else if (arch === 'arm64' || arch === 'arm') {
            return 'https://baidu-coverage.bj.bcebos.com/ftp/install_tools/iUT/testMateCli-darwin-aarch64-latest';
        }
    }
    if (platform === 'linux') {
        if (arch === 'x64') {
            return 'https://baidu-coverage.bj.bcebos.com/ftp/install_tools/iUT/testMateCli-linux-x86-latest';
        }
    }
    return undefined;
};

const getBinariesLocation = async () => {
    const context = await getExtensionContextAsync();
    return vscode.Uri.joinPath(context.globalStorageUri, 'bin');
};

const getTestMateLocation = async () => {
    const binFolder = await getBinariesLocation();
    return vscode.Uri.joinPath(binFolder, 'testMateCli');
};

const getTestMateLocalVersion = (() => {
    let version: string | null = null;

    return async (useCache = true, reportError = false) => {
        if (version && useCache) {
            return version;
        }
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        const rawLocalVersion = await runTestMate('--version', reportError, undefined, 5 * 1000).catch(() => null);
        version = rawLocalVersion ? rawLocalVersion.replace(/^COMATE:/, '') : null;
        return version;
    };
})();

const getTestMateLatestVersion = (() => {
    let remoteVersion: string | null = null;
    let lastRequestTime = 0;

    return async () => {
        const isExpired = Date.now() - lastRequestTime > 10 * 60 * 1000; // 10 分钟
        if (remoteVersion && !isExpired) {
            return remoteVersion;
        }
        try {
            const res = await axios.get<string>('https://baidu-coverage.bj.bcebos.com/ftp/install_tools/iUT/VERSION');
            remoteVersion = res.data.trim();
            lastRequestTime = Date.now();
            return remoteVersion;
        }
        catch (e) {
            error('failed to get latest version number of TestMate', (e as Error).message);
            return remoteVersion;
        }
    };
})();

const reportExecution = async (succeed: boolean, reason?: string) => {
    const [userName] = await iocContainer.get(UserService).getCurrentUser().catch(() => ['']);
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    const localVersion = await getTestMateLocalVersion();
    const latestVersion = await getTestMateLatestVersion();
    reportTestMateExecution({
        userName,
        ideType: 'vscode',
        iutVersion: localVersion ?? '',
        bosVersion: latestVersion ?? '',
        system: platform,
        arch,
        upState: succeed ? 0 : 1,
        reason,
    });
};

const UNREAL_NAMES = new Set(['root', 'work', 'map', 'unknown', 'Administrator', 'mac']);
const VALID_NAME_REG = /^[a-zA-Z0-9_]+$/;

export const buildTracingArgs = async () => {
    const username = await iocContainer
        .get(UserService)
        .getCurrentUser()
        .then(([value]) => {
            if (UNREAL_NAMES.has(value)) {
                return null;
            }
            if (VALID_NAME_REG.test(value)) {
                return value;
            }
            return null;
        })
        .catch(() => null);
    const ide = getIdeName();
    return [
        '--useSource',
        ide,
        ...(username ? ['--userName', username] : []),
    ];
};

export const runTestMate = async (args: string, report = true, cwd?: string, timeout?: number) => {
    const location = await getTestMateLocation();
    const command = `export CGO_ENABLED=1; "${location.fsPath}" ${args}`;
    info(`Running command: ${command}`);
    try {
        const stdout = await execCommand(
            command,
            cwd ?? vscode.env.appRoot,
            {timeout}
        );
        report && reportExecution(true);
        return stdout;
    }
    catch (e) {
        const err = e as ExecException;
        error(`Failed to run Test Mate: exit code ${err.code}, ${err.message}`);
        report && reportExecution(false, `exit code: ${err.code}, message: ${err.message}`);
        throw e;
    }
};

const shouldInstallTestMate = async () => {
    const toolLocation = await getTestMateLocation();
    const isExist = await isFileExist(toolLocation.fsPath);
    if (!isExist) {
        info('Test Mate is not installed.');
        return true;
    }
    const localVersion = await getTestMateLocalVersion();
    const latestVersion = await getTestMateLatestVersion();
    info(`Test Mate versions: local - ${localVersion}, latest - ${latestVersion}`);
    if (localVersion === latestVersion) {
        vscode.commands.executeCommand('setContext', 'baidu.comate.context.enableBatchUnitTest', true);
    }
    return localVersion !== latestVersion;
};

export const autoUpdateTestMate = async () => {
    const shouldInstall = await shouldInstallTestMate();
    if (!shouldInstall) {
        return;
    }
    const downloadFrom = buildTestMateDownloadUrl();
    if (!downloadFrom) {
        reportExecution(false, 'unsupported system or arch');
        info(`Test Mate is current not supported on ${platform}-${arch}`);
        return;
    }
    info(`Try to install Test Mate from ${downloadFrom}`);
    const toolLocation = await getTestMateLocation();
    const lastSepIndex = toolLocation.fsPath.lastIndexOf(sep);
    const folder = toolLocation.fsPath.slice(0, lastSepIndex);
    const filename = toolLocation.fsPath.slice(lastSepIndex + 1);
    try {
        await download(
            downloadFrom,
            folder,
            filename
        );
        await chmod(toolLocation.fsPath, 0o755);
        info(`Successfully downloaded Test Mate to ${toolLocation.fsPath}`);
        const version = await getTestMateLocalVersion(false, true);
        vscode.commands.executeCommand('setContext', 'baidu.comate.context.enableBatchUnitTest', true);
        info(`Current Test Mate version: ${version}`);
    }
    catch (e) {
        reportExecution(false, `Download error: ${(e as Error).message}`);
        error(`Failed to download Test Mate: ${(e as Error).message}`);
    }
};
