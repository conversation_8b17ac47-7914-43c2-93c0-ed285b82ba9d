import * as vscode from 'vscode';
import {injectable} from 'inversify';
import {error, info} from '@/common/outputChannel';
import {Repository} from '@/external/builtinGitExtension';
import {BuiltinGitProvider} from './BuiltinGitProvider';

interface RepositoryStateProviderConfig {
    // 性能阈值配置
    performanceThresholdMs?: number;
    // 清理间隔
    cleanupIntervalMs?: number;
}

@injectable()
export class RepositoryStateProvider implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    private readonly repositoryListeners: Map<string, vscode.Disposable> = new Map();
    private readonly listeners: Set<(repository: Repository) => void> = new Set();
    private readonly config: Required<RepositoryStateProviderConfig>;

    constructor() {
        this.config = {
            performanceThresholdMs: 10,
            cleanupIntervalMs: 60 * 60 * 1000,
        };

        this.watchRepositoryStateChange();
        this.startCleanupTimer();
    }

    private async watchRepositoryStateChange() {
        let attemptCount = 0;
        const maxAttemptCount = 5;

        while (attemptCount < maxAttemptCount) {
            try {
                const gitProvider = BuiltinGitProvider.createProvider();
                if (!gitProvider) {
                    attemptCount++;
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    continue;
                }

                const repositories = gitProvider.repositories;
                info(`Initializing repository state tracking for ${repositories.length} repositories`);

                repositories.forEach(repository => {
                    try {
                        this.onRepositoryStateChanged(repository);
                    }
                    catch (ex) {
                        error('Failed to initialize state tracking for repository', (ex as Error).message);
                    }
                });

                const disposable = gitProvider.onDidOpenRepository(repository => {
                    this.onRepositoryStateChanged(repository);
                });

                this.disposables.push(disposable);
                break;
            }
            catch (ex) {
                attemptCount++;
                if (attemptCount >= maxAttemptCount) {
                    error('Failed to watch repository state change', (ex as Error).message);
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }
    }

    private onRepositoryStateChanged(repository: Repository) {
        const key = repository.rootUri.toString();

        // 如果已经存在监听器，先取消
        if (this.repositoryListeners.has(key)) {
            this.repositoryListeners.get(key)?.dispose();
        }

        const changeListener = repository.state.onDidChange(() => {
            const startTime = performance.now();
            this.listeners.forEach(listener => {
                try {
                    listener(repository);
                } catch (ex) {
                    error('Error in repository state listener', (ex as Error).message);
                }
            });
            const endTime = performance.now();

            if (endTime - startTime > this.config.performanceThresholdMs) {
                info(`Slow repository state change handling: ${endTime - startTime}ms`);
            }
        });

        this.repositoryListeners.set(key, changeListener);
        this.disposables.push(changeListener);
    }

    private cleanupStaleListeners() {
        const gitProvider = BuiltinGitProvider.createProvider();
        if (!gitProvider) {
            return;
        }

        // 移除不再存在的仓库的监听器
        this.repositoryListeners.forEach((listener, key) => {
            const exists = gitProvider.repositories.some(
                repo => repo.rootUri.toString() === key
            );

            if (!exists) {
                listener.dispose();
                this.repositoryListeners.delete(key);
            }
        });
    }

    // 定期清理 暂定一小时清理一次
    private startCleanupTimer() {
        const cleanupInterval = setInterval(() => {
            this.cleanupStaleListeners();
        }, this.config.cleanupIntervalMs);

        this.disposables.push({
            dispose: () => clearInterval(cleanupInterval),
        });
    }

    /**
     * 添加仓库状态变更监听器
     * @param listener 监听器回调函数
     * @returns 取消监听的方法
     */
    addRepositoryStateListener(listener: (repository: Repository) => void): () => void {
        this.listeners.add(listener);
        return () => {
            this.listeners.delete(listener);
        };
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.repositoryListeners.forEach(listener => listener.dispose());
        this.listeners.clear();
        this.repositoryListeners.clear();
    }
}
