import path from 'node:path';
import * as vscode from 'vscode';
import {Lazy<PERSON><PERSON><PERSON><PERSON>dentifer, inject, injectable} from 'inversify';
import {CreatePromptTemplatePayload, EventMessage, InputBoxMessageHistory, UserMessage} from '@shared/protocols';
import {
    ACTION_PROMPTTEMPLATE_LIST,
    ACTION_PROMPTTEMPLATE_CREATE,
    ACTION_PROMPTTEMPLATE_DELETE,
    getPromptTemplateRootPath,
    getPromptTemplatePath,
    ACTION_PROMPTTEMPLATE_UPDATE,
    getPromptTemplateUuidFromPath,
} from '@comate/plugin-shared-internals';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {INPUTBOX_HISTORY_KEY} from '@/constants';
import {ChatViewProvider} from '../ChatViewProvider';
import {KernelProvider} from '../KernelProvider';
import {
    CMD_COMATE_PLUS_CREATE_PROMPTTEMPLATE,
    CMD_COMATE_PLUS_DELETE_PROMPTTEMPLATE,
    CMD_COMATE_PLUS_EDIT_PROMPTTEMPLATE,
} from '../ComatePlusChatSession/constants';
import {LogCategory, LogUploaderProvider} from '../LogUploaderProvider';

const PromptCompareThreshold = {
    similarity: 0.8,
    contentLength: 10,
    count: 3,
};

interface DeleteResponse {
    status: string;
    payload?: string;
}

function getMatchingCharacters(s1: string, s2: string, maxDistance: number) {
    const matched1 = [];
    const matched2 = [];
    const s2Matches = Array(s2.length).fill(false);

    for (let i = 0; i < s1.length; i++) {
        const start = Math.max(0, i - maxDistance);
        const end = Math.min(i + maxDistance + 1, s2.length);

        for (let j = start; j < end; j++) {
            if (!s2Matches[j] && s1[i] === s2[j]) {
                matched1.push(s1[i]);
                matched2.push(s2[j]);
                s2Matches[j] = true;
                break;
            }
        }
    }

    return [matched1, matched2];
}

function jaroDistance(s1: string, s2: string) {
    if (!s1.length || !s2.length) {
        return 0;
    }

    const maxDistance = Math.floor(Math.max(s1.length, s2.length) / 2) - 1;
    const [matched1, matched2] = getMatchingCharacters(s1, s2, maxDistance);

    const m = matched1.length;
    if (m === 0) {
        return 0;
    }

    let t = 0;
    for (let i = 0; i < m; i++) {
        if (matched1[i] !== matched2[i]) {
            t++;
        }
    }
    t = t / 2;

    return (m / s1.length + m / s2.length + (m - t) / m) / 3;
}

function jaroWinklerDistance(s1: string, s2: string, prefixScale = 0.1) {
    const jaroDist = jaroDistance(s1, s2);

    let prefixLength = 0;
    const maxPrefixLength = 4;
    for (let i = 0; i < Math.min(s1.length, s2.length, maxPrefixLength); i++) {
        if (s1[i] === s2[i]) {
            prefixLength++;
        }
        else {
            break;
        }
    }

    return jaroDist + prefixLength * prefixScale * (1 - jaroDist);
}

@injectable()
export class PromptTemplateProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    // 记录未保存的PromptTemplate文件
    private readonly unsavedFileMap: Map<string, boolean> = new Map();

    constructor(
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(LogUploaderProvider) private readonly loggerProvider: LogUploaderProvider
    ) {
        this.disposables.push(
            vscode.commands.registerCommand(
                CMD_COMATE_PLUS_CREATE_PROMPTTEMPLATE,
                this.createPromptTemplate.bind(this)
            ),
            vscode.commands.registerCommand(
                CMD_COMATE_PLUS_EDIT_PROMPTTEMPLATE,
                this.editPromptTemplate.bind(this)
            ),
            vscode.commands.registerCommand(
                CMD_COMATE_PLUS_DELETE_PROMPTTEMPLATE,
                this.deletePromptTemplate.bind(this)
            ),
            vscode.workspace.onDidSaveTextDocument(
                async (document: vscode.TextDocument) => {
                    try {
                        const filePath = document.fileName;
                        if (this.isPromptTemplateFile(filePath)) {
                            const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                            const shouldSave = await this.shouldSavePromptTemplate(filePath, workspacePath!);
                            await vscode.languages.setTextDocumentLanguage(document, 'markdown');
                            if (shouldSave) {
                                // eslint-disable-next-line max-depth
                                if (this.unsavedFileMap.get(filePath)) {
                                    this.chatViewProvider.sendDataToWebview(EventMessage.PromptTemplateSaveEvent);
                                    this.unsavedFileMap.delete(filePath);
                                    this.loggerProvider.logUserAction({
                                        category: LogCategory.PromptTemplate,
                                        action: 'save',
                                    });
                                }
                                else {
                                    this.chatViewProvider.sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                                        type: 'success',
                                        message: '自定义指令已保存',
                                        duration: 3000,
                                    });
                                }
                            }
                        }
                    }
                    catch (e) {
                        //
                    }
                }
            )
        );
    }

    private isPromptTemplateFile(filePath: string) {
        const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspacePath) {
            return false;
        }
        return filePath.endsWith('.prompt')
            && (path.dirname(filePath) === getPromptTemplateRootPath(workspacePath));
    }

    private async shouldSavePromptTemplate(filePath: string, workspacePath: string) {
        const result: DeleteResponse | undefined = await this.kernelProvider.client?.sendRequest(
            ACTION_PROMPTTEMPLATE_UPDATE,
            {data: getPromptTemplateUuidFromPath(filePath, workspacePath)}
        );
        if (result?.status !== 'success') {
            this.chatViewProvider.sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                type: 'fail',
                message: result?.payload,
            });
            return false;
        }
        return true;
    }

    updatePromptTemplates = async () => {
        const {
            payload: promptTemplates,
        }: any = await this.kernelProvider.client?.sendRequest(ACTION_PROMPTTEMPLATE_LIST, {});
        return this.chatViewProvider.sendDataToWebview(EventMessage.PromptTemplateListUpdateEvent, promptTemplates);
    };

    async createPromptTemplate(data?: CreatePromptTemplatePayload) {
        const {status, payload}: any = await this.kernelProvider.client?.sendRequest(
            ACTION_PROMPTTEMPLATE_CREATE,
            // ideListener类型限制了，放在data字段中吧
            data ? {...data, data: {isSystem: false}} : {data: {isSystem: true}}
        );

        if (status === 'success') {
            const filePath = vscode.Uri.file(payload.uri);
            try {
                const document = await vscode.workspace.openTextDocument(filePath.with({scheme: 'untitled'}));
                await vscode.languages.setTextDocumentLanguage(document, 'markdown');
                const editor = await vscode.window.showTextDocument(document);
                editor.edit(editBuilder => {
                    const position = new vscode.Position(0, 0);
                    editBuilder.insert(position, payload.content || '');
                    if (!this.unsavedFileMap.get(payload.uri)) {
                        this.unsavedFileMap.set(payload.uri, true);
                    }
                });
            }
            catch (e) {
                this.unsavedFileMap.get(payload.uri) && this.unsavedFileMap.delete(payload.uri);
                vscode.window.showErrorMessage(`创建自定义指令文件时发生错误: ${(e as Error)?.message}`);
            }
        }
        if (status === 'error') {
            await this.chatViewProvider.sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                type: 'fail',
                message: payload,
            });
        }
    }

    async editPromptTemplate(uuid: string) {
        try {
            const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspacePath) {
                throw new Error('当前未打开工作区');
            }
            const uri = vscode.Uri.file(getPromptTemplatePath(workspacePath, uuid));
            const document = await vscode.workspace.openTextDocument(uri);
            await vscode.languages.setTextDocumentLanguage(document, 'markdown');
            await vscode.window.showTextDocument(document);
        }
        catch (e) {
            vscode.window.showErrorMessage(`打开自定义指令文件失败: ${(e as Error)?.message}`);
        }
    }

    async deletePromptTemplate(uuid: string) {
        const result: DeleteResponse | undefined = await this.kernelProvider.client?.sendRequest(
            ACTION_PROMPTTEMPLATE_DELETE,
            {data: uuid}
        );
        if (result?.status === 'success') {
            try {
                const editor = vscode.window.activeTextEditor;
                const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                this.chatViewProvider.sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                    type: 'success',
                    message: '删除自定义指令成功',
                });
                if (workspacePath && editor?.document.fileName === getPromptTemplatePath(workspacePath, uuid)) {
                    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
            }
            catch (e) {
                //
            }
            await this.updatePromptTemplates();
        }
        else {
            await this.chatViewProvider.sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                type: 'fail',
                message: result?.payload,
            });
        }
    }

    async comparePrompt(message: Pick<UserMessage, 'content'>) {
        if (message.content.length > PromptCompareThreshold.contentLength) {
            const context = await getExtensionContextAsync();
            const history = context.globalState.get<InputBoxMessageHistory[]>(INPUTBOX_HISTORY_KEY) || [];
            const similarInputs = history.filter(historyInput => {
                const similarity = jaroWinklerDistance(message.content, historyInput.value);
                return similarity >= PromptCompareThreshold.similarity;
            });

            return similarInputs.length > PromptCompareThreshold.count;
        }
        return false;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
