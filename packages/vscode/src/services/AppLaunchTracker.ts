import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {LogCategory, LogUploaderProvider} from './LogUploaderProvider';

@injectable()
class AppLaunchTracker implements vscode.Disposable {
    private static readonly CHECK_INTERVAL = 1000 * 60 * 60; // 每小时检查一次

    private checkTimer: NodeJS.Timer | null = null;

    constructor(@inject(LogUploaderProvider) private readonly logUploadProvider: LogUploaderProvider) {
        this.track();
        this.startPeriodicCheck();
    }

    private startPeriodicCheck() {
        this.checkTimer = setInterval(() => {
            this.track();
        }, AppLaunchTracker.CHECK_INTERVAL);
    }

    async track() {
        this.logUploadProvider.logUserAction({
            category: LogCategory.App,
            action: 'launch',
        });
    }

    dispose() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
    }
}

export default AppLaunchTracker;
