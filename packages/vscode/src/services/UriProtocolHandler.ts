import * as vscode from 'vscode';
import qs from 'qs';
import {SHOW_CHAT_PANEL_AND_CMD_COMATE_PLUS_START} from './ComatePlusChatSession/constants';

export class ComateUriProtocolHandler implements vscode.UriHandler, vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    constructor() {
        this.disposables.push(vscode.window.registerUriHandler(this));
    }

    handleUri(uri: vscode.Uri) {
        switch (uri.path) {
            case '/open-description':
                this.openDescription();
                break;
            case '/open-comate-plus':
                this.openComatePlus(uri);
                break;
        }
    }

    private openComatePlus(uri: vscode.Uri) {
        const urlQuery = qs.parse(uri.query);
        if (typeof urlQuery.file === 'string') {
            // file 参数指定打开文件，line 参数指定选中行号
            vscode.workspace.openTextDocument(vscode.Uri.file(urlQuery.file)).then(doc => {
                const line = typeof urlQuery.line === 'string' ? parseInt(urlQuery.line, 10) : NaN;
                const selection = line >= 0 && line < doc.lineCount
                    ? doc.lineAt(line).range
                    : undefined;
                vscode.window.showTextDocument(doc, {
                    selection,
                    preview: false,
                });
            });
        }
        vscode.commands.executeCommand(SHOW_CHAT_PANEL_AND_CMD_COMATE_PLUS_START, {
            source: urlQuery.source || 'OpenComatePlus',
            pluginName: urlQuery.plugin,
            capability: urlQuery.command,
            query: urlQuery.query,
            data: urlQuery.data,
            traceId: urlQuery.traceId,
            originURI: uri,
        });
    }

    private openDescription() {
        // 打开插件介绍页
        vscode.commands.executeCommand(
            'vscode.open',
            vscode.Uri.parse(`${vscode.env.uriScheme}:extension/baidu.comate`)
        );
        // 左侧栏定位到插件搜索框，用插件id筛选出插件
        vscode.commands.executeCommand('workbench.extensions.action.showExtensionsWithIds', ['baidu.comate']);
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}

export default new ComateUriProtocolHandler();
