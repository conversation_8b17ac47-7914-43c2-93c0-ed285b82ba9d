import Parser from 'web-tree-sitter';

export const COMPONENT_NAME_CHECKER_REGEX = /^[A-Z]/;

export const getFunctionName = (node: Parser.SyntaxNode) => {
    let functionNode: Parser.SyntaxNode | undefined = node;
    switch (node.type) {
        case 'function_declaration':
            break;
        case 'lexical_declaration':
            functionNode = node.namedChildren.find(child => child.type === 'variable_declarator');
            break;
        default:
            functionNode = undefined;
            break;
    }
    return functionNode?.childForFieldName('name')?.text;
};

export const checkIsReactComponent = (node: Parser.SyntaxNode) => {
    const functionName = getFunctionName(node);
    return functionName ? COMPONENT_NAME_CHECKER_REGEX.test(functionName) : false;
};

export const checkIsTrivial = (functionNode: Parser.SyntaxNode) => {
    if (functionNode.endPosition.row - functionNode.startPosition.row < 15) {
        // 如果函数体行数小于 15 行，则认为是 trivial
        return true;
    }
    const numOfJsx = functionNode.descendantsOfType('jsx_element').length
        + functionNode.descendantsOfType('jsx_self_closing_element').length;
    if (numOfJsx === 0) {
        return true;
    }
    if (numOfJsx > 1) {
        // 如果 jsx 元素超过 1 个，则不认为是 trivial，可能需要拆分
        return false;
    }
    // 只有 1 个 jsx 元素时，确保函数体内的声明数量
    const functionBody = functionNode.descendantsOfType('statement_block')[0];
    if (functionBody) {
        const numOfDeclarations = functionBody
            .children
            .filter(item => item.type === 'lexical_declaration' || item.type === 'expression_statement')
            .length;
        if (numOfDeclarations >= 1) {
            // 如果声明数量至少 1 个，则不认为是 trivial，可能需要拆分
            return false;
        }
    }
    return true;
};

export const trimMarkdownCodeBlockSyntax = (content: string) => {
    return content.replace(/^\s*```.*\n/, '').replace(/\n\s*```$/, '\n');
};
