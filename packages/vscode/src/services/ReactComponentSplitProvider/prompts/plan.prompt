# 任务描述
用户指定了一个 React 组件，意图是从中找到可复用的逻辑提取出来，让原组件更可读也促进代码复用。你的任务是根据下方要求，输出拆分计划。

请聚焦在以下 3 个类型的拆分：

## 1. 通用计算逻辑（computation）
- 纯粹的计算过程，没有什么副作用。
- 注意命名不能以 `use` 开头，否则会被识别为 hook。

## 2. 有状态的逻辑处理（hook）
- 多个基础 Hook（useEffect, useMemo, useCallback, useRef...）在配合实现一些业务的逻辑，这些可能会在其它组件中也用到。
- 注意 React 中 hook 的命名必须以 `use` 开头。

## 3. 独立的 UI 元素（jsx）
- 截取原 JSX 里的部分代码，它通常是由多个html基础元素组成的视觉块，可独立存在，也会在其它组件复用

---

# 指定的组件（已隐藏上下文）：
```tsx
{{code}}
```

请你从一名专业 React 工程师的视角审阅{{componentName}}组件，按任务要求给出拆分计划。

# 要点说明：
1. 计划要清晰明确，能清楚知道做什么，拆分哪些代码。
2. 注意拆分粒度，避免拆出的是个很薄的 wrapper 函数，切勿为了拆分而拆分（被拆的代码通常是由多行的逻辑组成）。
3. 结合代码给出最合适的建议，不是每个类型（computation、hook、jsx）的拆分都需要，鼓励保守策略，拆分出真正有复用价值的逻辑。
4. 给出建议即可，不要给出具体的代码。
5. 每个拆分类型最多出现一个，因此你要选出最合适的。

请确保输出的格式符合以下 JSON schema：

```json
{
  "type": "array",
  "description": "代码拆分方案数组，每个元素是一个具体的模块拆分设计",
  "items": {
    "type": "object",
    "description": "拆分后的jsx、hook、computation信息",
    "properties": {
      "kind": {
        "type": "string",
        "description": "模块类型",
        "enum": ["jsx", "hook", "computation"]
      },
      "name": {
        "type": "string",
        "description": "函数名称"
      },
      "plan": {
        "type": "string",
        "description": "说明拆分计划，拆分后模块的设计（比如参数/内部状态/返回值）"
      },
      "alreadyExists": {
         "type": "boolean",
         "description": "请回顾原代码确认是否已存在该函数"
       }
    },
    "required": ["kind", "name", "plan", "alreadyExists"],
    "additionalProperties": false
  }
}
```