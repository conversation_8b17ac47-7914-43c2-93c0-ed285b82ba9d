import {extname} from 'node:path';
import * as vscode from 'vscode';
import {inject, injectable} from 'inversify';
import Parser from 'web-tree-sitter';
import axios from 'axios';
import {noop, omit, sortBy} from 'lodash';
import {SSEProcessor} from '@comate/plugin-shared-internals';
import {TYPES} from '@/inversify.config';
import {
    CMD_SHOW_REACT_COMPONENT_SPLIT_FAILURE_REASON,
    CMD_SHOW_REACT_COMPONENT_SPLIT_RESULT,
    CMD_SPLIT_REACT_COMPONENT,
} from '@/constants';
import {substitute} from '@/utils/prompt';
import {getFirstCodeBlock, safeParseJson} from '@/utils/common';
import {
    acceptCode,
    forwardToQianfan,
    generateTrackUuid,
    QianfanStreamErrorResponse,
    QianfanStreamResponse,
    showCode,
} from '@/api';
import {WithRequired} from '@/utils/typeUtils';
import {checkTreeSitterSupport, extractFunctionNodes} from '@/utils/treeSitterUtils';
import {buildParams} from '@/common/Fetcher';
import {debug} from '@/common/outputChannel';
import {isSaasOrPoc} from '@/utils/features';
import {splitIntoWords} from '@/utils/textSimilarity';
import {CodeLensProvider} from '../FoldedCodeLensProvider';
import {CodelensConfig, CodeLensDisplayMode, ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {DiffProvider} from '../DiffProvider';
import {ILicenseController} from '../LicenseController/types';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {UserService} from '../UserService';
import {SplitKind, SplitPlan} from './types';
import planPromptTemplate from './prompts/plan.prompt';
import splitPromptTemplate from './prompts/split.prompt';
import mergePromptTemplate from './prompts/merge.prompt';
import {checkIsReactComponent, checkIsTrivial, getFunctionName, trimMarkdownCodeBlockSyntax} from './utils';

// NOTE: 当前 EB4-turbo 支持输入 5120 tokens, 输出 2048 tokens，因此过大的组件无法完整输入或输出，需要做个经验性的限制。
// 当前最复杂的 prompt 模版占用了 600 tokens，还剩 4520 个 tokens 给代码，经验上，从代码提取单词后，平均一个单词占 4 个 tokens，因此可支持长度最大为 1130 个单词的组件。
// 同时由于输出限制在了 2048 个 tokens（约 512 个单词），在最后简化原组件环节，模型会输出相当一部分的原组件，因此这也限制了输入的组件的大小。
// 综上，当前就支持字符数最大在 1000 个单词内的组件。
const MAX_SUPPORTED_WORD_COUNT = 1000;

const PLAN_PRIORITY: SplitKind[] = ['computation', 'hook', 'jsx'];

interface JobState {
    filePath: string;
    componentName: string;
    // 原组件定义
    componentDefinition: string;
    status: 'running' | 'succeed' | 'failed';
    stage: 'plan' | 'split' | 'merge';
    // 原组件所在文件触发时的内容
    documentText: string;
    // 原组件在触发时的范围
    componentRange: vscode.Range;
    // 原组件的单词数
    wordCount: number;
    plans?: string;
    subFunctions?: string;
    merged?: string;
    reason?: string;
    // 采纳时上报的 uuid
    uuidPromise?: Promise<string | undefined>;
    // 是否已经上报展示
    hasReportedShown?: boolean;
}

@injectable()
export class ReactComponentSplitProvider implements CodeLensProvider, vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    static supportedLanguages = [
        'typescriptreact',
        'javascriptreact',
    ];
    private readonly _onDidChangeCodeLenses: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();
    onDidChangeCodeLenses: vscode.Event<void> = this._onDidChangeCodeLenses.event;
    private readonly jobStates: Map<string, JobState> = new Map();

    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(UserService) private readonly userService: UserService,
        @inject(TYPES.ILicenseController) private readonly licenseController: ILicenseController
    ) {
        this.disposables.push(
            vscode.languages.registerCodeLensProvider(
                ReactComponentSplitProvider.supportedLanguages.map(language => ({
                    scheme: 'file',
                    language: language,
                })),
                this
            ),
            // 注册组件拆分命令
            vscode.commands.registerCommand(
                CMD_SPLIT_REACT_COMPONENT,
                this.startSplitJob.bind(this)
            ),
            // 注册查看拆分结果命令
            vscode.commands.registerCommand(
                CMD_SHOW_REACT_COMPONENT_SPLIT_RESULT,
                this.showSplitResult.bind(this)
            ),
            // 注册查看拆分失败原因命令
            vscode.commands.registerCommand(
                CMD_SHOW_REACT_COMPONENT_SPLIT_FAILURE_REASON,
                this.showSplitFailureReason.bind(this)
            )
        );
    }

    async provideCodeLenses(
        document: vscode.TextDocument,
        cancelToken: vscode.CancellationToken
    ): Promise<vscode.CodeLens[]> {
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();
        if (codelensDisplayMode !== CodeLensDisplayMode.TextTitle) {
            return [];
        }
        if (this.diffProvider.isInDiff(document.uri.fsPath)) {
            return [];
        }
        const codelens = this.computeCodeLenses(document, cancelToken);
        return codelens;
    }

    async computeCodeLenses(
        document: vscode.TextDocument,
        cancelToken?: vscode.CancellationToken
    ): Promise<vscode.CodeLens[]> {
        const codelensConfig = this.configProvider.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        if (codelensConfig?.enableInlineSplit !== true || !this.licenseController.hasLicense) {
            return [];
        }
        return this.treeSitterProvider.getFunctionCodeLens(
            document,
            (node: Parser.SyntaxNode) => {
                return checkIsReactComponent(node) && !checkIsTrivial(node);
            },
            (document: vscode.TextDocument, range: vscode.Range, node: Parser.SyntaxNode) => {
                const command = this.decideCodeLensCommand(document, range, node, cancelToken);
                return command;
            }
        );
    }

    private async showSplitResult(jobKey: string) {
        const job = this.jobStates.get(jobKey);
        if (!job || !job.merged) {
            // TODO: 提示失败
            return;
        }
        // 上报展示
        if (!job.hasReportedShown) {
            job.uuidPromise?.then(async uuid => {
                if (uuid) {
                    await showCode(uuid);
                    job.hasReportedShown = true;
                }
            });
        }
        const replacement = this.buildReplacementCode(job);
        const document = await vscode.workspace.openTextDocument(job.filePath);
        const onAccept = async () => {
            this.jobStates.delete(jobKey);
            this._onDidChangeCodeLenses.fire();
            const uuid = await job.uuidPromise;
            if (uuid) {
                // TODO: 其实用户可能会编辑生成的代码后再点采纳，此时上报的还是原本生成的内容，会有些不准确。
                // 目前认为如果用户点了采纳说明大体正确，就直接认为全采纳了，后续可以看看这类情况应该如何统计。
                acceptCode({
                    uuid,
                    accepted: true,
                    content: '',
                });
            }
        };
        const onReject = async () => {
            this.jobStates.delete(jobKey);
            this._onDidChangeCodeLenses.fire();
        };
        const componentRange = this.locateComponent(document, job.componentName) ?? job.componentRange;
        const {diff} = this.diffProvider.createReplacementDiffHandler(
            document,
            componentRange,
            content => content,
            undefined,
            undefined,
            onAccept,
            onReject
        );
        diff(replacement);
    }

    private buildReplacementCode(job: JobState) {
        const subFunctions = job.subFunctions;
        const merged = job.merged;
        const replacement = subFunctions + '\n\n' + merged + '\n';
        return replacement;
    }

    private async showSplitFailureReason(jobKey: string) {
        const job = this.jobStates.get(jobKey);
        if (!job || !job.reason) {
            return;
        }
        vscode.window.showErrorMessage(`拆分失败：${job.reason}`);
        const timeout = setTimeout(
            () => {
                this.jobStates.delete(jobKey);
                this._onDidChangeCodeLenses.fire();
            },
            5 * 1000
        );
        this.disposables.push({
            dispose: () => {
                clearTimeout(timeout);
            },
        });
    }

    private locateComponent(document: vscode.TextDocument, componentName: string): vscode.Range | undefined {
        const tree = this.treeSitterProvider.getDocumentTree(document);
        const language = checkTreeSitterSupport(document.languageId);
        if (tree && language) {
            const nodes = extractFunctionNodes(language, tree.rootNode);
            const currentNode = nodes.find(item => getFunctionName(item) === componentName);
            if (currentNode) {
                // NOTE: range 调整为完整的首行、尾行
                return new vscode.Range(currentNode.startPosition.row, 0, currentNode.endPosition.row + 1, 0);
            }
        }
        return undefined;
    }

    // eslint-disable-next-line max-statements
    private async startSplitJob(
        document: vscode.TextDocument,
        range: vscode.Range,
        node: Parser.SyntaxNode,
        cancelToken?: vscode.CancellationToken
    ) {
        const componentName = getFunctionName(node);
        if (!componentName) {
            // TODO: 提示失败
            return;
        }
        const [jobKey, job] = this.initJobState(document, componentName, range, node);
        this._onDidChangeCodeLenses.fire();
        if (job.wordCount > MAX_SUPPORTED_WORD_COUNT) {
            // 如果组件过大，则不进行拆分
            const timeout = setTimeout(
                () => {
                    this.jobStates.delete(jobKey);
                    this._onDidChangeCodeLenses.fire();
                },
                7 * 1000
            );
            this.disposables.push(
                {
                    dispose: () => {
                        clearTimeout(timeout);
                    },
                }
            );
            return;
        }
        // 生成拆分计划
        try {
            job.plans = await this.generatePlans(job, cancelToken);
        }
        catch (e) {
            job.status = 'failed';
            job.reason = (e as Error).message;
            return;
        }
        // 生成拆分后的函数
        job.stage = 'split';
        this._onDidChangeCodeLenses.fire();
        try {
            job.subFunctions = await this.generateSubFunctions(job as WithRequired<JobState, 'plans'>, cancelToken);
        }
        catch (e) {
            job.status = 'failed';
            job.reason = (e as Error).message;
            return;
        }
        // 简化原组件
        job.stage = 'merge';
        this._onDidChangeCodeLenses.fire();
        try {
            job.merged = await this.generateMerged(
                job as WithRequired<JobState, 'plans' | 'subFunctions'>,
                cancelToken
            );
        }
        catch (e) {
            job.status = 'failed';
            job.reason = (e as Error).message;
            return;
        }
        job.status = 'succeed';
        this._onDidChangeCodeLenses.fire();
        job.uuidPromise = this.generateTrackUuid(document, jobKey);
        job.uuidPromise.then(uuid => {
            debug(`component: ${componentName}, uuid: ${uuid}`);
        });
        // 拆分完成，假如 codelens 已经不在当前窗口的可视范围，则提示用户查看
        const visibleRanges = vscode
            .window
            .visibleTextEditors
            .filter(editor => editor.document.uri.fsPath === document.uri.fsPath)
            .flatMap(editor => editor.visibleRanges);
        const isCodeLensVisible = visibleRanges.some(visibleRange => visibleRange.contains(job.componentRange.start));
        if (!isCodeLensVisible) {
            const originalLineCount = job.componentDefinition.trim().split('\n').length;
            const mergedLineCount = job.merged?.trim().split('\n').length;
            const choice = await vscode.window.showInformationMessage(
                `组件拆分完成：${componentName} 组件从 ${originalLineCount} 行简化为 ${mergedLineCount} 行，是否查看？`,
                '查看'
            );
            if (choice === '查看') {
                this.showSplitResult(jobKey);
            }
        }
    }

    private async generateMerged(
        job: WithRequired<JobState, 'plans' | 'subFunctions'>,
        cancelToken?: vscode.CancellationToken
    ): Promise<string> {
        const prompt = substitute(mergePromptTemplate, {
            code: job.componentDefinition,
            componentName: job.componentName,
            plans: job.plans,
            subFunctions: job.subFunctions,
        });
        const {content} = await this.genericGenerate({
            prompt,
            filePath: job.filePath,
            onUpdate: noop,
        }, cancelToken);
        const merged = getFirstCodeBlock(content);
        if (!merged) {
            throw new Error('未能重组原组件');
        }
        return trimMarkdownCodeBlockSyntax(merged);
    }

    private async generateSubFunctions(
        job: WithRequired<JobState, 'plans'>,
        cancelToken?: vscode.CancellationToken
    ): Promise<string> {
        const prompt = substitute(splitPromptTemplate, {
            code: job.componentDefinition,
            componentName: job.componentName,
            plans: job.plans,
        });
        const {content} = await this.genericGenerate({
            prompt,
            filePath: job.filePath,
            onUpdate: noop,
        }, cancelToken);
        const subFunctions = getFirstCodeBlock(content);
        if (!subFunctions) {
            throw new Error('未能按拆分方案生成函数');
        }
        return trimMarkdownCodeBlockSyntax(subFunctions);
    }

    private async generatePlans(job: JobState, cancelToken?: vscode.CancellationToken): Promise<string> {
        const prompt = substitute(planPromptTemplate, {
            code: job.componentDefinition,
            componentName: job.componentName,
        });
        const {content} = await this.genericGenerate({
            prompt,
            filePath: job.filePath,
            onUpdate: noop,
        }, cancelToken);
        const jsonText = getFirstCodeBlock(content);
        if (!jsonText) {
            throw new Error('拆分计划生成失败');
        }
        const parsedPlans = safeParseJson<Array<Partial<SplitPlan>>>(jsonText);
        if (!parsedPlans) {
            if (jsonText.trim().length > 0) {
                return jsonText;
            }
            throw new Error('拆分计划生成失败');
        }
        if (Array.isArray(parsedPlans)) {
            const validPlans = parsedPlans
                .filter(item => {
                    return !item.alreadyExists
                        && Boolean(item.name)
                        && Boolean(item.plan)
                        && item.kind
                        && PLAN_PRIORITY.includes(item.kind);
                })
                .map(item => omit(item, ['alreadyExists']));
            if (validPlans.length <= 0) {
                throw new Error('没有找到合适的拆分方案');
            }
            const sortedPlans = sortBy(validPlans, item => PLAN_PRIORITY.indexOf(item.kind!));
            return JSON.stringify(sortedPlans, null, 4);
        }
        return jsonText;
    }

    private async genericGenerate(options: {
        prompt: string;
        filePath: string;
        onUpdate: (content: string, uuid: string) => void;
    }, cancelToken?: vscode.CancellationToken) {
        const {prompt, filePath} = options;
        const axiosTokenSource = axios.CancelToken.source();
        const [username] = await this.userService.getCurrentUser();
        cancelToken?.onCancellationRequested(() => {
            axiosTokenSource.cancel();
        });
        const incomingMessage = await forwardToQianfan({
            username,
            key: isSaasOrPoc ? this.configProvider.getLicense() : undefined,
            pluginName: 'Comate',
            model: 'EB_4_turbo',
            function: 'COMPONENT_SPLIT_REACT',
            lang: extname(filePath),
            messages: [
                {
                    role: 'user',
                    content: prompt,
                },
            ],
            // eslint-disable-next-line camelcase
            max_output_tokens: 2048,
        }, axiosTokenSource.token);
        const processor = new SSEProcessor<QianfanStreamResponse | QianfanStreamErrorResponse>(
            incomingMessage,
            cancelToken
        );
        let content = '';
        for await (const chunk of processor.processSSE()) {
            if ('status' in chunk && chunk.status === 'FORBIDDEN') {
                throw new Error(chunk.message);
            }
            content += (chunk as QianfanStreamResponse).result;
        }
        return {content};
    }

    // eslint-disable-next-line complexity
    private decideCodeLensCommand(
        document: vscode.TextDocument,
        range: vscode.Range,
        node: Parser.SyntaxNode,
        cancelToken?: vscode.CancellationToken
    ): vscode.Command {
        const componentName = getFunctionName(node);
        if (!componentName) {
            return {
                title: '',
                command: '',
            };
        }
        const jobKey = this.createJobKey(document, componentName);
        const job = this.jobStates.get(jobKey);
        if (job?.wordCount && (job.wordCount > MAX_SUPPORTED_WORD_COUNT)) {
            return {
                title: '$(warning) 组件过大，暂不支持，建议手动粗略拆分后重试',
                command: '',
            };
        }
        if (job?.status === 'running') {
            if (job?.stage === 'plan') {
                return {
                    title: '$(loading~spin) [1/3] 思考中（用时略长，拆分后会提醒查看）',
                    command: '',
                };
            }
            else if (job?.stage === 'split') {
                return {
                    title: '$(loading~spin) [2/3] 拆分中（用时略长，拆分后会提醒查看）',
                    command: '',
                };
            }
            else {
                return {
                    title: '$(loading~spin) [3/3] 简化中（用时略长，拆分后会提醒查看）',
                    command: '',
                };
            }
        }
        else if (job?.status === 'failed') {
            return {
                title: '$(error) 拆分失败',
                tooltip: '查看失败原因',
                command: CMD_SHOW_REACT_COMPONENT_SPLIT_FAILURE_REASON,
                arguments: [jobKey],
            };
        }
        else if (job?.status === 'succeed') {
            const originalLineCount = job.componentDefinition.trim().split('\n').length;
            const mergedLineCount = job.merged?.trim().split('\n').length;
            return {
                title: `$(eye) 查看拆分结果（${originalLineCount} 行 -> ${mergedLineCount} 行）`,
                command: CMD_SHOW_REACT_COMPONENT_SPLIT_RESULT,
                arguments: [jobKey],
            };
        }
        else {
            return {
                title: '组件拆分',
                tooltip: `拆分组件 ${componentName}`,
                command: CMD_SPLIT_REACT_COMPONENT,
                arguments: [document, range, node, cancelToken],
            };
        }
    }

    private initJobState(
        document: vscode.TextDocument,
        componentName: string,
        range: vscode.Range,
        node: Parser.SyntaxNode
    ): [string, JobState] {
        const jobKey = this.createJobKey(document, componentName);
        const adjustedRange = new vscode.Range(range.start.line, 0, range.end.line + 1, 0);
        const job: JobState = {
            filePath: document.uri.fsPath,
            componentName,
            componentDefinition: document.getText(adjustedRange),
            status: 'running',
            stage: 'plan',
            documentText: document.getText(),
            componentRange: adjustedRange,
            wordCount: splitIntoWords(node.text).length,
        };
        this.jobStates.set(jobKey, job);
        return [jobKey, job];
    }

    private async generateTrackUuid(document: vscode.TextDocument, jobKey: string) {
        const job = this.jobStates.get(jobKey);
        if (!job) {
            return;
        }
        const params = await buildParams(document, job.componentRange.start, this.userService);
        if (params.type !== 'success') {
            return undefined;
        }
        // 数据上报需要的字段： https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/dDWTpYgSSR/lsyi21DqmPqGp7
        const replacement = this.buildReplacementCode(job);
        const res = await generateTrackUuid({
            ...params.value,
            // NOTE: 这里有点怪，字段名字是 model，但值需要是描述功能的 @wangning42
            model: 'COMPONENT_SPLIT',
            function: 'REACT',
            multiline: true,
            generatedContent: replacement,
            originGeneratedContent: replacement,
            // NOTE: 这时候只是为了生成 uuid，不认为是 shown 给用户了
            shown: false,
        });
        if (res.data.status !== 'OK') {
            return undefined;
        }
        return res.data.data?.uuid;
    }

    private createJobKey(document: vscode.TextDocument, componentName: string) {
        return `${document.uri.fsPath}-${componentName}`;
    }

    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
