/**
 * @file WebviewMessageHandler
 * @description Extension 中与 Webview 的通信类
 */

import * as vscode from 'vscode';
import {EventMessageCallbackTypes} from '@shared/protocols';
import {iocContainer} from '@/iocContainer';
import {TYPES} from '@/inversify.config';
import {ILogger} from './Logger/types';

/**
 * Extension uses this handler to send messages to Webviews. In addition,
 * it receives messages from a webview via its onDidReveiveMessage listener.
 */
type AsyncOrSyncReturnType<T extends (...args: any[]) => any> = (
    ...args: Parameters<T>
) => ReturnType<T> | Promise<ReturnType<T>>;
export class WebviewMessageHandler implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    private readonly handlers: Map<string, MessageHandler> = new Map();
    private lastSentReq: number = 0;
    private readonly pendingReplies: Map<string, PendingReply> = new Map();

    constructor(private readonly webview: vscode.Webview) {
        this.disposables.push(
            this.webview.onDidReceiveMessage(this.handleMessage.bind(this))
        );
    }

    /**
     * 向 webview 发送消息，API 设计参考 fetch
     *
     * message.send('sendData', {
     *     random: Math.random()
     * }).then(res => {
     *     console.log(res);
     * });
     */
    send<K extends keyof EventMessageCallbackTypes>(
        scope: Exclude<K, number>,
        data?: Parameters<EventMessageCallbackTypes[K]>[0]
    ): Promise<ReturnType<EventMessageCallbackTypes[K]>> {
        return new Promise((resolve, reject) => {
            const label = 'extension';
            const req = (++this.lastSentReq).toString();
            this.pendingReplies.set(req, {
                resolve,
                reject,
            });
            this.postMessage({
                label,
                req,
                scope,
                data,
            });
        });
    }

    /**
     * 监听 webview 消息，API 设计参考 express
     *
     * message.listen('receive', (data) => {
     *     vscode.window.showInformationMessage(data.toString());
     *     return {
     *         code: 0
     *     }
     * })
     */
    listen<K extends keyof EventMessageCallbackTypes>(
        scope: Exclude<K, number>,
        callback: AsyncOrSyncReturnType<EventMessageCallbackTypes[K]>
    ): void {
        this.handlers.set(scope, callback);
        if (scope === 'userActionStartLogEvent' || scope === 'userActionStopLogEvent') {
            return;
        }
        const logger = iocContainer.get<ILogger>(TYPES.ILogger);
        logger.stopDebugLog({event: scope});
    }

    private async postMessage(message: MessagePayload) {
        await this.webview.postMessage(message);
    }

    private async handleMessage(message: MessagePayload) {
        const {label, req, scope} = message;

        if (label === 'extension') {
            const reply = this.pendingReplies.get(req);

            if (!reply) {
                return;
            }
            reply.resolve(message.data);
            this.pendingReplies.delete(req);
        }
        else if (label === 'webview') {
            const handler = this.handlers.get(scope);
            if (handler === undefined) {
                return;
            }
            const data = await handler(message.data);
            // Send resolved data back to Webview.
            this.postMessage({
                label,
                req,
                scope,
                data,
            });
        }
        else if (label === 'webviewSendLog') {
            const handler = this.handlers.get('userActionStartLogEvent');
            if (handler === undefined) {
                return;
            }
            handler(message);
        }
        else if (label === 'webviewListenLog') {
            const handler = this.handlers.get('userActionStopLogEvent');
            if (handler === undefined) {
                return;
            }
            handler(message);
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}

type MessageHandler = (...args: any[]) => Promise<any> | any;

interface MessagePayload {
    label: 'extension' | 'webview' | 'webviewSendLog' | 'webviewListenLog';
    req: string;
    scope: string;
    data: any;
}

interface PendingReply {
    resolve: (value?: any) => void;
    reject: (reason?: any) => void;
}
