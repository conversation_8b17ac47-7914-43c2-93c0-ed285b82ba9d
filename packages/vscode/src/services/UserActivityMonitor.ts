import * as vscode from 'vscode';

export class UserActivityMonitor {
    private lastActivityTime: number = Date.now();
    private isActive: boolean = true;
    private readonly INACTIVITY_THRESHOLD = 30 * 60 * 1000; // 30 minutes

    constructor(context: vscode.ExtensionContext) {
        // Monitor window focus
        context.subscriptions.push(
            vscode.window.onDidChangeWindowState(e => {
                this.isActive = e.focused;
                if (e.focused) {
                    this.updateActivity();
                }
            })
        );

        // Monitor editor activity
        context.subscriptions.push(
            vscode.window.onDidChangeActiveTextEditor(() => this.updateActivity()),
            vscode.window.onDidChangeTextEditorSelection(() => this.updateActivity()),
            vscode.workspace.onDidChangeTextDocument(() => this.updateActivity())
        );
    }

    private updateActivity() {
        this.lastActivityTime = Date.now();
        this.isActive = true;
    }

    isUserActive(): boolean {
        const timeSinceLastActivity = Date.now() - this.lastActivityTime;
        return this.isActive && timeSinceLastActivity < this.INACTIVITY_THRESHOLD;
    }
}
