import path from 'path';
import * as vscode from 'vscode';
import {simpleGit} from 'simple-git';
import type {DefaultLogFields, ListLogLine, SimpleGit, DiffResultTextFile} from 'simple-git';
import {execa} from 'execa';
import {inject, injectable} from 'inversify';
import {getLicenseFullDetail, GitChangeData, uploadGitChange} from '@/api';
import {isSaaS} from '../../utils/features';
import {info, error, debug} from '../../common/outputChannel';
import {VSCodeConfigProvider} from '../ConfigProvider';

// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/Hb6OQz5Jc7/sI_upFqRaZopB2
// 后缀与语言映射沿用后端给的表
const languageMap = {
    mustache: 'Mustache',
    gradle: 'Groovy',
    lhs: 'Haskell',
    xsd: 'XSD',
    f90: 'Fortran',
    asax: 'ASP.NET',
    '4th': 'Forth',
    gql: 'GraphQL',
    mll: 'OCaml',
    ksc: 'Kermit',
    scrbl: 'Racket',
    ec: 'C',
    am: 'make',
    bf: 'Brainfuck',
    cmd: 'Batch',
    h: 'C/C++ Header',
    css: 'CSS',
    psql: 'SQL',
    adso: 'ADSO/IDSM',
    gant: 'Groovy',
    es: 'JavaScript',
    sv: 'Verilog',
    i3: 'Modula3',
    json: 'JSON',
    ch: 'xBase',
    rexx: 'Rexx',
    idl: 'IDL',
    sls: 'Scheme',
    vhdl: 'VHDL',
    fmt: 'Oracle Forms',
    pig: 'Pig Latin',
    cbl: 'COBOL',
    hs: 'Haskell',
    asmx: 'ASP.NET',
    twig: 'Twig',
    erl: 'Erlang',
    erb: 'ERB',
    htm: 'HTML',
    cl: 'Lisp/OpenCL',
    mxml: 'MXML',
    n: 'Nemerle',
    vba: 'Visual Basic',
    r: 'R',
    fun: 'Standard ML',
    xhtml: 'HTML',
    cob: 'COBOL',
    jcl: 'JCL',
    ksh: 'Korn Shell',
    vcproj: 'MSBuild',
    il: 'SKILL',
    sld: 'Scheme',
    py: 'Python',
    def: 'Windows Module Definition',
    asp: 'ASP',
    focexec: 'Focus',
    idr: 'Idris',
    xml: 'XML',
    ada: 'Ada',
    shader: 'HLSL',
    ttcn: 'TTCN',
    b: 'Brainfuck',
    less: 'LESS',
    frt: 'Forth',
    pom: 'Maven',
    yml: 'YAML',
    l: 'lex',
    php3: 'PHP',
    scala: 'Scala',
    sbl: 'Softbridge Basic',
    fr: 'Forth',
    f95: 'Fortran',
    vert: 'GLSL',
    tcl: 'Tcl/Tk',
    ccs: 'CCS',
    mps: 'MUMPS',
    ftl: 'Freemarker',
    cmake: 'CMake',
    build: 'NAnt script',
    vhd: 'VHDL',
    tcsh: 'Shell',
    e: 'Specman e',
    purs: 'PureScript',
    ss: 'Scheme',
    xslt: 'XSLT',
    master: 'ASP.NET',
    dmap: 'NASTRAN DMAP',
    p: 'Pascal',
    st: 'Smalltalk',
    ex: 'Elixir',
    xsl: 'XSLT',
    c: 'C',
    pug: 'Pug',
    vbs: 'Visual Basic',
    tpl: 'Smarty',
    cuh: 'CUDA',
    mat: 'Unity-Prefab',
    mg: 'Modula3',
    pl: 'Perl',
    'pom.xml': 'Maven',
    y: 'yacc',
    jsx: 'JSX',
    bash: 'Shell',
    wxl: 'WiX',
    scss: 'Sass',
    vala: 'Vala',
    wxs: 'WiX',
    mc: 'Windows Message File',
    makefile: 'make',
    csproj: 'MSBuild',
    asm: 'Assembly',
    dita: 'DITA',
    gd: 'GDScript',
    glade: 'Glade',
    sql: 'SQL',
    cs: 'C#',
    wl: 'Mathematica',
    aspx: 'ASP.NET',
    cljc: 'ClojureC',
    tk: 'Tcl/Tk',
    frm: 'Visual Basic',
    qml: 'QML',
    drl: 'Drools',
    fb: 'Forth',
    fth: 'Forth',
    lfe: 'LFE',
    ttcnpp: 'TTCN',
    g: 'ANTLR Grammar',
    cshtml: 'Razor',
    cls: 'Visual Basic',
    sed: 'sed',
    f83: 'Forth',
    bst: 'TeX',
    geom: 'GLSL',
    ftn: 'Fortran',
    ts: 'TypeScript',
    lidr: 'Literate Idris',
    dpr: 'Pascal',
    robot: 'RobotFramework',
    lgt: 'Logtalk',
    java: 'Java',
    po: 'PO File',
    yaml: 'YAML',
    vm: 'Velocity',
    g4: 'ANTLR Grammar',
    kts: 'Kotlin',
    sas: 'SAS',
    do: 'Stata',
    liquid: 'liquid',
    as: 'ActionScript',
    jsp: 'JSP',
    vbproj: 'MSBuild',
    ice: 'Slice',
    rkt: 'Racket',
    gnumakefile: 'make',
    f: 'Fortran',
    go: 'Go',
    asa: 'ASP',
    aj: 'AspectJ',
    bat: 'Batch',
    toml: 'TOML',
    prefab: 'Unity-Prefab',
    da: 'DAL',
    wlt: 'Mathematica',
    mli: 'OCaml',
    php5: 'PHP',
    cljs: 'ClojureScript',
    rb: 'Ruby',
    sol: 'Solidity',
    pl1: 'PL',
    pm: 'Perl',
    diff: 'diff',
    ctl: 'Visual Basic',
    trigger: 'Apex Trigger',
    ahk: 'AutoHotkey',
    frag: 'GLSL',
    lua: 'Lua',
    cxx: 'C++',
    psm1: 'PowerShell',
    icl: 'Clean',
    hxx: 'C/C++ Header',
    mly: 'OCaml',
    plh: 'Perl',
    'cmakelists.txt': 'CMake',
    lsp: 'Lisp',
    plx: 'Perl',
    for: 'Fortran',
    rhtml: 'Ruby',
    ttcn2: 'TTCN',
    sitemap: 'ASP.NET',
    srm: 'PowerBuilder',
    vb: 'Visual Basic',
    abap: 'ABAP',
    brs: 'BrightScript',
    vapi: 'Vala Header',
    rake: 'Ruby',
    sch: 'Scheme',
    dtd: 'DTD',
    ascx: 'ASP.NET',
    exs: 'Elixir',
    jspf: 'JSP',
    p6: 'Perl',
    dofile: 'AMPLE',
    csh: 'Shell',
    svh: 'Verilog',
    zsh: 'zsh',
    s: 'Assembly',
    sra: 'PowerBuilder',
    sca: 'Visual Fox Pro',
    gsp: 'Grails',
    tex: 'TeX',
    ino: 'Arduino Sketch',
    f77: 'Fortran',
    ttcn3: 'TTCN',
    xq: 'XQuery',
    clj: 'Clojure',
    srf: 'PowerBuilder',
    psd1: 'PowerShell',
    es6: 'JavaScript',
    m3: 'Modula3',
    fs: 'F#',
    ac: 'm4',
    perl: 'Perl',
    tesc: 'GLSL',
    comp: 'GLSL',
    tese: 'GLSL',
    ample: 'AMPLE',
    glsl: 'GLSL',
    rktl: 'Racket',
    swift: 'Swift',
    webinfo: 'ASP.NET',
    pde: 'Arduino Sketch',
    fsx: 'F#',
    cg: 'HLSL',
    ism: 'InstallShield',
    php: 'PHP',
    dart: 'Dart',
    el: 'Lisp',
    ig: 'Modula3',
    xmi: 'XMI',
    pcl: 'Patran',
    ps1: 'PowerShell',
    tsv: 'RobotFramework',
    sc: 'Scheme',
    srs: 'PowerBuilder',
    elm: 'Elm',
    handlebars: 'Handlebars',
    pyj: 'RapydScript',
    ini: 'INI',
    ils: 'SKILL++',
    hrl: 'Erlang',
    html: 'HTML',
    sty: 'TeX',
    forth: 'Forth',
    cginc: 'HLSL',
    dtx: 'TeX',
    wxi: 'WiX',
    bas: 'Visual Basic',
    page: 'Visualforce',
    ecpp: 'ECPP',
    exp: 'Expect',
    fsi: 'F#',
    pgc: 'C',
    coffee: 'CoffeeScript',
    cpp: 'C++',
    ft: 'Forth',
    jsf: 'JavaServer Faces',
    kt: 'Kotlin',
    inc: 'PHP',
    scm: 'Scheme',
    component: 'Visualforce',
    mk: 'make',
    pyx: 'Cython',
    rc: 'Windows Resource File',
    phtml: 'PHP',
    php4: 'PHP',
    btm: 'Batch',
    cr: 'Crystal',
    pfo: 'Fortran',
    lisp: 'Lisp',
    sass: 'Sass',
    sh: 'Shell',
    adb: 'Ada',
    wixproj: 'MSBuild',
    rs: 'Rust',
    pcc: 'C++',
    hpp: 'C/C++ Header',
    pas: 'Pascal',
    v: 'Verilog',
    feature: 'Cucumber',
    'c++': 'C++',
    graphql: 'GraphQL',
    sml: 'Standard ML',
    awk: 'awk',
    pp: 'Pascal',
    tsx: 'TypeScript',
    xquery: 'XQuery',
    haml: 'Haml',
    'build.xml': 'Ant',
    d: 'D/dtrace',
    nim: 'Nim',
    m: 'Objective C',
    ads: 'Ada',
    cc: 'C++',
    jl: 'Lisp/Julia',
    e4: 'Forth',
    pm6: 'Perl',
    vue: 'Vue.js',
    hx: 'Haxe',
    mjs: 'JavaScript',
    oscript: 'LiveLink OScript',
    rex: 'Oracle Reports',
    itk: 'Tcl/Tk',
    cson: 'CSON',
    rc2: 'Windows Resource File',
    'blade.php': 'Blade',
    md: 'Markdown',
    cu: 'CUDA',
    ml: 'OCaml',
    xaml: 'XAML',
    mm: 'Objective C++',
    styl: 'Stylus',
    js: 'JavaScript',
    cfc: 'ColdFusion',
    hb: 'Harbour',
    asd: 'Lisp',
    logtalk: 'Logtalk',
    m4: 'm4',
    sig: 'Standard ML',
    dsr: 'Visual Basic',
    proto: 'ProtoBuf',
    slim: 'Slim',
    ui: 'Qt',
    wdproj: 'MSBuild',
    startup: 'AMPLE',
    mt: 'Mathematica',
    cfm: 'ColdFusion',
    hlsl: 'HLSL',
    sru: 'PowerBuilder',
    smarty: 'Smarty',
    dcl: 'Clean',
    eex: 'EEx',
    prg: 'xBase',
    vim: 'vim',
    fpm: 'Forth',
    ses: 'Patran',
    srw: 'PowerBuilder',
    groovy: 'Groovy',
    hbs: 'Handlebars',
    mako: 'Mako',
    pad: 'Ada',
    hh: 'C/C++ Header',
    rx: 'Forth',
};

const languageBlackList = [
    'YAML',
    'JSON',
    'XML',
    'Markdown',
    'TOML',
    'Unity-Prefab',
    'ProtoBuf',
    'Groovy',
    'Smarty',
    'INI',
    'Cucumber',
    'Velocity',
    'RobotFramework',
    'Stylus',
    'SQL',
    'LESS',
    'Sass',
    'CSS',
];

const commitMessageBlackList = [
    '%merge%',
    'revert%',
    'auto resolve conflict%',
    'init commit',
];

const fileNameBlackList = [
    '%.min.js',
    '%.bundle.js',
    '%.map.js',
];

function filterValidCommit(str: string, pattern: string) {
    const regexPattern = pattern.replace(/%/g, '.*');
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(str);
}

async function isInGit(cwd: string) {
    try {
        const {stdout} = await execa('git', ['rev-parse', '--is-inside-work-tree'], {cwd});
        return stdout.trim() === 'true';
    }
    catch {
        return false;
    }
}

interface Cache {
    [key: string]: {
        hash: string;
        insertions: number;
        deletions: number;
        date: number;
    };
}

@injectable()
export default class CodeRatioCalculatorByGit {
    constructor(@inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider) {}

    private timer?: NodeJS.Timeout;
    private cache: Cache = {};

    async start() {
        // 没有poc环境了，应该这么写可以吧
        if (!(isSaaS && await this.isEnabled())) {
            return;
        }
        this.gitInfoCollector();
        this.timer = setInterval(() => {
            this.gitInfoCollector();
        }, 1000 * 60);
    }

    stop() {
        if (this.timer) {
            clearInterval(this.timer);
        }
    }
    async gitInfoCollector() {
        const workspaceFolders = vscode.workspace.workspaceFolders || [];
        debug(`codeRatioCalculatorByGit gitInfoCollector: ${JSON.stringify(workspaceFolders)}`);
        const messages = [];
        if (workspaceFolders.length > 0) {
            const uris = workspaceFolders.map(v => v.uri.fsPath);
            if (!(await isInGit(uris[0]))) {
                return;
            }
            const gitInstances = await this.getGitInstances(uris);
            for (const gitInstance of gitInstances) {
                const logs = await this.getLogs(gitInstance);
                // 缓存里的过滤
                const commits = this.getValidCommits(logs).filter(v => !this.cache[v.hash]);
                for (const commit of commits) {
                    const change = await this.getCommitChangeCount(gitInstance, commit);
                    const data = {
                        hash: commit.hash,
                        insertions: change.insertions,
                        deletions: change.deletions,
                        date: (new Date(commit.date).getTime()) / 1000,
                    };
                    if (data.insertions !== 0 || data.deletions !== 0) {
                        messages.push(data);
                    }
                    this.cache[commit.hash] = data;
                }
            }
        }
        this.uploadData(messages.map(v => ({
            commitId: v.hash,
            key: this.configProvider.getLicense(),
            addLines: v.insertions,
            deleteLines: v.deletions,
            commitTime: v.date,
        })));
    }

    async uploadData(data: GitChangeData[]) {
        info(`codeRatioCalculatorByGit uploadData: ${JSON.stringify(data)}`);
        if (data.length > 0) {
            const res = await uploadGitChange(data);
            debug(`codeRatioCalculatorByGit updatedata response: ${JSON.stringify(res.data)}`);
        }
    }

    async isEnabled() {
        const res = await getLicenseFullDetail(this.configProvider.getLicense());
        debug(`codeRatioCalculatorByGit isEnabled: ${JSON.stringify(res)}`);
        return ['ENTERPRISE', 'TRIAL_ENTERPRISE', 'CUSTOMIZED_ENTERPRISE'].includes(res.typeCode);
    }

    async getGitInstances(workspaceFolders: string[]) {
        const gits = [];
        for (const workspaceFolder of workspaceFolders) {
            try {
                const git = simpleGit(workspaceFolder);
                gits.push(git);
            }
            catch (e: unknown) {
                if (e instanceof Error) {
                    error(`CodeRatioCalculator init error: ${e.message}`);
                }
            }
        }
        return gits;
    }
    async getLogs(git: SimpleGit) {
        const user = await git.getConfig('user.name');
        if (!user || !user.value) {
            debug(`codeRatioCalculatorByGit getLogs: ${JSON.stringify(user)}`);
            return [];
        }
        return (await git.log(['--author', user.value, '-20'])).all;
    }

    getValidCommits(commits: ReadonlyArray<DefaultLogFields & ListLogLine>) {
        debug(`codeRatioCalculatorByGit getValidCommits: ${JSON.stringify(commits)}`);
        return commits.filter(v => {
            return !commitMessageBlackList.some(pattern => filterValidCommit(v.message.toLocaleLowerCase(), pattern));
        });
    }

    async getCommitChangeCount(git: SimpleGit, commit: DefaultLogFields & ListLogLine) {
        try {
            const status = await git.diffSummary([
                '--shortstat',
                await git.revparse([`${commit.hash}^`]),
                commit.hash,
            ]);
            const filesToMerge: DiffResultTextFile[] = status
                .files
                .filter((v): v is DiffResultTextFile => !v.binary)
                .filter(v => {
                    const extName = path.extname(v.file).slice(1);
                    // @ts-ignore
                    const languageId = languageMap[extName];
                    return languageId && !languageBlackList.includes(languageId);
                })
                .filter(v => !fileNameBlackList.some(pattern => filterValidCommit(v.file, pattern)))
                .filter(v => {
                    return !((v.file.endsWith('.js') || v.file.endsWith('.html') || v.file.endsWith('.css'))
                        && (v.file.includes('output') || v.file.includes('dist')));
                });
            const [insertions, deletions] = filesToMerge.reduce((acc, cur) => {
                return [acc[0] + cur?.insertions, acc[1] + cur?.deletions];
            }, [0, 0]);
            debug(`codeRatioCalculatorByGit getCommitChangeCount: ${
                JSON.stringify({
                    insertions,
                    deletions,
                })
            }`);
            return {
                ...status,
                insertions,
                deletions,
            };
        }
        catch (e) {
            error(`codeRatioCalculatorByGit getCommitChangeCount: ${JSON.stringify(e)}`);
            return {
                insertions: 0,
                deletions: 0,
                hash: commit.hash,
                date: commit.date,
            };
        }
    }
}
