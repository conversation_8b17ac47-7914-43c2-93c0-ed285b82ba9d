import * as path from 'path';
import * as vscode from 'vscode';

export class TextRangeNotExistError extends Error {}
const replaceFailText = '抱歉，未找到替换的代码';

export async function checkTextRangeExist(fileUri: string, textRange: string) {
    if (!vscode.workspace.workspaceFolders) {
        throw new TextRangeNotExistError('请先打开一个项目');
    }

    const workspaceFolder = vscode.workspace.workspaceFolders[0].uri.fsPath;
    const absolutePath = path.join(workspaceFolder, fileUri);

    return new Promise<vscode.TextDocument>(
        (resolve, reject) => {
            vscode.workspace.openTextDocument(vscode.Uri.file(absolutePath)).then(
                (document: vscode.TextDocument) => {
                    const fullText = document.getText();
                    const originalCodeIndex = fullText.indexOf(textRange);
                    if (originalCodeIndex === -1) {
                        reject(new TextRangeNotExistError(replaceFailText));
                    }
                    resolve(document);
                },
                reject
            );
        }
    );
}

export async function replaceTextRange(fileUri: string, from: string, to: string) {
    const document = await checkTextRangeExist(fileUri, from);

    const fullText = document.getText();
    const originalCodeIndex = fullText.indexOf(from);
    const start = document.positionAt(originalCodeIndex);
    const end = document.positionAt(originalCodeIndex + from.length);
    const range = new vscode.Range(start, end);

    const editor = await vscode.window.showTextDocument(document, {
        viewColumn: vscode.ViewColumn.One,
        preview: false,
        selection: range,
    });

    const success = await editor.edit(editBuilder => {
        editBuilder.replace(range, to);
    });

    if (!success) {
        throw new TextRangeNotExistError(replaceFailText);
    }
}
