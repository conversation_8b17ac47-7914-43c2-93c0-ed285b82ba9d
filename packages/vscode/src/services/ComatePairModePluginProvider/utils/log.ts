import {acceptCode, generateTrackUuid} from '@/api';
import {getExtensionContextAsync} from '@/utils/extensionContext';

export const getLines = (text: string) => text.split('\n').length;

interface LogInfo {
    /** 仓库名 */
    repo: string;
    /** 触发扫描的能力 */
    capabilityName: string;
    /** 相对路径 */
    relativePath: string;
    /** 生成内容 */
    generatedContent: string;
    /** 原始内容 */
    content: string;
    /** 用于上报代码采纳的id，通过上报生成后 */
    traceId?: string;
}

type RangeId = string;
const rangeIdInfo = new Map<RangeId, LogInfo>();

export const logAcceptLine = (rangeId: string, generatedContent: string) => {
    const logInfo = rangeIdInfo.get(rangeId);
    if (logInfo?.traceId) {
        acceptCode({
            accepted: true,
            content: '',
            uuid: logInfo.traceId,
            // 如果和生成的内容一样，后端统一时直接取之前的diff行, 如果不是则取当前采纳行
            generatedContent: generatedContent === logInfo.content ? '' : generatedContent,
        });
    }
};

export const logGenereateLine = async (username: string, rangeId: string, logInfo: LogInfo) => {
    if (!rangeIdInfo.get(rangeId)) {
        rangeIdInfo.set(rangeId, logInfo);
        const context = await getExtensionContextAsync();
        const {data} = await generateTrackUuid(
            {
                username,
                content: logInfo.content,
                generatedContent: logInfo.generatedContent,
                model: 'comate-pair',
                function: logInfo.capabilityName,
                path: logInfo.relativePath,
                col: '1',
                row: '1',
                ide: $features.ENTERPRISE_VERSION === 'gitee' ? 'vscode-gitee' : 'vscode',
                repo: logInfo.repo,
                shown: true,
                multiline: true,
                pluginVersion: context.extension.packageJSON.version,
            }
        );
        rangeIdInfo.set(rangeId, {...rangeIdInfo.get(rangeId)!, traceId: data.data?.uuid});
    }
};
