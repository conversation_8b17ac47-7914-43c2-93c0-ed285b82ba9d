import * as vscode from 'vscode';
import {injectable} from 'inversify';

function createTimeoutEventClock(callback: () => void) {
    // eslint-disable-next-line @typescript-eslint/init-declarations
    let timeout: NodeJS.Timeout;
    return () => {
        clearTimeout(timeout);
        timeout = setTimeout(
            () => {
                callback();
                clearTimeout(timeout);
            },
            3000
        );
    };
}

@injectable()
export class ComatePairStatusBar implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private adviceNum: number = 0;
    private statusBarItem: vscode.StatusBarItem | null = null;

    get comatePairStatusBarItem() {
        return this.statusBarItem;
    }

    private isLoading: boolean = false;
    private readonly refreshStatusBarLoadingTimeoutClock = createTimeoutEventClock(
        () => {
            if (this.statusBarItem) {
                this.isLoading = false;
                this.foundAdvice();
            }
        }
    );

    loading() {
        if (this.isLoading) {
            this.refreshStatusBarLoadingTimeoutClock();
            return;
        }

        if (this.statusBarItem) {
            this.isLoading = true;
            this.statusBarItem.text = '$(loading~spin) 正在生成代码建议';
            this.refreshStatusBarLoadingTimeoutClock();
        }
    }

    createComatePairStatusBarItem() {
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
        this.statusBarItem.text = '$(comate-logo) 0';
        this.statusBarItem.tooltip = '未发现任何优化建议';
        this.statusBarItem.show();
        this.statusBarItem.command = {
            title: 'baidu.comate.showChatPanel',
            command: 'baidu.comate.showChatPanel',
            arguments: [
                {
                    activeTabKey: 'PAIR',
                    source: 'PAIR-statusBar',
                },
            ],
        };
        this.disposables.push(this.statusBarItem);
    }

    foundAdvice(num?: number) {
        if (this.statusBarItem) {
            if (num) {
                this.adviceNum = num;
            }
            this.statusBarItem.text = `$(comate-logo) ${this.adviceNum}`;
            this.statusBarItem.tooltip = `发现${this.adviceNum}个优化建议，点击查看`;
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
