import * as vscode from 'vscode';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {EventMessage} from '@shared/protocols';
import {ChatViewProvider} from '../ChatViewProvider';
import {TextRangeNotExistError, checkTextRangeExist, replaceTextRange} from './utils/replaceCode';
import {logAcceptLine} from './utils/log';

@injectable()
export class ReplaceTextProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider
    ) {
        this.activate();
    }

    async handleTextRange(action: EventMessage, fileUri: string, from: string, to?: string) {
        try {
            if (action === EventMessage.ComatePairFileContentReplace) {
                await replaceTextRange(fileUri, from, to!);
            }
            else if (action === EventMessage.ComatePairFileContentCheckExist) {
                await checkTextRangeExist(fileUri, from);
            }

            this.chatViewProvider.sendDataToWebview(
                action,
                {result: {success: true}, args: {fileUri, from, to}}
            );
        }
        catch (ex) {
            if (ex instanceof TextRangeNotExistError) {
                this.chatViewProvider.sendDataToWebview(
                    action,
                    {result: {success: false, message: ex.message}, args: {fileUri, from, to}}
                );
            }
            throw ex;
        }
    }

    activate() {
        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.ComatePairFileContentReplace,
            listener: async (args: {fileUri: string, from: string, to: string, logId?: string}) => {
                await this.handleTextRange(EventMessage.ComatePairFileContentReplace, args.fileUri, args.from, args.to);

                if (args.logId) {
                    logAcceptLine(args.logId, args.to);
                }
            },
        });

        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.ComatePairFileContentCheckExist,
            listener: async (args: {fileUri: string, from: string, logId: string}) => {
                this.handleTextRange(EventMessage.ComatePairFileContentCheckExist, args.fileUri, args.from);
            },
        });

        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.ComatePairFileContentCopy,
            listener: async (args: {content: string, logId: string}) => {
                await vscode.env.clipboard.writeText(args.content);
                if (args.logId) {
                    logAcceptLine(args.logId, args.content);
                }
            },
        });
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
