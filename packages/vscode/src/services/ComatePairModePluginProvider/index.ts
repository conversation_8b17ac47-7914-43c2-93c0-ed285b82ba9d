/* eslint-disable complexity */
/* eslint-disable max-depth */
import {basename, extname} from 'node:path';
import {randomUUID} from 'node:crypto';
import {sum} from 'lodash';
import {LazyServiceIdentifer, inject, injectable} from 'inversify';
import {
    ACTION_SCAN_QUERY,
    ACTION_SCAN_TASK_PROGRESS,
    ACTION_SESSION_FINISH,
    DrawElement,
    ScanQueryPayload,
    TaskProgressPayload,
    Execution,
} from '@comate/plugin-shared-internals';
import * as vscode from 'vscode';
import {EventMessage, Feature} from '@shared/protocols';
import {findCodeBlocks, isCodeBlock, isReplaceMethod} from '@shared/utils/scan';
import {getFirstWorkspaceRepoName} from '@/utils/workspace';
import {KernelProvider} from '../KernelProvider';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {activeFileContext} from '../../utils/document';
import {FeatureFlags} from '../FeatureFlags';
import {LogCategory, LogUploaderProvider} from '../LogUploaderProvider';
import {ComatePairStatusBar} from './ComatePairStatusBar';
import {checkTextRangeExist, replaceTextRange} from './utils/replaceCode';
import {logGenereateLine} from './utils/log';

const PairScanProgressInformationViewAction = '查看扫描结果';
const PairScanProgressInformationAcceptAction = '立即采纳';

function foundCodeBlockInScanProgressPayload(task: TaskProgressPayload) {
    const codeBlock = findCodeBlocks(task.chunk);
    return codeBlock;
}

interface MessageOf<A, P> {
    sessionId: string;
    execution: Execution;
    data: {action: A, payload: P};
}

type RelativeFilePath = string;
type UUID = string;

export interface ExtendedTaskProgressPayload extends TaskProgressPayload {
    loading: boolean;
    fail?: boolean;
    fileUri: string;
    fileName: string;
    content: DrawElement | DrawElement[];
}

@injectable()
export class ComatePairModePluginProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private repoName: string = '';
    private activeTabKey: 'CHAT' | 'PAIR' = 'CHAT';
    private readonly scanFileCreatedTimeMap: Map<RelativeFilePath, number> = new Map();
    /**
     * 每个扫描任务的根据扫描文件构建的 Map
     */
    // eslint-disable-next-line max-len
    private scanTaskProgressMap: Array<
        {fileUri: RelativeFilePath, fileName: string, messages: ExtendedTaskProgressPayload[]}
    > = [];
    /**
     * 每个扫描文件根据UUID构建的 Map
     */
    private readonly scanFilePathUUIDMap: Map<UUID, RelativeFilePath> = new Map();
    private readonly discardTaskIds: Set<UUID> = new Set();

    constructor(
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(FeatureFlags) private readonly featureFlags: FeatureFlags,
        @inject(ComatePairStatusBar) private readonly statusBar: ComatePairStatusBar,
        @inject(LogUploaderProvider) private readonly logProvider: LogUploaderProvider
    ) {
    }

    start() {
        this.activate();
    }

    get isPairTabActive() {
        return this.activeTabKey === 'PAIR';
    }

    private activate(): void {
        this.featureFlags.hasAccessTo(Feature.ComatePair).then(enable => {
            if (enable) {
                this.statusBar.createComatePairStatusBarItem();
            }
        });

        const saveEvent = vscode.workspace.onDidSaveTextDocument(this.onDidSaveDocument.bind(this));
        this.disposables.push(saveEvent);

        const activeFileChangeEvent = vscode.window.onDidChangeActiveTextEditor(
            this.onActiveTextEditorChange.bind(this)
        );
        this.disposables.push(activeFileChangeEvent);

        const createFiledEvent = vscode.workspace.onDidCreateFiles(
            this.onCreateFile.bind(this)
        );

        this.disposables.push(createFiledEvent);

        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.ComatePairScanResultMessageSyncRequest,
            listener: async () => {
                this.logUserAction('syncMessage');

                const result: typeof this.scanTaskProgressMap = [];
                for await (const fileTask of this.scanTaskProgressMap) {
                    const messages = [];
                    for (const message of fileTask.messages) {
                        const codeBlocks = foundCodeBlockInScanProgressPayload(message);
                        if (message.fail && !codeBlocks.length) {
                            continue;
                        }
                        try {
                            // 另外，如果一次扫描有多个函数，则message里会有多个block，大概的结构如下
                            // ['text', {codeBlock}, 'text', {codeBlock}, ...]
                            // 现在能够判定中间应该会有一个人工插入的----分隔符，目前没找到更好的办法，先全部废弃
                            for (const codeBlock of codeBlocks) {
                                const replaceMethod = codeBlock.acceptMethods?.find(isReplaceMethod);
                                if (replaceMethod) {
                                    await checkTextRangeExist(fileTask.fileUri, replaceMethod.from);
                                    continue;
                                }
                            }
                            this.logGenerateCode(message);
                            messages.push(message);
                        }
                        catch (ex) {
                            continue;
                        }
                    }
                    if (messages.length) {
                        result.push({fileUri: fileTask.fileUri, fileName: fileTask.fileName, messages});
                    }
                }

                const isEmpty = this.scanTaskProgressMap.every(task => !task.messages.length);
                if (isEmpty) {
                    this.setUnreadContextDot(false);
                }
                this.scanTaskProgressMap = result;
                this.chatViewProvider.sendDataToWebview(
                    EventMessage.ComatePairScanResultMessageSyncRequest,
                    {
                        activeFileUri: this.activeFileUri,
                        messages: result,
                    }
                );
            },
        });

        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.ComatePairClearHistory,
            listener: () => {
                this.scanTaskProgressMap = [];
                this.scanFilePathUUIDMap.clear();
                this.setUnreadContextDot(false);
                this.chatViewProvider.sendDataToWebview(
                    EventMessage.ComatePairScanResultMessageSyncRequest,
                    {
                        activeFilePath: this.activeFileUri,
                        messages: [],
                    }
                );
            },
        });

        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.ComatePairScanProgressCancel,
            listener: ({taskId}: {taskId: string}) => {
                this.discardTaskIds.add(taskId);
                const message = this.getMessageByTaskId(taskId);
                if (message) {
                    message.fail = true;
                }
            },
        });
    }

    private getMessageByTaskId(taskId: string) {
        const filePathOfCurrentTask = this.scanFilePathUUIDMap.get(taskId)!;
        const fileTask = this.scanTaskProgressMap.find(({fileUri}) => fileUri === filePathOfCurrentTask);
        if (fileTask) {
            const message = fileTask.messages.find(message => message.taskId === taskId);
            return message;
        }
        return null;
    }

    private hasUnreadMessage: boolean = false;
    private setUnreadContextDot(hasUnreadMessage: boolean) {
        this.hasUnreadMessage = hasUnreadMessage;
        vscode.commands.executeCommand(
            'setContext',
            'baidu.comate.context.comatePairMessagesCount',
            hasUnreadMessage ? 1 : 0
        );
    }

    private isRegisterEndSessionEvent = false;
    registerEndSessionEvent() {
        if (!this.isRegisterEndSessionEvent) {
            this.isRegisterEndSessionEvent = true;
            // todo: 确认下 engine.client.onRequest 是否是一对多的
            // 现在如果把session_finish事件挪到这里来的话，会导致触发时间早于扫描时间
        }
    }

    private generateScanId(filePath: string) {
        const id = randomUUID();
        this.scanFilePathUUIDMap.set(id, filePath);

        return id;
    }

    private async showInformationMessageWithAction(filename: string, opts?: Record<string, () => any>) {
        if (this.chatViewProvider.webViewVisible) {
            return;
        }
        const secondaryButtonText = typeof opts === 'object' ? Object.keys(opts)[0] : undefined;
        const selected = await vscode.window.showInformationMessage(
            `Comate 正在对当前文件开始优化，点击查看 ${filename} 文件的优化内容并采纳`,
            secondaryButtonText as string,
            PairScanProgressInformationViewAction
        );

        if (selected === PairScanProgressInformationViewAction) {
            this.logUserAction('clickMessage');
            vscode.commands.executeCommand('baidu.comate.showChatPanel', {activeTabKey: 'PAIR', source: 'PAIR'});
        }
        else if (selected === secondaryButtonText) {
            opts![secondaryButtonText!]?.();
        }
    }

    private previosInformationToastTime: number = 0;
    private get canInformationMessageToast() {
        return Date.now() - this.previosInformationToastTime > 30 * 60 * 1000;
    }

    private async showInformationMessage(filename: string) {
        if (this.canInformationMessageToast) {
            this.previosInformationToastTime = Date.now();
            this.showInformationMessageWithAction(filename);
        }
    }

    private get activeFileUri() {
        const document = vscode.window.activeTextEditor?.document;
        if (document?.uri) {
            return vscode.workspace.asRelativePath(document.uri);
        }
        return null;
    }

    private failScanTaskTimeout: NodeJS.Timeout = setTimeout(() => ({}));
    private scan(document: vscode.TextDocument, fileInfo: ReturnType<typeof activeFileContext>) {
        this.registerEndSessionEvent();

        const userName = this.configProvider.getConfig<string>(ConfigKey.Username, '');
        const scanPayload: ScanQueryPayload = {
            scanId: this.generateScanId(fileInfo.activeFilePath),
            context: {
                query: '',
                ...fileInfo,
            },
            systemInfo: {
                userId: userName,
                userDetail: {
                    name: userName,
                    displayName: userName,
                    email: userName + '@baidu.com',
                },
                cwd: vscode.workspace.workspaceFolders?.[0].uri.fsPath ?? '',
            },
        };
        // Sava All 的场景，只处理当前激活的文件
        if (vscode.workspace.asRelativePath(document?.uri) !== fileInfo.activeFilePath) {
            return;
        }

        this.kernelProvider.client?.sendRequest(ACTION_SCAN_QUERY, scanPayload);
        this.kernelProvider.client?.onRequest(
            ACTION_SCAN_TASK_PROGRESS,
            (res: MessageOf<typeof ACTION_SCAN_TASK_PROGRESS, TaskProgressPayload>) => {
                if (!this.repoName) {
                    getFirstWorkspaceRepoName().then(repo => {
                        this.repoName = repo;
                    });
                }
                clearTimeout(this.failScanTaskTimeout);
                // 被用户手动废弃的消息就不要了
                if (this.discardTaskIds.has(res.data.payload.taskId)) {
                    return;
                }

                this.failScanTaskTimeout = setTimeout(
                    () => {
                        const message = this.getMessageByTaskId(res.data.payload.taskId);
                        if (message) {
                            message.loading = false;
                            message.fail = true;

                            if (this.chatViewProvider.webViewVisible) {
                                this.chatViewProvider.sendDataToWebview(
                                    EventMessage.ComatePairScanResultMessageStream,
                                    {message, activeFileUri: this.activeFileUri}
                                );
                            }
                        }
                        this.handleScanFinish(res.data.payload.taskId);
                    },
                    5000
                );

                // 设置扫描时间，总是覆盖
                // TODO: 目前是针对工作区设置了1分钟的时间，如果针对单文件的话，会触发多个流同时输出。需要处理完这个场景再放开
                this.previousScanTaskFinishTime = Date.now();
                // 通过uuid找出这个扫描任务的原始文件
                const filePathOfCurrentTask = this.scanFilePathUUIDMap.get(res.data.payload.taskId)!;
                // 如果找不到，则代表被用户主动清理历史了
                if (!filePathOfCurrentTask) {
                    return;
                }

                // 设置每个文件第一次触发扫描的时间，用于在webview做排序
                if (!this.scanFileCreatedTimeMap.get(filePathOfCurrentTask)) {
                    this.scanFileCreatedTimeMap.set(filePathOfCurrentTask, Date.now());
                }

                const isCurrentFile = ({fileUri}: {fileUri: string}) => fileUri === filePathOfCurrentTask;
                // 判断有没有之前存在的任务，这里需要处理侧边栏关闭、打开把stream续上的场景
                const previous = this.scanTaskProgressMap.find(isCurrentFile);
                const {chunk} = res.data.payload;
                const extendedPayload: ExtendedTaskProgressPayload = {
                    ...res.data.payload,
                    content: typeof chunk === 'object' && 'content' in chunk ? chunk.content : chunk,
                    loading: true,
                    fileName: basename(filePathOfCurrentTask),
                    fileUri: filePathOfCurrentTask,
                };

                if (previous) {
                    this.scanTaskProgressMap = this.scanTaskProgressMap.map(task => {
                        if (isCurrentFile(task)) {
                            const i = task.messages.findIndex(({taskId}) => taskId === res.data.payload.taskId);
                            const index = i === -1 ? 0 : i;
                            task.messages[index] = extendedPayload;
                        }
                        return task;
                    });
                }
                else {
                    this.scanTaskProgressMap.push({
                        fileUri: filePathOfCurrentTask,
                        fileName: basename(filePathOfCurrentTask),
                        messages: [extendedPayload],
                    });
                }

                // 理论上设置不为零就行
                this.setUnreadContextDot(!this.isPairTabActive);

                if (extname(filePathOfCurrentTask) === '.svg') {
                    const codeBlock = findCodeBlocks(res.data.payload.chunk);
                    if (codeBlock.length) {
                        this.showInformationMessageWithAction(
                            basename(filePathOfCurrentTask),
                            {
                                [PairScanProgressInformationAcceptAction]: () => {
                                    const {from, to} = codeBlock[0].acceptMethods!.find(isReplaceMethod)!;
                                    replaceTextRange(filePathOfCurrentTask, from, to);
                                },
                            }
                        );
                    }
                }
                else {
                    // 如果用户关闭了Comate侧边栏，给一个提示
                    this.showInformationMessage(basename(filePathOfCurrentTask));
                }

                if (this.isPairTabActive) {
                    this.logGenerateCode(extendedPayload);
                }
                this.statusBar.loading();
                if (this.chatViewProvider.webViewVisible) {
                    this.chatViewProvider.sendDataToWebview(
                        EventMessage.ComatePairScanResultMessageStream,
                        {message: extendedPayload, activeFileUri: this.activeFileUri}
                    );
                }
            }
        );

        this.kernelProvider.client?.onRequest(ACTION_SESSION_FINISH, (res: MessageOf<void, void>) => {
            if (this.discardTaskIds.has(res.sessionId)) {
                return;
            }

            const message = this.getMessageByTaskId(res.sessionId);
            if (message) {
                message.loading = false;
                if (this.chatViewProvider.webViewVisible) {
                    this.chatViewProvider.sendDataToWebview(
                        EventMessage.ComatePairScanResultMessageStream,
                        {message, activeFileUri: this.activeFileUri}
                    );
                }
            }

            this.handleScanFinish(res.sessionId);
        });
    }

    async handleScanFinish(taskId: string) {
        this.statusBar.foundAdvice(this.scanTaskProgressMap.reduce<number>(
            (acc, fileTask) => {
                const total = fileTask.messages.map(message => {
                    const {content} = message;
                    if (Array.isArray(content)) {
                        const code = content.filter(isCodeBlock);
                        return code.length;
                    }
                    else {
                        return 0;
                    }
                });
                return acc + sum(total);
            },
            0
        ));

        const message = this.getMessageByTaskId(taskId);
        if (message) {
            message.loading = false;
        }
    }

    private previousScanTaskFinishTime: number = 0;
    private async onDidSaveDocument(document: vscode.TextDocument) {
        const enabled = await this.featureFlags.hasAccessTo(Feature.ComatePair);
        if (!enabled) {
            return;
        }

        // 举例: 上一次任务小于一分钟，不执行
        const lessThan1Min = Date.now() - this.previousScanTaskFinishTime < 60 * 1000;
        if (lessThan1Min) {
            return;
        }

        // 如果没办法推送提示消息，且侧边栏也没打，就不扫描了
        if (!this.chatViewProvider.webViewVisible && !this.canInformationMessageToast) {
            return;
        }

        // 如果已经有未读的红点标识了，就不扫描了
        if (this.chatViewProvider.webViewVisible && this.activeTabKey === 'CHAT' && this.hasUnreadMessage) {
            return;
        }

        this.scan(document, activeFileContext());
    }

    private async onCreateFile(event: vscode.FileCreateEvent) {
        const enabled = await this.featureFlags.hasAccessTo(Feature.ComatePair);
        if (!enabled) {
            return;
        }

        // 只处理第一个文件
        const file = event.files[0];
        const document = await vscode.workspace.openTextDocument(file);

        this.scan(document, activeFileContext(document));
    }

    private async onActiveTextEditorChange(textEditor?: vscode.TextEditor) {
        if (textEditor) {
            const activeFileUri = vscode.workspace.asRelativePath(textEditor.document.uri);
            this.chatViewProvider.sendDataToWebview(
                EventMessage.ComatePairActiveDocumentChange,
                {activeFileUri}
            );
        }
    }

    private logGenerateCode(message: ExtendedTaskProgressPayload) {
        const codeBlocks = foundCodeBlockInScanProgressPayload(message);
        // 另外，如果一次扫描有多个函数，则message里会有多个block，大概的结构如下
        // ['text', {codeBlock}, 'text', {codeBlock}, ...]
        // 现在能够判定中间应该会有一个人工插入的----分隔符，目前没找到更好的办法，先全部废弃
        for (const codeBlock of codeBlocks) {
            const replaceMethod = codeBlock.acceptMethods?.find(isReplaceMethod);
            if (replaceMethod) {
                const capabilityName = message.capabilityName;
                logGenereateLine(
                    this.configProvider.getConfig(ConfigKey.Username, ''),
                    codeBlock.id,
                    {
                        repo: this.repoName,
                        relativePath: message.fileUri,
                        capabilityName: capabilityName || 'UNKNOWN',
                        generatedContent: replaceMethod.to,
                        content: replaceMethod.from,
                    }
                );
            }
        }
    }

    private async logUserAction(action: string, label = '') {
        this.logProvider.internalLogUserAction(
            'event',
            {
                category: LogCategory.comatePair,
                action,
                label,
            }
        );
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
