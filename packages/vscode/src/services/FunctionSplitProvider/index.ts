import * as vscode from 'vscode';
import axios from 'axios';
import {injectable, inject} from 'inversify';
import Parser from 'web-tree-sitter';
import {TYPES} from '@/inversify.config';
import {CMD_SPLIT_FUNCTION} from '@/constants';
import {L10n, getGenerationFailureText} from '@/common/L10nProvider/L10n';
import {SplitProviderText} from '@/common/L10nProvider/constants';
import {getCompleteFirstLine} from '../../utils/document';
import {stripExtraIndent} from '../../utils/indent';
import {GenerateCodeOptions, generateCode} from '../../api';
import {addMarkdownCodeBlock, findLeadingNonWhitespaceIndex, stripMarkdownCodeBlock} from '../../utils/common';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {iocContainer} from '../../iocContainer';
import {UserService} from '../UserService';
import {buildParams, fetchAndStreamCode} from '../../common/Fetcher';
import {ChatViewProvider} from '../ChatViewProvider';
import {CodeLensDisplayMode, CodelensConfig, ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {DiffProvider} from '../DiffProvider';
import {Conversation} from '../ChatViewProvider/Conversation';
import {ITimeTracker} from '../TimeTracker/types';
import {ILicenseController} from '../LicenseController/types';
import {CodeLensProvider} from '../FoldedCodeLensProvider';
import {ChatBaseProvider} from '../ChatBaseProvider';
import {TextResponse} from '../ChatViewProvider/TextResponse';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';

const MAX_FUNCTION_LENGTH = 9000;

export function filterSplitFunctionNode(node: Parser.SyntaxNode) {
    if (node.hasError()) {
        return false;
    }
    const lineCount = node.endPosition.row - node.startPosition.row;
    return lineCount >= 19 && node.text.length <= MAX_FUNCTION_LENGTH;
}

function buildReplacement(document: vscode.TextDocument, content: string, padding: string) {
    const lines = stripMarkdownCodeBlock(content, document.languageId).split('\n');
    const paddingLines = lines.map((line, index) => (line.trim() && index !== 0
        ? `${padding}${line}`
        : line)
    );
    return paddingLines.join('\n').trimEnd();
}

@injectable()
export class FunctionSplitProvider extends ChatBaseProvider implements CodeLensProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'javascript',
        'typescript',
        'cpp',
        'c',
        // 暂时不支持 vue，效果不好
    ];
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(TYPES.ITimeTracker) private readonly timeTracker: ITimeTracker,
        @inject(TYPES.ILicenseController) private readonly licenseController: ILicenseController,
        @inject(PerformanceLogProvider) private readonly performanceLog: PerformanceLogProvider,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider
    ) {
        super(diffProvider);
        this.disposables.push(
            vscode.languages.registerCodeLensProvider(
                FunctionSplitProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this
            ),
            vscode.commands.registerCommand(
                CMD_SPLIT_FUNCTION,
                (document: vscode.TextDocument, range: vscode.Range) => {
                    this.handleSplitFunctionCommand(document, range, performance.now());
                }
            )
        );
    }

    async handleSplitFunctionCommand(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        startTime: number = performance.now()
    ) {
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_SPLIT_FUNCTION});
        const firstLine = getCompleteFirstLine(document, functionRange);
        const functionContent = stripExtraIndent(document.getText(functionRange), true, firstLine);
        const conversation = this.chatViewProvider.createConversation(
            L10n.t(SplitProviderText.PROMPT),
            'splitFunction',
            addMarkdownCodeBlock(functionContent, document.languageId)
        );
        const definitionLine = document.lineAt(functionRange.start).text;
        const padding = definitionLine.slice(0, findLeadingNonWhitespaceIndex(definitionLine));
        const res = await this.resolve(document, functionRange, conversation, functionContent, padding, startTime);
        if (res?.uuid) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'splitFunction',
                duration: performance.now() - startTime,
                uuid: res.uuid,
                type: 'all',
            });
        }
    }

    async resolve(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        conversation: Conversation,
        functionContent: string,
        padding: string,
        startTime: number
    ) {
        const streamMode = this.configProvider.getConfig(ConfigKey.EnableStreamingSession);
        const reply = conversation.addResponse(streamMode ? 'stream' : 'text', '', 'inProgress', {
            regenerate: async () => {
                this.resolve(document, functionRange, conversation, functionContent, padding, performance.now());
            },
            // 打开全文复制按钮
            copyAll: () => {},
        });
        const paramsRes = await buildParams(
            document,
            functionRange.start,
            iocContainer.get(UserService)
        );
        if (paramsRes.type !== 'success') {
            reply.fail(getGenerationFailureText(paramsRes.reason));
            return {uuid: ''};
        }
        const params = {
            ...paramsRes.value,
            content: functionContent,
            model: 'ERNIE_BOT',
            function: 'CODE_SPLIT',
        };
        try {
            if (streamMode) {
                return this.fetchAndStream(document, functionRange, padding, reply, params, startTime);
            }
            else {
                return this.fetchWithParams(document, functionRange, padding, reply, params);
            }
        }
        catch (e: any) {
            reply.fail(getGenerationFailureText(e.message));
            return {uuid: ''};
        }
    }

    async provideCodeLenses(document: vscode.TextDocument) {
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();
        if (codelensDisplayMode !== CodeLensDisplayMode.TextTitle) {
            return [];
        }
        if (this.diffProvider.isInDiff(document.uri.fsPath)) {
            return [];
        }
        const codeLenses = await this.computeCodeLenses(document);
        return this.tmpCodeLensProvider.filterOverlapCodeLenses(document, codeLenses);
    }

    async computeCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensConfig = this.configProvider.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        if (codelensConfig?.enableInlineSplit !== true || !this.licenseController.hasLicense) {
            return [];
        }
        return this.treeSitterProvider.getFunctionCodeLens(
            document,
            filterSplitFunctionNode,
            (document: vscode.TextDocument, range: vscode.Range, node: Parser.SyntaxNode) => {
                const parent = node.parent;
                const editRange = parent && parent.type === 'export_statement'
                    ? new vscode.Range(
                        new vscode.Position(parent.startPosition.row, parent.startPosition.column),
                        range.end
                    )
                    : range;
                return {
                    title: L10n.t(SplitProviderText.CODELENS_TITLE),
                    tooltip: L10n.t(SplitProviderText.CODELENS_TOOLTIP),
                    command: CMD_SPLIT_FUNCTION,
                    arguments: [document, editRange],
                };
            }
        );
    }

    private async fetchWithParams(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        padding: string,
        reply: TextResponse,
        params: GenerateCodeOptions
    ) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        this.timeTracker.recordChatStart(reply.message.replyTo ?? -1);
        const result = await generateCode(params, axiosTokenSource.token);
        this.timeTracker.bindChatId(result.data.data?.uuid ?? '', reply.message.replyTo ?? -1);

        if (result.data.status !== 'OK') {
            reply.fail(getGenerationFailureText(result.data.message));
            return;
        }
        const generatedContent = result.data.data?.content;
        if (!generatedContent) {
            reply.fail(L10n.t(SplitProviderText.GENERATE_ERROR));
            return;
        }
        const uuid = result.data.data?.uuid;
        reply.success(
            generatedContent,
            this.getSuccessActions(
                reply,
                document,
                functionRange,
                padding,
                uuid,
                result.data.data?.chatId
            ),
            uuid
        );
    }

    private async fetchAndStream(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        padding: string,
        reply: TextResponse,
        params: GenerateCodeOptions,
        startTime: number
    ) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        let firstToken = true;
        const {content, uuid, processor, chatId} = await fetchAndStreamCode(
            params,
            (content, uuid, chatId) => {
                if (content) {
                    reply.update(
                        content,
                        this.getSuccessActions(
                            reply,
                            document,
                            functionRange,
                            padding,
                            uuid,
                            chatId
                        )
                    );
                    if (firstToken) {
                        this.performanceLog.log({
                            plugin: 'comate',
                            skill: 'splitFunction',
                            duration: performance.now() - startTime,
                            uuid,
                            type: 'first-token',
                        });
                        firstToken = false;
                    }
                }
            },
            reply.message.cancelTokenSource?.token,
            axiosTokenSource.token
        );
        if (processor.error) {
            reply.fail(getGenerationFailureText(processor.errorMsg));
            return;
        }
        if (!content) {
            reply.fail(L10n.t(SplitProviderText.GENERATE_ERROR));
            return;
        }
        reply.success(
            content,
            this.getSuccessActions(
                reply,
                document,
                functionRange,
                padding,
                uuid,
                chatId
            ),
            uuid
        );
        return {uuid};
    }

    private getSuccessActions(
        reply: TextResponse,
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        padding: string,
        uuid?: string,
        chatId?: string
    ) {
        const {diff, accept} = this.diffProvider.createReplacementDiffHandler(
            document,
            functionRange,
            text => buildReplacement(document, text, padding),
            uuid
        );
        const chatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getChatId: () => chatId,
            getMessageContent: () => reply.message.content,
            getTrackUuid: () => uuid || reply.message.extra?.uuid,
        };
        return {
            ...this.defaultActions(chatResponseProxy, undefined, undefined, {ignoreSmartApplyFeature: true}),
            diff,
            accept,
        };
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
