import * as path from 'path';
import * as vscode from 'vscode';
import axios from 'axios';
import {injectable, inject} from 'inversify';
import Parser from 'web-tree-sitter';
import {Actions, BuiltinAgent} from '@shared/protocols';
import {TYPES} from '@/inversify.config';
import {CMD_OPTIMIZE_FUNCTION} from '@/constants';
import {L10n, getGenerationFailureText, getRequestFailureText} from '@/common/L10nProvider/L10n';
import {OptimizeProviderText} from '@/common/L10nProvider/constants';
import {isPoc, isInternal} from '@/utils/features';
import {GenerateCodeOptions, generateCode} from '../../api';
import {getCompleteFirstLine} from '../../utils/document';
import {stripExtraIndent} from '../../utils/indent';
import {addMarkdownCodeBlock, findLeadingNonWhitespaceIndex} from '../../utils/common';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {iocContainer} from '../../iocContainer';
import {UserService} from '../UserService';
import {optimize, optimizeStreamCode, OptimizeParams} from '../AutoComateChatSession/api';
import {buildParams, fetchAndStreamCode} from '../../common/Fetcher';
import {ChatViewProvider} from '../ChatViewProvider';
import {CodeLensDisplayMode, CodelensConfig, ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {Conversation} from '../ChatViewProvider/Conversation';
import {ITimeTracker} from '../TimeTracker/types';
import {ILicenseController} from '../LicenseController/types';
import {CodeLensProvider} from '../FoldedCodeLensProvider';
import {ChatBaseProvider} from '../ChatBaseProvider';
import {DiffProvider} from '../DiffProvider';
import {TextResponse} from '../ChatViewProvider/TextResponse';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';

const CMD_OPTIMIZE_CODE = 'baidu.comate.optimizeCode';

export function filterOptimizeFunctionNode(node: Parser.SyntaxNode) {
    if (node.hasError()) {
        return false;
    }
    return node.endPosition.row > node.startPosition.row;
}

@injectable()
export class FunctionOptimizeProvider extends ChatBaseProvider implements CodeLensProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'javascript',
        'typescript',
        'cpp',
        'c',
        'vue',
    ];
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(TYPES.ITimeTracker) private readonly timeTracker: ITimeTracker,
        @inject(TYPES.ILicenseController) private readonly licenseController: ILicenseController,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(PerformanceLogProvider) private readonly performanceLog: PerformanceLogProvider,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider
    ) {
        super(diffProvider);
        this.disposables.push(
            vscode.languages.registerCodeLensProvider(
                FunctionOptimizeProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this
            ),
            vscode.commands.registerCommand(
                CMD_OPTIMIZE_FUNCTION,
                (document: vscode.TextDocument, range: vscode.Range) => {
                    this.handleOptimizeFunctionCommand(document, range, performance.now());
                }
            ),
            vscode.commands.registerCommand(
                CMD_OPTIMIZE_CODE,
                () => {
                    this.handleOptimizeSelectedCode(performance.now());
                }
            )
        );
    }

    async handleOptimizeSelectedCode(startTime: number = performance.now()) {
        const document = vscode.window.activeTextEditor?.document;
        if (!document) {
            return;
        }
        const selection = vscode.window.activeTextEditor?.selection;
        if (!selection || !selection.start || !selection.end || !selection.start.isBefore(selection.end)) {
            return;
        }
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_OPTIMIZE_CODE});
        const range = new vscode.Range(selection.start, selection.end);
        const functionContent = document.getText(range);
        const firstLine = getCompleteFirstLine(document, range);
        const conversation = this.chatViewProvider.createConversation(
            L10n.t(OptimizeProviderText.OPTIMIZE_CODE_PROMPT),
            'optimizeFunction',
            addMarkdownCodeBlock(stripExtraIndent(functionContent, false, firstLine), document.languageId)
        );
        const res = await this.resolve(document, range, conversation, functionContent, '', startTime);
        if (res?.uuid) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'optimizeFunction',
                duration: performance.now() - startTime,
                uuid: res.uuid,
                type: 'all',
            });
        }
    }

    async handleOptimizeFunctionCommand(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        startTime: number = performance.now()
    ) {
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_OPTIMIZE_FUNCTION});
        const firstLine = getCompleteFirstLine(document, functionRange);
        const functionContent = stripExtraIndent(document.getText(functionRange), true, firstLine);
        if (!functionContent) {
            const createConversation = this.chatViewProvider.createConversation('', 'optimizeFunction', '');
            createConversation.addResponse(
                'text',
                L10n.t(OptimizeProviderText.NO_SELECTION),
                'failed',
                undefined,
                undefined,
                BuiltinAgent.Comate
            );
            return;
        }
        const conversation = this.chatViewProvider.createConversation(
            L10n.t(OptimizeProviderText.OPTIMIZE_FUNCTION_PROMPT),
            'optimizeFunction',
            addMarkdownCodeBlock(functionContent, document.languageId)
        );
        const definitionLine = document.lineAt(functionRange.start).text;
        const padding = definitionLine.slice(0, findLeadingNonWhitespaceIndex(definitionLine));
        const res = await this.resolve(document, functionRange, conversation, functionContent, padding, startTime);
        if (res?.uuid) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'optimizeFunction',
                duration: performance.now() - startTime,
                uuid: res.uuid,
                type: 'all',
            });
        }
    }

    async resolve(
        document: vscode.TextDocument,
        functionRange: vscode.Range,
        conversation: Conversation,
        functionContent: string,
        padding: string,
        startTime: number
    ) {
        const streamMode = this.configProvider.getConfig(ConfigKey.EnableStreamingSession);
        const reply = conversation.addResponse(
            streamMode ? 'stream' : 'text',
            '',
            'inProgress',
            {
                regenerate: async () => {
                    this.resolve(document, functionRange, conversation, functionContent, padding, performance.now());
                },
                // 打开全文复制按钮
                copyAll: () => {},
            },
            undefined,
            BuiltinAgent.Comate
        );
        const paramsRes = await buildParams(
            document,
            functionRange.start,
            iocContainer.get(UserService)
        );
        if (paramsRes.type !== 'success') {
            reply.fail(getGenerationFailureText(paramsRes.reason));
            return {uuid: ''};
        }
        const file = {
            path: paramsRes.value.path,
            name: path.basename(document.fileName),
            start: {line: functionRange.start.line, column: functionRange.start.character},
            end: {line: functionRange.end.line, column: functionRange.end.character},
            content: paramsRes.value.content,
            repo: paramsRes.value.repo,
        };
        const normalParams = {
            file,
            compiler: vscode.version,
            device: paramsRes.value.device,
            ide: $features.ENTERPRISE_VERSION === 'gitee' ? 'vscode-gitee' : 'vscode',
            // 内部版本username为用户名，saas版本username为license
            username: isInternal ? paramsRes.value.username : paramsRes.value.key,
        };

        const pocParams = {
            ...paramsRes.value,
            content: functionContent,
            model: 'ERNIE_BOT',
            function: 'CODE_TUNING',
        };
        // poc当前交付版本不包含autowork新增接口，调优建议沿用comate一方能力
        const params = isPoc ? pocParams : normalParams;
        try {
            if (streamMode) {
                return await this.fetchAndStream(reply, params, conversation, startTime);
            }
            else {
                return await this.fetchWithParams(reply, params);
            }
        }
        catch (e: any) {
            reply.fail(getGenerationFailureText(e.message));
            return {uuid: ''};
        }
    }

    async provideCodeLenses(document: vscode.TextDocument) {
        const isInDIff = this.diffProvider.isInDiff(document.uri.fsPath);
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();
        if (codelensDisplayMode !== CodeLensDisplayMode.TextTitle) {
            return [];
        }
        if (isInDIff) {
            return [];
        }
        const codeLenses = await this.computeCodeLenses(document);
        return this.tmpCodeLensProvider.filterOverlapCodeLenses(document, codeLenses);
    }

    async computeCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensConfig = this.configProvider.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        if (codelensConfig?.enableInlineOptimize !== true || !this.licenseController.hasLicense) {
            return [];
        }
        return this.treeSitterProvider.getFunctionCodeLens(
            document,
            filterOptimizeFunctionNode,
            (document: vscode.TextDocument, range: vscode.Range, node: Parser.SyntaxNode) => {
                const parent = node.parent;
                const editRange = parent && parent.type === 'export_statement'
                    ? new vscode.Range(
                        new vscode.Position(parent.startPosition.row, parent.startPosition.column),
                        range.end
                    )
                    : range;
                return {
                    title: L10n.t(OptimizeProviderText.CODELENS_TITLE),
                    tooltip: L10n.t(OptimizeProviderText.CODELENS_TOOLTIP),
                    command: CMD_OPTIMIZE_FUNCTION,
                    arguments: [document, editRange],
                };
            }
        );
    }

    private async replySuccess(reply: TextResponse, generatedContent: string, uuid?: string, chatId?: string) {
        const chatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getChatId: () => chatId,
            getMessageContent: () => reply.message.content,
            getTrackUuid: () => uuid || reply.message.extra?.uuid,
        };

        reply.success(
            generatedContent,
            {
                ...this.defaultActions(chatResponseProxy),
                ...this.extraActions(chatResponseProxy),
            },
            uuid
        );
        return {uuid};
    }

    // eslint-disable-next-line complexity
    private async fetchWithParams(reply: TextResponse, params: OptimizeParams | GenerateCodeOptions) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        this.timeTracker.recordChatStart(reply.message.replyTo ?? -1);

        if (isPoc) {
            const result = await generateCode(params as GenerateCodeOptions, axiosTokenSource.token);
            this.timeTracker.bindChatId(result.data.data?.uuid ?? '', reply.message.replyTo ?? -1);

            if (result.data.status !== 'OK') {
                reply.fail(getGenerationFailureText(result.data.message));
                return;
            }

            const generatedContent = result.data.data?.content;
            if (!generatedContent) {
                reply.fail(L10n.t(OptimizeProviderText.OPTIMIZE_ERROR));
                return;
            }

            return this.replySuccess(reply, generatedContent, result.data.data?.uuid, result.data.data?.chatId);
        }
        else {
            const result = await optimize(params as OptimizeParams);
            this.timeTracker.bindChatId(result.data.data?.adoptionUuid ?? '', reply.message.replyTo ?? -1);

            if (result.data.code !== 200) {
                reply.fail(getGenerationFailureText(result.data.message));
                return;
            }

            const generatedContent = result.data.data?.content;
            if (!generatedContent) {
                reply.fail(L10n.t(OptimizeProviderText.OPTIMIZE_ERROR));
                return;
            }

            return this.replySuccess(reply, generatedContent, result.data.data?.adoptionUuid, result.data.data?.chatId);
        }
    }

    private async fetchAndStream(
        reply: TextResponse,
        params: OptimizeParams | GenerateCodeOptions,
        conversation: Conversation,
        startTime: number
    ) {
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        const baseChatResponseProxy = {
            getMessageId: () => String(reply.message.id),
            getMessageContent: () => reply.message.content,
            updateMessage: () => conversation.updateView(),
        };
        let firstToken = true;
        // TODO: 类型约束
        const processStreamCode = async (streamMethod: any) => {
            const {content, uuid, processor, chatId} = await streamMethod(
                params,
                (content: string, uuid: string, chatId: string) => {
                    const chatResponseProxy = {
                        ...baseChatResponseProxy,
                        getChatId: () => chatId,
                        getTrackUuid: () => uuid || reply.message.extra?.uuid,
                    };
                    if (content && firstToken) {
                        this.performanceLog.log({
                            plugin: 'comate',
                            skill: 'optimizeFunction',
                            duration: performance.now() - startTime,
                            uuid,
                            type: 'first-token',
                        });
                        firstToken = false;
                    }
                    const actions: Actions = {
                        ...this.defaultActions(chatResponseProxy),
                        ...this.extraActions(chatResponseProxy),
                    };
                    return reply.update(content, actions);
                },
                reply.message.cancelTokenSource?.token,
                axiosTokenSource.token
            );
            if (processor.error) {
                const msg = processor.errorMsg;
                reply.fail(getRequestFailureText(msg));
                return;
            }
            if (!content) {
                reply.fail(L10n.t(OptimizeProviderText.OPTIMIZE_ERROR));
                return;
            }
            const chatResponseProxy = {
                ...baseChatResponseProxy,
                getChatId: () => chatId,
                getTrackUuid: () => uuid || reply.message.extra?.uuid,
            };
            reply.success(
                content,
                {...this.defaultActions(chatResponseProxy), ...this.extraActions(chatResponseProxy)},
                uuid
            );
            return {uuid};
        };
        if (isPoc) {
            return processStreamCode(fetchAndStreamCode);
        }
        else {
            return processStreamCode(optimizeStreamCode);
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
