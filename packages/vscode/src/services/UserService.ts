import {platform} from 'node:process';
import {readFile} from 'node:fs/promises';
import {resolve} from 'node:path';
import * as os from 'os';
import * as vscode from 'vscode';
import {injectable} from 'inversify';
import 'reflect-metadata';
import {execCommand} from '@/utils/cp';
import {promiseCatchError, thenableCatchError} from '@/utils/promiseCatchError';
import {COMATE_CONFIG_PREFIX} from '../constants';

@injectable()
export class UserService implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private readonly currentUserPromise: Promise<readonly [string, string]> = this.doGetCurrentUser();

    async getCurrentUser() {
        // TODO 使用 configProvider 会相互引用
        const config = vscode.workspace.getConfiguration(COMATE_CONFIG_PREFIX);
        const configUsername = config.get<string>('username');
        return configUsername ? [configUsername] : this.currentUserPromise;
    }

    /**
     * 用户信息优先级 iCoding > git > os.user
     *
     * @returns [用户邮箱前缀, 用户显示名称]
     */
    private async doGetCurrentUser(): Promise<readonly [string, string]> {
        const [, iCodingUserName, displayUserName] = await this.getUserNameFromICoding();
        if (iCodingUserName) {
            return [iCodingUserName, displayUserName];
        }
        const [, gitUserName] = await this.getUserNameFromGit();
        if (gitUserName) {
            return [gitUserName, gitUserName];
        }

        const duguanjiaUsername = await this.getUserNameFromDuGuanJia();
        if (duguanjiaUsername) {
            return [duguanjiaUsername, duguanjiaUsername];
        }

        return this.getOSUserName();
    }

    private getOSUserName() {
        try {
            const {username} = os.userInfo();
            return [username, username] as const;
        }
        catch (e) {
            return ['unknown', 'unknown'] as const;
        }
    }

    private async getUserNameFromDuGuanJia() {
        try {
            if (platform === 'darwin' || platform === 'linux') {
                const config = await execCommand('cat ~/.duguanjia/.Config.plist', process.cwd());
                const [, username] = /<key>CONFIG_LAST_USERNAME<\/key>[\n\s]*<string>(.*)<\/string>/.exec(config) ?? [];
                return username;
            }
            else if (platform === 'win32') {
                const config = await readFile(
                    resolve('C:/Program Files (x86)/Baidu/DuGuanJia/config/DuGuanjiaTray.ini'),
                    'utf-8'
                );
                const [, username] = /Username=(.*)/.exec(config) ?? [];
                return username;
            }
            return undefined;
        }
        catch (e) {
            return undefined;
        }
    }

    private async getUserNameFromGit() {
        try {
            const gitApi = vscode.extensions.getExtension('vscode.git')!.exports.getAPI(1);
            if (gitApi.repositories.length === 0) {
                return ['Uninitialized'] as [any];
            }
            const currentRepositoryPath = gitApi.repositories[0].rootUri.fsPath;
            return promiseCatchError(execCommand('git config --get user.name', currentRepositoryPath));
        }
        catch {
            return [undefined, undefined];
        }
    }

    private async getUserNameFromICoding() {
        const PROVIDER_ID = 'baidu-icoding';
        const [err, session] = await thenableCatchError(vscode.authentication.getSession(PROVIDER_ID, [], {
            createIfNone: true,
        }));
        if (err) {
            return [err];
        }
        return [null, session?.account.id, session?.account.label];
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
