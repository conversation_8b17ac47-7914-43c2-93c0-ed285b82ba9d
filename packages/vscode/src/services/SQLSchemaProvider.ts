import fsPromise from 'node:fs/promises';
import {dirname} from 'path';
import {injectable, inject} from 'inversify';
import * as vscode from 'vscode';
import {EventMessage} from '@shared/protocols';
import {getSQLDDL} from '@/api';
import {error} from '@/common/outputChannel';
import {isFileExist} from '@/utils/fs';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {ChatViewProvider} from './ChatViewProvider';
import {openFile} from './SmartUTProvider/utils';

@injectable()
export class SQLSchemaProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider
    ) {}

    start() {
        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.GetSqlConnectionEvent,
            listener: async () => {
                const context = await getExtensionContextAsync();
                const records = context.globalState.get<Record<string, number | undefined>>(
                    'sql_schema_config', {}
                );

                return records;
            },
        });

        this.chatViewProvider.appendUnAttachedWebViewListener({
            action: EventMessage.SubmitSqlConnectionEvent,
            listener: async (params: any) => {
                const {
                    dialect,
                    host,
                    port,
                    username,
                    password,
                    database,
                    path,
                } = params;

                const context = await getExtensionContextAsync();
                context.globalState.update(
                    'sql_schema_config', params
                );

                const res = await getSQLDDL({
                    dialect,
                    host,
                    port: parseInt(port, 10),
                    username,
                    password,
                    database,
                }).catch(() => null);


                if (res?.status === 'OK' && res?.data?.length > 0) {
                    try {
                        const folder = dirname(path);
                        const isFolderExist = await isFileExist(folder);
                        if (!isFolderExist) {
                            await fsPromise.mkdir(folder, {recursive: true});
                            await fsPromise.stat(folder);
                        }
                        await fsPromise.writeFile(path, res?.data ?? '', 'utf-8');
                        openFile(path, vscode.ViewColumn.Active);
                        return {
                            ok: true,
                            msg: '',
                        };
                    }
                    catch (e) {
                        error(e instanceof Error ? e.message : e?.toString());
                        return {
                            ok: false,
                            msg: '文件创建失败，请确认配置信息后重新尝试',
                        };
                    }
                }

                return {
                    ok: false,
                    msg: res?.message ?? '服务异常，请联系管理员',
                };
            },
        });
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
