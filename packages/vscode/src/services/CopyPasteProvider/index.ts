import {inject, injectable} from 'inversify';
import {pick} from 'lodash';
import * as vscode from 'vscode';
import {logUserPasteMertrics} from '../AutoComateChatSession/api';
import {Config<PERSON>ey, VSCodeConfigProvider} from '../ConfigProvider';

const expandRange = (range: vscode.Range, linesBefore: number, linesAfter: number) => {
    return new vscode.Range(
        new vscode.Position(Math.max(range.start.line - linesBefore, 0), range.start.character),
        new vscode.Position(range.end.line + linesAfter, range.end.character)
    );
};

const getDocumentAfterSeconds = (config: {
    document: vscode.TextDocument;
    beforeText: string;
    afterText: string;
    second: number;
}, callback: (text: string) => any) => {
    return setTimeout(
        () => {
            const {document, beforeText, afterText} = config;
            const content = document.getText();
            const startIndex = content.indexOf(beforeText);
            const endIndex = content.lastIndexOf(afterText);
            if (startIndex < 0 || endIndex < 0) {
                callback('');
                return;
            }

            const startPosition = document.positionAt(startIndex + beforeText.length);
            const endPosition = document.positionAt(endIndex);
            const text = config.document.getText(new vscode.Range(startPosition, endPosition));
            callback(startPosition.isBefore(endPosition) ? text : '');
        },
        config.second * 1000
    );
};

@injectable()
export class DocumentCopyPasteProvider {
    disposables: vscode.Disposable[] = [];
    private readonly loginName = this.configProvider.getConfig(ConfigKey.Username);
    private hasPreviousReportTaskId: NodeJS.Timeout = setTimeout(() => {});

    constructor(@inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider) {
        const changeEvent = vscode.workspace.onDidChangeTextDocument(
            async (ev: vscode.TextDocumentChangeEvent) => {
                const {document, contentChanges, reason} = ev;
                const changed = contentChanges[0];
                const copyedText = await vscode.env.clipboard.readText();
                // 排除掉撤销和恢复
                if (!reason && (!changed?.text || changed.text !== copyedText || copyedText.length < 32)) {
                    return;
                }

                const insertPositionRange = changed.range.start;
                const cursorAfterPasted = vscode.window.activeTextEditor?.selection.active;
                if (!cursorAfterPasted) {
                    return;
                }

                // 把复制的内容扩大到行首和行尾
                const fullPastedText = document.getText(
                    new vscode.Range(
                        new vscode.Position(changed.range.start.line, 0),
                        new vscode.Position(changed.range.start.line, Infinity)
                    )
                );
                clearTimeout(this.hasPreviousReportTaskId);
                const beforeText = document.getText(
                    new vscode.Range(
                        new vscode.Position(0, 0),
                        insertPositionRange
                    )
                );
                const afterText = document.getText(
                    new vscode.Range(
                        cursorAfterPasted,
                        new vscode.Position(document.lineCount + 1, 0)
                    )
                );

                const payload = {
                    // 文件路径
                    file: document.fileName,
                    // 上下100行
                    content: document.getText(expandRange(changed.range, 100, 100)),
                    // 编辑器
                    ide: 'VSCODE',
                    version: 2,
                    originalPastedContent: changed.text,
                    // 用户粘贴的内容
                    pastedContent: fullPastedText,
                    // 用户粘贴时光标所在的行、列（坐标1）
                    cursorPositionBeforePaste: pick(insertPositionRange, ['character', 'line']),
                    // 粘贴完毕后光标所在的行、列（坐标2）
                    cursorPositionAfterPaste: pick(cursorAfterPasted, ['character', 'line']),
                    // 5s之后坐标1到坐标2区间内的代码变动
                    pastedContentAfter5Seconds: '',
                    // 30s之后坐标1到坐标2区间内的代码变动
                    pastedContentAfter30Seconds: '',
                };

                this.hasPreviousReportTaskId = getDocumentAfterSeconds(
                    {document, beforeText, afterText, second: 5},
                    text => {
                        payload.pastedContentAfter5Seconds = text;
                        getDocumentAfterSeconds(
                            {document, beforeText, afterText, second: 25},
                            text => {
                                payload.pastedContentAfter30Seconds = text;
                                logUserPasteMertrics({...payload, loginName: this.loginName as string});
                            }
                        );
                    }
                );
            }
        );
        this.disposables.push(changeEvent);
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
    }
}
