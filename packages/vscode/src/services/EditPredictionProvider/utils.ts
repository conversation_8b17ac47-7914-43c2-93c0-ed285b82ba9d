import {Position} from 'vscode-languageclient';
import {ITextDocument} from '@/common/lsp/types';

/**
 * 如果改写起始和末尾是空行，模型很可能会把空行删掉，模型的判断大多时候是不符合用户预期的，因此把改写范围调整到前后没有空行的区间。
 * 因为返回的范围一定包含光标所在行，当光标所在行前或后都是空行时，仍会保留光标行在返回的区间里。
 * @param document 文档对象
 * @param cursor 光标位置
 * @param startLine 起始行
 * @param endLine 结束行
 * @returns 新的起始行和结束行
 */
export function adjustRewriteRange(
    document: Pick<ITextDocument, 'lineAt'>,
    cursor: Position,
    startLine: number,
    endLine: number
) {
    let newStartLine = startLine;
    let newEndLine = endLine;

    while (
        newStartLine < endLine
        && newStartLine < cursor.line
        && document.lineAt(newStartLine).isEmptyOrWhitespace
    ) {
        newStartLine++;
    }

    while (
        newEndLine > newStartLine
        && newEndLine > cursor.line
        && document.lineAt(newEndLine).isEmptyOrWhitespace
    ) {
        newEndLine--;
    }

    return [newStartLine, newEndLine];
}
