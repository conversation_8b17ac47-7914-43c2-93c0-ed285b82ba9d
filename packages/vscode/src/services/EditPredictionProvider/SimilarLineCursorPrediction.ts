import {randomUUID} from 'node:crypto';
import {Position} from 'vscode-languageclient';
import {maxBy, range} from 'lodash';
import {
    computeJaccardSimilarity,
    computeLCSScore,
    computeLengthScore,
    computePrefixSimilarity,
} from '@/utils/textSimilarity';
import {ITextDocument, TextLine} from '@/common/lsp/types';
import {GenerateCodeOptions} from '@/api';
import {ActionType, apiPersistRecord, TriggerSource} from '@/api/smartTab';
import {SuccessReturn, UnsuccessReturn} from '../types';
import {CursorPrediction, EditContext, ICursorPredictionProvider} from './types';

/**
 * 判断一行文本是否是导入语句，非常简易的判断，后面靠模型了
 *
 * @param line 要判断的文本行
 * @returns 如果是导入语句，则返回 true；否则返回 false
 */
function isImport(line: TextLine) {
    // js, python, go, c++
    const conditionA = ['import', 'from', '#include', '#import'].some(keyword => line.text.startsWith(keyword));
    // commonJS require
    const conditionB = line.text.includes('const') && line.text.includes('require(');
    return conditionA || conditionB;
}

export class SimilarLineCursorPrediction implements ICursorPredictionProvider {
    constructor(
        private readonly buildParams: (
            document: ITextDocument,
            position: Position
        ) => Promise<SuccessReturn<GenerateCodeOptions> | UnsuccessReturn>
    ) {}

    async provideCursorPrediction(
        document: ITextDocument,
        position: Position,
        fromLine: number,
        toLine: number,
        context: EditContext
    ): Promise<CursorPrediction | undefined> {
        if (!context.acceptedInlineCompletion || context.acceptedInlineCompletion.includes('\n')) {
            return;
        }
        const lineAtCursor = document.lineAt(position.line);
        if (this.shouldIgnoreLine(lineAtCursor)) {
            return;
        }
        const lineChars = lineAtCursor.text.split('');
        lineChars.splice(
            position.character - context.acceptedInlineCompletion.length,
            context.acceptedInlineCompletion.length
        );
        const lineBeforeAccept = lineChars.join('');
        const trimmedTargetLine = lineBeforeAccept.trim();
        // 如果采纳前该行小于10个字符，不太有比较意义，放弃预测
        if (trimmedTargetLine.length < 10) {
            return;
        }
        const candidateLineStart = Math.max(0, fromLine);
        const candidateLineEnd = Math.min(document.lineCount - 1, toLine) + 1;
        const candidates = range(candidateLineStart, candidateLineEnd)
            .filter(lineNumber => lineNumber !== position.line)
            .map(lineNumber => document.lineAt(lineNumber));
        const targetLineIndent = lineBeforeAccept.length - lineBeforeAccept.trimStart().length;
        const scores = candidates.map(line => {
            const indent = line.text.length - line.text.trimStart().length;
            const indentScore = targetLineIndent === indent ? 1 : 0;
            const trimmedLine = line.text.trim();
            const prefixMatchScore = computePrefixSimilarity(trimmedLine, trimmedTargetLine);
            const jaccardScore = computeJaccardSimilarity(trimmedLine, trimmedTargetLine);
            const lengthScore = computeLengthScore(
                trimmedLine,
                trimmedTargetLine,
                Math.round(trimmedTargetLine.length / 2)
            );
            const lcsScore = computeLCSScore(trimmedLine, trimmedTargetLine);
            return {
                score: indentScore * 1 + prefixMatchScore * 1 + jaccardScore * 1 + lengthScore * 1 + lcsScore * 1,
                line,
            };
        });
        // NOTE: 现在分数最高是 5，当 winner 分数大于一定阈值时才提供预测
        const winners = scores.filter(candidate => candidate.score > 3);
        // 至少要有多个分数达到阈值的候选时才提供预测
        if (winners.length > 0) {
            const scoreWinner = maxBy(winners, 'score')!;
            const distanceWinner = winners[0];
            // 如果最高分和行号更近的那个分数差值大于一定阈值，则返回最高分
            const topWinner = scoreWinner.score - distanceWinner.score > 0.5 ? scoreWinner : distanceWinner;
            const prediction = topWinner.line.range.start;
            const uuidPromise = this.generateTrackUuid(document, position, prediction.line);
            return {
                uri: document.uri,
                position: prediction,
                uuidPromise,
            };
        }
        return undefined;
    }

    private shouldIgnoreLine(lineAtCursor: TextLine) {
        return isImport(lineAtCursor);
    }

    private async generateTrackUuid(document: ITextDocument, position: Position, predictedLine: number) {
        const uuid = randomUUID();
        const base = await this.buildParams(document, position);
        if (base.type === 'success') {
            await apiPersistRecord({
                ...base.value,
                uuid,
                generatedContent: String(predictedLine + 1),
                shown: false,
                actionType: ActionType.PositionPredict,
                triggerSource: TriggerSource.SimilarCodeRewrite,
                model: 'completion_agent_client',
            });
        }
        return uuid;
    }
}
