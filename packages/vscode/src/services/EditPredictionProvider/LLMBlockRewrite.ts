import {CancellationToken, Position, Range} from 'vscode-languageserver';
import {ActionType, apiRewriteCodeBlock, RewriteCodeBlockResult} from '@/api/smartTab';
import {GenerateCodeOptions} from '@/api';
import {ITextDocument} from '@/common/lsp/types';
import {WithRequired} from '@/utils/typeUtils';
import {error} from '@/common/outputChannel';
import {SuccessReturn, UnsuccessReturn} from '../types';
import {formatForRequest} from '../ProgrammingContextTracker/utils';
import {BlockRewrite, EditContext, IBlockRewriteProvider} from './types';

export class LLMBlockRewrite implements IBlockRewriteProvider {
    constructor(
        private readonly buildParams: (
            document: ITextDocument,
            position: Position
        ) => Promise<SuccessReturn<GenerateCodeOptions> | UnsuccessReturn>
    ) {}

    async provideRewrite(
        document: ITextDocument,
        startLine: number,
        endLine: number,
        context: WithRequired<EditContext, 'triggerSource'>,
        token?: CancellationToken
    ): Promise<BlockRewrite | undefined> {
        const cursor: Position = {
            line: startLine + 1,
            character: 0,
        };
        const requestOptions = await this.buildParams(document, cursor);
        if (requestOptions.type !== 'success') {
            return undefined;
        }
        let response: RewriteCodeBlockResult | null = null;
        try {
            response = await apiRewriteCodeBlock(
                {
                    ...requestOptions.value,
                    neighborSnippetList: context.neighborSnippets
                        ? JSON.stringify(context.neighborSnippets)
                        : undefined,
                    diffList: JSON.stringify(formatForRequest(context.programmingContext)),
                    rewriteSpecificStartRow: startLine + 1,
                    rewriteSpecificEndRow: endLine + 1,
                    actionType: ActionType.Rewrite,
                    triggerSource: context.triggerSource,
                },
                20 * 1000,
                token
            );
        }
        catch (e) {
            error(`apiRewriteCodeBlock failed: ${(e as Error).message}`);
            return undefined;
        }
        const predictedStartLineIdx = response.startRow - 1;
        const predicatedEndLineIdx = response.endRow - 1;
        const endTextLine = document.lineAt(predicatedEndLineIdx);
        const endPosition = endTextLine.range.end;
        return {
            range: Range.create(predictedStartLineIdx, 0, endPosition.line, endPosition.character),
            content: response.generatedContent,
            uuidPromise: Promise.resolve(response.uuid),
        };
    }
}
