import {DocumentUri, Position, Range} from 'vscode-languageclient';
import {CancellationToken} from 'vscode-languageserver';
import {SnippetItem} from '@/common/prompt/NeighborSnippets';
import {ITextDocument} from '@/common/lsp/types';
import {TriggerSource} from '@/api/smartTab';
import {WithRequired} from '@/utils/typeUtils';
import {ProgrammingAction} from '../ProgrammingContextTracker/types';

export interface EditContext {
    /** 若上一个编辑是自动补全，提供补全的文本 */
    acceptedInlineCompletion?: string;
    /** 相似片段或跨文件依赖 */
    neighborSnippets?: SnippetItem[];
    /** 触发的来源 */
    triggerSource?: TriggerSource;
    /** 编码上下文 */
    programmingContext: ProgrammingAction[];
}

export interface CursorPrediction {
    uri: DocumentUri;
    position: Position;
    uuidPromise: Promise<string>;
}

export interface BlockRewrite {
    range: Range;
    content: string;
    uuidPromise: Promise<string>;
}

export interface ICursorPredictionProvider {
    provideCursorPrediction(
        document: ITextDocument,
        position: Position,
        // inclusive
        fromLine: number,
        // inclusive
        toLine: number,
        context: EditContext,
        token?: CancellationToken
    ): Promise<CursorPrediction | undefined>;
}

export interface IBlockRewriteProvider {
    provideRewrite(
        document: ITextDocument,
        startLine: number,
        // inclusive
        endLine: number,
        context: WithRequired<EditContext, 'triggerSource'>,
        token?: CancellationToken
    ): Promise<BlockRewrite | undefined>;
}

/**
 * 定义了预测编辑的接口，具体实现的类中需要根据现状直接提供具体的改写编辑。
 */
export interface IEditPredictionProvider {
    provideEdit(
        document: ITextDocument,
        cursor: Position,
        contex: EditContext,
        token?: CancellationToken
    ): Promise<BlockRewrite | undefined>;
}
