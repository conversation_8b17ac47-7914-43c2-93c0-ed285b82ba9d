/* eslint-disable @typescript-eslint/prefer-readonly */
import * as vscode from 'vscode';
import {APIState, API as GitAPI, GitExtension, PublishEvent, Repository} from '@/external/builtinGitExtension';

export class BuiltinGitProvider implements vscode.Disposable {
    get repositories(): Repository[] {
        return this._gitAPI.repositories as any[];
    }

    get state(): APIState {
        return this._gitAPI.state;
    }

    static instance?: BuiltinGitProvider;

    private _onDidOpenRepository = new vscode.EventEmitter<Repository>();
    readonly onDidOpenRepository: vscode.Event<Repository> = this._onDidOpenRepository.event;
    private _onDidCloseRepository = new vscode.EventEmitter<Repository>();
    readonly onDidCloseRepository: vscode.Event<Repository> = this._onDidCloseRepository.event;
    private _onDidChangeState = new vscode.EventEmitter<APIState>();
    readonly onDidChangeState: vscode.Event<APIState> = this._onDidChangeState.event;
    private _onDidPublish = new vscode.EventEmitter<PublishEvent>();
    readonly onDidPublish: vscode.Event<PublishEvent> = this._onDidPublish.event;
    private _onDidStageAll = new vscode.EventEmitter<Repository>();
    readonly onDidStageAll: vscode.Event<Repository> = this._onDidStageAll.event;

    private _onDidRepoChange = new vscode.EventEmitter<Repository>();
    readonly onDidRepoChange: vscode.Event<Repository> = this._onDidRepoChange.event;

    private _gitAPI: GitAPI;
    private _disposables: vscode.Disposable[];

    private constructor(extension: vscode.Extension<GitExtension>) {
        const gitExtension = extension.exports;
        this._gitAPI = gitExtension.getAPI(1);
        this._disposables = [];
        this._disposables.push(this._gitAPI.onDidCloseRepository(e => this._onDidCloseRepository.fire(e as any)));
        this._disposables.push(this._gitAPI.onDidOpenRepository(e => this._onDidOpenRepository.fire(e as any)));
        this._disposables.push(this._gitAPI.onDidChangeState(e => this._onDidChangeState.fire(e)));
        this._disposables.push(this._gitAPI.onDidPublish(e => this._onDidPublish.fire(e)));
        this._gitAPI.onDidChangeState(e => e);
    }

    static createProvider(): BuiltinGitProvider | undefined {
        if (this.instance) {
            return this.instance;
        }
        try {
            const extension = vscode.extensions.getExtension<GitExtension>('vscode.git');
            if (extension && extension.isActive) {
                this.instance = new BuiltinGitProvider(extension);
                return this.instance;
            }
            return undefined;
        }
        catch (e) {
            return undefined;
        }
    }

    getRepository(uri: vscode.Uri) {
        return this._gitAPI.getRepository(uri);
    }

    dispose() {
        this._disposables.forEach(disposable => disposable.dispose());
    }
}
