import * as vscode from 'vscode';
import {injectable} from 'inversify';

interface Context {
    document: vscode.TextDocument;
    range: vscode.Range;
    title: string;
    tooltip: string;
    command: string;
    data: any;
}

// TODO: 这个类需要重构，可以将 registerCodeLensProvider 单独提取出来，这个类提供一个封装好的 register
// 其他地方通过这个 register 注册，从而统一管理所有的临时 codeLens

type filterCodelensCallback = (document: vscode.TextDocument, codeLenses: vscode.CodeLens[]) => vscode.CodeLens[];

@injectable()
export class TemporaryCodeLensProvider implements vscode.CodeLensProvider, vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private readonly _onDidChangeCodeLenses = new vscode.EventEmitter<void>();
    onDidChangeCodeLenses?: vscode.Event<void> | undefined = this._onDidChangeCodeLenses.event;
    private codelensContext: Context | undefined = undefined;

    private cancelTimer: NodeJS.Timeout | undefined = undefined;

    filterOverlapCodeLensesCallback: filterCodelensCallback[] = [];

    constructor() {
        this.disposables.push(
            vscode.languages.registerCodeLensProvider({scheme: 'file'}, this),
            // Debounce document changes to reduce update frequency
            vscode.workspace.onDidChangeTextDocument(this.debounce((e: vscode.TextDocumentChangeEvent) => {
                if (this.codelensContext?.document.uri.toString() === e.document.uri.toString()) {
                    this.removeTmpCodeLens();
                }
            }, 250)), // 250ms debounce
            // Only update when actually changing files
            vscode.window.onDidChangeActiveTextEditor((editor?: vscode.TextEditor) => {
                if (this.codelensContext
                    && editor
                    && this.codelensContext.document.uri.toString() !== editor.document.uri.toString()
                ) {
                    this.removeTmpCodeLens();
                }
            })
        );
    }

    async provideCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        if (this.codelensContext?.document.uri.toString() !== document.uri.toString()) {
            // 打开其他文件把 codelens 置空
            this.codelensContext = undefined;
            return [];
        }
        const {range, title, tooltip, data, command} = this.codelensContext;
        const codeLens = new vscode.CodeLens(
            range,
            {
                title,
                tooltip,
                command,
                arguments: [data],
            }
        );
        return [codeLens];
    }

    filterOverlapCodeLenses(document: vscode.TextDocument, codeLenses: vscode.CodeLens[]): vscode.CodeLens[] {
        const filteredCodeLenses = this.filterOverlapCodeLensesCallback.reduce(
            (acc, filter) => filter(document, acc),
            codeLenses
        );
        return filteredCodeLenses.filter(codeLens => (
            !this.codelensContext || codeLens.range.start.line !== this.codelensContext.range.start.line
        ));
    }

    onFilterOverlapCodeLenses(filter: filterCodelensCallback) {
        this.filterOverlapCodeLensesCallback.push(filter);
    }

    /**
     * 提供一个临时的 codeLens，用于在特定操作后显示，只要文档有修改这个 codeLens 就会消失
     * 与函数上的 codeLens 重叠时，函数上的 codeLens 会被隐藏
     */
    triggerDisplayTmpCodeLens(context: Context) {
        this.codelensContext = context;
        this._onDidChangeCodeLenses.fire();
        this.clearCancelTimer();
        // 定时 10 秒后自动消失
        this.cancelTimer = setTimeout(
            () => this.removeTmpCodeLens(),
            10 * 1000
        );
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }

    private clearCancelTimer() {
        if (this.cancelTimer) {
            clearTimeout(this.cancelTimer);
        }
    }

    private removeTmpCodeLens() {
        this.codelensContext = undefined;
        this._onDidChangeCodeLenses.fire();
        this.clearCancelTimer();
    }

    private debounce<T extends (...args: any[]) => void>(
        fn: T,
        delay: number
    ): (...args: Parameters<T>) => void {
        let timeoutId: NodeJS.Timeout;
        return (...args: Parameters<T>) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => fn(...args), delay);
        };
    }
}
