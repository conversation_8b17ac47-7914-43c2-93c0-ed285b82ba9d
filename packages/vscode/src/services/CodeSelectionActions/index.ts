import * as vscode from 'vscode';
import {injectable} from 'inversify';
import {CMD_EXPLAIN_SELECTED_CODE, RegisteredCommand} from '@/constants';
import {L10n} from '@/common/L10nProvider/L10n';
import {CodeSelectionActionsProviderText} from '@/common/L10nProvider/constants';
import {isPoc} from '@/utils/features';

const CMD_OPTIMIZE_CODE = 'baidu.comate.optimizeCode';

@injectable()
export class CodeSelectionActionsProvider implements vscode.CodeActionProvider, vscode.Disposable {
    static readonly supportedLanguages = [
        'python',
        'java',
        'go',
        'cpp',
        'javascript',
        'typescript',
        'javascriptreact',
        'jsx',
        'typescriptreact',
    ];

    private disposables: vscode.Disposable[] = [];

    constructor() {
        this.disposables.push(
            vscode.languages.registerCodeActionsProvider(
                CodeSelectionActionsProvider.supportedLanguages.map(item => ({
                    scheme: 'file',
                    language: item,
                })),
                this,
                {
                    providedCodeActionKinds: [
                        vscode.CodeActionKind.QuickFix,
                    ],
                }
            )
        );
    }

    async provideCodeActions(
        document: vscode.TextDocument,
        range: vscode.Range | vscode.Selection
    ): Promise<vscode.CodeAction[]> {
        const codeActions: vscode.CodeAction[] = [];
        const editor = vscode.window.activeTextEditor;

        if (editor && document === editor.document) {
            const userSelection = editor.selection;
            const selectedText = document.getText(userSelection);

            if (userSelection.isEmpty || !selectedText.trim()) {
                return [];
            }

            if (!range.isEqual(userSelection)) {
                return [];
            }

            const explaincode = new vscode.CodeAction(
                L10n.t(CodeSelectionActionsProviderText.EXPLAIN_SELECTED_CODE),
                vscode.CodeActionKind.QuickFix
            );
            explaincode.command = {
                command: CMD_EXPLAIN_SELECTED_CODE,
                title: L10n.t(CodeSelectionActionsProviderText.EXPLAIN_SELECTED_CODE),
                arguments: [],
            };
            // explaincode.isPreferred = true;
            codeActions.push(explaincode);

            const optimizecode = new vscode.CodeAction(
                L10n.t(CodeSelectionActionsProviderText.OPTIMIZE_SELECTED_CODE),
                vscode.CodeActionKind.QuickFix
            );
            optimizecode.command = {
                command: CMD_OPTIMIZE_CODE,
                title: L10n.t(CodeSelectionActionsProviderText.OPTIMIZE_SELECTED_CODE),
                arguments: [],
            };
            codeActions.push(optimizecode);

            if (!isPoc) {
                const inlineChatCode = new vscode.CodeAction(
                    L10n.t(CodeSelectionActionsProviderText.INLINECHAT_SELECTED_CODE),
                    vscode.CodeActionKind.QuickFix
                );
                inlineChatCode.command = {
                    command: RegisteredCommand.showInlineChat,
                    title: L10n.t(CodeSelectionActionsProviderText.INLINECHAT_SELECTED_CODE),
                    arguments: [],
                };
                codeActions.push(inlineChatCode);
            }
        }
        return codeActions;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
