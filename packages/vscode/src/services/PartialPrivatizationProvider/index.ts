/**
 * 和部分私有化相关的内容，以VPC为主，和混合云不同
 */
import dns from 'node:dns/promises';
import * as vscode from 'vscode';
import {injectable, inject, LazyServiceIdentifer} from 'inversify';
import {debounce} from 'lodash';
import {EventMessage, Feature} from '@shared/protocols';
import {LicenseFullDetail, setApiHost} from '@comate/plugin-shared-internals';
import {Address4, Address6} from 'ip-address';
import {getLicenseFullDetail} from '@/api';
import {error, info} from '@/common/outputChannel';
import {CONTEXT_SHOW_GIT_MESSAGE, CONTEXT_SHOW_FEEDBACK} from '@/constants';
import {isInternal, isPoc} from '@/utils/features';
import {VSCodeConfigProvider} from '../ConfigProvider';
import {ChatViewProvider} from '../ChatViewProvider';

export function defaultLicenseFullDetail(): LicenseFullDetail {
    return {
        key: '',
        type: '标准版',
        typeCode: 'TRIAL_INDIVIDUAL',
        typeId: 2,
        customized: false,
        customizedUrl: '',
        vpcConfig: {
            status: false,
            endpoint: '',
            intranetDomain: '',
            intranetSubnets: [],
            intranetErrorMsg: '',
        },
        features: {
            enableUploadKnowledgeFromIDE: true,
            customFeedbackURL: '',
        },
        commonConfig: {
            diffTrackerRateSeconds: 180,
            effectiveInputLines: 30,
            uploadFilesBranchChangeMonitoringInterval: 10,
        },
    };
}

@injectable()
export class PartialPrivatizationProvider {
    private readonly disposables: vscode.Disposable[] = [];
    private enable: boolean = false;
    isInternalNetwork: boolean = true;
    private readonly debouncedUpdateConfig: () => void;
    private vpcConfig: LicenseFullDetail['vpcConfig'] = {
        status: false,
        endpoint: '',
        intranetDomain: '',
        intranetSubnets: [],
        intranetErrorMsg: '请在规定的网络环境中使用',
    };
    private features: Record<string, any> = {};
    private commonConfig: Record<string, any> = {
        diffTrackerRateSeconds: 180,
        effectiveInputLines: 30,
        uploadFilesBranchChangeMonitoringInterval: 10,
    };
    private readonly configInitialized: Promise<void>;
    private resolveConfigInitialized!: () => void;
    private readonly _onConfigUpdated = new vscode.EventEmitter<LicenseFullDetail['vpcConfig']>();
    readonly onConfigUpdated: vscode.Event<LicenseFullDetail['vpcConfig']> = this._onConfigUpdated.event;

    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider
    ) {
        this.debouncedUpdateConfig = debounce(this.updateConfig.bind(this), 300);
        this.configInitialized = new Promise(resolve => {
            this.resolveConfigInitialized = resolve;
        });
    }

    async start() {
        this.disposables.push(
            vscode.workspace.onDidChangeConfiguration(async e => {
                if (e.affectsConfiguration('baidu.comate.license')) {
                    this.updateConfig();
                }
                if (e.affectsConfiguration('baidu.comate.privateService')) {
                    this.debouncedUpdateConfig();
                }
            })
        );
        this.updateConfig();
    }

    get isEnable() {
        return this.enable;
    }

    get intranetErrorMsg() {
        return this.vpcConfig?.intranetErrorMsg;
    }

    get privateHost() {
        return this.vpcConfig?.endpoint;
    }

    feature(key: string) {
        return this.features[key];
    }

    async updateConfig() {
        try {
            await this.retryOperation(this.performUpdateConfig.bind(this), 3);
            this.chatViewProvider.sendDataToWebview(EventMessage.PrivateServiceUpdateEvent);
        }
        catch (error) {
            if (error instanceof Error) {
                info('Failed to update config after retries:', error.message);
            }
        }
    }

    private async performUpdateConfig() {
        const privateService = this.configProvider.getPrivateService();
        if (privateService && (!isInternal)) {
            setApiHost(privateService);
        }
        const license = this.configProvider.getLicense();
        try {
            if (license && license.length > 32) {
                const licenseDetail = await getLicenseFullDetail(license);
                this.enable = Boolean(licenseDetail?.vpcConfig?.status);
                this.vpcConfig = licenseDetail?.vpcConfig;
                this.features = licenseDetail?.features;
                this.commonConfig = licenseDetail?.commonConfig;
                this._onConfigUpdated.fire(this.vpcConfig);
                this.resolveConfigInitialized();
                this.chatViewProvider.sendDataToWebview(
                    EventMessage.LicenseFullDetailChangeEvent,
                    {...licenseDetail, endpoint: privateService}
                );
                if (this.enable && licenseDetail?.vpcConfig?.endpoint) {
                    vscode.commands.executeCommand('setContext', CONTEXT_SHOW_GIT_MESSAGE, false);
                    this.chatViewProvider.sendDataToWebview(EventMessage.FeatureFlagsChangeEvent, {
                        [Feature.EnableUserGuide]: false,
                        [Feature.ComatePlus]: false,
                    });
                    this.updatePrivateServiceConfig(licenseDetail?.vpcConfig?.endpoint);
                    setApiHost(licenseDetail.vpcConfig?.endpoint);
                    vscode.commands.executeCommand('setContext', CONTEXT_SHOW_FEEDBACK, false);
                }
            }
            else if (!isPoc) {
                this.configProvider.updatePrivateConfig('');
                this.resolveConfigInitialized();
            }
        }
        catch {
            this.resolveConfigInitialized();
        }
    }

    private async retryOperation(operation: () => Promise<void>, maxRetries: number): Promise<void> {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                await operation();
                return;
            }
            catch (error) {
                if (attempt === maxRetries) {
                    throw error;
                }
                info(`Retry attempt ${attempt} failed. Retrying...`);
                await new Promise(resolve => setTimeout(resolve, 300));
            }
        }
    }

    async updatePrivateServiceConfig(baseURL: string) {
        try {
            this.configProvider.updatePrivateConfig(baseURL);
        }
        catch (err) {
            if (err instanceof Error) {
                error(`Private service health error: ${err.message}`);
            }
        }
    }

    // 解析网络环境
    async parseNetworkEnvironment() {
        if (!this.vpcConfig) {
            // 改了下类型，如果为空时直接返回了，但实际没有用到这个方法，如果后面有用请 @wuweiqi 确认
            return true;
        }
        const {intranetDomain, intranetSubnets, intranetErrorMsg} = this.vpcConfig;
        try {
            const ip = await dns.lookup(intranetDomain);
            const isInternal = this.isIpInCidrList(ip.address, intranetSubnets, ip.family);
            this.isInternalNetwork = isInternal;
            if (!isInternal) {
                this.chatViewProvider.sendDataToWebview(EventMessage.FeatureFlagsChangeEvent, {
                    [Feature.OnlyErrorResponseInChat]: true,
                });
                vscode.window.showInformationMessage(intranetErrorMsg);
                return false;
            }
        }
        catch (err) {
            if (err instanceof Error) {
                error(`Dns looup ${intranetDomain} error: ${err.message}`);
            }
        }
        return true;
    }

    isIpInCidrList(ip: string, cidrList: string[], family: number) {
        if (family === 4) {
            // 尝试创建 IPv4 地址对象
            const ipObj = new Address4(ip);
            for (const cidr of cidrList) {
                const network = new Address4(cidr);
                if (ipObj.isInSubnet(network)) {
                    return true;
                }
            }
            return false;
        }
        const ipObj = new Address6(ip);
        for (const cidr of cidrList) {
            const network = new Address6(cidr);
            if (ipObj.isInSubnet(network)) {
                return true;
            }
        }
        return false;
    }

    async getCommonConfig(key: string): Promise<number> {
        await this.configInitialized;
        return this.commonConfig[key];
    }
}
