import Ajv from 'ajv';
import * as vscode from 'vscode';
import {CodeChunk} from '../common/types';
import {SymbolSearchParams, SearchAction} from '../api/codeSearch';
import {getPathRelativeToWorkspace, reviveFullUriToWorkspace} from '../utils/workspace';
import {
    getDetailedSymbolInfo,
    getSymbolDefinition,
    getSymbolReferences,
    getSymbolImplementations,
} from '../utils/symbols';

const ajv = new Ajv();

export interface SearchResponse {
    data?: CodeChunk[];
    error?: string;
    success: boolean;
}

const VALID_ACTIONS = Object.keys(SearchAction).filter(item => isNaN(Number(item)));

const validate = ajv.compile({
    type: 'object',
    properties: {
        repo: {type: 'string', nullable: true},
        file: {type: 'string', nullable: true},
        action: {enum: VALID_ACTIONS},
        symbol: {
            type: 'object',
            properties: {
                name: {type: 'string', nullable: true},
                kind: {type: 'string', nullable: true}, // 这里的限制都尽量 soft
            },
        },
    },
    required: ['action'],
});

function positionToLineAndColumn(position: vscode.Position) {
    return {
        line: position.line,
        column: position.character,
    };
}

async function locationToSymbolChunkMatch(
    location: vscode.Location
): Promise<Pick<CodeChunk, 'content' | 'contentStart' | 'contentEnd'>> {
    const selectionRange = location.range;
    const expandedRange = new vscode.Range(
        selectionRange.start.line - 10,
        0,
        selectionRange.end.line + 10,
        selectionRange.end.character
    );
    const doc = await vscode.workspace.openTextDocument(location.uri);
    const content = doc.getText(expandedRange);
    return {
        content,
        contentStart: positionToLineAndColumn(expandedRange.start),
        contentEnd: positionToLineAndColumn(expandedRange.end),
    };
}

async function getSymbolMetadata(symbol: vscode.SymbolInformation) {
    const detailedSymbolInfo = await getDetailedSymbolInfo(symbol);
    if (!detailedSymbolInfo) {
        throw new Error('Unable to find the detailed symbol information');
    }
    const doc = await vscode.workspace.openTextDocument(symbol.location.uri);
    const range = detailedSymbolInfo.range;
    const content = doc.getText(range);
    return {
        content,
        contentStart: positionToLineAndColumn(range.start),
        contentEnd: positionToLineAndColumn(range.end),
    };
}

async function provideDocumentContent(params: SymbolSearchParams): Promise<SearchResponse> {
    // eslint-disable-next-line @typescript-eslint/init-declarations
    let fileToRead: vscode.Uri | undefined;
    try {
        const symbolDefinition = await getSymbolDefinition(params.symbol);
        fileToRead = symbolDefinition.location.uri;
    }
    catch (e) {
        fileToRead = await reviveFullUriToWorkspace(params.file);
    }
    if (!fileToRead) {
        throw new Error('Unable to determine which file to read');
    }
    const doc = await vscode.workspace.openTextDocument(fileToRead);
    const content = doc.getText();
    const data: CodeChunk[] = [
        {
            repo: params.repo,
            path: getPathRelativeToWorkspace(fileToRead),
            type: 'didopen',
            content,
            contentStart: {
                line: 0,
                column: 0,
            },
            contentEnd: {
                line: doc.lineCount,
                column: 0,
            },
        },
    ];
    return {
        data,
        success: true,
        error: '',
    };
}

async function provideSymbolDefinition(params: SymbolSearchParams): Promise<SearchResponse> {
    const targetFilePath = await reviveFullUriToWorkspace(params.file);
    const symbol = await getSymbolDefinition(params.symbol, targetFilePath);
    const metadata = await getSymbolMetadata(symbol);
    const data: CodeChunk[] = [
        {
            repo: params.repo,
            path: getPathRelativeToWorkspace(symbol.location.uri),
            type: 'definition',
            ...metadata,
        },
    ];
    return {
        data,
        success: true,
        error: '',
    };
}

function isExcludedFile(fileUri: vscode.Uri) {
    const path = fileUri.fsPath;
    if (path.endsWith('.class')) {
        return true;
    }
    return false;
}

async function provideSymbolReferences(params: SymbolSearchParams): Promise<SearchResponse> {
    const targetFilePath = await reviveFullUriToWorkspace(params.file);
    const locations = await getSymbolReferences(params.symbol, targetFilePath);
    if (!locations) {
        return {
            data: [],
            success: true,
            error: '',
        };
    }
    const filteredLocations = locations.filter(item => !isExcludedFile(item.uri));
    const data: CodeChunk[] = await Promise.all(filteredLocations.map(async location => {
        return {
            repo: params.repo,
            path: getPathRelativeToWorkspace(location.uri),
            type: 'reference',
            ...(await locationToSymbolChunkMatch(location)),
        };
    }));
    return {
        data,
        success: true,
        error: '',
    };
}

async function provideSymbolImplementations(params: SymbolSearchParams): Promise<SearchResponse> {
    const targetFilePath = await reviveFullUriToWorkspace(params.file);
    const locations = await getSymbolImplementations(params.symbol, targetFilePath);
    if (locations) {
        const normalizedLocations = locations
            .filter(item => {
                const uri = (item as vscode.Location).uri ?? (item as vscode.LocationLink).targetUri;
                return !isExcludedFile(uri);
            })
            .map(item => {
                if ((item as vscode.Location).uri) {
                    // it is a vscode.Location
                    return item as vscode.Location;
                }
                return new vscode.Location(
                    (item as vscode.LocationLink).targetUri,
                    (item as vscode.LocationLink).targetRange
                );
            });
        const data: CodeChunk[] = await Promise.all(normalizedLocations.map(async location => {
            return {
                repo: params.repo,
                path: getPathRelativeToWorkspace(location.uri),
                type: 'implements',
                ...(await locationToSymbolChunkMatch(location)),
            };
        }));
        return {
            data,
            success: true,
            error: '',
        };
    }
    return {
        data: [],
        success: true,
        error: '',
    };
}

export async function provideSymbolContext(params: SymbolSearchParams) {
    const isValid = validate(params);
    if (!isValid) {
        throw new Error('The given arguments do not match the schema');
    }
    switch (params.action) {
        case SearchAction.DidOpen: {
            const result = await provideDocumentContent(params);
            return result;
        }
        case SearchAction.Definition: {
            const result = await provideSymbolDefinition(params);
            return result;
        }
        case SearchAction.Reference: {
            const result = await provideSymbolReferences(params);
            return result;
        }
        case SearchAction.Implements: {
            const result = await provideSymbolImplementations(params);
            return result;
        }
        default:
            throw new Error(`Unknown action ${params.action}`);
    }
}
