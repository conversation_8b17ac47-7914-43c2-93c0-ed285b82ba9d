/* eslint-disable complexity */
import * as vscode from 'vscode';
import {injectable, inject, LazyServiceIdentifer} from 'inversify';
import {EventMessage, SmartApplyStatus} from '@shared/protocols';
import {ACTION_COMATE_SMART_APPLY} from '@comate/plugin-shared-internals';
import {RegisteredCommand} from '@/constants';
import {getTraceRepoInfo} from '@/utils/trace';
import {sleep} from '@/utils/common';
import {info} from '@/common/outputChannel';
import {UserService} from '../UserService';
import {KernelProvider} from '../KernelProvider';
import {ChatViewProvider} from '../ChatViewProvider';
import {ApplyTask} from './ApplyTask';

@injectable()
export class SmartApplyProvider implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private runningTask: {key: string, filePath: string, task: ApplyTask} | null = null;

    constructor(
        @inject(UserService) private readonly userService: UserService,
        @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(new LazyServiceIdentifer(() => ChatViewProvider)) private readonly chatViewProvider: ChatViewProvider
    ) {
        this.disposables.push(
            vscode.commands.registerCommand(RegisteredCommand.smartApply, this.smartApply.bind(this)),
            vscode.workspace.onDidCloseTextDocument(e => {
                const relativePath = vscode.workspace.asRelativePath(e.uri);
                if (relativePath === this.runningTask?.filePath) {
                    const task = this.runningTask.task;
                    this.runningTask = null;
                    task.reject();
                }
            })
        );

        this.kernelProvider.client?.onRequest(
            ACTION_COMATE_SMART_APPLY,
            async ({uuid, text, error, traceId, diff, finish}) => {
                const task = this.runningTask?.task;
                if (!task || task.key !== uuid) {
                    return;
                }

                if (finish || error) {
                    task.end(text, diff);
                    info(`smartApply finish, traceId=${traceId}`);
                    const status = error ? SmartApplyStatus.APPLIED : SmartApplyStatus.INQUIRE;
                    if (error) {
                        this.chatViewProvider.sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                            type: 'fail',
                            message: `生成失败：${error}，请重新生成`,
                        });
                    }
                    this.chatViewProvider.sendDataToWebview(EventMessage.SmartApplyUpdate, {key: uuid, status});
                }
                else {
                    task.update(text);
                }
            }
        );
    }

    private async smartApply(
        {content, key, accepted, uuid}: {uuid: string, content: string, key: string, accepted?: boolean}
    ) {
        if (this.runningTask) {
            const task = this.runningTask.task;
            if (accepted) {
                await task.accept();
                return;
            }
            else if (accepted === false) {
                await task.reject();
                return;
            }
            else {
                await task.reject();
                await sleep(50);
            }
        }

        const activeEditor = vscode.window.activeTextEditor;
        const document = activeEditor?.document;
        const selection = activeEditor?.selection;
        if (document?.uri.scheme !== 'file' || !selection) {
            this.chatViewProvider.sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                type: 'info',
                message: '未识别到编码区的光标位置，无法插入',
            });
            this.chatViewProvider.sendDataToWebview(EventMessage.SmartApplyUpdate, {
                key,
                status: SmartApplyStatus.UNTOUCHED,
            });
            return;
        }

        const filePath = vscode.workspace.asRelativePath(document.uri.fsPath);
        const [username] = await this.userService.getCurrentUser();
        const eol = document.eol === vscode.EndOfLine.LF ? '\n' : '\r\n';
        this.runningTask = {
            filePath,
            key,
            task: new ApplyTask(key, document.uri.fsPath, content, uuid, () => {
                this.runningTask = null;
            }),
        };
        this.runningTask.task.track('generate');
        this.kernelProvider.client?.sendRequest(ACTION_COMATE_SMART_APPLY, {
            uuid: key,
            change: content,
            file: {
                path: filePath,
                content: document.getText(),
                eol,
                repoName: (await getTraceRepoInfo()).repoName,
            },
            username,
        });

        return filePath;
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
