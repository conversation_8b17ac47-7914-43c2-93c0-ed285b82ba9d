/* eslint-disable camelcase */
import * as vscode from 'vscode';
import {EventMessage, SmartApplyStatus} from '@shared/protocols';
import {diffLines} from 'diff';
import axios from 'axios';
import {once} from 'lodash';
import {iocContainer} from '@/iocContainer';
import {sleep, splitEol} from '@/utils/common';
import {isSaaS} from '@/utils/features';
import {ChatViewProvider} from '../ChatViewProvider';
import {DiffHighlightProvider} from '../DiffProvider/DiffHighlightProvider';
import {overwriteDocument} from '../Composer/utils/edit';
import {LogCategory, LogUploaderProvider} from '../LogUploaderProvider';

export class ApplyTask {
    private readonly diffHighlightProvider = iocContainer.get(DiffHighlightProvider);
    private readonly chatViewProvider = iocContainer.get(ChatViewProvider);
    private originalContent: string = '';
    private generatedContent: string = '';
    private diff: string = '';
    private readonly codelensDisposable: vscode.Disposable | undefined;
    private readonly startTime = Date.now();
    private endTime = Date.now();
    private readonly reportedEvaluateData = once(() => {
        if (isSaaS) {
            return;
        }
        axios.post('https://apigo.baidu-int.com/chatgpt/comate/eval/data/intelligent/adoption', {
            original_query: '',
            changed_file_content: this.originalContent,
            adopted_code_block: this.changed,
            model_result: this.diff,
            full_text: this.generatedContent,
            end_to_end_time: (this.endTime - this.startTime) / 1000,
        }, {
            headers: {
                'x-app-code': 'apiGoAc-23cb4f5bf60b41e2ad64930d89f847fb-online',
            },
        });
    });

    private readonly textDecoration = vscode.window.createTextEditorDecorationType(
        {
            isWholeLine: true,
            backgroundColor: {id: 'diffEditor.insertedLineBackground'},
            outlineWidth: '1px',
            outlineStyle: 'solid',
            outlineColor: {id: 'diffEditor.insertedTextBorder'},
            rangeBehavior: vscode.DecorationRangeBehavior.ClosedClosed,
        }
    );

    protected readonly generatedContentLines: {lines: string[], next: number} = {lines: [], next: 0};
    get nextLine() {
        return this.generatedContentLines.lines[this.generatedContentLines.next];
    }

    constructor(
        readonly key: string,
        readonly filePath: string,
        readonly changed: string,
        private readonly trackUuid: string,
        private readonly onCancel: () => void
    ) {
        const range = this.safeFullRange;
        this.diffHighlightProvider.addLoadingRange(filePath, range);
        this.diffHighlightProvider.updateIndexLineDecorations(filePath, this.safeFullRange);
        this.diffHighlightProvider.addOnCancelCallback(filePath, async (source?: {finish?: boolean}) => {
            // await this.diffHighlightProvider.clearLoadingRange(filePath, false);
            if (!source?.finish) {
                this.onCancel();
                // 手动 Esc 需要处理
                this.reject();
            }
        });

        this.codelensDisposable = this.diffHighlightProvider.onDidDiffAcceptedChanged(({accepted}) => {
            this.track(accepted ? 'accepted' : 'rejected');
            if (!this.diffHighlightProvider.isInDiff(this.uri.fsPath)) {
                this.chatViewProvider.sendDataToWebview(EventMessage.SmartApplyUpdate, {
                    key: this.key,
                    status: SmartApplyStatus.APPLIED,
                });
                this.codelensDisposable?.dispose();
            }
        });

        vscode.workspace.openTextDocument(this.uri).then(document => {
            this.originalContent = document.getText();
        });

        const editor = vscode.window.activeTextEditor;
        this.streamOutput(editor!);
    }

    private async streamOutput(editor: vscode.TextEditor) {
        while (!this.isEnd) {
            // eslint-disable-next-line no-negated-condition
            if (typeof this.nextLine !== 'undefined') {
                const lines = this.generatedContentLines.lines.slice(0, this.generatedContentLines.next + 1);
                const content = lines.join('\n');
                await this.updateEditor(editor, content);
                this.generatedContentLines.next = this.generatedContentLines.next + 1;
            }

            await sleep(17);
        }
    }

    get uri() {
        return vscode.Uri.file(this.filePath);
    }

    get safeFullRange() {
        return new vscode.Range(
            new vscode.Position(0, 0),
            new vscode.Position(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER)
        );
    }

    private isEnd = false;
    async update(content: string) {
        if (content) {
            this.generatedContentLines.lines = content.replace(/\r?\n$/, '').split(/\r?\n/);
        }
    }

    private async updateEditor(editor: vscode.TextEditor, text: string) {
        const generatedContentLines = splitEol(text);
        const range = new vscode.Range(
            new vscode.Position(0, 0),
            new vscode.Position(generatedContentLines.length, Number.MAX_SAFE_INTEGER)
        );

        await this.diffHighlightProvider.updateIndexLineDecorations(
            this.uri.fsPath,
            new vscode.Range(
                new vscode.Position(generatedContentLines.length + 1, 0),
                new vscode.Position(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER)
            )
        );

        const partialOriginalContent = splitEol(this.originalContent).slice(0, range.end.line).join('\n');
        if (partialOriginalContent.trim() === text.trim()) {
            return;
        }

        await editor.edit(editBuilder => {
            editBuilder.replace(range, text);
        }, {undoStopBefore: false, undoStopAfter: false});
        const diffs = diffLines(this.originalContent, text);

        const {add: addedLineRanges} = diffs.reduce<{add: vscode.Range[], currentLine: number}>(
            (result, diff) => {
                if (diff.added) {
                    const range = new vscode.Range(
                        new vscode.Position(result.currentLine, 0),
                        new vscode.Position(result.currentLine + (diff.count! - 1), Number.MAX_SAFE_INTEGER)
                    );
                    result.add.push(range);
                }
                // eslint-disable-next-line no-param-reassign
                result.currentLine += diff.count || 1;
                return result;
            },
            {add: [], currentLine: 0}
        );

        editor.setDecorations(this.textDecoration, addedLineRanges);
    }

    async end(text: string, diff: string) {
        this.isEnd = true;
        this.diff = diff;
        this.endTime = Date.now();
        this.generatedContent = text;

        this.textDecoration.dispose();
        this.diffHighlightProvider.updateIndexLineDecorations(
            this.uri.fsPath,
            new vscode.Range(
                new vscode.Position(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER),
                new vscode.Position(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER)
            )
        );
        this.diffHighlightProvider.clearLoadingRange(this.filePath, {finish: true});

        const editor = vscode.window.activeTextEditor!;
        await editor.edit(editBuilder => {
            editBuilder.replace(this.safeFullRange, this.originalContent);
        }, {undoStopBefore: false, undoStopAfter: false});
        const originalContentLines = this.originalContent ? splitEol(this.originalContent) : [];
        const generatedContentLines = splitEol(text);
        const startLine = originalContentLines.findIndex((line, i) => {
            return line !== generatedContentLines[i];
        });
        // 如果-1要么就是完全相同，要么就是原内容行数小于
        const startDifferentLine = startLine === -1 ? Math.max(0, originalContentLines.length - 1) : startLine;
        const range = new vscode.Range(
            new vscode.Position(startDifferentLine, 0),
            new vscode.Position(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER)
        );
        const modifiedContent = generatedContentLines.slice(startDifferentLine).join('\n');
        await this.diffHighlightProvider.applyLineDiffHighlight(
            this.uri.fsPath,
            range,
            modifiedContent,
            this.trackUuid
        );

        // 滚动到选中的行位置
        editor.revealRange(range, vscode.TextEditorRevealType.InCenterIfOutsideViewport);
    }

    private stop() {
        if (this.isEnd) {
            return;
        }

        this.isEnd = true;
        this.onCancel();
        this.diffHighlightProvider.clearLoadingRange(this.filePath);
        this.chatViewProvider.sendDataToWebview(EventMessage.SmartApplyUpdate, {
            key: this.key,
            status: SmartApplyStatus.APPLIED,
        });
        this.textDecoration.dispose();
        this.codelensDisposable?.dispose();
    }

    async reject() {
        await this.stop();
        await this.diffHighlightProvider.rejectAllChanges();
        await overwriteDocument(this.uri, this.originalContent);
        await this.diffHighlightProvider.updateIndexLineDecorations(
            this.uri.fsPath,
            new vscode.Range(
                new vscode.Position(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER),
                new vscode.Position(Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER)
            )
        );
    }

    async accept() {
        await this.stop();
        await this.diffHighlightProvider.acceptAllChanges();
    }

    track(label: 'accepted' | 'rejected' | 'generate') {
        if (label === 'accepted') {
            this.reportedEvaluateData();
        }
        const logProvider = iocContainer.get(LogUploaderProvider);
        logProvider.logUserAction({category: LogCategory.App, action: 'smartApply', label});
    }
}
