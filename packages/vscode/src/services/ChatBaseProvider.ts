/* eslint-disable max-lines */
/* eslint-disable max-statements */
/* eslint-disable max-depth */
import {writeFile} from 'node:fs/promises';
import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {omit} from 'lodash';
import {
    Actions,
    FeedbackOptions,
    MessageType,
    Accept,
    Feature,
    EventMessage,
    SmartApplyStatus,
} from '@shared/protocols';
import {AcceptWithDependentFiles} from '@comate/plugin-shared-internals';
import {isInternal, isPoc} from '@/utils/features';
import {TYPES} from '@/inversify.config';
import {iocContainer} from '@/iocContainer';
import {L10n} from '@/common/L10nProvider/L10n';
import {ChatProviderText, AcceptProviderText} from '@/common/L10nProvider/constants';
import {generateFileComment} from '@/api/codeSearch';
import {reviveFullUriToWorkspace} from '@/utils/workspace';
import captureActualCodeAfterGeneration from '@/utils/captureActualCodeAfterGeneration';
import {isFileExist, ensureFilePathExists} from '@/utils/fs';
import {isValidUri} from '@/utils/common';
import {postInsertionTasks} from '@/common/telemetry/postTasks';
import {RegisteredCommand} from '@/constants';
import {ACCEPTANCE_TYPE, GenerateCodeOptions, acceptCode, modifyCode} from '../api';
import {DiffProvider} from './DiffProvider';
import {chatActionDelayedReporter} from './ChatViewProvider/chatActionDelayedReporter';
import {ITimeTracker} from './TimeTracker/types';
import {UserService} from './UserService';
import {apis} from './AutoComateChatSession/api';
import {ChatViewProvider} from './ChatViewProvider';
import {DiffHighlightProvider} from './DiffProvider/DiffHighlightProvider';
import {LogCategory, LogUploaderProvider} from './LogUploaderProvider';

// 如果代码块中有```会有bug，但是与后端的正则逻辑保持一致
function parseMarkdownCodeBlocks(mdText: string) {
    // 匹配 `````, ```任何字符(非贪婪)````的正则表达式
    const regex = /```.*?\n([^`]+)```/g;
    // eslint-disable-next-line @typescript-eslint/init-declarations
    let match;
    const resultArray = [];

    while ((match = regex.exec(mdText))) {
        resultArray.push(match[1]);
    }

    return resultArray;
}

export interface ChatResponseProxy {
    getMessageId: () => string;
    getMessageContent: () => string | undefined;
    getTrackUuid: () => string | undefined;
    getChatId: () => string | undefined;
    updateMessage?: () => void;
    getResponseType?: 'normal' | 'async';
}

interface AcceptanceContext {
    originContent?: string;
    insertPosition?: vscode.Position;
    acceptanceType: ACCEPTANCE_TYPE;
}

export type Action = (content: string, language?: string, extra?: {filePath: string, from: string, to: string}) => any;

@injectable()
export class ChatBaseProvider {
    lastSelection: vscode.Selection | undefined;
    private enableSmartApply: boolean = false;

    constructor(@inject(DiffProvider) readonly diffProvider: DiffProvider) {
        iocContainer.get(UserService).getCurrentUser().then(([userName]) => {
            apis.getAccessTo({featureName: Feature.SmartApply, userName}).then(res => {
                this.enableSmartApply = res.status;
            });
        });
    }

    private logUserAction(action: string) {
        const logUploaderProvider = iocContainer.get(LogUploaderProvider);
        logUploaderProvider.logUserAction({category: LogCategory.chatCodeBlockAction, action});
    }

    protected defaultActions(
        chatResponseProxy: ChatResponseProxy,
        sourceProvider?: MessageType,
        params?: GenerateCodeOptions,
        opts?: {
            skipCodeRetention?: boolean;
            /** 忽略掉智能采纳的小流量，总是返回accept动作 */
            ignoreSmartApplyFeature?: boolean;
        }
    ): Record<string, Action> {
        // eslint-disable-next-line @typescript-eslint/init-declarations
        let id: number | undefined;
        const skipCodeRetention = opts?.skipCodeRetention;
        const ignoreSmartApplyFeature = opts?.ignoreSmartApplyFeature;
        const documentAndPosition = () => {
            const activeEditor = vscode.window.activeTextEditor;
            const document = activeEditor?.document;
            const selection = activeEditor?.selection;
            if (document?.uri.scheme !== 'file' && document?.uri.scheme !== 'untitled') {
                return {};
            }

            if (selection) {
                this.lastSelection = selection;
            }
            if (!this.lastSelection) {
                vscode.window.showInformationMessage(L10n.t(ChatProviderText.NO_INSERT_POSITION));
                return {};
            }
            return {document, insertPosition: this.lastSelection.active};
        };
        const generateWholeFileDocstring = (document: vscode.TextDocument) => {
            // 有可能第一次点击采纳的部分还没来得及被插入就触发了下一次生成，导致原始内容不完整
            setTimeout(() => {
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => {
                        reject(
                            new Error(
                                'Generate whole file docstring timeout after 5s, the new conversation will be aborted'
                            )
                        );
                    }, 5000);
                });

                const fileDocstringPromise = generateFileComment({
                    content: document.getText(),
                    repo: params?.repo ?? '',
                    path: params?.path ?? '',
                    username: params?.username ?? '',
                });

                // 如果服务端 5s 内没有返回结果，则舍弃此次请求
                Promise
                    .race([timeoutPromise, fileDocstringPromise])
                    .then((fileDocstringRes: any) => {
                        if (
                            fileDocstringRes.success
                            && fileDocstringRes.data.content
                            && fileDocstringRes.data.generate_count > 0
                        ) {
                            this.diffProvider.generateFileDocstring(fileDocstringRes.data, document);
                        }
                    })
                    .catch(error => {
                        console.error(error);
                    });
            }, 100);
        };

        const createAcceptHandler =
            // eslint-disable-next-line complexity
            (document?: vscode.TextDocument, insertPosition?: vscode.Position) => async (content: string) => {
                this.logUserAction('accept');
                const {document: currentDocument, insertPosition: currentInsertPosition} = documentAndPosition();
                const actualDocument = document ?? currentDocument;
                const actualInsertPosition = insertPosition ?? currentInsertPosition;
                if (actualDocument && actualInsertPosition) {
                    if (iocContainer.get(DiffHighlightProvider).getLoadingRange(actualDocument.uri.fsPath)) {
                        iocContainer.get(ChatViewProvider).sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                            type: 'fail',
                            message: '当前文件正在修改，请等待执行完成后再进行采纳',
                        });
                        return;
                    }
                    const replaceAll = id === undefined
                        ? false
                        : await this.diffProvider.checkAndUpdateWithDiffContent(actualDocument, id);
                    if (!replaceAll) {
                        await this.diffProvider.acceptInsertion(actualDocument, actualInsertPosition, content);
                    }
                    this.diffProvider.closeDiffEditor();
                    const originContent = document?.getText() ?? '';
                    this.accept(
                        chatResponseProxy,
                        content,
                        {
                            originContent,
                            insertPosition,
                            acceptanceType: ACCEPTANCE_TYPE.INSERT,
                        },
                        actualDocument
                    );
                    if (sourceProvider === 'docstring' && isInternal) {
                        generateWholeFileDocstring(actualDocument);
                    }
                    if (!skipCodeRetention) {
                        const offset = actualDocument.offsetAt(actualInsertPosition);
                        const uuid = chatResponseProxy.getTrackUuid();
                        await postInsertionTasks(
                            iocContainer,
                            {
                                offset,
                                uri: actualDocument.uri,
                                displayText: content,
                                uuid: uuid ?? '',
                                telemetry: {} as any,
                            },
                            'chat'
                        );
                    }
                    return undefined;
                }
                else {
                    const chatView = iocContainer.get(ChatViewProvider);
                    chatView.sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                        type: 'info',
                        message: '未识别到编码区的光标位置，无法插入',
                    });
                    return {status: 'failed'};
                }
            };

        const accept = createAcceptHandler();
        const actions: Actions = {
            diff: async (content: string) => {
                this.logUserAction('diff');
                const {document, insertPosition} = documentAndPosition();
                if (document && insertPosition) {
                    id = await this.diffProvider.showInsertionDiff(
                        document,
                        insertPosition,
                        content,
                        createAcceptHandler(document, insertPosition)
                    );
                }
            },
            accept,
            smartApply: async (content, _, extra) => {
                this.logUserAction('smartApply');
                const {uniqKey} = extra;
                const activeEditor = vscode.window.activeTextEditor;
                if (activeEditor) {
                    const scheme = activeEditor.document.uri.scheme;
                    const isText = scheme === 'file' || scheme === 'untitled';
                    const text = activeEditor.document.getText();
                    // 空文件或者文件字符超长
                    if (isText && (!text || text.length > 50000)) {
                        accept(content);
                        const chatViewProvider = iocContainer.get(ChatViewProvider);
                        chatViewProvider.sendDataToWebview(EventMessage.SmartApplyUpdate, {
                            key: uniqKey,
                            status: SmartApplyStatus.UNTOUCHED,
                        });
                        return;
                    }
                }

                const uuid = chatResponseProxy.getTrackUuid();
                return vscode.commands.executeCommand(RegisteredCommand.smartApply, {uuid, content, key: uniqKey});
            },
            copy: async (content: string) => {
                this.logUserAction('copy');
                const {document} = documentAndPosition();
                vscode.env.clipboard.writeText(content);

                this.accept(
                    chatResponseProxy,
                    content,
                    {
                        insertPosition: vscode.window.activeTextEditor?.selection.active,
                        acceptanceType: ACCEPTANCE_TYPE.COPY,
                    },
                    document
                );
                if (sourceProvider === 'docstring' && document && isInternal) {
                    generateWholeFileDocstring(document);
                }
            },
        };
        const uuid = chatResponseProxy.getTrackUuid();
        const chatId = chatResponseProxy.getChatId();
        if (uuid && (isPoc || chatId)) {
            actions.feedback = async (options: FeedbackOptions) => {
                // 好心人，记得检查一下三方插件是否也需要变更
                // packages/vscode/src/services/ComatePlusChatSession/index.ts
                modifyCode({uuid: uuid, chatId: chatId, ...options});
            };
        }
        return this.enableSmartApply && !ignoreSmartApplyFeature
            ? actions
            : omit(actions, ['smartApply']);
    }

    private async replaceToFile(
        editor: vscode.TextEditor,
        document: vscode.TextDocument,
        from: string,
        to: string
    ): Promise<void> {
        const match = document.getText().indexOf(from);
        // Get the first match's range
        const matchRange = new vscode.Range(
            document.positionAt(match),
            document.positionAt(match + from.length)
        );
        // Create a workspace edit
        const edit = new vscode.WorkspaceEdit();
        edit.replace(document.uri, matchRange, to);

        // Apply the edit
        await vscode.workspace.applyEdit(edit);

        // Move the cursor to the replaced text
        const selectRange = new vscode.Range(
            document.positionAt(match),
            document.positionAt(match + to.length)
        );

        editor.selection = new vscode.Selection(selectRange.start, selectRange.end);
        editor.revealRange(selectRange);
    }

    private async getPathInput(path: string, metadata?: vscode.WorkspaceEditEntryMetadata) {
        if (metadata?.needsConfirmation) {
            const errorMessage = L10n.t(AcceptProviderText.PATH_ERROR);
            const textInputOptions: vscode.InputBoxOptions = {
                placeHolder: errorMessage,
                validateInput: (input: string) => {
                    return isValidUri(input) ? null : errorMessage;
                },
                value: path,
            };
            return vscode.window.showInputBox(textInputOptions);
        }
        else {
            return path;
        }
    }

    private async insertToFile(
        chatResponseProxy: ChatResponseProxy,
        path: string,
        position: vscode.Position,
        newText: string,
        metadata?: vscode.WorkspaceEditEntryMetadata
    ): Promise<void> {
        try {
            const inputPath = await this.getPathInput(path, metadata);
            if (inputPath) {
                const isExist = await isFileExist(inputPath);
                if (!isExist) {
                    writeFile(inputPath, '');
                }
                vscode
                    .workspace
                    .openTextDocument(inputPath)
                    .then(document => {
                        vscode
                            .window
                            .showTextDocument(document)
                            .then(editor => {
                                editor.edit(editBuilder => {
                                    editBuilder.insert(position, newText);
                                });
                            });
                    });
                this.accept(
                    chatResponseProxy,
                    newText,
                    {
                        insertPosition: position,
                        acceptanceType: ACCEPTANCE_TYPE.INSERTTOFILE,
                    }
                );
            }
        }
        catch (ex) {
            vscode.window.showInformationMessage((ex as Error).message);
        }
    }

    private async showFileInsertDiff(
        chatResponseProxy: ChatResponseProxy,
        path: string,
        position: vscode.Position,
        newText: string,
        metadata?: vscode.WorkspaceEditEntryMetadata
    ): Promise<void> {
        try {
            const inputPath = await this.getPathInput(path, metadata);
            if (inputPath) {
                const isExist = await isFileExist(inputPath);
                if (!isExist) {
                    writeFile(inputPath, '');
                }
                const document = await vscode.workspace.openTextDocument(inputPath);
                await this.diffProvider.showInsertionDiff(
                    document,
                    new vscode.Position(position.line, position.character),
                    newText,
                    () => this.insertToFile(chatResponseProxy, inputPath, position, newText)
                );
            }
        }
        catch (ex) {
            vscode.window.showInformationMessage((ex as Error).message);
        }
    }

    private async showFileReplaceDiff(
        chatResponseProxy: ChatResponseProxy,
        path: string,
        from: string,
        to: string,
        metadata?: vscode.WorkspaceEditEntryMetadata
    ): Promise<void> {
        try {
            const inputPath = await this.getPathInput(path, metadata);
            if (inputPath) {
                const isExist = await isFileExist(inputPath);
                if (!isExist) {
                    writeFile(inputPath, '');
                }
                const uri = await reviveFullUriToWorkspace(inputPath);
                if (!uri) {
                    return;
                }
                const editor = await vscode.window.showTextDocument(uri, {
                    preview: false,
                });
                const document = editor.document;
                const match = document.getText().indexOf(from);
                const matchRange = new vscode.Range(
                    document.positionAt(match),
                    document.positionAt(match + from.length)
                );

                await this.diffProvider.showReplacementDiff(
                    document,
                    matchRange,
                    to,
                    () => {
                        this.accept(
                            chatResponseProxy,
                            to,
                            {
                                insertPosition: vscode.window.activeTextEditor?.selection.active,
                                acceptanceType: ACCEPTANCE_TYPE.REPLACETOFILE,
                            }
                        );
                        return this.replaceToFile(editor, document, from, to);
                    }
                );
            }
        }
        catch (ex) {
            vscode.window.showInformationMessage((ex as Error).message);
        }
    }

    private async batchApplyEdits(chatResponseProxy: ChatResponseProxy, edits: Accept) {
        const workspaceEdit = new vscode.WorkspaceEdit();
        const succeedEdits = [];
        const failedEdits = [];
        const MAX_SIZE = 5 * 1024 * 1024; // 批量采纳最大文件大小为 5MB

        for (const edit of edits) {
            try {
                const uri = vscode.Uri.file(edit.path);
                const {size} = await vscode.workspace.fs.stat(uri);
                if (size > MAX_SIZE) {
                    throw new Error(`The file "${edit.path}" exceeds the maximum allowed size.`);
                }

                const isExist = await isFileExist(edit.path);
                if (edit.type === 'replace') {
                    if (!isExist) {
                        throw new Error(`The file "${edit.path}" does not exist.`);
                    }
                    const range = new vscode.Range(
                        new vscode.Position(edit.range.start.line, edit.range.start.character),
                        new vscode.Position(edit.range.end.line, edit.range.end.character)
                    );
                    workspaceEdit.replace(uri, range, edit.newText);
                }
                else if (edit.type === 'insert') {
                    if (isExist) {
                        const position = new vscode.Position(edit.position.line, edit.position.character);
                        workspaceEdit.insert(uri, position, edit.newText);
                    }
                    else {
                        writeFile(edit.path, new TextEncoder().encode(edit.newText));
                    }
                }
                succeedEdits.push({edit});
                this.accept(
                    chatResponseProxy,
                    edit.newText,
                    {
                        acceptanceType: ACCEPTANCE_TYPE.INSERTTOFILE,
                    }
                );
            }
            catch (error) {
                failedEdits.push({edit, error});
            }
        }

        await vscode.workspace.applyEdit(workspaceEdit);

        // 做次去重防止重复保存
        const filePathsToSave = new Set(succeedEdits.map(item => item.edit.path));
        const savePromises = Array.from(filePathsToSave).map(async filePath => {
            try {
                const uri = vscode.Uri.file(filePath);
                const {size} = await vscode.workspace.fs.stat(uri);
                if (size > MAX_SIZE) {
                    throw new Error(`The file "${filePath}" exceeds the maximum allowed size for saving.`);
                }

                const document = await vscode.workspace.openTextDocument(uri);
                if (document.isDirty) {
                    await document.save();
                }
            }
            catch (error) {
                const edit = edits.find(e => e.path === filePath);
                failedEdits.push({edit, error});
            }
        });

        await Promise.allSettled(savePromises);

        return {succeedEdits, failedEdits};
    }

    protected extraActions(chatResponseProxy: ChatResponseProxy) {
        const actions: Actions = {
            // 采纳到新文件
            newFile: async (content: string, language?: string, extra?: any) => {
                this.logUserAction('newFile');
                let isExtraSuccOpen = false;
                if (extra) {
                    const {filePath, metadata} = extra;
                    const inputPath = await this.getPathInput(filePath, metadata);
                    if (inputPath) {
                        const isEnsureSucc = await ensureFilePathExists(filePath);
                        if (isEnsureSucc) {
                            await writeFile(filePath, content);
                            const uri = await reviveFullUriToWorkspace(filePath);
                            if (uri) {
                                await vscode.window.showTextDocument(uri, {preview: true});
                                isExtraSuccOpen = true;
                            }
                        }
                    }
                }

                if (!isExtraSuccOpen) {
                    vscode
                        .workspace
                        .openTextDocument({content: content, language: language})
                        .then(doc => {
                            vscode.window.showTextDocument(doc);
                        });
                }

                this.accept(
                    chatResponseProxy,
                    content,
                    {
                        insertPosition: vscode.window.activeTextEditor?.selection.active,
                        acceptanceType: ACCEPTANCE_TYPE.NEWFILE,
                    }
                );
            },
            insertIntoTerminal: async (generatedContent: string) => {
                this.logUserAction('insertIntoTerminal');
                const terminal = vscode.window.activeTerminal ?? vscode.window.createTerminal();
                terminal.show();
                vscode.env.clipboard.writeText(generatedContent);
                await vscode.commands.executeCommand('workbench.action.terminal.focus');
                await vscode.commands.executeCommand('workbench.action.terminal.paste');

                this.accept(
                    chatResponseProxy,
                    generatedContent,
                    {
                        insertPosition: vscode.window.activeTextEditor?.selection.active,
                        acceptanceType: ACCEPTANCE_TYPE.INSERT,
                    }
                );
            },
            replaceToFile: async (_, __, extra) => {
                this.logUserAction('replaceToFile');
                const {filePath, from, to, replaceAll} = extra;
                const uri = await reviveFullUriToWorkspace(filePath);
                if (uri) {
                    const editor = await vscode.window.showTextDocument(uri, {
                        preview: false,
                    });
                    const document = editor.document;
                    const matchDetect = document.getText().indexOf(from);
                    if (matchDetect > 0) {
                        await this.replaceToFile(editor, document, from, to);

                        this.accept(
                            chatResponseProxy,
                            extra.to,
                            {
                                originContent: extra.from,
                                insertPosition: vscode.window.activeTextEditor?.selection.active,
                                acceptanceType: ACCEPTANCE_TYPE.REPLACETOFILE,
                            }
                        );

                        if (replaceAll) {
                            // eslint-disable-next-line
                            while (document.getText().indexOf(from) > 0) {
                                await this.replaceToFile(editor, document, from, to);

                                this.accept(
                                    chatResponseProxy,
                                    extra.to,
                                    {
                                        originContent: extra.from,
                                        insertPosition: vscode.window.activeTextEditor?.selection.active,
                                        acceptanceType: ACCEPTANCE_TYPE.REPLACETOFILE,
                                    }
                                );
                            }
                        }
                    }
                    else {
                        vscode.window.showInformationMessage('当前代码已经采纳或已经失效，请重试');
                    }
                }
                else {
                    vscode.window.showErrorMessage('Failed to open file');
                }
            },
            insertToFile: async (actionData, __, extra) => {
                this.logUserAction('insertToFile');
                const data = typeof actionData === 'string' ? extra : actionData;
                const {filePath, position, newText, metadata} = data;
                await this.insertToFile(chatResponseProxy, filePath, position, newText, metadata);
            },
            showFileInsertDiff: async (_, __, extra) => {
                this.logUserAction('showFileInsertDiff');
                const {filePath, position, newText, metadata} = extra;
                await this.showFileInsertDiff(chatResponseProxy, filePath, position, newText, metadata);
            },
            showFileReplaceDiff: async (_, __, extra) => {
                this.logUserAction('showFileReplaceDiff');
                const {filePath, from, to, metadata} = extra;
                await this.showFileReplaceDiff(chatResponseProxy, filePath, from, to, metadata);
            },
            batchAccept: async (edits: Accept) => {
                this.logUserAction('batchAccept');
                const result = await this.batchApplyEdits(chatResponseProxy, edits);
                return result;
            },
            // 采纳目录，仅用于command-button的action，目前服务于paddle和f2c场景，用于统计采纳会有特殊行为，具体联系zhangenming02
            acceptDir: async actionData => {
                this.logUserAction('acceptDir');
                const {from, to, newText, deleteIfExists} = actionData;
                this.accept(chatResponseProxy, newText, {
                    acceptanceType: ACCEPTANCE_TYPE.NEWFILE,
                });
                const fromUri = vscode.Uri.file(from);
                const toUri = vscode.Uri.file(to);
                if (deleteIfExists) {
                    // 目录/文件已经存在时 复制之前删除整个目录/文件，主要保证目录里没有多余内容。
                    await vscode.workspace.fs.delete(toUri, {recursive: true});
                    await vscode.workspace.fs.createDirectory(toUri);
                }
                await vscode.workspace.fs.copy(fromUri, toUri, {overwrite: true});
            },
            // 纯用于采纳依赖，目前服务于xiaomi-aiot场景，当前不统计，具体联系tianyanbo
            acceptDependentFiles: async (content: string) => {
                this.logUserAction('acceptDependentFiles');
                const whatToAccept = JSON.parse(content) as Pick<AcceptWithDependentFiles, 'dependencies'>;
                await Promise.allSettled(whatToAccept.dependencies.map(copyAction => {
                    const fromUri = vscode.Uri.file(copyAction.fromPath);
                    const toUri = vscode.Uri.file(copyAction.toPath);
                    return vscode.workspace.fs.copy(fromUri, toUri);
                }));
                const chatView = iocContainer.get(ChatViewProvider);
                chatView.sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                    type: 'info',
                    message: '依赖文件已成功采纳',
                });
            },
            // 采纳文件和依赖
            acceptWithDependentFiles: async (content: string) => {
                this.logUserAction('acceptWithDependentFiles');
                const whatToAccept = JSON.parse(content) as AcceptWithDependentFiles;
                const entryFileUri = vscode.Uri.file(whatToAccept.entry.filePath);
                // TODO: 这里最好判断下是否本地有这个文件，如果有的话提醒是否要覆盖？
                await vscode.workspace.fs.writeFile(
                    entryFileUri,
                    Buffer.from(whatToAccept.entry.content)
                );
                await Promise.allSettled(whatToAccept.dependencies.map(copyAction => {
                    const fromUri = vscode.Uri.file(copyAction.fromPath);
                    const toUri = vscode.Uri.file(copyAction.toPath);
                    return vscode.workspace.fs.copy(fromUri, toUri);
                }));
                vscode.window.showTextDocument(entryFileUri);
                this.accept(
                    chatResponseProxy,
                    whatToAccept.entry.content,
                    {
                        acceptanceType: ACCEPTANCE_TYPE.INSERTTOFILE,
                    }
                );
            },
            viewFile: async (_, __, extra) => {
                this.logUserAction('viewFile');
                const {filePath} = extra;
                const uri = await reviveFullUriToWorkspace(filePath);
                if (uri) {
                    await vscode.window.showTextDocument(uri, {preview: true});
                }
            },
        };
        const uuid = chatResponseProxy.getTrackUuid();
        const chatId = chatResponseProxy.getChatId();
        if (uuid && (isPoc || chatId)) {
            actions.feedback = async (options: FeedbackOptions) => {
                modifyCode({uuid: uuid, chatId: chatId, ...options});
            };
        }
        return actions;
    }

    private accept(
        chatResponseProxy: ChatResponseProxy,
        generatedContent: string,
        context: AcceptanceContext,
        document?: vscode.TextDocument
    ) {
        const {originContent, insertPosition, acceptanceType} = context;
        const uuid = chatResponseProxy.getTrackUuid();
        // getResponseType 为了区分uuid是否通过异步获取，目前只有开放平台uuid是通过异步获取
        const getResponseType = chatResponseProxy?.getResponseType ?? 'normal';
        if (uuid && getResponseType === 'normal') {
            chatActionDelayedReporter.add(uuid, async () => {
                acceptCode({
                    accepted: true,
                    content: '',
                    uuid,
                    generatedContent,
                    acceptanceInfo: {
                        originContent: originContent ?? '',
                        row: insertPosition?.line ?? 0,
                        col: insertPosition?.character ?? 0,
                        acceptanceContent: generatedContent,
                        acceptanceType: acceptanceType ?? ACCEPTANCE_TYPE.INSERT,
                    },
                });
                iocContainer.get<ITimeTracker>(TYPES.ITimeTracker).recordChatEnd(uuid, generatedContent);
                document && captureActualCodeAfterGeneration(document.uri, uuid);
            });
        }
        else {
            chatActionDelayedReporter.add(chatResponseProxy.getMessageId(), () => {
                this.track(
                    chatResponseProxy,
                    generatedContent,
                    {
                        originContent,
                        insertPosition,
                        acceptanceType,
                    }
                );
                if (uuid && document) {
                    captureActualCodeAfterGeneration(document.uri, uuid);
                }
            });
        }
    }

    // eslint-disable-next-line
    private async track(chatResponseProxy: ChatResponseProxy, acceptedContent: string, context: AcceptanceContext) {
        const {originContent, insertPosition, acceptanceType} = context;
        const uuid = chatResponseProxy.getTrackUuid();
        if (!uuid) {
            return;
        }
        try {
            const codeBlocks = parseMarkdownCodeBlocks(chatResponseProxy.getMessageContent() ?? '');
            // 返回代码块在 markdown 中的索引
            const index = codeBlocks.findIndex(v => v.includes(acceptedContent));
            // jsx 当前找不到codeblock的index，符合预期，需要通过下面方式直接采纳 hack下
            if (index === -1) {
                await acceptCode({
                    uuid,
                    accepted: true,
                    content: '',
                    // acceptedBlocks: index.toString(),
                    generatedContent: acceptedContent,
                    acceptanceInfo: {
                        originContent: originContent ?? '',
                        row: insertPosition?.line ?? 0,
                        col: insertPosition?.character ?? 0,
                        acceptanceContent: acceptedContent,
                        acceptanceType: acceptanceType ?? ACCEPTANCE_TYPE.INSERT,
                    },
                });
            }
            else {
                await acceptCode({
                    uuid,
                    accepted: true,
                    content: '',
                    acceptedBlocks: index.toString(),
                    // generatedContent:
                    acceptanceInfo: {
                        originContent: originContent ?? '',
                        row: insertPosition?.line ?? 0,
                        col: insertPosition?.character ?? 0,
                        acceptanceContent: acceptedContent,
                        acceptanceType: acceptanceType ?? ACCEPTANCE_TYPE.INSERT,
                    },
                });
            }
        }
        catch {
            // nothing to do
        }
    }
}
