/* eslint-disable camelcase */
import {mkdir, writeFile} from 'node:fs/promises';
import path from 'node:path';
import {dirname} from 'path';
import * as vscode from 'vscode';
import Parser from 'web-tree-sitter';
import {L10n} from '@/common/L10nProvider/L10n';
import {UnitTestProviderText} from '@/common/L10nProvider/constants';
import {inferLanguage} from '@/utils/languages';
import {
    findPreviousSiblings,
    getNameFromCppFunctionDefinition,
    hasMatchingAncestor,
    isJSNodeInTopLevel,
    TreeSitterLanguage,
} from '../../utils/treeSitterUtils';
import {isFileExist} from '../../utils/fs';
import {AcceptResult, UnitTestItem} from '../../api/unitTest';
import {error} from '../../common/outputChannel';
import {getFileExtname, isFileTooLarge} from '../TreeSitterProvider';
import {resolveAccept} from './handler';

const jsSuffix = [
    '.ts',
    '.js',
    '.tsx',
    '.jsx',
    '.javascript',
    '._js',
    '.es',
    '.es6',
    '.gs',
    '.jake',
    '.jslib',
    '.jsm',
    '.jss',
    '.mjs',
    '.njs',
    '.sjs',
];

// 服务端是根据文件后缀来判断的，这里得保持一致，要不然就会生成失败
export function isFileTestable(document: vscode.TextDocument) {
    const suffix = getFileExtname(document);
    const fileName = document.fileName;
    if (suffix === '.java') {
        // 测试文件正常是这个路径，但也可以改，不在这个路径下的测试文件后面还有 @Test 注解检测
        // 能过滤大部分情况了，其他情况有用户反馈再优化
        return !fileName.includes('/src/test/java/');
    }
    if (suffix === '.go') {
        // 后面再优化吧
        return !fileName.endsWith('_test.go');
    }
    if (suffix && ['.cpp', '.hpp', '.cc', '.c++', '.c', '.h', '.cxx'].includes(suffix)) {
        return true;
    }
    if (suffix && jsSuffix.includes(suffix)) {
        return true;
    }
    if (suffix === '.py') {
        return !fileName.startsWith('test_') && !fileName.endsWith('_test.py');
    }
    return false;
}

export function isTestable(document: vscode.TextDocument) {
    if (isFileTooLarge(document)) {
        return false;
    }
    return isFileTestable(document);
}

export function isCppConstructorOrDestructor(name: string) {
    const fields = name.split('::');
    if (fields.length >= 2) {
        const [nameSpace, functionName] = fields.slice(-2);
        return nameSpace === functionName || `~${nameSpace}` === functionName;
    }
    return false;
}

// eslint-disable-next-line complexity
export function isCppTestableFunction(node: Parser.SyntaxNode) {
    const parent = node.parent;
    // 内部函数
    if (
        parent
        && parent.type === 'compound_statement'
        && parent.parent
        && parent.parent.type === 'function_definition'
    ) {
        return false;
    }

    // 私有方法
    if (
        parent
        && parent.type === 'field_declaration_list'
        && parent.parent
        && parent.parent.type === 'class_specifier'
    ) {
        const accessSpecifier = findPreviousSiblings(node, node => node.type === 'access_specifier');
        const isPublic = accessSpecifier && accessSpecifier.text === 'public:';
        const functionName = getNameFromCppFunctionDefinition(node);
        const className = parent.parent.childForFieldName('name')?.text;

        const isConstruct = className === functionName
            || functionName === `~${className}`
            || className?.endsWith(`::${functionName}`)
            || (functionName?.startsWith('~') && className?.endsWith(`::${functionName.slice(1)}`));
        return isPublic && !isConstruct;
    }

    const name = getNameFromCppFunctionDefinition(node);
    return name && !['TEST', 'TEST_F', 'main'].includes(name) && !isCppConstructorOrDestructor(name);
}

export function isPythonTestableFunction(node: Parser.SyntaxNode) {
    const parent = node.parent;
    if (!parent) {
        return false;
    }
    const isNestedFunction = hasMatchingAncestor(node, ancestor => ancestor.type === 'function_definition');
    if (isNestedFunction) {
        return false;
    }
    const grandparent = parent.parent;
    if (grandparent && grandparent.type === 'class_definition') {
        if (grandparent.childForFieldName('name')?.text.startsWith('Test')) {
            return false;
        }
        if (grandparent.childForFieldName('superclasses')?.text.includes('unittest.TestCase')) {
            return false;
        }
    }
    const functionName = node.childForFieldName('name')?.text;
    if (functionName?.startsWith('test_')) {
        return false;
    }
    if (functionName?.startsWith('_')) {
        return false;
    }
    return true;
}

// eslint-disable-next-line complexity
export function isTestableFunction(languageId: string, node: Parser.SyntaxNode) {
    if (languageId === 'java') {
        const firstNamedChild = node.firstNamedChild;
        if (!firstNamedChild || firstNamedChild.type !== 'modifiers') {
            return false;
        }
        const children = firstNamedChild.children ?? [];
        const isTestFunction = children.some(child => child.text.startsWith('@Test'));
        const isPublicFunction = children.some(child => child.text === 'public');
        return !isTestFunction && isPublicFunction;
    }
    else if (
        languageId === TreeSitterLanguage.JavaScript
        || languageId === TreeSitterLanguage.TypeScript
        || languageId === TreeSitterLanguage.Tsx
    ) {
        return isJSNodeInTopLevel(node);
    }
    else if (languageId === TreeSitterLanguage.Go) {
        return true;
    }
    else if (languageId === 'cpp' || languageId === 'c') {
        return isCppTestableFunction(node);
    }
    else if (languageId === 'python') {
        return isPythonTestableFunction(node);
    }
    return false;
}

export async function openFile(filePath: string, column?: vscode.ViewColumn) {
    try {
        const document = await vscode.workspace.openTextDocument(filePath);
        if (document) {
            // TODO：不确定能否只调一次 showTextDocument 传入 uri
            vscode.window.showTextDocument(document, column);
        }
    }
    catch (e) {
        // noop
    }
}

export async function writeUnitTestResult(data: AcceptResult, callback: () => Promise<void>) {
    try {
        const folder = dirname(data.ut_file_path);
        const isFolderExist = await isFileExist(folder);
        if (!isFolderExist) {
            await mkdir(folder, {recursive: true});
        }
        await writeFile(data.ut_file_path, data.ut_file_content, 'utf-8');
        callback();
    }
    catch (e: any) {
        error('write unit test result failed, ', e.message ?? 'unknown error');
    }
}

export async function acceptUnitTest(unitTest: UnitTestItem, callback: () => Promise<void>) {
    const res = await resolveAccept(unitTest);
    if (res.result === 'SUCCESS' && res.data) {
        await writeUnitTestResult(res.data, callback);
        openFile(res.data.ut_file_path, vscode.ViewColumn.Beside);
    }
    else {
        vscode.window.showErrorMessage(
            `${L10n.t(UnitTestProviderText.ACCEPT_ERROR)} ${res.msg.slice(0, 120)}`
        );
    }
}

export const FRAMEWORK_DISPLAY_NAME_MAP: Record<string, string> = {
    junit4: 'JUnit4',
    junit5: 'JUnit5',
    auto: 'Auto',
    mockito: 'Mockito',
    jmockit: 'JMockit',
    gotests: 'gotests',
    gomock: 'GoMock',
    monkey: 'Monkey',
    sqlmock: 'Sqlmock',
    httptest: 'httptest',
    gtest: 'GooleTest',
    gmock: 'Gmock',
    jest: 'Jest',
    mocha: 'Mocha',
};

function mapUtFilePath(srcFilepath: string, language: string) {
    const sep = path.sep;
    const dotIndex = srcFilepath.lastIndexOf('.');
    const sepIndex = srcFilepath.lastIndexOf(sep);
    const jsPathWithTestSuffix = srcFilepath.slice(0, dotIndex) + '.test' + srcFilepath.slice(dotIndex);
    const jsPath = jsPathWithTestSuffix.slice(0, sepIndex) + `${sep}__tests__` + jsPathWithTestSuffix.slice(sepIndex);
    switch (language) {
        case 'go':
            return srcFilepath.split('.go')[0] + '_test.go';
        case 'java':
            return srcFilepath.replace(`${sep}main${sep}`, `${sep}test${sep}`).split('java')[0] + 'Test.java';
        case 'cpp':
            return srcFilepath.slice(0, dotIndex) + '_test' + srcFilepath.slice(dotIndex);
        case 'js':
            return jsPath;
        case 'python':
            return srcFilepath.slice(0, sepIndex + 1) + 'test_' + srcFilepath.slice(sepIndex + 1);
        default:
            return '';
    }
}

export async function mapUtFile(srcFilepath: string) {
    const language = inferLanguage(srcFilepath);
    if (!language) {
        return undefined;
    }
    try {
        const utFilePath = mapUtFilePath(srcFilepath, language);
        const utFileContent = (await vscode.workspace.openTextDocument(vscode.Uri.file(utFilePath))).getText();
        return {exist_ut_file_path: utFilePath ?? '', exist_ut_file_content: utFileContent ?? ''};
    }
    catch (err) {
        return undefined;
    }
}
