import * as vscode from 'vscode';
import {Position} from 'vscode-languageserver-types';
import {inject, injectable} from 'inversify';
import {TYPES} from '@/inversify.config';
import {inferLanguage} from '@/utils/languages';
import {GenerateUnitTestOptions} from '@/api/unitTest';
import {ILocationLinkResolver, ISymbolUsageFinder} from '../CrossFileContextAnalyzer/types';
import {SymbolSignatureController} from '../CrossFileContextAnalyzer/SymbolSignatureReader';

const GO_IMPORT_PATH_REGEX = /"(.+)"/;

const GO_BASIC_TYPES = new Set([
    'bool',
    'string',
    'int',
    'int8',
    'int16',
    'int32',
    'int64',
    'uint',
    'uint8',
    'uint16',
    'uint32',
    'uint64',
    'uintptr',
    'byte',
    'rune',
    'float32',
    'float64',
    'complex64',
    'complex128',
    'any',
    'error',
]);

@injectable()
export class UTContextAnalyzer {
    constructor(
        @inject(TYPES.ISymbolUsageFinder) private readonly symbolUsageFinder: ISymbolUsageFinder,
        @inject(TYPES.ILocationLinkResolver) private readonly locationLinkResolver: ILocationLinkResolver,
        @inject(SymbolSignatureController) private readonly symbolSignatureController: SymbolSignatureController
    ) {}

    async extractFunctionContext(document: vscode.TextDocument, position: vscode.Position) {
        const currentFilePath = document.uri.fsPath;
        const language = inferLanguage(currentFilePath);
        if (language === 'go') {
            return this.extractGoFunctionContext(document, position);
        }
        return undefined;
    }

    // eslint-disable-next-line complexity
    private async extractGoFunctionContext(
        document: vscode.TextDocument,
        position: vscode.Position
    ): Promise<GenerateUnitTestOptions['param_struct']> {
        const currentFilePath = document.uri.fsPath;
        const symbolUsage = await this.symbolUsageFinder.findOnFunction(
            currentFilePath,
            Position.create(position.line, position.character)
        );
        const crossFileUsages = symbolUsage.filter(item => {
            if (item.language === 'go') {
                const isBasicType = GO_BASIC_TYPES.has(item.typeIdentifier.name) && !item.packageIdentifier;
                return !isBasicType;
            }
            return false;
        });
        const receiverStruct = [];
        const paramStruct = [];
        const returnStruct = [];
        for (const usage of crossFileUsages) {
            if (usage.language === 'go') {
                const typeIdentifierDefLocation = await this.locationLinkResolver.resolveTypeDefinition(
                    currentFilePath,
                    usage.typeIdentifier.range
                );
                // eslint-disable-next-line max-depth
                if (!typeIdentifierDefLocation || typeIdentifierDefLocation.uri === currentFilePath) {
                    continue;
                }
                const typeSiganture = await this.symbolSignatureController.getSignature(
                    typeIdentifierDefLocation.uri,
                    typeIdentifierDefLocation.range
                );
                if (!typeSiganture) {
                    continue;
                }
                let packageName = '';
                const packageImportLocation = usage.packageIdentifier
                    ? await this.locationLinkResolver.resolveDefinition(
                        currentFilePath,
                        usage.packageIdentifier.range
                    )
                    : undefined;
                if (packageImportLocation && packageImportLocation.uri === currentFilePath) {
                    const importLine = document.lineAt(packageImportLocation.range.start.line).text;
                    const match = GO_IMPORT_PATH_REGEX.exec(importLine);
                    packageName = match ? match[1] : '';
                }
                const info = {
                    structInfo: typeSiganture.content,
                    packageName,
                };
                if (usage.kind === 'receiverType') {
                    receiverStruct.push(info);
                }
                if (usage.kind === 'parameterType') {
                    paramStruct.push(info);
                }
                if (usage.kind === 'returnType') {
                    returnStruct.push(info);
                }
            }
        }
        return {
            receiverStruct,
            paramStruct,
            returnStruct,
        };
    }
}
