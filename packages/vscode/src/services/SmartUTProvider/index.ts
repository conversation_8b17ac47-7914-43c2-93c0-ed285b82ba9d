/* eslint-disable camelcase */
import * as vscode from 'vscode';
import axios from 'axios';
import {injectable, inject} from 'inversify';
import Parser from 'web-tree-sitter';
import {Actions, FeedbackOptions} from '@shared/protocols';
import {formatPrompt, mergePluginConfig, localPluginConfig} from '@comate/plugin-shared-internals';
import {isPoc} from '@/utils/features';
import {TYPES} from '@/inversify.config';
import {L10n, getGenerationFailureText} from '@/common/L10nProvider/L10n';
import {GlobalText, UnitTestProviderText} from '@/common/L10nProvider/constants';
import consoleLogger from '@/common/consoleLogger';
import {timeout} from '@/utils/promise';
import {COMATE_TEST_SCHEMA} from '../../constants';
import {checkTreeSitterSupport, extractFunctionNodes} from '../../utils/treeSitterUtils';
import {TreeSitterProvider} from '../TreeSitterProvider';
import {CodeLensDisplayMode, CodelensConfig, ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';
import {iocContainer} from '../../iocContainer';
import {ChatViewProvider} from '../ChatViewProvider';
import {UserService} from '../UserService';
import {ACCEPTANCE_TYPE, acceptCode, generateTrackUuid, modifyCode} from '../../api';
import {UnitTestItem, UnitTestMetaInfo} from '../../api/unitTest';
import {stripExtraIndent} from '../../utils/indent';
import {addMarkdownCodeBlock} from '../../utils/common';
import {getCompleteFirstLine} from '../../utils/document';
import {buildParams} from '../../common/Fetcher';
import {Conversation} from '../ChatViewProvider/Conversation';
import {ITimeTracker} from '../TimeTracker/types';
import {ILicenseController} from '../LicenseController/types';
import {CodeLensProvider} from '../FoldedCodeLensProvider';
import {TextResponse} from '../ChatViewProvider/TextResponse';
import {PerformanceLogProvider} from '../PerformanceLogProvider';
import {DiffProvider} from '../DiffProvider';
import {TemporaryCodeLensProvider} from '../TemporaryCodeLensProvider';
import {CMD_GENERATE_UNIT_TEST, CMD_CREATE_UNIT_TEST, CTXEnableUnitTest} from './constants';
import {
    GenerateContext,
    fetchUnitTest,
    getUnitTestConfig,
    generateUnitTestByStreamApi,
} from './handler';
import {
    acceptUnitTest,
    FRAMEWORK_DISPLAY_NAME_MAP,
    isFileTestable,
    isTestable,
    isTestableFunction,
    mapUtFile,
} from './utils';
import {UTContextAnalyzer} from './context';

async function generateUUID(
    document: vscode.TextDocument,
    context: GenerateContext,
    content: string,
    shown: boolean,
    unitTestConfig?: UnitTestMetaInfo,
    featureTag?: string
): Promise<{
    uuid: string | undefined;
    chatId: string | undefined;
}> {
    try {
        const userService = iocContainer.get(UserService);
        const position = new vscode.Position(context.startLine, 0);
        const paramsResult = await buildParams(document, position, userService);
        if (paramsResult.type !== 'success') {
            return {uuid: undefined, chatId: undefined};
        }
        const res = await generateTrackUuid({
            ...paramsResult.value,
            model: 'SMARTUT',
            multiline: true,
            generatedContent: content,
            shown,
            utModelType: unitTestConfig?.prompt_lab?.prompt ? 'prompt_lab' : '',
            utFeatureTag: featureTag,
        });
        if (res.data.status !== 'OK') {
            return {uuid: undefined, chatId: undefined};
        }
        return {
            uuid: res.data.data?.uuid,
            chatId: res.data.data?.chatId,
        };
    }
    catch (e: any) {
        return {uuid: undefined, chatId: undefined};
    }
}

/**
 * 检查单测输出结果的Markdown格式，如果没有code格式，就将整个结果包一层code格式
 * 单测返回结果有可能有其他解释，没有其他解释就认为都是代码
 *
 * @param content 需要检查的内容
 * @returns
 */
function addCodeBlockFormat(content: string, language: string) {
    const lines = content.split('\n');
    const hasCodeBlock = lines.some(line => line.trim().startsWith('```'));
    return hasCodeBlock ? content : `\`\`\`${language}\n${content}\n\`\`\``;
}

@injectable()
export class SmartUTProvider implements CodeLensProvider, vscode.Disposable {
    private disposables: vscode.Disposable[] = [];

    constructor(
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(TreeSitterProvider) private readonly treeSitterProvider: TreeSitterProvider,
        @inject(ChatViewProvider) private readonly chatViewProvider: ChatViewProvider,
        @inject(UserService) private readonly userService: UserService,
        @inject(TYPES.ITimeTracker) private readonly timeTracker: ITimeTracker,
        @inject(TYPES.ILicenseController) private readonly licenseController: ILicenseController,
        @inject(PerformanceLogProvider) private readonly performanceLog: PerformanceLogProvider,
        @inject(DiffProvider) readonly diffProvider: DiffProvider,
        @inject(UTContextAnalyzer) private readonly utContextAnalyzer: UTContextAnalyzer,
        @inject(TemporaryCodeLensProvider) private readonly tmpCodeLensProvider: TemporaryCodeLensProvider
    ) {
        this.disposables.push(
            vscode.commands.registerCommand(
                CMD_CREATE_UNIT_TEST,
                () => {
                    return this.createUnitTest();
                }
            ),
            vscode.window.onDidChangeActiveTextEditor((editor?: vscode.TextEditor) => {
                const isDocumentTestable = editor && isFileTestable(editor.document);
                vscode.commands.executeCommand('setContext', CTXEnableUnitTest, isDocumentTestable ?? false);
            }),
            vscode.languages.registerCodeLensProvider({scheme: 'file'}, this),
            vscode.commands.registerCommand(
                CMD_GENERATE_UNIT_TEST,
                async (document: vscode.TextDocument, range: vscode.Range) => {
                    vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_GENERATE_UNIT_TEST});
                    const startTime = performance.now();
                    const res = await this.generate(document, range);
                    if (res?.uuid) {
                        this.performanceLog.log({
                            plugin: 'comate',
                            skill: 'unitTest',
                            duration: performance.now() - startTime,
                            uuid: res.uuid,
                            type: 'all',
                        });
                    }
                    return res;
                }
            )
        );
        const isDocumentTestable = vscode.window.activeTextEditor
            ? isFileTestable(vscode.window.activeTextEditor.document)
            : false;
        vscode.commands.executeCommand('setContext', CTXEnableUnitTest, isDocumentTestable ?? false);
    }

    async provideCodeLenses(document: vscode.TextDocument) {
        const codelensDisplayMode = this.configProvider.getCodeLensDisplayMode();
        if (codelensDisplayMode !== CodeLensDisplayMode.TextTitle) {
            return [];
        }
        if (this.diffProvider.isInDiff(document.uri.fsPath)) {
            return [];
        }
        const codeLenses = await this.computeCodeLenses(document);
        return this.tmpCodeLensProvider.filterOverlapCodeLenses(document, codeLenses);
    }

    async computeCodeLenses(document: vscode.TextDocument): Promise<vscode.CodeLens[]> {
        const codelensConfig = this.configProvider.getConfig<CodelensConfig>(ConfigKey.EnableCodelens);
        if (codelensConfig?.enableInlineUnitTest !== true || !this.licenseController.hasLicense) {
            return [];
        }
        // TODO: file 会包括 COMATE_TEST_SCHEMA
        if (document.uri.scheme === COMATE_TEST_SCHEMA) {
            return [];
        }
        if (!isTestable(document)) {
            return [];
        }
        try {
            const treeSitterLanguage = checkTreeSitterSupport(document.languageId);
            if (!treeSitterLanguage) {
                return [];
            }
            const tree = this.treeSitterProvider.getDocumentTree(document);
            if (!tree) {
                return [];
            }

            const nodes = extractFunctionNodes(treeSitterLanguage, tree.rootNode)
                .filter(node => node.startPosition.row !== node.endPosition.row) // 确保函数整体大于一行
                .filter(node => {
                    return isTestableFunction(treeSitterLanguage, node);
                });
            return this.getCodelens(nodes, document);
        }
        catch (e) {
            return [];
        }
    }

    private getCodelens(nodes: Parser.SyntaxNode[], document: vscode.TextDocument) {
        const codeLenses = nodes.map(node => {
            const {startPosition, endPosition} = node;
            const start = new vscode.Position(startPosition.row, startPosition.column);
            const end = new vscode.Position(endPosition.row, endPosition.column);
            const range = new vscode.Range(start, end);

            const codeLens = new vscode.CodeLens(
                new vscode.Range(
                    new vscode.Position(startPosition.row, 0),
                    new vscode.Position(endPosition.row, 0)
                ),
                {
                    title: L10n.t(UnitTestProviderText.CODELENS_TITLE),
                    tooltip: L10n.t(UnitTestProviderText.CODELENS_TOOLTIP),
                    command: CMD_GENERATE_UNIT_TEST,
                    arguments: [document, range],
                }
            );
            return codeLens;
        });

        return codeLenses;
    }

    private async createUnitTest() {
        const startTime = performance.now();
        const document = vscode.window.activeTextEditor?.document;
        if (!document || !isTestable(document)) {
            return;
        }
        const selection = vscode.window.activeTextEditor?.selection;
        if (!selection || !selection.start || !selection.end) {
            return;
        }
        vscode.commands.executeCommand('baidu.comate.showChatPanel', {source: CMD_CREATE_UNIT_TEST});
        const res = await this.generate(document, new vscode.Range(selection.start, selection.end), true);
        if (res?.uuid) {
            this.performanceLog.log({
                plugin: 'comate',
                skill: 'unitTest',
                duration: performance.now() - startTime,
                uuid: res.uuid,
                type: 'all',
            });
        }
    }

    async pluginConfig() {
        const workspace = vscode.workspace.workspaceFolders;
        const pluginConfigs = this.chatViewProvider.pluginConfigs;
        // @ts-ignore
        const localConfig = await localPluginConfig(workspace);
        const {config} = mergePluginConfig(pluginConfigs['comate'], localConfig);
        return config || {};
    }

    displayPrompt(prompt: string, unitTestConfig: UnitTestMetaInfo) {
        return formatPrompt(prompt, {
            focalMethod: '',
            focalFile: '',
            utFramework: unitTestConfig.test_framework,
            mockFramework: unitTestConfig.mock_framework,
        });
    }

    private async resolveParams(
        document: vscode.TextDocument,
        range: vscode.Range,
        isSelection: boolean,
        unitTestConfig: UnitTestMetaInfo
    ) {
        const functionContent = document.getText(range);
        let pluginConfig = {
            unitTestPrompt: '',
        };

        let query = L10n.t(
            UnitTestProviderText.PROMPT,
            isSelection ? L10n.t(GlobalText.COMMON_CODE) : L10n.t(GlobalText.COMMON_FUNCTION),
            this.getOptionText(unitTestConfig)
        );

        const firstLine = getCompleteFirstLine(document, range);
        let code = addMarkdownCodeBlock(
            stripExtraIndent(functionContent, !isSelection, firstLine),
            document.languageId
        );

        // 开关没开启或语言是英文时，走默认逻辑
        if (L10n.isEnglish) {
            return {query, code, pluginConfig};
        }
        try {
            pluginConfig = await this.pluginConfig();
            let prompt = pluginConfig.unitTestPrompt;
            if (pluginConfig.unitTestPrompt) {
                prompt = prompt.replace(
                    '{{utFramework}}{{mockFramework}}',
                    '{{utFramework}}、{{mockFramework}}'
                );
                prompt = prompt.replace(
                    '{{mockFramework}}{{utFramework}}',
                    '{{mockFramework}}、{{utFramework}}'
                );
            }
            query = this.displayPrompt(prompt, unitTestConfig);
            if (prompt.includes('{{focalFile}}') && !prompt.includes('{{focalMethod}}')) {
                code = addMarkdownCodeBlock(
                    vscode.window.activeTextEditor?.document.getText() ?? '',
                    document.languageId
                );
            }
            return {query, code, pluginConfig};
        }
        catch {
            return {query, code, pluginConfig};
        }
    }

    private async generate(document: vscode.TextDocument, range: vscode.Range, isSelection = false) {
        const {start, end} = range;
        const dependencyPromise = timeout(
            this.utContextAnalyzer.extractFunctionContext(document, range.start),
            3 * 1000
        );
        const unitTestConfig = await getUnitTestConfig(document, this.configProvider, this.userService);
        const {query, code, pluginConfig} = await this.resolveParams(
            document,
            range,
            isSelection,
            unitTestConfig
        );
        const conversation = this.chatViewProvider.createConversation(
            query,
            'unitTest',
            code
        );
        const context = {startLine: start.line + 1, endLine: end.line + 1, dependencyPromise};
        return this.processResult(document, context, conversation, unitTestConfig, performance.now(), pluginConfig);
    }

    async processResult(
        document: vscode.TextDocument,
        context: GenerateContext,
        conversation: Conversation,
        unitTestConfig: UnitTestMetaInfo,
        startTime: number,
        pluginConfig: {unitTestPrompt: string, unitTestAddContext?: boolean}
    ) {
        const streamMode = this.configProvider.getConfig(ConfigKey.EnableStreamingSession);
        const reply: TextResponse = conversation.addResponse(streamMode ? 'stream' : 'text', '', 'inProgress', {
            regenerate: async () => {
                this.processResult(document, context, conversation, unitTestConfig, startTime, pluginConfig);
            },
            // 打开全文复制按钮
            copyAll: () => {},
        });

        // 语言为英文时屏蔽开放平台能力
        if (!L10n.isEnglish) {
            unitTestConfig.prompt_lab = {
                prompt: pluginConfig.unitTestPrompt,
                add_context: Boolean(pluginConfig.unitTestAddContext) ?? true,
            };
        }
        try {
            if (streamMode) {
                return this.fetchAndStream(document, context, reply, unitTestConfig, startTime);
            }
            else {
                return this.fetchWithConfig(document, context, reply, unitTestConfig);
            }
        }
        catch (e: any) {
            reply.fail(getGenerationFailureText(e.message));
            return {uuid: ''};
        }
    }

    private async fetchWithConfig(
        document: vscode.TextDocument,
        context: GenerateContext,
        reply: TextResponse,
        unitTestConfig: UnitTestMetaInfo
    ) {
        const result = await fetchUnitTest(document, context, unitTestConfig);
        if (result.result === 'SUCCESS' && result.data) {
            const content = this.getDisplayText(document, result.data);
            const caseInfo = result.data.case_info;
            const featureTag = caseInfo.feature_tag;
            // 生成本次单测的 uuid
            const {uuid, chatId} = await generateUUID(document, context, content, true, unitTestConfig, featureTag);
            reply.success(
                addCodeBlockFormat(content, caseInfo?.language ?? document.languageId),
                this.getActions(uuid, result.data, chatId, content),
                uuid
            );
            return {uuid};
        }
        // 打点
        generateUUID(document, context, '', false, unitTestConfig);
        reply.fail(getGenerationFailureText(result.msg));
        return {uuid: ''};
    }

    private async fetchAndStream(
        document: vscode.TextDocument,
        context: GenerateContext,
        reply: TextResponse,
        unitTestConfig: UnitTestMetaInfo,
        startTime: number
    ) {
        const fileName = document.fileName;
        const fileContent = document.getText();
        const {startLine, endLine, dependencyPromise} = context;
        const utFile = await mapUtFile(document.uri.fsPath);
        const param_struct = await dependencyPromise
            ?.then(result => {
                consoleLogger.debug('(ut-context)(success)', result);
                return result;
            })
            .catch(e => {
                consoleLogger.debug('(ut-context)(fail)', e);
                return undefined;
            });
        const params = {
            src_file_content: fileContent,
            src_file_path: fileName,
            start_line: startLine,
            end_line: endLine,
            exist_ut_file: utFile,
            meta: unitTestConfig,
            param_struct,
        };
        const axiosTokenSource = axios.CancelToken.source();
        reply.message.cancelTokenSource?.token.onCancellationRequested(() => axiosTokenSource.cancel());
        this.timeTracker.recordChatStart(reply.message.replyTo ?? -1);
        let firstToken = true;
        const {content, unitTest, processor, errorMsg} = await generateUnitTestByStreamApi(
            params,
            async (content: string) => {
                if (content && firstToken) {
                    this.performanceLog.log({
                        plugin: 'comate',
                        skill: 'unitTest',
                        duration: performance.now() - startTime,
                        type: 'first-token',
                    });
                }
                firstToken = false;
                reply.update(content);
            },
            reply.message.cancelTokenSource?.token,
            axiosTokenSource.token
        );
        if (processor && processor.error) {
            reply.fail(getGenerationFailureText(processor.errorMsg));
            return;
        }
        if (errorMsg) {
            reply.fail(getGenerationFailureText(errorMsg));
            return;
        }
        if (!content) {
            reply.fail(L10n.t(UnitTestProviderText.GENERATE_ERROR));
            return;
        }
        const {uuid, chatId} = await generateUUID(document, context, content, true);
        reply.success(
            addCodeBlockFormat(content, unitTest?.case_info.language ?? document.languageId),
            this.getActions(uuid, unitTest, chatId, content, document),
            uuid
        );
        if (uuid) {
            this.timeTracker.bindChatId(uuid, reply.message.replyTo ?? -1);
            return {uuid};
        }
        return {uuid: ''};
    }

    private getDisplayText(document: vscode.TextDocument, unitTest: UnitTestItem) {
        if (unitTest.display_content) {
            return unitTest.display_content;
        }

        const res = [];
        const imports = (() => {
            const raw = unitTest.imports;
            if (document.languageId === 'go') {
                if (raw === null) {
                    return ['import testing'];
                }
                return raw.map(item => `import ${item}`);
            }
            return raw ?? [];
        })();
        if (imports.length > 0) {
            res.push(...imports);
            res.push('');
        }
        if (unitTest.fields && unitTest.fields.length > 0) {
            res.push(...unitTest.fields);
            res.push('');
        }
        res.push(...(unitTest.methods ?? []).map(item => `${item}\n`));
        return addMarkdownCodeBlock(res.join('\n').trim(), document.languageId);
    }

    private getActions(
        uuid?: string,
        unitTest?: UnitTestItem,
        chatId?: string,
        generatedContent?: string,
        document?: vscode.TextDocument
    ) {
        const actions: Actions = {
            copy: async (acceptedContent: string) => {
                vscode.env.clipboard.writeText(acceptedContent);
                if (!uuid) {
                    return;
                }
                acceptCode({uuid, accepted: true, content: '', generatedContent: acceptedContent});
                this.timeTracker.recordChatEnd(
                    uuid,
                    acceptedContent
                );
            },
        };
        if (unitTest) {
            actions.accept = async () => {
                acceptUnitTest(unitTest, async () => {
                    if (!uuid || !generatedContent) {
                        return;
                    }
                    // 生成单测 采纳埋点
                    const documentText = document?.getText() ?? '';
                    acceptCode({
                        uuid,
                        accepted: true,
                        content: '',
                        acceptanceInfo: {
                            originContent: documentText,
                            row: 0,
                            col: 0,
                            acceptanceContent: generatedContent,
                            acceptanceType: ACCEPTANCE_TYPE.INSERT,
                        },
                    });
                    this.timeTracker.recordChatEnd(
                        uuid,
                        // TODO 这里需要获取生成的内容
                        generatedContent
                    );
                    this.timeTracker.isCodeByComateNonSerial = true;
                });
            };
        }
        if (uuid && (isPoc || chatId)) {
            actions.feedback = (options: FeedbackOptions) => {
                modifyCode({uuid, chatId, ...options});
            };
        }
        return actions;
    }

    private getOptionText(config: UnitTestMetaInfo) {
        const {test_framework, mock_framework} = config;
        const res = [];
        if (test_framework === 'off') {
            res.push(`${L10n.t(GlobalText.COMMON_WITHOUT)} ${L10n.t(GlobalText.COMMON_FRAMEWORK)}`);
        }
        else if (test_framework) {
            const framework = FRAMEWORK_DISPLAY_NAME_MAP[test_framework] ?? test_framework;
            res.push(`${L10n.t(GlobalText.COMMON_USING)} ${framework}`);
        }
        if (mock_framework === 'off') {
            res.push(`${L10n.t(GlobalText.COMMON_WITHOUT)} Mocks`);
        }
        else if (mock_framework) {
            const mock = FRAMEWORK_DISPLAY_NAME_MAP[mock_framework] ?? mock_framework;
            res.push(`${L10n.t(GlobalText.COMMON_USING)} ${mock}`);
        }
        const comma = L10n.t(GlobalText.COMMON_COMMA);
        return res.length > 0 ? `${comma}${res.join(comma)}` : '';
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
