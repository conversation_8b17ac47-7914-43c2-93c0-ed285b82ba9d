/* eslint-disable camelcase */
import {readFile} from 'node:fs/promises';
import {IncomingMessage} from 'node:http';
import * as vscode from 'vscode';
import {CancelToken, AxiosResponse} from 'axios';
import {SSEProcessor} from '@comate/plugin-shared-internals';
import {isFileExist} from '@/utils/fs';
import {L10n} from '@/common/L10nProvider/L10n';
import {GlobalText} from '@/common/L10nProvider/constants';
import consoleLogger from '@/common/consoleLogger';
import {
    AcceptUnitTestResponse,
    UnitTestItem,
    UnitTestResponse,
    acceptUnitTest,
    generateUnitTest,
    UnitTestMetaInfo,
    generateAndStreamUnitTest,
    GenerateUnitTestOptions,
} from '../../api/unitTest';
import {checkTreeSitterSupport, TreeSitterLanguage} from '../../utils/treeSitterUtils';
import {error, warning} from '../../common/outputChannel';
import {UserService} from '../UserService';
import {ConfigKey, VSCodeConfigProvider} from '../ConfigProvider';

export interface GenerateContext {
    startLine: number;
    endLine: number;
    dependencyPromise?: Promise<GenerateUnitTestOptions['param_struct']>;
}

export const readResponseSteam = async (
    res: AxiosResponse<IncomingMessage, any>,
    update: (content: string) => void,
    cancellationToken?: vscode.CancellationToken
) => {
    let content = '';

    const processor = new SSEProcessor<UnitTestItem>(res.data, cancellationToken);
    // eslint-disable-next-line no-undef-init
    let unitTest: UnitTestItem | undefined = undefined;
    for await (const chunk of processor.processSSE()) {
        if (chunk.display_content) {
            content += chunk.display_content;
            update(content);
        }
        unitTest = chunk;
    }
    if (unitTest) {
        unitTest.display_content = content;
    }

    return {content, processor, unitTest, errorMsg: undefined};
};

function getFrameworkConfigKeyByLanguageId(languageId: TreeSitterLanguage) {
    switch (languageId) {
        case TreeSitterLanguage.Java:
            return ConfigKey.UnitTestFrameworkForJava;
        case TreeSitterLanguage.Go:
            return ConfigKey.UnitTestFrameworkForGo;
        case TreeSitterLanguage.Cpp:
        case TreeSitterLanguage.C:
            return ConfigKey.UnitTestFrameworkForCpp;
        case TreeSitterLanguage.JavaScript:
        case TreeSitterLanguage.TypeScript:
        case TreeSitterLanguage.Tsx:
            return ConfigKey.UnitTestFrameworkForJs;
        case TreeSitterLanguage.Python:
            return ConfigKey.UnitTestFrameworkForPython;
        default:
            return undefined;
    }
}

function getMockkConfigKeyByLanguageId(languageId: TreeSitterLanguage) {
    switch (languageId) {
        case TreeSitterLanguage.Java:
            return ConfigKey.UnitTestMockForJava;
        case TreeSitterLanguage.Go:
            return ConfigKey.UnitTestMockForGo;
        case TreeSitterLanguage.Cpp:
        case TreeSitterLanguage.C:
            return ConfigKey.UnitTestMockForCpp;
        case TreeSitterLanguage.JavaScript:
        case TreeSitterLanguage.TypeScript:
        case TreeSitterLanguage.Tsx:
            return ConfigKey.UnitTestMockForJs;
        case TreeSitterLanguage.Python:
            return ConfigKey.UnitTestMockForPython;
        default:
            return undefined;
    }
}

const wrapConfig = (config?: string) => (config === 'auto' ? '' : config);

export async function getUnitTestConfig(
    document: vscode.TextDocument,
    configProvider: VSCodeConfigProvider,
    userService: UserService
): Promise<UnitTestMetaInfo> {
    const [username] = await userService.getCurrentUser();
    const languageId = checkTreeSitterSupport(document.languageId);
    if (!languageId) {
        return {
            test_framework: '',
            mock_framework: 'off',
            user: username,
        };
    }
    const frameworkKey = getFrameworkConfigKeyByLanguageId(languageId);
    const framework = frameworkKey
        ? wrapConfig(configProvider.getConfig<string>(frameworkKey))
        : '';

    const mockKey = getMockkConfigKeyByLanguageId(languageId);
    const mock = mockKey
        ? wrapConfig(configProvider.getConfig<string>(mockKey))
        : '';

    return {
        test_framework: framework ?? '',
        mock_framework: mock ?? 'off',
        user: username,
    };
}

export async function generateUnitTestByApi(
    document: vscode.TextDocument,
    context: GenerateContext,
    unitTestConfig: UnitTestMetaInfo
): Promise<UnitTestResponse> {
    const fileName = document.fileName;
    const content = document.getText();
    const {startLine, endLine, dependencyPromise} = context;
    const param_struct = await dependencyPromise
        ?.then(result => {
            consoleLogger.debug('(ut-context)(success)', result);
            return result;
        })
        .catch(e => {
            consoleLogger.debug('(ut-context)(fail)', e);
            return undefined;
        });
    const res = await generateUnitTest({
        src_file_content: content,
        src_file_path: fileName,
        start_line: startLine,
        end_line: endLine,
        meta: unitTestConfig,
        param_struct,
    });
    return res.data;
}

export async function generateUnitTestByStreamApi(
    params: GenerateUnitTestOptions,
    update: (content: string) => void,
    cancellationToken?: vscode.CancellationToken,
    cancelToken?: CancelToken
) {
    let content = '';
    const updateContent = (curContent: string) => {
        content += curContent;
        update(curContent);
    };
    try {
        const res = await generateAndStreamUnitTest(params, cancelToken);
        return readResponseSteam(res, updateContent, cancellationToken);
    }
    catch (e) {
        // 单测部分接口区别于其他功能，返回码为400时，需要展示 License 相关报错提示，具体提示内容从接口信息中读取
        const dedcoder = new TextDecoder();
        for await (const body of (e as any).response.data) {
            const decodedData = dedcoder.decode(body, {stream: true});
            const msg: string = JSON.parse(decodedData).msg;
            if (msg) {
                error('request failed: ', msg);
                return {content, unitTest: undefined, processor: undefined, errorMsg: msg};
            }
        }
        throw e;
    }
}

export async function fetchUnitTest(
    document: vscode.TextDocument,
    context: GenerateContext,
    unitTestConfig: UnitTestMetaInfo
): Promise<UnitTestResponse> {
    try {
        const result = await generateUnitTestByApi(document, context, unitTestConfig);
        return result;
    }
    catch (e) {
        return {result: 'FAIL', msg: (e as Error).message};
    }
}

async function readUnitTestContent(file: string) {
    try {
        const exist = await isFileExist(file);
        if (exist) {
            const content = await readFile(file, 'utf-8');
            return content;
        }
        return '';
    }
    catch (e) {
        warning('read unit test content failed, file path:' + file);
        return '';
    }
}

export async function resolveAccept(unitTest: UnitTestItem): Promise<AcceptUnitTestResponse> {
    try {
        const content = await readUnitTestContent(unitTest.case_info.test_file_path);
        const res = await acceptUnitTest({
            case: unitTest,
            ut_file_content: content,
        });
        return res.data;
    }
    catch (e: any) {
        return {result: 'FAIL', msg: e.message ?? L10n.t(GlobalText.COMMON_UNKNOWN_ERROR)};
    }
}
