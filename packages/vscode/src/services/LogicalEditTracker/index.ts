import {
    DidChangeTextDocumentParams,
    DidOpenTextDocumentParams,
    Disposable,
    LRUCache,
    Position,
    Range,
    TextDocumentContentChangeEvent,
    DocumentUri,
} from 'vscode-languageserver-protocol';
import {Change, diffLines} from 'diff';
import {findIndex, findLastIndex, last, reverse} from 'lodash';
import {IWorkspaceProvider} from '@/common/lsp/types';
import {trace} from '@/common/outputChannel';
import {elideLongLine} from '@/utils/common';
import {ILogicalEditTracker, LogicalEdit} from './types';

interface TextDocumentChange {
    range: Range;
    text: string;
}

/**
 * 一个编辑事件的记录
 */
interface EditLogEntry {
    change: TextDocumentChange;
    /**
     * 根据编辑信息计算的光标位置
     */
    cursor: Position;
    /**
     * 编辑后的文本快照
     */
    snapshot: string;
    modifiedTime: number;
}

interface IntermediateEdits {
    base: string;
    // TODO: 理论上这里是个长度为 2 的 fixed-size queue，可以优化下减少些内存 @tianzerun
    edits: EditLogEntry[];
}

export interface Options {
    /** 最多保存的编辑块（diff块）的数量 */
    maxEditStack?: number;
    /** 当文件超过这个行数后不对改文件进行变更记录 */
    maxFileLineCount?: number;
    /** 一个 diff 块中 */
    maxDiffLineChars: number;
    /** 一个 diff 块的最大字符数，超过后应当截断 */
    maxDiffBlockChars?: number;
}

export const defaultOptions: Required<Options> = {
    maxEditStack: 50,
    maxFileLineCount: 8000,
    maxDiffLineChars: 300,
    maxDiffBlockChars: 3000,
};

const createDiffText = (
    changes: Change[],
    options: Pick<Required<Options>, 'maxDiffLineChars' | 'maxDiffBlockChars'>
) => {
    const changedLines = [];

    const firstChangedBlock = findIndex(changes, item => Boolean(item.added || item.removed));
    const lastChangedBlock = findLastIndex(changes, item => Boolean(item.added || item.removed));

    const startLineNumber = changes
        .slice(0, firstChangedBlock)
        .reduce((acc, cur) => acc + (cur.count ?? 0), 1);
    let oldFileLineNumber = startLineNumber;
    let newFileLineNumber = startLineNumber;

    for (const change of changes.slice(firstChangedBlock, lastChangedBlock + 1)) {
        const lines = change.value.replace(/\r?\n$/, '').split(/\r?\n/);
        if (change.added) {
            for (const line of lines) {
                changedLines.push(`${newFileLineNumber}+|${elideLongLine(line, options.maxDiffLineChars)}`);
                newFileLineNumber++;
            }
        }
        else if (change.removed) {
            for (const line of lines) {
                changedLines.push(`${oldFileLineNumber}-|${elideLongLine(line, options.maxDiffLineChars)}`);
                oldFileLineNumber++;
            }
        }
        else {
            for (const line of lines) {
                changedLines.push(`${newFileLineNumber} |${elideLongLine(line, options.maxDiffLineChars)}`);
                newFileLineNumber++;
                oldFileLineNumber++;
            }
        }
    }
    // NOTE: 当 diff 块长度超过 maxDiffBlockChars 时，应当截断，优先保留后面的行。
    let startIndex = changedLines.length;
    let totalChars = 0;
    while (startIndex > 0) {
        startIndex--;
        totalChars += changedLines[startIndex].length;
        if (totalChars > options.maxDiffBlockChars) {
            startIndex++;
            break;
        }
    }
    return changedLines.slice(startIndex).join('\n');
};

export class LogicalEditTracker implements ILogicalEditTracker, Disposable {
    private readonly disposables: Disposable[] = [];
    private readonly intermediates: LRUCache<DocumentUri, IntermediateEdits>;
    private readonly finalizedEdits: LogicalEdit[] = [];
    private lastEditedFile: DocumentUri | null = null;
    private readonly finalOptions: Required<Options>;
    private _lastEditTimestamp: number = 0;

    constructor(private readonly workspaceProvider: IWorkspaceProvider, readonly options: Options = defaultOptions) {
        this.finalOptions = {
            ...defaultOptions,
            ...options,
        };
        // NOTE: 这里其实 key 是文件，但数量用最大编辑栈来限制一下，鸽子笼理论，假设一个文件就一个编辑块。
        this.intermediates = new LRUCache<DocumentUri, IntermediateEdits>(this.finalOptions.maxEditStack);
        this.disposables.push(
            workspaceProvider.onDidChangeTextDocument(this.handleTextDocumentChange.bind(this)),
            workspaceProvider.onDidOpenTextDocument(this.handleTextDocumentOpen.bind(this))
        );
        // 插件激活前可能已经有文件被打开了，因此没有open事件，这些文件都手动处理一下。
        for (const doc of workspaceProvider.visibleTextDocuments) {
            this.handleTextDocumentOpen({textDocument: doc});
        }
    }

    async getEdits(uri?: DocumentUri): Promise<LogicalEdit[]> {
        const result = [...this.finalizedEdits];
        const intermediate = uri ? this.intermediates.get(uri) : undefined;
        if (uri && intermediate) {
            const newText = last(intermediate.edits)?.snapshot;
            if (newText) {
                const diffs = diffLines(intermediate.base, newText, {
                    ignoreWhitespace: true,
                });
                const diffText = createDiffText(diffs, this.finalOptions);
                if (diffText.trim().length > 0) {
                    result.push({
                        uri,
                        diff: diffText,
                        modifiedTime: this._lastEditTimestamp || Date.now(),
                    });
                }
            }
        }
        return result;
    }

    get lastEditTimestamp(): number {
        return this._lastEditTimestamp;
    }

    /**
     * 标记一个编辑思路的结束，将中间的编辑记录转为最终的编辑快照。
     *
     * @param uri 可选参数，文档URI，未提供时默认处理所有文件。
     */
    finalizeEdit(uri?: DocumentUri) {
        const targets = uri
            ? [uri]
            : [...this.intermediates.keys()];
        for (const item of targets) {
            this._finalizeEdit(item);
        }
    }

    // eslint-disable-next-line complexity
    private async handleTextDocumentChange(e: DidChangeTextDocumentParams) {
        if (e.contentChanges.length <= 0) {
            return;
        }
        const fileUri = e.textDocument.uri;
        if (this.isIgnored(fileUri)) {
            return;
        }
        const topChange = e.contentChanges[0];
        if (!TextDocumentContentChangeEvent.isIncremental(topChange)) {
            return;
        }
        const intermediate = await this.ensureTrackDocument(fileUri);
        const doc = await this.workspaceProvider.openTextDocument(fileUri);
        if (doc.lineCount > this.finalOptions.maxFileLineCount) {
            return;
        }
        this._lastEditTimestamp = Date.now();
        const text = doc.getText();
        // 当前编辑如果是以下情况就标记着一段新思路的开始，此时捕获之前的编辑为一个完整的思路。
        if (this.lastEditedFile && (fileUri !== this.lastEditedFile)) {
            // 切换了编辑的文件，标记一个思路结束。
            trace('LogicalEditTracker(finalize): file changed');
            this._finalizeEdit(this.lastEditedFile);
        }
        else if (this.isCrossLineEdit(fileUri, topChange)) {
            trace('LogicalEditTracker(finalize): cross line edit');
            this._finalizeEdit(fileUri);
        }
        else if (this.isUpRightEdit(fileUri, topChange)) {
            trace('LogicalEditTracker(finalize): up right edit');
            this._finalizeEdit(fileUri);
        }
        else if (this.isVShapeEdit(fileUri, topChange)) {
            trace('LogicalEditTracker(finalize): v shape edit');
            this._finalizeEdit(fileUri);
        }
        else if (this.isSameLineEdit(fileUri, topChange)) {
            // 如果当前编辑和上一个编辑在同一行，则删去上一个编辑的记录。
            trace('LogicalEditTracker(update): same line edit');
            intermediate.edits.pop();
        }
        intermediate.edits.push({
            change: topChange,
            cursor: this.inferCursorPosition(topChange),
            snapshot: text,
            modifiedTime: Date.now(),
        });
        this.lastEditedFile = fileUri;
    }

    private _finalizeEdit(fileUri: DocumentUri) {
        const intermediate = this.intermediates.get(fileUri);
        if (!intermediate) {
            return;
        }
        const endEdit = last(intermediate.edits);
        const endText = endEdit?.snapshot;
        if (!endText) {
            return;
        }
        const diffs = diffLines(intermediate.base, endText, {
            ignoreWhitespace: true,
        });
        if (diffs.some(change => (change.added || change.removed) && change.value.trim().length > 0)) {
            // 存在非空（空格、换行）的编辑，则保存为一条完整的思路编辑。
            const diffText = createDiffText(diffs, this.finalOptions);
            if (diffText.trim().length > 0) {
                // NOTE: 这里 -1 是给当前未结束的思路的diff留的。
                if (this.finalizedEdits.length >= this.finalOptions.maxEditStack - 1) {
                    // 当连贯思路编辑队列超过最大值时，删除最旧的一个编辑。
                    this.finalizedEdits.shift();
                }
                this.finalizedEdits.push({
                    uri: fileUri,
                    diff: diffText,
                    modifiedTime: endEdit.modifiedTime,
                });
            }
        }
        intermediate.base = endText;
        intermediate.edits.length = 0;
    }

    private isSameLineEdit(fileUri: DocumentUri, change: TextDocumentChange) {
        const [prev] = this.getRecentEdits(fileUri);
        if (prev) {
            return prev.change.range.start.line === change.range.start.line;
        }
        return false;
    }

    private isCrossLineEdit(fileUri: DocumentUri, change: TextDocumentChange) {
        const intermediate = this.intermediates.get(fileUri);
        const lastEdit = last(intermediate?.edits);
        if (!lastEdit) {
            return false;
        }
        // 当前编辑的开始行号和上一个编辑的结束行号相差1，则认为当前编辑是跨行了。
        return Math.abs(change.range.start.line - lastEdit.cursor.line) > 1;
    }

    /**
     * $e_{x-1}^{row} - e_{x-2}^{row} = -1$，并且 $e_x^{row} = e_{x-1}^{row}$
     */
    private isUpRightEdit(fileUri: DocumentUri, change: TextDocumentChange) {
        const [prev, beforePrev] = this.getRecentEdits(fileUri);
        if (beforePrev && prev) {
            return (prev.change.range.end.line - beforePrev.change.range.end.line === -1
                && prev.change.range.end.line === change.range.start.line);
        }
        return false;
    }

    /**
     * $e_{x}^{row} - e_{x-1}^{row} = -1$，并且 $e_{x-1}^{row} - e_{x-2}^{row} = 1$
     */
    private isVShapeEdit(fileUri: DocumentUri, change: TextDocumentChange) {
        const [prev, beforePrev] = this.getRecentEdits(fileUri);
        if (beforePrev && prev) {
            return (change.range.end.line - prev.change.range.end.line === -1
                && prev.change.range.end.line - beforePrev.change.range.end.line === 1);
        }
        return false;
    }

    private getRecentEdits(fileUri: DocumentUri): Array<EditLogEntry | undefined> {
        const intermediate = this.intermediates.get(fileUri);
        if (!intermediate) {
            return [];
        }
        return reverse([...intermediate?.edits ?? []]);
    }

    private inferCursorPosition(change: TextDocumentChange): Position {
        const line = change.range.start.line + change.text.split(/\r?\n/).length - 1;
        const character = change.text.includes('\n')
            ? change.text.length - (change.text.lastIndexOf('\n') + 1)
            : change.range.start.character + change.text.length;
        return {line, character};
    }

    private async handleTextDocumentOpen(e: DidOpenTextDocumentParams) {
        const fileUri = e.textDocument.uri;
        if (this.isIgnored(fileUri)) {
            return;
        }
        this.ensureTrackDocument(fileUri);
    }

    private async ensureTrackDocument(fileUri: DocumentUri) {
        if (!this.intermediates.has(fileUri)) {
            const doc = await this.workspaceProvider.openTextDocument(fileUri);
            this.intermediates.set(fileUri, {
                base: doc.getText(),
                edits: [],
            });
        }
        return this.intermediates.get(fileUri)!;
    }

    private isIgnored(fileUri: DocumentUri) {
        const workspaces = this.workspaceProvider.workspaceFolders;
        if (!workspaces) {
            return true;
        }
        if (!workspaces.some(item => fileUri.startsWith(item.uri))) {
            return true;
        }
        return false;
    }

    dispose(): void {
        this.disposables.forEach(d => d.dispose());
    }
}
