import path from 'node:path';
import {
    ensureNodeExists,
} from '@/utils/engineExec';
import {log, LogLevel} from '@/common/outputChannel';
import {LogCategory, LogUploaderProvider} from '../LogUploaderProvider';
import {inject, injectable} from 'inversify';
import {ChildProcess, fork, spawn} from 'node:child_process';

@injectable()
export class BinStartupTestProvider {
    private timer: NodeJS.Timer | null = null;
    constructor(@inject(LogUploaderProvider) private readonly logger: LogUploaderProvider) {
    }

    start() {
        this.runDownloadBin();
        this.runVSCBin();
        // 设置每小时执行一次 (1小时 = 60 * 60 * 1000 毫秒)
        this.timer = setInterval(() => {
            this.runDownloadBin();
            this.runVSCBin();
        }, 60 * 60 * 1000);
    }

    stop() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    async runDownloadBin() {
        const nodePath = await ensureNodeExists(this.logger, LogCategory.BinTest);
        if (nodePath) {
            try {
                const cp = spawn(nodePath, ['child-process/binStartup.js'], {
                    cwd: path.resolve(__dirname),
                });
                this.addMonitor(cp, 'spawn');
            }
            catch (error) {
                if (error instanceof Error) {
                    this.logger.logUserAction({
                        category: LogCategory.BinTest,
                        action: 'binStartupErr',
                        content: error?.message,
                    });
                    log(LogLevel.Error, 'binStartupTest', error.message);
                }
            }
        }
    }

    async runVSCBin() {
        const cp = fork('child-process/binStartup.js', [], {
            cwd: path.resolve(__dirname),
            stdio: 'pipe',
        });
        this.addMonitor(cp, 'fork');
    }

    addMonitor(cp: ChildProcess, type: string) {
        // 定义事件处理函数
        const onStdout = (data: Buffer) => {
            const output = data.toString().trim();
            if (output === 'Binary startup successful') {
                this.logger.logUserAction({
                    category: LogCategory.BinTest,
                    action: type,
                    content: 'success',
                });
            }
            else {
                this.logger.logUserAction({
                    category: LogCategory.BinTest,
                    action: type,
                    content: `failure: ${output}`,
                }, 'error');
            }
            console.log('Binary startup successful');
        };

        const onMessage = (message: any) => {
            console.log('Message from child process:', message);
        };

        const onStderr = (data: Buffer) => {
            const error = data.toString().trim();
            console.error('Error from child process:', error);
            this.logger.logUserAction({
                category: LogCategory.BinTest,
                action: type,
                content: `exec stderr: ${error}`,
            }, 'error');
        };

        const onError = (error: Error) => {
            console.error(`exec error: ${error}`);
            this.logger.logUserAction({
                category: LogCategory.BinTest,
                action: type,
                content: `exec error: ${error}`,
            }, 'error');
        };

        const onExit = (code: number | null) => {
            if (code !== 0) {
                log(LogLevel.Info, 'binStartupTest', `child process exited with code ${code}`);
                this.logger.logUserAction({
                    category: LogCategory.BinTest,
                    action: type,
                    content: `exec exit: ${code}`,
                });
            }
        };

        const onClose = (code: number | null) => {
            if (code !== 0) {
                log(LogLevel.Info, 'binStartupTest', `child process closed with code ${code}`);
                this.logger.logUserAction({
                    category: LogCategory.BinTest,
                    action: type,
                    content: `exec close: ${code}`,
                });
            }
            cleanup();
        };

        const cleanup = () => {
            cp.stdout?.removeListener('data', onStdout);
            cp.stderr?.removeListener('data', onStderr);
            cp.removeListener('error', onError);
            cp.removeListener('exit', onExit);
            cp.removeListener('close', onClose);
        };

        cp.stdout?.on('data', onStdout);
        cp.stderr?.on('data', onStderr);
        cp.on('message', onMessage);
        cp.on('error', onError);
        cp.on('exit', onExit);
        cp.on('close', onClose);
    }
}
