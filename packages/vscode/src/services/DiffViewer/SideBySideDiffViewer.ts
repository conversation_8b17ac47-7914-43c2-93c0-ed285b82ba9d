import {groupBy, max, min, noop, range, repeat} from 'lodash';
import * as vscode from 'vscode';
import {AdvancedLinesDiffComputer, LinesDiff, RangeMapping} from 'vscode-diff';
import {countChineseCharacter} from '@/utils/common';
import {
    createTabToAcceptGuide,
    createEndOfLineWhitespace,
    createInvisibleTabToAcceptGuide,
} from '@/common/decorations';
import {IDiffViewer} from './types';

function toTextLines(text: string, startLine: number): vscode.TextLine[] {
    const lines = text.split(/\r?\n/);
    return lines.map((line, idx) => {
        const lineNumber = startLine + idx;
        const range = new vscode.Range(lineNumber, 0, lineNumber, line.length);
        const nonWhitespaceCharMatch = /\S/.exec(line);
        const firstNonWhitespaceCharacterIndex = nonWhitespaceCharMatch ? nonWhitespaceCharMatch.index : line.length;
        const textLine: vscode.TextLine = {
            lineNumber,
            text: line,
            range,
            rangeIncludingLineBreak: new vscode.Range(range.start, new vscode.Position(range.start.line + 1, 0)),
            firstNonWhitespaceCharacterIndex,
            isEmptyOrWhitespace: firstNonWhitespaceCharacterIndex === line.length,
        };
        return textLine;
    });
}

function getLineWidth(text: string) {
    // 计算中文字符数量，3个中文字符等于5个英文字符宽度
    const chineseCharactersCount = countChineseCharacter(text);
    return text.length - chineseCharactersCount + Math.round(chineseCharactersCount * 5 / 3);
}

function getMaxLineWidth(editor: vscode.TextEditor, startLine: number, endLine: number) {
    return max(range(startLine, endLine + 1).map(line => getLineWidth(editor.document.lineAt(line).text))) ?? 0;
}

function breakApartRangeByTextLines(textLines: vscode.TextLine[], range: vscode.Range): vscode.Range[] {
    const ranges = textLines.map(line => line.range);
    return ranges.map(lineRange => lineRange.intersection(range)).filter(item => item !== undefined) as vscode.Range[];
}

export function getLeftMostNonWhitespaceColumn(text: string) {
    const lines = text.split('\n');
    return min(lines.map(line => line.search(/\S/))) ?? 0;
}

export function trimCommonLeadingWhitespaces(text: string) {
    const endTrimmed = text.trimEnd();
    const leftMost = getLeftMostNonWhitespaceColumn(endTrimmed);
    const lines = endTrimmed.split('\n');
    return lines.map(line => line.slice(leftMost)).join('\n');
}

function getTextAtRange(textLines: vscode.TextLine[], range: vscode.Range): string {
    return textLines
        .map(line => {
            const intersection = line.range.intersection(range);
            if (intersection) {
                return line.text.substring(intersection.start.character, intersection.end.character);
            }
            return undefined;
        })
        .filter(item => item !== undefined)
        .join('');
}

function toVSCRange(range: RangeMapping['originalRange']): vscode.Range {
    return new vscode.Range(
        range.startLineNumber - 1,
        range.startColumn - 1,
        range.endLineNumber - 1,
        range.endColumn - 1
    );
}

interface ColorOptions {
    backlightColor?: vscode.ThemeColor | string;
    insertedTextColor?: vscode.ThemeColor | string;
    removedTextBackground?: vscode.ThemeColor | string;
    insertedTextBackground?: vscode.ThemeColor | string;
}

const defaultColorOptions: Required<ColorOptions> = {
    backlightColor: new vscode.ThemeColor('editor.rangeHighlightBackground'),
    insertedTextColor: new vscode.ThemeColor('baidu.comate.colors.inlineDiff.insertedTextForeground'),
    removedTextBackground: new vscode.ThemeColor('baidu.comate.colors.inlineDiff.removedTextBackground'),
    insertedTextBackground: new vscode.ThemeColor('baidu.comate.colors.inlineDiff.insertedTextBackground'),
};

export class SideBySideDiffViewer implements IDiffViewer {
    private readonly finalizedColorOptions: Required<ColorOptions>;

    constructor(readonly colorOptions: ColorOptions = defaultColorOptions) {
        this.finalizedColorOptions = {
            ...defaultColorOptions,
            ...colorOptions,
        };
    }

    async showDiff(editor: vscode.TextEditor, startLine: number, endLine: number, newText: string) {
        const oldLineRange = range(startLine, endLine + 1);
        const oldTextLines = oldLineRange.map(lineNumber => editor.document.lineAt(lineNumber));
        const newTextLines = toTextLines(newText, startLine);

        if (startLine + newTextLines.length > editor.document.lineCount) {
            // 由于 decorations 需要加在实际存在的行后面，所以如果新文本比旧文本长，抛出异常
            throw new Error(
                'Unable to render the diff: the replaced document cannot be longer than the original document '
            );
        }

        const decorations: vscode.TextEditorDecorationType[] = [];
        // highlight diffs
        const diffComputer = new AdvancedLinesDiffComputer();
        const diffs = diffComputer.computeDiff(
            oldTextLines.map(line => line.text),
            newTextLines.map(line => line.text),
            {
                ignoreTrimWhitespace: false,
                computeMoves: false,
                maxComputationTimeMs: 0,
            }
        );
        if (diffs.changes.length === 0) {
            // No changes to show
            return {
                hide: noop,
            };
        }
        const metadata = this.getDiffMetadata(diffs, oldTextLines, newTextLines, editor.selection.active);
        const acceptGuideAt = Math.max(metadata.firstChangedLine - 1, 0);

        if (metadata.isAfterCursorSingleLineCompletion && metadata.firstChange) {
            decorations.push(
                ...this.showReplacedTextAsGhostText(editor, oldTextLines, newTextLines, metadata.firstChange),
                ...this.addDecorationEol(editor, acceptGuideAt, createEndOfLineWhitespace(1)),
                ...this.addDecorationEol(editor, acceptGuideAt, createTabToAcceptGuide())
            );
        }
        else if (!metadata.hasInserted && metadata.hasRemoved) {
            // 仅删除：只高亮删除部分
            decorations.push(
                ...this.addRemovedTextHighlight(editor, oldTextLines, diffs, {showNewLineRemoval: true}),
                ...this.addDecorationEol(editor, acceptGuideAt, createEndOfLineWhitespace(1)),
                ...this.addDecorationEol(editor, acceptGuideAt, createTabToAcceptGuide())
            );
        }
        else if (metadata.noCrossLineAddition && !metadata.hasRemoved) {
            // 无跨行的新增，无删除，用 ghost text 展示新增文本
            decorations.push(
                ...this.showInsertedTextAsGhostText(editor, newTextLines, diffs),
                ...this.addDecorationEol(editor, acceptGuideAt, createEndOfLineWhitespace(1)),
                ...this.addDecorationEol(editor, acceptGuideAt, createTabToAcceptGuide())
            );
        }
        else if (metadata.noCrossLineChanges) {
            // 无跨行更改，用 inline 方式展示更改
            decorations.push(
                ...this.showInlineChanges(editor, oldTextLines, newTextLines, diffs),
                ...this.addDecorationEol(editor, acceptGuideAt, createEndOfLineWhitespace(1)),
                ...this.addDecorationEol(editor, acceptGuideAt, createTabToAcceptGuide())
            );
        }
        else {
            // 其它情况：side-by-side 展示 diff
            decorations.push(
                ...this.addBacklight(editor, oldTextLines, diffs),
                ...this.addWhitespaceGutter(editor, startLine, endLine, newTextLines.length),
                ...this.addPaddedTabGuide(editor, startLine, endLine, newTextLines.length),
                ...this.addRemovedTextHighlight(editor, oldTextLines, diffs),
                ...this.showNewTextOnTheSide(editor, newTextLines, diffs)
            );
        }
        return {
            hide: () => {
                decorations.forEach(decoration => decoration.dispose());
            },
        };
    }

    // eslint-disable-next-line complexity
    private getDiffMetadata(
        linesDiff: LinesDiff,
        oldTextLines: vscode.TextLine[],
        newTextLines: vscode.TextLine[],
        cursor: vscode.Position
    ) {
        let noCrossLineAddition = true;
        let noCrossLineChanges = true;
        let hasInserted = false;
        let hasRemoved = false;
        let leftMostColumn = min(oldTextLines.map(line => line.firstNonWhitespaceCharacterIndex)) ?? 0;
        const realFirstLineNumber = oldTextLines[0].lineNumber;
        let firstChange = null;
        let numberOfChanges = 0;
        for (const change of linesDiff.changes) {
            for (const inner of change.innerChanges ?? []) {
                // eslint-disable-next-line max-len
                noCrossLineAddition = noCrossLineAddition
                    && inner.modifiedRange.startLineNumber === inner.modifiedRange.endLineNumber;
                noCrossLineChanges = noCrossLineChanges
                    && inner.originalRange.startLineNumber === inner.originalRange.endLineNumber
                    && inner.modifiedRange.startLineNumber === inner.modifiedRange.endLineNumber;
                hasInserted = hasInserted || !inner.modifiedRange.isEmpty();
                hasRemoved = hasRemoved || !inner.originalRange.isEmpty();
                leftMostColumn = Math.min(inner.originalRange.startColumn - 1);
                if (firstChange === null) {
                    firstChange = inner;
                }
                numberOfChanges++;
            }
        }
        const checkSingleLineCompletion = () => {
            if (
                oldTextLines.length !== newTextLines.length
                || numberOfChanges !== 1
                || !firstChange
            ) {
                return false;
            }
            const originalRange = toVSCRange(firstChange.originalRange.delta(realFirstLineNumber));
            const modifiedRange = toVSCRange(firstChange.modifiedRange.delta(realFirstLineNumber));
            if (
                originalRange.end.line !== cursor.line
                || originalRange.end.character !== cursor.character
            ) {
                return false;
            }
            const originalText = getTextAtRange(oldTextLines, originalRange);
            const modifiedText = getTextAtRange(newTextLines, modifiedRange);
            return modifiedText.startsWith(originalText);
        };

        return {
            noCrossLineAddition,
            noCrossLineChanges,
            hasInserted,
            hasRemoved,
            leftMostColumn,
            firstChangedLine: realFirstLineNumber + (firstChange?.originalRange.startLineNumber ?? 1) - 1,
            isAfterCursorSingleLineCompletion: checkSingleLineCompletion(),
            firstChange,
        };
    }

    private showInlineChanges(
        editor: vscode.TextEditor,
        oldTextLines: vscode.TextLine[],
        newTextLines: vscode.TextLine[],
        linesDiff: LinesDiff
    ) {
        const startLine = oldTextLines[0].lineNumber;
        const decorations: vscode.TextEditorDecorationType[] = [];
        for (const change of linesDiff.changes) {
            for (const inner of change.innerChanges ?? []) {
                const originalRange = toVSCRange(inner.originalRange.delta(startLine));
                if (!originalRange.isEmpty) {
                    const removedDecoration = vscode.window.createTextEditorDecorationType({
                        backgroundColor: this.finalizedColorOptions.removedTextBackground,
                        textDecoration: 'line-through;',
                    });
                    editor.setDecorations(removedDecoration, [originalRange]);
                    decorations.push(removedDecoration);
                }
                const modifiedRange = toVSCRange(inner.modifiedRange.delta(startLine));
                const insertedText = getTextAtRange(newTextLines, modifiedRange);
                if (insertedText.length > 0) {
                    const insertRange = toVSCRange(inner.originalRange.collapseToEnd().delta(startLine));
                    const insertedDecoration = vscode.window.createTextEditorDecorationType({
                        before: {
                            contentText: insertedText,
                            color: this.finalizedColorOptions.insertedTextColor,
                            textDecoration: 'none; white-space: pre',
                            height: '100%',
                            fontStyle: 'italic',
                            backgroundColor: this.finalizedColorOptions.insertedTextBackground,
                        },
                    });
                    editor.setDecorations(insertedDecoration, [insertRange]);
                    decorations.push(insertedDecoration);
                }
            }
        }
        return decorations;
    }

    private showInsertedTextAsGhostText(
        editor: vscode.TextEditor,
        newTextLines: vscode.TextLine[],
        linesDiff: LinesDiff
    ) {
        const startLine = newTextLines[0].lineNumber;
        const decorations: vscode.TextEditorDecorationType[] = [];
        for (const change of linesDiff.changes) {
            for (const inner of change.innerChanges ?? []) {
                if (!inner.modifiedRange.isEmpty()) {
                    const vscRange = toVSCRange(inner.modifiedRange.delta(startLine));
                    const insertedText = getTextAtRange(newTextLines, vscRange);
                    const insertRange = toVSCRange(inner.originalRange.delta(startLine));
                    const decoration = vscode.window.createTextEditorDecorationType({
                        before: {
                            contentText: insertedText,
                            color: this.finalizedColorOptions.insertedTextColor,
                            textDecoration: 'none; white-space: pre',
                            height: '100%',
                            fontStyle: 'italic',
                        },
                    });
                    decorations.push(decoration);
                    editor.setDecorations(decoration, [insertRange]);
                }
            }
        }
        return decorations;
    }

    private showReplacedTextAsGhostText(
        editor: vscode.TextEditor,
        oldTextLines: vscode.TextLine[],
        newTextLines: vscode.TextLine[],
        change: RangeMapping
    ) {
        const startLine = newTextLines[0].lineNumber;
        const decorations: vscode.TextEditorDecorationType[] = [];
        const originalRange = toVSCRange(change.originalRange.delta(startLine));
        const modifiedRange = toVSCRange(change.modifiedRange.delta(startLine));
        const originalText = getTextAtRange(oldTextLines, originalRange);
        const modifiedText = getTextAtRange(newTextLines, modifiedRange);
        if (modifiedText.startsWith(originalText)) {
            const insertRange = new vscode.Range(originalRange.end, originalRange.end);
            const decoration = vscode.window.createTextEditorDecorationType({
                after: {
                    contentText: modifiedText.slice(originalText.length),
                    color: this.finalizedColorOptions.insertedTextColor,
                    textDecoration: 'none; white-space: pre',
                    height: '100%',
                    fontStyle: 'italic',
                },
            });
            editor.setDecorations(decoration, [insertRange]);
            decorations.push(decoration);
        }
        return decorations;
    }

    private addRemovedTextHighlight(
        editor: vscode.TextEditor,
        oldTextLines: vscode.TextLine[],
        linesDiff: LinesDiff,
        options?: {
            showNewLineRemoval: boolean;
        }
    ) {
        const decorations: vscode.TextEditorDecorationType[] = [];
        const startLine = oldTextLines[0].lineNumber;
        const removedRanges = [];
        for (const change of linesDiff.changes) {
            for (const inner of change.innerChanges ?? []) {
                if (!inner.originalRange.isEmpty()) {
                    const vscRange = toVSCRange(inner.originalRange.delta(startLine));
                    const partial = breakApartRangeByTextLines(oldTextLines, vscRange);
                    removedRanges.push(...partial);
                }
            }
        }
        if (options?.showNewLineRemoval) {
            const removedNewlineRanges = removedRanges.filter(range => {
                if (range.isEmpty) {
                    const removedLine = oldTextLines.find(line => line.lineNumber === range.start.line);
                    return removedLine?.isEmptyOrWhitespace;
                }
                return false;
            });
            const removeNewlineDecoration = vscode.window.createTextEditorDecorationType({
                backgroundColor: this.finalizedColorOptions.removedTextBackground,
                isWholeLine: true,
            });
            editor.setDecorations(removeNewlineDecoration, removedNewlineRanges);
            decorations.push(removeNewlineDecoration);
        }
        const removedTextRanges = removedRanges.filter(range => !range.isEmpty);
        const removedTextDecoration = vscode.window.createTextEditorDecorationType({
            backgroundColor: this.finalizedColorOptions.removedTextBackground,
        });
        editor.setDecorations(removedTextDecoration, removedTextRanges);
        decorations.push(removedTextDecoration);
        return decorations;
    }

    private showNewTextOnTheSide(editor: vscode.TextEditor, newTextLines: vscode.TextLine[], linesDiff: LinesDiff) {
        const startLine = newTextLines[0].lineNumber;
        const insertedRanges = [];
        for (const change of linesDiff.changes) {
            for (const inner of change.innerChanges ?? []) {
                if (!inner.modifiedRange.isEmpty()) {
                    const vscRange = toVSCRange(inner.modifiedRange.delta(startLine));
                    const partial = breakApartRangeByTextLines(newTextLines, vscRange);
                    insertedRanges.push(...partial.filter(range => !range.isEmpty));
                }
            }
        }
        const insertedRangesByLine = groupBy(insertedRanges, item => item.start.line);
        const decorations: vscode.TextEditorDecorationType[] = [];
        const addUnchangedText = (text: string, range: vscode.Range) => {
            const unchangedRangeDecoration = vscode.window.createTextEditorDecorationType({
                after: {
                    contentText: text,
                    color: this.finalizedColorOptions.insertedTextColor,
                    backgroundColor: new vscode.ThemeColor('editor.background'),
                    textDecoration: 'none; white-space: pre',
                    height: '100%',
                },
            });
            editor.setDecorations(unchangedRangeDecoration, [range]);
            decorations.push(unchangedRangeDecoration);
        };
        const addNewText = (text: string, range: vscode.Range) => {
            const insertedRangeDecoration = vscode.window.createTextEditorDecorationType({
                after: {
                    contentText: text,
                    color: this.finalizedColorOptions.insertedTextColor,
                    backgroundColor: this.finalizedColorOptions.insertedTextBackground,
                    textDecoration: 'none; white-space: pre',
                    height: '100%',
                },
            });
            editor.setDecorations(insertedRangeDecoration, [range]);
            decorations.push(insertedRangeDecoration);
        };
        for (const line of newTextLines) {
            const oldLine = editor.document.lineAt(line.lineNumber);
            const endOfLine = new vscode.Range(oldLine.range.end, oldLine.range.end);
            const insertedRangesAtLine = insertedRangesByLine[String(line.lineNumber)] ?? [];
            let unchangedStart = line.range.start;
            for (const insertedRange of insertedRangesAtLine) {
                const startCharacter = unchangedStart.character;
                const endCharacter = insertedRange.start.character;
                if (endCharacter > startCharacter) {
                    addUnchangedText(line.text.slice(startCharacter, endCharacter), endOfLine);
                }
                addNewText(line.text.slice(insertedRange.start.character, insertedRange.end.character), endOfLine);
                unchangedStart = insertedRange.end;
            }
            const startCharacter = unchangedStart.character;
            const endCharacter = line.range.end.character;
            if (endCharacter > startCharacter) {
                addUnchangedText(line.text.slice(startCharacter, endCharacter), endOfLine);
            }
        }
        return decorations;
    }

    private addBacklight(editor: vscode.TextEditor, oldTextLines: vscode.TextLine[], linesDiff: LinesDiff) {
        const decorations: vscode.TextEditorDecorationType[] = [];
        const leftMostChangeColumn = min(
            linesDiff.changes.flatMap(inner =>
                inner.innerChanges?.map(change => {
                    if (change.originalRange.startLineNumber !== change.originalRange.endLineNumber) {
                        return 0;
                    }
                    return change.originalRange.startColumn - 1;
                })
            )
        );
        const leftMostCharacterColumn = min(oldTextLines.map(line => line.firstNonWhitespaceCharacterIndex));
        const leftMostColumn = min([leftMostChangeColumn, leftMostCharacterColumn]) ?? 0;
        const rightMostColumn = max(oldTextLines.map(line => getLineWidth(line.text))) ?? 0;

        const highlighterDecoration = vscode.window.createTextEditorDecorationType({
            backgroundColor: this.colorOptions.backlightColor,
        });

        editor.setDecorations(
            highlighterDecoration,
            oldTextLines.map(line =>
                new vscode.Range(line.lineNumber, leftMostColumn, line.lineNumber, line.text.length)
            )
        );

        decorations.push(highlighterDecoration);

        for (const textLine of oldTextLines) {
            const alignOffset = rightMostColumn - getLineWidth(textLine.text);
            if (alignOffset) {
                const buffer = vscode.window.createTextEditorDecorationType({
                    before: {
                        contentText: repeat(' ', alignOffset),
                        textDecoration: 'none; white-space: pre;',
                        height: '100%',
                    },
                });
                decorations.push(buffer);
                editor.setDecorations(buffer, [new vscode.Range(textLine.range.end, textLine.range.end)]);
            }
        }

        return decorations;
    }

    private addWhitespaceGutter(
        editor: vscode.TextEditor,
        startLine: number,
        endLine: number,
        newTextLineCount: number
    ) {
        const decorations: vscode.TextEditorDecorationType[] = [];
        const newEndLine = startLine + newTextLineCount - 1;
        const maxEndLine = Math.max(endLine, newEndLine);
        const oldMaxLineWidth = getMaxLineWidth(editor, startLine, endLine);
        const fullMaxLineWidth = getMaxLineWidth(editor, startLine, maxEndLine);
        const fullTextLines = range(startLine, maxEndLine + 1).map(line => editor.document.lineAt(line));
        for (const line of fullTextLines) {
            const gutterWidth = line.lineNumber <= endLine
                ? fullMaxLineWidth - oldMaxLineWidth + 1
                : fullMaxLineWidth - getLineWidth(line.text) + 1;
            const gutter = createEndOfLineWhitespace(gutterWidth);
            decorations.push(gutter);
            editor.setDecorations(gutter, [new vscode.Range(line.range.end, line.range.end)]);
        }
        return decorations;
    }

    private addPaddedTabGuide(editor: vscode.TextEditor, startLine: number, endLine: number, newTextLineCount: number) {
        const decorations: vscode.TextEditorDecorationType[] = [];
        const newEndLine = startLine + newTextLineCount - 1;
        const maxEndLine = Math.max(endLine, newEndLine);
        const fullTextLines = range(startLine, maxEndLine + 1).map(line => editor.document.lineAt(line));
        const firstLine = fullTextLines[0];
        const acceptGuide = createTabToAcceptGuide();
        const invisibleAcceptGuide = createInvisibleTabToAcceptGuide();
        const whitespaceGutter = createEndOfLineWhitespace(3);
        editor.setDecorations(
            acceptGuide,
            [new vscode.Range(firstLine.range.end, firstLine.range.end)]
        );
        editor.setDecorations(
            invisibleAcceptGuide,
            fullTextLines.slice(1).map(line => new vscode.Range(line.range.end, line.range.end))
        );
        editor.setDecorations(
            whitespaceGutter,
            fullTextLines.map(line => new vscode.Range(line.range.end, line.range.end))
        );
        decorations.push(acceptGuide, invisibleAcceptGuide, whitespaceGutter);
        return decorations;
    }

    private addDecorationEol(editor: vscode.TextEditor, line: number, decoration: vscode.TextEditorDecorationType) {
        const decorations: vscode.TextEditorDecorationType[] = [];
        const textLine = editor.document.lineAt(line);
        editor.setDecorations(
            decoration,
            [new vscode.Range(textLine.range.end, textLine.range.end)]
        );
        decorations.push(decoration);
        return decorations;
    }
}
