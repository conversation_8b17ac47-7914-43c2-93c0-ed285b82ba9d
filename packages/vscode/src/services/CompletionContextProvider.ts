import * as vscode from 'vscode';
import {inferLanguage} from '@/utils/languages';
import {NeighborSnippets, SnippetItem} from '@/common/prompt/NeighborSnippets';
import {iocContainer} from '@/iocContainer';
import {getPathRelativeToWorkspace} from '@/utils/workspace';
import {commentCode} from '@/utils/comments';
import {CrossFileContextAnalyzer} from './CrossFileContextAnalyzer';

export interface CompletionContextResolver {
    resolve: () => Promise<SnippetItem[] | undefined> | SnippetItem[] | undefined;
}

export class CompletionContextProvider {
    constructor(private readonly crossFileContextAnalyzer: CrossFileContextAnalyzer) {}

    provideCompletionContextResolver(
        document: vscode.TextDocument,
        position: vscode.Position
    ): CompletionContextResolver {
        let neighborSourceProvider: NeighborSnippets | null = null;
        const supportNeighborSource = this.checkNeighborSourceSupport();
        const supportImportSource = this.crossFileContextAnalyzer.checkSupport(document.fileName);
        // 提前开始获取 context
        if (supportNeighborSource) {
            neighborSourceProvider = this.prepareNeighborSource(document, position);
        }
        if (supportImportSource) {
            this.prepareImportSource(document);
        }
        return {
            resolve: async () => {
                try {
                    const neighborSource = supportNeighborSource && neighborSourceProvider
                        ? this.resolveNeighborSource(neighborSourceProvider, document)
                        : undefined;
                    const importSource = supportImportSource
                        ? await this.resolveImportSource(document, position)
                        : undefined;
                    // NOTE: 目前服务没有用 neighbor source，因此 import source 优先在前面，以免 finalize 时被省略
                    const result = this.finalize(
                        ...(importSource ?? []),
                        ...(neighborSource ?? [])
                    );
                    return result;
                }
                catch (e) {
                    console.error('(context): CompletionContextResolver failed to retrieve context');
                    return undefined;
                }
            },
        };
    }

    private checkNeighborSourceSupport() {
        return true;
    }

    private prepareNeighborSource(document: vscode.TextDocument, position: vscode.Position) {
        const neighborSourceProvider = new NeighborSnippets(
            iocContainer,
            document,
            position,
            {importSource: false, neighborSource: true}
        );
        return neighborSourceProvider;
    }

    private resolveNeighborSource(
        neighborSnippetProvider: NeighborSnippets,
        document: vscode.TextDocument
    ): SnippetItem[] | undefined {
        const neighborSource = neighborSnippetProvider.getFixedWindowSnippets();
        const language = inferLanguage(document.uri.fsPath);
        return neighborSource?.map(item => {
            const filePath = getPathRelativeToWorkspace(vscode.Uri.parse(item.filePath));
            return {
                ...item,
                content: commentCode(item.content, language),
                filePath,
            };
        });
    }

    private prepareImportSource(document: vscode.TextDocument) {
        this.crossFileContextAnalyzer.prepare(document.uri.fsPath);
    }

    private async resolveImportSource(document: vscode.TextDocument, position: vscode.Position) {
        const symbolDefs = await this.crossFileContextAnalyzer.getReferencedSymbolsContextFromCache(
            document.uri.fsPath,
            document.offsetAt(position)
        );
        const language = inferLanguage(document.uri.fsPath);
        const importSource: SnippetItem[] = symbolDefs.map(item => {
            const filePath = getPathRelativeToWorkspace(vscode.Uri.parse(item.relevantFile));
            return {
                content: commentCode(item.snippet, language),
                filePath,
                type: 'IMPORT_SOURCE',
            };
        });
        return importSource;
    }

    private finalize(...snippetItems: SnippetItem[]) {
        let accumulatedLength = 0;
        let topN = 0;
        for (const item of snippetItems) {
            accumulatedLength += item.content.length;
            if (accumulatedLength > 4000) {
                break;
            }
            topN++;
        }
        return snippetItems.slice(0, topN);
    }
}
