/* eslint-disable @typescript-eslint/no-unused-vars */
import {injectable} from 'inversify';
import {ITimeTracker} from './types';

@injectable()
export class EmptyTimeTracker implements ITimeTracker {
    isCodeByComateNonSerial = true;

    recordChatStart(_recordId: number): void {
        // do nothing
    }
    recordChatEnd(_uuid: string, _content: string): void {
        // do nothing
    }
    recordCompletionStart(): void {
        // do nothing
    }
    recordCompletionEnd(_content: string): void {
        // do nothing
    }
    bindChatId(_uuid: string, _recordId: number): void {
        // do nothing
    }
    dispose() {
        // do nothing
    }
}
