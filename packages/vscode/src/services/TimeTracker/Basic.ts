/* eslint-disable complexity, max-depth */
import {inject, injectable} from 'inversify';
import * as vscode from 'vscode';
import {isEqual} from 'lodash';
import {track} from '../../api';
import {UserService} from '../UserService';
import {VisualStateManagerProvider} from '../VisualStateManagerProvider';
import {ITimeTracker} from './types';

@injectable()
export class BasicTimeTracker implements ITimeTracker {
    private disposables: vscode.Disposable[] = [];
    private readonly chatStartTimeMapping = new Map<number, number>();
    private readonly uuidRecordIdMapping = new Map<string, number>();
    private comateStartRecommendTime = 0;

    private userEditStartTime = Date.now();
    private userEditLine = 0;
    private nowEditFileName = '';
    isCodeByComateNonSerial = false;

    constructor(
        @inject(UserService) private readonly userService: UserService,
        @inject(VisualStateManagerProvider) readonly visualStateManagerProvider: VisualStateManagerProvider
    ) {
        vscode.workspace.onDidChangeTextDocument((event: vscode.TextDocumentChangeEvent) => {
            const uri = vscode.window.activeTextEditor?.document?.uri?.fsPath;
            const loading = uri ? this.visualStateManagerProvider.getLoadingRange(uri) : false;

            if (!loading) {
                this.recordUserCoding(event);
            }
        });
    }

    recordChatStart(recordId: number) {
        const now = Date.now();
        this.chatStartTimeMapping.set(recordId, now);
    }

    bindChatId(uuid: string, recordId: number) {
        this.uuidRecordIdMapping.set(uuid, recordId);
    }

    async recordChatEnd(uuid: string, content: string) {
        const [username] = await this.userService.getCurrentUser().catch(() => ['unknown']);
        const lineNumber = content.split('\n').length;
        const recordId = this.uuidRecordIdMapping.get(uuid);
        if (recordId !== undefined) {
            const startTime = this.chatStartTimeMapping.get(recordId);
            const elapsed = startTime && (Date.now() - startTime);
            // 过滤掉耗时不足 0.4s 或超过 3min 的打点
            if (elapsed && elapsed > 400 && elapsed < 180000) {
                track({
                    username: username,
                    category: 'codingDuration',
                    action: 'codeByComateNonSerial',
                    operation: {
                        time: elapsed,
                        lineNumber: lineNumber,
                    },
                });
            }
        }
    }

    recordCompletionStart() {
        this.comateStartRecommendTime = Date.now();
    }

    async recordCompletionEnd(content: string) {
        const [username] = await this.userService.getCurrentUser().catch(() => ['unknown']);
        const lineNumber = content.split('\n').length;
        const elapsed = Date.now() - this.comateStartRecommendTime;
        // 过滤掉耗时不足 0.2s 或超过 3min 的打点
        if (elapsed > 200 && elapsed < 180000) {
            track({
                username: username,
                category: 'codingDuration',
                action: 'codeByComateCompletion',
                operation: {
                    time: elapsed,
                    lineNumber: lineNumber,
                },
            });
        }
    }

    private async recordUserCoding(event: vscode.TextDocumentChangeEvent) {
        if (event.contentChanges.length <= 0) {
            return;
        }
        if (this.isCodeByComateNonSerial) {
            this.isCodeByComateNonSerial = false;
            this.userEditStartTime = Date.now();
            return;
        }
        if (event.document.uri.scheme !== 'file') {
            return;
        }
        if (['Log'].includes(event.document.languageId)) {
            return;
        }
        for (const change of event.contentChanges) {
            // 如果该范围起始与结束相等，则认为是用户手动插入的内容，否则可能是 Comate 续写采纳或删除字符等
            const isInsert = isEqual(change.range.start, change.range.end);
            const isDifferentLocation = change.range.start.line !== this.userEditLine
                || event.document.fileName !== this.nowEditFileName;
            if (isInsert && isDifferentLocation) {
                const now = Date.now();
                const elapsed = now - this.userEditStartTime;
                // 过滤掉耗时不足 0.4s 或超过 3min 的打点
                if (elapsed > 400 && elapsed < 180000) {
                    const [username] = await this.userService.getCurrentUser().catch(() => ['unknown']);
                    track({
                        username: username,
                        category: 'codingDuration',
                        action: 'codeByUser',
                        operation: {
                            time: elapsed,
                        },
                    });
                    this.userEditStartTime = now;
                    this.userEditLine = change.range.start.line;
                    this.nowEditFileName = event.document.fileName;
                }
            }
        }
    }

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
