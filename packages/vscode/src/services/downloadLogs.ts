import {basename, join} from 'path';
import {homedir} from 'os';
import * as vscode from 'vscode';
import AdmZ<PERSON> from 'adm-zip';
import {noop} from 'lodash';
import {ensureDirectoryExist} from '@comate/plugin-shared-internals';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {COMATE_HOME_FOLDER} from '@/utils/fs';
import {iocContainer} from '@/iocContainer';
import {getOutputChannelNames} from './outputChannelRegistry';
import {VSCodeConfigProvider} from './ConfigProvider';

const builtinLogsUri: vscode.Uri[] = [
    // Window
    vscode.Uri.from({scheme: 'output', path: 'rendererLog'}),
    // Extension Host
    vscode.Uri.from({scheme: 'output', path: 'exthost'}),
    // Extension Host (Remote)
    vscode.Uri.from({scheme: 'output', path: 'remoteexthost'}),
];

async function guessOutputChannelFiles(): Promise<vscode.Uri[]> {
    const context = await getExtensionContextAsync();
    const extensionId = `${context.extension.packageJSON.publisher}.${context.extension.packageJSON.name}`;
    const result: vscode.Uri[] = [];
    const outputChannelNames = getOutputChannelNames();
    for (const name of outputChannelNames) {
        for (let i = 0; i < outputChannelNames.length; i++) {
            result.push(
                vscode.Uri.from({
                    scheme: 'output',
                    path: `extension-output-${extensionId}-#${i + 1}-${name}`,
                })
            );
        }
    }
    return result;
}

async function collectEnvironmentInfo() {
    const context = await getExtensionContextAsync();
    return {
        vscode: {
            version: vscode.version,
            name: vscode.env.appName,
            uiKind: vscode.env.uiKind,
        },
        process: {
            platform: process.platform,
            arch: process.arch,
            release: process.release,
        },
        comate: {
            version: context.extension.packageJSON.version,
        },
    };
}

export async function downloadLogs() {
    await ensureDirectoryExist(COMATE_HOME_FOLDER);
    const logsZipFile = join(COMATE_HOME_FOLDER, `logs-${Date.now()}.zip`);
    const zip = new AdmZip();
    // 1. Output logs
    const outputChannelUris = await guessOutputChannelFiles();
    for (const uri of [...outputChannelUris, ...builtinLogsUri]) {
        try {
            const document = await vscode.workspace.openTextDocument(uri);
            zip.addFile(basename(uri.fsPath), Buffer.from(document.getText(), 'utf-8'));
        }
        catch (e) {
            // NOTE: 有些uri不存在是预期内，无需报错
            continue;
        }
    }
    // 2. comate-engine logs
    const engineLogsFolder = join(homedir(), '.comate-engine', 'log');
    zip.addLocalFolder(engineLogsFolder, 'comate-engine');
    // 3. Environment info
    const environment = await collectEnvironmentInfo();
    zip.addFile('environment.json', Buffer.from(JSON.stringify(environment, null, 4), 'utf-8'));
    // 4. Extension settings
    const configProvider = iocContainer.get(VSCodeConfigProvider);
    const localConfigs = configProvider.getLocalConfigs();
    zip.addFile('settings.json', Buffer.from(JSON.stringify(localConfigs, null, 4), 'utf-8'));
    await zip.writeZipPromise(logsZipFile);
    const choice = await vscode.window.showInformationMessage('日志文件已打包', '查看');
    if (choice === '查看') {
        vscode.commands.executeCommand('revealFileInOS', vscode.Uri.file(logsZipFile)).then(noop, () => {
            vscode.commands.executeCommand('vscode.open', vscode.Uri.file(logsZipFile));
        });
    }
}

(async () => {
    const context = await getExtensionContextAsync();
    context.subscriptions.push(
        vscode.commands.registerCommand('baidu.comate.downloadLogs', downloadLogs)
    );
})();
