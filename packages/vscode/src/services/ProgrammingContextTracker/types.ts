import {Position, Diagnostic} from 'vscode-languageserver-types';
import {URI} from 'vscode-uri';
import {DiagnosticInfo, EditCode, RunTerminalCommand} from '@/api';

export interface AgumentedDiagnosticInfo extends DiagnosticInfo {
    raw: Diagnostic;
}

export type ProgrammingAction =
    | EditCode
    | RunTerminalCommand
    | AgumentedDiagnosticInfo;

export interface IProgrammingContextTracker {
    getContext(uri?: URI, position?: Position, maxEntries?: number): Promise<ProgrammingAction[]>;
    get lastEditTimestamp(): number;
}
