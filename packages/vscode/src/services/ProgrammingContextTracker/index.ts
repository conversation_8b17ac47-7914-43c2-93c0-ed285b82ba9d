import * as path from 'path';
import dayjs from 'dayjs';
import {orderBy} from 'lodash';
import {URI} from 'vscode-uri';
import {Position} from 'vscode-languageserver-types';
import {IWorkspaceProvider} from '@/common/lsp/types';
import {hideSensitiveContent} from '@/utils/common';
import {ILogicalEditTracker} from '../LogicalEditTracker/types';
import {ITerminalCommandTracker} from '../TerminalCommandTracker/types';
import {IDiagnosticTracker} from '../DiagnosticTracker/types';
import {IProgrammingContextTracker, ProgrammingAction} from './types';

export class ProgrammingContextTracker implements IProgrammingContextTracker {
    constructor(
        private readonly workspaceProvider: IWorkspaceProvider,
        private readonly editTracker: ILogicalEditTracker,
        private readonly terminalCommandTracker: ITerminalCommandTracker,
        private readonly diagnosticTracker: IDiagnosticTracker
    ) {
    }

    get lastEditTimestamp(): number {
        return this.editTracker.lastEditTimestamp;
    }

    async getContext(uri?: URI, position?: Position, maxEntries: number = 50): Promise<ProgrammingAction[]> {
        const [edits, commands] = await Promise.all([
            this.editTracker.getEdits(uri?.fsPath),
            this.terminalCommandTracker.getCommands(),
        ]);

        const actions: Array<ProgrammingAction & {timestamp: number}> = [];

        for (const edit of edits) {
            actions.push({
                name: 'edit_diff',
                path: this.workspaceProvider.asRelativePath(edit.uri),
                content: hideSensitiveContent(edit.diff),
                // eslint-disable-next-line camelcase
                modified_time: dayjs(edit.modifiedTime).format('YYYY-MM-DD HH:mm:ss'),
                timestamp: edit.modifiedTime,
            });
        }

        const belongingWorkspace = uri ? this.workspaceProvider.getWorkspaceFolder(uri.fsPath) : undefined;
        const belongingWorkspaceUri = belongingWorkspace?.uri ? URI.parse(belongingWorkspace.uri) : undefined;

        for (const command of commands) {
            const relativePath = belongingWorkspaceUri && command.cwd
                ? path.relative(belongingWorkspaceUri.fsPath, command.cwd)
                : null;
            actions.push({
                name: 'run_cmd',
                content: hideSensitiveContent(command.command),
                path: relativePath,
                output: command.output,
                // eslint-disable-next-line camelcase
                modified_time: dayjs(command.startTime).format('YYYY-MM-DD HH:mm:ss'),
                timestamp: command.startTime,
            });
        }

        if (uri && position) {
            const diagnostics = this.diagnosticTracker.getDiagnostics(uri, position);
            for (const item of diagnostics) {
                actions.push({
                    name: 'diagnostic_info',
                    content: item.message,
                    path: this.workspaceProvider.asRelativePath(uri.fsPath),
                    output: JSON.stringify(item),
                    // eslint-disable-next-line camelcase
                    modified_time: dayjs(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
                    timestamp: Date.now(),
                    raw: item,
                });
            }
        }

        const orderedActions = orderBy(actions, 'timestamp');

        return orderedActions.slice(Math.max(0, orderedActions.length - maxEntries));
    }
}
