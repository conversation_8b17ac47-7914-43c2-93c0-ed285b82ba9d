import * as vscode from 'vscode';
import {Actions, Message, MessageStatus, MessageType, Metadata, AssistantMessage} from '@shared/protocols';
import {ChatModel} from './ChatModel';
import {chatActionDelayedReporter} from './chatActionDelayedReporter';
import {StreamingResponse, TextResponse} from './TextResponse';

interface ConversationHandler {
    updateView: (uuid?: string) => void;
}

export class Conversation {
    question: Message;

    constructor(
        private readonly sessionUuid: string,
        private readonly chatModel: ChatModel,
        private readonly handler: ConversationHandler,
        readonly type: MessageType,
        query: string,
        code?: string
    ) {
        this.chatModel.cancelAllMessages(sessionUuid);
        this.question = this.chatModel.addMessage(
            sessionUuid,
            {
                role: 'user',
                content: query,
                code,
                type,
                status: 'success',
                stream: false,
            }
        );
    }

    addResponse(
        kind: 'text' | 'stream',
        content: string,
        status: MessageStatus,
        actions?: Actions,
        metadata?: Metada<PERSON>,
        agent?: string
    ) {
        this.chatModel.cancelAllMessages(this.sessionUuid);
        const cancelTokenSource = new vscode.CancellationTokenSource();
        const message = this.chatModel.addMessage(
            this.sessionUuid,
            {
                role: 'assistant',
                content,
                status,
                replyTo: this.question.id,
                type: this.type,
                actions,
                cancelTokenSource,
                stream: kind === 'stream',
                metadata,
                agent,
            } as AssistantMessage);
        this.handler.updateView();
        if (kind === 'stream') {
            return new StreamingResponse(message, this);
        }
        return new TextResponse(message, this);
    }

    // TODO WIP 不一定要单拎出来
    pluginAddResponse(kind: 'text' | 'stream', content: string, status: MessageStatus, {
        name,
        icon,
        extra,
    }: {
        name: string;
        icon: string;
        extra: any;
    }, actions?: Actions) {
        this.chatModel.cancelAllMessages(this.sessionUuid);
        const cancelTokenSource = new vscode.CancellationTokenSource();
        const message = this.chatModel.addMessage(
            this.sessionUuid,
            {
                role: 'assistant',
                content,
                status,
                replyTo: this.question.id,
                type: this.type,
                actions,
                cancelTokenSource,
                stream: kind === 'stream',
                name,
                icon,
                extra,
            }
        );

        this.handler.updateView();
        // 用户主动取消，触发上报的逻辑
        cancelTokenSource.token.onCancellationRequested(() => {
            chatActionDelayedReporter.execute(String(message.id));
        });
        if (kind === 'stream') {
            return new StreamingResponse(message, this);
        }
        return new TextResponse(message, this);
    }

    getMessageById(messageId: number) {
        return this.chatModel.getMessageById(this.sessionUuid, messageId);
    }

    updateView(uuid?: string) {
        return this.handler.updateView(uuid);
    }
}
