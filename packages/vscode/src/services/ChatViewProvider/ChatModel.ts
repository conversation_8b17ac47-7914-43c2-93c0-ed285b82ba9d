/* eslint-disable complexity */
import {DehydratedMessage, Message} from '@shared/protocols';
import * as vscode from 'vscode';
import {ACTION_CHAT_SESSION_FIND, ACTION_CHAT_SESSION_SAVE, ChatSessionDetail} from '@comate/plugin-shared-internals';
import debounce from 'lodash/debounce';
import {iocContainer} from '@/iocContainer';
import {KernelProvider} from '../KernelProvider';

type MessageChangeHandler = () => void;

export class ChatModel {
    private readonly _messagesMap: Map<string, Message[]> = new Map();
    private _id = Date.now();
    private readonly _messagesChangeHandlers: MessageChangeHandler[] = [];
    private readonly _debouncedStoreFunctions: Map<string, () => void> = new Map();

    private sendToKernel<T>(method: string, payload: any) {
        const kernelProvider = iocContainer.get<KernelProvider>(KernelProvider);
        return kernelProvider.sendRequest(method, payload) as T;
    }

    getMessagesSync(sessionUuid: string) {
        const sessionMessages = this._messagesMap.get(sessionUuid);
        if (!sessionMessages) {
            this._messagesMap.set(sessionUuid, []);
        }
        return this._messagesMap.get(sessionUuid)!;
    }

    async getMessages(sessionUuid: string) {
        const sessionMessages = this._messagesMap.get(sessionUuid);

        if (!sessionMessages) {
            // eslint-disable-next-line @typescript-eslint/init-declarations
            let session: {payload: ChatSessionDetail} | undefined;
            try {
                session = await Promise.race(
                    [
                        this.sendToKernel<{payload: ChatSessionDetail} | undefined>(
                            ACTION_CHAT_SESSION_FIND,
                            {sessionUuid}
                        ),
                        new Promise<undefined>(resolve => {
                            setTimeout(
                                () => {
                                    resolve(undefined);
                                },
                                3000
                            );
                        }),
                    ]
                );
            }
            catch (e) {
                // do nothing
            }

            this._messagesMap.set(sessionUuid, session?.payload?.messages || []);
        }

        return this._messagesMap.get(sessionUuid) as Message[];
    }

    onDidChangeMessages(handler: MessageChangeHandler) {
        this._messagesChangeHandlers.push(handler);
    }

    private fireMessagesChangeEvent() {
        for (const handler of this._messagesChangeHandlers) {
            handler();
        }
    }

    /**
     * 获取没有event handlers的messages
     */
    async getDehydratedMessages(sessionUuid: string): Promise<DehydratedMessage[]> {
        const messages = await this.getMessages(sessionUuid);
        return messages.map(message => {
            const tmp = {...message};
            delete tmp.actions;
            delete tmp.onCancel;
            delete tmp.cancelTokenSource;
            return {
                ...tmp,
                sessionUuid,
                actions: Object.keys(message.actions ?? {}),
            };
        });
    }

    async storeMessages_(sessionUuid: string) {
        const messages = await this.getDehydratedMessages(sessionUuid);
        const workspaces = vscode.workspace.workspaceFolders;
        const rootPath = workspaces?.[0]?.uri.fsPath;
        // eslint-disable-next-line @typescript-eslint/init-declarations
        let storeSession: {payload: ChatSessionDetail} | undefined;
        try {
            storeSession = await Promise.race(
                [
                    this.sendToKernel<{payload: ChatSessionDetail} | undefined>(
                        ACTION_CHAT_SESSION_FIND,
                        {sessionUuid}
                    ),
                    new Promise<undefined>(resolve => {
                        setTimeout(
                            () => {
                                resolve(undefined);
                            },
                            3000
                        );
                    }),
                ]
            );
        }
        catch (e) {
            // do nothing
        }

        const title = (messages[0]?.content || messages[0]?.code) && typeof messages[0]?.content === 'string'
            ? ((messages[0]?.content || '') + (messages[0]?.code || ''))
            : (
                typeof messages[1]?.content === 'string'
                    ? ((messages[1]?.content || '') + (messages[1]?.code || ''))
                    : messages[1]?.content
            );
        const session: ChatSessionDetail = {
            isAgent: false,
            ...storeSession?.payload,
            ctime: storeSession?.payload?.ctime || Date.now(),
            workspaceDirectory: rootPath || '',
            sessionUuid,
            messages: messages,
            title,
            utime: Date.now(),
        };
        this.sendToKernel(ACTION_CHAT_SESSION_SAVE, session);
    }

    storeMessages(sessionUuid: string) {
        if (!this._debouncedStoreFunctions.has(sessionUuid)) {
            this.storeMessages_(sessionUuid);
            const debouncedFunction = debounce(
                () => {
                    this.storeMessages_(sessionUuid);
                },
                1500
            );

            this._debouncedStoreFunctions.set(sessionUuid, debouncedFunction);
        }

        const debouncedFunction = this._debouncedStoreFunctions.get(sessionUuid);
        if (debouncedFunction) {
            debouncedFunction();
        }
    }

    addMessage(sessionUuid: string, content: Omit<Message, 'id' | 'timestamp'>): Message {
        const message: Message = {
            ...content,
            id: this._id++,
            timestamp: Date.now(),
        };
        const messages = this._messagesMap.get(sessionUuid);

        if (!messages) {
            this._messagesMap.set(sessionUuid, []);
        }
        this.getMessagesSync(sessionUuid).push(message);
        this.fireMessagesChangeEvent();

        return message;
    }

    getMessageById(sessionUuid: string, messageId: number) {
        return this.getMessagesSync(sessionUuid).find(v => v.id === messageId);
    }

    updateMessage(sessionUuid: string, id: number, partialMessage: Partial<Message>) {
        const idx = this.getMessagesSync(sessionUuid).findIndex(item => item.id === id);
        const message = this.getMessagesSync(sessionUuid)[idx];

        if (idx === -1) {
            return;
        }

        const processedMessage = {
            ...message,
            ...partialMessage,
            extra: {
                ...message?.extra,
                ...partialMessage?.extra,
            },
        } as Message;
        this.getMessagesSync(sessionUuid)[idx] = processedMessage;
        this.fireMessagesChangeEvent();
    }

    cancelAllMessages(sessionUuid: string) {
        for (const message of this.getMessagesSync(sessionUuid)) {
            if (message.status === 'inProgress') {
                message.status = 'canceled';
                message.cancelTokenSource?.cancel();
            }
        }
    }

    cancelMessage(sessionUuid: string, id: number) {
        const message = this.getMessagesSync(sessionUuid).find(item => item.id === id);
        if (message) {
            message.cancelTokenSource?.cancel();
            message.status = 'canceled';
        }
    }

    clearMessages(sessionUuid: string) {
        vscode.commands.executeCommand('setContext', 'baidu.comate.context.helpSendEvent', false);
        this.getMessagesSync(sessionUuid).length = 0;
        this.storeMessages(sessionUuid);
        this.fireMessagesChangeEvent();
    }

    // eslint-disable-next-line complexity
    getLatestMessages(sessionUuid: string, count: number, order: Record<number, number>) {
        const questions: Message[] = [];
        type QuestionId = number;
        const answers = new Map<QuestionId, Message[]>();
        for (const msg of this.getMessagesSync(sessionUuid)) {
            if (msg.role === 'user') {
                questions.push(msg);
            }
            else if (msg.role === 'assistant' && typeof msg.replyTo === 'number') {
                const previous = answers.get(msg.replyTo) ?? [];
                answers.set(msg.replyTo, [...previous, msg]);
            }
        }

        const histories: Message[] = [];
        for (const question of questions) {
            const currentAnswerOrder = order[question.id];
            const ans = answers.get(question.id) ?? [];
            const currentAnswer = typeof currentAnswerOrder === 'number'
                ? ans[currentAnswerOrder]
                : ans[ans.length - 1];
            if (currentAnswer && currentAnswer.status === 'success' && currentAnswer.content) {
                histories.push(question, currentAnswer);
            }
        }

        if (count > 0) {
            return histories.slice(-count);
        }
        return [];
    }
}
