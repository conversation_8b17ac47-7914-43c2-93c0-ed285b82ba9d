/* eslint-disable max-lines */
import {randomUUID} from 'node:crypto';
import * as vscode from 'vscode';
import platform from '@shared/platform';
import {
    EventMessage,
    Feature,
    MessageType,
    AgentListWithId,
    CommandListWithId,
    KnowledgeList,
    BuiltinAgent,
    LicenseValidity,
    Suggestion,
    AutoWorkKnowledgeType,
    DehydratedMessage,
    UserQueryPromptType,
    InputBoxMessageHistory,
    ConfigItem,
    SearchAPIParamType,
    TrackAPIParamType,
    APIItem,
    UnityRequestType,
    HttpUnityResponseType,
    WebviewAgentConversationType,
    PromptTemplateList,
    CreatePromptTemplatePayload,
} from '@shared/protocols';
import {SlashType, UT} from '@shared/constants';
import {injectable, inject} from 'inversify';
import {debounce, zipObject} from 'lodash';
import {
    AgentPayload,
    CustomCommandPayload,
    LicenseFullDetail,
    ACTION_CHAT_SESSION_LIST,
    ACTION_CHAT_SESSION_DELETE,
    ACTION_COMATE_PLUS_CHAT_CANCEL,
} from '@comate/plugin-shared-internals';
import {iocContainer} from '@/iocContainer';
import {getExtensionContextAsync} from '@/utils/extensionContext';
import {codeExplainDefaultConfig, docStringDefaultConfig, inlineCommentDefaultConfig} from '@/utils/defaultConfig';
import {postSurveyContent} from '@/api/survey';
import {CURRENT_LOGIN_AVATAR, login} from '@/utils/login';
import {isInternal} from '@/utils/features';
import {TYPES} from '@/inversify.config';
import {getInternalUserInfoMemorized} from '@/api/user';
import {L10n} from '@/common/L10nProvider/L10n';
import {getLatestActivityInfo} from '@/api/license';
import {ChatProviderText} from '@/common/L10nProvider/constants';
import {getLicenseFullDetail} from '@/api';
import {vscodeCommands} from '@/utils/vscodeComands';
import {promiseWithResolvers} from '@/utils/promiseWithResolvers';
import {getDeviceUUIDThrottled} from '@/utils/deviceUUID';
import {
    COMATE_CONFIG_PREFIX,
    CMD_HANDLE_INPUT_FOCUS,
    BANNER_VERSION_CONFIG_KEY,
    SURVEY_VERSION_CONFIG_KEY,
    INPUTBOX_HISTORY_KEY,
    COMMAND_LOGOUT,
    RegisteredCommand,
} from '../../constants';
import {CMD_INVOKE_NL2CODE} from '../NL2CodeProvider/constants';
import {error} from '../../common/outputChannel';
import {
    joinWorkspacePath,
    openDocument,
    reviveFullUriToWorkspace,
    serializeChatKnowledges,
} from '../../utils/workspace';
import {getHtmlForWebview} from '../../utils/getHtmlForWebview';
import {
    getFilesOrFoldersByKeywordAndType,
    recordRecentSelectedFiles,
    getOpenAndRecentFiles,
    getOpenAndRecentFolders,
    workspaceStatus,
} from '../../utils/files';
import {UserService} from '../UserService';
import {FeatureFlags} from '../FeatureFlags';
import {PluginConfigs} from '../ComatePlusChatSession';
import {AutoComateConversation} from '../AutoComateChatSession/Conversation';
import {
    CMD_BUILTIN_COMMAND,
    CMD_COMATE_PLUS_START_COMMAND,
    CMD_COMATE_PLUS_START_SESSION,
    CMD_LICENSE_EXPIRED,
    CMD_WEBVIEW_INIT,
    CMD_ADD_CACHE,
    CMD_GET_CACHE,
    CMD_COMATE_PLUS_CREATE_PROMPTTEMPLATE,
    CMD_COMATE_PLUS_DELETE_PROMPTTEMPLATE,
    CMD_COMATE_PLUS_EDIT_PROMPTTEMPLATE,
} from '../ComatePlusChatSession/constants';
import {WebviewMessageHandler} from '../WebviewMessageHandler';
import {VSCodeConfigProvider, getBasicConfigSectionName} from '../ConfigProvider';
import {ILogger} from '../Logger/types';
import {LogCategory, LogUploaderEvent, LogUploaderProvider} from '../LogUploaderProvider';
import {CustomizeProvider} from '../CustomizeProvider';
import {CMD_USER_GUIDE_FETCH, CMD_USER_GUIDE_CHANGE, CMD_KNOWLEDGE_TYPE_GUIDE_FETCH} from '../UserGuideProvider';
import {CMD_SEARCH_API_BY_KEYWORD, CMD_TRACK_CLICK_SEARCHED_API} from '../SmartAPIProvider';
import {CMD_RECREATE_INDEX} from '../EmbeddingsService/controller';
import {defaultLicenseFullDetail} from '../PartialPrivatizationProvider';
import {getUnityRequestResponse} from '../AutoComateChatSession/getUnityRequestResponse';
import {
    CMD_GET_AGENT_CONVERSATIONS,
    CMD_ADD_AGENT_TASK_CONVERSATION,
    CMD_ADD_SET_FOREGROUND,
    CMD_ADD_NEW_MESSAGE,
    CMD_ADD_UPDATE_AGENT_STATUS,
    CMD_ADD_GET_AGENT_STATUS,
} from '../AgentProvider/constants';

import {downloadLogs} from '../downloadLogs';
import {CMD_DEFAULT_MODEL, CMD_UPDATE_DEFAULT_MODEL} from '../ModelSelector';
import {KernelProvider} from '../KernelProvider';
import {ChatModel} from './ChatModel';
import {Conversation} from './Conversation';

const COLOR_THEME_MAPPING = {
    [vscode.ColorThemeKind.Light]: 'light',
    [vscode.ColorThemeKind.Dark]: 'dark',
    [vscode.ColorThemeKind.HighContrast]: 'dark',
    [vscode.ColorThemeKind.HighContrastLight]: 'light',
};

const COMMAND_LAST_SESSION_UUID = 'baidu.comate.lastSessionUuid';

@injectable()
export class ChatViewProvider implements vscode.WebviewViewProvider, vscode.Disposable {
    static readonly viewId = 'comate.views.chat';
    private disposables: vscode.Disposable[] = [];
    private webViewMessageHandler?: WebviewMessageHandler;
    private readonly chatModel: ChatModel = new ChatModel();
    private readonly _onDidClearSession = new vscode.EventEmitter<void>();
    /** 因为webview挂载会晚于插件，插件需要在webview实例化完监听事件，估其余provider可以先将事件注册到这里 */
    private readonly unAttachedWebViewListener: Array<{action: EventMessage, listener: (...args: any[]) => any}> = [];
    /** panel创建会和初始化事件竞态 实例未创建时先暂存一下 */
    private readonly unAttachedPanelListener: Array<{scope: string, data: any}> = [];
    private readonly logger = iocContainer.get<ILogger>(TYPES.ILogger);
    readonly onDidClearSession = this._onDidClearSession.event;
    private panel?: vscode.WebviewPanel;
    private webView?: vscode.WebviewView;
    /** 表示 webview 是否完成初始化，侧边栏打开且事件监听加上了  */
    private isWebviewInitialized = false;
    private engineInited = false;
    private readonly isEngineInitialized = promiseWithResolvers<void>();
    private defaultChatSessionUuid: string;
    chatCompletionList: [
        AgentListWithId[],
        CommandListWithId[],
        KnowledgeList[],
        PromptTemplateList[],
    ] = [[], [], [], []];
    pluginConfigs: PluginConfigs = {
        comate: {
            enabled: false,
            config: {
                ...docStringDefaultConfig,
                ...codeExplainDefaultConfig,
                ...inlineCommentDefaultConfig,
            },
        },
    };

    constructor(
        @inject(UserService) private readonly userService: UserService,
        @inject(VSCodeConfigProvider) private readonly configProvider: VSCodeConfigProvider,
        @inject(FeatureFlags) private readonly featureFlags: FeatureFlags,
        // @inject(KernelProvider) private readonly kernelProvider: KernelProvider,
        @inject(LogUploaderProvider) private readonly logUploadProvider: LogUploaderProvider
    ) {
        const lastSessionUuid = vscode
            .workspace
            .getConfiguration(COMATE_CONFIG_PREFIX)
            .get('lastSessionUuid') as string;
        if (lastSessionUuid) {
            this.defaultChatSessionUuid = lastSessionUuid;
            (async () => {
                await this.waitingEngineInit();
                const messages = await this.chatModel.getDehydratedMessages(lastSessionUuid);
                this.sendDataToWebview(
                    EventMessage.MessagesRefreshEvent,
                    messages
                );
            })();
        }
        else {
            this.defaultChatSessionUuid = randomUUID();
            vscode.commands.executeCommand(
                'setContext',
                COMMAND_LAST_SESSION_UUID,
                this.defaultChatSessionUuid
            );
        }
        this.chatModel.onDidChangeMessages(
            async () => {
                await this.waitingEngineInit();
                const messages = await this.chatModel.getDehydratedMessages(this.defaultChatSessionUuid);
                vscode.commands.executeCommand(
                    'setContext',
                    'baidu.comate.context.messagesCount',
                    messages.length
                );
            }
        );
        this.disposables.push(
            vscode.window.registerWebviewViewProvider(
                ChatViewProvider.viewId,
                this,
                {
                    webviewOptions: {
                        retainContextWhenHidden: true,
                    },
                }
            ),
            vscode.window.onDidChangeActiveColorTheme(this.handleColorThemeChange.bind(this)),
            vscode.commands.registerCommand('baidu.comate.historyChat', () => {
                this.webViewMessageHandler?.send(EventMessage.HistoryChatEvent);
            }),
            vscode.commands.registerCommand('baidu.comate.moreOperations', () => {
                this.webViewMessageHandler?.send(EventMessage.MoreOperationsEvent);
            }),
            vscode.commands.registerCommand('baidu.comate.createNewChat', () => {
                this.webViewMessageHandler?.send(EventMessage.CreateNewChatEvent, this.messages.length);
                this.logUploadProvider.logUserAction({
                    category: LogCategory.createNewChat,
                    content: `Creating new chat at ${new Date().toISOString()}`,
                });
            }),
            vscode.commands.registerCommand(
                'baidu.comate.showChatPanel',
                async (opts?: {activeTabKey?: string, source?: string}) => {
                    this.logUploadProvider.logUserAction({
                        category: LogCategory.showChatPanel,
                        source: opts?.source ?? 'command',
                        content: this.webView?.visible,
                    });
                    await vscode.commands.executeCommand('comate.views.chat.focus');
                    // 不加setTimeout右键自然语言生成代码触发时的focus不生效
                    // 虽然加了偶尔还是先focus然后闪一下消失
                    setTimeout(
                        () => {
                            this.webViewMessageHandler?.send(EventMessage.InputFocusEvent, opts);
                        },
                        100
                    );
                }
            ),
            vscode.commands.registerCommand(
                'baidu.comate.toggleChatPanel',
                async () => {
                    const visible = this.getChatViewVisible();

                    if (visible) {
                        // 关闭侧边栏
                        await vscode.commands.executeCommand('workbench.action.closeSidebar');
                        setTimeout(
                            () => {
                                // 将焦点返回到编辑区
                                vscode.commands.executeCommand('workbench.action.focusActiveEditorGroup');
                            },
                            100
                        );
                    }
                    else {
                        await vscode.commands.executeCommand('baidu.comate.showChatPanel');
                    }
                }
            ),
            vscode.workspace.onDidChangeConfiguration(e => {
                if (e.affectsConfiguration(COMATE_CONFIG_PREFIX)) {
                    this.onDidChangeComateConfiguration();
                }
            })
        );
        this.registerContextListener();
        this.featureFlags.on(this.handleFeatureFlagsChange.bind(this));
    }
    get messages() {
        return this.chatModel.getMessagesSync(this.defaultChatSessionUuid);
    }

    get engineInitialized() {
        return this.engineInited;
    }

    get webviewInitialized() {
        return this.isWebviewInitialized;
    }

    get kernelProvider() {
        return iocContainer.get(KernelProvider);
    }

    /**
     * 判断`Comate`侧边栏视图是否可见
     */
    get webViewVisible() {
        return !!this.webView?.visible;
    }

    get sessionUuid() {
        return this.defaultChatSessionUuid;
    }

    get messageHandler() {
        return this.webViewMessageHandler;
    }

    waitingEngineInit() {
        return this.isEngineInitialized.promise;
    }

    appendUnAttachedWebViewListener(event: typeof this.unAttachedWebViewListener[0]) {
        this.logger.startDebugLog({event: event.action});
        this.unAttachedWebViewListener.push(event);
        this.logger.stopDebugLog({event: event.action});
    }

    // 把一次用户的输入和一个或多个返回结果当成一轮回话
    createConversation(question: string, type: MessageType, code?: string) {
        const sessionUuid = this.sessionUuid;
        return new Conversation(
            this.defaultChatSessionUuid,
            this.chatModel,
            {
                updateView: async (uuid?: string) => {
                    // await this.waitingEngineInit();
                    const messages = await this.chatModel.getDehydratedMessages(sessionUuid);
                    if (sessionUuid === this.sessionUuid) {
                        this.sendDataToWebview(
                            EventMessage.MessagesRefreshEvent,
                            messages,
                            uuid
                        );
                    }
                    this.chatModel.storeMessages(sessionUuid);
                },
            },
            type,
            question,
            code
        );
    }

    createAutoComateConversation(sessionUuid: string) {
        const conversation = new AutoComateConversation(this.chatModel, sessionUuid);
        conversation.onDidChange(async () => {
            const messages = await this.chatModel.getDehydratedMessages(sessionUuid);
            if (sessionUuid === this.sessionUuid) {
                this.sendDataToWebview(
                    EventMessage.MessagesRefreshEvent,
                    messages
                );
            }
            this.chatModel.storeMessages(sessionUuid);
        });
        return conversation;
    }

    getChatHistory(count: number, order: Record<number, number>) {
        return this.chatModel.getLatestMessages(this.defaultChatSessionUuid, count, order);
    }

    updateChatCompletionList(
        info: [AgentListWithId[], CommandListWithId[], KnowledgeList[], PromptTemplateList[]],
        pluginConfigs: PluginConfigs
    ) {
        this.chatCompletionList = info;
        this.pluginConfigs = {
            ...this.pluginConfigs,
            ...pluginConfigs,
        };
        return this.sendDataToWebview(EventMessage.UpdateChatCompletionEvent, [info, pluginConfigs]);
    }

    engineInitEvent() {
        this.engineInited = true;
        this.isEngineInitialized.resolve();
        this.sendDataToWebview(EventMessage.EngineInitEvent, {});
    }

    async currentQuery(): Promise<{
        prompt: string;
        agent?: string;
        slash?: string;
        knowledgeList: KnowledgeList[];
    }> {
        return this.sendDataToWebview(EventMessage.CurrentQueryEvent, {});
    }

    checkMessagesRefreshEvent(event: string, content?: any, uuid?: string) {
        const isInStreamProgress = content.some(
            (item: DehydratedMessage) => (item.status === 'inProgress' && item.content.length > 0)
        );
        // stream 类型消息仅在开始和结束时记录日志，流式输出的过程中不额外记录
        if (!isInStreamProgress) {
            this.logger.startDebugLog({event, extra: content, uuid});
        }

        const isChatCanceled = content.some(
            (item: DehydratedMessage) => item.status === 'canceled'
        );
        if (isChatCanceled) {
            this.logger.triggerCancelLog({event, extra: content, uuid});
        }

        const isChatFailed = content.some(
            (item: DehydratedMessage) => item.status === 'failed'
        );
        if (isChatFailed) {
            this.logger.triggerErrorLog({event, extra: content, uuid});
        }
    }

    async sendDataToWebview(event: string, content?: any, uuid?: string) {
        if (this.webViewMessageHandler) {
            if (event === EventMessage.MessagesRefreshEvent) {
                this.checkMessagesRefreshEvent(event, content, uuid);
            }
            else {
                this.logger.startDebugLog({event, extra: content});
            }
            return this.webViewMessageHandler.send(event, content);
        }
        this.logger.triggerInfoLog({event, extra: 'do not have webViewMessageHandler'});
    }

    private handleColorThemeChange(e: vscode.ColorTheme) {
        this.sendDataToWebview(EventMessage.ColorThemeChangeEvent, COLOR_THEME_MAPPING[e.kind]);
    }

    // eslint-disable-next-line complexity
    async handleUserQuery(prompt: UserQueryPromptType) {
        const {
            prompt: content,
            needContext,
            type,
            chatIntentRecognition,
            messageOrder = {},
            agent,
            slash,
            knowledgeList: rawKnowledgeList,
            responseReminder,
            disableCode,
            extraData,
        } = prompt;
        const knowledgeList = serializeChatKnowledges(rawKnowledgeList);
        // 有新消息进入的时候清空suggestion
        this.suggestionsRefresh([]);

        const secure: boolean = this.configProvider.getSecurityEnhancement();

        // 调用 autoComate 的通信逻辑
        if (
            (!agent || agent === BuiltinAgent.Comate) && !slash
            // 开启了意图识别开关后，agent 不存在或者是 comate 且没有指令
            || ([BuiltinAgent.Comate, undefined].includes(agent as any) && !slash && chatIntentRecognition)
            // 没有agent，带了知识
            || (!agent && knowledgeList && knowledgeList.length !== 0)
            // 特殊的三方能力
            || (slash && [SlashType.AUTO_TEST, SlashType.IAPI].includes(slash as SlashType))
        ) {
            // agent的逻辑一定要前提，保证传下来的一定是用户选的，这样后续意图识别才不会踩坑
            vscodeCommands.askAutoWork({
                query: content || '',
                agent: BuiltinAgent.Comate,
                slash: slash as SlashType,
                knowledgeList,
                responseReminder,
                disableCode,
                skipAnalyze: !chatIntentRecognition,
                chatIntentRecognition,
                metadata: {
                    secure,
                },
            });
        }
        else if (agent) {
            if (agent === 'Comate') {
                if (slash) {
                    // eslint-disable-next-line max-depth
                    if (slash === UT) {
                        this.sendDataToWebview(EventMessage.AgentConversationAddFromIdeEvent, {
                            conversationId: '',
                            payload: {
                                query: '',
                                knowledgeList: [],
                            },
                            messageType: 'add-conversation',
                            conversationType: WebviewAgentConversationType.TestBotConversation,
                        });
                    }
                    else {
                        vscode.commands.executeCommand(CMD_BUILTIN_COMMAND, slash);
                    }
                }
                // 如果选中 comate，但是没有/，特殊处理下。因为comate的skill都不需要输入，如果有输入多半可能要对话了
                else {
                    vscode.commands.executeCommand(CMD_INVOKE_NL2CODE, content, {needContext, messageOrder});
                }
            }
            else {
                // 用户选择了插件，调用开放平台的通信逻辑
                // 用户选择了自定义Prompt(agent === 'PromptTemplate')，也调用开放平台的通信逻辑
                vscode.commands.executeCommand(
                    CMD_COMATE_PLUS_START_SESSION,
                    content,
                    agent,
                    slash,
                    knowledgeList,
                    extraData,
                    disableCode
                );
            }
        }
        else if (type === 'utSetting') {
            vscode.commands.executeCommand('workbench.action.openSettings', 'baidu.comate.unitTest');
        }
        // 普通对话
        else {
            vscode.commands.executeCommand(
                CMD_INVOKE_NL2CODE,
                content,
                {needContext, messageOrder, metadata: {secure}}
            );
        }
    }

    private handleSubmitCommand(payload: CustomCommandPayload) {
        vscode.commands.executeCommand(CMD_COMATE_PLUS_START_COMMAND, payload);
    }
    private handleHelpSend() {
        vscode.commands.executeCommand('setContext', 'baidu.comate.context.helpSendEvent', true);
    }

    private handleVisitHelpDocs() {
        vscode.env.openExternal(vscode.Uri.parse(platform.resolve('helpDocUrl')));
    }

    private handleFeedback() {
        vscode.env.openExternal(vscode.Uri.parse(platform.resolve('feedbackUrl')));
    }
    private async handleUsernameChange() {
        const config = vscode.workspace.getConfiguration(COMATE_CONFIG_PREFIX);
        const configUsername = config.get<string>('username');
        if (configUsername && $features.PLATFORM === 'internal') {
            const internalUserInfo = await getInternalUserInfoMemorized(configUsername);
            if (internalUserInfo && internalUserInfo.chineseName) {
                const chineseName = internalUserInfo.chineseName;
                this.sendDataToWebview(EventMessage.UsernameChangeEvent, {configUsername, chineseName});
                return;
            }
        }
        this.sendDataToWebview(EventMessage.UsernameChangeEvent, {configUsername});
    }

    // 取消请求
    private async handleResponseCancel(messageId: number) {
        this.chatModel.cancelMessage(this.defaultChatSessionUuid, messageId);
        const messages = await this.chatModel.getDehydratedMessages(this.defaultChatSessionUuid);
        this.sendDataToWebview(
            EventMessage.MessagesRefreshEvent,
            messages
        );
        this.kernelProvider.sendRequest(ACTION_COMATE_PLUS_CHAT_CANCEL, {
            pluginName: messages[messages.length - 1].name,
            messageId: messages[messages.length - 1].id,
            chatCancleToken: 'stop',
        });
    }

    private async handleOpenCodeBlock({
        filePath,
        startLine,
        endLine,
        viewColumn,
    }: {
        filePath: string;
        startLine?: number;
        endLine?: number;
        viewColumn?: vscode.ViewColumn;
    }) {
        const uri = await reviveFullUriToWorkspace(filePath);
        if (!uri) {
            return;
        }
        const selection = (startLine != null && endLine != null)
            ? new vscode.Range(startLine, 0, endLine + 1, 0) // 默认选中 endLine 一整行，所以直接到 endLine + 1 行的第0个字符
            : undefined;
        vscode.window.showTextDocument(uri, {
            viewColumn,
            preserveFocus: true,
            preview: true,
            selection,
        });
    }

    handleLicenseChange(validity: LicenseValidity) {
        this.sendDataToWebview(EventMessage.LicenseValidityChangeEvent2, validity);
    }

    handleFeatureFlagsChange(flags: Record<Feature, boolean>) {
        this.sendDataToWebview(EventMessage.FeatureFlagsChangeEvent, flags);
    }

    async resolveWebviewView(webviewView: vscode.WebviewView): Promise<void> {
        const context = await getExtensionContextAsync();
        this.webView = webviewView;
        this.webView.onDidChangeVisibility(() => {
            this.logUploadProvider.logUserAction({
                category: LogCategory.ChatVisibilityChange,
                content: this.webView?.visible,
            });
        });

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [context.extensionUri],
        };
        const scriptPath = webviewView.webview.asWebviewUri(
            vscode.Uri.joinPath(context.extensionUri, 'dist', 'chat.js')
        );
        webviewView.webview.html = getHtmlForWebview(scriptPath, L10n.t(ChatProviderText.WEBVIEW_TITLE));

        this.webViewMessageHandler !== undefined && this.webViewMessageHandler.dispose();
        this.webViewMessageHandler = new WebviewMessageHandler(webviewView.webview);

        for (const {action, listener} of this.unAttachedWebViewListener) {
            this.webViewMessageHandler.listen(action, listener);
        }

        this.registerActionHandler(this.webViewMessageHandler);
        this.disposables.push(
            this.webViewMessageHandler,
            webviewView.onDidChangeVisibility(() => {
                return this.sendDataToWebview(EventMessage.InputFocusEvent);
            })
        );
    }

    async showDetailPanel({
        title,
        viewColumn,
    }: {
        title: string;
        viewColumn?: vscode.ViewColumn;
    }): Promise<void> {
        const defaultActiveColumn = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn || vscode.ViewColumn.One
            : vscode.ViewColumn.One;
        const activeColumn = viewColumn || defaultActiveColumn;
        if (this.panel) {
            this.panel.reveal(activeColumn, true);
        }
        else {
            this.panel = vscode.window.createWebviewPanel(
                'AgentPanel',
                title,
                activeColumn,
                {
                    enableScripts: true,
                    retainContextWhenHidden: true,
                }
            );
        }
        const webview = this.panel.webview;
        const context = await getExtensionContextAsync();

        const scriptPath = webview.asWebviewUri(
            vscode.Uri.joinPath(context.extensionUri, 'dist', 'panel.js')
        );
        webview.html = getHtmlForWebview(scriptPath, 'AgentPanel');

        webview.onDidReceiveMessage(
            message => {
                switch (message.scope) {
                    case EventMessage.PanelDataSendEvent:
                        webview.postMessage({
                            scope: EventMessage.PanelDataListenEvent,
                            data: message.data,
                        });
                        break;
                    case EventMessage.PanelReadyEvent:
                        for (const {scope, data} of this.unAttachedPanelListener) {
                            webview.postMessage({scope, data});
                        }
                        this.unAttachedPanelListener.length = 0;
                        break;
                }
            }
        );

        this.disposables.push(this.panel);
        this.panel.onDidDispose(() => {
            this.panel = undefined;
        });
    }

    /**
     * @deprecated
     * 暂时标记为过期
     */
    suggestionsRefresh(suggestions: Suggestion[]) {
        this.sendDataToWebview(EventMessage.SuggestionsRefreshEvent, {suggestions});
    }

    modalOpen({key, title, description}: {key: string, title: string, description: string}) {
        this.sendDataToWebview(EventMessage.ModalOpenEvent, {key, title, description});
    }

    updateLoginStatus(isLogin: boolean) {
        this.sendDataToWebview(EventMessage.LoginStatusChangeEvent, isLogin);
    }

    getChatViewVisible() {
        return this.webView?.visible;
    }

    // eslint-disable-next-line max-statements
    private registerActionHandler(handler: WebviewMessageHandler) {
        handler.listen(
            EventMessage.UserActionStartLogEvent,
            (message: any) => {
                const {logTime, ...rest} = message.data;
                this.logger.startDebugLog({
                    event: message.scope,
                    time: message.data.logTime,
                    extra: rest,
                });
            }
        );

        handler.listen(
            EventMessage.UserActionStopLogEvent,
            (message: any) => {
                this.logger.stopDebugLog({
                    event: message.scope,
                    time: message.data.logTime,
                });
            }
        );

        handler.listen(
            EventMessage.MessageActionTriggerEvent,
            async ({messageId, action, content, language, extra}) => {
                const message = this.chatModel.getMessageById(this.defaultChatSessionUuid, messageId);
                if (!message) {
                    this.logger.triggerErrorLog({
                        event: EventMessage.MessageActionTriggerEvent,
                        extra: `failed to run accept command (id: ${messageId})`,
                    });
                    error(`failed to run accept command (id: ${messageId})`);
                    return;
                }
                const actions = message?.actions;
                if (actions) {
                    // @ts-ignore
                    const callback = actions[action];
                    if (callback) {
                        return callback(content, language, extra);
                    }
                }
            }
        );

        handler.listen(EventMessage.ClearSessionEvent, async () => {
            this.chatModel.clearMessages(this.defaultChatSessionUuid);
            this._onDidClearSession.fire();
            const messages = await this.chatModel.getDehydratedMessages(this.defaultChatSessionUuid);
            this.sendDataToWebview(
                EventMessage.MessagesRefreshEvent,
                messages
            );
        });
        handler.listen(EventMessage.HelpSendEvent, this.handleHelpSend.bind(this));
        handler.listen(EventMessage.VisitHelpDocsEvent, this.handleVisitHelpDocs.bind(this));
        handler.listen(EventMessage.FeedbackEvent, this.handleFeedback.bind(this));
        handler.listen(EventMessage.QuerySendEvent, this.handleUserQuery.bind(this));
        // 监听通用请求事件
        handler.listen(EventMessage.PassthroughRequest, async <T>(params: UnityRequestType) => {
            const authorization = params.authorization;
            const [username] = await this.userService.getCurrentUser();
            const license = this.configProvider.getLicense();
            const response: HttpUnityResponseType<T> = authorization
                ? await getUnityRequestResponse<T>(params, isInternal ? username : license)
                : await getUnityRequestResponse<T>(params, '');
            return response;
        });
        handler.listen(EventMessage.SubmitCommandEvent, this.handleSubmitCommand.bind(this));
        handler.listen(EventMessage.ResponseCancelEvent, this.handleResponseCancel.bind(this));
        handler.listen(EventMessage.UsernameFetchEvent, async () => {
            // 同步用户名
            const [username] = await this.userService.getCurrentUser();
            if (username && $features.PLATFORM === 'internal') {
                const internalUserInfo = await getInternalUserInfoMemorized(username);
                if (internalUserInfo && internalUserInfo.chineseName) {
                    const chineseName = internalUserInfo.chineseName;
                    return {username, chineseName};
                }
            }
            return {username};
        });
        handler.listen(EventMessage.MessagesFetchEvent, async () => {
            await this.waitingEngineInit();
            return this.chatModel.getDehydratedMessages(this.defaultChatSessionUuid);
        });
        handler.listen(EventMessage.ColorThemeFetchEvent, () => {
            const colorTheme = vscode.window.activeColorTheme;
            return colorTheme && COLOR_THEME_MAPPING[colorTheme.kind];
        });
        handler.listen(EventMessage.ShowCodeBlockEvent, this.handleOpenCodeBlock.bind(this));
        handler.listen(EventMessage.ShowWebviewPanelEvent, this.showDetailPanel.bind(this));
        handler.listen(EventMessage.CopyToClipboardEvent, (content: string) => {
            vscode.env.clipboard.writeText(content);
        });
        // eslint-disable-next-line complexity
        handler.listen(EventMessage.LinkClickEvent, async (href: string) => {
            if (href.startsWith('//command:')) {
                const command = href.slice('//command:'.length);
                vscode.commands.executeCommand(command);
                return;
            }
            // 是否为外部链接
            if (/^(https:|http:|mailto:)/.test(href) || href.startsWith(vscode.env.uriScheme)) {
                vscode.env.openExternal(vscode.Uri.parse(href));
                return;
            }

            const regex = /:(\d+)$/;
            const selectionRegex = /#L(\d)-(\d+)$/;
            const match = regex.exec(href) ?? selectionRegex.exec(href);

            if (match) {
                const selection = match
                    ? new vscode.Range(
                        new vscode.Position(Number(match[1]) - 1, 0),
                        new vscode.Position(Number(match[2] ?? match[1]) - 1, 0)
                    )
                    : undefined;

                // 支持跳转到指定行号 [打开README.md文件](./README.md:12)
                const filePath = href.slice(0, -match[0].length);

                // Windows 会有概率返回 /C:/Users/<USER>
                const uniformPath = /^\/[a-z]:\//i.test(filePath)
                    ? filePath.slice(1)
                    // Windows 系统会返回 .\ 的路径，到这里会decode成 .%5c，仅遇到这个case
                    : decodeURIComponent(filePath).replace(new RegExp(/^\.\\/), '');

                try {
                    await openDocument([vscode.Uri.parse(uniformPath), ...joinWorkspacePath(uniformPath)], selection);
                }
                catch (ex) {
                    error(
                        `LinkClickEvent: filepath=${filePath}, uniformPath=${uniformPath}, error=${(ex as any).message}`
                    );
                }
            }
            else {
                // 是否能还原成相对于当前工作区的文件路径
                const uri = await reviveFullUriToWorkspace(href);
                if (uri) {
                    try {
                        await vscode.window.showTextDocument(uri);
                    }
                    catch (ex) {
                        this.sendDataToWebview(EventMessage.ToastMessageChangeEvent, {
                            type: 'fail',
                            message: '不支持查看当前文件类型',
                        });
                    }
                }
            }
            // 其余情况暂不处理
        });

        handler.listen(EventMessage.WebViewInitEvent, async () => {
            vscode.commands.executeCommand(CMD_WEBVIEW_INIT, {});
            this.isWebviewInitialized = true;
            const features: Feature[] = [
                Feature.SaaSV0301,
                Feature.ComatePair,
            ];
            // 初始化成功后，发送消息给webview
            const flags = await Promise.all(
                features.map(v => this.featureFlags.hasAccessTo(v))
            );

            const featureFlags = zipObject(features, flags);
            this.sendDataToWebview(EventMessage.FeatureFlagsChangeEvent, featureFlags);
            return this.engineInited;
        });
        handler.listen(
            EventMessage.LicenseValidityFetchEvent2,
            () => vscode.commands.executeCommand(CMD_LICENSE_EXPIRED, {})
        );
        handler.listen(EventMessage.FilesFetchEvent, async ({keyword, type}: {keyword?: string, type?: string}) => {
            // 选择代码库或当前文件时，无需选择下级列表; 选择知识库时，无需做本地文件的选择
            // eslint-disable-next-line max-len
            if (
                type === AutoWorkKnowledgeType.CURRENT_FILE || type === AutoWorkKnowledgeType.REPO
                || type === AutoWorkKnowledgeType.KNOWLEDGE
            ) {
                return [];
            }
            if (keyword) {
                const files = await getFilesOrFoldersByKeywordAndType(keyword, type);
                return files;
            }
            if (type === AutoWorkKnowledgeType.FILE) {
                const openAndRecentFiles = await getOpenAndRecentFiles();
                return openAndRecentFiles;
            }
            if (type === AutoWorkKnowledgeType.FOLDER) {
                const openAndRecentFolders = await getOpenAndRecentFolders();
                return openAndRecentFolders;
            }
            return [];
        });

        handler.listen(EventMessage.SelectedFilesRecordEvent, (selectedFile: string) => {
            recordRecentSelectedFiles(selectedFile);
        });

        const handleInputFocus = debounce(() => {
            vscode.commands.executeCommand(CMD_HANDLE_INPUT_FOCUS);
        }, 200);

        handler.listen(EventMessage.HandleInputFocusEvent, handleInputFocus);

        const switchChatSessionEvent = async (sessionUuid?: string, isDelete: boolean = false) => {
            if (sessionUuid) {
                this.defaultChatSessionUuid = sessionUuid;
                const messages = await this.chatModel.getDehydratedMessages(sessionUuid);
                this.sendDataToWebview(
                    EventMessage.MessagesRefreshEvent,
                    messages
                );
            }
            else {
                this.defaultChatSessionUuid = randomUUID();
                this.sendDataToWebview(
                    EventMessage.MessagesRefreshEvent,
                    []
                );
                if (!isDelete) {
                    this.sendDataToWebview(
                        EventMessage.SwitchChatSessionEvent,
                        {status: 'success'}
                    );
                }
            }
            vscode.commands.executeCommand('setContext', COMMAND_LAST_SESSION_UUID, this.defaultChatSessionUuid);
        };

        handler.listen(EventMessage.SwitchChatSessionEvent, switchChatSessionEvent);

        handler.listen(EventMessage.ChatSessionListFetchEvent, async () => {
            const res = await this.kernelProvider?.sendRequest<{payload: any[]}>(
                ACTION_CHAT_SESSION_LIST,
                {}
            );

            // this.sendDataToWebview(EventMessage.ChatSessionListFetchEvent, res?.payload || []);

            return res?.payload || [];
        });

        handler.listen(EventMessage.ChatSessionDeleteEvent, async (sessionUuid: string) => {
            this.chatModel.cancelAllMessages(sessionUuid);
            await new Promise(resolve => setTimeout(resolve, 300));
            await this.kernelProvider?.sendRequest(
                ACTION_CHAT_SESSION_DELETE,
                {sessionUuid}
            );
            if (this.defaultChatSessionUuid === sessionUuid) {
                switchChatSessionEvent(undefined, true);
            }
            const res = await this.kernelProvider?.sendRequest<{payload: any[]}>(
                ACTION_CHAT_SESSION_LIST,
                {}
            );

            this.sendDataToWebview(EventMessage.ChatSessionListFetchEvent, res?.payload || []);
        });

        handler.listen(EventMessage.BannerVersionFetchEvent, async () => {
            const context = await getExtensionContextAsync();
            const bannerVersion = context.globalState.get<number>(BANNER_VERSION_CONFIG_KEY) ?? 0;
            const activityInfo = await getLatestActivityInfo(this.configProvider.getLicense());
            const activityVersion = activityInfo?.bannerShowSwitch === true
                ? parseInt(activityInfo.version ?? '0', 10)
                : 0;
            return {
                bannerShow: activityInfo?.bannerShowSwitch ?? false,
                bannerVersion,
                activityVersion,
                bannerUrl: activityInfo?.bannerUrl || '',
                actionUri: activityInfo?.lotteryKey || '',
            };
        });

        handler.listen(EventMessage.BannerVersionRefreshEvent, async (version: number) => {
            const context = await getExtensionContextAsync();
            context.globalState.update(BANNER_VERSION_CONFIG_KEY, version);
        });

        handler.listen(
            EventMessage.IdeToastMessageEvent,
            ({type, message}: {type: 'info' | 'error', message: string}) => {
                if (type === 'info') {
                    vscode.window.showInformationMessage(message);
                    this.logger.triggerInfoLog({event: EventMessage.IdeToastMessageEvent, extra: message});
                }
                else if (type === 'error') {
                    vscode.window.showErrorMessage(message);
                    this.logger.triggerErrorLog({event: EventMessage.IdeToastMessageEvent, extra: message});
                }
            }
        );

        handler.listen(EventMessage.SurveyVersionFetchEvent, async () => {
            const context = await getExtensionContextAsync();
            return context.globalState.get<number>(SURVEY_VERSION_CONFIG_KEY);
        });

        handler.listen(EventMessage.SurveyVersionRefreshEvent, async (version: number) => {
            const context = await getExtensionContextAsync();
            context.globalState.update(SURVEY_VERSION_CONFIG_KEY, version);
        });

        handler.listen(EventMessage.InputHistoryFetchEvent, async () => {
            const context = await getExtensionContextAsync();
            const history = context.globalState.get<InputBoxMessageHistory[]>(INPUTBOX_HISTORY_KEY);
            return history || [];
        });

        handler.listen(EventMessage.InputHistoryRefreshEvent, async (history: InputBoxMessageHistory[]) => {
            const context = await getExtensionContextAsync();
            context.globalState.update(INPUTBOX_HISTORY_KEY, history);
        });

        handler.listen(EventMessage.SurveySendEvent, postSurveyContent);

        handler.listen(EventMessage.OpenPluginConfigPanelEvent, (toKnowledge?: boolean) => {
            vscode.commands.executeCommand('baidu.comate.openPluginConfigPanel', toKnowledge);
        });

        handler.listen(EventMessage.QueryVisibilitySelectorLogEvent, (content: any) => {
            this.logUploadProvider.logUserAction({
                category: LogCategory.queryVisibility,
                content,
            });
        });

        handler.listen(EventMessage.AvatarFetchEvent, async () => {
            const context = await getExtensionContextAsync();
            return context.secrets.get(CURRENT_LOGIN_AVATAR);
        });

        handler.listen(EventMessage.WorkspaceStatusEvent, workspaceStatus);

        // 插件登录
        handler.listen(EventMessage.LoginTriggerEvent, () => {
            login(iocContainer);
        });
        // 初始化登录状态获取
        handler.listen(EventMessage.LoginStatusFetchEvent, () => {
            // 内部版本直接返回 true 防止获取license为空，然后认为没登录
            if (isInternal) {
                return true;
            }
            return !!this.configProvider.getLicense();
        });

        handler.listen(EventMessage.OpenLicenseConfigEvent, async () => {
            const context = await getExtensionContextAsync();
            vscode.commands.executeCommand('workbench.action.openSettings', {
                query: `@ext:${context.extension.id} ${getBasicConfigSectionName()}`,
            });
        });

        handler.listen(EventMessage.LanguageFetchEvent, async () => {
            return this.configProvider.getDisplayLanguage();
        });

        handler.listen(EventMessage.CopyLogHistoryEvent, async (uuid?: string) => {
            this.logger.reportLogHistoryByUuid(uuid);
        });

        handler.listen(
            EventMessage.UploadUserActionLog,
            (data: LogUploaderEvent, type: 'event' | 'error' = 'event') => {
                this.logUploadProvider.logUserAction(data, type);
            }
        );
        handler.listen(EventMessage.CustomizeUserFetchEvent, () => {
            const customizeProvider = iocContainer.get(CustomizeProvider);
            const customized = customizeProvider.isCustomizeUser();
            return {customized};
        });
        handler.listen(EventMessage.UserGuideFetchEvent, async () => {
            const result = await vscode.commands.executeCommand(CMD_USER_GUIDE_FETCH, {});
            return result as ConfigItem[];
        });
        handler.listen(EventMessage.RepoGuideFetchEvent, () => {
            return vscode.commands.executeCommand(CMD_KNOWLEDGE_TYPE_GUIDE_FETCH);
        });

        handler.listen(EventMessage.SearchAPIByKeywordEvent, async (params: SearchAPIParamType) => {
            const result = await vscode.commands.executeCommand(CMD_SEARCH_API_BY_KEYWORD, params);
            return result as APIItem[];
        });

        handler.listen(EventMessage.TrackClickSearchedAPIEvent, (params: TrackAPIParamType) => {
            vscode.commands.executeCommand(CMD_TRACK_CLICK_SEARCHED_API, params);
        });

        handler.listen(EventMessage.LoginOutEvent, async () => {
            return vscode.commands.executeCommand(COMMAND_LOGOUT);
        });

        handler.listen(EventMessage.LicenseFullDetailFetchEvent, async () => {
            let licenseType: LicenseFullDetail = defaultLicenseFullDetail();
            try {
                licenseType = await getLicenseFullDetail(this.configProvider.getLicense());
            }
            catch {
                // nothing
            }
            return {...licenseType, endpoint: this.configProvider.getPrivateService()};
        });

        handler.listen(EventMessage.UserGuideChangeEvent, async () => {
            return vscode.commands.executeCommand(CMD_USER_GUIDE_CHANGE);
        });

        handler.listen(EventMessage.CodeIndexEvent, async payload => {
            return vscode.commands.executeCommand(CMD_RECREATE_INDEX, {command: payload?.command || 'recreate'});
        });

        handler.listen(EventMessage.InitFetchEvent, async () => {
            return {
                deviceId: await getDeviceUUIDThrottled(),
            };
        });

        handler.listen(EventMessage.AgentConversationFetchEvent, async () => {
            return vscode.commands.executeCommand(CMD_GET_AGENT_CONVERSATIONS);
        });

        handler.listen(EventMessage.AgentConversationAddEvent, async agentPayload => {
            return vscode.commands.executeCommand(CMD_ADD_AGENT_TASK_CONVERSATION, agentPayload);
        });

        handler.listen(
            EventMessage.AgentConversationSetForegroundEvent,
            agentPayload => {
                vscode.commands.executeCommand(CMD_ADD_SET_FOREGROUND, agentPayload);
            }
        );

        handler.listen(
            EventMessage.AgentConversationNewMessageEvent,
            async (agentPayload: AgentPayload) => {
                return vscode.commands.executeCommand(CMD_ADD_NEW_MESSAGE, agentPayload);
            }
        );

        handler.listen(EventMessage.PanelDataSendEvent, (data: {type: string, data: any}) => {
            if (this.panel) {
                this.panel.webview.postMessage({
                    scope: EventMessage.PanelDataListenEvent,
                    data,
                });
            }
            else {
                this.unAttachedPanelListener.push({scope: EventMessage.PanelDataListenEvent, data});
            }
        });

        handler.listen(EventMessage.AgentSwitchStatusEvent, () => {
            return vscode.commands.executeCommand(CMD_ADD_GET_AGENT_STATUS);
        });
        handler.listen(
            EventMessage.AgentSwitchChangeEvent,
            ({type, enable}: {type: WebviewAgentConversationType, enable: boolean}) => {
                return vscode.commands.executeCommand(CMD_ADD_UPDATE_AGENT_STATUS, {type, enable});
            }
        );
        handler.listen(EventMessage.SettingButtonClickEvent, async (settingToSearch?: string) => {
            vscode.commands.executeCommand('workbench.action.openSettings', settingToSearch);
        });
        handler.listen(EventMessage.InvokeSmartApply, async ({content, key, accepted}) => {
            return vscode.commands.executeCommand(RegisteredCommand.smartApply, {content, key, accepted});
        });
        handler.listen(EventMessage.CacheAddEvent, async ({key, value}) => {
            vscode.commands.executeCommand(CMD_ADD_CACHE, {key, value});
        });
        handler.listen(EventMessage.CacheGetEvent, async key => {
            return vscode.commands.executeCommand(CMD_GET_CACHE, {key});
        });
        handler.listen(EventMessage.DownloadLogEvent, () => {
            downloadLogs();
        });
        handler.listen(EventMessage.DefaultModelSelectEvent, async modelList => {
            return vscode.commands.executeCommand(CMD_DEFAULT_MODEL, modelList);
        });
        handler.listen(EventMessage.UpdateDefaultModelSelectEvent, async key => {
            return vscode.commands.executeCommand(CMD_UPDATE_DEFAULT_MODEL, key);
        });
        handler.listen(
            EventMessage.PromptTemplateCreateEvent,
            (data: CreatePromptTemplatePayload) => {
                vscode.commands.executeCommand(CMD_COMATE_PLUS_CREATE_PROMPTTEMPLATE, data);
            }
        );
        handler.listen(EventMessage.PromptTemplateEditEvent, (uuid: string) => {
            vscode.commands.executeCommand(CMD_COMATE_PLUS_EDIT_PROMPTTEMPLATE, uuid);
        });
        handler.listen(EventMessage.PromptTemplateDeleteEvent, (uuid: string) => {
            vscode.commands.executeCommand(CMD_COMATE_PLUS_DELETE_PROMPTTEMPLATE, uuid);
        });
        handler.listen(EventMessage.PassthroughMessageToIDEFromWebviewEvent, (params: any) => {
            return this.kernelProvider.ptSend(params) as any;
        });
    }

    private async registerContextListener() {
        const context = await getExtensionContextAsync();
        this.disposables.push(context.secrets.onDidChange(async event => {
            if (event.key === CURRENT_LOGIN_AVATAR) {
                const avatar = await context.secrets.get(CURRENT_LOGIN_AVATAR);
                this.sendDataToWebview(EventMessage.AvatarChangeEvent, avatar);
            }
        }));
    }

    private onDidChangeComateConfiguration() {
        this.handleUsernameChange();
        const displayLanguage = this.configProvider.getDisplayLanguage();
        this.sendDataToWebview(EventMessage.LanguageChangeEvent, displayLanguage);
    }

    resourcePath = async (filePath: string) => {
        const context = await getExtensionContextAsync();
        return this.webView?.webview.asWebviewUri(
            vscode.Uri.joinPath(context.extensionUri, 'dist', filePath)
        );
    };

    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables = [];
    }
}
