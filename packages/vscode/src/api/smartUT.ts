import {info} from '../common/outputChannel';
import {createAxios} from './common';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'http://10.11.60.160:8200',
    'internal-test': 'http://smart-ut.test.cov.appspace.baidu.com',
    saas: 'https://comate.baidu.com',
    'saas-test': 'http://smart-ut.test.cov.appspace.baidu.com',
    poc: 'https://comate.baidu.com',
};

const axiosInstance = createAxios({
    baseURL: BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
});

info(`SmartUT api baseURL: ${axiosInstance.defaults.baseURL}`);

export interface TestMateExecution {
    userName: string;
    ideType: 'vscode';
    iutVersion: string; // 本地版本
    bosVersion: string; // 远端版本
    system: string;
    arch: string;
    upState: number; // 0 for success, 1 for fail
    reason?: string;
}

export async function reportTestMateExecution(data: TestMateExecution) {
    const res = await axiosInstance.post<void>(
        '/api/iutupgrade/callback',
        data,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return res;
}
