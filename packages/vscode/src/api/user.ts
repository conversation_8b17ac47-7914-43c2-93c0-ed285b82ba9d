import axios from 'axios';
import {memoize} from 'lodash';
import {info} from '@/common/outputChannel';
import {withLogger} from './common';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'http://dmc.baidu-int.com',
    'internal-test': 'http://10.12.194.215:8777',
};

const axiosInstance = withLogger(axios.create({
    baseURL: BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
    proxy: false,
}));

info(`User api baseURL: ${axiosInstance.defaults.baseURL}`);

interface UserInfo {
    uuapName: string;
    departmentName: string;
    chineseName: string;
}

interface Response {
    status: 'OK' | string;
    message: string;
    data: UserInfo | null;
}

export async function getInternalUserInfo(username: string) {
    const response = await axiosInstance.get<Response>(`/uic/${username}/info`);
    if (response.data.status === 'OK') {
        return response.data.data;
    }
    throw new Error(response.data.message);
}

export const getInternalUserInfoMemorized = memoize(getInternalUserInfo);
