import {SearchAPIParamType, TrackAPIParamType, APIItem} from '@shared/protocols';
import {info} from '../common/outputChannel';
import {createAxios} from './common';
import {ResponseBase} from '.';

const BASE_URL_MAPPING: Record<string, string> = {
    'internal-bapi': 'https://comate.baidu-int.com/api',
    'internal-bapi-test': 'http://bapi-code-server.test.bapi.appspace.baidu.com',
    'saas-bapi': 'https://comate.baidu.com/api',
    'saas-bapi-test': 'http://bapi-code-server.test.bapi.appspace.baidu.com',
    'internal-AIapi': 'https://comate.baidu-int.com/api',
    'internal-AIapi-test': 'http://iapi-ai-server.test.bapi.appspace.baidu.com',
    'saas-AIapi': 'https://comate.baidu.com/api',
    'saas-AIapi-test': 'http://iapi-ai-server.test.bapi.appspace.baidu.com',
    poc: 'https://comate.baidu.com',
};

const axiosInstanceBapi = createAxios({
    baseURL: BASE_URL_MAPPING[`${$features.PLATFORM}-bapi` + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
});

const axiosInstanceAIapi = createAxios({
    baseURL: BASE_URL_MAPPING[`${$features.PLATFORM}-AIapi` + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
});

info(`SmartAPI bapi baseURL: ${axiosInstanceBapi.defaults.baseURL}`);
info(`SmartAPI AIapi baseURL: ${axiosInstanceAIapi.defaults.baseURL}`);

export async function searchAPIByKeywordAPI(params: SearchAPIParamType) {
    const res = await axiosInstanceAIapi.get<ResponseBase<APIItem[]>>(
        '/api/retrieve/knowledge',
        {
            params,
        }
    );
    return res.data.data;
}

export function trackClickSearchedApiAPI(params: TrackAPIParamType) {
    axiosInstanceBapi.post<void>(
        '/bapi/plugin/save/searchTrack',
        params
    );
}
