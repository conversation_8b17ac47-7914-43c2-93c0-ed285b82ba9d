import {KnowledgeTypeExampleItem} from '@shared/protocols';
import {LogLevel} from '@/common/outputChannel';
import {withLogger} from '../common';
import {getFeature, getAxios} from './common';

const ES_MAPPING: Record<string, string> = {
    internal: '/nlcodesearch',
    'internal-test': '/nlcodesearch',
    saas: '/retrieval',
    'saas-test': '/retrieval',
    poc: '/retrieval',
};

const feature = getFeature();
const es = ES_MAPPING[feature];

const axiosInstance = withLogger(getAxios(), LogLevel.Trace);

interface KnowledgeTypeExampleResponse {
    code: number;
    message?: string;
    data: KnowledgeTypeExampleItem[];
}

export async function knowledgeTypeGuideConfig(repo = '') {
    const res = await axiosInstance.get<KnowledgeTypeExampleResponse>(
        `${es}/v2/remote-index-repo-suggested-queries?repo=${repo}`
    );
    if (res.status === 200 && res.data.code === 200) {
        return res.data.data ?? [];
    }
    return [];
}
