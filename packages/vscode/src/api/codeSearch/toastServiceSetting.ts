import * as vscode from 'vscode';
import {L10n} from '@/common/L10nProvider/L10n';
import {GlobalText} from '@/common/L10nProvider/constants';
import {debounce} from '../../utils/Debouncer';

export async function toastServiceSetting() {
    const informationText = L10n.ti(GlobalText.COMMON_CUSTOMIZE_ERROR);
    const actionText = L10n.ti(GlobalText.COMMON_NAVIGATE_TO_SETTING);
    const choice = await vscode.window.showInformationMessage(
        informationText,
        actionText
    );
    if (choice === actionText) {
        vscode.commands.executeCommand('workbench.action.openSettings', 'baidu.comate.customizeService');
    }
}

export const toastServiceSettingDebounced = debounce(3000, toastServiceSetting);

export async function toastPrivateServiceSetting() {
    const informationText = L10n.ti(GlobalText.COMMON_PRIVATE_ERROR);
    const actionText = L10n.ti(GlobalText.COMMON_NAVIGATE_TO_SETTING);
    const choice = await vscode.window.showInformationMessage(
        informationText,
        actionText
    );
    if (choice === actionText) {
        vscode.commands.executeCommand('workbench.action.openSettings', 'baidu.comate.privateService');
    }
}

export const toastPrivateServiceSettingDebounced = debounce(3000, toastPrivateServiceSetting);
