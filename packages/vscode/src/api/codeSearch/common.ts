import {createAxios} from '../common';
import {withCustomHeaders} from './withCustomHeaders';

export const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://cs.baidu-int.com',
    'internal-test': 'https://sourcegraphtest.baidu-int.com',
    saas: 'https://comate.baidu.com/api/autowork',
    'saas-test': 'https://comate-cop.now.baidu-int.com/api/autowork',
    poc: 'https://comate.baidu.com/api/autowork',
};

export function getFeature() {
    return $features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '');
}

export function getVersionPrefix(version?: 'v1' | 'v2'): string {
    return version === 'v2' ? '/v2' : '';
}

export function getAxios() {
    const feature = getFeature();
    const axiosInstance = withCustomHeaders(createAxios({
        baseURL: BASE_URL_MAPPING[feature],
    }));
    return axiosInstance;
}
