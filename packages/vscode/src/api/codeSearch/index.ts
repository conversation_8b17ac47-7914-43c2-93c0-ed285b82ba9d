export {
    FileIndex,
    PatchFileIndex,
    RemoteIndexMetadata,
    IndexerConfig,
    IndexVersionResponse,
    Version,
    getRemoteIndexInfo,
    patchRemoteIndexFile,
    postRemoteIndexMetadata,
    getIndexerConfig,
    getRemoteIndexVersion,
    codeIndexServiceHealthy,
} from './embeddingIndex';

export {
    SearchSymbol,
    SearchAction,
    SymbolSearchParams,
    accessTo,
    getSymbolSearchParams,
    getEmbeddingMatches,
    codeSearchServiceHealthy,
} from './embeddingCodeSearch';
import {getAxios} from './common';

interface FileDocstringParams {
    content: string;
    repo: string;
    path: string;
    username: string;
}

export interface FileDocstringData {
    content: string;
    path: string;
    generate_count: number;
    uuid: string;
}

export interface FileDocstringResponse {
    success: boolean;
    error: any;
    data: FileDocstringData;
}

const axiosInstance = getAxios();

/**
 * 批量生成文件函数注释
 */
export async function generateFileComment(params: FileDocstringParams) {
    const data = JSON.stringify(params);
    const res = await axiosInstance.post<FileDocstringResponse>(
        '/comment_generate',
        data,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
    return res.data;
}
