import {LogLevel} from '@/common/outputChannel';
import {createAxios} from '../common';
import {withCustomHeaders} from './withCustomHeaders';
import {getFeature, getVersionPrefix, BASE_URL_MAPPING, getAxios} from './common';

const ES_MAPPING: Record<string, string> = {
    internal: 'embeddingindex',
    'internal-test': 'embeddingindex',
    saas: 'index',
    'saas-test': 'index',
    poc: 'index',
};

const feature = getFeature();
const es = ES_MAPPING[feature];

const axiosInstance = getAxios();

const axiosTraceLogging = withCustomHeaders(createAxios(
    {
        baseURL: BASE_URL_MAPPING[feature],
    },
    LogLevel.Trace
));

export interface FileIndex {
    repo: string;
    repo_id: string;
    filepath: string;
    sha1: string;
    user: string;
    env: 1 | 2; // 1 vscode 2 idea
    version?: string;
    /** 对应三种构建场景，首次构建、重新构建、由定时任务触发的构建 */
    scene: 'firstBuild' | 'reBuild' | 'common';
    status: 'pending' | 'running' | 'finished' | 'failed';
}

interface CreateFileIndex extends Omit<FileIndex, 'status'> {
    action: 'create' | 'recreate';
    content: string;
}

interface UpdateFileIndex extends Omit<CreateFileIndex, 'action'> {
    action: 'update';
}

interface DeleteFileIndex extends Omit<FileIndex, 'sha1' | 'status'> {
    action: 'delete';
}

export type PatchFileIndex = CreateFileIndex | UpdateFileIndex | DeleteFileIndex;

interface RemoteIndexInfoResponse {
    data: FileIndex[] | null;
    success: boolean;
    error: null;
    progress: number;
    total: number; // 上传文件数
    finished: number; // 索引构建完成的文件数
}

export type Version = 'v1' | 'v2';

export async function getRemoteIndexInfo(repoId: string, version?: Version) {
    const versionPrefix = getVersionPrefix(version);
    const res = await axiosTraceLogging.get<RemoteIndexInfoResponse>(
        `/${es}${versionPrefix}/remote-index-info?repo_id=${repoId}`
    );
    if (res.data.success) {
        return res.data;
    }
    throw new Error(`Failed to GET /embeddingindex/remote-index-info?repo_id=${repoId}`);
}

export async function patchRemoteIndexFile(data: PatchFileIndex, version?: Version) {
    const versionPrefix = getVersionPrefix(version);
    const res = await axiosTraceLogging.post(
        `/${es}${versionPrefix}/remote-index-file`,
        data
    );
    return res;
}

export interface RemoteIndexMetadata {
    repo_id: string;
    repo: string;
    branch?: string;
    device: string;
    user: string;
    action?: 'finish';
}

export async function postRemoteIndexMetadata(data: RemoteIndexMetadata, version?: Version) {
    const versionPrefix = getVersionPrefix(version);
    const res = await axiosInstance.post(
        `/${es}${versionPrefix}/remote-index-repo-meta`,
        data
    );
    return res;
}

export interface IndexerConfig {
    black_pattern: string[];
    white_pattern: string[];
    config: {
        max_file_size_kb: number;
        max_file_count: number;
        index_interval_minute: number; // 索引更新最小间隔
        concurrent_file_count: number; // 并发上传文件数
        file_interval_milliseconds: number; // 文件更新最小间隔
        progress_interval_seconds: number; // 查询索引构建进度最小间隔
    };
    text: {
        authorization_corner: string; // 右下授权提醒文案
        authorization_side: string; // 侧边栏授权提醒文案
    };
}

interface IndexerConfigResponse {
    data: IndexerConfig;
    status: 'ok';
    message: string;
}
export async function getIndexerConfig() {
    const res = await axiosInstance.get<IndexerConfigResponse>(
        `/${es}/v2/remote-index-config`
    );
    if (res.data.status === 'ok') {
        return res.data.data;
    }
    throw new Error(res.data.message);
}

export interface IndexVersionResponse {
    success: boolean;
    error: string;
    version: 'v1' | 'v2';
}

/**
 * 获取索引构建服务版本
 */
export async function getRemoteIndexVersion(repoId: string) {
    const res = await axiosInstance.get<IndexVersionResponse>(
        `/${es}/remote-server-version?repo_id=` + repoId
    );
    return res.data.version;
}

// 代码向量服务探活接口 暂时只用于混合云用户场景
export async function codeIndexServiceHealthy() {
    await axiosInstance.get(`/${es}/health`);
}
