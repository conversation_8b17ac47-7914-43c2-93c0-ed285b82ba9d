import {AxiosInstance, AxiosRequestConfig, AxiosHeaders} from 'axios';
import {getHeadersConfig} from '../common';
import {toastServiceSettingDebounced} from './toastServiceSetting';

interface CustomAxiosRequestConfig extends AxiosRequestConfig {
    // 用于混合云用户响应报错特殊处理
    customized?: boolean;
    headers: AxiosHeaders;
}

export function withCustomHeaders(instance: AxiosInstance) {
    instance.interceptors.request.use(async (config: CustomAxiosRequestConfig) => {
        const headersConfig = await getHeadersConfig();
        const candidates = {
            'X-Comate-License': headersConfig.license,
            'X-Comate-User': headersConfig.user ? encodeURIComponent(headersConfig.user) : undefined,
            'X-Client-Type': headersConfig.clientType,
            'X-Client-Version': headersConfig.clientVersion,
        };
        config.baseURL = headersConfig.customizeService || config.baseURL;
        // 用于混合云用户响应报错特殊处理
        config.customized = !!headersConfig.customizeService;
        for (const [key, value] of Object.entries(candidates)) {
            if (value !== undefined) {
                config.headers[key] = value;
            }
        }
        return config;
    }, error => {
        return Promise.reject(error);
    });

    instance.interceptors.response.use(response => {
        return response;
    }, error => {
        if (error.config.customized) {
            toastServiceSettingDebounced();
        }
        return Promise.reject(error);
    });
    return instance;
}
