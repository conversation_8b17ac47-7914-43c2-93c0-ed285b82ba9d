import {Feature} from '@shared/protocols';
import {info} from '@/common/outputChannel';
import {isInternal} from '@/utils/features';
import {CodeChunk} from '../../common/types';
import {getFeature, BASE_URL_MAPPING, getAxios} from './common';

const CS_MAPPING: Record<string, string> = {
    internal: 'nlcodesearch',
    'internal-test': 'nlcodesearch',
    saas: 'retrieval',
    'saas-test': 'retrieval',
    poc: 'retrieval',
};

const feature = getFeature();
const cs = CS_MAPPING[feature];

export const baseURL = BASE_URL_MAPPING[feature];

const axiosInstance = getAxios();

info(`CodeSearch api baseURL: ${axiosInstance.defaults.baseURL}`);

type SearchSymbolKind =
    | 'FILE'
    | 'MODULE'
    | 'NAMESPACE'
    | 'PACKAGE'
    | 'CLASS'
    | 'ENUM'
    | 'INTERFACE'
    | 'STRUCT'
    | 'METHOD'
    | 'CONSTRUCTOR'
    | 'FUNCTION'
    | 'STRING'
    | 'NUMBER'
    | 'BOOLEAN'
    | 'ARRAY'
    | 'OBJECT'
    | 'NULL'
    | 'VARIABLE'
    | 'CONSTANT'
    | 'PROPERTY'
    | 'FIELD'
    | 'KEY'
    | 'ENUMMEMBER'
    | 'TYPEPARAMETER'
    | 'EVENT'
    | 'OPERATOR';

export interface SearchSymbol {
    name: string;
    kind: SearchSymbolKind;
}

export enum SearchAction {
    Definition = 'Definition',
    Reference = 'Reference',
    Implements = 'Implements',
    DocumentSymbol = 'DocumentSymbol', // 当前文件的所有符号
    DidOpen = 'DidOpen', // 当前打开文件的内容
    CallHierarchy = 'CallHierarchy', // TODO: 再找悦浩确认下
}

export interface SymbolSearchParams {
    repo: string;
    file?: string;
    action: SearchAction;
    symbol: SearchSymbol;
}

interface GetSymbolSearchResponse {
    params: SymbolSearchParams;
}

export async function getSymbolSearchParams(query: string) {
    const res = await axiosInstance.post<GetSymbolSearchResponse[]>(
        `/${cs}/nl2searchparam`,
        {
            query,
        }
    );
    return res.data;
}

interface EmbeddingSearchResult {
    status?: 'ok';
    success?: boolean; // 这里先兼容下，之后统一
    data: CodeChunk[] | null;
    queryType: 'architecture' | 'other';
}

interface EmbeddingSearchParams {
    query: string[];
    repo: string[];
    path?: string[];
    needRank: boolean;
    // TODO: 透传conversionId用于日志记录和检索历史
}

export async function getEmbeddingMatches(data: EmbeddingSearchParams) {
    const response = await axiosInstance.post<EmbeddingSearchResult>(
        `/${cs}/multiembeddingsearch`,
        data,
        {
            timeout: 1000 * 15,
        }
    );
    if (response.data.success || response.data.status === 'ok') {
        const data = response.data.data || [];
        return {data, queryType: response.data.queryType};
    }
    return {data: [] as CodeChunk[], queryType: 'other'};
}

// 代码检索服务探活接口 暂时只用于混合云用户场景
export async function codeSearchServiceHealthy() {
    await axiosInstance.get(`/${cs}/health`);
}

/** 小流量判断 */
export async function accessTo({user, type}: {user: string, type: Feature}) {
    if (!isInternal) {
        return {data: {status: false}};
    }

    return axiosInstance.get<{status: boolean}>(`${cs}/hasAccess`, {params: {user, type}});
}
