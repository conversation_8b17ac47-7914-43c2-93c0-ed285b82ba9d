import {IncomingMessage} from 'node:http';
import {CancelToken} from 'axios';
import * as vscode from 'vscode';
import {ENVIRONMENT} from '@comate/plugin-shared-internals';
import {hideSensitive} from '@/utils/common';
import {info} from '../common/outputChannel';
import {createAxios, withProxy} from './common';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'http://smart-ut.prod.cov.appspace.baidu.com',
    'internal-test': 'http://smart-ut.test.cov.appspace.baidu.com',
    saas: 'https://comate.baidu.com',
    'saas-test': 'http://smart-ut.test.cov.appspace.baidu.com',
    poc: 'https://comate.baidu.com',
};

const axiosInstance = withProxy(createAxios({
    baseURL: BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
}));

info(`UnitTest api baseURL: ${axiosInstance.defaults.baseURL}`);

interface PromptLab {
    prompt?: string;
    add_context?: boolean;
}
export interface UnitTestMetaInfo {
    test_framework: string;
    mock_framework: string;
    user: string;
    prompt_lab?: PromptLab;
}

interface ExistUTFile {
    exist_ut_file_path?: string;
    exist_ut_file_content?: string;
}

// 跨文件的依赖信息
export interface CrossFileDefinition {
    packageName: string;
    structInfo: string;
}

export interface GenerateUnitTestOptions {
    src_file_content: string;
    src_file_path: string;
    start_line: number;
    end_line: number;
    exist_ut_file?: ExistUTFile;
    meta?: UnitTestMetaInfo;
    param_struct?: {
        // 入参类型定义
        paramStruct?: CrossFileDefinition[];
        // 内部调用定义
        innerCalls?: CrossFileDefinition[];
        // 返回值类型定义
        returnStruct?: CrossFileDefinition[];
        // 方法所属的结构体，跨文件（for go）
        receiverStruct?: CrossFileDefinition[];
    };
}

export interface UnitTestCaseInfo {
    language: string;
    package_name: string;
    src_file_name: string;
    test_file_name: string;
    test_file_path: string;
    feature_tag?: string;
}

export interface UnitTestItem {
    case_info: UnitTestCaseInfo;
    fields: string[] | null;
    imports: string[] | null; // 不知道为啥，在插件里用子进程调用时，imports可能为null
    methods: string[] | null;
    display_content?: string;
}

export interface UnitTestResponse {
    result: 'SUCCESS' | 'FAIL';
    msg: string;
    data?: UnitTestItem;
}

export async function generateUnitTest(data: GenerateUnitTestOptions) {
    const deductedData = hideSensitive(data, ['src_file_content']);
    const key = vscode.workspace.getConfiguration().get('baidu.comate.license');
    const result = await axiosInstance.post<UnitTestResponse>(
        '/api/plugin/ut/key/gen',
        deductedData,
        {
            headers: {
                'Content-Type': 'application/json',
            },
            proxy: false,
            timeout: 2 * 60 * 1000, // 2 分钟后超时
            params: {
                key,
                env: $features.ENVIRONMENT === ENVIRONMENT.TEST ? 'test' : 'online',
            },
        }
    );
    return result;
}

const unitTestUrl = $features.PLATFORM === 'internal' ? '/api/plugin/ut/gen' : '/api/plugin/ut/key/gen';

export async function generateAndStreamUnitTest(data: GenerateUnitTestOptions, cancelToken?: CancelToken) {
    const deductedData = hideSensitive({
        ...data,
        stream: true,
    }, ['src_file_content']);
    const key = vscode.workspace.getConfiguration().get('baidu.comate.license');
    const result = await axiosInstance.post<IncomingMessage>(
        unitTestUrl,
        deductedData,
        {
            headers: {
                'Content-Type': 'application/json',
            },
            proxy: false,
            timeout: 5 * 60 * 1000,
            responseType: 'stream',
            cancelToken,
            params: {
                key,
                env: $features.ENVIRONMENT === ENVIRONMENT.TEST ? 'test' : 'online',
            },
        }
    );
    return result;
}

interface AcceptUnitTestParams {
    case: UnitTestItem;
    ut_file_content: string;
}

export interface AcceptResult {
    ut_file_path: string;
    ut_file_content: string;
}

export interface AcceptUnitTestResponse {
    result: 'SUCCESS' | 'FAIL';
    msg: string;
    data?: AcceptResult;
}

const acceptUnitTestUrl = $features.PLATFORM === 'internal' ? '/api/plugin/ut/accept' : '/api/plugin/ut/key/accept';

export async function acceptUnitTest(data: AcceptUnitTestParams) {
    const deductedData = hideSensitive(data, ['ut_file_content']);
    const key = vscode.workspace.getConfiguration().get('baidu.comate.license');
    const result = await axiosInstance.post<AcceptUnitTestResponse>(
        acceptUnitTestUrl,
        deductedData,
        {
            headers: {
                'Content-Type': 'application/json',
            },
            proxy: false,
            timeout: 2 * 60 * 1000, // 2 分钟后超时
            params: {
                key,
                env: $features.ENVIRONMENT === ENVIRONMENT.TEST ? 'test' : 'online',
            },
        }
    );
    return result;
}
