import {memoize} from 'lodash';
import {info} from '@/common/outputChannel';
import {createAxios} from './common';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu.com/api',
    'internal-test': 'http://10.11.144.132:8210',
    saas: 'https://comate.baidu.com/api',
    'saas-test': 'http://10.11.144.132:8030',
    poc: 'https://comate.baidu.com/api',
};

const axiosInstance = createAxios({
    baseURL: BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
});

info(`License api baseURL: ${axiosInstance.defaults.baseURL}`);

interface Response<T> {
    status: 'OK' | string;
    message: string;
    data: T;
}

export async function checkIfLicenseFromOfficialSource(license: string) {
    const response = await axiosInstance.get<Response<boolean>>(`customer/key/keyExists?key=${license}`);
    if (response.data.status === 'OK') {
        return response.data.data;
    }
    throw new Error(response.data.message);
}

export const checkIfLicenseFromOfficialSourceMemoized = memoize(checkIfLicenseFromOfficialSource);

interface ActivityInfoResponse {
    status: string;
    message?: string;
    data?: {
        version?: string;
        bannerUrl?: string;
        lotteryKey?: string;
        bannerShowSwitch?: boolean;
    };
}

// 获取活动信息，内外版本都有，内部的也从 https://comate.baidu.com 获取
export async function getLatestActivityInfo(license: string) {
    const res = await axiosInstance.get<ActivityInfoResponse>(
        '/activity/plugin/banner',
        {
            timeout: 1000 * 10,
            params: {
                license,
                type: $features.PLATFORM,
            },
        }
    );
    if (res.data.status === 'OK') {
        return res.data.data;
    }
    return {version: '0', bannerShowSwitch: false};
}
