import axios, {AxiosResponse} from 'axios';
import {
    axiosInstance,
    TextModel,
} from '@comate/plugin-shared-internals';
import {error} from '@/common/outputChannel';

interface ParamsInferCardScoreByCommittedFiles {
    titles: string[];
    diff: string;
}

interface ResultsInferCardScoreByCommittedFiles {
    result: {
        distance: number[];
        rank: number[];
    };
    // eslint-disable-next-line camelcase
    ret_code: number;
}

export const apiInferCardScoreByCommittedDiff = async (params: ParamsInferCardScoreByCommittedFiles) => {
    try {
        const res = await axios.post<typeof params, AxiosResponse<ResultsInferCardScoreByCommittedFiles>>(
            'http://10.27.142.62:8264/wenxin/inference/rank',
            params,
            {timeout: 2 * 1000}
        );
        if (res.data.ret_code !== 200) {
            throw new Error(`status code: ${res.data.ret_code}`);
        }
        return res.data;
    }
    catch (e) {
        throw e;
    }
};

//  右下角弹框=1，ourceControl=2
type Source = '1' | '2';

interface Params {
    query: string;
    task_engine_userId: string;
    codeModule?: string;
    hisMsg?: string;
    codePath?: string;
    source?: Source;
    cmdType?: string;
    from?: 'vscode' | 'jetbrains';
}

interface Card {
    cardIdAndTile: string;
    cardDetailUrl: string;
    cardType: string;
    cardTypeStyle: string;
    cardStatusStyle: string;
    cardStatus: string;
    cardPlan: string;
}

interface ResponseData {
    content: Card[];
    contentByHisMsg: Card[];
}

export const getIssuesByQuery = async (params: Params): Promise<ResponseData> => {
    try {
        const res: AxiosResponse<ResponseData> = await axios.post(
            'http://uflow.baidu-int.com/workflow/api/flow/v1/trigger-webhook/8229f149f0ed422c9b995467da87e684',
            params,
            {
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: 20 * 1000,
            }
        );

        if (res.status !== 200) {
            throw new Error(`status code: ${res.status}`);
        }
        return res.data;
    }
    catch (e) {
        throw e;
    }
};

interface FunctionCall {
    name: string;
    arguments: string;
    thoughts?: string;
}

interface ErnieBotResponse {
    result: string;
    function_call?: FunctionCall;
}

export interface ResponseBase<T> {
    status: string;
    data: T;
}

export interface ChatMessage {
    role: 'user' | 'assistant';
    content: string;
}

interface ErnieBotParams {
    prompt: string;
    username: string;
    key?: string;
    pluginName: string;
    model?: TextModel;
}

export const callErnieBot = async ({prompt, model, username, key, pluginName}: ErnieBotParams): Promise<string> => {
    try {
        const messages = [{role: 'user', content: prompt}] as ChatMessage[];
        const params = {messages, username, key, pluginName, model};
        const response: ResponseBase<ErnieBotResponse> = await axiosInstance('/api/v2/api/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            data: JSON.stringify(params),
        })
            .then(res => res.data);

        if (response.status === 'OK') {
            return response.data.result;
        }
        else {
            return '';
        }
    }
    catch (ex) {
        error('callErnieBot error:', (ex as Error).message);
        throw ex;
    }
};
