import {KnowledgeQueryWorkspace} from '@comate/plugin-shared-internals';
import {info} from '@/common/outputChannel';
import {createAxios} from './common';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu-int.com',
    'internal-test': 'https://comate.baidu-int.com',
    saas: 'https://comate.baidu.com',
    'saas-test': 'https://comate.baidu.com',
    poc: 'https://comate.baidu.com',
};

const axiosInstance = createAxios({
    baseURL: BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
});

info(`Knowledge api baseURL: ${axiosInstance.defaults.baseURL}`);

export interface Chunk {
    id: number;
    workspaceType: string;
    workspaceUUID: string;
    knowledgeType: string;
    knowledgeUUID: string;
    content: string;
    source: string;
    status: string;
    knowledgeName: string;
    keys: any[];
    score?: number | null;
    hits?: number | null;
    creator?: string | null;
}

export interface KnowledgeQueryResult {
    chunks: Chunk[];
}

export async function getKnowledgeQueryResult(
    username: string,
    query: string,
    workspaces: KnowledgeQueryWorkspace[],
    additionalQueries?: string[]
): Promise<KnowledgeQueryResult> {
    const data = JSON.stringify({
        query,
        retrievalType: 'TEXT',
        workspaces,
        additionalQueries,
    });
    const res = await axiosInstance.post(
        '/api/v2/api/aidevops/knowledge/rest/v1/retrieval',
        data,
        {
            headers: {
                'X-Source': 'COMATE',
                'Content-Type': 'application/json',
                'Uuap-login-name': username,
            },
        }
    );

    if (res.status === 200) {
        return res.data.data;
    }
    return {chunks: []};
}
