import qs from 'qs';
import {CancellationToken} from 'vscode-languageserver';
import {hideSensitive} from '../utils/common';
import {tradeCancelToken} from './common';
import {
    GenerateCodeOptions,
    GenerateTrackUuidParams,
    Response,
    axiosInstance,
    generateCode,
} from '.';

export interface PredictNextEditLineParams {
    // agent 自己生成的用于追踪的 id
    uuid: string;
    username: string;
    repo: string;
    path: string;
    // 1-based indexing
    row: number;
    // 1-based indexing
    col: number;
    // 代码片段
    content: string;
    // DiffBlock[] JSON 序列化后的字符串
    diffList: string;
}

export interface PredictNextEditLineResult {
    // 透传回来的 uuid
    uuid: string;
    row: number;
    // 透传回来的 path
    path: string;
}

export async function apiPredictNextEditLine(params: PredictNextEditLineParams, cancelToken?: CancellationToken) {
    const axiosCancelToken = tradeCancelToken(cancelToken);
    const query = qs.stringify(hideSensitive(params, ['content']));
    const result = await axiosInstance.post<Response<PredictNextEditLineResult>>(
        '/generate/position',
        query,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            cancelToken: axiosCancelToken?.token,
        }
    );
    return result;
}

export interface RewriteCodeBlockParams extends GenerateCodeOptions {
    // ProgrammingContextElement[] JSON 序列化后的字符串
    diffList: string;
    actionType: ActionType;
    triggerSource: TriggerSource;
    // 1-based indexing
    rewriteSpecificStartRow: number;
    // 1-based indexing
    rewriteSpecificEndRow: number;
}

export interface RewriteBaseResult {
    uuid: string;
    // 1-based indexing
    startRow: number;
    // 1-based indexing
    endRow: number;
    generatedContent: string;
    path: string;
    score: number;
}

export interface RewriteCodeBlockResult extends RewriteBaseResult {
    modiType: 'completion' | 'rewrite';
    // 续写时为续写替换范围，改写时 range[0] 表示改写的开始行，range[2] 表示改写的结束行
    range: [number, number, number, number];
}

export async function apiRewriteCodeBlock(
    params: RewriteCodeBlockParams,
    timeout?: number,
    cancelToken?: CancellationToken
) {
    const axiosCancelToken = tradeCancelToken(cancelToken);
    const query = qs.stringify(hideSensitive(params, ['content', 'neighborSnippetList']));
    const result = await axiosInstance.post<Response<RewriteCodeBlockResult>>(
        '/generate/rewrite',
        query,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            cancelToken: axiosCancelToken?.token,
            timeout,
        }
    );
    if (result.data.status !== 'OK' || !result.data.data) {
        throw new Error(result.data.message);
    }
    return result.data.data;
}

export enum ActionType {
    Rewrite = 'REWRITE',
    PositionPredict = 'POSITION_PREDICT',
}

export enum TriggerSource {
    MethodParameterAdded = 'METHOD_PARAMETER_ADDED', // 方法参数添加 调模型改写
    MethodParameterRemoved = 'METHOD_PARAMETER_REMOVED', // 方法参数删除 调模型改写
    MethodParameterTypeChanged = 'METHOD_PARAMETER_TYPE_CHANGED', // 方法参数类型修改 调模型改写
    MethodParameterNameChanged = 'METHOD_PARAMETER_NAME_CHANGED', // 方法参数名修改 插件端改写
    MethodReturnChanged = 'METHOD_RETURN_CHANGED', // 方法返回值修改 调模型改写
    MethodNameChanged = 'METHOD_NAME_CHANGED', // 方法名修改 插件端改写
    MultiNameChanged = 'MULTI_NAME_CHANGED', // 多名字修改 调模型改写
    SimilarCodeRewrite = 'SIMILAR_CODE_REWRITE', // 相似代码改写 调模型改写
    ReferenceUpdate = 'REFERENCE_UPDATE', // 引用更新 调模型改写（如函数定义变更，需要对引用该函数的位置进行改写）
    DiagnosticInfo = 'DIAGNOSTIC_INFO', // 光标附近有诊断信息触发的改写
    DiagnosticInfoKeyboardMove = 'DIAGNOSTIC_INFO_KEYBOARD_MOVE', // 光标键盘移动到诊断信息上触发的改写
    EditCode = 'EDIT_CODE', // 编辑代码触发改写
}

export interface RewriteRecord extends GenerateTrackUuidParams {
    uuid: string;
    actionType: ActionType.Rewrite;
    triggerSource: TriggerSource;
    rewritePreCode: string; // 前缀代码片段
    rewriteMidCode: string; // 中间代码片段
    rewriteSufCode: string; // 后缀代码片段
    rewriteSpecificStartRow: string; // 改写特定行，1-based indexing
    rewriteSpecificEndRow: string; // 改写特定行，1-based indexing
}

export interface PositionPredictRecord extends GenerateTrackUuidParams {
    uuid: string;
    actionType: ActionType.PositionPredict;
    triggerSource: TriggerSource;
}

type PersistRecordParams = RewriteRecord | PositionPredictRecord;

// 自生成的 uuid，上报信息给 gateway 记录
export async function apiPersistRecord(params: PersistRecordParams) {
    const result = await generateCode(params);
    return result;
}
