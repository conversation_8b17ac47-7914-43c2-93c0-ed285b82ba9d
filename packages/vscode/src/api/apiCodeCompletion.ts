/*
 * api 调用代码续写
 */

import pMemoize from 'p-memoize';
import {info} from '@/common/outputChannel';
import {createAxios} from './common';

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'http://10.11.135.254:8600/api',
    'internal-test': 'http://iapi-ai-server.test.bapi.appspace.baidu.com/api',
};

const BAPI_BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://yapi.baidu-int.com/bapi',
    'internal-test': 'http://bapi-code-server.test.bapi.appspace.baidu.com/bapi',
};

const axiosInstance = createAxios({
    baseURL: BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
});

const axiosBapiInstance = createAxios({
    baseURL: BAPI_BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
});

info(`API code completion baseURL: ${axiosInstance.defaults.baseURL}`);
info(`BAPI code completion baseURL: ${axiosBapiInstance.defaults.baseURL}`);

export interface CodeCompletion {
    interfaceCode: string; // 接口实现代码
    type: string; // 类型, apiDoc, code
    sampleCode: string[]; // 实例代码列表
    dataModel?: string[]; // interface 定义
    content: string; // 待补代码的内容的全部内容
    cursorIndexFunction: number; // 鼠标在待补代码的位置
    username?: string; // 用户名
}

export interface SearchImplementCode {
    repoName: string; // 仓库名
    queryList: string; // 查询列表
    username?: string; // 用户名
}

export enum RepoCodeCompletionType {
    Request = 1, // 请求代码
}

interface RepoRegexParams {
    repo: string;
    parseType: RepoCodeCompletionType; // 解析场景
}

export interface RepoFindCodeRegular {
    id: number;
    styleRegular: string; // 相似代码正则
    requestRegular: string; // 请求代码正则
    apiNameRegular: string; // 接口名称正则
    apiPathRegular: string; // 路径正则
    sampleMethod?: string; // 请求示例
}

// api 调用代码生成
export async function codeCompletion(data: CodeCompletion): Promise<{content: string, generateId: string}> {
    const res = await axiosInstance.post('/code/completion', data, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    if (!res.data?.data || res.data?.code !== 200) {
        throw new Error(res.data.message ?? '续写接口调用代码生成失败');
    }
    const {content, generateId} = res.data.data;
    return {content, generateId};
}

// api 实现代码查询
export async function getImplementCode(data: SearchImplementCode): Promise<{data: string, type: string}> {
    const res = await axiosBapiInstance.get(
        '/repoIcafeMapping/searchRelatedApiContent',
        {
            params: data,
        }
    );
    if (!res.data.data || res.data?.code !== 200) {
        throw new Error(res.data.message ?? '接口实现代码查询失败');
    }
    return res.data;
}

// 埋点
export async function sendEvent(data: {uuid: string, generateId?: string, isAdopt?: boolean}): Promise<void> {
    try {
        await axiosBapiInstance.post('/tracking/apiCall/adoptClick', {}, {params: data});
    }
    catch {
        // ignore error
    }
}

// 查询接口代码正则匹配
export async function getCodeRegExp(params: RepoRegexParams): Promise<RepoFindCodeRegular[]> {
    const res = await axiosBapiInstance.get('/repoRegularMapping/queryByRepo', {params});
    if (!res.data.data || res.data?.code !== 200) {
        return [];
    }
    return res.data.data;
}

// 触发生成正则，并返回结果
export async function startGenerateRegular(
    data: {repo: string, path: string, content: string, username: string}
): Promise<RepoFindCodeRegular | null> {
    const res = await axiosInstance.post('/code/regular', data);
    if (!res.data.data || res.data?.code !== 200) {
        return null;
    }
    return res.data.data;
}

// 获取 api 文档匹配关键字
export async function getApiNameKeyword(): Promise<string[] | null> {
    try {
        const res = await axiosInstance.get('/code/completion/keywords');
        if (!res.data.data || res.data?.code !== 200) {
            return null;
        }
        return res.data.data;
    }
    catch {
        return null;
    }
}

export const memorizedGetApiNameKeyword = pMemoize(getApiNameKeyword);

// 获取续写
export async function codeCompletionV2(
    data: Omit<CodeCompletion, 'interfaceCode' | 'type'> & SearchImplementCode
): Promise<{content: string, generateId: string}> {
    const res = await axiosInstance.post('/code/completion/v2', data, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
    if (!res.data?.data || res.data?.code !== 200) {
        throw new Error(res.data.message ?? '续写接口调用代码生成失败');
    }
    const {content, generateId} = res.data.data;
    return {content, generateId};
}
