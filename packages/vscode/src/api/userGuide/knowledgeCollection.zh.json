[{"id": 1, "title": "🛠 针对文档内容提问", "displayQuery": "TensorBoard进行模型训练过程的可视化  #TensorFlow ", "primaryChoice": {"promptQuery": "TensorBoard进行模型训练过程的可视化  #TensorFlow ", "agent": "AutoWork", "knowledgeList": [{"id": "f980f2e5-0173-41f7-ae9a-f138aa83af66", "name": "TensorFlow", "type": "SYSTEM"}]}}, {"id": 2, "title": "🛠 针对文档内容提问", "displayQuery": "Spring MVC的流程 #Spring Framework ", "primaryChoice": {"promptQuery": "Spring MVC的流程 #Spring Framework ", "agent": "AutoWork", "knowledgeList": [{"id": "28d0d78e-a8d2-41aa-8887-c8b088c59145", "name": "Spring Framework", "type": "SYSTEM"}]}}, {"id": 3, "title": "🛠 API相关问题", "displayQuery": " #微博热搜 生成这个接口的调用代码", "primaryChoice": {"promptQuery": " #微博热搜 生成这个接口的调用代码", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 4, "title": "🛠 API相关问题", "displayQuery": " #微博热搜 生成包含20条数据的mock响应", "primaryChoice": {"promptQuery": " #微博热搜 生成包含20条数据的mock响应", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 5, "title": "🛠 API相关问题", "displayQuery": " #微博热搜 生成测试异常场景的测试用例", "primaryChoice": {"promptQuery": " #微博热搜 生成测试异常场景的测试用例", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}]