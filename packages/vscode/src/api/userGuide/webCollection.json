[{"id": 1, "title": "📂 Summarize the webpage content", "displayQuery": "Summarize the webpage content. #version control - How do I undo the most recent local commits in Git? - Stack Overflow ", "primaryChoice": {"promptQuery": "Summarize the webpage content. #version control - How do I undo the most recent local commits in Git? - Stack Overflow ", "agent": "AutoWork", "knowledgeList": [{"id": "e8524c71-6ce0-4724-b088-96f9ef2e84a6", "name": "version control - How do I undo the most recent local commits in Git? - Stack Overflow", "type": "URL"}]}}, {"id": 2, "title": "📂 Summarize the webpage content", "displayQuery": "Summarize the content of the paper. #https://arxiv.org/pdf/2312.12832 ", "primaryChoice": {"promptQuery": "Summarize the content of the paper. #https://arxiv.org/pdf/2312.12832 ", "agent": "AutoWork", "knowledgeList": [{"id": "ec7d9681-b940-4437-b40d-55fbac4feb79", "name": "https://arxiv.org/pdf/2312.12832", "type": "URL"}]}}, {"id": 3, "title": "💡 Conduct an online search", "displayQuery": " #Web Search What is the technical implementation of GPT-4o?", "primaryChoice": {"promptQuery": " #Web Search What is the technical implementation of GPT-4o?", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}, {"id": 4, "title": "💡 Conduct an online search", "displayQuery": " #Web What are the features of Ferret-UI LLM？", "primaryChoice": {"promptQuery": " #Web What are the features of Ferret-UI LLM？", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}, {"id": 5, "title": "💡 Conduct an online search", "displayQuery": " #Web Trending python repositories on GitHub today", "primaryChoice": {"promptQuery": " #Web Trending python repositories on GitHub today", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}]