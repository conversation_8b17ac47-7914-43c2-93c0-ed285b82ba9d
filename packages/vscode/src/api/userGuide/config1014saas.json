[{"id": 54, "title": "🖇️ 总结网页内容", "displayQuery": "总结下这个网页内容 #ChatGPT 下一代模型官宣！比 GPT-4 强 100 倍 ", "primaryChoice": {"promptQuery": "总结下这个网页内容 #ChatGPT 下一代模型官宣！比 GPT-4 强 100 倍 ", "agent": "Comate", "knowledgeList": [{"id": "2581fa13-825a-45dd-9ccc-862bc04f6586", "type": "URL"}]}, "type": "web", "language": "zh"}, {"id": 55, "title": "🖇️ 总结网页内容", "displayQuery": " #一文掌握 Prompt：万能框架+优化技巧+常用指标  如何写好Prompt", "primaryChoice": {"promptQuery": " #一文掌握 Prompt：万能框架+优化技巧+常用指标  如何写好Prompt", "agent": "Comate", "knowledgeList": [{"id": "5ee9aff6-bbd3-46b2-94b4-67f18a266639", "type": "URL"}]}, "type": "web", "language": "zh"}, {"id": 56, "title": "🖇️ 总结网页内容", "displayQuery": " #手机导航上的红绿灯倒计时，是怎么来的  用到了哪些核心技术", "primaryChoice": {"promptQuery": " #手机导航上的红绿灯倒计时，是怎么来的  用到了哪些核心技术", "agent": "Comate", "knowledgeList": [{"id": "96a02ee6-2cb0-4f1f-98d1-b79e03afa526", "type": "URL"}]}, "type": "web", "language": "zh"}, {"id": 57, "title": "🖇️ 总结网页内容", "displayQuery": " #VS Code Copilot、Zed AI、Cursor 对比 - 开发调优 - LINUX DO  总结有哪些差异", "primaryChoice": {"promptQuery": " #VS Code Copilot、Zed AI、Cursor 对比 - 开发调优 - LINUX DO  总结有哪些差异", "agent": "Comate", "knowledgeList": [{"id": "06bf681d-cfe5-4db9-a0cd-8a4e3839dfba", "type": "URL"}]}, "type": "web", "language": "zh"}, {"id": 58, "title": "📖 学习官网最佳实践生成代码", "displayQuery": " #千帆开发文档  如何调用文心大模型？", "primaryChoice": {"promptQuery": " #千帆开发文档  如何调用文心大模型？", "agent": "Comate", "knowledgeList": [{"id": "c5d5d924-9c8b-4771-9858-7aeffe322121", "name": "千帆官方开发文档", "type": "SYSTEM"}]}, "type": "web", "language": "zh"}, {"id": 59, "title": "🔎 通过网络检索最新技术动态", "displayQuery": " #网络检索 如何在Windows或Mac上部署Llama", "primaryChoice": {"promptQuery": " #网络检索 如何在Windows或Mac上部署Llama", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}, "type": "web", "language": "zh"}, {"id": 60, "title": "🔎 通过网络检索最新技术动态", "displayQuery": " #网络检索 萝卜快跑的技术原理", "primaryChoice": {"promptQuery": " #网络检索 萝卜快跑的技术原理", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}, "type": "web", "language": "zh"}, {"id": 61, "title": "🔎 通过网络检索最新技术动态", "displayQuery": " #网络检索 Cursor AI可以取代程序员吗？", "primaryChoice": {"promptQuery": " #网络检索 Cursor AI可以取代程序员吗？", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}, "type": "web", "language": "zh"}, {"id": 62, "title": "🔎 通过网络检索最新技术动态", "displayQuery": " #网络检索 2024年最新前端技术趋势", "primaryChoice": {"promptQuery": " #网络检索 2024年最新前端技术趋势", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}, "type": "web", "language": "zh"}, {"id": 63, "title": "🔎 通过网络检索最新技术动态", "displayQuery": " #网络检索 如何在Windows或Mac上部署Llama", "primaryChoice": {"promptQuery": " #网络检索 如何在Windows或Mac上部署Llama", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}, "type": "web", "language": "zh"}, {"id": 64, "title": "🅰️ 生成百度地图 API 调用代码", "displayQuery": " #骑行路线规划  生成这个百度地图接口的调用代码", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": " #骑行路线规划  生成这个百度地图接口的调用代码", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "骑行路线规划", "type": "API"}]}}, {"id": 65, "title": "🅰️ 生成微博热搜 API Mock 响应", "displayQuery": " #微博热搜  生成包含20条数据的mock响应", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": " #微博热搜  生成包含20条数据的mock响应", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 66, "title": "🪜 全库代码安全扫描与修复", "displayQuery": " /代码安全 ", "primaryChoice": {"promptQuery": "", "agent": "security", "slash": "codeScan", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 67, "title": "📐 总结变更内容创建 Git issue", "displayQuery": " @Git /创建 issue ", "primaryChoice": {"promptQuery": "", "agent": "git", "slash": "createIssue", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 68, "title": "📐 基于变更生成 Git 提交信息", "displayQuery": " @Git /生成提交 ", "primaryChoice": {"promptQuery": "", "agent": "git", "slash": "commitMessage", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 68, "title": "🛠 实现常见代码转化", "displayQuery": " @工具箱 /base64解码 5Z+65LqO5paH5b+D5aSn5qih5Z6L77yM57uT5ZCI55m+5bqm57yW56iL5aSn5pWw5o2u77yM5Li65L2g55Sf5oiQ5LyY6LSo57yW56iL5Luj56CB44CCCuaWh+W/g+W/q+eggSAtIEJhaWR1IENvbWF0Ze+8jOabtOaHguS9oOeahEFJ57yW56iL5LyZ5Ly077yM56CU5Y+R5pWI546H5o+Q5Y2H5aW95biu5omL44CCCgoKCg==", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "5Z+65LqO5paH5b+D5aSn5qih5Z6L77yM57uT5ZCI55m+5bqm57yW56iL5aSn5pWw5o2u77yM5Li65L2g55Sf5oiQ5LyY6LSo57yW56iL5Luj56CB44CCCuaWh+W/g+W/q+eggSAtIEJhaWR1IENvbWF0Ze+8jOabtOaHguS9oOeahEFJ57yW56iL5LyZ5Ly077yM56CU5Y+R5pWI546H5o+Q5Y2H5aW95biu5omL44CCCgoKCg==", "agent": "dev-tools", "slash": "base64-decode", "knowledgeList": []}}, {"id": 69, "title": "🛠 实现常见代码转化", "displayQuery": " @工具箱 /JSON 格式化  json：{\"name\":\"Comate\",\"age\":\"3\"}", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "{\"name\":\"Comate\",\"age\":\"3\"}", "agent": "dev-tools", "slash": "json-format", "knowledgeList": []}}, {"id": 70, "title": "🗞️ 使用飞桨插件解决飞桨框架问题", "displayQuery": " @飞桨 /智能问答 飞桨中如何进行梯度裁剪？", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "飞桨中如何进行梯度裁剪？", "agent": "paddle", "slash": "chat", "knowledgeList": []}}, {"id": 71, "title": "🗞️ 使用飞桨插件解决飞桨框架问题", "displayQuery": " @飞桨 /代码生成 实现ResNet50训练与评估", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "实现ResNet50训练与评估", "agent": "paddle", "slash": "chat", "knowledgeList": []}}]