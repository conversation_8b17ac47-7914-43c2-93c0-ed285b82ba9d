[{"id": 1, "title": "🛠 Ask questions about the document", "displayQuery": "‘TensorBoard’ Visualize the process of model training  #TensorFlow ", "primaryChoice": {"promptQuery": "‘TensorBoard’ Visualize the process of model training  #TensorFlow ", "agent": "AutoWork", "knowledgeList": [{"id": "f980f2e5-0173-41f7-ae9a-f138aa83af66", "name": "TensorFlow", "type": "SYSTEM"}]}}, {"id": 2, "title": "🛠 Ask questions about the document", "displayQuery": "What is the process of Spring MVC? #Spring Framework ", "primaryChoice": {"promptQuery": "What is the process of Spring MVC? #Spring Framework ", "agent": "AutoWork", "knowledgeList": [{"id": "28d0d78e-a8d2-41aa-8887-c8b088c59145", "name": "Spring Framework", "type": "SYSTEM"}]}}, {"id": 3, "title": "🛠 API related issues", "displayQuery": " #Weibo Hot Search Generate the calling code for this interface", "primaryChoice": {"promptQuery": " #Weibo Hot Search Generate the calling code for this interface", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 4, "title": "🛠 API related issue", "displayQuery": " #Weibo Hot Search Generate a mock response containing 20 pieces of data", "primaryChoice": {"promptQuery": " #Weibo Hot Search Generate a mock response containing 20 pieces of data", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 5, "title": "🛠 API related issues", "displayQuery": " #Weibo Hot Search Generate test cases for testing exceptional scenarios", "primaryChoice": {"promptQuery": " #Weibo Hot Search Generate test cases for testing exceptional scenarios", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}]