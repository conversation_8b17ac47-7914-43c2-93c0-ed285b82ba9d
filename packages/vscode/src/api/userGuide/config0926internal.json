[{"id": 54, "title": "🖇️ 总结网页内容", "displayQuery": "总结下这个网页内容 #ChatGPT 下一代模型官宣！比 GPT-4 强 100 倍 ", "primaryChoice": {"promptQuery": "总结下这个网页内容 #ChatGPT 下一代模型官宣！比 GPT-4 强 100 倍 ", "agent": "Comate", "knowledgeList": [{"id": "dc8a68c6-cc12-47fc-81cf-fe89a60df1bc", "type": "URL"}]}, "type": "web", "language": "zh"}, {"id": 55, "title": "🖇️ 总结网页内容", "displayQuery": " #一文掌握 Prompt：万能框架+优化技巧+常用指标  如何写好Prompt", "primaryChoice": {"promptQuery": " #一文掌握 Prompt：万能框架+优化技巧+常用指标  如何写好Prompt", "agent": "Comate", "knowledgeList": [{"id": "f05a53d1-53e6-44fb-890f-4c26d5e86286", "type": "URL"}]}, "type": "web", "language": "zh"}, {"id": 56, "title": "🖇️ 总结网页内容", "displayQuery": " #手机导航上的红绿灯倒计时，是怎么来的  用到了哪些核心技术", "primaryChoice": {"promptQuery": " #手机导航上的红绿灯倒计时，是怎么来的  用到了哪些核心技术", "agent": "Comate", "knowledgeList": [{"id": "ff61a750-a35a-40e2-bafc-dbbde3add596", "type": "URL"}]}, "type": "web", "language": "zh"}, {"id": 57, "title": "🖇️ 总结网页内容", "displayQuery": " #VS Code Copilot、Zed AI、Cursor 对比 - 开发调优 - LINUX DO  总结有哪些差异", "primaryChoice": {"promptQuery": " #VS Code Copilot、Zed AI、Cursor 对比 - 开发调优 - LINUX DO  总结有哪些差异", "agent": "Comate", "knowledgeList": [{"id": "a92b2282-7032-45c3-8965-09b9bd12ffc7", "type": "URL"}]}, "type": "web", "language": "zh"}, {"id": 58, "title": "📖 学习官网最佳实践生成代码", "displayQuery": " #千帆开发文档  如何调用文心大模型？", "primaryChoice": {"promptQuery": " #千帆开发文档  如何调用文心大模型？", "agent": "Comate", "knowledgeList": [{"id": "c5d5d924-9c8b-4771-9858-7aeffe322121", "name": "千帆官方开发文档", "type": "SYSTEM"}]}, "type": "web", "language": "zh"}, {"id": 59, "title": "🔎 通过网络检索最新技术动态", "displayQuery": " #网络检索 如何在Windows或Mac上部署Llama", "primaryChoice": {"promptQuery": " #网络检索 如何在Windows或Mac上部署Llama", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}, "type": "web", "language": "zh"}, {"id": 60, "title": "🔎 通过网络检索最新技术动态", "displayQuery": " #网络检索 萝卜快跑的技术原理", "primaryChoice": {"promptQuery": " #网络检索 萝卜快跑的技术原理", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}, "type": "web", "language": "zh"}, {"id": 61, "title": "🔎 通过网络检索最新技术动态", "displayQuery": " #网络检索 Cursor AI可以取代程序员吗？", "primaryChoice": {"promptQuery": " #网络检索 Cursor AI可以取代程序员吗？", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}, "type": "web", "language": "zh"}, {"id": 62, "title": "🔎 通过网络检索最新技术动态", "displayQuery": " #网络检索 2024年最新前端技术趋势", "primaryChoice": {"promptQuery": " #网络检索 2024年最新前端技术趋势", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}, "type": "web", "language": "zh"}, {"id": 63, "title": "🔎 通过网络检索最新技术动态", "displayQuery": " #网络检索 如何在Windows或Mac上部署Llama", "primaryChoice": {"promptQuery": " #网络检索 如何在Windows或Mac上部署Llama", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}, "type": "web", "language": "zh"}, {"id": 64, "title": "🅰️ 生成百度地图 API 调用代码", "displayQuery": " #骑行路线规划  生成这个百度地图接口的调用代码", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": " #骑行路线规划  生成这个百度地图接口的调用代码", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "骑行路线规划", "type": "API"}]}}, {"id": 65, "title": "🅰️ 生成微博热搜 API Mock 响应", "displayQuery": " #微博热搜  生成包含20条数据的mock响应", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": " #微博热搜  生成包含20条数据的mock响应", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 66, "title": "🪜 全库代码安全扫描与修复", "displayQuery": " /代码安全 ", "primaryChoice": {"promptQuery": "", "agent": "security", "slash": "codeScan", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 67, "title": "📐 总结变更内容创建 Git issue", "displayQuery": " @Git /创建 issue ", "primaryChoice": {"promptQuery": "", "agent": "git", "slash": "createIssue", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 68, "title": "📈 使用 Jarvis 插件完成应用部署", "displayQuery": " @Jarvis /应用部署 ", "primaryChoice": {"promptQuery": "", "agent": "jarvis", "slash": "jarvisAppDeploy", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 69, "title": "📯 使用 AppDev 插件解决 App 编码问题", "displayQuery": " @AppDev /智能问答  Android云控组件怎么用？", "primaryChoice": {"promptQuery": "Android云控组件怎么用？", "agent": "smartapp", "slash": "chat", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 70, "title": "📯 使用 AppDev 插件解决 App 编码问题", "displayQuery": " @AppDev /智能问答  如何添加新的端能力？", "primaryChoice": {"promptQuery": "如何添加新的端能力？", "agent": "smartapp", "slash": "chat", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 71, "title": "🧬 使用 GDP 插件解决 GDP 使用问题", "displayQuery": " @GDP /智能问答  日志中出现 context canceled 的错误怎么解决？", "primaryChoice": {"promptQuery": "日志中出现 context canceled 的错误怎么解决？", "agent": "gdp", "slash": "chat", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 72, "title": "🧬 使用 GDP 插件解决 GDP 使用问题", "displayQuery": " @GDP /代码生成  有一个第三方服务，它提供了http get 和post 接口， get 接口描述为 path：/abc/h/get， 服务是通过bns 对外提供服务，参数有 a=str，b=trt 两个。post 接口描述：path：abc/h/post ，参数有c=str这个参数，也get 一样的通过同一个bns 进行或者， 帮我写一个客户端代码，访问get 和post 接口，返回的结果get 结果json为{\"A\":string,\"B\":int,\"C\":string} ，post 结果json 为 {\"A\":string,”D”:int,”E”:int}", "primaryChoice": {"promptQuery": "有一个第三方服务，它提供了http get 和post 接口， get 接口描述为 path：/abc/h/get， 服务是通过bns 对外提供服务，参数有 a=str，b=trt 两个。post 接口描述：path：abc/h/post ，参数有c=str这个参数，也get 一样的通过同一个bns 进行或者， 帮我写一个客户端代码，访问get 和post 接口，返回的结果get 结果json为{\"A\":string,\"B\":int,\"C\":string} ，post 结果json 为 {\"A\":string,”D”:int,”E”:int}", "agent": "gdp", "slash": "codeGenerator", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 73, "title": "🧿 使用 TOR 助理查找我编辑的文档", "displayQuery": " @TOR 助理 /找知识库文档  查找我编辑的文档", "primaryChoice": {"promptQuery": "查找我编辑的文档", "agent": "tor", "slash": "docSearch", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 74, "title": "🧿 使用 TOR 助理为变量命名", "displayQuery": " @TOR 助理 /代码变量起名 为“插件变量注册”起个变量名", "primaryChoice": {"promptQuery": "为“插件变量注册”起个变量名", "agent": "tor", "slash": "codeNameCreate", "knowledgeList": []}, "language": "zh", "type": "other"}, {"id": 75, "title": "☕️ 使用 iCafe 插件查询所需卡片", "displayQuery": " @iCafe 查询最近14天我负责未完成的卡片", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "最近14天我负责未完成的卡片", "agent": "icafe", "slash": "searchBySelf", "knowledgeList": []}}, {"id": 76, "title": "🚰 使用 iPipe 插件查询所需流水线", "displayQuery": " @iPipe /获取我收藏的流水线 ", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "", "agent": "ipipe", "slash": "getCollectiPipe", "knowledgeList": []}}, {"id": 77, "title": "📐 基于变更生成 Git 提交信息", "displayQuery": " @Git /生成提交 ", "primaryChoice": {"promptQuery": "", "agent": "git", "slash": "commitMessage", "knowledgeList": []}, "language": "zh", "type": "other"}]