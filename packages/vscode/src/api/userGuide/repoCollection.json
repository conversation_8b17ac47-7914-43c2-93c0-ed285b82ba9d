[{"id": 1, "rule": "REPO", "reason": "Since no open codebase was found in the current workspace, I am using the open-source Github codebase github.com/langchain-ai/langchain as an example. Once there is an open Git codebase in your local workspace, you can analyze it at any time with #current codebase. The above information is translated into English.", "title": "🧐 Sort out the current codebase architecture", "displayQuery": "Sort out the #Current codebase architecture", "primaryChoice": {"promptQuery": "Sort out the #Current codebase architecture", "agent": "AutoWork", "knowledgeList": [{"id": "repo", "name": "repo", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "Sort out the #Current codebase architecture", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}]}}, {"id": 2, "rule": "REPO", "reason": "Since no open codebase was found in the current workspace, I am using the open-source Github codebase github.com/langchain-ai/langchain as an example. Once there is an open Git codebase in your local workspace, you can analyze it at any time with #current codebase. The above information is translated into English.", "title": "🧐 Sort out the current codebase architecture", "displayQuery": " #Current codebase What is the main purpose of this application?", "primaryChoice": {"promptQuery": "Sort out the #Current codebase architecture", "agent": "AutoWork", "knowledgeList": [{"id": "repo", "name": "repo", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": " #Current codebase What is the main purpose of this application?", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}]}}, {"id": 3, "rule": "REPO", "reason": "Since no open codebase was found in the current workspace, I am using the open-source Github codebase github.com/langchain-ai/langchain as an example. Once there is an open Git codebase in your local workspace, you can analyze it at any time with #current codebase. The above information is translated into English.", "title": "🧐 Sort out the current codebase architecture", "displayQuery": " #Current file Are there any specific libraries or frameworks being used?", "primaryChoice": {"promptQuery": "Are there any specific libraries or frameworks being used?", "agent": "AutoWork", "knowledgeList": [{"id": "currentFile", "name": "currentFile", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "Are there any specific libraries or frameworks being used?", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}, {"id": "currentFile", "name": "currentFile", "type": "FILE"}]}}, {"id": 2, "rule": "REPO", "reason": "Since no open codebase was found in the current workspace, I am using the open-source Github codebase github.com/langchain-ai/langchain as an example. Once there is an open Git codebase in your local workspace, you can analyze it at any time with #current codebase. The above information is translated into English.", "title": "🧐 Sort out the current codebase architecture", "displayQuery": " #Current file Analyze the current file usage", "primaryChoice": {"promptQuery": "Analyze the current file usage", "agent": "AutoWork", "knowledgeList": [{"id": "currentFile", "name": "currentFile", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "Analyze the current file usage", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}, {"id": "currentFile", "name": "currentFile", "type": "FILE"}]}}]