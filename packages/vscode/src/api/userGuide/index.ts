/**
 * @file 用户引导配置
 */

import {KnowledgeList, ConfigItem} from '@shared/protocols';
import {info} from '../../common/outputChannel';
import {createAxios, withProxy} from '../common';
import {ResponseBase} from '../index';

export interface Choice {
    promptQuery: string;
    agent: string;
    slash?: string;
    knowledgeList: KnowledgeList[];
}

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu-int.com/api',
    'internal-test': 'http://10.11.58.93:8080',
    saas: 'https://comate.baidu.com/api',
    'saas-test': 'https://comate-cop.now.baidu-int.com/api',
    poc: 'https://comate.baidu.com/api',
};

const axiosInstance = withProxy(createAxios({
    baseURL: BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
}));

info(`UserGuide api baseURL: ${axiosInstance.defaults.baseURL}`);

export async function userGuideConfig(repo: string) {
    const res = await axiosInstance.get<ResponseBase<ConfigItem[]>>(
        `/userGuide?repo=${repo}`
    );
    return res;
}

export async function userGuideConfigWithExclude(repo: string, lastUserGuideIds: number[]) {
    const excludeUuids = lastUserGuideIds.join(',');
    const res = await axiosInstance.get<ResponseBase<ConfigItem[]>>(
        `/userGuide?excludeUuids=${excludeUuids}&repo=${repo}`
    );
    return res;
}
