[{"id": 1, "title": "📂 总结网页内容", "displayQuery": "总结下这个网页内容 #version control - How do I undo the most recent local commits in Git? - Stack Overflow ", "language": "zh", "type": "knowledge", "primaryChoice": {"promptQuery": "总结下这个网页内容 #version control - How do I undo the most recent local commits in Git? - Stack Overflow ", "agent": "AutoWork", "knowledgeList": [{"id": "e8524c71-6ce0-4724-b088-96f9ef2e84a6", "name": "version control - How do I undo the most recent local commits in Git? - Stack Overflow", "type": "URL"}]}}, {"id": 2, "title": "📂 总结网页内容", "displayQuery": "总结论文内容 #https://arxiv.org/pdf/2312.12832 ", "language": "zh", "type": "knowledge", "primaryChoice": {"promptQuery": "总结论文内容 #https://arxiv.org/pdf/2312.12832 ", "agent": "AutoWork", "knowledgeList": [{"id": "ec7d9681-b940-4437-b40d-55fbac4feb79", "name": "https://arxiv.org/pdf/2312.12832", "type": "URL"}]}}, {"id": 3, "title": "💡 进行网络搜索", "displayQuery": " #网络检索 gpt 4o 的技术实现是什么？", "language": "zh", "type": "knowledge", "primaryChoice": {"promptQuery": " #网络检索 gpt 4o 的技术实现是什么？", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}, {"id": 4, "title": "💡 进行网络搜索", "displayQuery": " #网络检索 苹果发布的手机端模型Ferret-UI有什么特点？", "language": "zh", "type": "knowledge", "primaryChoice": {"promptQuery": " #网络检索 苹果发布的手机端模型Ferret-UI有什么特点？", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}, {"id": 5, "title": "💡 进行网络搜索", "displayQuery": " #网络检索 今日github热门python代码仓库", "language": "zh", "type": "knowledge", "primaryChoice": {"promptQuery": " #网络检索 今日github热门python代码仓库", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}, {"id": 6, "title": "📂 Summarize the webpage content", "displayQuery": "Summarize the webpage content. #version control - How do I undo the most recent local commits in Git? - Stack Overflow ", "language": "en", "type": "knowledge", "primaryChoice": {"promptQuery": "Summarize the webpage content. #version control - How do I undo the most recent local commits in Git? - Stack Overflow ", "agent": "AutoWork", "knowledgeList": [{"id": "e8524c71-6ce0-4724-b088-96f9ef2e84a6", "name": "version control - How do I undo the most recent local commits in Git? - Stack Overflow", "type": "URL"}]}}, {"id": 7, "title": "📂 Summarize the webpage content", "displayQuery": "Summarize the content of the paper. #https://arxiv.org/pdf/2312.12832 ", "language": "en", "type": "knowledge", "primaryChoice": {"promptQuery": "Summarize the content of the paper. #https://arxiv.org/pdf/2312.12832 ", "agent": "AutoWork", "knowledgeList": [{"id": "ec7d9681-b940-4437-b40d-55fbac4feb79", "name": "https://arxiv.org/pdf/2312.12832", "type": "URL"}]}}, {"id": 8, "title": "💡 Conduct an online search", "displayQuery": " #Web Search What is the technical implementation of GPT-4o?", "language": "en", "type": "knowledge", "primaryChoice": {"promptQuery": " #Web Search What is the technical implementation of GPT-4o?", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}, {"id": 9, "title": "💡 Conduct an online search", "displayQuery": " #Web What are the features of Ferret-UI LLM？", "language": "en", "type": "knowledge", "primaryChoice": {"promptQuery": " #Web What are the features of Ferret-UI LLM？", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}, {"id": 10, "title": "💡 Conduct an online search", "displayQuery": " #Web Trending python repositories on GitHub today", "language": "en", "type": "knowledge", "primaryChoice": {"promptQuery": " #Web Trending python repositories on GitHub today", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}, {"id": 11, "rule": "REPO", "reason": "因当前工作空间未找到打开的代码库，所以我使用 Github 开源代码库 github.com/langchain-ai/langchain 作为示例。当本地工作空间存在打开的 Git 代码库后，您可以随时使用 #当前代码库 进行分析。", "title": "🧐 梳理当前代码库架构", "displayQuery": "帮我梳理一下 #当前代码库 架构", "language": "zh", "type": "repo", "primaryChoice": {"promptQuery": "帮我梳理一下 #当前代码库 架构", "agent": "AutoWork", "knowledgeList": [{"id": "repo", "name": "repo", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "帮我梳理一下 #当前代码库 架构", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}]}}, {"id": 12, "rule": "REPO", "reason": "因当前工作空间未找到打开的代码库，所以我使用 Github 开源代码库 github.com/langchain-ai/langchain 作为示例。当本地工作空间存在打开的 Git 代码库后，您可以随时使用 #当前代码库 进行分析。", "title": "🧐 梳理当前代码库架构", "displayQuery": " 当前代码库 这个代码库的主要用途是什么？", "language": "zh", "type": "repo", "primaryChoice": {"promptQuery": "这个代码库的主要用途是什么？", "agent": "AutoWork", "knowledgeList": [{"id": "repo", "name": "repo", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "这个代码库的主要用途是什么？", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}]}}, {"id": 13, "rule": "REPO", "reason": "因当前工作空间未找到打开的代码库，所以我使用 Github 开源代码库 github.com/langchain-ai/langchain 作为示例。当本地工作空间存在打开的 Git 代码库后，您可以随时使用 #当前代码库 进行分析。", "title": "🧐 梳理当前代码库架构", "displayQuery": " #当前文件 依赖哪些基础库和框架", "language": "zh", "type": "repo", "primaryChoice": {"promptQuery": "当前文件 依赖哪些基础库和框架？", "agent": "AutoWork", "knowledgeList": [{"id": "currentFile", "name": "currentFile", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "当前文件 依赖哪些基础库和框架？", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}, {"id": "currentFile", "name": "currentFile", "type": "FILE"}]}}, {"id": 14, "rule": "REPO", "reason": "因当前工作空间未找到打开的代码库，所以我使用 Github 开源代码库 github.com/langchain-ai/langchain 作为示例。当本地工作空间存在打开的 Git 代码库后，您可以随时使用 #当前代码库 进行分析。", "title": "🧐 梳理当前代码库架构", "displayQuery": " #当前文件 分析文件用途", "language": "zh", "type": "repo", "primaryChoice": {"promptQuery": "分析文件用途？", "agent": "AutoWork", "knowledgeList": [{"id": "currentFile", "name": "currentFile", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "分析文件用途", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}, {"id": "currentFile", "name": "currentFile", "type": "FILE"}]}}, {"id": 15, "rule": "REPO", "reason": "Since no open codebase was found in the current workspace, I am using the open-source Github codebase github.com/langchain-ai/langchain as an example. Once there is an open Git codebase in your local workspace, you can analyze it at any time with #current codebase. The above information is translated into English.", "title": "🧐 Sort out the current codebase architecture", "displayQuery": "Sort out the #Current codebase architecture", "language": "en", "type": "repo", "primaryChoice": {"promptQuery": "Sort out the #Current codebase architecture", "agent": "AutoWork", "knowledgeList": [{"id": "repo", "name": "repo", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "Sort out the #Current codebase architecture", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}]}}, {"id": 16, "rule": "REPO", "reason": "Since no open codebase was found in the current workspace, I am using the open-source Github codebase github.com/langchain-ai/langchain as an example. Once there is an open Git codebase in your local workspace, you can analyze it at any time with #current codebase. The above information is translated into English.", "title": "🧐 Sort out the current codebase architecture", "displayQuery": " #Current codebase What is the main purpose of this application?", "language": "en", "type": "repo", "primaryChoice": {"promptQuery": "Sort out the #Current codebase architecture", "agent": "AutoWork", "knowledgeList": [{"id": "repo", "name": "repo", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": " #Current codebase What is the main purpose of this application?", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}]}}, {"id": 17, "rule": "REPO", "reason": "Since no open codebase was found in the current workspace, I am using the open-source Github codebase github.com/langchain-ai/langchain as an example. Once there is an open Git codebase in your local workspace, you can analyze it at any time with #current codebase. The above information is translated into English.", "title": "🧐 Sort out the current codebase architecture", "displayQuery": " #Current file Are there any specific libraries or frameworks being used?", "language": "en", "type": "repo", "primaryChoice": {"promptQuery": "Are there any specific libraries or frameworks being used?", "agent": "AutoWork", "knowledgeList": [{"id": "currentFile", "name": "currentFile", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "Are there any specific libraries or frameworks being used?", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}, {"id": "currentFile", "name": "currentFile", "type": "FILE"}]}}, {"id": 18, "rule": "REPO", "reason": "Since no open codebase was found in the current workspace, I am using the open-source Github codebase github.com/langchain-ai/langchain as an example. Once there is an open Git codebase in your local workspace, you can analyze it at any time with #current codebase. The above information is translated into English.", "title": "🧐 Sort out the current codebase architecture", "displayQuery": " #Current file Analyze the current file usage", "language": "en", "type": "repo", "primaryChoice": {"promptQuery": "Analyze the current file usage", "agent": "AutoWork", "knowledgeList": [{"id": "currentFile", "name": "currentFile", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "Analyze the current file usage", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}, {"id": "currentFile", "name": "currentFile", "type": "FILE"}]}}, {"id": 19, "title": "🛠 使用插件解决专业问题", "displayQuery": " /base64解码 5Z+65LqO5paH5b+D5aSn5qih5Z6L77yM57uT5ZCI55m+5bqm57yW56iL5aSn5pWw5o2u77yM5Li65L2g55Sf5oiQ5LyY6LSo57yW56iL5Luj56CB44CCCuaWh+W/g+W/q+eggSAtIEJhaWR1IENvbWF0Ze+8jOabtOaHguS9oOeahEFJ57yW56iL5LyZ5Ly077yM56CU5Y+R5pWI546H5o+Q5Y2H5aW95biu5omL44CCCgoKCg==", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "5Z+65LqO5paH5b+D5aSn5qih5Z6L77yM57uT5ZCI55m+5bqm57yW56iL5aSn5pWw5o2u77yM5Li65L2g55Sf5oiQ5LyY6LSo57yW56iL5Luj56CB44CCCuaWh+W/g+W/q+eggSAtIEJhaWR1IENvbWF0Ze+8jOabtOaHguS9oOeahEFJ57yW56iL5LyZ5Ly077yM56CU5Y+R5pWI546H5o+Q5Y2H5aW95biu5omL44CCCgoKCg==", "agent": "dev-tools", "slash": "base64-decode", "knowledgeList": []}}, {"id": 20, "title": "🛠 使用插件解决专业问题", "displayQuery": " @飞桨 飞桨中如何进行梯度裁剪？", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "飞桨中如何进行梯度裁剪？", "agent": "paddle", "slash": "chat", "knowledgeList": []}}, {"id": 21, "title": "🛠 使用插件解决专业问题", "displayQuery": " @百度小程序 百度小程序如何实现一个搜索框组？", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "百度小程序如何实现一个搜索框组？", "agent": "smartapp", "slash": "chat", "knowledgeList": []}}, {"id": 22, "title": "🛠 Solve professional issues using plugins", "displayQuery": " /base64 decode 5Z+65LqO5paH5b+D5aSn5qih5Z6L77yM57uT5ZCI55m+5bqm57yW56iL5aSn5pWw5o2u77yM5Li65L2g55Sf5oiQ5LyY6LSo57yW56iL5Luj56CB44CCCuaWh+W/g+W/q+eggSAtIEJhaWR1IENvbWF0Ze+8jOabtOaHguS9oOeahEFJ57yW56iL5LyZ5Ly077yM56CU5Y+R5pWI546H5o+Q5Y2H5aW95biu5omL44CCCgoKCg==", "language": "en", "type": "other", "primaryChoice": {"promptQuery": "5Z+65LqO5paH5b+D5aSn5qih5Z6L77yM57uT5ZCI55m+5bqm57yW56iL5aSn5pWw5o2u77yM5Li65L2g55Sf5oiQ5LyY6LSo57yW56iL5Luj56CB44CCCuaWh+W/g+W/q+eggSAtIEJhaWR1IENvbWF0Ze+8jOabtOaHguS9oOeahEFJ57yW56iL5LyZ5Ly077yM56CU5Y+R5pWI546H5o+Q5Y2H5aW95biu5omL44CCCgoKCg==", "agent": "dev-tools", "slash": "base64-decode", "knowledgeList": []}}, {"id": 23, "title": "🛠 Solve professional issues using plugins", "displayQuery": " @Paddle How to perform gradient clipping in Paddle?", "language": "en", "type": "other", "primaryChoice": {"promptQuery": "How to perform gradient clipping in Paddle?", "agent": "paddle", "slash": "chat", "knowledgeList": []}}, {"id": 24, "title": "🛠 Solve professional issues using plugins", "displayQuery": " @smartapp How to implement a search box group in Baidu smartapp?", "language": "en", "type": "other", "primaryChoice": {"promptQuery": "How to implement a search box group in Baidu smartapp？", "agent": "smartapp", "slash": "chat", "knowledgeList": []}}, {"id": 25, "title": "🛠 使用知识增强生成效果", "displayQuery": "TensorBoard进行模型训练过程的可视化  #TensorFlow ", "language": "zh", "type": "knowledge", "primaryChoice": {"promptQuery": "TensorBoard进行模型训练过程的可视化  #TensorFlow ", "agent": "AutoWork", "knowledgeList": [{"id": "f980f2e5-0173-41f7-ae9a-f138aa83af66", "name": "TensorFlow", "type": "SYSTEM"}]}}, {"id": 26, "title": "🛠 使用知识增强生成效果", "displayQuery": "Spring MVC的流程 #Spring Framework ", "language": "zh", "type": "knowledge", "primaryChoice": {"promptQuery": "Spring MVC的流程 #Spring Framework ", "agent": "AutoWork", "knowledgeList": [{"id": "28d0d78e-a8d2-41aa-8887-c8b088c59145", "name": "Spring Framework", "type": "SYSTEM"}]}}, {"id": 27, "title": "🅰️ 围绕 API 高效研发", "displayQuery": " #微博热搜 生成这个接口的调用代码", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": " #微博热搜 生成这个接口的调用代码", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 28, "title": "🅰️ 围绕 API 高效研发", "displayQuery": " #微博热搜 生成包含20条数据的mock响应", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": " #微博热搜 生成包含20条数据的mock响应", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 29, "title": "🅰️ 围绕 API 高效研发", "displayQuery": " #微博热搜 生成测试异常场景的测试用例", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": " #微博热搜 生成测试异常场景的测试用例", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 30, "title": "🛠 Enhance Generation with Knowledge", "displayQuery": "‘TensorBoard’ Visualize the process of model training  #TensorFlow ", "language": "en", "type": "knowledge", "primaryChoice": {"promptQuery": "‘TensorBoard’ Visualize the process of model training  #TensorFlow ", "agent": "AutoWork", "knowledgeList": [{"id": "f980f2e5-0173-41f7-ae9a-f138aa83af66", "name": "TensorFlow", "type": "SYSTEM"}]}}, {"id": 31, "title": "🛠 Enhance Generation with Knowledge", "displayQuery": "What is the process of Spring MVC? #Spring Framework ", "language": "en", "type": "knowledge", "primaryChoice": {"promptQuery": "What is the process of Spring MVC? #Spring Framework ", "agent": "AutoWork", "knowledgeList": [{"id": "28d0d78e-a8d2-41aa-8887-c8b088c59145", "name": "Spring Framework", "type": "SYSTEM"}]}}, {"id": 32, "title": "🅰️ Efficient Development Around APIs", "displayQuery": " #Weibo Hot Search Generate the calling code for this interface", "language": "en", "type": "other", "primaryChoice": {"promptQuery": " #Weibo Hot Search Generate the calling code for this interface", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 33, "title": "🅰️ Efficient Development Around APIs", "displayQuery": " #Weibo Hot Search Generate a mock response containing 20 pieces of data", "language": "en", "type": "other", "primaryChoice": {"promptQuery": " #Weibo Hot Search Generate a mock response containing 20 pieces of data", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 34, "title": "🅰️ Efficient Development Around APIs", "displayQuery": " #Weibo Hot Search Generate test cases for testing exceptional scenarios", "language": "en", "type": "other", "primaryChoice": {"promptQuery": " #Weibo Hot Search Generate test cases for testing exceptional scenarios", "agent": "AutoWork", "slash": "生成API调用代码", "knowledgeList": [{"id": "", "display": "微博热搜热榜", "type": "API"}]}}, {"id": 36, "title": "🛠 使用插件解决专业问题", "displayQuery": " @iCafe 查询最近14天我负责未完成的卡片", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "最近14天我负责未完成的卡片", "agent": "icafe", "slash": "searchBySelf", "knowledgeList": []}}, {"id": 37, "title": "🛠 使用插件解决专业问题", "displayQuery": " @飞桨 将以下代码转换为飞桨", "language": "zh", "type": "other", "primaryChoice": {"promptQuery": "", "agent": "paddle", "slash": "code-convert", "knowledgeList": []}}]