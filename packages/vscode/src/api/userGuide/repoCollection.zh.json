[{"id": 1, "rule": "REPO", "reason": "因当前工作空间未找到打开的代码库，所以我使用 Github 开源代码库 github.com/langchain-ai/langchain 作为示例。当本地工作空间存在打开的 Git 代码库后，您可以随时使用 #当前代码库 进行分析。", "title": "🧐 梳理当前代码库架构", "displayQuery": "帮我梳理一下 #当前代码库 架构", "primaryChoice": {"promptQuery": "帮我梳理一下 #当前代码库 架构", "agent": "AutoWork", "knowledgeList": [{"id": "repo", "name": "repo", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "帮我梳理一下 #当前代码库 架构", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}]}}, {"id": 2, "rule": "REPO", "reason": "因当前工作空间未找到打开的代码库，所以我使用 Github 开源代码库 github.com/langchain-ai/langchain 作为示例。当本地工作空间存在打开的 Git 代码库后，您可以随时使用 #当前代码库 进行分析。", "title": "🧐 梳理当前代码库架构", "displayQuery": " 当前代码库 这个代码库的主要用途是什么？", "primaryChoice": {"promptQuery": "这个代码库的主要用途是什么？", "agent": "AutoWork", "knowledgeList": [{"id": "repo", "name": "repo", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "这个代码库的主要用途是什么？", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}]}}, {"id": 3, "rule": "REPO", "reason": "因当前工作空间未找到打开的代码库，所以我使用 Github 开源代码库 github.com/langchain-ai/langchain 作为示例。当本地工作空间存在打开的 Git 代码库后，您可以随时使用 #当前代码库 进行分析。", "title": "🧐 梳理当前代码库架构", "displayQuery": " #当前文件 依赖哪些基础库和框架", "primaryChoice": {"promptQuery": "当前文件 依赖哪些基础库和框架？", "agent": "AutoWork", "knowledgeList": [{"id": "currentFile", "name": "currentFile", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "当前文件 依赖哪些基础库和框架？", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}, {"id": "currentFile", "name": "currentFile", "type": "FILE"}]}}, {"id": 4, "rule": "REPO", "reason": "因当前工作空间未找到打开的代码库，所以我使用 Github 开源代码库 github.com/langchain-ai/langchain 作为示例。当本地工作空间存在打开的 Git 代码库后，您可以随时使用 #当前代码库 进行分析。", "title": "🧐 梳理当前代码库架构", "displayQuery": " #当前文件 分析文件用途", "primaryChoice": {"promptQuery": "分析文件用途？", "agent": "AutoWork", "knowledgeList": [{"id": "currentFile", "name": "currentFile", "type": "FILE"}]}, "alternativeChoice": {"promptQuery": "分析文件用途", "agent": "AutoWork", "knowledgeList": [{"id": "github.com/langchain-ai/langchain", "name": "repo", "type": "FILE"}, {"id": "currentFile", "name": "currentFile", "type": "FILE"}]}}]