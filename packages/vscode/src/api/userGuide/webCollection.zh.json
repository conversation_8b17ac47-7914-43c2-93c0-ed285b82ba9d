[{"id": 1, "title": "📂 总结网页内容", "displayQuery": "总结下这个网页内容 #version control - How do I undo the most recent local commits in Git? - Stack Overflow ", "primaryChoice": {"promptQuery": "总结下这个网页内容 #version control - How do I undo the most recent local commits in Git? - Stack Overflow ", "agent": "AutoWork", "knowledgeList": [{"id": "e8524c71-6ce0-4724-b088-96f9ef2e84a6", "name": "version control - How do I undo the most recent local commits in Git? - Stack Overflow", "type": "URL"}]}}, {"id": 2, "title": "📂 总结网页内容", "displayQuery": "总结论文内容 #https://arxiv.org/pdf/2312.12832 ", "primaryChoice": {"promptQuery": "总结论文内容 #https://arxiv.org/pdf/2312.12832 ", "agent": "AutoWork", "knowledgeList": [{"id": "ec7d9681-b940-4437-b40d-55fbac4feb79", "name": "https://arxiv.org/pdf/2312.12832", "type": "URL"}]}}, {"id": 3, "title": "💡 进行网络搜索", "displayQuery": " #网络检索 gpt 4o 的技术实现是什么？", "primaryChoice": {"promptQuery": " #网络检索 gpt 4o 的技术实现是什么？", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}, {"id": 4, "title": "💡 进行网络搜索", "displayQuery": " #网络检索 苹果发布的手机端模型Ferret-UI有什么特点？", "primaryChoice": {"promptQuery": " #网络检索 苹果发布的手机端模型Ferret-UI有什么特点？", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}, {"id": 5, "title": "💡 进行网络搜索", "displayQuery": " #网络检索 今日github热门python代码仓库", "primaryChoice": {"promptQuery": " #网络检索 今日github热门python代码仓库", "agent": "AutoWork", "knowledgeList": [{"name": "网络检索", "type": "WEB", "id": "web_search"}]}}]