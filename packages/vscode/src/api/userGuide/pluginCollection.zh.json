[{"id": 1, "title": "🛠 使用插件解决专业问题", "displayQuery": " /base64解码 试试这段解码", "primaryChoice": {"promptQuery": "5oKo5aW977yM5oiR5piv5oKo55qE5pm66IO957yW56CB5Yqp5omLQmFpZHUgQ29tYXRl", "agent": "dev-tools", "slash": "base64-decode", "knowledgeList": []}}, {"id": 2, "title": "🛠 使用插件解决专业问题", "displayQuery": " @飞桨 飞桨中如何进行梯度裁剪？", "primaryChoice": {"promptQuery": "飞桨中如何进行梯度裁剪？", "agent": "paddle", "slash": "chat", "knowledgeList": []}}, {"id": 3, "title": "🛠 使用插件解决专业问题", "displayQuery": " @百度小程序 百度小程序如何实现一个搜索框组？", "primaryChoice": {"promptQuery": "百度小程序如何实现一个搜索框组？", "agent": "smartapp", "slash": "chat", "knowledgeList": []}}]