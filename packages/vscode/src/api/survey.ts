/**
 * @file 调研结果记录
 */

import axios from 'axios';
import {UserService} from '@/services/UserService';
import {iocContainer} from '../iocContainer';

interface SurveyContent {
    score: number;
    suggestion: string;
    platform: string;
    consumer: string;
    version: number;
}
export async function postSurveyContent(params: SurveyContent) {
    try {
        const userService = iocContainer.get(UserService);
        const [username] = await userService.getCurrentUser();

        axios.post(
            'http://ai-summary.baidu.com/api/surveys',
            {data: {username, ...params}},
            {
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }
    catch (e) {
        // eslint-disable-next-line
    }
}
