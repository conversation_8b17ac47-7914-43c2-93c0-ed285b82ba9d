import axios, {AxiosInstance, CreateAxiosDefaults} from 'axios';
import * as vscode from 'vscode';
import {cloneDeep} from 'lodash';
import {CancellationToken} from 'vscode-languageserver-protocol';
import {LogLevel, log, error as errorLog} from '@/common/outputChannel';
import {safeStringify} from '@/utils/common';
import {isInternal, isPoc} from '@/utils/features';
import {COMATE_CONFIG_PREFIX} from '@/constants';
import {L10n} from '@/common/L10nProvider/L10n';

export const withLogger = (axiosInstance: AxiosInstance, logLevel: LogLevel = LogLevel.Debug) => {
    axiosInstance.interceptors.request.use(config => {
        if (config.custom?.disableLogging) {
            return config;
        }
        const debugInfo = [];
        if (config.method) {
            debugInfo.push(config.method.toUpperCase());
        }
        if (config.url) {
            debugInfo.push(config.url);
        }
        if (typeof config.data === 'string') {
            debugInfo.push(config.data);
        }
        else if (config.data instanceof URLSearchParams) {
            const requestData = safeStringify(Object.fromEntries(config.data));
            if (requestData) {
                debugInfo.push(requestData);
            }
        }
        log(logLevel, ...debugInfo);
        return config;
    }, error => {
        errorLog(error?.message);
        return Promise.reject(error);
    });

    axiosInstance.interceptors.response.use(response => {
        if (response.config.custom?.disableLogging) {
            return response;
        }
        const debugInfo = [`API RES: from (${response.config.url})`];
        const responseData = safeStringify(response.data);
        if (response.headers['content-type'] === 'application/json' && responseData) {
            debugInfo.push(responseData);
        }
        log(logLevel, ...debugInfo);
        return response;
    }, error => {
        errorLog(error?.message);
        return Promise.reject(error);
    });

    return axiosInstance;
};

// const getEndpointFromConfig = () => {
//     const config = vscode.workspace.getConfiguration(COMATE_CONFIG_PREFIX);
//     const value = config.get<string>('serviceEndpoint');
//     return value ?? '';
// };

export const withProxy = (axiosInstance: AxiosInstance) => {
    // if (!isPoc) {
    return axiosInstance;
    // }

    // axiosInstance.interceptors.request.use(config => {
    //     const endpoint = getEndpointFromConfig();
    //     if (endpoint) {
    //         config.baseURL = endpoint;
    //     }
    //     return config;
    // }, error => {
    //     return Promise.reject(error);
    // });

    // return axiosInstance;
};

interface UserInfo {
    user: string;
    license: string;
    customizeService?: string;
}

interface HeadersConfig extends Partial<UserInfo> {
    clientType?: string;
    clientVersion?: string;
}

const headersConfig: HeadersConfig = {};

export function updateApiHeadersConfig(config: HeadersConfig | undefined) {
    Object.assign(headersConfig, config);
}

type UserInfoGetter = () => Promise<UserInfo> | UserInfo;

let getUserInfo: UserInfoGetter | null = null;

export function setApiUserInfoGetter(getter: UserInfoGetter) {
    getUserInfo = getter;
}

export async function getHeadersConfig() {
    const userInfo = getUserInfo
        ? await getUserInfo()
        : undefined;
    updateApiHeadersConfig(userInfo);
    return cloneDeep(headersConfig);
}

function getAcceptLanguage() {
    const currentLanguage = L10n.currentLanguage;
    switch (currentLanguage) {
        case 'en':
            return 'en-US,en';
        default:
            return 'zh-CN,zh';
    }
}

export function withAcceptLanguage(axiosInstance: AxiosInstance) {
    axiosInstance.interceptors.request.use(async config => {
        config.headers['Accept-Language'] = getAcceptLanguage();
        return config;
    }, error => {
        return Promise.reject(error);
    });
    return axiosInstance;
}

const WHITE_LIST_PREFIX = [
    '/key/type/',
];

function withPrivateService(axiosInstance: AxiosInstance) {
    axiosInstance.interceptors.request.use(async config => {
        // eslint-disable-next-line no-console
        const comateConfig = vscode.workspace.getConfiguration(COMATE_CONFIG_PREFIX);
        const privateService = comateConfig.get<string>('privateService');
        if (!isInternal && config.baseURL && privateService) {
            if (!isPoc && WHITE_LIST_PREFIX.find(v => config.url?.startsWith(v))) {
                return config;
            }
            // 如果config.baseURL包含https://comate.baidu.com，把 https://comate.baidu.com 替换成headersConfig.privateService
            config.baseURL = config.baseURL.replace('https://comate.baidu.com', privateService);
            // eslint-disable-next-line no-console
            console.log('service url', `${config.baseURL}${config.url}`);
            // 私有域请求超时时间放大至2min
            config.timeout = 2 * 60 * 1000;
        }
        return config;
    }, error => {
        return Promise.reject(error);
    });
    return axiosInstance;
}

export function createAxios(options: CreateAxiosDefaults, logLevel?: LogLevel) {
    return withPrivateService(withAcceptLanguage(withLogger(
        axios.create({proxy: false, ...options}),
        logLevel
    )));
}

export function tradeCancelToken(cancelToken?: CancellationToken) {
    if (!cancelToken) {
        return undefined;
    }
    const axiosCancelToken = axios.CancelToken.source();
    cancelToken.onCancellationRequested(() => {
        axiosCancelToken.cancel();
    });
    return axiosCancelToken;
}
