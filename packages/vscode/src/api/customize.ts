/**
 * @file 混合云相关接口
 */

import {info} from '@/common/outputChannel';
import {createAxios, withProxy} from './common';
import {ResponseBase} from './index';

export interface Config {
    key: string;
    typeId: number; // 前端根据id渲染账号类型
    type: string; // 账号类型
    customized: boolean; // 是否混合云部署账号，true：是，false：否
    customizedUrl: string; // 自定义服务地址。混合云部署会有自定义服务地址，非混合云部署会返回默认地址
}

const BASE_URL_MAPPING: Record<string, string> = {
    internal: 'https://comate.baidu-int.com/api',
    'internal-test': 'http://10.11.58.93:8080',
    saas: 'https://comate.baidu.com/api',
    'saas-test': 'https://comate-cop.now.baidu-int.com/api',
    poc: 'https://comate.baidu.com/api',
};

info(`Customize api baseURL: ${BASE_URL_MAPPING[$features.PLATFORM]}`);

const axiosInstance = withProxy(createAxios({
    baseURL: BASE_URL_MAPPING[$features.PLATFORM + ($features.ENVIRONMENT === 'test' ? '-test' : '')],
}));

export async function userCustomizeConfig(licenseOrUserName: string) {
    const res = await axiosInstance.get<ResponseBase<Config>>(
        '/key/type/' + encodeURIComponent(licenseOrUserName)
    );
    return res;
}
