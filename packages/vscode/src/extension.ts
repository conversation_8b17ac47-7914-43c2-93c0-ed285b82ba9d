import * as vscode from 'vscode';
import platform from '@shared/platform';
import {INTERNAL_PLATFORM_DEFINITIONS} from '@shared/platform/internal';
import {PLATFORM, ENVIRONMENT, setPlatformAndEnvironment} from '@comate/plugin-shared-internals';
import {autoUpdate} from './utils/autoUpdater';
import {registerShowPromptionCommand, showPromotionPopup} from './promotion';
import {activateCommon} from './main';
import {iocContainer} from './iocContainer';
import {ITimeTracker} from './services/TimeTracker/types';
import {TYPES} from './inversify.config';
import {BasicTimeTracker} from './services/TimeTracker';
import {ILicenseController} from './services/LicenseController/types';
import {EmptyLicenseController} from './services/LicenseController/Empty';
import uriProtocolHandler from './services/UriProtocolHandler';
import {ICompletionSuccessRateTracker} from './services/CompletionProvider/SuccessRateTracker/types';
import {BasicCompletionSuccessRateTracker} from './services/CompletionProvider/SuccessRateTracker/Basic';
import {EmbeddingsController} from './services/EmbeddingsService/controller';
import {KernelProvider} from './services/KernelProvider';
import {ComatePlusChatSession} from './services/ComatePlusChatSession';
// import {ComatePairModePluginProvider} from './services/ComatePairModePluginProvider';
import {ILogger} from './services/Logger/types';
import {BasicLogger} from './services/Logger';
import {getInternalUserInfoMemorized} from './api/user';
import {UserService} from './services/UserService';
import {IL10nProvider} from './common/L10nProvider/types';
import {EmptyL10nProvider} from './common/L10nProvider/EmptyL10nProvider';
import {TerminalLinkProvider} from './services/TerminalLink/TerminalLinkProvider';
import {DocumentCopyPasteProvider} from './services/CopyPasteProvider';
import {IssueGenerateProvider} from './services/IssueGenerateProvider';
import {DiagnosticProvider} from './services/DiagnosticProvider';
import {DynamicWordDiffProvider} from './services/DynamicWordDiffProvider';
import {AutoPasteProvider} from './services/AutoPasteProvider';
import {CustomLspProvider} from './services/CustomLspProvider/customLspProvider';
import {QuickFixProvider} from './services/QuickFixProvider';
import {RepositoryStateProvider} from './services/RepositoryStateProvider';

setPlatformAndEnvironment(PLATFORM.INTERNAL, $features.ENVIRONMENT as ENVIRONMENT);

async function checkInternalUsernameValidity(username: string) {
    const internalUserInfo = await getInternalUserInfoMemorized(username);
    if (internalUserInfo === null) {
        const choice = await vscode.window.showInformationMessage(
            `检测到您配置中的用户 ${username} 不是您的邮箱前缀，请及时修改。`,
            '去修改'
        );
        if (choice === '去修改') {
            vscode.commands.executeCommand('baidu.comate.openSettings');
        }
    }
}

export async function activate(context: vscode.ExtensionContext) {
    platform.use(INTERNAL_PLATFORM_DEFINITIONS);
    activateCommon(context, {
        createL10nProvider: () => {
            iocContainer.bind<IL10nProvider>(TYPES.IL10nProvider).to(EmptyL10nProvider).inSingletonScope();
            return iocContainer.get<IL10nProvider>(TYPES.IL10nProvider);
        },
        createTimeTracker: () => {
            iocContainer
                .bind<ITimeTracker>(TYPES.ITimeTracker)
                .to(BasicTimeTracker)
                .inSingletonScope();
            return iocContainer.get<ITimeTracker>(TYPES.ITimeTracker);
        },
        createLogger: () => {
            iocContainer
                .bind<ILogger>(TYPES.ILogger)
                .to(BasicLogger)
                .inSingletonScope();
            return iocContainer.get<ILogger>(TYPES.ILogger);
        },
        createLicenseController: () => {
            iocContainer
                .bind<ILicenseController>(TYPES.ILicenseController)
                .to(EmptyLicenseController)
                .inSingletonScope();
            return iocContainer.get<ILicenseController>(TYPES.ILicenseController);
        },
        createCompletionSuccessRateTracker() {
            iocContainer
                .bind<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker)
                .to(BasicCompletionSuccessRateTracker)
                .inSingletonScope();
            return iocContainer.get<ICompletionSuccessRateTracker>(TYPES.ICompletionSuccessRateTracker);
        },
        createExclusiveFeatures() {
            iocContainer.bind(EmbeddingsController).toSelf().inSingletonScope();
            iocContainer.bind(ComatePlusChatSession).toSelf().inSingletonScope();
            iocContainer.bind(KernelProvider).toSelf().inSingletonScope();
            // iocContainer.bind(ComatePairModePluginProvider).toSelf().inSingletonScope();
            iocContainer.bind(TerminalLinkProvider).toSelf().inSingletonScope();
            iocContainer.bind(DocumentCopyPasteProvider).toSelf().inSingletonScope();
            iocContainer.bind(IssueGenerateProvider).toSelf().inSingletonScope();
            iocContainer.bind(DiagnosticProvider).toSelf().inSingletonScope();
            iocContainer.bind(DynamicWordDiffProvider).toSelf().inSingletonScope();
            iocContainer.bind(AutoPasteProvider).toSelf().inSingletonScope();
            iocContainer.bind(CustomLspProvider).toSelf().inSingletonScope();
            iocContainer.bind(QuickFixProvider).toSelf().inSingletonScope();
            iocContainer.bind(RepositoryStateProvider).toSelf().inSingletonScope();

            iocContainer.get(KernelProvider).start();
            iocContainer.get(CustomLspProvider).start();
            iocContainer.get(ComatePlusChatSession).start();
            iocContainer.get(EmbeddingsController).start();
            // iocContainer.get(ComatePairModePluginProvider).start();
            iocContainer.get(TerminalLinkProvider).start();
            iocContainer.get(AutoPasteProvider).start(context);

            return [
                iocContainer.get(ComatePlusChatSession),
                iocContainer.get(EmbeddingsController),
                iocContainer.get(KernelProvider),
                iocContainer.get(CustomLspProvider),
                // iocContainer.get(ComatePairModePluginProvider),
                iocContainer.get(TerminalLinkProvider),
                iocContainer.get(DocumentCopyPasteProvider),
                iocContainer.get(IssueGenerateProvider),
                iocContainer.get(DiagnosticProvider),
                iocContainer.get(DynamicWordDiffProvider),
                iocContainer.get(QuickFixProvider),
                iocContainer.get(RepositoryStateProvider),
                uriProtocolHandler,
                registerShowPromptionCommand(),
            ];
        },
    });
    autoUpdate();
    showPromotionPopup();
    const [username] = await iocContainer.get(UserService).getCurrentUser().catch(() => ['unknown']);
    checkInternalUsernameValidity(username);
}

export function deactivate() {}
