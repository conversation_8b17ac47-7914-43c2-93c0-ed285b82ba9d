export const BAIDU_CONFIG_NAMESPACE = 'baidu';
export const COMATE_CONFIG_SECTION = 'comate';
export const COMATE_CONFIG_PREFIX = `${BAIDU_CONFIG_NAMESPACE}.${COMATE_CONFIG_SECTION}`;
export const COMATE_COMPLETION_SCHEMA = 'comate-completion';
export const COMATE_TEST_SCHEMA = 'comate-test-scheme';
export const COMATE_DOCSTRING_SCHEME = 'comate-docstring-scheme';
export const COMATE_NL2CODE_SCHEME = 'comate-nl2code-scheme';

export const SWAN_APP_NAME = '百度开发者工具';

export const DEBOUNCE_LIMIT = 75;

// 行间注释
export const CMD_ADD_COMMENT = 'baidu.comate.addInlineComment';
// 函数注释
export const CMD_GENERATE_DOCSTRING = 'baidu.comate.generateDocstring';
// 代码解释
export const CMD_EXPLAIN_FUNCTION = 'baidu.comate.explainFunction';
export const CMD_EXPLAIN_SELECTED_CODE = 'baidu.comate.explainSelectedCode';
// 调优建议
export const CMD_OPTIMIZE_FUNCTION = 'baidu.comate.optimizeFunction';
// 函数拆分
export const CMD_SPLIT_FUNCTION = 'baidu.comate.splitFunction';
// 行间日志
export const CMD_ADD_INLINE_LOG = 'baidu.comate.addInlineLog';
// 编辑区操作点击 icon 后展开详细面板
export const CMD_SHOW_CODELENS_DETAIL_QUICKPICK = 'baidu.comate.showCodelensDetailQuickPick';

export const CMD_SHOW_INLINECHAT_QUICKPICK = 'baidu.comate.inlineChat.trigger';

// 拆分 React 组件
export const CMD_SPLIT_REACT_COMPONENT = 'baidu.comate.splitReactComponent';

// 查看 React 组件拆分结果
export const CMD_SHOW_REACT_COMPONENT_SPLIT_RESULT = 'baidu.comate.showReactComponentSplitResult';

// 查看 React 组件拆分失败原因
export const CMD_SHOW_REACT_COMPONENT_SPLIT_FAILURE_REASON = 'baidu.comate.showReactComponentSplitFailureReason';

export const BANNER_VERSION_CONFIG_KEY = 'baidu.comate.bannerVersion';

export const SURVEY_VERSION_CONFIG_KEY = 'baidu.comate.surveyVersion';

export const CMD_HANDLE_INPUT_FOCUS = 'baidu.comate.handleInputFocus';

export const CONTEXT_SHOW_FEEDBACK = 'baidu.comate.context.showFeedback';

export const CONTEXT_SHOW_PLUGIN_CONFIG = 'baidu.comate.context.showPluginConfigPanel';

export const AGREEMENT_VERSION_CONFIG_KEY = 'baidu.comate.agreementVersion';

export const INPUTBOX_HISTORY_KEY = 'baidu.comate.inputboxHistory';

export const INLINECHAT_PROMPT_HISTORY_KEY = 'baidu.comate.inlineChatHistory';

export const COMMAND_LOGOUT = 'baidu.comate.logout';

export const CONTEXT_SHOW_GIT_MESSAGE = 'baidu.comate.context.showGitMessage';

export const CONTEXT_WORD_DIFF_ACTIVE = 'baidu.comate.context.wordDiffActive';

/** TODO: 建议 command 都挪到一个对象里来 */
export const RegisteredCommand = {
    /** 打开侧边栏 */
    showChatPanel: 'baidu.comate.showChatPanel',
    /** 切换侧边栏的打开状态 */
    toggleChatPanel: 'baidu.comate.toggleChatPanel',
    /** 唤起 InlineChat */
    showInlineChat: 'baidu.comate.inlineChat.trigger',
    /** 唤起 InlineChat 的同时选中相关代码 */
    showInlineChatAndSelectCode: 'baidu.comate.inlineChat.triggerAndSelectCode',
    /** 取消当前正在执行的 InlineChat */
    cancelInlineChat: 'baidu.comate.inlinechat.cancel',
    /** 编辑区操作点击 icon 后展开详细面板 */
    showCodelensDetailQuickPick: 'baidu.comate.showCodelensDetailQuickPick',
    /** DynamicWordDiff 的取消操作 */
    cancelWordDiff: 'baidu.comate.wordDiff.cancel',
    /** DynamicWordDiff 的采纳操作 */
    acceptWordDiffChange: 'baidu.comate.wordDiff.accept',
    /** DynamicWordDiff 的取消操作 */
    rejectWordDiffChange: 'baidu.comate.wordDiff.reject',
    /** 关闭 InlineChat */
    closeInlineChat: 'baidu.comate.inlineChat.close',
    /** 智能粘贴 */
    smartApply: 'baidu.comate.smartApply',
    /** 添加选中代码至对话 */
    addFileContextToChat: 'baidu.comate.addFileContextToChat',
};

export const BASE_WEB_HOST = $features.ENVIRONMENT === 'test'
    ? 'https://comate-external-test.now.baidu.com'
    : 'https://comate.baidu.com';

export {SUPPORTED_SUFFIX_CONFIG, SUFFIX_LANG_MAP} from '@comate/plugin-shared-internals';

export const DEFAULT_SUGGESTION_MODE_MAP = {
    extremeFast: {
        displayName: '极速模式',
        requestDelayMs: 25,
    },
    fast: {
        displayName: '速度优先',
        requestDelayMs: 50,
    },
    balance: {
        displayName: '智能平衡',
        requestDelayMs: 75,
    },
    accurate: {
        displayName: '精准优先',
        requestDelayMs: 100,
    },
    extremeAccurate: {
        displayName: '精准模式',
        requestDelayMs: 125,
    },
};
