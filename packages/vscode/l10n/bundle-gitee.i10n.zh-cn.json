{"statusBar.not.support.text": "暂不支持该语言", "statusBar.enable.text": "启用", "statusBar.title": "文心快码", "statusBar.nosuggestions": "文心快码: 无建议", "completion.panel.accept.text": "采纳此推荐", "completion.panel.accept.tooltip": "将此次推荐结果应用到代码中", "completion.panel.codelens.disabled.text": "Baidu Comate 补全生成面板需要启用 Code Lens 功能，请在设置中启用此功能后重试~", "completion.panel.title": "补全生成面板", "completion.panel.empty.text": "暂无结果", "completion.panel.hidden.text": "重复项隐藏", "completion.panel.synthesizing.text": "生成补全", "completion.decoration.basic": "💡 Tab 采纳, Esc 取消", "completion.decoration.widget.text": "💡 Tab 采纳 Comate 推荐, Enter 采纳 IDE 推荐", "completion.decoration.acceptLine.text": "💡 {0} + ↓ 逐行采纳", "nl2code.empty.text": "抱歉，我无法回答这个问题，您可以试着向我提问其他编程问题", "nl2code.no.selection.text": "请先选中一段代码，再试一次", "optimize.codelens.title": "调优建议", "optimize.codeActions.title": "使用 Comate 调优", "optimize.codelens.tooltip": "Comate 优化该函数", "optimize.error": "未能优化函数", "optimize.code.prompt": "请优化下面的代码：", "optimize.no.selection.text": "请框选要优化的代码", "optimize.function.prompt": "请优化下面的函数：", "chatTrial.codelens.title": "打开侧边栏", "chatTips.selectedCode.title": "{0} + I 基于选中代码进行对话", "comment.prompt": "请为下面的函数增加行间注释：", "comment.codelens.title": "行间注释", "comment.codelens.tooltip": "Comate 为该函数增加行间注释", "comment.inlinechat.title": "/行间注释", "comment.generate.error": "未能生成行间注释", "docstring.prompt": "为下面的函数生成注释：", "docstring.codelens.title": "函数注释", "docstring.codelens.tooltip": "Comate 生成该函数注释", "docstring.inlinechat.title": "/函数注释", "docstring.generate.error": "未能生成注释", "split.prompt": "请拆分下面的函数：", "split.codelens.title": "函数拆分", "split.codelens.tooltip": "Comate 拆分该函数", "split.generate.error": "未能拆分函数", "explain.codelens.title": "代码解释", "explain.codeActions.title": "使用 Comate 解释", "explain.codelens.tooltip": "Comate 生成该函数的解释", "explain.generate.error": "未能生成代码解释", "explain.no.selection.text": "请框选要解释的代码", "explain.prompt": "解释下面的{0}：", "unitTest.codelens.title": "生成单测", "unitTest.codelens.tooltip": "Comate 生成该函数的单测", "unitTest.generate.error": "未能生成单测", "unitTest.accept.error": "Comate 采纳单元测试用例失败：", "unitTest.prompt": "请为下面的{0}生成单测{1}：", "inlinechat.placeholder.generate": "提交", "inlinechat.placeholder.selected": "输入自然语言修改代码或选择推荐项(Enter键提交，Esc键取消)", "inlinechat.placeholder.unselected": "输入自然语言生成代码或选择推荐项(Enter键提交，Esc键取消)", "inlinechat.title": "{0} Comate 正在生成代码 {1}", "inlinechat.accept": "采纳 {0}", "inlinechat.reject": "放弃 {0}", "inlinechat.placeholder.error": "请先采纳或放弃上一次代码", "inlinechat.codelens.title": "开始对话", "inlinechat.codeActions.title": "使用 Comate 编辑", "diff.title": "查看变更", "diff.accept": "采纳 {0}", "diff.reject": "放弃 {0}", "diff.dualScreenDiff.open": "查看变更 {0}", "chat.webview.title": "Comate 对话", "chat.no.insertPosition.text": "请先选中插入位置", "accept.path.error": "请输入正确的文件路径", "quickFix.action.text": "Comate 错误修复", "quickFix.prompt": "请根据以下的提示信息修复代码{0}：", "quickFix.generate.error": "未能修复代码", "quickFix.prompt.suffix": "，请告诉我修复后的代码，不要行间解释", "regexHover.tooltip": "查看正则解释", "regexHover.prompt": "请解释这个正则表达式的含义：", "embeddings.maximum.files.message": "无法创建向量索引：workspace已超过{0}个文件上限", "openPlatform.title.text": "Comate 开放平台", "openPlatform.webview.title": "Comate 插件配置中心", "messageGenerate.generate.error": "生成失败：{0}", "messageGenerate.noDiff.error": "未检测到变更内容", "messageGenerate.getDiff.error": "获取变更内容失败：{0}", "messageGenerate.emptyContent.error": "未能生成commit message", "autoWork.auth.modal.title": "代码授权", "autoWork.indexing.message": "向量索引创建中, 请耐心等待", "autoWork.connection.interrupt.message": "连接已中断，请重试", "autoWork.indexing.reminder.text": "Comate 正在理解你的代码库，索引创建进度 {0}%；索引创建完成后点击重新生成，可获得更符合业务场景的答案。", "autoWork.error": "AutoWork 生成失败", "autoWork.task.thought": "分析您的目标", "autoWork.task.answer": "构建您的答案", "autoWork.autoDebug": "Comate分析与修复", "autoWork.autoDebug.prompt": "请帮我分析终端的报错日志，并提供解决方案。只解决第一个报错", "autoWork.autoDebug.thought": "分析您的报错信息", "autoWork.autoDebug.search": "检索报错相关信息", "autoWork.autoDebug.answer": "生成修复方案", "comatePlus.permission.workspace.text": "工作区文件读写", "comatePlus.permission.disk.text": "全盘文件读写", "comatePlus.permission.user.text": "获取用户详细信息", "comatePlus.auth.message": "是否授权插件 {0} 【{1}】的权限", "comatePlus.userInfo.error": "获取用户信息失败，请检查 Comate 配置中的用户名是否正确", "comatePlus.clear.permission": "已清除所有插件权限", "comatePlus.split.error": "已选择的函数足够精简，不符合拆分条件，请重新选择", "comatePlus.noSelection": "请在编辑区选择代码", "comatePlus.not.function.text": "已选择的代码中不包含函数声明，请重新选择", "common.generate.error": "生成失败：{0}", "common.unknown.error": "未知错误", "common.request.error": "请求失败：{0}", "common.code": "代码", "common.function": "函数", "common.authorize": "授权", "common.using": "使用", "common.without": "不使用", "common.comma": "，", "common.framework": "框架", "common.related": "关联", "common.prompt": "提示", "common.thinking": "思考过程", "common.searchResult": "搜索结果", "common.deny": "拒绝", "common.navigateToSetting": "前往配置", "common.customize.error": "混合云用户请到插件配置-服务配置中检查调试", "log.level.placeholder": "请选择日志等级", "do.not.show.again": "不再提示", "login.message": "Hi Mate，我是你的智能编码助手 {0}。快来登录，和我一起享受编程的乐趣吧！", "login.action.text": "Gitee 账号登录", "login.action.withLicense.text": "智能云账号登录", "login.device.error": "获取设备 ID 失败，无法绑定设备", "login.loggingIn.text": "正在登录 Baidu Comate ...", "login.success": "Baidu Comate 登录成功!", "login.failed": "登录失败：{0}", "login.token.error": "获取 Token 失败", "login.key.error": "'获取 key 失败", "login.install.internal": "去下载内部版本", "login.internalUser.message": "检测到您是百度内部用户，建议使用内部版本Comate（需要卸载当前插件并下载内部版本）", "login.agreement.message": "尊敬的Baidu Comate用户，为了提供更好的服务，我们将于2024年4月17日更新[用户服务协议](https://comate.baidu.com/zh/userAgreement)。请您关注并查看更新条款。感谢您的支持！", "renew.action.text": "前往续费", "command.quick.pick.placeholder": "请选择快捷指令"}