{"name": "comate", "displayName": "Comate 度厂版", "description": "Coding mate, Pair you create", "publisher": "baidu", "homepage": "https://comate.baidu.com", "version": "3.8.2", "license": "UNLICENSED", "private": true, "keywords": ["baidu", "comate", "assistant", "ernie-bot", "ernie-code", "ai", "documentation", "autocomplete", "intellisense", "refactor", "javascript", "python", "typescript", "php", "go", "golang", "ruby", "c++", "java"], "engines": {"vscode": "^1.69.0"}, "icon": "assets/icon.png", "categories": ["Programming Languages", "Machine Learning", "Education", "Snippets"], "tags": ["ai", "autocomplete", "code completion", "chat", "intellisense", "intellicode", "documentation", "generative ai", "ernie-code", "ernie-bot", "javascript", "python", "java", "typescript", "c#", "c++", "php", "shell", "c", "ruby"], "activationEvents": ["onFileSystem:comate-diff", "onFileSystem:comate-ut-report", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "baidu.comate.generate", "title": "%command.generate.title%", "category": "Comate"}, {"command": "baidu.comate.cancel", "title": "取消本次推荐 (ESC)", "category": "Comate"}, {"command": "baidu.comate.createUnitTest", "title": "生成单测", "category": "Comate"}, {"command": "baidu.comate.showChatPanel", "title": "%command.showChatPanel.title%", "category": "Comate"}, {"command": "baidu.comate.addFileContextToChat", "title": "%command.addFileContextToChat.title%", "category": "Comate"}, {"command": "baidu.comate.inlineChat.trigger", "title": "%command.inlineChatTrigger.title%", "category": "Comate"}, {"command": "baidu.comate.inlineChat.close", "title": "关闭inlineChat", "category": "Comate"}, {"command": "baidu.comate.optimizeCode", "title": "调优建议", "category": "Comate"}, {"command": "baidu.comate.showPromotion", "title": "查看新功能", "category": "Comate"}, {"command": "baidu.comate.clearSession", "title": "清空会话", "icon": "$(clear-all)", "enablement": "baidu.comate.context.messagesCount > 0 || baidu.comate.context.comatePairMessagesCount > 0 || baidu.comate.context.helpSendEvent", "category": "Comate"}, {"command": "baidu.comate.explainSelectedCode", "title": "解释代码", "category": "Comate"}, {"command": "baidu.comate.toggleDebugLog", "title": "%command.toggleDebugLog.title%", "category": "Comate"}, {"command": "baidu.comate.openPluginConfigPanel", "title": "打开插件配置面板", "category": "Comate", "icon": "$(extensions)"}, {"command": "baidu.comate.setLogLevel", "title": "%command.setLogLevel.title%", "category": "Comate", "icon": "$(gear)"}, {"command": "baidu.comate.openSettings", "title": "%command.openSettings.title%", "icon": "$(gear)", "category": "Comate"}, {"command": "baidu.comate.visitHelpDocs", "title": "%command.visitHelpDocs.title%", "icon": "$(question)", "category": "Comate"}, {"command": "baidu.comate.feedback", "title": "%command.feedback.title%", "category": "Comate", "icon": "$(feedback)"}, {"command": "baidu.comate.chatWithTerminalSelectionContext", "category": "Comate", "title": "对此分析与修复"}, {"command": "baidu.comate.recreateIndex", "title": "%command.recreateIndex.title%", "category": "Comate"}, {"command": "baidu.comate.sourceControlGenerateMessage", "title": "%command.generateMessage.title%", "icon": "assets/newIcon.png"}, {"command": "baidu.comate.createNewChat", "title": "%command.createNewChat.title%", "icon": "$(add)", "category": "Comate"}, {"command": "baidu.comate.historyChat", "title": "历史对话", "icon": "$(history)", "category": "Comate"}, {"command": "baidu.comate.moreOperations", "title": "更多", "icon": "$(ellipsis)", "category": "Comate"}, {"command": "baidu.comate.downloadLogs", "title": "%command.downloadLogs.title%", "icon": "$(cloud-download)", "category": "Comate"}], "menus": {"commandPalette": [{"command": "baidu.comate.generate", "when": "baidu.comate.activated"}, {"command": "baidu.comate.cancel", "when": "false"}, {"command": "baidu.comate.clearSession", "when": "false"}, {"command": "baidu.comate.explainSelectedCode", "when": "false"}, {"command": "baidu.comate.optimizeCode", "when": "false"}, {"command": "baidu.comate.createUnitTest", "when": "false"}, {"command": "baidu.comate.openPluginConfigPanel", "when": "false"}, {"command": "baidu.comate.showPromotion", "when": "false"}, {"command": "baidu.comate.moreOperations", "when": "false"}, {"command": "baidu.comate.chatWithTerminalSelectionContext", "when": "false"}, {"command": "baidu.comate.inlineChat.close", "when": "false"}, {"command": "baidu.comate.historyChat", "when": "false"}], "editor/inlineCompletions/actions": [{"command": "baidu.comate.generate", "when": "baidu.comate.activated", "group": "comate@1"}, {"command": "baidu.comate.cancel", "when": "baidu.comate.activated", "group": "comate@2"}, {"command": "baidu.comate.showChatPanel", "when": "baidu.comate.activated", "group": "comate@3"}], "editor/context": [{"submenu": "baidu.comate.commands", "group": "0_0_comate@1"}], "baidu.comate.commands": [{"command": "baidu.comate.addFileContextToChat", "when": "baidu.comate.activated && editorHasSelection", "group": "0_comate@1"}, {"command": "baidu.comate.inlineChat.trigger", "when": "baidu.comate.activated && editorHasSelection", "group": "0_comate@1"}, {"command": "baidu.comate.createUnitTest", "when": "baidu.comate.activated && baidu.comate.enableUnitTest && editorHasSelection", "group": "1_comate@1"}, {"command": "baidu.comate.explainSelectedCode", "when": "baidu.comate.activated && editorHasSelection", "group": "1_comate@2"}, {"command": "baidu.comate.optimizeCode", "when": "baidu.comate.activated && editorHasSelection", "group": "1_comate@3"}, {"command": "baidu.comate.showChatPanel", "when": "baidu.comate.activated", "group": "1_comate@4"}], "view/title": [{"command": "baidu.comate.moreOperations", "when": "view == comate.views.chat", "group": "navigation@5"}, {"command": "baidu.comate.openSettings", "when": "view == comate.views.chat", "group": "navigation@4"}, {"command": "baidu.comate.openPluginConfigPanel", "when": "view == comate.views.chat", "group": "navigation@3"}, {"command": "baidu.comate.historyChat", "when": "view == comate.views.chat", "group": "navigation@2"}, {"command": "baidu.comate.createNewChat", "when": "view == comate.views.chat", "group": "navigation@1"}], "terminal/context": [{"command": "baidu.comate.chatWithTerminalSelectionContext", "group": "navigation@1"}], "scm/title": [{"command": "baidu.comate.sourceControlGenerateMessage", "group": "navigation"}]}, "submenus": [{"id": "baidu.comate.commands", "label": "Comate"}], "keybindings": [{"command": "baidu.comate.toggleChatPanel", "key": "ctrl+y", "win": "win+y", "mac": "cmd+y"}, {"command": "baidu.comate.cancel", "key": "escape", "mac": "escape", "when": "inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.trigger", "key": "alt+\\", "when": "editorTextFocus && !editorHasSelection && baidu.comate.activated && !inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.commit", "key": "Tab", "when": "inlineSuggestionHasIndentationLessThanTabSize && inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && suggestWidgetVisible"}, {"command": "baidu.comate.acceptNextLine", "key": "ctrl+down", "win": "ctrl+down", "mac": "cmd+down", "when": "editorTextFocus && !editorReadonly && baidu.comate.activated && inlineSuggestionVisible"}, {"command": "baidu.comate.triggerCodeBasedOnLineComment", "key": "alt+p", "mac": "alt+p", "win": "alt+p", "when": "editorTextFocus && !editorHasSelection && baidu.comate.activated"}, {"command": "baidu.comate.inlineChat.trigger", "key": "ctrl+i", "mac": "cmd+i", "when": "editorTextFocus && !editorReadonly && baidu.comate.activated && !baidu.comate.inlinechat.quickPickVisible"}, {"command": "baidu.comate.inlineChat.close", "key": "ctrl+i", "mac": "cmd+i", "when": "!editorReadonly && baidu.comate.activated && baidu.comate.inlinechat.quickPickVisible"}, {"command": "baidu.comate.inlinechat.cancel", "key": "escape", "when": "baidu.comate.activated && editorTextFocus && baidu.comate.editor.isLoading"}, {"command": "baidu.comate.diffAllAccept", "key": "alt+a", "when": "baidu.comate.activated && editorTextFocus && !editorReadonly"}, {"command": "baidu.comate.diffAllAccept", "key": "ctrl+s", "win": "ctrl+s", "mac": "cmd+s", "when": "baidu.comate.activated && editorTextFocus && !editorReadonly && baidu.comate.inlinechat.codelensVisible"}, {"command": "baidu.comate.inlinechat.cancel", "key": "ctrl+z", "win": "ctrl+z", "mac": "cmd+z", "when": "baidu.comate.activated && editorTextFocus && baidu.comate.editor.isLoading"}, {"command": "baidu.comate.diffAllReject", "key": "ctrl+z", "win": "ctrl+z", "mac": "cmd+z", "when": "baidu.comate.activated && editorTextFocus && !editorReadonly && baidu.comate.inlinechat.codelensVisible"}, {"command": "baidu.comate.diffDualScreenOpen", "key": "alt+d", "when": "baidu.comate.activated && editorTextFocus && !editorReadonly"}, {"command": "baidu.comate.diffAllReject", "key": "alt+x", "when": "baidu.comate.activated && editorTextFocus && !editorReadonly"}, {"command": "baidu.comate.diffAccept", "key": "ctrl+s", "win": "ctrl+s", "mac": "cmd+s", "when": "baidu.comate.activated && editorTextFocus && isInDiffEditor && baidu.comate.inlinechat.isDiffCodelensVisible"}, {"command": "baidu.comate.diffAccept", "key": "alt+a", "when": "baidu.comate.activated && editorTextFocus && isInDiffEditor"}, {"command": "baidu.comate.diffReject", "key": "alt+x", "when": "baidu.comate.activated && editorTextFocus && isInDiffEditor"}, {"command": "baidu.comate.diffReject", "key": "ctrl+z", "win": "ctrl+z", "mac": "cmd+z", "when": "baidu.comate.activated && editorTextFocus && isInDiffEditor && baidu.comate.inlinechat.isDiffCodelensVisible"}, {"command": "baidu.comate.wordDiff.accept", "key": "ctrl+s", "win": "ctrl+s", "mac": "cmd+s", "when": "baidu.comate.activated && editorTextFocus && baidu.comate.context.wordDiffActive"}, {"command": "baidu.comate.wordDiff.accept", "key": "Tab", "when": "baidu.comate.activated && editorTextFocus && baidu.comate.context.wordDiffActive && !inlineSuggestionVisible"}, {"command": "baidu.comate.wordDiff.cancel", "key": "escape", "when": "baidu.comate.activated && editorTextFocus && baidu.comate.context.wordDiffActive && !inlineSuggestionVisible"}, {"command": "baidu.comate.wordDiff.cancel", "key": "ctrl+z", "win": "ctrl+z", "mac": "cmd+z", "when": "baidu.comate.activated && editorTextFocus && baidu.comate.context.wordDiffActive"}, {"command": "baidu.comate.tabStreak.tab", "key": "Tab", "when": "editorTextFocus && baidu.comate.context.tabStreak"}, {"command": "baidu.comate.tabStreak.escape", "key": "Escape", "when": "editorTextFocus && baidu.comate.context.tabStreak"}], "viewsContainers": {"activitybar": [{"id": "comate", "title": "文心快码 Baidu Comate", "icon": "assets/Comate.svg"}]}, "views": {"comate": [{"id": "comate.views.chat", "type": "webview", "name": ""}]}, "configuration": [{"title": "基本配置", "properties": {"baidu.comate.username": {"type": "string", "default": "", "description": "邮箱前缀"}}}, {"title": "推荐配置", "properties": {"baidu.comate.langSuggestion": {"type": "object", "default": {"all": true, "cpp": true, "css": true, "go": true, "html": true, "java": true, "javascript": true, "less": true, "perl": true, "php": true, "python": true, "ruby": true, "shell": true, "swift": true, "typescript": true, "others": true, "vue": true, "san": true, "sass": true, "scss": true, "vhdl": true, "lua": true, "mermaid": true, "pug": true, "swan": true, "stylus": true, "rust": true, "kotlin": true, "graphql": true, "objectivec": true, "ada": true, "agda": true, "alloy": true, "antlr": true, "applescript": true, "assembly": true, "augeas": true, "awk": true, "batchfile": true, "bluespec": true, "csharp": true, "clojure": true, "cmake": true, "coffeescript": true, "commonlisp": true, "cuda": true, "dart": true, "dockerfile": true, "elixir": true, "elm": true, "emacslisp": true, "erlang": true, "fsharp": true, "fortran": true, "glsl": true, "groovy": true, "haskell": true, "idris": true, "isabelle": true, "javaserverpages": true, "json": true, "julia": true, "lean": true, "literateagda": true, "literatecoffeescript": true, "literatehaskell": true, "makefile": true, "maple": true, "markdown": true, "mathematica": true, "matlab": true, "ocaml": true, "pascal": true, "powershell": true, "prolog": true, "protocolbuffer": true, "r": true, "racket": true, "restructuredtext": true, "rmarkdown": true, "sas": true, "scala": true, "scheme": true, "smalltalk": true, "solidity": true, "sparql": true, "sql": true, "stan": true, "standardml": true, "stata": true, "systemverilog": true, "tcl": true, "tcsh": true, "tex": true, "thrift": true, "verilog": true, "visualbasic": true, "xslt": true, "yacc": true, "yaml": true, "zig": true, "jupyter": true, "xml": true}, "description": "是否开启指定语言推荐"}, "baidu.comate.inlineSuggestionMode": {"type": "string", "enum": ["extremeFast", "fast", "balance", "accurate", "extremeAccurate"], "enumItemLabels": ["极速模式", "速度优先", "智能平衡", "精准优先", "精准模式"], "enumDescriptions": ["停顿即快速推荐", "推荐速度较快", "智能平衡推荐速度与准确度", "准确度佳，略影响推荐速度", "准确度最佳，适当影响推荐速度"], "description": "自定义调节推荐响应速度与推荐准确度", "default": "extremeAccurate"}, "baidu.comate.linePreferMode": {"type": "string", "enum": ["auto", "singleLine", "multiLine"], "enumItemLabels": ["自动", "单行优先", "多行优先"], "description": "自定义调节推荐内容为多行优先或单行优先", "default": "multiLine"}, "baidu.comate.enableCommentEnhancement": {"type": "boolean", "default": false, "description": "是否开启快捷键触发注释生成代码功能"}}}, {"title": "快速修复", "properties": {"baidu.comate.enableQuickFix": {"type": "boolean", "default": true, "description": "是否开启 Comate 快速修复功能"}}}, {"title": "行间按钮", "properties": {"baidu.comate.enableCodelens": {"type": "object", "default": {"enableInlineUnitTest": true, "enableInlineExplain": true, "enableInlineDocstring": true, "enableInlineSplit": true, "enableInlineComment": true, "enableInlineOptimize": true, "enableInlineLog": true, "enableChatPanelShortcut": true}, "properties": {"enableInlineDocstring": {"type": "boolean", "default": true, "description": "是否开启生成函数注释的行间展示"}, "enableInlineComment": {"type": "boolean", "default": true, "description": "是否开启生成行内注释的行间展示"}, "enableInlineUnitTest": {"type": "boolean", "default": true, "description": "是否开启生成单测用例的行间展示"}, "enableInlineSplit": {"type": "boolean", "default": true, "description": "是否开启长函数拆分的行间展示"}, "enableInlineOptimize": {"type": "boolean", "default": true, "description": "是否开启代码调优的行间展示"}, "enableInlineExplain": {"type": "boolean", "default": true, "description": "是否开启代码解释的行间展示"}, "enableInlineLog": {"type": "boolean", "default": true, "description": "是否开启添加日志的行间展示"}, "enableChatPanelShortcut": {"type": "boolean", "default": true, "description": "是否开启修改代码的行间展示"}}, "additionalProperties": false, "description": "配置函数注释、行内注释、生成单测、长函数拆分、代码解释、代码调优的行间展示"}, "baidu.comate.codelensDisplayMode": {"type": "string", "enum": ["expand", "collapse"], "enumItemLabels": ["文字平铺", "最小化 ICON 展示"], "description": "编辑区操作（行间按钮）展示方式", "default": "expand"}, "baidu.comate.docstringOutputPosition": {"type": "string", "enum": ["sidebar", "editor"], "enumItemLabels": ["侧边栏", "编辑区"], "description": "函数注释、行间注释等输出位置", "default": "sidebar"}}}, {"title": "安全增强", "properties": {"baidu.comate.enableSecurityEnhancement": {"type": "boolean", "default": true, "description": "是否开启 Comate 安全增强模式"}, "baidu.comate.enableSecurityScanBox": {"type": "boolean", "default": true, "description": "是否开启 Comate 安全扫描"}}}, {"title": "单元测试", "properties": {"baidu.comate.unitTestFrameworkForJava": {"type": "string", "enum": ["junit4", "junit5", "auto"], "enumDescriptions": ["JUnit4", "JUnit5", "自动"], "description": "配置 Java 语言单测生成的测试框架版本", "default": "auto"}, "baidu.comate.unitTestMockForJava": {"type": "string", "enum": ["<PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "auto", "off"], "enumDescriptions": ["<PERSON><PERSON><PERSON>", "JMockit", "自动", "关闭"], "description": "配置 Java 语言单测生成的 Mock 框架", "default": "auto"}, "baidu.comate.unitTestFrameworkForGo": {"type": "string", "enum": ["gotests", "auto"], "enumDescriptions": ["gotests范式", "自动"], "description": "配置 Go 语言单测生成的测试框架版本", "default": "auto"}, "baidu.comate.unitTestMockForGo": {"type": "string", "enum": ["gomock", "monkey", "sqlmock", "httptest", "auto", "off"], "enumDescriptions": ["GoMock", "Monkey", "Sqlmock", "httptest", "自动", "关闭"], "description": "配置 Go 语言单测生成的 Mock 框架", "default": "auto"}, "baidu.comate.unitTestFrameworkForC/C++": {"type": "string", "enum": ["gtest", "auto"], "enumDescriptions": ["GoogleTest", "自动"], "description": "配置 C/C++ 语言单测生成的测试框架版本", "default": "auto"}, "baidu.comate.unitTestMockForC/C++": {"type": "string", "enum": ["gmock", "auto", "off"], "enumDescriptions": ["Gmock", "自动", "关闭"], "description": "配置 C/C++ 语言单测生成的 Mock 框架", "default": "auto"}, "baidu.comate.unitTestFrameworkForJs/Ts": {"type": "string", "enum": ["jest", "mocha", "auto"], "enumDescriptions": ["Jest", "<PERSON><PERSON>", "自动"], "description": "配置 JS/TS 语言单测生成的测试框架版本", "default": "auto"}, "baidu.comate.unitTestMockForJs/Ts": {"type": "string", "enum": ["auto", "off"], "enumDescriptions": ["自动", "关闭"], "description": "配置 JS/TS 语言单测生成的 Mock 框架", "default": "auto"}, "baidu.comate.unitTestFrameworkForPython": {"type": "string", "enum": ["pytest", "unittest", "auto"], "enumDescriptions": ["pytest", "unittest", "自动"], "description": "配置 Python 语言单测生成的测试框架版本", "default": "auto"}, "baidu.comate.unitTestMockForPython": {"type": "string", "enum": ["auto", "off"], "enumDescriptions": ["自动", "关闭"], "description": "配置 Python 语言单测生成的 Mock 框架", "default": "auto"}}}, {"title": "智能粘贴", "properties": {"baidu.comate.enableAutoPaste": {"type": "boolean", "default": false, "description": "是否开启 Comate 智能粘贴功能"}}}, {"title": "实验室", "properties": {"baidu.comate.beta": {"type": "object", "default": {"enableFullStackIntelligence": false, "enableSecurityIntelligence": false, "enableCompletionIntelligence": false}, "properties": {"enableFullStackIntelligence": {"type": "boolean", "default": false, "description": "开启全栈编程智能体"}, "enableSecurityIntelligence": {"type": "boolean", "default": false, "description": "开启安全智能体"}, "enableCompletionIntelligence": {"type": "boolean", "default": false, "description": "开启编码智能体"}, "enableDebugIntelligence": {"type": "boolean", "default": false, "description": "开启Debug智能体"}}, "additionalProperties": false, "description": "配置是否启用智能体能力"}}}], "icons": {"comate-logo": {"description": "Baidu Comate icon", "default": {"fontPath": "assets/comate.woff", "fontCharacter": "\\0041"}}, "comate-logo-disabled": {"description": "Baidu Comate icon", "default": {"fontPath": "assets/comate.woff", "fontCharacter": "\\0042"}}, "comate-logo-mini": {"description": "Baidu Comate icon", "default": {"fontPath": "assets/comateMini.woff", "fontCharacter": "\\0041"}}}, "iconFonts": [{"id": "comate-font", "src": [{"path": "assets/comate.woff", "format": "woff"}]}], "colors": [{"id": "baidu.comate.colors.inlineDiff.insertedTextBackground", "description": "Color for inline diff inserted text background", "defaults": {"dark": "#4e5a34", "light": "#e9f2d2"}}, {"id": "baidu.comate.colors.inlineDiff.removedTextBackground", "description": "Color for inline diff removed text background", "defaults": {"dark": "#692621", "light": "#fbd6d3"}}, {"id": "baidu.comate.colors.inlineDiff.insertedTextForeground", "description": "Color for inline diff inserted text foreground", "defaults": {"dark": "#ffffff56", "light": "#00000077", "highContrast": "#ffffff", "highContrastLight": "#292929"}}]}, "scripts": {"clean": "rimraf dist/", "test": "vitest", "compile": "npm run clean && npm run prepare-platform && npm-run-all compile:webview compile:extension", "compile:extension": "NODE_ENV=production webpack --mode production", "compile:webview": "cd ../webview && npm run compile", "watch": "npm run clean && npm run prepare-platform && npm-run-all -p watch:*", "watch:extension": "NODE_ENV=development webpack --mode none --watch", "watch:webview": "cd ../webview && npm run watch", "watch:package-json": "node script/watch-package-json.cjs", "bundle": "node script/bundle.js", "bundle:webview": "cd ../webview && npm run bundle:webview", "prepare-platform": "node script/prepare-platform.cjs", "pre-commit": "node script/pre-commit.cjs", "doc": "node script/generate-webview-doc.js && typedoc ./shared/protocolsDoc.ts"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/generator": "^7.24.5", "@babel/parser": "^7.24.5", "@babel/traverse": "^7.24.5", "@comate/kernel-shared": "workspace:^", "@comate/plugin-shared-internals": "workspace:^", "@preact/preset-vite": "2.8.1", "@reskript/config-lint": "^6.1.1", "@rspack/cli": "^1.0.0", "@rspack/core": "^1.0.0", "@testing-library/preact": "^3.2.3", "@types/adm-zip": "^0.5.5", "@types/crypto-js": "^4.1.1", "@types/diff": "^5.0.9", "@types/diff-match-patch": "^1.0.36", "@types/jest": "^27.0.1", "@types/lodash": "^4.14.202", "@types/node": "^16.18.21", "@types/proper-lockfile": "^4.1.4", "@types/qs": "^6.9.11", "@types/raf": "^3.4.3", "@types/react-syntax-highlighter": "^15.5.7", "@types/uuid": "^9.0.7", "@types/vscode": "1.69.0", "@types/ws": "^8.5.10", "babel-plugin-transform-object-hasown": "^1.1.0", "clean-webpack-plugin": "^4.0.0", "confusing-browser-globals": "^1.0.10", "copy-webpack-plugin": "^12.0.2", "eslint": "^8.16.0", "inversify": "^6.0.1", "javascript-obfuscator": "^4.1.0", "jsdom": "^24.0.0", "npm-run-all": "^4.1.5", "raw-loader": "^4.0.2", "reflect-metadata": "^0.1.13", "tailwindcss": "^3.3.2", "ts-loader": "^9.5.1", "typedoc": "^0.25.13", "vite": "^5.1.6", "vsce": "^2.9.0", "webpack": "^5.72.1", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^4.9.2", "webpack-merge": "^5.8.0", "webpack-obfuscator": "^3.5.1"}, "dependencies": {"@vscode/l10n": "^0.0.18", "adm-zip": "^0.5.12", "ajv": "^8.12.0", "axios": "^1.5.0", "compare-versions": "^6.0.0-rc.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "diff": "^7.0.0", "diff-match-patch": "^1.0.5", "fastest-levenshtein": "^1.0.16", "form-data": "^4.0.0", "globby": "13.2.2", "ip-address": "^9.0.5", "json-stable-stringify": "^1.2.1", "junk": "^4.0.1", "levenshtein-edit-distance": "^3.0.1", "lodash": "^4.17.21", "markdown-table": "^3.0.3", "marked": "^11.2.0", "material-file-icons": "^2.4.0", "node-machine-id": "^1.1.12", "p-debounce": "^4.0.0", "p-memoize": "^7.1.0", "p-wait-for": "^5.0.2", "proper-lockfile": "^4.1.2", "qs": "^6.11.2", "raf": "^3.4.1", "simple-git": "^3.25.0", "strip-ansi": "^7.1.0", "systeminformation": "^5.23.19", "uuid": "^9.0.1", "vscode-diff": "^2.1.1", "vscode-languageclient": "8.0.2", "vscode-languageserver": "8.0.2", "vscode-languageserver-protocol": "^3.17.5", "vscode-languageserver-textdocument": "^1.0.11", "vscode-languageserver-types": "^3.17.5", "vscode-uri": "^3.0.7", "web-tree-sitter": "0.20.8", "ws": "^8.14.2"}}