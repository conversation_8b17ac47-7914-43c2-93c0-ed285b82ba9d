const childProcess = require('node:child_process');
const {mkdir, access} = require('node:fs/promises');
const readline = require('node:readline');
const path = require('node:path');
const fs = require('node:fs/promises');

function exec(command) {
    return new Promise((resolve, reject) => {
        childProcess.exec(command, (error, stdout) => {
            if (error) {
                reject(error);
                return;
            }
            resolve(stdout);
        });
    });
}

async function pathExists(path) {
    try {
        await access(path);
        return true;
    } catch (error) {
        if (error.code === 'ENOENT') {
            return false;
        }
        throw error;
    }
}

async function main() {
    // 检查是否存在目录
    const exists = await pathExists('bundle');
    if (exists) {
        await exec('rm -rf bundle');
    }
    await mkdir('bundle');
    // 打包
    await exec('NODE_ENV=production BUILD_TARGET=stable npm run compile');
    await exec('cp -r dist/* bundle');

    await mkdir('bundle/comate-engine');

    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
    });
    let repoPath = path.resolve(__dirname, '../../../../internal-extensions/packages/comate-engine');
    rl.question('请输入 comate-engine 的绝对路径，默认认为在相邻项目', answer => {
        if (answer.trim()) {
            repoPath = answer;
        }
        rl.close();
    });
    rl.on('close', async () => {
        await exec(`cp -r ${repoPath}/bundle/* bundle/comate-engine`);
        await mkdir('bundle/comate-engine/bin');
        await exec(`cp -r ${repoPath}/bin/bundle.js bundle/comate-engine/bin/comate.js`);
        // const res = await fetch('https://now.bdstatic.com/stash/v1/dc8c989/frontend/92932cd/resources/node/node-darwin-arm64', {
        //     headers: {
        //         'Content-Type': 'application/octet-stream',
        //     },
        // });
        // const fileStream = fs.createWriteStream('bundle/comate-engine/bin/node', {flags: 'wx'});
        // await finished(Readable.fromWeb(res.body).pipe(fileStream));
        // //TODO 根据环境下载对应的 node
        // await chmod('bundle/comate-engine/bin/node', '755');
        await exec('cp .env.production bundle');

        const plugins = await fs.readdir(path.resolve(__dirname, '../bundle/comate-engine/plugins'));

        // TODO 从package.json 中读取 icon，后续插件可能有更多资源，可以指定资源目录
        for (const plugin of plugins) {
            const pluginPackage = await fs.readFile(
                path.resolve(__dirname, '../bundle/comate-engine/plugins', plugin, 'package.json')
            );
            const {comate: {icon}} = JSON.parse(pluginPackage);
            await mkdir(path.resolve(__dirname, '../bundle/assets/plugins', plugin), {recursive: true});
            await fs.copyFile(
                path.resolve(__dirname, '../bundle/comate-engine/plugins', plugin, icon),
                path.resolve(__dirname, '../bundle/assets/plugins', plugin, path.basename(icon))
            );
        }
        process.exit(0);
    });

}
main();
