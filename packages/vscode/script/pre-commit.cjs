const {execSync} = require('child_process');
const {readFile, writeFile} = require('fs/promises');
const path = require('path');
const {platformIndependentFields} = require('./constants.cjs');

async function stripPlatformSpecificFields() {
    console.info('Stripping platform specific fields from packages/vscode/package.json...');
    const packageJsonPath = path.resolve(__dirname, '..', 'package.json');
    const gitRooPath = path.resolve(__dirname, '..', '..', '..');
    const raw = await readFile(packageJsonPath, 'utf8');
    const jsonData = JSON.parse(raw);
    const entries = Object.fromEntries(
        platformIndependentFields
            .filter(field => field in jsonData)
            .map(field => [field, jsonData[field]])
    );
    const strippedJson = JSON.stringify(entries, null, 4);
    await writeFile(packageJsonPath, strippedJson);
    execSync(`git add ${packageJsonPath}`, {cwd: gitRooPath});
}

async function main() {
    await stripPlatformSpecificFields();
}

main();
