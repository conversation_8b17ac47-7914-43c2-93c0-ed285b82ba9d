const {watch} = require('chokidar');
const {preparePackageJson} = require('./common.cjs');
const path = require('path');

function main() {
    const platform = process.env.PLATFORM;
    const enterpriseVersion = process.env.ENTERPRISE_VERSION;
    if (!platform) {
        console.error('Error: please set PLATFORM env');
        process.exit(1);
    }
    const sourcePath = path.resolve(
        __dirname, '..', `package.${platform}${enterpriseVersion ? '-' + enterpriseVersion : ''}.json`
    );
    const watcher = watch(sourcePath);
    console.info(`Watching ${path.basename(sourcePath)}...`);
    watcher.on('change', () => {
        console.info(`Changes to ${path.basename(sourcePath)} detected`);
        preparePackageJson(platform, enterpriseVersion);
    });
}

main();
