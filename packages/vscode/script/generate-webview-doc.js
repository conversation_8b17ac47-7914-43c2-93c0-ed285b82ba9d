const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generator = require('@babel/generator').default;
const path = require('path');
const fs = require('fs/promises');

// 异步函数，用于编译 JavaScript 代码
async function compileJavaScript() {
    const code = await fs.readFile(path.resolve(__dirname, '../shared/protocols.ts'), 'utf-8'); // 设定要编译的文件路径

    const ast = parser.parse(code, {
        sourceType: 'module', // 或使用 "script"，取决于代码类型
        plugins: [
            'typescript', // 如果解析 TypeScript，包括这个插件
        ],
    });
    traverse(ast, {
        enter(path) {
            // 并且父元素是名为eventmessage的ExportNamedDeclaration
            if (path.isTSEnumMember() && path.parent?.id?.name === 'EventMessage') {
                const originalComments = path.node.leadingComments || [];
                // 移除所有现有的前导注释
                path.node.leadingComments = [];
                path.addComment('leading', [
                    `*\n * @see {@link EventMessageCallbackTypes.${path.node.initializer.value}} 点击查看详情\n${
                        originalComments.map(v => '* @see' + v.value.replace('*', '')).join('\n')
                    }\n`,
                ]);
            }
        },
    });
    // 从 AST 生成新的源代码
    const output = generator(ast, {}, code);
    await fs.writeFile(path.resolve(__dirname, '../shared/protocolsDoc.ts'), output.code);
}

// 调用函数
compileJavaScript();
