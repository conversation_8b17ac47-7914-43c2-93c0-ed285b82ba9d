const {readFile, writeFile} = require('node:fs/promises');
const path = require('node:path');
const {platformIndependentFields} = require('./constants.cjs');

async function preparePackageJson(platform, enterpriseVersion) {
    console.info(
        `Merging package.${platform}${enterpriseVersion ? '-' + enterpriseVersion : ''}.json into package.json...`
    );
    // eslint-disable-next-line max-len
    const sourcePath = path.resolve(__dirname, '..', `package.${platform}${enterpriseVersion ? '-' + enterpriseVersion : ''}.json`);
    const targetPath = path.resolve(__dirname, '..', 'package.json');
    const sourceRaw = await readFile(sourcePath);
    const targetRaw = await readFile(targetPath);
    const sourceContent = JSON.parse(sourceRaw);
    const targetContent = JSON.parse(targetRaw);
    const respectedTargetContent = Object.fromEntries(
        platformIndependentFields
            .filter(field => field in targetContent)
            .map(field => [field, targetContent[field]])
    );
    const mergedContent = {
        ...sourceContent,
        ...respectedTargetContent,
    };
    const mergedRaw = JSON.stringify(mergedContent, null, 4);
    await writeFile(targetPath, mergedRaw);
}

exports.preparePackageJson = preparePackageJson;
