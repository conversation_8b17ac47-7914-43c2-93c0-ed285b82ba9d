# 开发手册

## src 开发说明

src 是 Comate VS Code 插件的主要代码，围绕 [VS Code API](https://code.visualstudio.com/api/references/vscode-api) 实现了例如续写 [InlineCompletionItemProvider](https://code.visualstudio.com/api/references/vscode-api#InlineCompletionItemProvider)，行间按钮 [CodeLensProvider](https://code.visualstudio.com/api/references/vscode-api#CodeLensProvider.provideCodeLenses) 等各样功能。

### 开发

```shell
# 在项目最外层
pnpm run bootstrap
```

开发调试时，使用 VS Code 自带的 Debug 插件，按需选择 `▷ Run Comate (Internal)` 或 `▷ Run Comate (SaaS)` 即可，调试任务会自动设置好对应的环境变量并运行 watch 任务。

#### 多版本说明

目前插件有厂内、SaaS两个版本，并且我们时常希望避免将对内的功能注册到SaaS版，反之亦然（SaaS的License逻辑不希望出现在厂内），因此插件有两个入口文件 [src/extension.ts](./src/extension.ts)（厂内） 和 [src/extension.saas.ts](./src/extension.saas.ts)（SaaS），便于初始化对应版本的功能。

理想情况下，我们希望功能实现的差异可以通过依赖注入等设计模式区分开，在入口文件处实例化不同的实现，但很多时候差异在一个调用位置比较深的函数中，重构十分繁琐时可以简单地通过 `$features.PLATFORM` 变量为不同的版本调整实现。`$features.PLATFORM` 有 2 个值：`internal` 和 `saas`，分别对应厂内和SaaS版本。

另外，由于插件的 package.json 也有不同，我们分别创建了 [package.internal.json](./package.internal.json) 和 [package.saas.json](./package.saas.json) 进行维护，同时让 package.json 成为一个软链，其根据运行/打包前的 `PLATFORM` 环境变量决定指向的版本。README.md，CHANGELOG.md 文件同理。

由于选择了用代码区分厂内和SaaS版本，因此开发时更要重视你的变更会对不同版本造成的影响。例如有这样一个需求：“函数注释”支持自定义prompt，新增接口获取prompt配置，暂时只在厂内发布。“函数注释”功能封装在了 [DocstringProvider](./src/services/DocstringProvider/index.ts)，如果这时在实现中不做任何平台区分，直接增加获取prompt的逻辑，可能会导致SaaS版本出错，比如接口其实在SaaS环境尚不可用，导致功能无法使用。

## webview 开发说明

视图相关代码，比如对话侧边栏的具体实现。

### 技术选型
- Preact (通过 preact/compat 也支持使用大部分 React 库)
- tailwindcss

### 开发

目前 WebView 不仅在 VS Code，也在 JetBrains 版 Comate 插件中使用了，后续可能有更多 IDE 通过 WebView 接入我们的视图。我们希望视图尽量一致，少量的差异通过 $features 变量去区分。

### 独立打包视图给其它使用方

```bash
# run in packages/vscode
pnpm run bundle:webview
```
- 环境变量： `WEBVIEW_CONSUMER`: `vscode`（默认）, `jetbrains`
- 产出位置： packages/vscode/view-consumer/${WEBVIEW_CONSUMER}/dist（把dist目录压缩发给使用方接入即可）

常用的调试包可以使用这个命令

```bash
PLATFORM=internal WEBVIEW_CONSUMER=jetbrains ENVIRONMENT=production npm run bundle:webview
```
