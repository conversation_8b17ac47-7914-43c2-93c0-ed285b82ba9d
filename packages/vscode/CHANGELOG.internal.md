# Change Log

版本：v3.8.0（2025-02-13）
- 🎉 Debug 智能体接管编辑器诊断信息修复，提供更准确的修改。
- 🎉 编码中提供实时改写建议，在拼写错误、变量名修改等场景提供 Tab 一键改写（内测中）。
- 🌈 命令面板中 Comate 相关指令统一风格，便于查找。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v3.7.0（2025-01-13）
- 🎉 对话交互全新升级，更流畅的使用体验。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v3.6.0（2025-01-02）
- 🌈 优化全链路的性能，带来更流畅的交互体验。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v3.5.0（2024-12-26）
- 🎉 感知光标附近诊断信息，实时提供修复代码。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v3.4.0（2024-12-20）
- 🎉 选中代码后右键菜单项新增「修改代码 ⌘I」选项，快来试试吧！
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v3.3.0（2024-12-13）
- 🎉 现在，你可以创建自定义指令了，轻松编排管理常用 prompt，提问更高效。
- 🎉 对话支持切换模型了，可以根据问题自定义使用速度或质量优先的模型。
- 🌈 安全智能体支持了对指定文件或目录进行扫描。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v3.2.0（2024-12-05）
- 🎉 Debug 智能体接管终端报错，在报错处跳转即可唤起，诊断修复能力更强大。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v3.1.0（2024-11-28）
- 🛠️ 修复对话侧边栏样式等其他已知问题，提升用户体验。

版本：v3.0.0（2024-11-21）
- 🎉 开发全流程升级，发布编码智能体、安全智能体、全栈编程智能体。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.24.0（2024-11-01）
- 🎉 现已支持历史会话，新建会话无需担心记录丢失，可随时切回上下文继续提问。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.23.0（2024-10-24）
- 🎉 Happy 1024!
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.22.0（2024-10-17）
- 🎉 智能粘贴为你带来全新的操作体验，自动改写粘贴的代码与上下文有机结合。
- 🌈 Paddle 插件优化了多文件时采纳的体验，便于查阅确认后再写入。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.21.0（2024-09-26）
- 🎉 实验室推出重磅功能，用自然语言编程，就在此刻。
- 🌈 Paddle 插件体验优化，新增指令代码转换的功能指引更清晰。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.20.0（2024-09-12）
- 🎉 终端报错智能 Debug 能力升级，对前端 Webpack 等问题可以提供更精准的诊断和修复。
- 🎉 从终端报错跳转到对应代码行时提供分析与修复的快捷按钮，直面问题现场。
- 🎉 Paddle 插件支持根据目录将文件中 Pytorch 框架转化为飞桨框架。
- 🌈 支持无障碍使用，为视障开发者提供更加便捷、高效的工作环境，使他们能够充分体验技术进步带来的便利与福祉。
- 🌈 行间对话交互优化：更佳的流式输出效果，更准确的触发范围识别。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.19.0（2024-09-06）
- 🎉 提问后增加关联问题建议，开拓提问角度和思路，让提问更深入和全面。
- 🎉 新增 React 组件拆分功能，试试把这项任务交给 Comate，让持续维护代码更简单。
- 🎉 基于代码变更可以智能生成 commit message 了，记得前往 Source Control 区域点击 Comate 图标体验。
- 🌈 对话引导增加代码库相关示例问题。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.18.0（2024-08-30）
- 🎉 行间对话生成的代码支持流式输出了，更快看到编辑的效果，提升协同效率。
- 🎉 支持手动触发代码库索引构建，当回复未能参考代码库最新的代码时，可以手动触发更新。
- 🌈 对话侧边栏迎来样式全面升级，减少干扰元素，功能指示更清晰，便于专注在内容上。
- 🌈 优化对话回复中的 Mermaid 流程图样式，提升阅读体验。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.17.0（2024-08-22）
- 🎉 对话问答支持一键切换高级模式，生成的内容更能和您的代码库相关，回答时效性更强。
- 🌈 对话侧边栏视觉细节升级，输出更流畅，等待效果更丝滑。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.16.0（2024-08-15）
- 🎉 光标悬浮在不同知识类型时会浮窗显示详细信息、并提供了快捷示例，激发灵感。
- 🌈 对话消息过多后自动隐藏历史消息，滚动到顶部后再加载，减小渲染开销。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.15.0（2024-08-09）
- 🎉 Comate 行间对话更新了，支持对划选的代码进行提问或编辑，清晰展示代码变更，快使用快捷键 `cmd+I`(macOS) / `ctrl+I`(Windows) 体验吧！
- 🎉 新增「添加日志」按钮，一键为函数内部增加打印日志，便于 debug。
- 🎉 光标悬浮在任意插件时，浮窗显示更详细的插件信息和快捷提问按钮，插件能力更清晰。
- 🌈 提升首次打开对话侧边栏时的显示速度，提问 0 距离。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.13.0（2024-07-24）
- 🎉 使用「查看变更」时支持在编辑器对比视图中一键采纳 `⌥A`(macOS) / `Alt+A`(Windows)，操作更流畅。
- 🌈 优化续写在编辑器底部状态栏的提示，信息更加直观明确。
- 🌈 解决了对话在消息结尾时输出慢的问题，提升流畅性。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.12.0（2024-07-12）
- 🎉 iCafe 插件支持对 Open API 进行提问，描述你想要查询的资源，轻松获得调用代码示例。
- 🎉 iPipe 插件支持查询构建记录、收藏流水线等。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.11.0（2024-06-27）
- 🎉 Comate 支持行间对话了，`cmd+I`(macOS) / `ctrl+I`(Windows) 唤起输入框，生成的内容直接作用在代码上，人机协同编码更进一步！
- 🌈 增加终端日志内 NPM 相关的错误识别，一键唤起 Comate 进行分析与修复。
- 🌈 支持快捷键触发行间注释生成代码，结对编程由你主导。
- 🌈 更强大的模糊搜索（拼音，首字母），让唤起插件和指令更加便捷。
- 🌈 提升 Go 单测的生成质量。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.10.0（2024-06-14）
- 💡 单测支持自定义 Prompt 了，可前往开放平台配置页面进行设置，生成更符合要求的代码。
- 🌈 提升 Go 续写部分场景的准确性。
- 🛠️ 修复了输入框关键词搜索找不到匹配项等已知问题，提升用户体验。

版本：v2.9.0（2024-06-07）
- 🎉 采纳函数注释后自动帮你在当前文件触发批量生成注释，可查看变更后一键采纳，提升使用效率。
- 💡 修改侧边栏启动样式，新增使用引导，帮你快速熟悉侧边栏功能。
- 💡 对话区插件增加「/插件介绍」指令，帮你更好地了解每个插件的特色功能与使用方式。
- 🌈 优化了终端日志中 Go 语言错误识别的文本范围，修复了覆盖文件路径链接后无法跳转的问题。
- 🌈 优化了输入框提示文字样式，更好应对不同屏幕尺寸下的内容展示。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.8.0（2024-05-31）
- 🎉 Comate 可以排查终端错误信息了，划选报错后在右键菜单点击「Comate: 对此分析与修复」或 ⌘ + 点击报错唤起。
- 🎉 Comate 安全助手插件升级为一方能力，通过输入“/代码安全”指令，可一键扫描、一键修复安全漏洞，高效解决安全问题，助您写出更安全的代码！
- 💡 可以根据自己的使用偏好，在设置中调整推荐内容以单行或多行优先出现。
- 🌈 注释生成代码场景增加 loading 提示、在选中的代码后提示对话快捷键，别忘了 Comate 在和你一起 coding！
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.7.0（2024-05-24）
- 🎉 Comate 上线网络检索了，你可以在输入框中 # 选择「网络检索」进行提问，Comate 便会利用网络知识增强回答。
- 💡 在选中代码后的灯泡中加入了 Comate 解释、Comate 调优的快捷选项，便于提问选中的代码。
- 🌈 对话框输入时无需 @ / 即可选择插件，tab/shift+tab、ctrl+n/ctrl+p 上下切换候选列表选项。
- 🌈 对话框对全键盘操作更友好，ctrl+c 可终止当前生成。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.6.0（2024-05-16）
- 🎉 AutoWork 新增自动化测试和生成API调用代码两个指令。
- 🛠️ 提升 AutoWork 连接稳定性。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.5.0（2024-05-09）
- 🎉 对话框现支持上下键切换历史，便于在历史提问的基础上再次修改。
- 🌈 提升 JS/TS 续写部分场景的准确性。

版本：v2.4.0（2024-04-25）
- 🎉 在 Comate 中可以关联网页了，支持更个性化的问答：你可以在侧边栏中通过「#」关联你的 URL 链接，Comate 帮你深度解读信息，并生成更贴合你需求背景的回答。
- 🎉 发布「工具箱」插件，支持 Base64 编解码、JSON 和 YAML 互转等能力。
- 💡 对话框现在支持 # 引用多个文件或者目录来进行提问，以获得更准确的回答。
- 💡 对话区代码操作现在支持新建文件和插入到终端，代码采纳过程更加顺畅。
- 🌈 对话框视觉升级，你现在可以更直观的看到 Comate 支持的能力了。
- 🌈 对停止生成功能的交互进行了优化，使其变得更加容易操作。
- 🌈 提升 C++ 续写部分场景的准确性。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.3.9（2024-03-19）
- 🎉 新增 AppDev 官方插件，支持百度app开发智能问答、智能析错。
- 🎉 新增 GDP 官方插件，支持 GDP 框架代码生成，智能问答。
- 🎉 新增飞桨 PaddlePaddle 官方插件，支持代码生成，智能问答。
- 🎉 新增百度智能小程序官方插件，支持代码生成，智能问答。
- 🎉 新增 Postman 智能协作插件，支持根据 Postman Request 智能生成接口实现代码。
- 🎉 新增前端一站式 FCNAP 官方插件，支持生成配置环境、部署的路由转发、中间件等高级配置。
- 🎉 新增大前端插件，支持 json 格式化，json 转化 ts 类型，base64 编码解码等六种能力。
- 💡 现在，我们支持在消息流式输出过程中对代码进行操作，可以更快的采纳或复制代码。
- 🌈 对消息的渲染性能进行了优化，现在渲染长消息的速度提升了4倍，有效地解决了滚动卡顿的问题。
- 🌈 对采纳操作和 Tab 键的交互逻辑进行了智能化处理，让您在使用过程中享受到更流畅的用户体验。
- 🌈 我们对代码库相关检索逻辑进行了深度优化，提升了检索效率。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.3.4（2024-03-11）
- 🎉 安全助手插件发布，提供代码安全检测（SCA 高危安全风险）等功能。
- 🎉 Git 插件发布，可通过 Git 插件连接 Github、Gitee、GitLab 等多个常用工具，为你智能生成提交信息、创建 Issue。
- 💡 新增 /help 指令，帮助用户快速上手 Comate。
- 💡 支持配置行内代码操作的展示形式，满足用户的个性化需求。
- 🌈 我们对对话区的样式进行了全新改版，现在的页面信息呈现更加清晰，界面更加清爽。
- 🌈 对输入框的样式进行了精调，可以更清晰的展示更多内容。
- 🌈 优化了 # 分类选择功能，可以根据工作区状态动态调整可选项。
- 🌈 增强了知识集检索能力，让检索更容易。
- 🛠️ 修复其他已知问题，提升用户体验。

版本：v2.3.0（2024-02-06）
- 导航栏新增“帮助文档”、“意见反馈”和“设置”的入口，使您能更快速地获取信息和进行个性化配置。
- 新增了插件内的小调研功能，让我们能够及时了解到您对各项功能的满意度，以便针对性地进行优化。
- 现在，我们支持多行推荐时按行采纳，让您能更为精准地选择需要的代码。
- 对于不标准的 Markdown，我们解决了其展示混乱的问题，让您能更为清晰地阅读文档。
- 完美修复了部分续写场景下括号不完整的问题，免去了您查错的麻烦。
- 对代码检索策略进行了优化，能让您更为快速地找到需要的代码部分。
- 我们的 AutoWork 功能现在新增了代码采纳能力，让代码编写工作更为智能、高效。
- 我们增加了消息的前置判断，防止在未输入有效内容时发送了消息

版本：v2.0.0（2024-01-15）
- 我们发布了 Comate+ 开放平台，现支持通过插件扩展 Comate 能力，提供强大的开放性与定制性，让你的编程现场从未有过的强大。
- 发布多个插件，通过 Git 插件连接 Github、Gitee、GitLab 等多个常用工具，为你智能生成提交信息、创建 Issue，快来通过【@】【/】【#】在 Comate 侧边栏中尝试最新的智能化开发吧。
- 通过丰富的开放基座，企业可以通过 Comate 的插件形式将自身服务、能力、知识等连接到编程现场，增强推荐效果、提供个性化能力，为组织量身打造自有智能代码助手。

版本：v1.20.3（2024-12-26）
- 当 Comate 推荐内容与 IDE 推荐内容共存时，将不会相互冲突。
- 修复其他已知问题，提升用户体验。

版本：v1.15.0（2024-10-27）
- 当 hover 在正则上时，将提供正则解释的快捷入口。
- 修复其他已知问题，提升用户体验。

版本：v1.12.6（2023-09-14）
- 支持代码调优

版本：v1.12.0（2023-09-01）
- 支持 Go 语言批量生成单测
- 增加错误诊断的一键修复功能

版本：v1.11.0（2023-08-11）
- 支持长函数拆分和行间注释

版本：v1.10.1（2023-07-04）
- 支持多轮会话能力
- 单测生成支持 TS/JS 语言

版本：v1.9.1（2023-06-16）
- 新增代码解释功能

版本：v1.9.0（2023-06-10）
- 新增自然语言生成代码对话界面，快捷键 ⌘Y 唤出

版本：v1.8.0（2023-06-01）
- 支持自然语言生成代码

版本：v1.7.0（2023-05-20）
- 支持 Python/Java/Go 语言的函数级别代码生成注释
- 支持 C++ 语言的单测生成

版本：v1.6.0（2023-05-16）
- 支持 Go 语言的单测生成

版本：v1.5.2（2023-05-11）
- 支持 Java 语言的单测生成能力

版本：v1.4.1（2023-04-19）
- 增加首次使用引导
- 支持自定义调节推荐响应速度与推荐准确度

版本：v1.3.1（2023-03-29）
- 适配本地 VS Code 版本

版本：v0.2.5（2023-03-01）
- 支持多条推荐功能

版本：v0.2.2（2023-02-17）
- 优化删除字符时的使用体验
- 去掉全局的禁用配置

版本：v0.2.1（2023-02-15）
- 增加状态栏
- 优化推荐和复用策略

版本：v0.1.0（2022-08-25）
- 产品上线
