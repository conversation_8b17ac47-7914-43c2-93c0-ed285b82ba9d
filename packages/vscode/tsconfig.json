{"compilerOptions": {"module": "commonjs", "target": "es2021", "rootDirs": ["src", "shared"], "outDir": "dist", "types": ["reflect-metadata", "node"], "baseUrl": ".", "paths": {"@shared/*": ["shared/*"], "@/*": ["src/*"]}, "moduleResolution": "Node", "strict": true, "sourceMap": true, "composite": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "esModuleInterop": true, "declaration": true, "resolveJsonModule": true, "skipLibCheck": true}, "exclude": ["node_modules"], "include": ["src/**/*", "shared/**/*", "l10n/*.json", "package.json"]}