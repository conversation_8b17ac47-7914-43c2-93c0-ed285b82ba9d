{"command.generate.title": "Generate more for inline completion", "command.cancel.title": "Cancel (ESC)", "command.createUnitTest.title": "Generate Unit Test", "command.showChatPanel.title": "Start Chat", "command.showInlineChat.title": "Start Inline Chat", "command.optimizeCode.title": "Optimize Code", "command.clearSession.title": "Clear Chat", "command.openPluginConfigPanel.title": "Plugin <PERSON>s", "command.openSettings.title": "Settings", "command.visitHelpDocs.title": "Help", "command.explainSelectedCode.title": "Explain Selected Code", "command.authorized.title": "Authorized", "command.renewLicense.title": "Upgrade to Pro", "command.setLogLevel.title": "Set Log Level", "command.feedback.title": "<PERSON><PERSON><PERSON>", "command.recreateIndex.title": "Update search index for current codebase", "command.logout.title": "Logout", "command.generateMessage.title": "Intelligently generate commit message", "command.autoDebug.title": "Comate: Analyze and fix this", "command.toggleDebugLog.title": "Toggle Debug Log", "command.addFileContextToChat.title": "Add Selected Code to Chat", "command.downloadLogs.title": "DownloadLogs", "command.createNewChat.title": "Create New Chat", "command.inlineChatTrigger.title": "Inline <PERSON><PERSON>", "activitybar.comate.title": "文心快码 Baidu Comate", "configuration.basic.license.description": "License", "configuration.basic.username.description": "Username", "configuration.basic.title": "Basic", "configuration.displayLanguage.title": "Language", "configuration.displayLanguage.description": "Configure Language", "configuration.suggestion.title": "Completion", "configuration.suggestion.enableInlineSuggestion.description": "Enable code completion", "configuration.suggestion.langSuggestion.description": "Turn on/off code completion for specific languages", "configuration.suggestion.inlineSuggestionMode.description": "Code Completion Mode", "configuration.suggestion.inlineSuggestionMode.item.extremeFast": "Fastest", "configuration.suggestion.inlineSuggestionMode.item.fast": "Speed First", "configuration.suggestion.inlineSuggestionMode.item.balance": "Balanced", "configuration.suggestion.inlineSuggestionMode.item.accurate": "Quality First", "configuration.suggestion.inlineSuggestionMode.item.extremeAccurate": "High Quality", "configuration.suggestion.linePreferMode.description": "Multiline or single line is preferred", "configuration.suggestion.linePreferMode.item.auto": "Auto", "configuration.suggestion.linePreferMode.item.single": "Single Line", "configuration.suggestion.linePreferMode.item.multi": "Multi Line", "configuration.enableCommentEnhancement.description": "Enable generating code based on comments by shortcut", "configuration.chat.title": "Cha<PERSON>", "configuration.chat.enableCodebaseEnhancedContext.description": "Enable Comate to automatically add codebase for enhanced answers", "configuration.quickFix.title": "Quick Fix", "configuration.quickFix.enableQuickFix.description": "Enable Quick Fix", "configuration.codeLens.title": "CodeLens", "configuration.codeLens.codelensDisplayMode.item.expand": "Expand", "configuration.codeLens.codelensDisplayMode.item.collapse": "Collapse", "configuration.codeLens.docstringOutputPosition.item.sidebar": "Sidebar", "configuration.codeLens.docstringOutputPosition.item.editor": "Editor", "configuration.codeLens.enableInlineDocstring.description": "Enable Inline Docstring CodeLens", "configuration.codeLens.enableInlineComment.description": "Enable Inline Comment CodeLens", "configuration.codeLens.enableInlineUnitTest.description": "Enable Inline UnitTest CodeLens", "configuration.codeLens.enableInlineSplit.description": "Enable Inline Split CodeLens", "configuration.codeLens.enableInlineOptimize.description": "Enable Inline Optimize CodeLens", "configuration.codeLens.enableInlineExplain.description": "Enable Inline Explain CodeLens", "configuration.codeLens.enableInlineLog.description": "Enable Inline Log CodeLens", "configuration.codeLens.enableCodelens.description": "Controls the visibility of CodeLens", "configuration.codeLens.codelensDisplayMode.description": "Controls the display of CodeLens", "configuration.codeLens.docstringOutputPosition.description": "Controls the output positions of docstring CodeLens", "configuration.security.title": "Security Enhancement", "configuration.security.enableSecurityEnhancement.description": "Enable Security Enhancement", "configuration.unitTest.title": "Unit Test", "configuration.unitTest.unitTestFrameworkForJava.description": "Set testing framework for generating Java unit tests", "configuration.unitTest.unitTestMockForJava.description": "Set mocking framework for generating Java unit tests", "configuration.unitTest.unitTestFrameworkForGo.description": "Set testing framework for generating Go unit tests", "configuration.unitTest.unitTestMockForGo.description": "Set mocking framework for generating Go unit tests", "configuration.unitTest.unitTestFrameworkForC/C++.description": "Set testing framework for generating C/C++ unit tests", "configuration.unitTest.unitTestMockForC/C++.description": "Set mocking framework for generating C/C++ unit tests", "configuration.unitTest.unitTestFrameworkForJs/Ts.description": "Set testing framework for generating JS/TS unit tests", "configuration.unitTest.unitTestMockForJs/Ts.description": "Set mocking framework for generating JS/TS unit tests", "configuration.unitTest.unitTestFrameworkForPython.description": "Set testing framework for generating Python unit tests", "configuration.unitTest.unitTestMockForPython.description": "Set mocking framework for generating Python unit tests", "configuration.unitTest.framework.item.auto": "Automatically detect which testing framework to use", "configuration.unitTest.mock.item.auto": "Automatically detect which mocking framework to use", "configuration.unitTest.mock.item.off": "Do not use any mocking framework", "configuration.private.title": "Enterprise Settings", "configuration.private.serviceBaseUrl.description": "Private service base URL", "configuration.beta.title": "Experimental", "configuration.beta.agents.markdownDescription": "Try out the latest agentic features (only available to enterprise users). Click [here](https://comate.baidu.com/en/survey?track=comateAgentApplyFromIDE) to apply for an enterprise account.", "configuration.beta.agents.fullStackAgent.description": "Enable full-stack programming agent", "configuration.beta.agents.securityAgent.description": "Enable security vulnerability-finder agent"}