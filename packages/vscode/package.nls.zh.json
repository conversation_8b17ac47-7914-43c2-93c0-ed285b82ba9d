{"command.generate.title": "生成更多补全", "command.cancel.title": "取消本次推荐 (ESC)", "command.createUnitTest.title": "生成单测", "command.showChatPanel.title": "打开侧边栏", "command.showInlineChat.title": "开始对话", "command.optimizeCode.title": "调优建议", "command.clearSession.title": "清空会话", "command.openPluginConfigPanel.title": "打开插件配置面板", "command.openSettings.title": "设置", "command.visitHelpDocs.title": "帮助文档", "command.explainSelectedCode.title": "解释代码", "command.authorized.title": "已授权", "command.renewLicense.title": "前往续费", "command.setLogLevel.title": "设置日志等级", "command.feedback.title": "意见反馈", "command.recreateIndex.title": "为当前代码库更新索引", "command.logout.title": "退出登录", "command.autoDebug.title": "Comate: 对此分析与修复", "command.generateMessage.title": "智能生成commit message", "command.toggleDebugLog.title": "切换调试日志", "command.addFileContextToChat.title": "添加选中代码至对话", "command.downloadLogs.title": "下载运行日志", "command.createNewChat.title": "新建对话", "command.inlineChatTrigger.title": "修改代码", "activitybar.comate.title": "文心快码 Baidu Comate", "configuration.basic.title": "基本配置", "configuration.basic.license.description": "License", "configuration.basic.username.description": "用户名", "configuration.displayLanguage.title": "语言", "configuration.displayLanguage.description": "设置语言", "configuration.suggestion.title": "推荐配置", "configuration.suggestion.enableInlineSuggestion.description": "是否开启行间代码补全", "configuration.suggestion.langSuggestion.description": "是否开启指定语言推荐", "configuration.suggestion.inlineSuggestionMode.description": "自定义调节推荐响应速度与推荐准确度", "configuration.suggestion.inlineSuggestionMode.item.extremeFast": "极速模式", "configuration.suggestion.inlineSuggestionMode.item.fast": "速度优先", "configuration.suggestion.inlineSuggestionMode.item.balance": "智能平衡", "configuration.suggestion.inlineSuggestionMode.item.accurate": "精准优先", "configuration.suggestion.inlineSuggestionMode.item.extremeAccurate": "精准模式", "configuration.suggestion.linePreferMode.description": "自定义调节推荐内容为多行优先或单行优先", "configuration.suggestion.linePreferMode.item.auto": "自动", "configuration.suggestion.linePreferMode.item.single": "单行优先", "configuration.suggestion.linePreferMode.item.multi": "多行优先", "configuration.enableCommentEnhancement.description": "是否开启快捷键触发注释生成代码功能", "configuration.chat.title": "Cha<PERSON>", "configuration.chat.enableCodebaseEnhancedContext.description": "是否默认开启基于代码库的上下文增强", "configuration.quickFix.title": "快速修复", "configuration.quickFix.enableQuickFix.description": "是否开启 Comate 快速修复功能", "configuration.codeLens.title": "行间按钮", "configuration.codeLens.codelensDisplayMode.item.expand": "文字平铺", "configuration.codeLens.codelensDisplayMode.item.collapse": "最小化 ICON 展示", "configuration.codeLens.docstringOutputPosition.item.sidebar": "侧边栏", "configuration.codeLens.docstringOutputPosition.item.editor": "编辑区", "configuration.codeLens.enableInlineDocstring.description": "是否开启生成函数注释的行间展示", "configuration.codeLens.enableInlineComment.description": "是否开启生成行内注释的行间展示", "configuration.codeLens.enableInlineUnitTest.description": "是否开启生成单测用例的行间展示", "configuration.codeLens.enableInlineSplit.description": "是否开启长函数拆分的行间展示", "configuration.codeLens.enableInlineOptimize.description": "是否开启代码调优的行间展示", "configuration.codeLens.enableInlineExplain.description": "是否开启代码解释的行间展示", "configuration.codeLens.enableInlineLog.description": "是否开启添加日志的行间展示", "configuration.codeLens.enableCodelens.description": "配置函数注释、行内注释、生成单测、长函数拆分、代码解释、代码调优的行间展示", "configuration.codeLens.codelensDisplayMode.description": "编辑区操作展示方式", "configuration.codeLens.docstringOutputPosition.description": "函数注释、行间注释等输出位置", "configuration.security.title": "安全增强", "configuration.security.enableSecurityEnhancement.description": "是否开启 Comate 安全增强模式", "configuration.unitTest.title": "单元测试", "configuration.unitTest.unitTestFrameworkForJava.description": "配置 Java 语言单测生成的测试框架版本", "configuration.unitTest.unitTestMockForJava.description": "配置 Java 语言单测生成的 Mock 框架", "configuration.unitTest.unitTestFrameworkForGo.description": "配置 Go 语言单测生成的测试框架版本", "configuration.unitTest.unitTestMockForGo.description": "配置 Go 语言单测生成的 Mock 框架", "configuration.unitTest.unitTestFrameworkForC/C++.description": "配置 C/C++ 语言单测生成的测试框架版本", "configuration.unitTest.unitTestMockForC/C++.description": "配置 C/C++ 语言单测生成的 Mock 框架", "configuration.unitTest.unitTestFrameworkForJs/Ts.description": "配置 JS/TS 语言单测生成的测试框架版本", "configuration.unitTest.unitTestMockForJs/Ts.description": "配置 JS/TS 语言单测生成的 Mock 框架", "configuration.unitTest.unitTestFrameworkForPython.description": "配置 Python 语言单测生成的测试框架版本", "configuration.unitTest.unitTestMockForPython.description": "配置 Python 语言单测生成的 Mock 框架", "configuration.unitTest.framework.item.auto": "自动", "configuration.unitTest.mock.item.auto": "自动", "configuration.unitTest.mock.item.off": "关闭", "configuration.private.title": "企业服务配置", "configuration.private.serviceBaseUrl.description": "私有服务地址", "configuration.beta.title": "实验室", "configuration.beta.agents.markdownDescription": "配置是否启用智能体能力，智能体为企业级能力，只有企业申请通过后可用，点击[开通企业](https://comate.baidu.com/zh/survey?track=comateAgentApplyFromIDE)", "configuration.beta.agents.fullStackAgent.description": "开启全栈编程智能体【企业】", "configuration.beta.agents.securityAgent.description": "开启安全智能体【企业】"}