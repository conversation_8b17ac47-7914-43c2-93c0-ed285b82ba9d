{"name": "comate-gitee", "displayName": "<PERSON><PERSON> (Gitee版)", "description": "Coding mate, Pair you create", "publisher": "BaiduComate", "homepage": "https://comate.baidu.com", "version": "2.4.2", "license": "UNLICENSED", "private": true, "keywords": ["gitee", "gitee comate", "baidu", "comate"], "engines": {"vscode": "^1.69.0"}, "icon": "assets/icon.png", "categories": ["Programming Languages", "Machine Learning", "Education", "Snippets"], "tags": ["ai", "autocomplete", "code completion", "chat", "intellisense", "intellicode", "documentation", "generative ai", "ernie-code", "ernie-bot", "javascript", "python", "java", "typescript", "c#", "c++", "php", "shell", "c", "ruby"], "activationEvents": ["onFileSystem:comate-diff", "onFileSystem:comate-ut-report", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "baidu.comate.generate", "title": "%command.generate.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.cancel", "title": "%command.cancel.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.createUnitTest", "title": "%command.createUnitTest.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.showChatPanel", "title": "%command.showChatPanel.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.optimizeCode", "title": "%command.optimizeCode.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.clearSession", "title": "%command.clearSession.title%", "icon": "$(clear-all)", "enablement": "baidu.comate.context.messagesCount > 0 || baidu.comate.context.comatePairMessagesCount > 0", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.openPluginConfigPanel", "title": "%command.openPluginConfigPanel.title%", "category": "<PERSON><PERSON> Comate", "enablement": "baidu.comate.pluginConfigPanelEnabled", "icon": "$(extensions)"}, {"command": "baidu.comate.openSettings", "title": "%command.openSettings.title%", "icon": "$(gear)", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.visitHelpDocs", "title": "%command.visitHelpDocs.title%", "icon": "$(question)", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.explainSelectedCode", "title": "%command.explainSelectedCode.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.authorized", "title": "%command.authorized.title%", "enablement": "false", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.renewLicense", "title": "%command.renewLicense.title%", "category": "<PERSON><PERSON> Comate"}, {"command": "baidu.comate.setLogLevel", "title": "%command.setLogLevel.title%", "category": "<PERSON><PERSON> Comate", "icon": "$(gear)"}, {"command": "baidu.comate.feedback", "title": "%command.feedback.title%", "category": "<PERSON><PERSON> Comate", "icon": "$(feedback)"}, {"command": "baidu.comate.logout", "title": "%command.logout.title%", "category": "<PERSON><PERSON> Comate", "enablement": "baidu.comate.context.isLogin", "icon": "$(log-out)"}], "menus": {"commandPalette": [{"command": "baidu.comate.generate", "when": "baidu.comate.activated"}, {"command": "baidu.comate.cancel", "when": "false"}, {"command": "baidu.comate.clearSession", "when": "false"}, {"command": "baidu.comate.explainSelectedCode", "when": "false"}, {"command": "baidu.comate.optimizeCode", "when": "false"}, {"command": "baidu.comate.createUnitTest", "when": "false"}, {"command": "baidu.comate.explainSelectedCode", "when": "false"}, {"command": "baidu.comate.authorized", "when": "false"}, {"command": "baidu.comate.renewLicense", "when": "false"}, {"command": "baidu.comate.openPluginConfigPanel", "when": "false"}, {"command": "baidu.comate.logout", "when": "baidu.comate.activated"}], "editor/context": [{"submenu": "baidu.comate.commands", "group": "0_0_comate@1"}], "baidu.comate.commands": [{"command": "baidu.comate.createUnitTest", "when": "baidu.comate.activated && baidu.comate.enableUnitTest && editorHasSelection", "group": "0_comate@1"}, {"command": "baidu.comate.explainSelectedCode", "when": "baidu.comate.activated && editorHasSelection", "group": "0_comate@2"}, {"command": "baidu.comate.optimizeCode", "when": "baidu.comate.activated && editorHasSelection", "group": "0_comate@3"}, {"command": "baidu.comate.showChatPanel", "when": "baidu.comate.activated", "group": "0_comate@4"}], "view/title": [{"command": "baidu.comate.clearSession", "when": "view == comate.views.chat", "group": "navigation@7"}, {"command": "baidu.comate.openSettings", "when": "view == comate.views.chat", "group": "navigation@6"}, {"command": "baidu.comate.visitHelpDocs", "when": "view == comate.views.chat", "group": "navigation@3"}, {"command": "baidu.comate.authorized", "when": "view == comate.views.chat && baidu.comate.context.validDays > 7", "group": "navigation@1"}, {"command": "baidu.comate.logout", "when": "view == comate.views.chat", "group": "comate@1"}]}, "submenus": [{"id": "baidu.comate.commands", "label": "<PERSON><PERSON> (Gitee版)"}], "keybindings": [{"command": "baidu.comate.showChatPanel", "key": "ctrl+y", "win": "win+y", "mac": "cmd+y"}, {"command": "baidu.comate.cancel", "key": "escape", "mac": "escape", "when": "inlineSuggestionVisible"}, {"command": "editor.action.inlineSuggest.commit", "key": "Tab", "when": "inlineSuggestionHasIndentationLessThanTabSize && inlineSuggestionVisible && !editorHoverFocused && !editorTabMovesFocus && suggestWidgetVisible"}, {"command": "editor.action.inlineSuggest.trigger", "key": "alt+\\", "when": "editorTextFocus && !editorHasSelection && baidu.comate.activated && !inlineSuggestionsVisible"}, {"command": "baidu.comate.acceptNextLine", "key": "ctrl+down", "win": "ctrl+down", "mac": "cmd+down", "when": "editorTextFocus && !editorReadonly && baidu.comate.activated && inlineSuggestionVisible"}], "viewsContainers": {"activitybar": [{"id": "comate", "title": "BAIDU COMATE (GITEE版)", "icon": "assets/Comate.svg"}]}, "views": {"comate": [{"id": "comate.views.chat", "type": "webview", "name": ""}]}, "configuration": [{"title": "%configuration.basic.title%", "properties": {"baidu.comate.username": {"type": "string", "default": "", "description": "%configuration.basic.username.description%"}, "baidu.comate.license": {"type": "string", "default": "", "description": "%configuration.basic.license.description%"}}}, {"title": "%configuration.displayLanguage.title%", "properties": {"baidu.comate.displayLanguage": {"type": "string", "enum": ["zh-cn", "en"], "enumItemLabels": ["中文 (Simplified Chinese)", "English"], "description": "%configuration.displayLanguage.description%", "default": "zh-cn"}}}, {"title": "%configuration.suggestion.title%", "properties": {"baidu.comate.langSuggestion": {"type": "object", "default": {"all": true, "cpp": true, "css": true, "go": true, "html": true, "java": true, "javascript": true, "less": true, "perl": true, "php": true, "python": true, "ruby": true, "shell": true, "swift": true, "typescript": true, "others": true, "vue": true, "san": true, "sass": true, "scss": true, "vhdl": true, "lua": true, "mermaid": true, "pug": true, "swan": true, "stylus": true, "rust": true, "kotlin": true, "graphql": true, "objectivec": true}, "description": "%configuration.suggestion.langSuggestion.description%"}, "baidu.comate.inlineSuggestionMode": {"type": "string", "enum": ["extremeFast", "fast", "balance", "accurate", "extremeAccurate"], "enumItemLabels": ["%configuration.suggestion.inlineSuggestionMode.item.extremeFast%", "%configuration.suggestion.inlineSuggestionMode.item.fast%", "%configuration.suggestion.inlineSuggestionMode.item.balance%", "%configuration.suggestion.inlineSuggestionMode.item.accurate%", "%configuration.suggestion.inlineSuggestionMode.item.extremeAccurate%"], "description": "%configuration.suggestion.inlineSuggestionMode.description%", "default": "extremeAccurate"}}}, {"title": "%configuration.chat.title%", "properties": {"baidu.comate.enableStreamingSession": {"type": "boolean", "default": true, "description": "%configuration.chat.enableStreamingSession.description%"}}}, {"title": "%configuration.quickFix.title%", "properties": {"baidu.comate.enableQuickFix": {"type": "boolean", "default": true, "description": "%configuration.quickFix.enableQuickFix.description%"}}}, {"title": "%configuration.codeLens.title%", "properties": {"baidu.comate.enableCodelens": {"type": "object", "default": {"enableInlineUnitTest": true, "enableInlineExplain": true, "enableInlineDocstring": true, "enableInlineSplit": true, "enableInlineComment": true, "enableInlineOptimize": true}, "properties": {"enableInlineDocstring": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineDocstring.description%"}, "enableInlineComment": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineComment.description%"}, "enableInlineUnitTest": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineUnitTest.description%"}, "enableInlineSplit": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineSplit.description%"}, "enableInlineOptimize": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineOptimize.description%"}, "enableInlineExplain": {"type": "boolean", "default": true, "description": "%configuration.codeLens.enableInlineExplain.description%"}}, "additionalProperties": false, "description": "%configuration.codeLens.enableCodelens.description%"}, "baidu.comate.codelensDisplayMode": {"type": "string", "enum": ["expand", "collapse"], "enumItemLabels": ["%configuration.codeLens.codelensDisplayMode.item.expand%", "%configuration.codeLens.codelensDisplayMode.item.collapse%"], "description": "%configuration.codeLens.codelensDisplayMode.description%", "default": "expand"}, "baidu.comate.docstringOutputPosition": {"type": "string", "enum": ["sidebar", "editor"], "enumItemLabels": ["%configuration.codeLens.docstringOutputPosition.item.sidebar%", "%configuration.codeLens.docstringOutputPosition.item.editor%"], "description": "%configuration.codeLens.docstringOutputPosition.description%", "default": "sidebar"}}}, {"title": "%configuration.security.title%", "properties": {"baidu.comate.enableSecurityEnhancement": {"type": "boolean", "default": false, "description": "%configuration.security.enableSecurityEnhancement.description%"}}}, {"title": "%configuration.unitTest.title%", "properties": {"baidu.comate.unitTestFrameworkForJava": {"type": "string", "enum": ["junit4", "junit5", "auto"], "enumDescriptions": ["JUnit4", "JUnit5", "%configuration.unitTest.framework.item.auto%"], "description": "%configuration.unitTest.unitTestFrameworkForJava.description%", "default": "auto"}, "baidu.comate.unitTestMockForJava": {"type": "string", "enum": ["<PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "auto", "off"], "enumDescriptions": ["<PERSON><PERSON><PERSON>", "JMockit", "%configuration.unitTest.mock.item.auto%", "%configuration.unitTest.mock.item.off%"], "description": "%configuration.unitTest.unitTestMockForJava.description%", "default": "auto"}, "baidu.comate.unitTestFrameworkForGo": {"type": "string", "enum": ["gotests", "auto"], "enumDescriptions": ["gotests", "%configuration.unitTest.framework.item.auto%"], "description": "%configuration.unitTest.unitTestFrameworkForGo.description%", "default": "auto"}, "baidu.comate.unitTestMockForGo": {"type": "string", "enum": ["gomock", "monkey", "sqlmock", "httptest", "auto", "off"], "enumDescriptions": ["GoMock", "Monkey", "Sqlmock", "httptest", "%configuration.unitTest.mock.item.auto%", "%configuration.unitTest.mock.item.off%"], "description": "%configuration.unitTest.unitTestMockForGo.description%", "default": "auto"}, "baidu.comate.unitTestFrameworkForC/C++": {"type": "string", "enum": ["gtest", "auto"], "enumDescriptions": ["GoogleTest", "%configuration.unitTest.framework.item.auto%"], "description": "%configuration.unitTest.unitTestFrameworkForC/C++.description%", "default": "auto"}, "baidu.comate.unitTestMockForC/C++": {"type": "string", "enum": ["gmock", "auto", "off"], "enumDescriptions": ["Gmock", "%configuration.unitTest.mock.item.auto%", "%configuration.unitTest.mock.item.off%"], "description": "%configuration.unitTest.unitTestMockForC/C++.description%", "default": "auto"}, "baidu.comate.unitTestFrameworkForJs/Ts": {"type": "string", "enum": ["jest", "mocha", "auto"], "enumDescriptions": ["Jest", "<PERSON><PERSON>", "%configuration.unitTest.framework.item.auto%"], "description": "%configuration.unitTest.unitTestFrameworkForJs/Ts.description%", "default": "auto"}, "baidu.comate.unitTestMockForJs/Ts": {"type": "string", "enum": ["auto", "off"], "enumDescriptions": ["%configuration.unitTest.mock.item.auto%", "%configuration.unitTest.mock.item.off%"], "description": "%configuration.unitTest.unitTestMockForJs/Ts.description%", "default": "auto"}, "baidu.comate.unitTestFrameworkForPython": {"type": "string", "enum": ["pytest", "unittest", "auto"], "enumDescriptions": ["<PERSON><PERSON><PERSON>", "Unittest", "%configuration.unitTest.framework.item.auto%"], "description": "%configuration.unitTest.unitTestFrameworkForPython.description%", "default": "auto"}, "baidu.comate.unitTestMockForPython": {"type": "string", "enum": ["auto", "off"], "enumDescriptions": ["%configuration.unitTest.mock.item.auto%", "%configuration.unitTest.mock.item.off%"], "description": "%configuration.unitTest.unitTestMockForPython.description%", "default": "auto"}}}], "icons": {"comate-logo": {"description": "Baidu Comate icon", "default": {"fontPath": "assets/comate.woff", "fontCharacter": "\\0041"}}, "comate-logo-disabled": {"description": "Baidu Comate icon", "default": {"fontPath": "assets/comate.woff", "fontCharacter": "\\0042"}}, "comate-logo-mini": {"description": "Baidu Comate icon", "default": {"fontPath": "assets/comateMini.woff", "fontCharacter": "\\0041"}}}, "iconFonts": [{"id": "comate-font", "src": [{"path": "assets/comate.woff", "format": "woff"}]}]}}