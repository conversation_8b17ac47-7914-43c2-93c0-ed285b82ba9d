import os from 'os';
import {PluginCapabilityType} from './schema.js';
import {PLATFORM} from './common/features.js';

export interface CapabilityIdentity {
    pluginName: string;
    capabilityName: string;
}

export function isCapabilityMatch(x: CapabilityIdentity, y: CapabilityIdentity) {
    return x.pluginName === y.pluginName && x.capabilityName === y.capabilityName;
}

const handleChatQueryType: PluginCapabilityType[] = ['Fallback', 'Prompt', 'Skill'];

/**
 * 判断插件是否可以处理聊天请求
 *
 * @param capabilityType 插件能力类型
 * @returns 如果插件能力类型为可以处理对话请求返回`true`
 */
export function canHandleChatQuery(capabilityType: PluginCapabilityType) {
    return handleChatQueryType.includes(capabilityType);
}

const showToUserType: PluginCapabilityType[] = ['Prompt', 'Skill'];

/**
 * 判断插件能力是否可以展示给用户
 * @param capabilityType 插件能力类型
 * @returns 如果插件能力类型为可以展示返回`true`
 */
export function canShowToUser(capabilityType: PluginCapabilityType) {
    return showToUserType.includes(capabilityType);
}

/**
 * 判断插件是否能够处理扫描请求
 *
 * @param capabilityType 插件能力类型
 * @returns 如果插件能力可以处理扫描请求返回`true`
 */
export function canHandleScanQuery(capabilityType: PluginCapabilityType) {
    return capabilityType === 'Scan';
}

/**
 * 判断插件是否能够进行querySelector
 *
 * @param capabilityType 插件能力类型
 * @returns 如果插件能力可以querySelector返回`true`
 */
export function canHandleQuerySelector(capabilityType: PluginCapabilityType) {
    return ['Prompt', 'Skill'].includes(capabilityType);
}

export function patchEnvPath() {
    const isWin = os.platform() === 'win32';
    const isInternal = process.env['PLATFORM'] === PLATFORM.INTERNAL;

    if (isInternal && !isWin) {
        // 补充一些厂内常见的git路径
        const deckPath = '/home/<USER>/deck/1.0/git/2.32.0/bin:';
        const deck2Path = `${os.homedir()}/.deck/2.0/devel/bin:`;
        process.env.PATH = `/opt/rh/rh-git227/root/usr/bin:${deckPath}${deck2Path}${process.env.PATH}`;
    }

    if (isWin) {
        // Windows下git常见安装路径
        process.env.PATH = `${process.env.PATH};C:\\Program Files\\Git\\bin`;
    }
}
