import crypto from 'crypto';
export interface VirtualDocument {
    existed: boolean;
    content: string;
    absolutePath: string;
    scheme?: 'file' | 'diff';
    isDirty?: boolean;
    isPreview?: boolean;
}

export interface VirtualDocumentSelection {
    start: {line: number, column: number};
    end: {line: number, column: number};
}

interface ReplaceVirtualDiffDocumentParams {
    absolutePath: string;
    content: string;
    scrollToLine?: number;
}

export class VirtualEditor {
    static event = 'VIRTUAL_EDITOR_EVENT';
    protected readonly promiseMap: Map<string, (res: any) => void> = new Map();

    constructor(
        private readonly config: {
            send: (event: string, args: any) => Promise<any>;
            logger: {verbose: (...args: any) => void};
        }
    ) {
    }

    resolve({uuid, ...res}: {uuid: string}) {
        const resolver = this.promiseMap.get(uuid);
        if (resolver) {
            resolver(res);
            this.promiseMap.delete(uuid);
        }
    }

    /**
     * 打开一个指定绝对路径的文档，如果文档存在，右侧文件区域第一列会打开Tab并激活
     */
    async openDocument(params: {absolutePath: string, selection?: VirtualDocumentSelection}): Promise<void> {
        this.sendToIDE('openDocument', params);
    }
    /**
     * 关闭一个指定绝对路径的文档Tab
     */
    async closeDocument(params: {absolutePath: string}): Promise<void> {
        this.sendToIDE('closeDocument', params);
    }

    /**
     * 删除一个指定绝对路径的文档
     */
    async deleteDocument(params: {absolutePath: string}): Promise<void> {
        this.sendToIDE('deleteDocument', params);
    }

    /**
     * 刷新文件树（只用于JetBrains）
     */
    async refreshProjectTree(): Promise<void> {
        this.sendToIDE('refreshProjectTree', {});
    }
    /**
     * 获取右侧光标所在的文档Tab, 返回绝对路径、内容、scheme，如果未打开任何文档，existed为false
     */
    async getActiveDocument(): Promise<VirtualDocument> {
        const uuid = this.sendToIDE('getActiveDocument', {});
        return this.withReceivePromise(uuid, {existed: false, content: '', absolutePath: '', scheme: 'file'});
    }
    /**
     * 获取指定绝对路径的文档, 返回绝对路径、内容、如果文档不存在，existed为false
     */
    async getDocument(params: {absolutePath: string}): Promise<VirtualDocument> {
        const uuid = this.sendToIDE('getDocument', params);
        return this.withReceivePromise(uuid, {
            existed: false,
            absolutePath: params.absolutePath,
            content: '',
        });
    }
    /**
     * 在编辑区打开一个Diff视图，并填充左右的内容，左侧diff文件的来源路径为absolutePath, 左侧基准文件不可编辑
     */
    async openVirtualDiffDocument(
        params: {absolutePath: string, content: string, modified: string, stream?: boolean}
    ): Promise<void> {
        this.sendToIDE('openVirtualDiffDocument', params);
    }
    /**
     * 以文件绝对路径为索引，关闭一个Diff视图
     */
    async closeVirtualDiffDocument(params: {absolutePath: string}): Promise<void> {
        this.sendToIDE('closeVirtualDiffDocument', params);
    }
    /**
     * 以全文替换的方式，更新diff视图右侧的文件内容，如果Diff不存在，则无效果
     */
    async replaceVirtualDiffModifiedDocument(params: ReplaceVirtualDiffDocumentParams): Promise<void> {
        this.sendToIDE('replaceVirtualDiffModifiedDocument', params);
    }
    /**
     * 获取以绝对路径为索引的diff视图右侧最新的内容，如果不存在，则existed为false
     */
    async getVirtualDiffDocument(params: {absolutePath: string}): Promise<VirtualDocument> {
        const uuid = this.sendToIDE('getVirtualDiffDocument', params);
        return this.withReceivePromise(uuid, {existed: false, content: '', absolutePath: params.absolutePath});
    }
    /**
     * 以全文替换的形式，覆盖指定绝对路径的文件并保存
     */
    async saveDocumentWithReplaceContent(params: {absolutePath: string, content: string}): Promise<void> {
        this.sendToIDE('saveDocumentWithReplaceContent', params);
    }

    /**
     * 以全文替换的形式，覆盖指定绝对路径的文件并保存并打开文件
     */
    async saveDocumentWithReplaceContentAndOpen(params: {absolutePath: string, content: string}): Promise<any> {
        const uuid = this.sendToIDE('saveDocumentWithReplaceContentAndOpen', params);
        return this.withReceivePromise(uuid, {});
    }

    async executeTerminalShell(
        params: {cmd: string, cwd?: string, duration?: number, run?: boolean}
    ): Promise<{completed: boolean, output: string}> {
        const uuid = this.sendToIDE('executeTerminalShell', params);
        return this.withReceivePromise(uuid, {completed: false, output: ''}, params.duration);
    }
    /**
     * 获取当前激活的终端，最新一条输出, 任何其余情况，都将output设置为空返回
     */
    async getLatestOutputFromActiveTerminal(): Promise<{output: string}> {
        const uuid = this.sendToIDE('getLatestOutputFromActiveTerminal', {});
        return this.withReceivePromise(uuid, {output: ''}, 2000);
    }

    async openUrlInEditorWebview(params: {url: string, title: string}) {
        this.sendToIDE('openUrlInEditorWebview', params);
    }

    private sendToIDE(action: string, payload: Record<string, any>) {
        // 过滤掉字符长度超过100的属性
        const filterLogProperty = Object.entries(payload).filter(([key]) => ['content', 'modified'].includes(key));
        this.config.logger.verbose(
            `VirtualEditor.${action}, args: ${filterLogProperty.map(([k, v]) => `${k}=${v}`).join(',')}`
        );
        const uuid = crypto.randomUUID();
        this.config.send(VirtualEditor.event, {action, payload: {...payload, uuid}}).catch(() => {});
        return uuid;
    }

    private withReceivePromise<T>(uuid: string, defaultValue: T, threshold = 3000): Promise<T> {
        return new Promise<T>(resolve => {
            const timeout = setTimeout(
                () => {
                    resolve(defaultValue);
                },
                threshold
            );
            this.promiseMap.set(uuid, response => {
                clearTimeout(timeout);
                resolve(response);
            });
        });
    }
}

export type VirtualEditMethod = Exclude<keyof VirtualEditor, 'resolve'>;

export type VirtualEditorMethodCall = {
    // eslint-disable-next-line @typescript-eslint/ban-types
    [K in keyof VirtualEditor as VirtualEditor[K] extends Function ? K : never]: {
        action: K;
        payload: Parameters<VirtualEditor[K]>[0] & {uuid: string};
        response: ReturnType<VirtualEditor[K]>;
    };
}[VirtualEditMethod];
