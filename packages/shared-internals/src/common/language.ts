export const SUPPORTED_SUFFIX_CONFIG: Record<string, string[]> = {
    cpp: [
        'cpp',
        'c++',
        'cc',
        'cp',
        'cxx',
        'h',
        'h++',
        'hh',
        'hpp',
        'hxx',
        'inc',
        'inl',
        'ino',
        'ipp',
        'ixx',
        're',
        'tcc',
        'tpp',
        'c',
        'cats',
        'idc',
    ],
    python: [
        'py',
        'cgi',
        'fcgi',
        'gyp',
        'gypi',
        'lmi',
        'py3',
        'pyde',
        'pyi',
        'pyp',
        'pyt',
        'pyw',
        'rpy',
        'smk',
        'spec',
        'tac',
        'wsgi',
        'xpy',
    ],
    css: [
        'css',
    ],
    go: [
        'go',
    ],
    less: [
        'less',
    ],
    javascript: [
        'js',
        '_js',
        'bones',
        'cjs',
        'es',
        'es6',
        'frag',
        'gs',
        'jake',
        'javascript',
        'jsb',
        'jscad',
        'jsfl',
        'jslib',
        'jsm',
        'jspre',
        'jss',
        'jsx',
        'mjs',
        'njs',
        'pac',
        'sjs',
        'ssjs',
        'xsjs',
        'xsjslib',
        'v',
        'veo',
    ],
    ruby: [
        'rb',
        'builder',
        'eye',
        'fcgi',
        'gemspec',
        'god',
        'jbuilder',
        'mspec',
        'pluginspec',
        'podspec',
        'prawn',
        'rabl',
        'rake',
        'rbi',
        'rbuild',
        'rbw',
        'rbx',
        'ru',
        'ruby',
        'spec',
        'thor',
        'watchr',
    ],
    java: [
        'java',
        'jav',
    ],
    shell: [
        'sh',
        'bash',
        'bats',
        'cgi',
        'command',
        'env',
        'fcgi',
        'ksh',
        'shin',
        'tmux',
        'tool',
        'zsh',
        'zsh-theme',
    ],
    php: [
        'php',
        'aw',
        'ctp',
        'fcgi',
        'inc',
        'php3',
        'php4',
        'php5',
        'phps',
        'phpt',
        'phtml',
    ],
    html: [
        'html',
        'hta',
        'htm',
        'html.hl',
        'inc',
        'xht',
        'xhtml',
    ],
    perl: [
        'pl',
        'al',
        'cgi',
        'fcgi',
        'perl',
        'ph',
        'plx',
        'pm',
        'psgi',
        't',
    ],
    typescript: [
        'ts',
        'cts',
        'mts',
        'tsx',
    ],
    swift: [
        'swift',
    ],
    pug: [
        'pug',
    ],
    scss: [
        'scss',
    ],
    vue: [
        'vue',
    ],
    stylus: [
        'styl',
    ],
    vhdl: [
        'vhdl',
        'vhd',
        'vhf',
        'vhi',
        'vho',
        'vhs',
        'vht',
        'vhw',
    ],
    rust: [
        'rs',
        'rs.in',
    ],
    mermaid: [
        'mermaid',
        'mmd',
    ],
    sass: [
        'sass',
    ],
    graphql: [
        'graphql',
        'gql',
        'graphqls',
    ],
    san: [
        'san',
    ],
    swan: [
        'swan',
        'wxml',
        'wxss',
    ],
    kotlin: [
        'kt',
        'kts',
        'ktm',
    ],
    objectivec: [
        'm',
        'mm',
    ],
    lua: [
        'lua',
    ],
};

// 后缀对应的语言
// copy from gateway-core/src/main/java/com/baidu/coding/suggestion/gateway/lang/bean/Lang.java
export const SUFFIX_LANG_MAP: Record<any, string> = {
    cpp: 'cpp',
    'c++': 'cpp',
    cc: 'cpp',
    cp: 'cpp',
    cxx: 'cpp',
    h: 'cpp',
    'h++': 'cpp',
    hh: 'cpp',
    hpp: 'cpp',
    hxx: 'cpp',
    inc: 'html',
    inl: 'cpp',
    ino: 'cpp',
    ipp: 'cpp',
    ixx: 'cpp',
    re: 'cpp',
    tcc: 'cpp',
    tpp: 'cpp',
    c: 'cpp',
    cats: 'cpp',
    idc: 'cpp',
    java: 'java',
    jav: 'java',
    py: 'python',
    cgi: 'shell',
    fcgi: 'shell',
    gyp: 'python',
    gypi: 'python',
    lmi: 'python',
    py3: 'python',
    pyde: 'python',
    pyi: 'python',
    pyp: 'python',
    pyt: 'python',
    pyw: 'python',
    rpy: 'python',
    smk: 'python',
    spec: 'ruby',
    tac: 'python',
    wsgi: 'python',
    xpy: 'python',
    go: 'go',
    php: 'php',
    aw: 'php',
    ctp: 'php',
    php3: 'php',
    php4: 'php',
    php5: 'php',
    phps: 'php',
    phpt: 'php',
    phtml: 'php',
    js: 'javascript',
    _js: 'javascript',
    bones: 'javascript',
    cjs: 'javascript',
    es: 'javascript',
    es6: 'javascript',
    frag: 'glsl',
    gs: 'javascript',
    jake: 'javascript',
    javascript: 'javascript',
    jsb: 'javascript',
    jscad: 'javascript',
    jsfl: 'javascript',
    jslib: 'javascript',
    jsm: 'javascript',
    jspre: 'javascript',
    jss: 'javascript',
    jsx: 'javascript',
    mjs: 'javascript',
    njs: 'javascript',
    pac: 'javascript',
    sjs: 'javascript',
    ssjs: 'javascript',
    xsjs: 'javascript',
    xsjslib: 'javascript',
    v: 'javascript',
    veo: 'javascript',
    pl: 'perl',
    al: 'perl',
    perl: 'perl',
    ph: 'perl',
    plx: 'perl',
    pm: 'perl',
    psgi: 'perl',
    t: 'perl',
    rb: 'ruby',
    builder: 'ruby',
    eye: 'ruby',
    gemspec: 'ruby',
    god: 'ruby',
    jbuilder: 'ruby',
    mspec: 'ruby',
    pluginspec: 'ruby',
    podspec: 'ruby',
    prawn: 'ruby',
    rabl: 'ruby',
    rake: 'ruby',
    rbi: 'ruby',
    rbuild: 'ruby',
    rbw: 'ruby',
    rbx: 'ruby',
    ru: 'ruby',
    ruby: 'ruby',
    thor: 'ruby',
    watchr: 'ruby',
    swift: 'swift',
    sh: 'shell',
    bash: 'shell',
    bats: 'shell',
    command: 'shell',
    env: 'shell',
    ksh: 'shell',
    shin: 'shell',
    tmux: 'shell',
    tool: 'shell',
    zsh: 'shell',
    'zsh-theme': 'shell',
    css: 'css',
    ts: 'typescript',
    cts: 'typescript',
    mts: 'typescript',
    tsx: 'typescript',
    less: 'less',
    html: 'html',
    hta: 'html',
    htm: 'html',
    'html.hl': 'html',
    xht: 'html',
    xhtml: 'html',
    m: 'objectivec',
    mm: 'objectivec',
    rs: 'rust',
    'rs.in': 'rust',
    kt: 'kotlin',
    kts: 'kotlin',
    ktm: 'kotlin',
    swan: 'swan',
    wxml: 'swan',
    wxss: 'swan',
    san: 'san',
    lua: 'lua',
    vue: 'vue',
    sass: 'sass',
    scss: 'scss',
    styl: 'stylus',
    pug: 'pug',
    vhdl: 'vhdl',
    vhd: 'vhdl',
    vhf: 'vhdl',
    vhi: 'vhdl',
    vho: 'vhdl',
    vhs: 'vhdl',
    vht: 'vhdl',
    vhw: 'vhdl',
    mermaid: 'mermaid',
    mmd: 'mermaid',
    graphql: 'graphql',
    gql: 'graphql',
    graphqls: 'graphql',
    ada: 'ada',
    adb: 'ada',
    ads: 'ada',
    agda: 'agda',
    als: 'alloy',
    g4: 'antlr',
    applescript: 'applescript',
    scpt: 'applescript',
    a51: 'assembly',
    asm: 'assembly',
    nasm: 'assembly',
    aug: 'augeas',
    auk: 'awk',
    awk: 'awk',
    gawk: 'awk',
    mawk: 'awk',
    nawk: 'awk',
    bat: 'batchfile',
    cmd: 'batchfile',
    bsv: 'bluespec',
    cake: 'csharp',
    cs: 'csharp',
    cshtml: 'csharp',
    boot: 'clojure',
    clj: 'clojure',
    cljc: 'clojure',
    cljs: 'clojure',
    cljx: 'clojure',
    cmake: 'cmake',
    _coffee: 'coffeescript',
    cjsx: 'coffeescript',
    coffee: 'coffeescript',
    cson: 'coffeescript',
    iced: 'coffeescript',
    asd: 'commonlisp',
    lisp: 'commonlisp',
    lsp: 'commonlisp',
    ny: 'commonlisp',
    cu: 'cuda',
    cuh: 'cuda',
    dart: 'dart',
    dockerfile: 'dockerfile',
    ex: 'elixir',
    exs: 'elixir',
    elm: 'elm',
    el: 'emacslisp',
    emacs: 'emacslisp',
    erl: 'erlang',
    escript: 'erlang',
    hrl: 'erlang',
    yrl: 'erlang',
    fs: 'fsharp',
    fsi: 'fsharp',
    fsx: 'fsharp',
    f: 'fortran',
    f03: 'fortran',
    f08: 'fortran',
    f90: 'fortran',
    f95: 'fortran',
    for: 'fortran',
    fpp: 'fortran',
    fp: 'glsl',
    fsh: 'glsl',
    fshader: 'glsl',
    geom: 'glsl',
    glsl: 'glsl',
    glslv: 'glsl',
    shader: 'glsl',
    vert: 'glsl',
    vsh: 'glsl',
    vshader: 'glsl',
    groovy: 'groovy',
    gtpl: 'groovy',
    gvy: 'groovy',
    hs: 'haskell',
    hsc: 'haskell',
    idr: 'idris',
    lidr: 'idris',
    thy: 'isabelle',
    jsp: 'javaserverpages',
    json: 'json',
    jl: 'julia',
    hlean: 'lean',
    lean: 'lean',
    lagda: 'literateagda',
    litcoffee: 'literatecoffeescript',
    lhs: 'literatehaskell',
    mak: 'makefile',
    mk: 'makefile',
    mpl: 'maple',
    markdown: 'markdown',
    md: 'markdown',
    mkd: 'markdown',
    mkdn: 'markdown',
    ron: 'markdown',
    ma: 'mathematica',
    mathematica: 'mathematica',
    mt: 'mathematica',
    nb: 'mathematica',
    wl: 'mathematica',
    matlab: 'matlab',
    eliom: 'ocaml',
    eliomi: 'ocaml',
    ml: 'ocaml',
    ml4: 'ocaml',
    mli: 'ocaml',
    mll: 'ocaml',
    mly: 'ocaml',
    dfm: 'pascal',
    lpr: 'pascal',
    pas: 'pascal',
    ps1: 'powershell',
    psd1: 'powershell',
    psm1: 'powershell',
    prolog: 'prolog',
    yap: 'prolog',
    proto: 'protocolbuffer',
    r: 'r',
    rd: 'r',
    rsx: 'r',
    scrbl: 'racket',
    rst: 'restructuredtext',
    rmd: 'rmarkdown',
    sas: 'sas',
    sbt: 'scala',
    scala: 'scala',
    scm: 'scheme',
    sld: 'scheme',
    sps: 'scheme',
    st: 'smalltalk',
    sol: 'solidity',
    rq: 'sparql',
    sparql: 'sparql',
    cql: 'sql',
    db2: 'sql',
    ddl: 'sql',
    pkb: 'sql',
    pks: 'sql',
    plb: 'sql',
    plsql: 'sql',
    prc: 'sql',
    sql: 'sql',
    udf: 'sql',
    stan: 'stan',
    fun: 'standardml',
    sml: 'standardml',
    ado: 'stata',
    do: 'stata',
    doh: 'stata',
    mata: 'stata',
    matah: 'stata',
    sv: 'systemverilog',
    svh: 'systemverilog',
    vh: 'systemverilog',
    adp: 'tcl',
    tcl: 'tcl',
    tm: 'tcl',
    csh: 'tcsh',
    tcsh: 'tcsh',
    bbx: 'tex',
    bib: 'tex',
    cbx: 'tex',
    dtx: 'tex',
    ins: 'tex',
    lbx: 'tex',
    mkii: 'tex',
    mkiv: 'tex',
    mkvi: 'tex',
    sty: 'tex',
    tex: 'tex',
    thrift: 'thrift',
    bas: 'visualbasic',
    frm: 'visualbasic',
    vb: 'visualbasic',
    vba: 'visualbasic',
    vbhtml: 'visualbasic',
    vbs: 'visualbasic',
    xsl: 'xslt',
    xslt: 'xslt',
    y: 'yacc',
    yacc: 'yacc',
    yaml: 'yaml',
    yml: 'yaml',
    zig: 'zig',
    ipynb: 'jupyter',
};
