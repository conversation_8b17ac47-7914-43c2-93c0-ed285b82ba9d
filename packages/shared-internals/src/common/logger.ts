import path from 'node:path';
import fs from 'node:fs';
import fsp from 'node:fs/promises';
import os from 'node:os';
import {
    RemoteConsole,
} from 'vscode-languageserver/node.js';
import dayjs from 'dayjs';

import {LogUploaderProvider} from './LogUploaderProvider.js';

import {
    ACTION_QUERY_SELECTOR,
    ACTION_COMATE_PLUS_QUERY_SELECTOR,
    ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN,
} from '@comate/plugin-shared-internals';

import {UserDetail} from '../bridge/shared.js';
export function isObject(value: any): value is object {
    return typeof value === 'object' && value !== null;
}
// TODO 抽取到全局utils里
export const isDevMod = process.env.NODE_ENV === 'development';

export interface UserDetailWithId extends UserDetail {
    uid: string;
    license: string;
    ideVersion: string;
    version: string;
    platform: string;
    customizeService?: string;
    language?: string;
    device: string;
}

const getCurrentTime = () => {
    return dayjs().format('YYYY-MM-DD HH:mm:ss');
};

const getDateString = (past?: number) => {
    return dayjs().subtract(past || 0, 'day').format('YYYY-MM-DD');
};

type LogItems = Array<object | any>;

/**
 * 最大日志行长度，超过此长度的日志将被过滤，以防日志文件过大
 * @param {number} maxLogLineLength 10KB
 */
const maxLogLineLength = 10000;

/**
 * 写入日志中需要过滤的字段
 * @param {string[]} loggerFilter
 */
const loggerFilter = [
    ACTION_QUERY_SELECTOR,
    ACTION_COMATE_PLUS_QUERY_SELECTOR,
    ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN,
];

/**
 * 日志留存策略
 * @param {number} MAX_LOG_SIZE_MB - 留存大小，单位为MB
 * @param {number} MAX_LOG_DAYS - 留存天数
 */
const MAX_LOG_SIZE_MB = 10;
const MAX_LOG_DAYS = 14;

const MAX_LOG_SIZE_BYTES = MAX_LOG_SIZE_MB * 1024 * 1024; // 自动计算 不用改

export interface LoggerRoleInstance {
    error: (...arr: LogItems) => void;
    warn: (...arr: LogItems) => void;
    info: (...arr: LogItems) => void;
    verbose: (...arr: LogItems) => void;
    setLogInstance: (console: RemoteConsole) => void;
    logUploader: LogUploaderProvider | undefined;
}

function isPaddleErrorLog(role: string, texts: LogItems) {
    if (role === 'pluginChannel' && texts.length > 0 && isObject(texts[0])) {
        const log = texts[0] as {
            source?: string;
            level?: string;
        };
        const providers = ['CodeConvertSkillProvider'];
        if (providers.includes(log.source ?? '') && log.level === 'error') {
            return true;
        }
    }
    return false;
}

export class Logger {
    logUploader?: LogUploaderProvider;
    clientConsole?: RemoteConsole;

    private logFile: string = '';
    private stream: fs.WriteStream | null = null;

    constructor(prefix?: string) {
        // TODO 空判断
        const name = getDateString();
        this.init(prefix ? prefix + '-' + name : name);
    }

    addLogUploader(userDetail: UserDetailWithId) {
        this.logUploader = new LogUploaderProvider({
            platform: userDetail.platform,
            ideVersion: userDetail.ideVersion,
            version: userDetail.version,
            license: userDetail.license,
            username: userDetail.name,
        });
    }

    getLoggerAsRole(role: string): LoggerRoleInstance {
        return {
            logUploader: this.logUploader,
            // asyncInfo = this.writeLog('INFO');
            error: this.writeLog('ERROR', role),
            warn: this.writeLog('WARN', role),
            info: this.writeLog('INFO', role),
            verbose: this.writeLog('VERBOSE', role),
            setLogInstance: (console: RemoteConsole) => {
                this.clientConsole = console;
            },
        };
    }

    private init(name: string) {
        const folder = path.join(os.homedir(), '.comate-engine', 'log');
        const folderExist = fs.existsSync(folder);
        if (!folderExist) {
            fs.mkdirSync(folder, {recursive: true});
        }
        this.logFile = path.join(os.homedir(), '.comate-engine', 'log', `${name}.log`);
        this.stream = fs.createWriteStream(this.logFile);

        this.deleteRedundantLog(folder);
    }

    /**
     * 删除过期日志
     * @param {string} folder - 日志目录
     * 当前的策略是 保留最近MAX_LOG_DAYS天的日志 如果日志文件大小超过MAX_LOG_SIZE_BYTES 删除更早的日志文件
     */
    private deleteRedundantLog(folder: string) {
        const oldestAllowedDate = getDateString(MAX_LOG_DAYS - 1);

        fsp
            .readdir(folder)
            .then(files => {
                const logSizes: {[filename: string]: number} = {};
                let totalSize = 0;
                const filesToDelete: string[] = [];
                const statPromises: Array<Promise<void>> = [];

                // 收集日志文件
                files.forEach(file => {
                    if (file.endsWith('.log')) {
                        const filePath = path.join(folder, file);
                        // 修改为文件的修改时间 而不是文件名的日期
                        const fileDate = fs.statSync(filePath).mtime;

                        if (dayjs(fileDate).isBefore(oldestAllowedDate)) {
                            filesToDelete.push(filePath);
                        }

                        const statPromise = fsp.stat(filePath).then(stats => {
                            logSizes[file] = stats.size;
                            totalSize += stats.size;
                        });
                        statPromises.push(statPromise);
                    }
                });

                // 与engine启动逻辑无关 不阻塞
                void Promise.all(statPromises).then(() => {
                    if (totalSize > MAX_LOG_SIZE_BYTES) {
                        const sortedFiles = Object
                            .entries(logSizes)
                            .sort(([aDate], [bDate]) => bDate.localeCompare(aDate));

                        let accumulatedSize = 0;
                        for (const [file, size] of sortedFiles) {
                            accumulatedSize += size;
                            if (accumulatedSize > MAX_LOG_SIZE_BYTES) {
                                filesToDelete.push(path.join(folder, file));
                            }
                        }
                    }

                    // 删除日志文件
                    Promise
                        .all(filesToDelete.map(file => fsp.unlink(file)))
                        .then(() => {
                            void this.logUploader?.logError(
                                'LogFileDelete',
                                `Deleted log files: ${filesToDelete.join(', ')}`
                            );
                            // 暂不上报剩余日志文件大小
                        })
                        .catch(error => {
                            void this.logUploader?.logError( // 上报删除日志异常
                                'DeleteLogError',
                                `Error while deleting previous logs: ${error}`
                            );
                        });
                });
            })
            .catch(error => {
                // 上报读取日志目录异常
                void this.logUploader?.logError('DeleteLogError', `Error while reading log directory: ${error}`);
            });
    }

    private concatString(...texts: LogItems) {
        return texts.reduce((acc, cur) => {
            if (isObject(cur)) {
                // eslint-disable-next-line
                acc += ' ' + JSON.stringify(cur);
            }
            else {
                // eslint-disable-next-line
                acc += ' ' + cur;
            }
            return acc;
        }, '');
    }

    // eslint-disable-next-line
    private writeLog(level: string, role: string) {
        // eslint-disable-next-line
        return (...texts: LogItems) => {
            if (!this.logFile) {
                return;
            }
            try {
                const content = `[${process.pid}] [${getCurrentTime()}] [${level}] [${role.toUpperCase()}] ${
                    this.concatString(...texts)
                }\n`;

                // 过滤指定字段
                if (level !== 'ERROR' && texts.length > 1) {
                    if (loggerFilter.some(e => texts[1] === e)) {
                        return;
                    }

                    // 过滤超长行
                    if (content.length > maxLogLineLength) {
                        // 理论上不该再有这个字段的上报
                        void this.logUploader?.logError(
                            'FilteredLog',
                            this.concatString(['__filtered_length__', texts[0], texts[1], content.length])
                        );
                        return;
                    }
                }

                // 不要await，不希望调用阻塞
                if (this.stream?.writable) {
                    this.stream.write(content);
                }

                // connection 断开了就不能输出了，系统能异常需要妥善处理
                switch (level) {
                    case ('ERROR'):
                        void this.logUploader?.logError(role, content);
                        this.clientConsole?.error(content);
                        break;
                    case ('WARN'):
                        void this.logUploader?.logError(role, content);
                        this.clientConsole?.warn(content);
                        break;
                    case ('INFO'):
                        if (isPaddleErrorLog(role, texts)) {
                            void this.logUploader?.logError('CodeConvertSkillProvider', content);
                        }
                        isDevMod && this.clientConsole?.info(content);
                        break;
                    case ('VERBOSE'):
                        isDevMod && this.clientConsole?.log(content);
                        break;
                    default:
                        isDevMod && this.clientConsole?.log(content);
                }
            }
            catch (e) {
                if (e instanceof Error) {
                    try {
                        // eslint-disable-next-line
                        if (this.stream?.writable) {
                            this.stream.write(`write ${level} log error ${e.message}`);
                        }
                    }
                    catch {
                        // really noting to do
                    }
                }
            }
        };
    }
}
