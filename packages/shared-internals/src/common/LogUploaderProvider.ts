import os from 'node:os';
import {axiosInstance} from './api.js';

const platform = process.env['PLATFORM'] ?? 'unknow-engine';
const ide = process.env['IDE'] ?? 'unknown';

const ERROR_WHITE_LIST = [
    'Importing JSON module',
    'Debugger listening',
    'SESSION_FINISH',
    'COMATE_PLUS_INITIALIZED',
];

interface LogUploaderEvent {
    category: string;
    label?: string;
    content?: any;
    action?: string;
    source?: string;
}

interface LogInitParams {
    platform: string;
    ideVersion: string;
    version: string;
    license: string;
    username: string;
}

// TODO 抽取到全局utils里
export const isDevMod = process.env.NODE_ENV === 'development';

type LogType = 'event' | 'error';
export class LogUploaderProvider {
    constructor(private readonly initParams: LogInitParams) {
    }

    async logError(category: string, content: string) {
        // 开发环境不上报
        if (isDevMod) {
            return;
        }

        if (ERROR_WHITE_LIST.some(e => content.includes(e))) {
            return;
        }
        void this.logUserAction({
            category,
            content,
        }, 'error');
    }

    async logUserAction(event: LogUploaderEvent, type: LogType = 'event') {
        // 开发环境不上报
        if (isDevMod) {
            return;
        }
        const params = {
            type,
            event,
            platform,
            ide,
            common: {
                app: `comate-${ide}`,
                license: this.initParams.license,
                username: this.initParams.username,
                version: this.initParams.version,
                ideVersion: this.initParams.ideVersion,
                os: {platform: os.platform(), arch: os.arch(), release: os.release()},
                timestamp: Date.now(),
            },
        };

        axiosInstance
            .post(
                '/logger/comate.log',
                params,
                {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            )
            .then(() => {})
            .catch(() => {});
    }
}
