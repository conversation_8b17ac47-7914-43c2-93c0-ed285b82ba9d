import path from 'node:path';

export function formatPrompt(promptTemplate: string, args?: Record<string, unknown>) {
    if (args) {
        return promptTemplate.replace(
            /\{\{(\w+)\}\}/g,
            (match, key) => {
                const replacement = args[key];
                // eslint-disable-next-line no-negated-condition
                return replacement !== undefined ? String(replacement) : match;
            }
        );
    }
    return promptTemplate;
}

export const PROMPTTEMPLATE_ROOT_PATH = '.comate/prompt' as const;

export const getPromptTemplateRootPath = (workspacePath: string) => {
    return path.join(workspacePath, PROMPTTEMPLATE_ROOT_PATH);
};

export const getPromptTemplatePath = (workspacePath: string, fileName: string) => {
    return path.join(workspacePath, PROMPTTEMPLATE_ROOT_PATH, `${fileName}.prompt`);
};

export const getPromptTemplateUuidFromPath = (filePath: string, workspacePath: string) => {
    const promptTemplateRootPath = getPromptTemplateRootPath(workspacePath);
    return path.relative(promptTemplateRootPath, filePath)?.replace(/\.prompt$/, '');
};
