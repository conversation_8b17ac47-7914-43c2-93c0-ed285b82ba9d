import {promises, exists, mkdir as nativeMakeDir, type MakeDirectoryOptions} from 'node:fs';
import path from 'node:path';
import fse from 'fs-extra';
import ospath from 'ospath';

// 读取文件，文件存在返回文件内容，没有返回空字符串
export async function readFileOrNull(filename: string): Promise<string> {
    try {
        const data = await promises.readFile(filename, 'utf8');
        return data;
    }
    catch (err) {
        return '';
    }
}

// 获取操作系统应用数据目录
export function getOsAppDataPath() {
    try {
        return ospath.data();
    }
    catch (e) {
        throw Error(`Failed to get ospath data: ${(e as Error).message}`);
    }
}

const getMode = (options: MakeDirectoryOptions) => {
    const defaults = {mode: 0o777};
    if (typeof options === 'number') {
        return options;
    }
    return ({...defaults, ...options}).mode;
};

function checkPath(pth: string) {
    if (process.platform === 'win32') {
        const pathHasInvalidWinCharacters = /[<>:"|?*]/.test(pth.replace(path.parse(pth).root, ''));

        if (pathHasInvalidWinCharacters) {
            const error = new Error(`Path contains invalid characters: ${pth}`);
            // @ts-ignore 错误代码
            error.code = 'EINVAL';
            throw error;
        }
    }
}

const makeDir = (
    dir: string,
    options: MakeDirectoryOptions & {
        recursive: true;
    }
) => {
    return new Promise((resolve, reject) => {
        try {
            checkPath(dir);
            nativeMakeDir(dir, {
                mode: getMode(options),
            }, resolve);
        }
        catch (e) {
            reject(e);
        }
    });
};

/**
 * 确定文件夹是否存在，不存在则创建
 * @param path 检查的文件夹路径
 * @returns boolean
 */
export async function ensureDirectoryExist(path: string) {
    try {
        const isExist = await new Promise(resolve => exists(path, resolve));
        if (!isExist) {
            await makeDir(path, {recursive: true});
        }
        return true;
    }
    catch (e) {
        return false;
    }
}

export const emptyDir = fse.emptyDir;
export const remove = fse.remove;
export const readJson: typeof fse.readJSON = fse.readJSON;
export const writeJSON: typeof fse.writeJSON = fse.writeJSON;

export type * as TypeFSE from 'fs-extra';
