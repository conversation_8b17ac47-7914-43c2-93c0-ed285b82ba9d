import {axiosInstance} from './api.js';

export interface ResponseBase<T> {
    code: number;
    data: T;
    message?: string;
    traceId: string;
}

// 企业付费版    typeCode: ENTERPRISE               typeId: 1
// 企业免费版    typeCode: TRIAL_ENTERPRISE         typeId: 2
// 高校专用版本  typeCode: EDUCATIONAL              typeId: 3
// 个人付费版    typeCode: INDIVIDUAL               typeId: 1
// 个人免费版    typeCode: TRIAL_INDIVIDUAL         typeId: 2
// 国际版       typeCode: INTERNATIONAL            typeId: 3
// 混合云企业版  typeCode: CUSTOMIZED_ENTERPRISE    typeId: 1

/**
 * vpcConfig字段不为空且status=true时，表明license所在企业为vpc环境部署，此时需要将endpoint字段设置为插件端访问的endpoint
 * status=true时，若intranetDomain字段不为空，那么在涉及autowork相关功能使用时需要进行内网环境校验，
 * 该字段内容为一个域名，具体校验方式为解析该域名，若解析得到的ip属于intranetSubnets列表中的任一个子网，
 * 则说明用户处于内网环境，用户可继续进行操作，否则说明不在内网环境，此时展示报错信息intranetErrorMsg
 */

export interface LicenseFullDetail {
    key: string;
    typeId: number;
    typeCode:
        | 'ENTERPRISE'
        | 'TRIAL_ENTERPRISE'
        | 'EDUCATIONAL'
        | 'INDIVIDUAL'
        | 'TRIAL_INDIVIDUAL'
        | 'INTERNATIONAL'
        | 'CUSTOMIZED_ENTERPRISE';
    type: string;
    customized: boolean; // 是否混合云部署账号，true：是，false：否
    customizedUrl: string; // 自定义服务地址。混合云部署会有自定义服务地址，非混合云部署会返回默认地址
    vpcConfig: {
        status: boolean;
        endpoint: string;
        intranetDomain: string;
        intranetSubnets: string[];
        intranetErrorMsg: string;
    };
    features: {
        enableUploadKnowledgeFromIDE: boolean; // 允许IDE内上传知识，默认可以上传
        customFeedbackURL: string;
    };
    commonConfig: {
        diffTrackerRateSeconds: number;
        effectiveInputLines: number;
        uploadFilesBranchChangeMonitoringInterval: number;
    };
}

export async function getUserLicenseType(license: string) {
    const res = await axiosInstance.get<ResponseBase<LicenseFullDetail>>('/api/key/type/' + license);
    return res.data?.data;
}
