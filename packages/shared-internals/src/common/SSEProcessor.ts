/* eslint-disable max-depth */
import {IncomingMessage} from 'http';
import * as vscode from 'vscode';

function splitChunk(text: string) {
    const lines = text.split('\n');
    const last = lines.pop() ?? '';
    return [lines.filter(line => line !== ''), last] as const;
}

export class SSEProcessor<T> {
    error: boolean = false;
    errorMsg: string = '';

    constructor(
        private readonly body: IncomingMessage,
        private readonly cancellationToken?: vscode.CancellationToken
    ) {}

    async *processSSE(): AsyncGenerator<T, void, void> {
        let lastRemainingData = '';
        const decoder = new TextDecoder();
        try {
            for await (const data of this.body) {
                const decodedData = decoder.decode(data, {stream: true});
                if (this.maybeCancel()) {
                    return;
                }
                const [items, remaining] = splitChunk(lastRemainingData + decodedData);
                lastRemainingData = remaining;
                for (const item of items) {
                    const text = item.replace(/^data:/, '').trim();
                    if (text === '') {
                        continue;
                    }
                    try {
                        const res = JSON.parse(text);
                        yield res;
                    }
                    catch (e: any) {
                        this.cancel();
                        this.error = true;
                        this.errorMsg = text;
                        return;
                    }
                }
            }
        }
        catch (e: any) {
            if (this.maybeCancel()) {
                return;
            }
            throw e;
        }
    }

    maybeCancel() {
        const cancel = !!this.cancellationToken?.isCancellationRequested;
        if (cancel) {
            this.cancel();
        }
        return cancel;
    }

    cancel() {
        this.body.destroy();
    }
}
