export interface ChatSession {
    /** 从头到尾的一次对话 */
    sessionUuid: string;
    /** 一次对话的第一条消息的内容 */
    title: string;
    /** 创建时间 */
    ctime: number;
    /** 最后一次修改时间 */
    utime: number;
    /** 会话当次的工作区目录 */
    workspaceDirectory: string;
    /**
     * 是否是智能体
     * @default false
     */
    isAgent?: boolean;
}

export interface ChatSessionDetail extends ChatSession {
    messages: any[];
}
