import {WorkspaceFolder} from 'vscode-languageserver-types';
import {URI} from 'vscode-uri';
import {readFileOrNull} from './fs.js';
import path from 'node:path';
import {axiosInstance} from './api.js';

export interface PluignConfigSetSchema {
    id: string;
    enabled: boolean;
    sections: Array<Record<string, any>>;
}

export interface PluginConfigSetValue {
    enabled: boolean;
    values: Record<string, any>;
}

export interface PluginConfigSet {
    pluginId: string;
    logo: string;
    pluginName: string;
    displayName: string;
    description: string;
    tags: string[];
    capability: string;
    version: string;
    createTime: string;
    updateTime: string;
    creator: string[];
    admin: string[];
    configSchema: PluignConfigSetSchema;
    configValues: PluginConfigSetValue;
    official: boolean;
}

export interface PluginMeta {
    independentProcess: boolean; // 是否独立进程
    enableBackgroundService: boolean; // 是否启用后台服务
    defaultPermissions: Array<'FileSystem' | 'User'>; // 默认授权，包含时`requestXxx`方法直接返回，不需要用户确认
    behaveAsBuiltIn: boolean; // 是否表现为一方能力
    onlyBusinesses: boolean; // 是否仅限企业可见
    enableDiagnostic: boolean; // 是否启用诊断能力
    diagnosticCapabilityName: string; // 诊断能力名称
}

// 合并配置，将 objectB 的配置合并到 objectA 中，如果 objectA 中存在相同的配置项，则以 objectB 为准
export function mergePluginConfig(objectA: Record<string, any> = {}, objectB: Record<string, any> = {}, depth = 1) {
    for (const key in objectB) {
        if (objectB.hasOwnProperty(key)) {
            if (
                depth < 3 && typeof objectB[key] === 'object' && objectB[key] != null
                && objectA.hasOwnProperty(key) && typeof objectA[key] === 'object' && objectA[key] != null
            ) {
                /* eslint-disable no-param-reassign */
                objectA[key] = mergePluginConfig(objectA[key], objectB[key], depth + 1);
            }
            else {
                /* eslint-disable no-param-reassign */
                objectA[key] = objectB[key];
            }
        }
    }
    return objectA;
}

export async function getCurrentUserPluginConfigSet(userName: string): Promise<PluginConfigSet[]> {
    try {
        const res = await axiosInstance('/api/v2/api/comateplus/configSets/config/enabled', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Username': userName,
            },
            data: JSON.stringify({
                username: userName,
            }),
        });

        // TODO 配置改成key是pluginname
        if (res.status === 200) {
            return res.data.data;
        }
        return [];
    }
    catch (error) {
        return [];
    }
}

// TODO 可以监听一下本地文件变化，然后更新配置
export async function localPluginConfig(workspaceFolders?: WorkspaceFolder[] | readonly WorkspaceFolder[] | null) {
    let mergedConfig: Record<string, any> = {};
    if (workspaceFolders && workspaceFolders.length > 0) {
        const uri = URI.parse(workspaceFolders[0].uri).fsPath;
        // 检查有没有comate.json
        const localConfig = path.join(uri, '.comate', 'plugin-local.json');
        const config = path.join(uri, '.comate', 'plugin.json');
        let localConfigJSON = {};
        let configJSON = {};
        // 配置文件内容可能会出错，所以要try catch
        try {
            localConfigJSON = JSON.parse(await readFileOrNull(localConfig) || '{}');
        }
        catch {
            // ignore
        }
        try {
            configJSON = JSON.parse(await readFileOrNull(config) || '{}');
        }
        catch {
            // ignore
        }
        mergedConfig = mergePluginConfig(configJSON, localConfigJSON);
    }
    return mergedConfig;
}

export async function pluginConfigDetail(
    pluginName: string,
    configSet: PluginConfigSet[],
    localPluginConfig: Record<string, any>
) {
    let cloudConfig: {
        // eslint-disable-next-line @typescript-eslint/member-delimiter-style
        enabled?: boolean;
        // eslint-disable-next-line @typescript-eslint/member-delimiter-style
        config?: Record<string, any>;
    } = {};
    const pluginConfig = configSet.find((v: any) => v.pluginName === pluginName)?.configValues;
    cloudConfig = {
        enabled: pluginConfig?.enabled || true,
        config: pluginConfig?.values,
    };
    const localConfig = localPluginConfig[pluginName];
    return mergePluginConfig(cloudConfig, localConfig);
}
