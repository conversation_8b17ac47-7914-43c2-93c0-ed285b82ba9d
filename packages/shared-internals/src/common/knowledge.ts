import {InformationQueryType} from '../bridge/information.js';
import {knowledgeAxiosInstance, getApiHost} from './api.js';

export interface KnowledgeChunk {
    id: number;
    workspaceType: string;
    workspaceUUID: string;
    knowledgeType: string;
    knowledgeUUID: string;
    knowledgeName: string;
    content: string;
    source: string;
    status: string;
    keys: any[];
    score?: number | null;
    hits?: number | null;
    creator?: string | null;
}

export interface KnowledgeQueryWorkspace {
    type: string;
    uuid: string;
}

export interface User {
    name: string;
    chineseName: string;
    email: string;
}

export interface KnowledgeSet {
    name: string;
    type: 'NORMAL' | 'SYSTEM' | 'FILE' | 'TEMP';
    /** 知识集的类型，文本还是代码 */
    fileContentType: 'TEXT' | 'LOCAL_CODE';
    uuid: string;
    origin: string;
    description: string;
    status: string;
    permission: string;
    iCodeCount: number;
    localCount: number;
    kuCount: number;
    creator: User;
    createAt: string;
    updater: User;
    updateAt: string;
}

export interface KnowledgeSetBase {
    id: string;
    uuid?: string;
    // name: string;
    type: string;
}

export interface KnowledgeOptions {
    knowledgeSets?: KnowledgeQueryWorkspace[];
    mergeUserKnowledge?: boolean;
    additionalQueries?: string[];
    queryRewrite?: boolean;
    withSource?: boolean;
    topK?: number;
    score?: number;
}

const XAccessToken = 'c138a4a8-64e1-451e-b193-80caba5dd0f2';

export async function getKnowledgeQueryResult(
    userName: string,
    query: string,
    retrievalType: InformationQueryType,
    localWorkspaces: KnowledgeQueryWorkspace[],
    options?: KnowledgeOptions,
    knowledgeUUIDs?: string[],
    baseURL?: string,
    cloudUserName?: string
): Promise<{chunks: KnowledgeChunk[]}> {
    const {knowledgeSets, additionalQueries, mergeUserKnowledge, queryRewrite, topK, score} = options || {};
    let workspaces = localWorkspaces;
    if (knowledgeSets?.length) {
        workspaces = mergeUserKnowledge ? knowledgeSets.concat(workspaces) : knowledgeSets;
    }

    // eslint-disable-next-line max-len
    const res = await knowledgeAxiosInstance(
        // 混合云用户使用企业内部署服务，无需『/api/v2/api/aidevops/knowledge』转发
        `${baseURL ? '' : '/api/v2/api/aidevops/knowledge'}/rest/v1/retrieval${knowledgeSets ? '/for-plugin' : ''}`,
        {
            method: 'POST',
            baseURL: baseURL || getApiHost(),
            headers: {
                'X-Source': 'COMATE',
                'Content-Type': 'application/json',
                'Uuap-login-name': userName,
                // 混合云用户不走gateway转发，用户信息通过Cloud-login-name获取
                'Cloud-login-name': cloudUserName ? encodeURIComponent(cloudUserName) : undefined,
                // 混合云用户不走gateway转发，需要请求时携带token
                'X-Access-Token': baseURL ? XAccessToken : undefined,
            },
            data: JSON.stringify({
                query,
                retrievalType,
                workspaces,
                additionalQueries,
                queryRewrite, // queryRewrite 不传的话默认为true
                knowledgeUUIDs,
                topK, // 正整数即可
                score,
            }),
        }
    )
        .then(res => res.data);
    if (res.code === 200) {
        return res.data;
    }
    return res;
}

export async function getCurrentUserKnowledgeSet(
    userName: string,
    baseURL?: string,
    cloudUserName?: string,
    query?: string
): Promise<KnowledgeSet[]> {
    // 混合云用户使用企业内部署服务，无需『/api/v2/api/aidevops/knowledge』转发
    const res = await knowledgeAxiosInstance(
        `${baseURL ? '' : '/api/v2/api/aidevops/knowledge'}/rest/v1/workspaces`,
        {
            baseURL: baseURL || getApiHost(),
            headers: {
                'X-Source': 'COMATE',
                'Uuap-login-name': userName,
                // 混合云用户不走gateway转发，用户信息通过Cloud-login-name获取
                'Cloud-login-name': cloudUserName ? encodeURIComponent(cloudUserName) : undefined,
                // 混合云用户不走gateway转发，需要请求时携带token
                'X-Access-Token': baseURL ? XAccessToken : undefined,
            },
            params: {query},
        }
    )
        .then(res => res.data);
    if (res.code === 200) {
        return res.data.list;
    }
    return res;
}

// 知识服务探活接口 暂时只用于混合云用户场景
export async function knowledgeServiceHealthy(baseURL?: string) {
    await knowledgeAxiosInstance('/api/knowledge/ok', {
        baseURL: baseURL || getApiHost(),
    });
}
