import anyMatch from 'anymatch';
import {globbyStream, Options as GlobbyOptions} from 'globby';
import {ActivationContext, VisibilitySelector} from '../schema.js';
import {LanguageDetector} from './languageDetector.js';

export {LanguageDetector};

const scores = {
    language: 10,
    activeFilePathGlobPartExact: 10,
    activeFilePathGlobPartStar: 5,
    activeFilePathGlobPartStarStar: 2,
    activeFileContent: 20,
    selectedContent: 40,
    workspaceFile: 30,
};

function globWeight(pattern: string) {
    const parts = pattern.split('/');
    return parts.reduce(
        (sum, part) => {
            if (part.includes('**')) {
                return sum + scores.activeFilePathGlobPartStarStar;
            }
            if (part.includes('*')) {
                return sum + scores.activeFilePathGlobPartStar;
            }
            return sum + scores.activeFilePathGlobPartExact;
        },
        0
    );
}

export class VisibilitySelectorMatcher {
    private readonly selector: VisibilitySelector;
    private readonly languageDetector: LanguageDetector;
    private readonly globWeights = new Map<string, number>();

    constructor(selector: VisibilitySelector, languageDetector: LanguageDetector) {
        this.selector = selector;
        this.languageDetector = languageDetector;

        for (const pattern of this.selector.activeFilePath ?? []) {
            this.globWeights.set(pattern, globWeight(pattern));
        }
        for (const pattern of this.selector.workspaceFile ?? []) {
            this.globWeights.set(pattern, globWeight(pattern));
        }
    }

    /**
     * 根据当前工作目录和激活上下文进行匹配
     *
     * @param cwd 当前工作目录
     * @param context 激活上下文
     * @returns 返回匹配得分，为`0`表示不匹配（隐藏插件），`-1`表示没有任何规则（不隐藏），正数则数字越大匹配度越高
     */
    async match(cwd: string, context: ActivationContext): Promise<number> {
        const tasks = [
            this.matchLanguage(context),
            this.matchActiveFilePath(cwd, context),
            this.matchActiveFileContent(context),
            this.matchSelectedContent(context),
            this.matchWorkspaceFile(cwd),
        ];
        const scores = await Promise.all(tasks);

        // 如果有一个条件没匹配（返回`0`），则视为整个选择器匹配不上，此时无论其它条件得多少分，都无效
        if (scores.includes(0)) {
            return 0;
        }

        return scores.reduce<number>(
            (sum, score) => {
                if (typeof score === 'number') {
                    return Math.max(sum, 0) + score;
                }
                return sum;
            },
            -1
        );
    }

    private async matchLanguage(context: ActivationContext): Promise<number | null> {
        if (!this.selector.language?.length) {
            return null;
        }
        const language = this.languageDetector.detect(context.activeFilePath, context.activeFileContent);
        const match = !language || this.selector.language.includes(language);
        return match ? scores.language : 0;
    }

    private async matchActiveFilePath(cwd: string, context: ActivationContext): Promise<number | null> {
        if (!this.selector.activeFilePath?.length) {
            return null;
        }
        const options = {cwd, matchBase: true, returnIndex: true};
        // @ts-expect-error 导出与ESM不匹配
        const index = anyMatch(this.selector.activeFilePath, context.activeFilePath, options);

        if (index < 0) {
            return 0;
        }
        const pattern = this.selector.activeFilePath[index];

        return this.globWeights.get(pattern) ?? 0;
    }

    private async matchActiveFileContent(context: ActivationContext): Promise<number | null> {
        if (!this.selector.activeFileContent?.length) {
            return null;
        }
        const matches = this.selector.activeFileContent.filter(v => context.activeFileContent.includes(v));
        return matches.length * scores.activeFileContent;
    }

    private async matchSelectedContent(context: ActivationContext): Promise<number | null> {
        if (!this.selector.selectedContent?.length) {
            return null;
        }
        const matches = this.selector.selectedContent.filter(v => context.selectedCode.includes(v));
        return matches.length * scores.activeFileContent;
    }

    private async matchWorkspaceFile(cwd: string): Promise<number | null> {
        if (!this.selector.workspaceFile?.length) {
            return null;
        }
        const options: GlobbyOptions = {cwd, gitignore: true};
        // eslint-disable-next-line no-unreachable-loop, @typescript-eslint/no-unused-vars
        for await (const entry of globbyStream(this.selector.workspaceFile, options)) {
            return scores.workspaceFile;
        }
        return 0;
    }
}
