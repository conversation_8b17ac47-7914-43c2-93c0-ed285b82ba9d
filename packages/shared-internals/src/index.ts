export * from './schema.js';
export {readPluginDescription} from './reader.js';
export {
    PluginConfigSet,
    PluginMeta,
    getCurrentUserPluginConfigSet,
    mergePluginConfig,
    localPluginConfig,
    pluginConfigDetail,
} from './common/pluginConfig.js';

export {
    setApiHost,
    getApiHost,
    setPlatform,
    SAAS_API_HOST,
    INTERNAL_API_HOST,
    SAAS_TEST_API_HOST,
    INTERNAL_TEST_API_HOST,
    axiosInstance,
    setPlatformAndEnvironment,
    createAxiosInstance,
    createAxiosCancelTokenSource,
    CanceledError,
    type Axios,
} from './common/api.js';

export {
    readFileOrNull,
    getOsAppDataPath,
    ensureDirectoryExist,
    emptyDir,
    readJson,
    writeJSON,
    remove,
    type TypeFSE,
} from './common/fs.js';

export {Deferred, createDeferred} from './common/deferred.js';

export {
    LogUploaderProvider,
} from './common/LogUploaderProvider.js';

export {
    SSEProcessor,
} from './common/SSEProcessor.js';

export {
    getKnowledgeQueryResult,
    KnowledgeQueryWorkspace,
    KnowledgeChunk,
    KnowledgeSet,
    KnowledgeSetBase,
    KnowledgeOptions,
    getCurrentUserKnowledgeSet,
    knowledgeServiceHealthy,
} from './common/knowledge.js';

export {
    ChatSession,
    ChatSessionDetail,
} from './common/chat.js';

export {
    formatPrompt,
    PROMPTTEMPLATE_ROOT_PATH,
    getPromptTemplateRootPath,
    getPromptTemplatePath,
    getPromptTemplateUuidFromPath,
} from './common/prompt.js';
export {VisibilitySelectorMatcher, LanguageDetector} from './visibilitySelector/index.js';
export {
    isCapabilityMatch,
    canHandleChatQuery,
    canShowToUser,
    canHandleScanQuery,
    canHandleQuerySelector,
    patchEnvPath,
} from './utils.js';
export {PLATFORM, WEBVIEW_CONSUMER, $features, ENVIRONMENT} from './common/features.js';

export * from './common/action.js';

export {getUserLicenseType, LicenseFullDetail} from './common/user.js';
export {NotificationMessage} from './common/notification.js';

export {applyDiff} from './bridge/udiff.js';
export {
    VirtualEditor,
    VirtualDocument,
    VirtualDocumentSelection,
    VirtualEditorMethodCall,
    VirtualEditMethod,
} from './editor/VirtualEditor.js';

export {SUFFIX_LANG_MAP, SUPPORTED_SUFFIX_CONFIG} from './common/language.js';
export {ACTION_V8_SNAP_SHOT} from './bridge/snapShot.js';
