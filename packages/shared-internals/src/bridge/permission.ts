export const ACTION_REQUEST_PERMISSION = 'REQUEST_PERMISSION';

export enum PermissionType {
    WorkspaceFileSystem = 'WorkspaceFileSystem',
    FullDiskFileSystem = 'FullDiskFileSystem',
    CodeSecurity = 'CodeSecurity',
    UserDetail = 'UserDetail',
}

export interface RequestPermissionPayload {
    pluginName: string;
    type: PermissionType;
}

export interface IdeSidePermissionPayload {
    pluginName: string;
    type: PermissionType;
    granted: boolean;
}

export interface IdeSidePermissionResponse {
    action: typeof ACTION_REQUEST_PERMISSION;
    payload: IdeSidePermissionPayload;
}
