import {Information} from './information.js';
import {SystemInfoParsed} from './shared.js';

export const ACTION_SECUBOT = 'ACTION_SECUBOT'; // 安全智能体
export const ACTION_SECUBOT_TASK_PROGRESS = 'ACTION_SECUBOT_TASK_PROGRESS'; // 安全智能体进度
export const SECUBOT_DEFAULT_QUERY = '为我的代码库扫描可能的安全漏洞';

export interface SecubotQueryPayload {
    pluginName: string;
    capabilityName: string;
    systemInfo: SystemInfoParsed;
    input?: SecubotAgentParsed;
    context?: secubotAgentContext;
}

// 支持上下文
export interface secubotAgentContext {
    query?: string;
    selectedCode?: string;
    activeFileContent?: string;
    activeFileLineContent?: string;
    activeFilePath?: string;
    activeFileName?: string;
    activeFileLanguage?: string;
    data?: any;
}
// copy的chatquery
export interface SecubotAgentParsed {
    messageId: string;
    pluginName: string;
    capability?: string | undefined;
    query: string;
    data?: any;
    informationList: Information[];
}

export interface SecubotCard {
    fileNum: number;
    flawNum: number;
    loading: boolean; // card中的任务进行状态 false表示结束
    type: string;
    children: SecubotFileFlaw[];
    bottomTip: string;
    cancellationToken?: boolean; // 用户停止生成的标记
}
export interface SecubotFileFlaw {
    description: string;
    type: string;
    fixStatus: 'none' | 'repairing' | 'verifying' | 'success' | 'fail';
    fileName: string;
    filePath: string;
    content?: string; // 修复后的content fixStatus 为success时 存在content
    children: SecubotFlaw[];
    action?: string;
    status?: string;
    accepted?: number;
    hasContent?: boolean;
    cancellationToken?: boolean; // 用户停止生成的标记
}
export interface SecubotFlaw {
    title: string;
    start: number;
    end: number;
    flawType: 'C' | 'H' | 'M' | 'L' | 'none';
    fixStatus: 'none' | 'true' | 'false';
    details: SecubotFlawDetail; // 漏洞详情 markdown
}
export interface SecubotFlawDetail {
    title: string;
    id?: string;
    flawType?: 'C' | 'H' | 'M' | 'L' | 'none';
    importPath?: string;
    description?: string;
    advice?: string;
    references?: SecubotLink[];
}
export interface SecubotLink {
    url: string;
    title: string;
}
