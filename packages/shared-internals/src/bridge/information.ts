import {KnowledgeOptions} from '../index.js';

export const ACTION_INFORMATION_QUERY = 'INFORMATION_QUERY';

export interface Information {
    uuid: string;
    type: ContextType | string;
    name?: string;
    content?: string;
    contentStart?: Position;
    contentEnd?: Position;
    path?: string;
}

export enum ContextType {
    /** 当前文件 */
    CURRENT_FILE = 'CURRENT_FILE',
    /** 文件 */
    FILE = 'FILE',
    /** 目录 */
    FOLDER = 'FOLDER',
    /** 代码库 */
    REPO = 'REPO',
    /** 系统知识集 */
    SYSTEM = 'SYSTEM',
    /** 普通知识集 */
    NORMAL = 'NORMAL',
    /** 网页 */
    URL = 'URL',
    /** Web，网络搜索 */
    WEB = 'WEB',
    /** 单个 API 链接 */
    API = 'API',
    /** API 的项目 */
    API_PROJECT = 'API_PROJECT',
    /** 用户划选的代码，理论上不会放到 context 里 */
    CODE = 'CODE',
    /** 历史存在，先保留该类型 */
    TEMP = 'TEMP',
    /** 终端，暂时只有autoDebug在用 */
    TERMINAL = 'TERMINAL',
}
export interface Position {
    line: number;
    column: number;
}

export enum InformationQueryType {
    Text = 'TEXT',
    Code = 'CODE',
    File = 'FILE',
}

export interface InformationPayload {
    pluginName: string;
    informationList: Information[];
    query: string;
    type: InformationQueryType;
    options?: KnowledgeOptions;
}

export interface IdeSideInformationPayload {
    pluginName: string;
    result: string[];
}

export interface IdeSideInformationResponse {
    action: typeof ACTION_INFORMATION_QUERY;
    payload: IdeSideInformationPayload;
}
