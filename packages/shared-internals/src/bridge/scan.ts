import {ActivationContext} from '../schema.js';
import {Range, SystemInfoParsed} from './shared.js';

/** IDE侧发起的完整的扫描任务 */
export const ACTION_SCAN_QUERY = 'SCAN_QUERY';

export const ACTION_WILL_SCAN = 'WILL_SCAN';

export const ACTION_SCAN_TASK = 'SCAN_TASK';

export const ACTION_RELEASE_SCAN_TASK = 'RELEASE_SCAN_TASK';

export const ACTION_REPORT_WILL_SCAN = 'REPORT_WILL_SCAN';

export const ACTION_SCAN_TASK_PROGRESS = 'SCAN_TASK_PROGRESS';

/** 获取是否存在存量扫描结果 */
export const ACTION_SCAN_CACHE_EXISTS = 'SCAN_CACHE_EXISTS';

export interface ScanQueryPayload {
    scanId: string;
    context: ActivationContext;
    systemInfo: SystemInfoParsed;
}

export interface WillScanPayload extends ScanQueryPayload {
    pluginName: string;
    capabilityName: string;
}

export interface ReleaseScanTaskPayload {
    scanId: string;
    pluginName: string;
    capabilityName: string;
}

export interface ScanTaskPayload {
    scanId: string;
    pluginName: string;
    capabilityName: string;
    description: string;
    ranges: Range[];
}

/** 表达参与扫描任务的目标 */
export interface ScanHandleGoal {
    /** 说明本次处理的目标 */
    description: string;
    /** 是否独占处理，如果独占并被选中，则其它插件能力都不会处理本次扫描 */
    exclusive?: boolean;
    /** 计划处理和修改的代码块 */
    ranges: Range[];
}

/** 扫描意愿结果 */
export interface ReportWillScanPayload {
    scanId: string;
    pluginName: string;
    capabilityName: string;
    goal: ScanHandleGoal | false;
}
