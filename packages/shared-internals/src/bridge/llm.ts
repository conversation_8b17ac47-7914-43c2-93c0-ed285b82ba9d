import {FunctionDefinition} from '../schema.js';

export const ACTION_ASK_LLM = 'ASK_LLM';

export const ACTION_ASK_LLM_STREAMING = 'ASK_LLM_STREAMING';

export enum LlmType {
    Text,
    Code,
    Function,
    ExplainCode,
    DocCommentForCode,
    CommentForCode,
}

export interface FunctionCall {
    name: string;
    arguments: string;
    thoughts?: string;
}

export interface LlmResponseTypes {
    [LlmType.Text]: string;
    [LlmType.Code]: string;
    [LlmType.Function]: FunctionCall | null;
    [LlmType.ExplainCode]: string;
    [LlmType.DocCommentForCode]: string;
    [LlmType.CommentForCode]: string;
}

interface PayloadBase {
    promptTemplate: string;
    args?: Record<string, unknown>;
    pluginName: string;
}

interface CodePayload {
    code: string;
    filePath: string;
    pluginName: string;
}

export enum TextModel {
    Default = 'default-model',
    ErnieBot = 'ernie-bot',
    ErnieBot128 = 'ernie-bot-3-128',
    ErnieBot4 = 'ernie-bot-4',
    ErnieBot4Turbo = 'ernie-bot-4-turbo',
    ErnieBot4Turbo128 = 'ernie-bot-4-turbo-128',
}

export interface ModelOptions {
    openMaxOutput?: boolean;
    temperature?: number;
    topP?: number;
    penaltyScore?: number;
    /**
     * 是否通过插件启用
     * @example byPlugin true // 表示以插件维度缓存多轮对话，false 表示以skill维度，默认skill维度
     * @example onlyQuery // 传一个字符串，例如直接把query传进来，防止query合并knownledge后太长
     */
    enableMultiturnDialogue?: boolean | {byPlugin?: boolean, onlyQuery?: string};
}

export enum FunctionModel {
    Default = 'default-model',
    ErnieBot = TextModel.ErnieBot,
    ErnieBot4 = TextModel.ErnieBot4,
}

export interface LlmPayloadTypes {
    [LlmType.Text]: PayloadBase & {model?: TextModel, modelOptions?: ModelOptions};
    [LlmType.Code]: PayloadBase & {model?: TextModel, modelOptions?: ModelOptions};
    [LlmType.Function]: PayloadBase & {functions: FunctionDefinition[], model?: FunctionModel};
    [LlmType.ExplainCode]: CodePayload;
    [LlmType.DocCommentForCode]: CodePayload;
    [LlmType.CommentForCode]: CodePayload;
}

export interface IdeSideLlmPayload {
    pluginName: string;
    type: LlmType;
    result: LlmResponseTypes[LlmType];
}

export interface IdeSideLlmResponse {
    action: typeof ACTION_ASK_LLM;
    payload: IdeSideLlmPayload;
}

export interface IdeSideLlmStreamingResponse {
    action: typeof ACTION_ASK_LLM_STREAMING;
    payload: IdeSideLlmPayload;
}

type GenerateLlmPayload<T extends LlmType> = T extends LlmType ? {type: T} & LlmPayloadTypes[T] : never;

export type LlmPayload = GenerateLlmPayload<LlmType>;
