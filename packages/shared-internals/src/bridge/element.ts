import {ChunkContent, DrawChunk, FailChunk, SectionChunk, TaskProgressChunk} from './progress.js';

type PrimitiveChildren = string | number | Array<string | number>;

type ElementChildren = DrawElement | string | number | Array<DrawElement | string | number>;

export type CodeBlockActions =
    | 'replaceToFile'
    | 'insertToFile'
    | 'copy'
    | 'accept'
    | 'diff'
    | 'showFileInsertDiff'
    | 'showFileReplaceDiff'
    | 'newFile'
    | 'insertIntoTerminal'
    | 'viewFile';
export interface FileLink {
    type: 'file-link';
    /** 指向项目中的具体文件，不能以`/`或`..`开始 */
    to: string;
    /** 指定跳转到的行号，行号以1起始 */
    line?: number;
    /** 可选的显示内容，不提供则使用`to`属性 */
    children?: PrimitiveChildren;
    /** 是否展示文件图标 */
    showIcon?: boolean;
}

export interface Markdown {
    type: 'markdown';
    /** 具体的Markdown内容 */
    children: PrimitiveChildren;
}

/** 指代时替换选中代码（或插入到光标位置） */
export interface ReplaceSelectionAcceptMethod {
    method: 'replaceSelection';
    /** 按当前选中还是按触发时选中 */
    cursorMode: 'current' | 'trigger';
}

/** 采纳时替换编辑器中的指定代码 */
export interface ReplaceContentAcceptMethod {
    method: 'replaceContent';
    /** 找到替换的代码 */
    from: string;
    /** 替换为该代码 */
    to: string;
}

/** 采纳到终端，可选直接执行 */
export interface ToTerminalAcceptMethod {
    method: 'toTerminal';
    /** 是否直接执行 */
    run?: boolean;
}

/** 采纳时复制到剪贴板 */
export interface CopyAcceptMethod {
    method: 'copy';
    /** 定制复制的内容，不设置则使用代码块的内容 */
    content?: string;
}

export type AcceptMethod =
    | ReplaceSelectionAcceptMethod
    | ReplaceContentAcceptMethod
    | ToTerminalAcceptMethod
    | CopyAcceptMethod;

export interface SimpleCodeBlock {
    type: 'code-block';
    language?: string;
    hidden?: boolean;
    acceptMethods?: AcceptMethod[];
    /** 代码块是否已经闭合 */
    closed: boolean;
    actions?: CodeBlockActions[];
    replaceToFileData?: {
        filePath: string;
        from: string;
        to: string;
        replaceAll?: boolean;
    };
    insertToFileData?: {
        filePath: string;
        position: {line: number, character: number};
        newText: string;
        metadata?: {needsConfirmation: boolean};
    };
    children: PrimitiveChildren;
}

export interface Suggestion {
    type: 'suggestion';
    acceptMethods: AcceptMethod[];
    title?: string;
    content: string;
    children: ElementChildren;
}

export interface CopyFile {
    /** 文件或目录的绝对路径 */
    fromPath: string;
    /** 目标位置 */
    toPath: string;
}

export interface AcceptWithDependentFiles {
    method: 'acceptWithDependentFiles';
    /** 主文件的内容以及路径 */
    entry: {
        filePath: string;
        content: string;
    };
    /** 被引用的文件会以 cp 的方式复制到目标目录，如果目标位置已存在时不会覆盖 */
    dependencies: CopyFile[];
}

export type Action =
    | 'copy'
    | 'insertIntoTerminal'
    | 'detail'
    | 'execute'
    | 'acceptDir'
    | 'acceptDependentFiles'
    | 'acceptWithDependentFiles'
    | 'insertToFile';

export interface CommandButton {
    type: 'command-button';
    variant?: 'primary' | 'default' | 'text';
    commandName: string;
    data: any;
    replyText?: string;
    action?: Action;
    propagation?: boolean;
    tooltipText?: string;
    actionData?: any;
    children: string;
}

export interface ButtonGroup {
    type: 'button-group';
    children: CommandButton | CommandButton[];
}

export interface AlertTag {
    type: 'alert-tag';
    level: 'normal' | 'low' | 'medium' | 'high' | 'critical';
    children: PrimitiveChildren;
}

export interface Loading {
    type: 'loading';
    children: string;
}

export interface Flex {
    type: 'flex';
    children: ElementChildren;
    /** 是否居中，为true时，默认带上align-items: center */
    centered?: boolean;
    /** 是否修改 flex-direction 为 column */
    vertical?: boolean;
    /** 是否垂直布局并且居中, 如果 true，默认开启 justify-content: center */
    verticalCentered?: boolean;
    /** 是否设置为 inline-flex */
    inline?: boolean;
    /** 是否要保留换行 */
    keepWrap?: boolean;
    /** 是否水平居中，如果 true，默认开启 justify-content: center */
    horizontalCentered?: boolean;
    /** 是否垂直方向左边对齐，如果 true，默认开启 align-items: flex-start */
    verticalStartAlign?: boolean;
    /** 是否两端对齐，如果 true，默认开启 justify-content: space-between */
    betweenCentered?: boolean;
    /** 是否尾端对齐，如果 true，默认开启 justify-content: flex-end */
    flexEndCentered?: boolean;
    /** 是否均摊分布对齐，如果 true，默认开启 justify-content: space-around */
    aroundCentered?: boolean;
    /** 尺寸参考 renderJsx */
    gap?: 'sm' | 's' | 'm' | 'l' | 'xl';
}

export interface Card {
    type: 'card';
    color: 'blue' | 'red' | 'yellow' | 'orange' | 'gray' | 'green';
    children: ElementChildren;
}

type NativeElementType =
    | 'p'
    | 'span'
    | 'ul'
    | 'ol'
    | 'em'
    | 'strong'
    | 'code'
    | 'blockquote'
    | 'h1'
    | 'h2'
    | 'h3'
    | 'h4'
    | 'h5'
    | 'h6'
    | 'li'
    | 'br'
    | 'hr'
    | 'img'
    | 'a'
    | 'table'
    | 'thead'
    | 'tbody'
    | 'tr'
    | 'th'
    | 'td'
    | 'div';

interface NativeElement {
    [key: string]: any;
    type: NativeElementType;
}

export type DrawElement =
    | NativeElement
    | SimpleCodeBlock
    | FileLink
    | Markdown
    | Suggestion
    | CommandButton
    | ButtonGroup
    | AlertTag
    | Loading
    | Flex
    | Card;

export const allowedNativeElement: Record<string, string[]> = {
    p: ['children'],
    span: ['children'],
    ul: ['children'],
    ol: ['children'],
    em: ['children'],
    strong: ['children'],
    code: ['children'],
    blockquote: ['children'],
    h1: ['children'],
    h2: ['children'],
    h3: ['children'],
    h4: ['children'],
    h5: ['children'],
    h6: ['children'],
    li: ['children'],
    table: ['children'],
    thead: ['children'],
    tbody: ['children'],
    tr: ['children'],
    th: ['children'],
    td: ['children'],
    br: [],
    hr: [],
    img: ['src', 'alt', 'width', 'height'],
    a: ['href', 'children'],
};

export function isNativeElement(element: DrawElement): element is NativeElement {
    return !!allowedNativeElement[element.type];
}

const INTERACTIVE_TYPE = new Set(['command-button']);

export function isInteractiveContent(content: ChunkContent | null | undefined) {
    if (!content) {
        return false;
    }

    if (Array.isArray(content)) {
        return content.some(isInteractiveContent);
    }

    if (typeof content === 'string') {
        return false;
    }

    if (INTERACTIVE_TYPE.has(content.type)) {
        return true;
    }

    if (INTERACTIVE_TYPE.has(content.children?.type)) {
        return true;
    }

    if (content.children) {
        if (Array.isArray(content.children)) {
            return content.children.some(isInteractiveContent);
        }
        else {
            return isInteractiveContent(content.children);
        }
    }

    return false;
}

export function isWrappedChunk(chunk: TaskProgressChunk): chunk is DrawChunk | FailChunk {
    return typeof chunk === 'object' && 'command' in chunk && (chunk.command === 'draw' || chunk.command === 'fail');
}

export function isSectionChunk(chunk: TaskProgressChunk): chunk is SectionChunk {
    return typeof chunk === 'object' && 'command' in chunk && (chunk.command === 'section');
}

export function extractChunkContent(chunk: TaskProgressChunk): ChunkContent | null {
    if (isWrappedChunk(chunk)) {
        return chunk.content;
    }

    if (isSectionChunk(chunk)) {
        return null;
    }

    return chunk;
}
