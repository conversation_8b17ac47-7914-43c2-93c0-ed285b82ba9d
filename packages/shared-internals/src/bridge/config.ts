export const ACTION_GET_PLUGIN_CONFIG = 'GET_PLUGIN_CONFIG';

export const ACTION_UPDATE_ENGINE_CONFIG = 'UPDATE_ENGINE_CONFIG';

export interface GetPluginConfigPayload {
    pluginName: string;
    key: string;
}

export interface IdeSideConfigPayload {
    pluginName: string;
    key: string;
    value: any;
}

export interface IdeSideConfigResponse {
    action: typeof ACTION_GET_PLUGIN_CONFIG;
    payload: IdeSideConfigPayload;
}
