export interface Message {
    id: string;
    role: 'assistant' | 'user';
    content: string;
    // 引用的代码块
    code?: string;
}

export enum AgentConversationType {
    /** 安全智能体 */
    SecuBotConversation = 'SecuBotConversation',
    /** Debug智能体 */
    DebugBotConversation = 'DebugBotConversation',
    /** 单测智能体 */
    TestBotConversation = 'TestBotConversation',
    /** 端到端智能体 */
    E2EBotConversation = 'E2EBotConversation',
}

export const AgentConversationTypeNames: Record<AgentConversationType, string> = {
    [AgentConversationType.SecuBotConversation]: '安全智能体',
    [AgentConversationType.DebugBotConversation]: 'Debug智能体',
    [AgentConversationType.TestBotConversation]: '单测智能体',
    [AgentConversationType.E2EBotConversation]: '端到端智能体',
};

export interface Conversation {
    [key: string]: any;
    id: string;
    title: string;
    status: AgentConversationStatus;
    ConversationType: AgentConversationType;
}

// 定义任务的状态类型
export enum AgentConversationStatus {
    /** 创建但未发生内容变化 */
    Ready = 'Ready',
    /** 进行中 */
    Running = 'Running',
    /** 失败 */
    Failed = 'Failed',
    /** 完成 */
    Completed = 'Completed',
    /** 停止生成 */
    Cancelled = 'Cancelled',
}

export interface AgentConversationInfo {
    id: string;
    status: AgentConversationStatus;
    type: AgentConversationType;
    lastQuery?: string;
}

export interface AgentPayload<T = any> {
    payload: T;
    conversationId: string;
    conversationType?: AgentConversationType;
    messageType: 'message-operation' | 'stop-generating' | 'add-conversation' | 'add-message';
}

// 为三方提供Debug智能体的能力
export const ACTION_DEBUG_TASK_PROCESS = 'ACTION_DEBUG_TASK_PROCESS';

export interface DebugAgentPayload {
    // 问题描述
    query: string;
    // 错误代码，使用markdown格式，比如： ```sh\n...\n```
    code?: string;
    // 触发方式
    triggerType?: string;
    // 插件是 VSCODE 还是 JETBRAINS 系列（区分具体版本）
    platform?: string;
    ideVersion?: string;
    command?: {commandLine: string, pwd: string};
    // 自定义 prompt，有的话会用这个传给接口
    customPrompt?: string;
    // terminal 的执行路径
    cwd?: string;
    contexts?: Record<string, any>;
    // 修复后是否执行验证，默认是 true
    needsValidation?: boolean;
}

export interface DebugAgentCodeContextItem {
    // 错误代码、关联代码
    type: 'errorCode' | 'relatedCode';
    // 文件路径
    filePath: string;
    // 代码块起始行
    startLineNum: number;
    // 代码块结束行
    endLineNum: number;
}

export interface BlockItem {
    type: 'text' | 'code';
    content: string;
    replaceToFileData?: {
        filePath: string;
        from: string;
        to: string;
    };
}

export interface DebugAgentResponse {
    // 错误原因
    errorReason?: string;
    // 引用的代码块和上下文
    contexts?: DebugAgentCodeContextItem[];
    // 错误分析和修复代码
    content?: string;
    blocks?: BlockItem[];
}

export interface RAGPayload {
    pluginName: string;
    capabilityName?: string;
    debugAgentPayload: DebugAgentPayload;
}

export const ACTION_ASK_RAG = 'ACTION_ASK_RAG';
export interface RAGPayload {
    pluginName: string;
    capabilityName?: string;
    prompt: string;
}
