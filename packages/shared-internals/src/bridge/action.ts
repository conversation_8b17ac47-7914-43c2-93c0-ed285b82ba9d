import {Accept} from './accept.js';

export type ActionType = 'regenerate' | 'enhancedGenerate' | 'batchAccept';
export type Actions = Partial<Record<string, (content?: any, language?: string, extra?: any) => void>>;

export interface ActionDataTypes {
    regenerate: any;
    enhancedGenerate: any;
    batchAccept: Accept;
}

export interface ActionConfig<T> {
    /** action 文本 */
    name: string;
    /** action 图标 */
    icon?: string;
    /** action 类型 用于颜色渲染 */
    type?: 'primary' | 'normal';
    /** action 描述，hover时展示 */
    description?: string;
    /** 二级描述信息，hover时展示 */
    subDescription?: string;
    /** 成功反馈信息 */
    successLabel?: string;
    /** action对应数据 */
    data?: T extends ActionType ? ActionDataTypes[T] : never;
    /** 位置 */
    position?: 'foot-left' | 'foot-left-before' | 'foot-left-after';
}

/** action的其他信息配置集合 */
export type ActionConfigs = Partial<Record<string, ActionConfig<ActionType>>>;

export interface ActionSet {
    actions: Actions;
    actionConfigs: ActionConfigs;
    activeActionKeys?: string[];
}
