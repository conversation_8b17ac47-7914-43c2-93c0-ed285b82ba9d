/**
 * 表示一个用户的信息
 */
export interface UserDetail {
    /** 用户名，如邮箱前缀 */
    readonly name: string;

    /** 用户显示的名称，如中文姓名 */
    readonly displayName: string;

    /** 用户的完整电子邮件地址 */
    readonly email: string;

    /** 用户license，saas才有 */
    readonly license?: string;
}

export interface SystemInfoParsed {
    userId: string;
    userDetail: UserDetail;
    cwd: string;
    repoId?: string;
}

export interface Range {
    /** 起始位置，包含 */
    start: number;
    /** 结束位置，不包含 */
    end: number;
}
