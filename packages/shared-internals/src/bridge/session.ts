import {AutoIncrementer} from './incrementer.js';
import {ACTION_LOG, LogLevel} from './log.js';
import {StreamingTaskManager, TaskManager} from './taskManager.js';

export const ACTION_SESSION_START = 'SESSION_START';

export const ACTION_SESSION_FINISH = 'SESSION_FINISH';

export interface ChannelImplement {
    on(type: 'message', listener: NodeJS.MessageListener): void;
    send(message: any, callback?: (error: Error | null) => void): void;
}

export interface Execution {
    executionId: number;
    segmentOrder: number;
    done: boolean;
}

type AnyListener = (payload: any, execution?: Execution) => Promise<void>;

type BuiltInAction = typeof ACTION_LOG | typeof ACTION_SESSION_START | typeof ACTION_SESSION_FINISH;

type Listener<P> = (payload: P, execution?: Execution) => Promise<void>;

export type SessionAssociation = Session | string;

export interface SessionOptions {
    /** 是否将生命周期与父`Session`对象链接，默认为`true`，关闭时关闭当前`Session`对象不会同步给父对象关闭 */
    lifeCycleConnected?: boolean;
}

export interface SessionInit {
    association: SessionAssociation;
    options?: SessionOptions | undefined;
}

export interface SessionMessage<A = string, P = any> {
    sessionId: string;
    data: {action: A, payload: P};
    execution?: Execution;
}

export class Session<M extends Record<string, any> = Record<string, any>> {
    readonly sessionId: string;
    protected readonly parent: Session | null;
    private readonly lifeCycleConnected: boolean;
    private readonly listeners = new Map<keyof M | BuiltInAction, AnyListener>();
    private readonly channel: ChannelImplement;
    private readonly timeoutTimer: ReturnType<typeof setTimeout>;
    private readonly executions = new TaskManager<number>();
    private readonly streams = new StreamingTaskManager<number>();
    private readonly incrementer = new AutoIncrementer();

    constructor(init: SessionInit, channel: ChannelImplement) {
        if (typeof init.association === 'string') {
            this.parent = null;
            this.sessionId = init.association;
        }
        else {
            this.parent = init.association;
            this.sessionId = init.association.sessionId;
        }
        this.lifeCycleConnected = init.options?.lifeCycleConnected ?? true;
        this.channel = channel;
        this.initializeListeners();
        this.timeoutTimer = setTimeout(
            () => this.log('warn', this.constructor.name, 'SessionTimeout'),
            5 * 60 * 1000 // 5 分钟Session超时
        );
    }

    async handleMessage<A extends keyof M>(action: A, payload: M[A], execution?: Execution) {
        if (action !== ACTION_LOG) {
            this.log('system', this.constructor.name, 'HandleMessage', {actionName: action});
        }
        await this.listeners.get(action)?.(payload, execution);

        if (!execution) {
            return;
        }

        // 理论上，这2个池子只有1个有对应的任务
        this.streams.put(execution.executionId, execution.segmentOrder, payload, execution.done);
        this.executions.resolve(execution.executionId, payload);
    }

    start(data: any) {
        this.log('system', this.constructor.name, 'SessionStart');
        void this.send({action: ACTION_SESSION_START});
        void this.send(data);
    }

    finish() {
        clearTimeout(this.timeoutTimer);
        this.log('system', this.constructor.name, 'SessionFinish');
        void this.send({action: ACTION_SESSION_FINISH});
    }

    // TODO identify whith child session
    log(level: LogLevel, source: string, action: string, detail?: Record<string, any>) {
        // 日志一路透传到最顶层，中间层不处理
        if (this.parent) {
            this.parent.log(level, source, action, detail);
        }
        else {
            void this.send({action: ACTION_LOG, payload: {level, source, action, detail: detail ?? {}}});
        }
    }

    send(data: any, execution?: Execution) {
        const transmission = execution ?? {
            executionId: this.incrementer.next(),
            segmentOrder: 0,
            done: true,
        };
        const message: SessionMessage = {
            sessionId: this.sessionId,
            data,
            execution: transmission,
        };
        this.channel.send(message);
        return this.executions.start(transmission.executionId);
    }

    sendStreaming<T>(data: any, execution?: Execution): AsyncIterable<T> {
        const transmission = execution ?? {
            executionId: this.incrementer.next(),
            segmentOrder: 0,
            done: true,
        };
        const message: SessionMessage = {
            sessionId: this.sessionId,
            data,
            execution: transmission,
        };
        this.channel.send(message);
        return this.streams.start(transmission.executionId);
    }

    protected setListener<A extends keyof M>(action: A, listener: Listener<M[A]>) {
        if (this.listeners.has(action)) {
            throw new Error(`Duplicate listener on action ${action.toString()}`);
        }

        this.listeners.set(action, listener);
    }

    protected forwardMessageToParent<A extends keyof M>(action: A) {
        this.setListener(
            action,
            async (payload, execution) => {
                if (this.parent) {
                    void this.parent.send({action, payload}, execution);
                }
            }
        );
    }

    protected initializeListeners() {
        this.forwardMessageToParent(ACTION_LOG);
        this.setListener(
            ACTION_SESSION_FINISH,
            async () => {
                if (this.parent && this.lifeCycleConnected) {
                    void this.parent.send({action: ACTION_SESSION_FINISH});
                }
            }
        );
    }
}
