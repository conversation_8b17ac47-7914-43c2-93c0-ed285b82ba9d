export interface WorkspaceEditEntryMetadata {
    /**  A flag which indicates that user confirmation is needed. */
    needsConfirmation: boolean;
    /** A human-readable string which is rendered prominent. */
    label: string;
    /** A human-readable string which is rendered less prominent on the same line. */
    description?: string;
}

export interface Position {
    line: number;
    column: number;
}

interface AcceptByReplace {
    type: 'replace';
    path: string;
    range: {start: Position, end: Position};
    newText: string;
    metadata?: WorkspaceEditEntryMetadata;
}

interface AcceptByInsert {
    type: 'insert';
    path: string;
    position: Position;
    newText: string;
    metadata?: WorkspaceEditEntryMetadata;
}

type AcceptByType = AcceptByReplace | AcceptByInsert;
export type Accept = AcceptByType[];
