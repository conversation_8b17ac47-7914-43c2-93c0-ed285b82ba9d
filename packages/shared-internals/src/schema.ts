import {JSONSchema7Definition} from 'json-schema';

export type PluginCapabilityType = 'Prompt' | 'Skill' | 'Fallback' | 'Scan' | 'Background';

// 下面是最终版本，支持逻辑运算，一期先做最简单的
// interface AndCondition<T> {
//     and: Array<Condition<T>>;
// }

// interface AllCondition<T> {
//     all: Array<Condition<T>>;
// }

// interface OrCondition<T> {
//     or: Array<Condition<T>>;
// }

// interface AnyCondition<T> {
//     any: Array<Condition<T>>;
// }

// type Condition<T> = AndCondition<T> | AllCondition<T> | OrCondition<T> | AnyCondition<T> | T[] | T;

// interface VisibilitySelectorItem {
//     language?: Condition<string>;
//     filePath?: Condition<string>;
//     fileContent?: Condition<string>;
//     selectedContent?: Condition<string>;
// }

// export type VisibilitySelector = VisibilitySelectorItem | Condition<VisibilitySelectorItem>;

// 这个是简化版本，相当于第一层是AND，第二层是OR
export interface VisibilitySelector {
    /** 当前激活文件的语言 */
    language?: string[];
    /** 当前激活文件的路径，使用Glob语法 */
    activeFilePath?: string[];
    /** 当前激活文件的内容包含关键字 */
    activeFileContent?: string[];
    /** 当前用户选中内容包含关键字 */
    selectedContent?: string[];
    /** 工作台包含指定文件，使用Glob语法 */
    workspaceFile?: string[];
}

interface ConfigObjectBase {
    title: string;
    description?: string;
}

interface ConfigStringObject {
    type: 'string';
    enum?: string[];
    default?: string;
}

interface ConfigNumberObject {
    type: 'number' | 'integer';
    default?: number;
}

interface ConfigBooleanObject {
    type: 'boolean';
    default?: boolean;
}

type ConfigPrimitiveObject = ConfigStringObject | ConfigNumberObject | ConfigBooleanObject;

interface ConfigArrayObject {
    type: 'array';
    items: Omit<ConfigPrimitiveObject, 'default'>;
}

export type ConfigSchemaObject = ConfigObjectBase & (ConfigPrimitiveObject | ConfigArrayObject);

// 来自于实际程序注册的结构
export interface ProviderCapabilityInfo {
    owner: PluginDescription;
    type: PluginCapabilityType;
    name: string;
    // 这个类型没那么准确，事实是只有`Fallback`类型可以不写，但考虑到配置本身在`package.json`中也用不上类型，所以没做严格要求
    displayName?: string;
    placeholder?: string;
    defaultUserMessage?: string;
    displayTag?: string;
    visibilitySelector?: VisibilitySelector;
    querySelector?: VisibilitySelector;
    queryScore?: number;
}

// 来自于`package.json`中的声明结构
export interface PluginConfigSection {
    title: string;
    properties: Record<string, ConfigSchemaObject>;
    required?: string[];
}

export interface PluginConfig {
    sections: PluginConfigSection[];
}

export interface CapabilityDescription {
    name: string;
    type: PluginCapabilityType;
    displayName: string;
    description: string;
    placeholder?: string;
    defaultUserMessage?: string;
    displayTag?: string;
    visibilitySelector?: VisibilitySelector;
    querySelector?: VisibilitySelector;
}

export interface PluginDescription {
    name: string;
    version: string;
    icon: string;
    entry: string;
    displayName: string;
    description: string;
    keywords: string[];
    capabilities: CapabilityDescription[];
    configSchema: PluginConfig;
}

// 运行时的共享类型
export interface ActivationContext {
    query: string;
    data?: any;
    selectedCode: string;
    activeFileContent: string;
    activeFileLineContent: string;
    activeFilePath: string;
    activeFileName: string;
    activeFileLanguage: string;
    selectedRange?: [{line: number, character: number}, {line: number, character: number}] | [];
}

export interface FunctionParameterDefinition {
    type: 'object';
    properties: Record<string, JSONSchema7Definition>;
    required?: string[] | undefined;
}

export interface FunctionDefinition {
    name: string;
    description: string;
    parameters: FunctionParameterDefinition;
}

export {ACTION_CHAT_QUERY, ACTION_CHAT_TASK_PROGRESS, ChatQueryParsed, ChatQueryPayload} from './bridge/chat.js';
export {ACTION_LOG, LogLevel, UserLogLevel, LogPayload} from './bridge/log.js';
export {
    FileLink,
    AcceptMethod,
    CopyAcceptMethod,
    ReplaceContentAcceptMethod,
    ReplaceSelectionAcceptMethod,
    SimpleCodeBlock,
    ToTerminalAcceptMethod,
    DrawElement,
    Markdown,
    Suggestion,
    CommandButton,
    ButtonGroup,
    isNativeElement,
    allowedNativeElement,
    isInteractiveContent,
    extractChunkContent,
    isWrappedChunk,
    isSectionChunk,
    AlertTag,
    Loading,
    Flex,
    AcceptWithDependentFiles,
    CopyFile,
    Card,
} from './bridge/element.js';
export {
    DrawChunk,
    SectionChunk,
    FailChunk,
    TaskProgressChunk,
    TaskProgressPayload,
    ChunkContent,
} from './bridge/progress.js';
export {
    ChannelImplement,
    Session,
    SessionAssociation,
    SessionInit,
    SessionOptions,
    Execution,
    ACTION_SESSION_START,
    ACTION_SESSION_FINISH,
} from './bridge/session.js';
export {Channel, ChannelImplementMaybe} from './bridge/channel.js';
export {
    RequestPermissionPayload,
    PermissionType,
    ACTION_REQUEST_PERMISSION,
    IdeSidePermissionPayload,
    IdeSidePermissionResponse,
} from './bridge/permission.js';
export {UserDetail, SystemInfoParsed, Range} from './bridge/shared.js';
export {
    ACTION_ASK_LLM,
    ACTION_ASK_LLM_STREAMING,
    LlmType,
    LlmResponseTypes,
    LlmPayloadTypes,
    LlmPayload,
    TextModel,
    ModelOptions,
    FunctionModel,
    FunctionCall,
    IdeSideLlmPayload,
    IdeSideLlmResponse,
    IdeSideLlmStreamingResponse,
} from './bridge/llm.js';
export {
    ACTION_GET_PLUGIN_CONFIG,
    ACTION_UPDATE_ENGINE_CONFIG,
    GetPluginConfigPayload,
    IdeSideConfigPayload,
    IdeSideConfigResponse,
} from './bridge/config.js';
export {
    Information,
    InformationQueryType,
    ACTION_INFORMATION_QUERY,
    InformationPayload,
    IdeSideInformationResponse,
    IdeSideInformationPayload,
} from './bridge/information.js';
export {
    ACTION_SCAN_QUERY,
    ACTION_WILL_SCAN,
    ACTION_SCAN_TASK,
    ACTION_RELEASE_SCAN_TASK,
    ACTION_SCAN_TASK_PROGRESS,
    ACTION_REPORT_WILL_SCAN,
    ACTION_SCAN_CACHE_EXISTS,
    ScanQueryPayload,
    WillScanPayload,
    ReleaseScanTaskPayload,
    ScanTaskPayload,
    ReportWillScanPayload,
    ScanHandleGoal,
} from './bridge/scan.js';
export {ACTION_START_BACKGROUND_SERVICE, StartBackgroundServicePayload} from './bridge/background.js';
export {ACTION_QUERY_SELECTOR, QuerySelectorPayload} from './bridge/selector.js';
export {ACTION_CUSTOM_COMMAND, CustomCommandPayload, CustomCommandInvokePayload} from './bridge/command.js';
export {
    ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN,
    ACTION_DIAGNOSTIC_SCAN_TASK_PROGRESS,
    ACTION_DIAGNOSTIC_SCAN,
    DiagnosticScanTaskProgressPayload,
    DiagnosticScanTaskProgressChunk,
    DiagnosticScanPayload,
    DiagnosticScanInvokePayload,
    DiagnosticCacheValue,
    DiagnosticScanChangedFiles,
    DiagnosticScanTypes,
    DiagnosticInfo,
    RepairData,
} from './bridge/diagnostic.js';
export {
    TaskStatus,
    DynamicActionItems,
    DynamicCodeChunks,
    DynamicRelativeFiles,
    DynamicCodePlan,
    DynamicCodeGenSteps,
    DynamicReminder,
    DynamicNotification,
    DynamicSection,
} from './bridge/section.js';
export {
    ActionType,
    Actions,
    ActionConfig,
    ActionConfigs,
    ActionSet,
} from './bridge/action.js';
export {
    Accept,
} from './bridge/accept.js';
export {
    AgentConversationType,
    AgentConversationTypeNames,
    AgentConversationStatus,
    AgentConversationInfo,
    Message as AgentMessage,
    AgentPayload,
    ACTION_DEBUG_TASK_PROCESS,
    DebugAgentPayload,
    DebugAgentCodeContextItem,
    DebugAgentResponse,
    BlockItem,
    RAGPayload as DebugAgentPluginPayload,
    ACTION_ASK_RAG,
    RAGPayload,
} from './bridge/agent.js';
export {
    ACTION_GENERATE_MESSAGE,
    ACTION_GENERATE_MESSAGE_REPORT,
    ACTION_BRANCH_CHANGE,
    GenerateMessageResponse,
} from './bridge/generateMessage.js';

// 智能体与插件通信 目前只支持安全智能体
export {
    ACTION_SECUBOT,
    ACTION_SECUBOT_TASK_PROGRESS,
    SECUBOT_DEFAULT_QUERY,
    SecubotQueryPayload,
    SecubotAgentParsed,
    SecubotFileFlaw,
} from './bridge/secubot.js';
