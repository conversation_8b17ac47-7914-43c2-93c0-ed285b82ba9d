import path from 'node:path';
import fs from 'node:fs/promises';
import {PluginDescription} from './schema.js';

/**
 * 验证插件描述信息
 *
 * @param description 插件描述信息
 */
function validatePluginDescription(description: PluginDescription) {
    // 改用`schema-utils`校验
    if (!description.name) {
        throw new Error('Missing plugin name');
    }

    if (!description.entry) {
        throw new Error(`Missing entry for plugin ${description.name}`);
    }

    if (description.capabilities.filter(v => v.type === 'Fallback').length > 1) {
        throw new Error('Only one fallback capability is allowed');
    }
}

export async function readPluginDescription(directory: string): Promise<PluginDescription> {
    const packageFile = path.join(directory, 'package.json');
    const packageInfo = JSON.parse(await fs.readFile(packageFile, 'utf-8'));
    const description = packageInfo.comate;

    if (!description) {
        throw new Error(`No comate field in ${packageFile}`);
    }

    validatePluginDescription(description);

    return description;
}
