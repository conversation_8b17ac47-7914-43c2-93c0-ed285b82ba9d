import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import json from '@rollup/plugin-json';
import dts from 'rollup-plugin-dts';
import terser from '@rollup/plugin-terser';

const config = [{
    input: 'dist/index.js',
    output: {
        file: 'bundle/index.js',
        format: 'es',
    },
    plugins: [
        resolve({
            preferBuiltins: true,
        }),
        commonjs(),
        json(),
        terser(),
    ]
}, {
    input: 'dist/index.d.ts',
    output: [{ file: 'bundle/index.d.ts', format: 'es' }],
    plugins: [dts()],
}];

export default config;
