import {render} from 'preact';
import '@vscode/codicons/dist/codicon.css';
import Chat from '@/components/Chat';
import '@/css/style.css';
import '@/css/theme.css';
import '@/css/variable.css';
import {<PERSON>elProvider} from '@/hooks/useKernel';
import {LastQueryProvider} from '../components/LastQueryProvider';

const App = (
    <KernelProvider>
        <LastQueryProvider>
            <Chat />
        </LastQueryProvider>
    </KernelProvider>
);

const ariaDiv = document.createElement('div');
const isWindows = window.navigator.userAgent.includes('Windows');
ariaDiv.style.position = 'absolute';
ariaDiv.style.left = '-999em';
ariaDiv.style.width = '1px'; // 添加宽度限制，防止元素内容超出预期
ariaDiv.setAttribute('id', 'aria-live-polite');
ariaDiv.setAttribute('aria-live', isWindows ? 'polite' : 'assertive');
ariaDiv.setAttribute('aria-atomic', 'true');
document.body.appendChild(ariaDiv);

const rootDiv = document.createElement('div');
rootDiv.style.position = 'relative';

render(
    App,
    document.body.appendChild(rootDiv)
);
