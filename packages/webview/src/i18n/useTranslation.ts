import {useCallback} from 'preact/hooks';
import {useTranslation as useI18nextTranslation} from 'react-i18next';
import {TranslationKey} from './init';

/**
 * 和 react-i18next 的 useTranslation 类似，只是增加了key的类型，防止遗漏
 *
 * @returns
 */
export function useTranslation() {
    const ret = useI18nextTranslation();

    // TODO: 把options的类型也加一下
    const t = useCallback(
        (key: TranslationKey, options?: Record<string, string>) => {
            return ret.t(key, options);
        },
        [ret]
    );

    return {...ret, t};
}
