{"clearModal.title": "Delete chat history", "clearModal.description": "Are you sure you want to delete the chat history?", "welcome.descriptions.part1": "", "welcome.descriptions.separator": "", "welcome.descriptions.part2": "to Chat", "welcome.login.text": "<PERSON><PERSON>", "welcome.loginWithLicense.text": "Login with License", "promotion.text.prefix": "type", "promotion.text": "to refer Codebase or Docs", "promotion.action.text": "Try", "guide.welcome.text": "", "guide.instruction.text": "You can ask me to generate code, answer questions related to the local codebase, or any other coding questions. Come and try to ask me:", "guide.change.text": "Change it up", "survey.title": "Rate for {{brand}}:", "survey.input.placeholder": "Enter your feedback (optional)", "brand.thinking": "is thinking, please wait patiently...", "common.close.text": "Close", "common.submitting.text": "Submitting...", "common.submit.text": "Submit", "common.delete.text": "Delete", "common.ok": "OK", "common.cancel": "Cancel", "common.refer": "Refer to", "common.search": "Search", "common.plan": "Plan", "common.reasoning": "Reasoning", "common.analyze": "Analyze", "common.thought": "Thought", "common.code.generate": "Code Generate", "common.answer": "Answer", "common.related": "Related", "common.selected.text": "Selected", "common.expand.text": "Expand", "common.collapse.text": "Collapse", "common.agent.name": "agent", "common.command.name": "command", "common.context.name": "context", "chatBox.generation.stop.text": "Generation stopped.", "chatBox.avatar.copy.text": "<PERSON><PERSON> Log", "suggestion.help.text1": "/help What can I do for you?", "suggestion.help.text2": "/help What can you do?", "suggestion.autowork.docstring": "Comments unsatisfactory? Click to call AutoWork for re-generation by context", "suggestion.autowork.comment": "Detailed explanation required? Activate AutoWork for comments based on whole file", "suggestion.autowork.explain": "Seeking deeper understanding? Click to activate AutoWork for in-depth code analysis", "suggestion.autowork.optimize": "Tuning from architecture? C<PERSON> to call AutoWork for tuning based on dependence relations", "suggestion.autowork.split": "Splitting not ideal? <PERSON>lick to call AutoWork for re-splitting from architecture", "suggestion.autowork.unitTest": "Unit test against expectation? Click to call AutoWork for re-generation based on whole file", "suggestion.autowork.authorize": "Generating more accurate content with current codebase", "suggestion.generate.ut.text": "Generate Unit Test for selected code", "suggestion.generate.doc.text": "Generate Docstring for selected code", "suggestion.ut.setting.text": "Customize test generation settings", "stopGeneration.button.text": "Stop generating", "suggestion.generate.ut.prompt": "Generate unit tests for the following code:", "suggestion.generate.doc.prompt": "Generate docstring for the following code:", "input.legacy.placeholder": "Please enter the description to generate code, you can press {{metaKey}} + Enter to break line", "input.notBound.placeholder": "Please press En<PERSON> to contact us as we can't get user information with current License", "input.default.placeholder": "Type / for commands, @ for plugin or # for docs", "input.command.placeholder": "Type / for commands", "input.code.placeholder": "Select code, and press Enter or click Send", "input.clear.placeholder": "Press Enter or click to clear the Chat", "input.help.placeholder": "Press Enter or click Send to view Help", "input.common.placeholder": "Ask anything", "input.autoTest.placeholder": "Use # API to associate API documentation or select code as a reference for generation of automated use case", "input.iapi.internal.placeholder": "Please enter the generation requirement, or use # API to associate API documentation, or select code as a reference for generation", "input.iapi.saas.placeholder": "Please enter the generation requirement, or use # API to associate API documentation, or select code as a reference for generation", "input.ut.placeholder": "Generate unit tests for selected code or diff code.", "input.stopGeneration.mac.placeholder": "Press Ctrl+C to terminate generation.", "input.stopGeneration.win.placeholder": "Press Ctrl+C to terminate generation.", "input.fileContext.tag.current": "Current", "action.regenerate.text": "Regenerate", "action.promptTemplate.create.text": "Save Prompt Template", "action.feedback.text": "", "action.regenerate.index.progress.text": "Comate is updating embeddings,which will enhance the quality of the generation", "action.regenerate.index.text": "Update search index for current codebase", "action.copyAll.text": "Copy all text", "markdown.code.action.suffix": "", "markdown.code.action.diff.text": "Diff", "markdown.code.action.accept.text": "Accept", "markdown.code.action.reject.text": "Reject", "markdown.code.action.copy.text": "Copy", "markdown.code.action.newFile.text": "Add File", "markdown.code.action.insertIntoTerminal.text": "Insert to Terminal", "markdown.code.action.runInTerminal.text": "Run", "markdown.code.action.replaceToFile.text": "Accept", "markdown.code.action.insertToFile.text": "Insert", "markdown.code.action.showFileInsertDiff.text": "Diff", "markdown.code.action.showFileReplaceDiff.text": "Diff", "markdown.code.action.viewFile.text": "ViewFile", "markdown.code.action.smartApply.text": "Apply", "markdown.code.action.reApply.text": "ReApply", "markdown.code.action.processing.text": "Processing", "feedback.popover.title": "<PERSON><PERSON><PERSON>", "feedback.popover.incomplete.text": "Content incomplete", "feedback.popover.incorrectFormat.text": "Wrong format", "feedback.popover.irrelevantAnswer.text": "Answer irrelevant ", "feedback.popover.noHelp.text": "No help", "feedback.popover.input.placeholder": "Welcome to feedback", "agent.comate.description": "Official plugin, supports function comments, interline comments and code explanation", "agent.comate.command.docstring": "docstring", "agent.comate.command.apiDoc": "api doc", "agent.comate.command.comment": "comment", "agent.comate.command.log": "logging", "agent.comate.command.explain": "explain", "agent.comate.command.optimize": "optimize", "agent.comate.command.split": "split", "agent.comate.command.clear": "clear", "agent.comate.command.ut": "unit test", "agent.comate.command.explainAndFix": "Analyze and fix", "agent.autowork.description": "自主理解需求、拆解任务，实现完整编程任务", "agent.autowork.command.ask": "Ask", "agent.autowork.command.autoTest": "Automated Test", "agent.autowork.refer.editor": "Current file", "agent.autowork.refer.terminal": "Terminal", "agent.autowork.refer.codebase": "Codebase", "agent.autowork.refer.currentCodebase": "Current codebase", "agent.autowork.refer.codebase.disabledReason": "no repository opened", "agent.autowork.refer.web": "Web", "agent.autowork.refer.web.placeholder": "Enter your URL and press Enter to submit, or press Esc to close", "agent.autowork.refer.folder": "Folders", "agent.autowork.refer.file": "Files", "agent.autowork.refer.file.disabledReason": "no file opened", "agent.autowork.refer.webSearch": "Web Search", "agent.autowork.refer.webSearch.disabledReason": "Selected in this conversation", "agent.autowork.refer.api.disabledReason": "Selected in this conversation", "agent.autowork.refer.knowledge.disabledReason": "Hybrid User please use VSCode to set up Knowledge set", "agent.autowork.refer.docs": "Docs", "agent.autowork.refer.addDoc": "Add new doc", "agent.autowork.refer.link.open": "Open", "agent.autowork.refer.link.unlink": "Unlink", "agent.autowork.refer.more.files": "and {{count}} more files", "agent.autowork.refer.switch.activeHelp": "", "agent.autowork.refer.switch.inactiveHelp": "Enable Comate to automatically add codebase for enhanced answers.", "agent.autowork.trial.ernieBot": "document to generate Python code for calling ERNIE Bot 4.0 with streaming output", "agent.autowork.trial.codeLogic": "", "agent.autowork.trial.explainCurrent": "explain the architecture of current", "agent.autowork.trial.architecture": "", "agent.autowork.moreReferences": "More References ({{count}})", "agent.promptTemplate.description": "Prompt Template", "knowledge.item.iapi.description": "Based on API, generate invocation code, Mocks, automated tests, etc.", "knowledge.item.iapi.search.doc.description": "What you entered can be used to retrieve APIs after selection.", "form.error.description.format": "Invalid {{property}} format", "message.copy.success": "<PERSON><PERSON>y", "hint.security.enhanced": "This response has been enhanced for security by Comate", "base.header.chat": "Cha<PERSON>", "base.header.pair": "Pair", "base.header.profile": "Profile", "base.header.logout": "Logout", "answer.generate.step1": "The context is being analyzed...", "answer.generate.step2": "I'm generating an answer for you...", "agent.task.secubot": "SecuBot", "agent.task.debugBot": "DebugBot", "agent.task.testBot": "TestBot", "agent.task.e2eBot": "E2EBot", "agent.task.status.running": "Running", "agent.task.status.completed": "Completed", "agent.task.status.failed": "Failed"}