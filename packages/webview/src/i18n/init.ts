import i18next from 'i18next';
import {initReactI18next} from 'react-i18next';
import enTranslation from './en/translation.json';
import zhTranslation from './zh/translation.json';
import {DEFAULT_LANGUAGE} from './config';

export type TranslationKey = keyof typeof enTranslation & keyof typeof zhTranslation;

i18next.use(initReactI18next).init({
    lng: DEFAULT_LANGUAGE,
    resources: {
        en: {
            translation: enTranslation,
        },
        zh: {
            translation: zhTranslation,
        },
    },
    fallbackLng: DEFAULT_LANGUAGE,
});
