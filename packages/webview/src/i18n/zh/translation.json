{"clearModal.title": "会话清空后无法恢复!", "clearModal.description": "确定要清空会话吗?", "welcome.descriptions.part1": "记住快捷键", "welcome.descriptions.separator": "，", "welcome.descriptions.part2": "快速唤起对话向我提问", "welcome.login.text": "登录", "welcome.loginWithLicense.text": "企业用户快速登录", "promotion.text.prefix": "", "promotion.text": "本地代码或知识集，体验 AutoWork 生成", "promotion.action.text": "去试试", "guide.welcome.text": "Hi，我是文心快码（Baidu Comate），您的智能代码助手", "guide.instruction.text": "你可以随时让我生成代码、解答本地代码库相关问题或其他编码问题。快来试着问我：", "guide.change.text": "换一换", "survey.title": "您对文心快码（{{brand}}）的满意度打分:", "survey.input.placeholder": "请输入您的反馈意见(选填)", "brand.thinking": "正在思考中，请耐心等候...", "common.close.text": "关闭", "common.submitting.text": "提交中...", "common.submit.text": "提交", "common.delete.text": "删除", "common.ok": "确定", "common.cancel": "取消", "common.refer": "参考", "common.explain": "解释", "common.search": "检索", "common.plan": "计划", "common.reasoning": "推理", "common.analyze": "分析", "common.thought": "思考", "common.code.generate": "代码生成", "common.answer": "生成", "common.related": "关联", "common.selected.text": "已选", "common.expand.text": "展开", "common.collapse.text": "收起", "common.agent.name": "插件", "common.command.name": "指令", "common.context.name": "知识", "chatBox.generation.stop.text": "已停止生成", "chatBox.avatar.copy.text": "复制相关日志", "suggestion.help.text1": "/help 你能做些什么呢？", "suggestion.help.text2": "/help 你有哪些能力？", "suggestion.autowork.docstring": "生成的注释不满意？点我唤起 AutoWork 引用上下文重新生成", "suggestion.autowork.comment": "需更细致解释？激活 AutoWork 基于整个文件生成注释", "suggestion.autowork.explain": "寻求更深入的理解？点我唤起 AutoWork 深挖代码逻辑", "suggestion.autowork.optimize": "希望从架构视角调优？点我唤起 AutoWork 基于依赖关系调优", "suggestion.autowork.split": "拆分的不理想？点我唤起 AutoWork 从架构视角重新拆分", "suggestion.autowork.unitTest": "单测不符合预期？点我唤起 AutoWork 参考整个文件重新生成", "suggestion.autowork.authorize": "基于当前代码库内容生成更精准内容", "suggestion.generate.ut.text": "为选中的代码生成单测", "suggestion.generate.doc.text": "为选中的代码生成注释", "suggestion.ut.setting.text": "修改生成单测的配置", "suggestion.generate.ut.prompt": "为下面这段代码生成单测：", "suggestion.generate.doc.prompt": "为下面这段代码生成注释：", "stopGeneration.button.text": "停止生成", "input.legacy.placeholder": "请输入描述来生成代码，可通过 {{metaKey}} + Enter 换行", "input.notBound.placeholder": "当前用户未与 License 绑定，按回车键联系我们", "input.default.placeholder": "使用 / 调起快捷指令、@ 调起插件或 # 引用知识", "input.command.placeholder": "输入 / 调起快捷指令", "input.code.placeholder": "框选代码后回车或点击发送", "input.clear.placeholder": "回车或点击发送清空对话", "input.help.placeholder": "回车或点击发送查看帮助信息", "input.common.placeholder": "请输入你的问题", "input.autoTest.placeholder": "使用 # API 关联接口文档，可框选代码作为自动化用例生成参考", "input.iapi.internal.placeholder": "请输入生成需求，可使用 # API 关联接口文档，可框选代码作为生成参考", "input.iapi.saas.placeholder": "请输入生成需求，可使用 # API 关联接口文档，可框选代码作为生成参考", "input.ut.placeholder": "为框选代码或变更代码生成单测", "input.stopGeneration.mac.placeholder": "Ctrl+C 停止生成", "input.stopGeneration.win.placeholder": "Ctrl+C 停止生成", "input.fileContext.tag.current": "当前", "action.regenerate.text": "重新生成", "action.promptTemplate.create.text": "保存自定义Prompt", "action.feedback.text": "反馈", "action.regenerate.index.progress.text": "Comate 正在为当前代码库更新索引，完成后可增强生成质量", "action.regenerate.index.text": "为当前代码库更新索引", "action.copyAll.text": "全文复制", "markdown.code.action.suffix": "成功", "markdown.code.action.diff.text": "查看变更", "markdown.code.action.accept.text": "采纳", "markdown.code.action.reject.text": "放弃", "markdown.code.action.copy.text": "复制", "markdown.code.action.newFile.text": "新建文件", "markdown.code.action.insertIntoTerminal.text": "插入到终端中", "markdown.code.action.runInTerminal.text": "运行", "markdown.code.action.replaceToFile.text": "采纳", "markdown.code.action.insertToFile.text": "插入", "markdown.code.action.showFileInsertDiff.text": "查看变更", "markdown.code.action.showFileReplaceDiff.text": "查看变更", "markdown.code.action.viewFile.text": "查看文件", "markdown.code.action.smartApply.text": "采纳", "markdown.code.action.reApply.text": "重新生成", "markdown.code.action.processing.text": "执行中", "feedback.popover.title": "意见反馈", "feedback.popover.incomplete.text": "内容不全", "feedback.popover.incorrectFormat.text": "格式不对", "feedback.popover.irrelevantAnswer.text": "答非所问", "feedback.popover.noHelp.text": "没有帮助", "feedback.popover.input.placeholder": "欢迎表扬、意见和建议", "agent.comate.description": "官方能力插件，支持函数注释、行间注释、代码解释", "agent.comate.command.docstring": "函数注释", "agent.comate.command.apiDoc": "接口文档", "agent.comate.command.comment": "行间注释", "agent.comate.command.log": "添加日志", "agent.comate.command.explain": "代码解释", "agent.comate.command.optimize": "调优建议", "agent.comate.command.split": "函数拆分", "agent.comate.command.clear": "清空对话框", "agent.comate.command.explainAndFix": "分析与修复", "agent.comate.command.ut": "生成单测", "agent.autowork.description": "自主理解需求、拆解任务，实现完整编程任务", "agent.autowork.command.ask": "Ask", "agent.autowork.command.autoTest": "自动化测试", "agent.autowork.refer.editor": "当前文件", "agent.autowork.refer.terminal": "终端", "agent.autowork.refer.codebase": "代码库", "agent.autowork.refer.currentCodebase": "当前代码库", "agent.autowork.refer.codebase.disabledReason": "未打开代码库", "agent.autowork.refer.web": "网页", "agent.autowork.refer.web.placeholder": "请输入URL，按 Enter 提交，按 Esc 关闭", "agent.autowork.refer.folder": "目录", "agent.autowork.refer.file": "文件", "agent.autowork.refer.file.disabledReason": "未打开文件", "agent.autowork.refer.webSearch": "网络检索", "agent.autowork.refer.webSearch.disabledReason": "当次对话已选择", "agent.autowork.refer.api.disabledReason": "当次对话已选择", "agent.autowork.refer.knowledge.disabledReason": "混合云用户请前往VSCode登录本账号并配置知识集", "agent.autowork.refer.docs": "知识集", "agent.autowork.refer.addDoc": "新增知识集", "agent.autowork.refer.link.open": "访问链接", "agent.autowork.refer.link.unlink": "解除绑定", "agent.autowork.refer.more.files": "和 {{count}} 个更多的文件", "agent.autowork.refer.switch.activeHelp": "", "agent.autowork.refer.switch.inactiveHelp": "开启后，自动将全库代码作为上下文增强回答质量", "agent.autowork.trial.ernieBot": "文档，使用 Python 生成调用 ERNIE Bot 4.0 的代码并流式输出", "agent.autowork.trial.codeLogic": "的代码逻辑", "agent.autowork.trial.explainCurrent": "解析当前", "agent.autowork.trial.architecture": "的架构", "agent.autowork.moreReferences": "更多引用（{{count}}）", "agent.promptTemplate.description": "自定义Prompt", "knowledge.item.iapi.description": "基于 API 文档生成调用代码、<PERSON><PERSON>、自动化测试等", "knowledge.item.iapi.search.doc.description": "（你输入的文本，选择后可用于检索API）", "form.error.description.format": "{{property}} 格式不正确", "message.copy.success": "复制成功", "hint.security.enhanced": "当前生成内容已经过安全增强", "base.header.chat": "对话", "base.header.pair": "助理", "base.header.profile": "个人中心", "base.header.logout": "退出登录", "answer.generate.step1": "正在分析上下文...", "answer.generate.step2": "正在为您生成回答...", "agent.task.secubot": "安全修复", "agent.task.debugBot": "代码debug", "agent.task.testBot": "生成单测", "agent.task.e2eBot": "全栈编程", "agent.task.status.running": "执行中", "agent.task.status.completed": "已完成", "agent.task.status.failed": "失败"}