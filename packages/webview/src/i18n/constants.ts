// 清空对话框
export const MODAL_CLEAR_TITLE = 'clearModal.title';
export const MODAL__CLEAR_DESCRIPTION = 'clearModal.description';

// welcome
export const WELCOME_DESCRIPTIONS_PART1 = 'welcome.descriptions.part1';
export const WELCOME_DESCRIPTIONS_SEPARATOR = 'welcome.descriptions.separator';
export const WELCOME_DESCRIPTIONS_PART2 = 'welcome.descriptions.part2';
export const WELCOME_LOGIN_TEXT = 'welcome.login.text';
export const WELCOME_LOGIN_WITH_LICENSE = 'welcome.loginWithLicense.text';

// promotion
export const PROMOTION_TEXT_PREFIX = 'promotion.text.prefix';
export const PROMOTION_TEXT = 'promotion.text';
export const PROMOTION_ACTION_TEXT = 'promotion.action.text';

// guide
export const GUIDE_WELCOME_TEXT = 'guide.welcome.text';
export const GUIDE_INSTRUCTION_TEXT = 'guide.instruction.text';
export const GUIDE_CHANGE_TEXT = 'guide.change.text';

// survey
export const SURVEY_TITLE = 'survey.title';
export const SURVEY_INPUT_PLACEHOLDER = 'survey.input.placeholder';

// 常用短语
export enum CommonText {
    CLOSE = 'common.close.text',
    SUBMITTING = 'common.submitting.text',
    SUBMIT = 'common.submit.text',
    OK = 'common.ok',
    CANCEL = 'common.cancel',
    SELECTED = 'common.selected.text',
    EXPAND = 'common.expand.text',
    COLLAPSE = 'common.collapse.text',
    DELETE = 'common.delete.text',
}

export enum ChatBoxText {
    GENERATION_STOP = 'chatBox.generation.stop.text',
    COPY_LOG = 'chatBox.avatar.copy.text',
}

// 会话引导
export const SUGGESTION_AUTOWORK_DOCSTRING = 'suggestion.autowork.docstring';
export const SUGGESTION_AUTOWORK_COMMENT = 'suggestion.autowork.comment';
export const SUGGESTION_AUTOWORK_EXPLAIN = 'suggestion.autowork.explain';
export const SUGGESTION_AUTOWORK_OPTIMIZE = 'suggestion.autowork.optimize';
export const SUGGESTION_AUTOWORK_SPLIT = 'suggestion.autowork.split';
export const SUGGESTION_AUTOWORK_UNITTEST = 'suggestion.autowork.unitTest';
export const SUGGESTION_AUTOWORK_AUTHORIZE = 'suggestion.autowork.authorize';

export enum SuggestionText {
    HELP1 = 'suggestion.help.text1',
    HELP2 = 'suggestion.help.text2',
    AUTOWORK_DOCSTRING = 'suggestion.autowork.docstring',
    AUTOWORK_COMMENT = 'suggestion.autowork.comment',
    AUTOWORK_EXPLAIN = 'suggestion.autowork.explain',
    AUTOWORK_OPTIMIZE = 'suggestion.autowork.optimize',
    AUTOWORK_SPLIT = 'suggestion.autowork.split',
    AUTOWORK_UNITTEST = 'suggestion.autowork.unitTest',
    AUTOWORK_AUTHORIZE = 'suggestion.autowork.authorize',
    GENERATE_UT = 'suggestion.generate.ut.text',
    GENERATE_DOC = 'suggestion.generate.doc.text',
    UT_SETTING = 'suggestion.ut.setting.text',
    UT_PROMPT = 'suggestion.generate.ut.prompt',
    DOC_PROMPT = 'suggestion.generate.doc.prompt',
}

// 停止生成
export const STOP_GENERATION_TEXT = 'stopGeneration.button.text';

// 输入框
export const INPUT_LEGACY_PLACEHOLDER = 'input.legacy.placeholder';
export const INPUT_NOTBOUND_PLACEHOLDER = 'input.notBound.placeholder';
export const INPUT_DEFAULT_PLACEHOLDER = 'input.default.placeholder';
export const INPUT_COMMAND_PLACEHOLDER = 'input.command.placeholder';
export const INPUT_CODE_PLACEHOLDER = 'input.code.placeholder';
export const INPUT_CLEAR_PLACEHOLDER = 'input.clear.placeholder';
export const INPUT_HELP_PLACEHOLDER = 'input.help.placeholder';
export const INPUT_COMMON_PLACEHOLDER = 'input.common.placeholder';
export const INPUT_AUTO_TEST_PLACEHOLDER = 'input.autoTest.placeholder';
export const INPUT_IAPI_INTERNAL_PLACEHOLDER = 'input.iapi.internal.placeholder';
export const INPUT_IAPI_SAAS_PLACEHOLDER = 'input.iapi.saas.placeholder';
export const INPUT_UT_PLACEHOLDER = 'input.ut.placeholder';
export const INPUT_STOP_GENERATION_WIN_PLACEHOLDER = 'input.stopGeneration.win.placeholder';
export const INPUT_STOP_GENERATION_MAC_PLACEHOLDER = 'input.stopGeneration.mac.placeholder';
export enum FileContextText {
    TAG_CURRENT = 'input.fileContext.tag.current',
}
// 操作按钮
export const ACTION_REGENERATE_TEXT = 'action.regenerate.text';
export const ACTION_FEEDBACK_TEXT = 'action.feedback.text';
export const ACTION_REGENERATE_INDEX_PROGRESS = 'action.regenerate.index.progress.text';
export const ACTION_REGENERATE_INDEX = 'action.regenerate.index.text';
export const ACTION_COPY_ALL_TEXT = 'action.copyAll.text';
export const ACTION_CREATE_PROMPTTEMPLATE_TEXT = 'action.promptTemplate.create.text';

// markdown 相关
export const MARKDOWN_CODE_ACTION_SUFFIX = 'markdown.code.action.suffix';
export const MARKDOWN_CODE_ACTION_DIFF = 'markdown.code.action.diff.text';
export const MARKDOWN_CODE_ACTION_ACCEPT = 'markdown.code.action.accept.text';
export const MARKDOWN_CODE_ACTION_REJECT = 'markdown.code.action.reject.text';
export const MARKDOWN_CODE_ACTION_COPY = 'markdown.code.action.copy.text';
export const MARKDOWN_CODE_ACTION_NEWFILE = 'markdown.code.action.newFile.text';
export const MARKDOWN_CODE_ACTION_INSERTINTOTERMINAL = 'markdown.code.action.insertIntoTerminal.text';
export const MARKDOWN_CODE_ACTION_RUNINTERMINAL = 'markdown.code.action.runInTerminal.text';
export const MARKDOWN_CODE_ACTION_REPLACETOFILE = 'markdown.code.action.replaceToFile.text';
export const MARKDOWN_CODE_ACTION_INSERTTOFILE = 'markdown.code.action.insertToFile.text';
export const MARKDOWN_CODE_ACTION_SHOWFILEINSERTDIFF = 'markdown.code.action.showFileInsertDiff.text';
export const MARKDOWN_CODE_ACTION_SHOWFILEREPLACEDIFF = 'markdown.code.action.showFileReplaceDiff.text';
export const MARKDOWN_CODE_ACTION_VIEWFILE = 'markdown.code.action.viewFile.text';
export const MARKDOWN_CODE_ACTION_SMARTAPPLY = 'markdown.code.action.smartApply.text';
export const MARKDOWN_CODE_ACTION_REAPPLY = 'markdown.code.action.reApply.text';
export const MARKDOWN_CODE_ACTION_PROCESSING = 'markdown.code.action.processing.text';

// 反馈
export const FEEDBACK_POPOVER_TITLE = 'feedback.popover.title';
export const FEEDBACK_POPOVER_INCOMPLETE = 'feedback.popover.incomplete.text';
export const FEEDBACK_POPOVER_INCORRECT = 'feedback.popover.incorrectFormat.text';
export const FEEDBACK_POPOVER_IRRELEVANT = 'feedback.popover.irrelevantAnswer.text';
export const FEEDBACK_POPOVER_NOHELP = 'feedback.popover.noHelp.text';
export const FEEDBACK_POPOVER_INPUT_PLACEHOLDER = 'feedback.popover.input.placeholder';

// agent comate
export const AGENT_COMATE_DESCRIPTION = 'agent.comate.description';
export const AGENT_COMATE_COMMAND_DOCSTRING = 'agent.comate.command.docstring';
export const AGENT_COMATE_COMMAND_COMMENT = 'agent.comate.command.comment';
export const AGENT_COMATE_COMMAND_LOG = 'agent.comate.command.log';
export const AGENT_COMATE_COMMAND_EXPLAIN = 'agent.comate.command.explain';
export const AGENT_COMATE_COMMAND_OPTIMIZE = 'agent.comate.command.optimize';
export const AGENT_COMATE_COMMAND_SPLIT = 'agent.comate.command.split';
export const AGENT_COMATE_COMMAND_CLEAR = 'agent.comate.command.clear';

// agent autowork
export const AGENT_AUTOWORK_DESCRIPTION = 'agent.autowork.description';
export const AGENT_AUTOWORK_COMMAND_ASK = 'agent.autowork.command.ask';
export const AGENT_AUTOWORK_COMMAND_AUTO_TEST = 'agent.autowork.command.autoTest';

export const AGENT_AUTOWORK_REFER_EDITOR = 'agent.autowork.refer.editor';
export const AGENT_AUTOWORK_REFER_TERMINAL = 'agent.autowork.refer.terminal';
export const AGENT_AUTOWORK_REFER_CODEBASE = 'agent.autowork.refer.codebase';
export const AGENT_AUTOWORK_REFER_CURRENT_CODEBASE = 'agent.autowork.refer.currentCodebase';
export const AGENT_AUTOWORK_REFER_CODEBASE_DISABLE_REASON = 'agent.autowork.refer.codebase.disabledReason';
export const AGENT_AUTOWORK_REFER_WEB = 'agent.autowork.refer.web';
export const AGENT_AUTOWORK_REFER_WEB_PLACEHOLDER = 'agent.autowork.refer.web.placeholder';
export const AGENT_AUTOWORK_REFER_FOLDER = 'agent.autowork.refer.folder';
export const AGENT_AUTOWORK_REFER_FILE = 'agent.autowork.refer.file';
export const AGENT_AUTOWORK_REFER_FILE_DISABLE_REASON = 'agent.autowork.refer.file.disabledReason';
export const AGENT_AUTOWORK_REFER_DOCS = 'agent.autowork.refer.docs';
export const AGENT_AUTOWORK_REFER_ADD = 'agent.autowork.refer.addDoc';
export const AGENT_AUTOWORK_REFER_SWITCH_ACTIVE_HELP = 'agent.autowork.refer.switch.activeHelp';
export const AGENT_AUTOWORK_REFER_SWITCH_INACTIVE_HELP = 'agent.autowork.refer.switch.inactiveHelp';
export const AGENT_AUTOWORK_REFER_WEB_SEARCH = 'agent.autowork.refer.webSearch';
export const AGENT_AUTOWORK_REFER_WEB_SEARCH_DISABLE_REASON = 'agent.autowork.refer.webSearch.disabledReason';
export const AGENT_AUTOWORK_REFER_API_DISABLE_REASON = 'agent.autowork.refer.api.disabledReason';
export const AGENT_AUTOWORK_REFER_KNOWLEDGE_DISABLE_REASON = 'agent.autowork.refer.knowledge.disabledReason';
export const AGENT_AUTOWORK_MORE_REFERENCES = 'agent.autowork.moreReferences';

// 知识类型相关
export const KNOWLEDGE_ITEM_IAPI_DESCRIPTION = 'knowledge.item.iapi.description';
export const KNOWLEDGE_ITEM_IAPI_SEARCH_DOC_DESCRIPTION = 'knowledge.item.iapi.search.doc.description';

// form
export const FORM_ERROR_DESCRIPTION_FORMAT = 'form.error.description.format';

// 日志
export const LOG_COPY_DETAIL = 'log.copy.detail';

// 消息提示
export const MESSAGE_COPY_SUCCESS = 'message.copy.success';

// hint, tooltip 类文案
export const HINT_SECURITY_CHECKED = 'hint.security.enhanced';

// header 部分，基础信息
export const BASE_HEADER_CHAT = 'base.header.chat';
export const BASE_HEADER_PAIR = 'base.header.pair';
export const BASE_HEADER_PROFILE = 'base.header.profile';
export const BASE_HEADER_LOGOUT = 'base.header.logout';

export const ANSWER_GENERATE_STEP1 = 'answer.generate.step1';
export const ANSWER_GENERATE_STEP2 = 'answer.generate.step2';

// agent task
export const AGENT_TASK_SECUBOT = 'agent.task.secubot';
export const AGENT_TASK_DEBUGBOT = 'agent.task.debugBot';
export const AGENT_TASK_TESTBOT = 'agent.task.testBot';
export const AGENT_TASK_E2EBOT = 'agent.task.e2eBot';

// agent task status
export const AGENT_TASK_STATUS_RUNNING = 'agent.task.status.running';
export const AGENT_TASK_STATUS_FAILED = 'agent.task.status.failed';
export const AGENT_TASK_STATUS_COMPLETED = 'agent.task.status.completed';
