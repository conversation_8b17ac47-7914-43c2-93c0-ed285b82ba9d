/* eslint-disable no-console */
interface MessagePayload {
    event: string;
    data: any;
    id: number;
}

declare global {
    interface Window {
        executeXcodeCallbackFunction: (data: MessageEvent<MessagePayload>) => void;
        executeJSFunction: (data: MessageEvent<MessagePayload>) => void;
        webkit?: {
            messageHandlers?: {
                xcodeHandler?: {
                    postMessage?: (data: any) => void;
                };
            };
        };
    }
}

type Handler = (data: any) => any;

let id = 0;
const resolveMap = {};

class XcodeMessageHandler {
    private readonly listeners = new Map<string, Handler>();

    constructor() {
        // webview 向 ide 发送消息，ide 使用这个方法回调
        window.executeXcodeCallbackFunction = (e: MessageEvent<MessagePayload>) => {
            const payload = e.data;
            resolveMap?.[e.data.id]?.(e.data.data);
            console.log('Webview->Extension:', payload);
        };
        // ide 向 webview 发消息，结果直接在这个函数里返回
        window.executeJSFunction = async (e: MessageEvent<MessagePayload>) => {
            const payload = e.data;
            console.log('Extension->Webview:', payload);
            const handler = this.listeners.get(payload.event);
            if (handler) {
                const res = await handler(payload.data);
                return res;
            }
        };
    }

    send<O>(event: string, data?: any): Promise<O> | undefined {
        const postMessage = window?.webkit?.messageHandlers?.xcodeHandler?.postMessage;
        if (postMessage) {
            const payload = {event, data, id: ++id};
            return new Promise(resolve => {
                resolveMap[payload.id] = resolve;
                // @ts-ignore
                window.webkit.messageHandlers.xcodeHandler.postMessage(payload);
            });
        }
        else {
            console.error(
                'window.webkit.messageHandlers.xcodeHandler.postMessage is missing, so webview will not work!'
            );
        }
    }

    listen(event: string, callback: (data: any) => void): void {
        this.listeners.set(event, callback);
    }
}

export const messageHandler = new XcodeMessageHandler();
