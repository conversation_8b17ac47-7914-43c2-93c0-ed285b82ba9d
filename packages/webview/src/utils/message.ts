import {EventMessage} from '@shared/protocols';
import {messageHandler} from './messageHandler';

/**
 * 通用的在webview控制编辑器，弹出右下角消息的方法
 */
export const toastMessage = (() => {
    const toast = (type: 'info' | 'error', message: string) => {
        messageHandler.send(EventMessage.IdeToastMessageEvent, {type, message});
    };
    return {
        error: message => toast('error', message),
        info: message => toast('info', message),
    };
})();
