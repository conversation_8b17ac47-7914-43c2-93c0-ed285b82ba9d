export const retryAsyncFunc = async <T>(
    asyncFunc: () => Promise<T>,
    retryInterval: number,
    totalRetryTime: number,
    defaultValue: T
): Promise<T> => {

    const retryLimit = Math.floor(totalRetryTime / retryInterval);

    const tryFunc = async (retryCount = 0): Promise<T> => {
        if (retryCount > retryLimit) {
            return defaultValue;
        }

        try {
            return await asyncFunc();
        }
        catch (ex) {
            return new Promise(resolve => {
                setTimeout(() => {
                    resolve(tryFunc(retryCount + 1));
                }, retryInterval);
            });
        }
    };

    return tryFunc();
};
