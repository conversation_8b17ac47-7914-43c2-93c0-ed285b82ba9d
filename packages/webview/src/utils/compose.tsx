import {ComponentType} from 'preact/compat';

export const compose = (components: ComponentType[]) => {
    return <P extends any>(Component: ComponentType<P>) => {
        return function ComposedComponent(props) {
            return components.reduceRight(
                (acc, Current) => {
                    return <Current>{acc}</Current>;
                },
                <Component {...props} />
            );
        };
    };
};
