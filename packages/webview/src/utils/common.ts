export function findLast<T>(list: T[], predicate: (item: T) => boolean) {
    let target: T | undefined = undefined;
    for (const item of list) {
        if (predicate(item)) {
            target = item;
        }
    }
    return target;
}

export async function safePromise<T>(promise: Promise<T> | undefined) {
    if (!promise) {
        return undefined;
    }
    try {
        return await promise;
    }
    catch {
        return undefined;
    }
}
