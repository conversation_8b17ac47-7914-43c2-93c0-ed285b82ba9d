import {DehydratedMessage, EventMessage} from '@shared/protocols';
import {messageHandler} from './messageHandler';

export const actionsToEventHandler = (
    messageId: DehydratedMessage['id'],
    actions: DehydratedMessage['actions']
): Record<string, () => void> => {
    if (Array.isArray(actions) && actions.length) {
        const events = {};
        for (const name of (actions ?? [])) {
            events[name] = async (content: string, language: string, extra: any) => {
                const res = await messageHandler.send(EventMessage.MessageActionTriggerEvent, {
                    messageId,
                    action: name,
                    content,
                    language,
                    extra,
                });
                if (res?.status === 'failed') {
                    throw new Error('execute code action failed');
                }
            };
        }
        return events;
    }
    return {};
};
