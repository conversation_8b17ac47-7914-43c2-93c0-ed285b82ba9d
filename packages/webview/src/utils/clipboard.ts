export function copyToClipboard(text: string) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text);
    }
    else {
        // 兼容方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        // 使textArea不可见
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            textArea.remove();
        }
        catch (err) {
            textArea.remove();
        }
    }
}
