/* eslint-disable no-console */
declare global {
    interface Window {
        messageRouter: {
            sendDataToCsharp: (data: Record<string, unknown>) => void;
        };
    }
}

type Handler = (data: any) => any;

interface MessagePayload {
    event: string;
    data: any;
}

class JetBrainsMessageHandler {
    private readonly listeners = new Map<string, Handler>();

    constructor() {
        window.addEventListener('message', (e: MessageEvent<MessagePayload>) => {
            const payload = e.data;
            console.log('Extension->Webview(' + payload.event + '):', payload);
            const handler = this.listeners.get(payload.event);
            handler && handler(payload.data);
        }, false);
    }

    send<O>(event: string, data?: any): Promise<O> | undefined {
        if (typeof window.messageRouter.sendDataToCsharp === 'undefined') {
            console.error('window.messageRouter.sendDataToCsharp is missing, so webview will not work!');
        }
        else {
            const payload = {event, data};
            return new Promise((resolve, reject) => {
                // @ts-ignore
                window.messageRouter.sendDataToCsharp({
                    request: JSON.stringify(payload),
                    persistent: false,
                    onSuccess: (responseData: any) => {
                        try {
                            console.log('Extension->Webview(' + event + '-reply):', responseData);
                            const jsonData = JSON.parse(responseData);
                            resolve(jsonData);
                        }
                        catch (e) {
                            console.log('Extension->Webview(reply)(Error): failed to parse response data');
                            reject(e);
                        }
                    },
                    onFailure: (code: number, message: string) => {
                        console.error('sendDataToJava received the following error:', code, message);
                        reject(new Error(message));
                    },
                });
                console.log('Webview->Extension(' + event + '):', payload);
            });
        }
    }

    listen(event: string, callback: (data: any) => void): void {
        this.listeners.set(event, callback);
    }
}

export const messageHandler = new JetBrainsMessageHandler();
