import {FeatureName, isFeatureVisible} from '@shared/utils/features';
import {isVS} from '@/config';

/* eslint-disable max-len */
const consumer = $features.WEBVIEW_CONSUMER;
const isWin = navigator.platform?.includes('Win');

const windowsSVG =
    '<code><svg style="display:inline-block" viewBox="64 64 896 896" width="14px" focusable="false" data-icon="windows" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M523.8 191.4v288.9h382V128.1zm0 642.2l382 62.2v-352h-382zM120.1 480.2H443V201.9l-322.9 53.5zm0 290.4L443 823.2V543.8H120.1z"></path></svg></code>';

const getKeyCombination = () => {
    if (isVS) {
        return '<code>Ctrl</code> <code>Shift</code>';
    }

    if (isWin) {
        if (consumer === 'jetbrains') {
            return '<code>Ctrl</code> <code>⇧</code>';
        } else {
            return windowsSVG;
        }
    } else if (consumer === 'jetbrains') {
        return '<code>⌘</code> <code>⇧</code>';
    } else {
        return '<code>⌘</code>';
    }
};

const getEnHelp = (
    supportAt: boolean,
    isPoc: boolean
) => (`Hi, I'm Baidu Comate, your AI coding assistant. I can generate code for you and help solve coding issues.
**AI-powered Code Suggestion**
* In the editor, Comate provides AI-powered code suggestions. Simply press Tab to accept.
* When adding comments in the code editor, Comate provides code suggestions after a line break.
* Hover over the code where suggestions are displayed to view shortcuts and customize how they are shown.
* Keyboard shortcuts：
    * \`Tab\` Accept
    * \`${isWin ? 'Ctrl' : '⌘'}\` \`→\` Accept Word
    * \`${isWin ? 'Ctrl' : '⌘'}\` \`↓\` Accept Line


**Chat**
<ul>
    <li>You can open the chat interface in the sidebar (shortcut: ${getKeyCombination()} <code>Y</code>) to ask me programming-related questions.</li>
</ul>

**Plugins**
- Select code in editor and right-click to find Baidu Comate to help explain code, generate unit tests, optimize code, and etc.
${
    supportAt
        ? `- Type \`@\` to use plugins and then type \`/\` for commands (or simply \`/\`).
- Official plugins currently available are listed below:
    <details>
        <summary>@Comate</summary>
        <ul>
            <li>/docstring</li>
            <li>/comment</li>
            <li>/explain</li>
            <li>/optimize</li>
            <li>/split</li>
            <li>/clear</li>
            <li>/help</li>
        <ul>
    </details>`
        : ''
    }

 ${
    isPoc ? '' : 'Additionally, you can open [settings](//command:baidu.comate.openSettings) to customize this extension to your preferences. Click [Help](//command:baidu.comate.visitHelpDocs) to view more information.'}`);

// eslint-disable-next-line complexity
const getZhHelp = (
    supportAt: boolean,
    enableSecurity: boolean,
    isPoc: boolean
) => (`您好，我是文心快码（Baidu Comate），您的智能代码助手，我可以为您生成代码、解决编码问题。
**代码智能补全**
* 在编辑器中，Comate 会智能给出补全提示，按\`Tab\`键采纳。
* 在代码编辑区写注释，换行后会给出补全代码。
* 鼠标悬浮到推荐代码上可以查看快捷键，设置快捷键显示方式。
* 快捷键：
    * \`Tab\` 采纳
${consumer === 'vscode' && `    * \`${isWin ? 'Ctrl' : '⌘'}\` \`→\` 逐单词采纳`}
    * \`${isWin ? 'Ctrl' : '⌘'}\` \`↓\` 逐行采纳。


**编程知识问答**
<ul>
    <li>您可以在侧边栏打开对话框(快捷键：${getKeyCombination()} <code>Y</code>)，向我提问常规编程问题。</li>
</ul>
${
    (enableSecurity && !isVS)
        ? `**代码安全**
* 一键漏洞检测：通过输入 \`代码安全\` 指令，回车或点击发送即可发起全局检测，同时也支持指定目录检测，如 \`代码安全 src/test\`。
* 一键漏洞修复：通过点击漏洞列表中的 \`发起修复\` 即可尝试自动修复，点击 \`采纳\` 按钮可直接使用安全的修复代码替换存在漏洞的原始代码，实现漏洞一键修复。`
        : ''
    }


**专业插件**
- 选中代码并点击右键，选择\`Baidu Comate\`，可进行代码解释、单测生成、代码优化等专业领域的操作。
${
    supportAt
        ? `- 使用时先通过\`@\`唤起插件，再通过\`/\`唤起命令（或直接使用\`/\`唤起命令）。
- 目前官方提供的插件如下：
    <details>
        <summary>@Comate</summary>
        ${
    (enableSecurity && !isVS)
        ? `<ul>
                <li>/函数注释</li>
                <li>/行间注释</li>
                <li>/代码解释</li>
                <li>/调优建议</li>
                <li>/函数拆分</li>
                <li>/清空对话框</li>
                <li>/代码安全</li>
                <li>/help</li>
            <ul>`
        : `<ul>
                <li>/函数注释</li>
                <li>/行间注释</li>
                <li>/代码解释</li>
                <li>/调优建议</li>
                <li>/函数拆分</li>
                <li>/清空对话框</li>
                <li>/help</li>
            <ul>`
    }
    </details>`
        : ''
    }

    ${
    (!isPoc && `此外，您还可以进入[设置页](//command:baidu.comate.openSettings)，进行个性化设置。了解更多信息，点此查看[帮助文档](//command:baidu.comate.visitHelpDocs)。${
        isFeatureVisible(FeatureName.CHAT_PANEL_FEEDBACK)
            ? '如果有使用建议，[点此反馈](//command:baidu.comate.feedback)给我们。'
            : ''
    }`) || ''
    }`);

// eslint-disable-next-line complexity
export const getHelpResponse = ({
    supportAt,
    language = 'zh',
    enableSecurity,
    isPoc,
}: {
    supportAt: boolean;
    language?: 'zh' | 'en';
    enableSecurity: boolean;
    isPoc: boolean;
}) => {

    if (language === 'en') {
        return getEnHelp(supportAt, isPoc);
    }
    return getZhHelp(supportAt, enableSecurity, isPoc);
};
