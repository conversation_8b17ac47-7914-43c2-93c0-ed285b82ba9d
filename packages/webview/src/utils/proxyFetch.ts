import {
    EventMessage,
    UnityRequestType,
    ResponseSuccessType,
    HttpUnityResponseRawSuccessType,
    HttpUnityResponseSuccessType,
} from '@shared/protocols';
import {messageHandler} from './messageHandler';

export const proxyFetch = async <T>(request: UnityRequestType): Promise<ResponseSuccessType<T>> => {
    const response = await messageHandler.send(
        EventMessage.PassthroughRequest,
        {
            method: request.method,
            url: request.url,
            useBaseUrl: request.useBaseUrl,
            body: request.body,
            options: request.options || {},
            headers: request.headers || {},
            authorization: !!request.authorization,
        }
    );
    if ('error' in response) {
        throw response.error;
    }
    else if (request.rawData) {
        return response as HttpUnityResponseRawSuccessType<T>;
    }
    else {
        return response as HttpUnityResponseSuccessType<T>;
    }
};
