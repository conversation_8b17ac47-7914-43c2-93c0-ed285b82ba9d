/**
 * @file ExtensionMessageHandler
 * @description WebView 中与 Extension 的通信类
 */

import {EventMessageCallbackTypes} from '@shared/protocols';
import {vscode} from './acquireVsCodeApi';

/**
 * WebView uses this handler to send messages to our extension and receives
 * messages from the extension.
 */
class ExtensionMessageHandler {
    private readonly handlers: Map<string, MessageHandler> = new Map(); // TODO 不是标准的 eventemitter，需要改进，要能支持多个 listener
    private lastSentReq: number = 0;
    private readonly pendingReplies: Map<string, PendingReply> = new Map();

    constructor() {
        const messageHandler = (e: MessageEvent) => {
            const message: MessagePayload = e.data;
            this.handleMessage(message);
        };

        window.addEventListener('message', messageHandler);
    }

    /**
     * 向 extHost 发送消息，API 设计参考 fetch
     *
     * message.send('sendData', {
     *     random: Math.random()
     * }).then(res => {
     *     console.log(res);
     * });
     */
    send<K extends keyof EventMessageCallbackTypes>(
        scope: Exclude<K, number>,
        data?: Parameters<EventMessageCallbackTypes[K]>[0]
    ): Promise<ReturnType<EventMessageCallbackTypes[K]>> {
        const time = new Date();
        return new Promise((resolve, reject) => {
            // 从 webview 发给 extHost 的消息，都有一个 webview 的 label
            // extHost 给 webview 的响应中，保持这个 label
            // 这样的话，在 handleMessage 的时候，就可以根据这个 label 进行判断
            // 如果是 label 是 webview，说明是 extHost 发给 webview 的响应
            // 这段逻辑在下面会有体现
            const label = 'webview';
            const req = (++this.lastSentReq).toString();
            this.pendingReplies.set(req, {
                resolve,
                reject,
            });
            this.postMessage({
                label: 'webviewSendLog',
                req,
                scope,
                data: {...data, logTime: time},
            });
            this.postMessage({
                label,
                req,
                scope,
                data,
            });
        });
    }

    /**
     * 监听 extHost 消息，API 设计参考 express
     *
     * message.listen('receive', (data) => {
     *     const div = document.createElement('div');
     *     div.innerText = data.toString();
     *     document.body.appendChild(div);
     *     return {
     *         code: 0
     *     }
     * })
     */
    listen<K extends keyof EventMessageCallbackTypes>(
        scope: Exclude<K, number>,
        callback: EventMessageCallbackTypes[K]
    ): void {
        const time = new Date();
        this.handlers.set(scope, callback);
        this.postMessage({
            label: 'webviewListenLog',
            req: 'listen',
            scope,
            data: {logTime: time},
        });
    }

    private async postMessage(message: MessagePayload) {
        vscode.postMessage(message);
    }

    private async handleMessage(message: MessagePayload) {
        const {label, req, scope} = message;

        // 如果 extHost 发给 webview 的消息中 label 为 webview
        // 说明是消息是 webview 发出请求的响应
        if (label === 'webview') {
            // Received a reply from the extension.
            const reply = this.pendingReplies.get(req);
            if (!reply) {
                return;
            }
            reply.resolve(message.data);
            this.pendingReplies.delete(req);
        }
        // 否则，说明消息是来自 extHost 的请求
        // 需要 webview 进行处理
        else if (label === 'extension') {
            const handler = this.handlers.get(scope);

            if (handler === undefined) {
                return;
            }
            const data = await handler(message.data);
            this.postMessage({
                label,
                req,
                scope,
                data,
            });
        }
    }
}

// 对于 WebView 环境，仅对外暴露单例
export const messageHandler = new ExtensionMessageHandler();

type MessageHandler = (data: any) => Promise<any> | any;

interface MessagePayload {
    label: 'extension' | 'webview' | 'webviewSendLog' | 'webviewListenLog';
    req: string;
    scope: string;
    data: any;
}

interface PendingReply {
    resolve: (value?: any) => void;
    reject: (reason?: any) => void;
}
