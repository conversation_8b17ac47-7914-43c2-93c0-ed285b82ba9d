import {parseInteractiveUri} from '@shared/utils/parseInteractiveUri';
import {EventMessage} from '@shared/protocols';
import {messageHandler} from './messageHandler';

export default function navigateFromUri(uriString: string) {
    const component = parseInteractiveUri(uriString);
    messageHandler.send(EventMessage.QuerySendEvent, {
        prompt: component.query,
        type: 'default',
        agent: component.agent,
        slash: component.slash,
        messageOrder: {},
        supportAt: false,
    });
    return component;
}
