import raf, {cancel} from 'raf';

const MAX_DELAY_MS = 400;
const MIN_DELAY_MS = 8;

const THRESHOLD = 1600;

const MAX_CHAR_CHUNK_SIZE = 40;
const MIN_CHAR_CHUNK_SIZE = 1;

export interface Consumer {
    update: (content: string) => void;
}

export class Typewriter {
    private text = '';
    private currentIndex = 0;
    private loop: number | undefined;
    private lastTime: number = 0;
    private lastText = '';
    private readonly threshold: number = THRESHOLD;

    constructor(
        private readonly consumer: Consumer,
        defaultText: string,
        threshold?: number
    ) {
        this.text = defaultText;
        this.lastText = this.text;
        this.currentIndex = defaultText.length;
        this.threshold = threshold ?? THRESHOLD;
    }

    run() {
        const remainingSize = this.text.length - this.currentIndex;
        if (remainingSize <= 0 && this.lastText === this.text) {
            this.loop = undefined;
            return;
        }

        const delay = Math.min(MAX_DELAY_MS, Math.max(this.threshold / remainingSize, MIN_DELAY_MS));
        const delta = Date.now() - this.lastTime;
        if (delta < delay) {
            this.loop = raf(() => this.run());
            return;
        }
        this.lastTime = Date.now();
        const charChunkSize = remainingSize > MAX_CHAR_CHUNK_SIZE
            ? Math.round(remainingSize / MAX_CHAR_CHUNK_SIZE)
            : MIN_CHAR_CHUNK_SIZE;
        this.currentIndex = Math.min(this.text.length, this.currentIndex + charChunkSize);
        this.consumer.update(this.text.slice(0, this.currentIndex));

        if (this.currentIndex < this.text.length) {
            this.loop = raf(() => this.run());
            return;
        }
        this.loop = undefined;
    }

    typeString(fullText: string) {
        if (fullText === this.text) {
            return;
        }
        this.lastText = this.text;
        this.text = fullText;
        if (!this.loop) {
            this.run();
        }
    }

    stop() {
        if (typeof this.loop === 'number') {
            cancel(this.loop);
            this.loop = undefined;
        }
    }

    /**
     * @param opt.useIncomingTextFirst 如果输入的文本和当前文本不一致，优先使用输入的文本
     */
    typeAllOut(fullText: string, opt?: {useIncomingTextFirst: boolean}) {
        if (opt?.useIncomingTextFirst) {
            this.lastText = this.text;
            this.text = fullText;
            this.consumer.update(this.text);
        }

        if (fullText.length >= this.text.length) {
            this.lastText = this.text;
            this.text = fullText;
        }
        this.stop();
        if (this.currentIndex < this.text.length) {
            this.consumer.update(this.text);
        }
    }
}
