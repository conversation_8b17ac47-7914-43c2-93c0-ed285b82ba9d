// import type {VirtualEditor} from '@comate/plugin-shared-internals';

import {EventMessage} from '@shared/protocols';
import {VirtualEditor, VirtualEditorMethodCall} from '@comate/plugin-shared-internals';
import {messageHandler} from './messageHandler';

/**
 * webview使用的虚拟编辑器
 * @usage: `virtualEditor.openDocument({absolutePath: 'path/to/file'})`
 */
export const virtualEditor = new Proxy(
    {},
    {
        get: function getter(target, key) {
            return (params: any) => {
                return messageHandler.send(EventMessage.VirtualEditorEvent, {
                    action: key as VirtualEditorMethodCall['action'],
                    payload: params,
                });
            };
        },
    }
) as Omit<VirtualEditor, 'resolve'>;
