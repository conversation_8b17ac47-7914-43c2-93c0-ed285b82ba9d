/* 此文件仅用作对vscode色值重定义，具体颜色样式请使用variable.css文件中--comate开头的色值 */

body.vscode-dark,
body.dark {
    --vscode-textLink-foreground: #17C3E5;
    --vscode-editor-foreground: #d4d4d4;
    --vscode-list-hoverBackground:#2a2d2e;
    --vscode-widget-border:#303031;
    --vscode-checkbox-border:#6b6b6b;
    --vscode-panel-border:rgba(128, 128, 128, 0.35);
    --vscode-sideBar-background:#252526;
    --vscode-notificationCenterHeader-background: #303031;
    --vscode-walkThrough-embeddedEditorBackground:rgba(0, 0, 0, 0.4);
    --comate-link-color-50:rgba(23, 195, 229, 0.5);
}

.vscode-light,
body.light {
    --vscode-textLink-foreground: #00A7D1;
    --vscode-editor-foreground: #000000;
    --vscode-list-hoverBackground:#e8e8e8;
    --vscode-widget-border:#d4d4d4;
    --vscode-panel-border:rgba(128, 128, 128, 0.35);
    --vscode-sideBar-background:#f3f3f3;
    --vscode-notificationCenterHeader-background: #e7e7e7;
    --vscode-walkThrough-embeddedEditorBackground: #f4f4f4;
    --comate-link-color-50:rgba(0, 167, 209, 0.5);
}

.vscode-high-contrast-light{
    --vscode-list-activeSelectionBackground:#0060c0;
    --vscode-textLink-foreground: #00A7D1;
    --vscode-toolbar-hoverBackground:rgba(184, 184, 184, 0.31);
    /* 下面这几个还需改变量名 */
    --vscode-walkThrough-embeddedEditorBackground:rgba(15, 74, 133, 0.1);
    --vscode-editor-wordHighlightStrongBackground:rgba(15, 74, 133, 0.1);
    --vscode-list-activeSelectionForeground:#FFFFFF;
    --vscode-textSeparator-foreground:rgba(0, 0, 0, 0.18);
    --vscode-inputOption-hoverBackground: rgba(184, 184, 184, 0.31);
}

.vscode-high-contrast:not(.vscode-high-contrast-light){
    --vscode-list-activeSelectionBackground:#04395e;
    --vscode-textLink-foreground: #17C3E5;
    --vscode-toolbar-hoverBackground:rgba(90, 93, 94, 0.31);
    /* 下面这几个还需改变量名 */
    --vscode-walkThrough-embeddedEditorBackground:rgba(255, 255, 255, 0.1);
    --vscode-editor-wordHighlightStrongBackground:rgba(255, 255, 255, 0.1);
    --vscode-textSeparator-foreground:rgba(255, 255, 255, 0.18);
    --vscode-inputOption-hoverBackground:rgba(90, 93, 94, 0.5);
}
