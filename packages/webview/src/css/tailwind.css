@tailwind base;
@tailwind components;
@tailwind utilities;

button {
    /* for idea version < 21.3 */
    background: none;
}

ol {
    list-style-type: decimal;
    margin-block-start: 3px;
    margin-block-end: 3px;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 1rem;
}

ul {
    list-style-type: disc;
    margin-block-start: 3px;
    margin-block-end: 3px;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 1rem;
}

.message-content {
    line-height: 160%;
    border-bottom: var(--comate-separator-border) 1px solid;
}

/* 两种场景，一种是markdown，一种是只展示代码块 */
.message-content-inProgress .message-content-markdown>pre:last-child .message-content-code-actions,
.message-content-inProgress>.message-content-code .message-content-code-actions {
    display: none;
}

.message-content-inProgress .message-content-markdown> :not(ol):not(ul):not(pre):last-child:after,
.message-content-inProgress .message-content-markdown>ol:last-child>li:last-child:not(:has(ul)):not(:has(li)):not(:has(p)):after,
.message-content-inProgress .message-content-markdown>ol:last-child>li:last-child:not(:has(ul)):not(:has(li))>p:last-child:after,
.message-content-inProgress .message-content-markdown>ol:last-child>li:last-child>ul:last-child>li:last-child:not(:has(p)):after,
.message-content-inProgress .message-content-markdown>ol:last-child>li:last-child>ul:last-child>li:last-child>p:last-child::after,
.message-content-inProgress .message-content-markdown>pre:last-child tr:last-child td:last-child:after,
.message-content-inProgress .message-content-markdown>ul:last-child>li:last-child:not(:has(ul)):not(:has(li)):not(:has(p)):after,
.message-content-inProgress .message-content-markdown>ul:last-child>li:last-child:not(:has(ul)):not(:has(li))>p:last-child::after,
.message-content-inProgress .message-content-markdown>ul:last-child>li:last-child>ul:last-child>li:last-child:not(:has(p)):after,
.message-content-inProgress .message-content-markdown>ul:last-child>li:last-child>ul:last-child>li:last-child>p:last-child::after {
    background-color: var(--comate-editor-foreground);
    border-radius: 50%;
    content: "";
    display: inline-block;
    height: 6px;
    margin-left: 6px;
    top: 50%;
    animation: inputcursor 1334ms cubic-bezier(.33, 0, .67, 1) infinite;
    transition: all 167ms cubic-bezier(.33, 0, .67, 1);
    vertical-align: baseline;
    width: 6px;
    opacity: 0.8;
}

.comate-content-table-container {
    border: 1px solid var(--comate-card-border);
    border-radius: 0.25rem;
    overflow: auto;
    width: 100%;
}

.message-content table {
    width: 100%;
    border-collapse: collapse;
    /* 去掉外边框 */
    border: none;
}

.message-content table th :not(custom-table) {
    text-align: start !important;
    padding: 0.25rem 0.75rem;
}

.message-content table td :not(custom-table){
    text-align: start !important;
    padding: 0.25rem 0.75rem;
}

/* 只保留内部边框 */
.message-content tr td:not(:last-child),
.message-content tr th:not(:last-child) {
    border-right: 1px solid var(--comate-card-border);
}

.message-content tr:not(:last-child) td,
.message-content tr:not(:last-child) th {
    border-bottom: 1px solid var(--comate-card-border);
}

.message-content table thead {
    border-radius: 0.25rem 0.25rem 0rem 0rem;
    background: radial-gradient(circle at -50% -50%,
            var(--vscode-editor-wordHighlightStrongBackground) 0%,
            var(--comate-editor-background) 55%);
}

.message-content table tbody {
    background-color: var(--vscode-walkThrough-embeddedEditorBackground);
    border-radius: 0 0 0.25rem 0.25rem;

}

.message-content li {
    margin-top: 0;
    margin-bottom: 0;
}

.message-content li>p {
    margin: 0 0;
}

/* 一级 li 有子元素时上 8px 下 4px */
.message-content li:has(ul, ol) {
    padding-top: 0.5rem;
    padding-bottom: 0.25rem;
}

/* 一级 li 无子元素上下 4px */
.message-content li:not(:has(ul, ol)) {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.message-content li li {
    padding-top: 0;
    padding-bottom: 0;
}

.message-content ul {
    margin-top: 0;
    margin-bottom: 0;
}

/* 二级 ul 上边距 6px */
.message-content ul ul {
    padding-top: 0.375rem;
    padding-bottom: 0;
}

/* 三级以上为空 */
.message-content ul ul ul {
    padding-top: 0;
    padding-bottom: 0;
}

.message-content ol {
    margin-top: 0;
    margin-bottom: 0;
}

/* 二级 ol 上边距 6px */
.message-content ol ol {
    padding-top: 0.375rem;
    padding-bottom: 0;
}

/* 三级以上为空 */
.message-content ol ol ol {
    padding-top: 0;
    padding-bottom: 0;
}


.message-content blockquote {
    border-left: 0.25rem solid var(--vscode-badge-background);
    padding: 0.25rem 0.625rem;
}

/* 标题边距规则 */
/* https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/DBLKIO3bMt/7kel25kqJArAoI#anchor-29c8ad40-589a-11ef-80b3-875435766475 */
.message-content .comate-article>:first-child {
    padding-top: 0;
}

.message-content .comate-article>:last-child {
    margin-bottom: 0;
}

.message-content-markdown>:first-child {
    padding-top: 0;
}

.message-content-markdown>:last-child {
    padding-bottom: 0;
}

.message-content h1 {
    opacity: 0.9;
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.75rem;
    letter-spacing: 0;
    text-align: justified;
    padding: 1.25rem 0 0.25rem;
}

.message-content h2 {
    opacity: 0.9;
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.75rem;
    letter-spacing: 0;
    text-align: justified;
    padding: 1.125rem 0 0.25rem;
}

.message-content h3 {
    opacity: 0.9;
    font-weight: 600;
    /* semibold */
    font-size: 1rem;
    line-height: 1.75rem;
    letter-spacing: 0;
    text-align: justify;
    padding: 1rem 0 0.25rem;
}

.message-content h4 {
    opacity: 0.9;
    font-weight: 600;
    /* semibold */
    font-size: 0.875rem;
    line-height: 1.5rem;
    letter-spacing: 0;
    text-align: justify;
    padding: 0.875rem 0 0.25rem;
}

.message-content h5 {
    opacity: 0.9;
    font-weight: 600;
    /* semibold */
    font-size: 0.8125rem;
    line-height: 1.375rem;
    letter-spacing: 0;
    text-align: justify;
    padding: 0.75rem 0 0.25rem;
}

.message-content h6 {
    opacity: 0.9;
    font-weight: 600;
    /* semibold */
    font-size: 0.8125rem;
    line-height: 1.375rem;
    letter-spacing: 0;
    text-align: justify;
    padding: 0.75rem 0 0.25rem;
}

.message-content hr {
    margin: 1.25rem 0;
    border-top: 0.06rem solid #444547;
}

.message-content p {
    /* 安全插件会出现一段内容后面有一个折行 */
    font-weight: 400;
    font-size: 0.8125rem;
    line-height: 1.375rem;
    letter-spacing: 0;
    padding: 0.25rem 0;
}

.message-content .comate-code-header {
    border-radius: 0.25rem 0.25rem 0rem 0rem;
    border: 1px solid var(--comate-card-border);
    border-bottom: none;
    background: radial-gradient(circle at -50% -50%,
            var(--vscode-editor-wordHighlightStrongBackground) 0%,
            var(--vscode-walkThrough-embeddedEditorBackground) 55%);
}

.message-content .comate-code-content {
    padding: 0.5rem 1rem;
    border-top: none;
    border: 1px solid var(--comate-card-border);
    background-color: var(--vscode-walkThrough-embeddedEditorBackground);
}

.message-content code:not(.inline-code) {
    background-color: transparent;
    border-radius: 0rem 0rem 0.25rem 0.25rem;
}

.font-pingfang {
    font-family: "PingFang SC", system-ui, -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, sans-serif;
}

.gradient-bg {
    background: linear-gradient(90deg, var(--vscode-textSeparator-foreground) 0%, var(--comate-optionButton-hoverBackground) 100%);
}

.user-select-none {
    user-select: none;
}

.ug-dynamic-height {
    height: 258px;
}

/* TODO: 为所有响应式加淡入淡出动画 */
@media (max-height: 600px) {
    .ugi-2 {
        display: none;
    }

    .ug-dynamic-height {
        height: 172px
    }
}

@media (max-height: 520px) {
    .ugi-1 {
        display: none;
    }

    .ug-dynamic-height {
        height: 86px;
    }
}

@media (max-height: 440px) {
    .comate-description {
        display: none;
    }
}

@media (max-height: 380px) {
    .comate-logo {
        display: none;
    }
}

@font-face {
    font-family: "DIN";
    src: url("../font/D-DIN-PRO-400-Regular.otf");
}

.input-box-size-extended .comate-description,
.input-box-size-extended .comate-chat-userguide {
    display: none;
}

@layer components {
    .ellipsis {
        @apply overflow-hidden whitespace-nowrap text-ellipsis;
    }
}
