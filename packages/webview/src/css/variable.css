body {
    /* 文本色 */
    /* 主题色、链接色 */
    --comate-link-color: var(--vscode-textLink-foreground);
    /* 修饰一些描述类文本色，比如弹出框提示框 */
    --comate-descriptionForeground: var(--vscode-descriptionForeground);
    /* 输入框文本色 */
    --comate-input-foreground: var(--vscode-input-foreground);
    /* 输入框placeholder色 */
    --comate-input-placeholderForeground: var(--vscode-input-placeholderForeground);
    /* 无法点击的颜色 */
    --comate-disabled-iconColor:var(--vscode-disabledForeground);
    /* 按钮文本色 */
    --comate-button-foreground: var(--vscode-button-foreground);
    /* 次级按钮文本色 */
    --comate-button-secondaryForeground: var(--vscode-button-secondaryForeground);
    /* 默认文本色 */
    --comate-editor-foreground: var(--vscode-editor-foreground);

    /* 背景色 */
    /* 页面背景色 */
    --comate-editor-background: var(--vscode-editor-background);
    /* Tooltip背景色 */
    /* tailwind.css和components/Tooltip/index.css中有border的样式用了这个背景色，后续考虑修改 */
    --comate-tooltip-background: var(--vscode-input-background);
    /* icon悬浮背景色 */
    --comate-icon-hoverBackground: var(--vscode-toolbar-hoverBackground);
    /* 列表项悬浮背景色 */
    --comate-listItem-hoverBackground: var(--vscode-list-hoverBackground);
    /* 列表、下拉框背景色,暂定为这个变量，后续输入框改版完成会修改 */
    --comate-list-background: var(--vscode-sideBar-background);
    /* 列表选中背景色 */
    --comate-list-activeSelectionBackground: var(--vscode-list-activeSelectionBackground);
    /* 其他输入框被调出时的背景覆盖色 */
    --comate-inputOverlay-background:var(--vscode-editorWidget-background);
    /* 反馈框背景色 */
    --comate-notifications-background: var(--vscode-notifications-background);
    /* 表格背景色 */
    --comate-table-background: var(--vscode-walkThrough-embeddedEditorBackground);
    /* 部分按钮的悬浮背景色 */
    --comate-button-hoverBackground: var(--vscode-button-hoverBackground);
    /* 次级按钮悬浮背景色 */
    --comate-button-secondaryHoverBackground: var(--vscode-button-secondaryHoverBackground);
    /* 选项式按钮的悬浮背景色 */
    --comate-optionButton-hoverBackground: var(--vscode-inputOption-hoverBackground);
    /* 智能体灰色按钮背景色 */
    --comate-grayButton-background: var(--vscode-notificationCenterHeader-background);
    /* 调用失败提示或删除按钮背景色 */
    --comate-danger-background: var(--vscode-charts-red);

    /* 边框、分割线颜色 */
    /* 面板分割线 */
    --comate-panel-border: var(--vscode-panel-border);
    /* 卡片、内容、问答分割线 */
    --comate-separator-border: var(--vscode-widget-border);
    /* 部分按钮边框 */
    --comate-button-border: var(--vscode-checkbox-border);
    /* Tooltip、弹窗边框 */
    --comate-tooltip-border: var(--vscode-editorWidget-border);
    /*卡片边框 */
    --comate-card-border: var(--vscode-keybindingLabel-bottomBorder);
}