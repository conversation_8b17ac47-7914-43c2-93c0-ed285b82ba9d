import {useRef, useEffect} from 'preact/compat';

type CustomEquals<T> = (previous: T | undefined, current: T) => boolean;

export const shallowEquals: CustomEquals<Record<string, any> | undefined> = (previous, current) => {
    if (previous === current) {
        return true;
    }
    if (previous === undefined || current === undefined) {
        return false;
    }

    const previousKeys = Object.keys(previous);
    const currentKeys = Object.keys(current);
    if (previousKeys.length !== currentKeys.length) {
        return false;
    }
    for (const key of previousKeys) {
        if (current[key] !== previous[key]) {
            return false;
        }
    }
    return true;
};

export function useOriginalCopy<T>(value: T, equals: CustomEquals<T>): T {
    const cache = useRef<T>(value);
    const equalsRef = useRef(equals);
    useEffect(
        () => {
            equalsRef.current = equals;
        },
        [equals]
    );
    useEffect(
        () => {
            if (!equalsRef.current(cache.current, value)) {
                cache.current = value;
            }
        },
        [value]
    );

    return equals(cache.current, value) ? cache.current : value;
}

export function useOriginalCopyShallow(value: Record<string, any> | undefined) {
    return useOriginalCopy(value, shallowEquals);
}
