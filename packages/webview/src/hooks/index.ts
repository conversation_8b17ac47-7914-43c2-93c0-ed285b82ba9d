import {useCallback, useState} from 'preact/hooks';

export const useToggle = (initial: boolean): [boolean, () => void, {open: () => void, close: () => void}] => {
    const [value, setValue] = useState(initial);

    const toggle = useCallback(
        () => {
            setValue(!value);
        },
        [value]
    );

    const open = useCallback(
        () => setValue(true),
        [setValue]
    );

    const close = useCallback(
        () => setValue(false),
        [setValue]
    );

    return [value, toggle, {open, close}];
};
