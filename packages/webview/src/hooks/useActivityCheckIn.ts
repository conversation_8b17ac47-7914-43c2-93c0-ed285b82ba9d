import {isInteractiveChatUri} from '@shared/utils/parseInteractiveUri';
import {EventMessage} from '@shared/protocols';
import {useCallback, useRef} from 'preact/hooks';
import navigateFromUri from '@/utils/navigateFromUri';
import {activityCheckInApi} from '@/api';
import {messageHandler} from '@/utils/messageHandler';
import {useExtensionConfig} from './useExtensionConfig';

export default function useActivityCheckIn() {
    const {config} = useExtensionConfig();
    const userRef = useRef(config);
    return useCallback(
        (href: string) => {
            // TODO 在这里拦截交互式聊天 uri，但是不够，最好在所有的link都拦一下
            if (isInteractiveChatUri(href)) {
                const component = navigateFromUri(href);
                // 上面留着是通用的，下面给活动单独处理
                activityCheckInApi({
                    license: userRef.current.license,
                    device: userRef.current.deviceId,
                    uuid: component.params?.uuid,
                });
                return;
            }
            messageHandler.send(EventMessage.LinkClickEvent, href);
        },
        []
    );
}
