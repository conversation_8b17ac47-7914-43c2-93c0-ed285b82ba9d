import {createContext} from 'preact';
import {useContext, useState, useEffect, useCallback} from 'preact/hooks';
import {noop} from 'lodash';
import {createPortal} from 'preact/compat';
import {ToastMessage} from '@shared/protocols';
import {Countdown} from '@/components/Tooltip/Countdown';
import successCircleOutlineIcon from '@/assets/successCircleOutline.svg';
import failCircleOutlineIcon from '@/assets/failCircleOutline.svg';
import infoIcon from '@/assets/info.svg';

interface Toast extends ToastMessage {
    render?: () => string | JSX.Element;
    createdTime: number;
}

const renderIcon = (type: Toast['type']) => {
    switch (type) {
        case 'success':
            return (
                <div
                    className="w-4 h-4 text-green-500"
                    // bca-disable-line
                    dangerouslySetInnerHTML={{__html: successCircleOutlineIcon}}
                />
            );
        case 'fail':
            return (
                <div
                    className="w-4 h-4 text-red-500"
                    // bca-disable-line
                    dangerouslySetInnerHTML={{__html: failCircleOutlineIcon}}
                />
            );
        case 'info':
            return (
                <div
                    className="w-4 h-4 text-[var(--comate-link-color)]"
                    // bca-disable-line
                    dangerouslySetInnerHTML={{__html: infoIcon}}
                />
            );
        default:
            return null;
    }
};

const toastContext = createContext<{toasts: Toast[], toast: (toast: Omit<Toast, 'createdTime'>) => void}>({
    toasts: [],
    toast: noop,
});

export const useToast = () => useContext(toastContext);
export const ToastProvider = props => {
    const [toasts, setToasts] = useState<Toast[]>([]);

    // TODO: 增加 closable, unlimited
    const toast = useCallback(
        (toast: Omit<Toast, 'createdTime'>) => {
            setToasts(prev => [...prev, {...toast, createdTime: Date.now()}]);
        },
        []
    );

    useEffect(
        () => {
            if (toasts.length > 0) {
                window.setTimeout(
                    () => {
                        setToasts(toasts =>
                            toasts.filter(toast => {
                                const expired = (Date.now() - toast.createdTime) > 5000;
                                return !expired;
                            })
                        );
                    },
                    5000
                );
            }
        },
        [toasts]
    );

    return (
        <toastContext.Provider value={{toasts, toast}}>
            {props.children}
            {createPortal(
                <div className="fixed w-full z-50 top-4">
                    {toasts.map(toast => {
                        return (
                            <div
                                key={toast}
                                // eslint-disable-next-line max-len
                                className="absolute left-1/2 translate-x-[-50%] px-[16px] h-[30px] max-w-[calc(100%-32px)] rounded flex items-center gap-4 shadow-[0_0_12px_0_rgba(0,0,0,0.15)] border border-[var(--comate-button-border)] bg-[var(--comate-tooltip-background)]"
                            >
                                <div className="flex items-center gap-2">
                                    {renderIcon(toast.type)}
                                    <div className="whitespace-nowrap">
                                        {toast.render ? toast.render() : toast.message}
                                    </div>
                                </div>
                                <Countdown milesecond={5 * 1000}>
                                    {(num: number) => {
                                        return num === 0
                                            ? null
                                            : (
                                                <span className="opacity-60">
                                                    {Math.floor(num / 1000)}s
                                                </span>
                                            );
                                    }}
                                </Countdown>
                            </div>
                        );
                    })}
                </div>,
                document.body
            )}
        </toastContext.Provider>
    );
};
