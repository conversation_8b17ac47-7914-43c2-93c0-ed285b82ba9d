import {createContext} from 'preact';
import {useContext, useEffect} from 'preact/hooks';
import {noop} from 'lodash';
import {useToggle} from '.';

const context = createContext<ReturnType<typeof useToggle>>([false, noop, {open: noop, close: noop}]);

export const useInputBoxSizeToggle = () => useContext(context);

export const InputBoxSizeToggleProvider = ({...props}) => {
    const toggleState = useToggle(false);

    const [isOpen] = toggleState;
    useEffect(
        () => {
            if (isOpen) {
                document.body.classList.add('input-box-size-extended');
            }
            return () => document.body.classList.remove('input-box-size-extended');
        },
        [isOpen]
    );

    return <context.Provider value={toggleState} {...props} />;
};
