import {createContext} from 'preact';
import {Dispatch, StateUpdater, useCallback, useContext, useEffect, useMemo, useState} from 'preact/hooks';
import {ActiveEditor, ContextType, EventMessage, KnowledgeList} from '@shared/protocols';
import {noop, without} from 'lodash';
import {messageHandler} from '@/utils/messageHandler';
import {extractMentionsFromMarkupedText} from '@/components/InputBox/utils/mention';

interface Value {
    activeEditor: (ActiveEditor & {visible?: boolean}) | null;
    selectedFileRelativePaths: string[];
    fileContexts: KnowledgeList[];
    onFileContextSelected: Dispatch<StateUpdater<string[]>>;
    onActiveEditorChanged: Dispatch<StateUpdater<Value['activeEditor']>>;
    popSelectedFile: () => void;
    onMuteActiveEditor: () => void;
}

const defaultValue = {
    activeEditor: null,
    selectedFileRelativePaths: [],
    fileContexts: [],
    popSelectedFile: noop,
    onFileContextSelected: noop,
    onMuteActiveEditor: noop,
    onActiveEditorChanged: noop,
};

const EditorWatcherContext = createContext<Value>(defaultValue);
export const useEditorWatcher = () => useContext(EditorWatcherContext);

export const EditorWatcherProvider = ({...props}) => {
    const [watcher, setWatcher] = useState<{activeEditor: Value['activeEditor']}>({activeEditor: null});
    const [selectedFileRelativePaths, setSelectedFileRelativePaths] = useState<string[]>([]);

    const activeEditorPath = watcher.activeEditor?.relativePath;
    const activeEditorVisible = watcher.activeEditor?.visible;
    const onActiveEditorChanged = useCallback(
        stateUpdator => {
            setWatcher(prev => ({
                ...prev,
                activeEditor: typeof stateUpdator === 'function' ? stateUpdator(prev.activeEditor) : stateUpdator,
            }));
        },
        []
    );
    /**
     * 劫持文件的选择事件，假设选择的是当前文件，且之前当前文件被删除，此时行为改为重置当前文件的选择状态
     */
    const onFileContextSelected = useCallback<typeof onFileContextSelected>(
        stateUpdator => {
            setSelectedFileRelativePaths(prev => {
                const nextState = typeof stateUpdator === 'function'
                    ? stateUpdator(prev)
                    : stateUpdator;
                if (!activeEditorVisible && nextState.includes(activeEditorPath!)) {
                    onActiveEditorChanged(activeEditor => ({
                        ...activeEditor,
                        visible: true,
                    }));
                    return without(nextState, activeEditorPath!);
                }
                return nextState;
            });
        },
        [activeEditorPath, activeEditorVisible, onActiveEditorChanged]
    );

    const onMuteActiveEditor = useCallback(
        () => {
            onActiveEditorChanged(activeEditor => {
                if (activeEditor) {
                    return {...activeEditor, visible: false};
                }
                return activeEditor;
            });
        },
        [onActiveEditorChanged]
    );

    const popSelectedFile = useCallback(
        () => {
            if (selectedFileRelativePaths.length) {
                onFileContextSelected(pre => pre.slice(0, pre.length - 1));
            }
            else {
                onMuteActiveEditor();
            }
        },
        [onFileContextSelected, onMuteActiveEditor, selectedFileRelativePaths.length]
    );

    useEffect(
        () => {
            messageHandler.send(EventMessage.EditorChangedEvent);
        },
        []
    );

    const value = useMemo(
        () => {
            const isActiveEditor = (relativePath: string) => {
                return relativePath !== watcher?.activeEditor?.relativePath;
            };
            const pathToContext = (relativePath: string) => {
                const basename = decodeURI(relativePath).split(/(\\|\/)/).pop()!;
                return {id: relativePath, name: basename, type: ContextType.FILE};
            };

            const fileContexts = selectedFileRelativePaths.filter(isActiveEditor).map(pathToContext);
            if (watcher.activeEditor && watcher.activeEditor.visible) {
                fileContexts.unshift({
                    id: watcher.activeEditor.relativePath,
                    name: watcher.activeEditor.name,
                    type: ContextType.FILE,
                });
            }
            return {
                ...watcher,
                selectedFileRelativePaths,
                onActiveEditorChanged,
                fileContexts,
                popSelectedFile,
                onFileContextSelected,
                onMuteActiveEditor,
            };
        },
        [
            onActiveEditorChanged,
            onFileContextSelected,
            onMuteActiveEditor,
            popSelectedFile,
            selectedFileRelativePaths,
            watcher,
        ]
    );

    return <EditorWatcherContext.Provider value={value} {...props} />;
};

export const useEditorWatchListener = (query: string) => {
    const {onFileContextSelected, selectedFileRelativePaths, onActiveEditorChanged} = useEditorWatcher();
    useEffect(
        () => {
            messageHandler.listen(EventMessage.EditorChangedEvent, ({activeEditor, fromCommand}) => {
                if (fromCommand) {
                    // 从右键菜单触发时，总是重置为可见，如果已经被选择过，则移除
                    onActiveEditorChanged(() => ({...activeEditor!, visible: true}));
                    if (selectedFileRelativePaths.includes(activeEditor!.relativePath)) {
                        onFileContextSelected(prev => without(prev, activeEditor!.relativePath));
                    }
                }
                else {
                    onActiveEditorChanged(prevActiveEditor => {
                        if (!activeEditor) {
                            return null;
                        }

                        const prevActiveRelativePath = prevActiveEditor?.relativePath;
                        const nextActiveRelativePath = activeEditor.relativePath;
                        const activeEditorChanged = prevActiveRelativePath === nextActiveRelativePath;
                        const shouldIgnoreChangeEvent = activeEditorChanged && !prevActiveEditor?.visible;
                        // 如果仅更新selection，且当前文件之前被删过，则跳过该事件
                        if (shouldIgnoreChangeEvent) {
                            return prevActiveEditor;
                        }

                        const mentions = extractMentionsFromMarkupedText(query);
                        const relativePathInMentions = mentions
                            .filter(({id}) => id.startsWith('file:'))
                            .map(({id}) => id.replace(/^file:/, ''));
                        // 如果在文件标签里来回点击切换，等价于当前文件已经出现在文件并且里，包括mentions解析出来的，则忽略该事件
                        const existed = [
                            prevActiveRelativePath,
                            ...selectedFileRelativePaths,
                            ...relativePathInMentions,
                        ]
                            .includes(nextActiveRelativePath);
                        if (existed) {
                            return prevActiveEditor ? {...prevActiveEditor, selection: activeEditor.selection} : null;
                        }

                        return {...activeEditor, visible: true};
                    });
                }
                // 纯粹为了类型兼容没啥用
                return {activeEditor};
            });
        },
        [onActiveEditorChanged, onFileContextSelected, query, selectedFileRelativePaths]
    );
};
