import {createContext} from 'preact';
import {useCallback, useContext, useEffect, useMemo, useState} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import {noop} from 'lodash';
import {LicenseFullDetail} from '@comate/plugin-shared-internals';
import {messageHandler} from '@/utils/messageHandler';
import {apiGetChatintentRecognition, apiPostChatintentRecognition} from '@/components/AutoWork/utils/api';
import {isInternal, isPoc, isSaas} from '@/utils/features';
import {isVSCode} from '@/config';
import {proxyFetch} from '@/utils/proxyFetch';
import {retryAsyncFunc} from '@/utils/retryAsyncFunc';

interface VpcConfig {
    system: {
        /** 是否展示首页引导 */
        enableUserGuide: boolean;
        /** 是否可用智能体 */
        enableAgent: boolean;
        /*
         * 环境信息，internal/saas/on-prem
         * internal: 内部版本
         * saas: SaaS版本
         * on-prem: 私有化部署版本
         */
        env: 'internal' | 'saas' | 'on-prem';
    };
    comateplus: {
        /** 是否可用三方插件 */
        enabled: boolean;
    };
    security: {
        /** 是否可用安全相关功能 */
        enabled: boolean;
    };
    autowork: {
        /** 是否开启意图识别 */
        enableIntentRecognition: boolean;
    };
    embedding: {
        /** 是否支持网络和api检索 */
        enableSearchFromInternet: boolean;
    };
}
interface Value {
    config: {
        /** 是否是混合云用户 */
        customized: boolean;
        /** 是否开启意图识别开关 */
        chatIntentRecognition?: boolean;
        license: string;
        /** license 图标类型 */
        licenseTypeId: number;
        /** licnese的用户类型 */
        licenseTypeCode: LicenseFullDetail['typeCode'];
        /** license 显示的名字 */
        licenseType: string;
        /** 更新默认用户反馈链接 */
        customFeedbackURL: string;
        /** 是否是自定义服务 */
        enablePrivateService: boolean;
        /** 自定义服务host */
        privateServiceHost?: string;
        /** 所有的回复仅使用该信息 */
        messageResponse: string;
        /** 设备id */
        deviceId: string;
        /** 是否展示首页引导 */
        enableUserGuide: boolean;
        /** 是否可用三方插件 */
        enablePlugin: boolean;
        /** 是否可用安全相关功能 */
        enableSecurity: boolean;
        /** 是否开启意图识别 */
        enableIntentRecognition: boolean;
        /** 是否可用智能体 */
        enableAgent: boolean;
        /** 是否支持网络和api检索 */
        enableSearchFromInternet: boolean;
        /** 是否是poc环境 */
        isPoc: boolean;
        /** 对话界面的是否显示反馈 */
        enableChatFeedback: boolean;
    };
    patchChatintentRecognition: (status: boolean) => void;
}

const mergeFp = (...nextValue) => currentValue => ({...Object.assign(currentValue, ...nextValue)});


const pocDefaultValue = isVSCode ? !isPoc : !isSaas;

const defaultState: Value = {
    config: {
        customized: false,
        license: '',
        licenseType: '', // 默认不显示
        licenseTypeId: 2, // 默认免费
        licenseTypeCode: 'TRIAL_INDIVIDUAL', // 默认免费
        customFeedbackURL: '',
        enablePrivateService: false,
        privateServiceHost: '',
        messageResponse: '',
        deviceId: '',
        enableUserGuide: pocDefaultValue,
        enablePlugin: pocDefaultValue,
        enableSecurity: pocDefaultValue,
        enableIntentRecognition: pocDefaultValue,
        enableAgent: pocDefaultValue,
        enableSearchFromInternet: pocDefaultValue,
        isPoc: isVSCode ? isPoc : isSaas,
        enableChatFeedback: pocDefaultValue,
    },
    patchChatintentRecognition: noop,
};
const extensionConfigContext = createContext<Value>(defaultState);

export const useExtensionConfig = () => useContext(extensionConfigContext);

export const ExtensionConfigProvider = ({...props}) => {
    const [config, setConfig] = useState<Value['config']>(defaultState.config);

    const initializedCustomizedCloudSetting = useCallback(
        async () => {
            const {customized} = await messageHandler.send(EventMessage.CustomizeUserFetchEvent);

            messageHandler.listen(EventMessage.CustomizeUserChangeEvent, ({customized}: {customized: boolean}) => {
                setConfig(mergeFp({customized}));
            });
            return {customized};
        },
        []
    );

    const initializedChatIntentRecognition = useCallback(
        async () => {
            const chatIntentRecognition = await apiGetChatintentRecognition();
            return {chatIntentRecognition: chatIntentRecognition.status};
        },
        []
    );

    const initializedLicenseFullDetail = useCallback(
        async () => {
            const res = await messageHandler.send(EventMessage.LicenseFullDetailFetchEvent);
            messageHandler.listen(EventMessage.LicenseFullDetailChangeEvent, e => {
                setConfig(prev => ({
                    ...prev,
                    enablePrivateService: res?.vpcConfig?.status || !!res?.endpoint,
                    license: e.key,
                    licenseType: e.type,
                    licenseTypeId: e.typeId,
                    licenseTypeCode: e?.typeCode,
                    customFeedbackURL: e?.features?.customFeedbackURL,
                    privateServiceHost: e?.endpoint,
                    messageResponse: e?.vpcConfig?.intranetErrorMsg,
                }));
            });
            return {
                enablePrivateService: res?.vpcConfig?.status || !!res?.endpoint,
                license: res.key,
                licenseType: res.type,
                licenseTypeId: res.typeId,
                licenseTypeCode: res?.typeCode,
                customFeedbackURL: res?.features?.customFeedbackURL,
                privateServiceHost: res?.endpoint,
                messageResponse: res?.vpcConfig?.intranetErrorMsg,
            };
        },
        []
    );

    const initializedBaseInformation = useCallback(
        async () => {
            const info = await messageHandler.send(EventMessage.InitFetchEvent);
            return {deviceId: info.deviceId};
        },
        []
    );

    const initializedPocConfig = useCallback(
        () => {
            const sync = async () => {
                const res = await proxyFetch<VpcConfig>({
                    method: 'GET',
                    url: '/api/config/global',
                    useBaseUrl: true,
                    rawData: true,
                });

                const config = res.data as VpcConfig;
                return {
                    enableUserGuide: config.system.enableUserGuide,
                    enablePlugin: config.comateplus.enabled,
                    enableSecurity: config.security.enabled,
                    enableIntentRecognition: config.autowork.enableIntentRecognition,
                    enableAgent: config.system.enableAgent,
                    enableSearchFromInternet: config.embedding.enableSearchFromInternet,
                    isPoc: isVSCode ? isPoc : config.system.env === 'on-prem',
                    // TODO 要改一下
                    enableChatFeedback: false,
                };
            };
            const pocConfig = retryAsyncFunc(
                sync,
                10000, // 循环间隔：10秒
                180000, // 总循环时间：3分钟
                {
                    enableUserGuide: false,
                    enablePlugin: false,
                    enableSecurity: false,
                    enableIntentRecognition: false,
                    enableAgent: false,
                    enableSearchFromInternet: false,
                    isPoc: isVSCode ? isPoc : true,
                    enableChatFeedback: false,
                }
            );
            return pocConfig;
        },
        []
    );

    const patchChatIntentRecognition = useCallback(
        (status: boolean) => {
            apiPostChatintentRecognition({status});
            setConfig(mergeFp({chatIntentRecognition: status}));
        },
        []
    );

    const setup = useCallback(
        async () => {
            try {
                if (isInternal) {
                    const configs = await Promise.all([
                        initializedChatIntentRecognition(),
                        initializedBaseInformation(),
                    ]);
                    setConfig(mergeFp(...configs));
                }
                else if (isPoc) {
                    let eventList = [
                        initializedLicenseFullDetail(),
                        initializedPocConfig(),
                    ];
                    if ($features.WEBVIEW_CONSUMER === 'vs') {
                        eventList = [initializedLicenseFullDetail(), initializedPocConfig()] as any;
                    }
                    const configs = await Promise.all(eventList);
                    setConfig(mergeFp(...configs));
                } else {
                    const configs = await Promise.all([
                        initializedCustomizedCloudSetting(),
                        initializedChatIntentRecognition(),
                        initializedLicenseFullDetail(),
                        initializedBaseInformation(),
                        initializedPocConfig(),
                    ]);
                    setConfig(mergeFp(...configs));
                }
            }
            catch (ex) {
                // eslint-disable-next-line no-console
                console.log('catch setup error', (ex as Error).message);
            }
        },
        [
            initializedBaseInformation,
            initializedChatIntentRecognition,
            initializedCustomizedCloudSetting,
            initializedLicenseFullDetail,
            initializedPocConfig,
        ]
    );

    const value = useMemo(
        () => ({config, patchChatintentRecognition: patchChatIntentRecognition}),
        [config, patchChatIntentRecognition]
    );

    useEffect(
        () => {
            setup();
        },
        [setup]
    );

    useEffect(
        () => {
            messageHandler.listen(EventMessage.PrivateServiceUpdateEvent, async () => {
                const configs = await initializedPocConfig();
                setConfig(mergeFp(configs));
            });
        },
        [initializedPocConfig]
    );

    return <extensionConfigContext.Provider value={value} {...props} />;
};
