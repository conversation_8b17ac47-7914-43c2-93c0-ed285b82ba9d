import {createContext} from 'preact';
import {useCallback, useContext, useEffect, useMemo, useRef, useState} from 'preact/hooks';
import {ComposerMessages, DehydratedAssistantMessage, EventMessage} from '@shared/protocols';
import {noop} from 'lodash';
import {messageHandler} from '@/utils/messageHandler';

interface ContextValue {
    messages: ComposerMessages;
    getAcceptance: (id: number) => boolean;
    getLastestMessageId: () => number | undefined;
    onAccept: (id: number, accept: boolean) => void;
}

const composerMessageContext = createContext<ContextValue>({
    messages: [],
    getAcceptance: () => false,
    onAccept: noop,
    getLastestMessageId: () => undefined,
});

export const useComposerMessage = () => useContext(composerMessageContext);

export const ComposerMessageProvider = ({...props}) => {
    const [messages, setMessages] = useState<ContextValue['messages']>([]);
    const acceptanceRef = useRef<Record<number, boolean>>({});
    const latestAssistantMessageRef = useRef<DehydratedAssistantMessage | undefined>(undefined);

    const onAccept = useCallback(
        (id: number, accept: boolean) => {
            acceptanceRef.current = {...acceptanceRef.current, [id]: accept};
        },
        []
    );

    const getAcceptance = useCallback(
        (id: number) => acceptanceRef.current[id],
        []
    );

    const getLastestMessageId = useCallback(
        () => latestAssistantMessageRef.current?.id,
        []
    );

    const value = useMemo(
        () => ({getLastestMessageId, getAcceptance, onAccept, messages}),
        [getAcceptance, getLastestMessageId, messages, onAccept]
    );

    useEffect(
        () => {
            messageHandler.listen(EventMessage.ChatLabMessageEvent, messages => {
                const latestMessage = messages[messages.length - 1];
                if (latestMessage?.role === 'assistant') {
                    latestAssistantMessageRef.current = latestMessage;
                }
                setMessages(messages);
            });
        },
        []
    );

    return <composerMessageContext.Provider value={value} {...props} />;
};
