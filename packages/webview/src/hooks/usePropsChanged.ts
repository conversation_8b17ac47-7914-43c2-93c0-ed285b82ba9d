import {useRef, useEffect} from 'preact/hooks';

const usePropsChanged = (props: Record<string, any>) => {
    const prevProps = useRef(props);

    useEffect(() => {
        const changedProps = Object.entries(props).reduce((acc, [key, val]) => {
            if (prevProps.current[key] !== val) {
                acc[key] = {
                    from: prevProps.current[key],
                    to: val,
                };
            }
            return acc;
        }, {});

        if (Object.keys(changedProps).length > 0) {
            // eslint-disable-next-line no-console
            console.log('Changed props:', changedProps);
        }

        prevProps.current = {...props};
    });
};

export default usePropsChanged;
