import {createContext} from 'preact';
import {fromPairs} from 'lodash';
import {merge} from 'lodash/fp';
import {useContext, useEffect, useState} from 'preact/hooks';
import {EventMessage, Feature, FeatureFlags} from '@shared/protocols';
import {apiGetAccessTo} from '@/components/AutoWork/utils/api';
import {messageHandler} from '@/utils/messageHandler';

type Value = Partial<Record<Feature, boolean>>;
const initContext = {
    [Feature.EnableUserGuide]: true,
    [Feature.EnableIAPI]: true,
    [Feature.ComatePlus]: true,
};
const AccessToContext = createContext<Value>(initContext);
export const useAccessTo = (feature: Feature) => useContext(AccessToContext)[feature];

export const AccessToProvider = ({...props}) => {
    const [accessTo, setAccessTo] = useState<Value>(initContext);
    useEffect(
        () => {
            (async () => {
                const features = [];
                const status = await Promise.all(features.map(featureName => apiGetAccessTo({featureName})));
                const updated = fromPairs(features.map((key, i) => [key, status[i].status]));
                setAccessTo(merge(updated));
            })();
        },
        []
    );
    useEffect(
        () => {
            (async () => {
                messageHandler.listen(EventMessage.FeatureFlagsChangeEvent, (flags: FeatureFlags) => {
                    setAccessTo(pre => ({...pre, ...flags}));
                });
            })();
        },
        []
    );

    return <AccessToContext.Provider value={accessTo} {...props} />;
};
