import {createContext} from 'preact';
import {useContext, useEffect, useState} from 'preact/hooks';
import i18next from 'i18next';

export type SupportedI18nLanguages = 'zh' | 'en';
const languageContext = createContext<SupportedI18nLanguages>('zh');

export const useI18nLanguage = () => useContext(languageContext);

export const I18nLangaugeProvider = ({...props}) => {
    const [lng, setLng] = useState<SupportedI18nLanguages>(i18next.language as SupportedI18nLanguages);
    useEffect(
        () => {
            const updateLng = () => setLng(i18next.language as SupportedI18nLanguages);
            i18next.on('languageChanged', updateLng);
            return () => {
                i18next.off('languageChanged', updateLng);
            };
        },
        []
    );

    return <languageContext.Provider value={lng} {...props} />;
};
