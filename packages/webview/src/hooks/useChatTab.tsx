import {createContext} from 'preact';
import {useContext, useState, useRef, useEffect, useCallback} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';

export enum ChatTabKey {
    CHAT = 'CHAT',
    PAIR = 'PAIR',
    LAB = 'LAB',
    AGENT = 'AGENT',
    KERNEL_DEMO = 'KERNEL_DEMO',
}

interface ContextValue {
    activeTabKey: ChatTabKey;
    activeTabKeyRef: React.MutableRefObject<ChatTabKey>;
    setActiveTabKey: (key: ChatTabKey) => void;
}

const Context = createContext<ContextValue>({
    activeTabKey: ChatTabKey.CHAT,
    activeTabKeyRef: {current: ChatTabKey.CHAT},
    setActiveTabKey: () => {},
});

export const ChatTabProvider = ({...props}) => {
    const [activeTabKey, setActiveTabKey] = useState<ChatTabKey>(ChatTabKey.CHAT);
    const activeTabKeyRef = useRef(activeTabKey);
    activeTabKeyRef.current = activeTabKey;

    const handleActiveTabKeyChange = useCallback(
        (key?: ChatTabKey) => {
            setActiveTabKey(key || activeTabKeyRef.current);
            if (key) {
                messageHandler.send(EventMessage.CacheAddEvent, {key: 'activeTabKey', value: key});
            }
        },
        []
    );

    useEffect(
        () => {
            messageHandler.send(EventMessage.CacheGetEvent, 'activeTabKey').then(key => {
                setActiveTabKey(key ?? activeTabKeyRef.current);
            });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return (
        <Context.Provider
            value={{
                activeTabKey,
                activeTabKeyRef,
                setActiveTabKey: handleActiveTabKeyChange,
            }}
            {...props}
        />
    );
};

export function useChatTabContext() {
    return useContext(Context);
}
