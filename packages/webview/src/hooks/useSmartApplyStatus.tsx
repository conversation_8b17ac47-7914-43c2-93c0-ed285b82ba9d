/**
 * 智能粘贴的状态管理context
 * 由于智能粘贴需求处理跨消息的多个代码块的采纳状态，改为全局管理统一设置唯一key
 */
import {EventMessage, SmartApplyStatus} from '@shared/protocols';
import {memoize} from 'lodash';
import {createContext} from 'preact';
import {useCallback, useContext, useEffect, useMemo, useState} from 'preact/hooks';
import {messageHandler} from '@/utils/messageHandler';

type MessageId = number | string;
// 智能采纳的key
type SmartApplyKey = string;
type SmartApplyState = Record<SmartApplyKey, {filePath: string, status: SmartApplyStatus}>;

const defaultValue = {
    status: {},
    smartApply: () => '',
    acceptedOrReject: () => Promise.resolve(),
    getSmartApplyStatus: () => SmartApplyStatus.UNTOUCHED,
};
interface ContextValue {
    status: SmartApplyState;
    smartApply: (messageId: MessageId, codeblock: string) => void;
    acceptedOrReject: (messageId: string, codeblock: string, accepted: boolean) => Promise<void>;
    getSmartApplyStatus: (messageId: MessageId, codeblock: string) => SmartApplyStatus;
}

const context = createContext<ContextValue>(defaultValue);

export const generateSmartApplyKey = memoize(
    (messageId: MessageId, codeblock: string) => {
        const base64code = window.btoa(window.encodeURIComponent(codeblock)).slice(-32);
        return `${base64code}-${messageId}`;
    },
    (messageId, codeblock) => {
        return messageId + codeblock.trim().replace(/\n$/, '');
    }
);

export const SmartApplyProvider = ({...props}) => {
    const [state, setState] = useState<SmartApplyState>({});
    const getSmartApplyStatus = useCallback(
        (messageId: MessageId, codeblock: string) => {
            const key = generateSmartApplyKey(messageId, codeblock);
            return state[key] ? state[key].status : SmartApplyStatus.UNTOUCHED;
        },
        [state]
    );

    /**
     * 更新已经存在的记录
     */
    const updateExistedSmartApplyRecord = useCallback(
        (key: string, status: SmartApplyStatus) => {
            setState(prev => {
                const existed = prev[key];
                if (existed) {
                    return {...prev, [key]: {...existed, status}};
                }
                return {...prev, [key]: {filePath: '', status}};
            });
        },
        []
    );

    /**
     * 执行智能粘贴并且把状态设置为应用中
     */
    const smartApply = useCallback(
        (messageId: MessageId, codeblock: string) => {
            const key = generateSmartApplyKey(messageId, codeblock);
            // const {filePath} = await messageHandler.send(EventMessage.InvokeSmartApply, {key, content: codeblock});
            updateExistedSmartApplyRecord(key, SmartApplyStatus.APPLING);
            return key;
        },
        [updateExistedSmartApplyRecord]
    );
    /**
     * 采纳或者放弃，并且状态变更为触发过
     */
    const acceptedOrReject = useCallback(
        async (messageId: string, codeblock: string, accepted: boolean) => {
            const key = generateSmartApplyKey(messageId, codeblock);
            updateExistedSmartApplyRecord(key, SmartApplyStatus.APPLIED);
            await messageHandler.send(EventMessage.InvokeSmartApply, {
                key,
                content: codeblock,
                accepted,
            });
        },
        [updateExistedSmartApplyRecord]
    );

    useEffect(
        () => {
            messageHandler.listen(EventMessage.SmartApplyUpdate, ({key, status}) => {
                updateExistedSmartApplyRecord(key, status);
            });
        },
        [updateExistedSmartApplyRecord]
    );

    const value = useMemo(
        () => ({
            status: state,
            smartApply,
            acceptedOrReject,
            getSmartApplyStatus,
        }),
        [acceptedOrReject, getSmartApplyStatus, smartApply, state]
    );

    return <context.Provider value={value} {...props} />;
};

export const useSmartApplyStatus = () => {
    return useContext(context);
};
