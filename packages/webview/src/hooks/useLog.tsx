import {createContext} from 'preact';
import {useCallback, useContext} from 'preact/hooks';
import {proxyFetch} from '@/utils/proxyFetch';

type LogType = 'event' | 'error';

interface ContextValue {
    log: (event: any, type?: LogType) => void;
}

const Context = createContext<ContextValue>({
    log: () => {},
});

interface Props {
    context: {userName: string};
    children: any;
}

export default function LogProvider({context, children}: Props) {

    const log = useCallback(
        async (event: any, type: LogType = 'event') => {
            await proxyFetch(
                {
                    method: 'POST',
                    url: '/logger/comate.log',
                    useBaseUrl: true,
                    body: JSON.stringify(
                        {
                            type,
                            event,
                            platform: $features.PLATFORM,
                            common: {
                                app: 'comate-vscode',
                                username: context.userName,
                                timestamp: Date.now(),
                            },
                        }
                    ),
                    authorization: true,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        },
        [context]
    );

    return (
        <Context.Provider value={{log}}>
            {children}
        </Context.Provider>
    );
}

export function useLog() {
    return useContext(Context);
}
