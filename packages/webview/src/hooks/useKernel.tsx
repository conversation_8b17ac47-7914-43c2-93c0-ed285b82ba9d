import {createContext} from 'preact';
import {useCallback, useContext, useEffect, useMemo, useState} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import type {
    PassthroughMessageResponse,
    PassthroughMessageParams,
} from '@comate/kernel-shared';

import {
    PTMessageClient,
    PTMessageServer,
    PT_KERNEL_SYSTEM_INITIALIZED_REQUEST,
    PT_WEBVIEW_SYSTEM_INITIALIZED_REQUEST,
} from '@comate/kernel-shared/browser';
import {messageHandler} from '@/utils/messageHandler';

// 请求参数类型
export interface RequestParams<T = any> {
    event: string;
    data: T;
}

// 扩展 KernelContextType 接口
export interface KernelContextType {
    /** kernel 是否启动成功 */
    isReady: boolean;
    /** 发送消息给kernel并且等待回复，请注意发送前一定要保证kernel ready，否则会返回一个reject */
    sendRequest: (event: string, data: any) => Promise<any>;
    /** 处理接收到的请求 */
    handleRequest: (event: string, callback: (params: any) => void) => Promise<any>;
    sendStreamRequest: (event: string, data: any) => AsyncIterable<any>;
}

const defaultState: KernelContextType = {
    isReady: false,
    sendRequest: () => Promise.resolve(),
    handleRequest: () => Promise.resolve(),
    sendStreamRequest: async function* () {},
};

const kernelContext = createContext<KernelContextType>(defaultState);

export const useKernel = () => useContext(kernelContext);

const timer = {
    setTimeout: window.setTimeout.bind(window),
    clearTimeout: window.clearTimeout.bind(window),
};

export interface KernelProviderProps {
    children?: preact.ComponentChildren;
}

export const KernelProvider = ({children, ...props}: KernelProviderProps) => {
    const [isReady, setIsReady] = useState(false);
    const [client, setClient] = useState<PTMessageClient>();
    const [server, setServer] = useState<PTMessageServer>();

    // 创建transport对象来处理消息发送
    const transport = useMemo(
        () => ({
            send: (data: PassthroughMessageParams<any> | PassthroughMessageResponse<any>) => {
                messageHandler.send(
                    EventMessage.PassthroughMessageToIDEFromWebviewEvent,
                    data
                );
            },
        }),
        []
    );

    const webviewInit = useCallback(
        async (messageClient: PTMessageClient) => {
            try {
                await messageClient.send(PT_WEBVIEW_SYSTEM_INITIALIZED_REQUEST, {time: new Date()});
                setIsReady(true);
            }
            catch (error) {
                console.error('webview init send error', error);
            }
        },
        []
    );

    useEffect(
        () => {
            const messageClient = new PTMessageClient(transport, timer);
            const messageServer = new PTMessageServer(transport);
            setClient(messageClient);
            setServer(messageServer);
            webviewInit(messageClient);
            messageServer.register(PT_KERNEL_SYSTEM_INITIALIZED_REQUEST, () => {
                console.log('PT_KERNEL_SYSTEM_INITIALIZED_REQUEST');
                setIsReady(true);
            });
        },
        [transport, webviewInit]
    );

    const sendRequest = useCallback(
        async <R = any>(event: string, data: any): Promise<R> => {
            if (!isReady) {
                return Promise.reject('kernel is not ready');
            }
            if (!client) {
                return Promise.reject('client not initialized');
            }
            return client.send(event, data);
        },
        [isReady, client]
    );

    const sendStreamRequest = useCallback(
        async function* <T = any, R = any>(event: string, data: any): AsyncGenerator<R> {
            if (!isReady) {
                throw new Error('kernel is not ready');
            }
            if (!client) {
                throw new Error('client not initialized');
            }

            const iterator = client.streamSend<T, R>(event, data);

            for await (const chunk of iterator) {
                yield chunk.data;
            }
        },
        [isReady, client]
    );


    const handleRequest = useCallback(
        async < R = any>(event: string, callback): Promise<R> => {
            if (!server) {
                return Promise.reject('server not initialized');
            }
            try {
                server.register(event, callback);
                return Promise.resolve() as Promise<R>;
            } catch (error) {
                console.error('handle request error:', error);
                return Promise.reject(error);
            }
        },
        [server]
    );

    useEffect(
        () => {
            const handler = (e: PassthroughMessageResponse<unknown> | PassthroughMessageParams<unknown>) => {
                if (e.type === 'request') {
                    server?.handleRequest(e);
                }
                else {
                    client?.handleResponse(e);
                }
            };

            messageHandler.listen(
                EventMessage.PassthroughMessageToWebviewFromIDEEvent,
                handler
            );

        },
        [client, server]
    );

    return (
        <kernelContext.Provider
            value={{
                isReady,
                sendRequest,
                handleRequest,
                sendStreamRequest,
            }}
            {...props}
        >
            {children}
        </kernelContext.Provider>
    );
};

// 导出所有相关类型
export type {
    PassthroughMessageResponse,
    PassthroughMessageParams,
    PTMessageClient,
    PTMessageServer,
};
