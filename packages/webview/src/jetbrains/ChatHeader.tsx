/* bca-disable */
import {useCallback, useEffect, useState, useMemo} from 'preact/hooks';
import {DehydratedMessage, EventMessage, VPC_PLUGIN_CONFIG_LINK} from '@shared/protocols';
import platform from '@shared/platform';
import {partial} from 'lodash';
import {messageHandler} from '@/utils/messageHandler';
import {openLink} from '@/actions';
import Menu from '@/components/Tabs/Menu';
import ChatTab, {ChatTabKey} from '@/components/Tabs/ChatTab';
import {isSaas} from '@/utils/features';
import {useExtensionConfig} from '@/hooks/useExtensionConfig';
import {useChatTabContext} from '@/hooks/useChatTab';
import {isVS} from '@/config';
import sqlIcon from '@/assets/SQLForm.svg';

// eslint-disable-next-line max-len
const enabledBtnStyle =
    'cursor-pointer font-[var(--vscode-icon-foreground)] hover:bg-[var(--comate-icon-hoverBackground)]';
const disabledBtnStyle = 'cursor-default opacity-60';

const pluginConfigurationLink = isSaas ? 'https://comate.baidu.com/cop' : 'https://comate.baidu-int.com/cop';

interface Props {
    messages: DehydratedMessage[];
    onClearMessages: () => void;
    onSQLClick: () => void;
    isLogin: boolean;
    username: string;
    createNewChat: (messagesLength: number) => void;
    handleHistoryChat: () => void;
}

// eslint-disable-next-line complexity
function JetBrainsChatHeader({messages, onClearMessages, onSQLClick, isLogin, username, createNewChat,handleHistoryChat}: Props) {
    const {config: {enablePrivateService, privateServiceHost, isPoc}} = useExtensionConfig();
    const {activeTabKey, setActiveTabKey} = useChatTabContext();

    const pluginConfigLink = useMemo(
        () => {
            return enablePrivateService ? privateServiceHost + VPC_PLUGIN_CONFIG_LINK : pluginConfigurationLink;
        },
        [privateServiceHost, enablePrivateService]
    );
    // eslint-disable-next-line max-len
    const shouldShowClearButton = messages.length > 0 && !messages.some(item => item.status === 'inProgress')
        && isLogin;

    const [canUsePluginConfiguration, setCanUsePluginConfiguration] = useState(true);

    const handleClearMessages = useCallback(
        () => {
            if (shouldShowClearButton) {
                onClearMessages();
            }
        },
        [onClearMessages, shouldShowClearButton]
    );

    const handlePluginConfigurationClick = useCallback(
        () => {
            if (canUsePluginConfiguration && isLogin) {
                messageHandler.send(EventMessage.LinkClickEvent, pluginConfigLink);
            }
        },
        [canUsePluginConfiguration, isLogin, pluginConfigLink]
    );

    const handleSQLClick = useCallback(
        () => {
            onSQLClick();
        },
        [onSQLClick]
    );

    const handleSettingClick = useCallback(
        () => {
            messageHandler.send(EventMessage.SettingButtonClickEvent);
        },
        []
    );

    const handleActiveTabKeyChange = useCallback(
        (key: string) => {
            setActiveTabKey(key as ChatTabKey);
        },
        [setActiveTabKey]
    );

    useEffect(
        () => {
            if ($features.PLATFORM !== 'poc') {
                messageHandler.listen(EventMessage.PluginConfigurationChangeEvent, status => {
                    setCanUsePluginConfiguration(status);
                });
            }
        },
        []
    );
    return (
        // eslint-disable-next-line max-len
        <header className="px-3 pt-1 text-xs flex items-center justify-between relative bg-[var(--comate-editor-background)] border-b border-[var(--comate-panel-border,#2B2C2A)]">
            <div className="bg-[var(--comate-editor-background)] z-10">
                <ChatTab
                    activeKey={activeTabKey}
                    onChange={handleActiveTabKeyChange}
                    username={username}
                    isLogin={isLogin}
                />
            </div>
            <ul className="m-0 ml-auto flex items-center gap-1 absolute right-3">
                <li className="list-none">
                    <div className={`rounded-[5px] ${enabledBtnStyle}`}>
                        <div
                            className="m-[3px] codicon codicon-add"
                            onClick={() => createNewChat(messages.length)}
                        />
                    </div>
                </li>
                <li className="list-none">
                    <div className={`rounded-[5px] ${enabledBtnStyle}`}>
                        <div
                            className="m-[3px] codicon codicon-history"
                            onClick={() => handleHistoryChat()}
                        />
                    </div>
                </li>
                {!isPoc && (
                    <li className="list-none">
                        <div className={`rounded-[5px] ${enabledBtnStyle}`}>
                            <div
                                className="m-[3px] codicon codicon-question"
                                onClick={partial(openLink, platform.resolve('helpDocUrl'))}
                            />
                        </div>
                    </li>
                )}
                {!isPoc && !enablePrivateService && (
                    <li className="list-none">
                        <div className={`rounded-[5px] ${enabledBtnStyle}`}>
                            <div
                                className="m-[3px] codicon codicon-feedback"
                                onClick={partial(openLink, platform.resolve('feedbackUrl'))}
                            />
                        </div>
                    </li>
                )}
                {(!isPoc && !isVS) && (
                    <li className="list-none">
                        <div className={`rounded-[5px] ${enabledBtnStyle}`}>
                            <div
                                className={`m-[3px] codicon codicon-extensions ${
                                    (canUsePluginConfiguration && isLogin) ? enabledBtnStyle : disabledBtnStyle
                                }`}
                                onClick={handlePluginConfigurationClick}
                            />
                        </div>
                    </li>
                )}
                {/* 生成sql的功能只开放给poc的JetBrains */}
                {isPoc && isLogin && $features.WEBVIEW_CONSUMER === 'jetbrains' && (
                    <li className="list-none">
                        <div className={`rounded-[5px] ${enabledBtnStyle}`}>
                            <div
                                className="p-[3px] w-4 h-4 box-content"
                                dangerouslySetInnerHTML={{__html: sqlIcon}}
                                onClick={handleSQLClick}
                            />
                        </div>
                    </li>
                )}
                <li className="list-none">
                    <div className={`rounded-[5px] ${enabledBtnStyle}`}>
                        <div
                            className="m-[3px] codicon codicon-gear"
                            onClick={handleSettingClick}
                        />
                    </div>
                </li>
                <li className="list-none">
                    {/* eslint-disable-next-line max-len */}
                    <div className={`rounded-[5px] ${shouldShowClearButton ? enabledBtnStyle : disabledBtnStyle}`}>
                        <div
                            className="m-[3px] codicon codicon-clear-all"
                            onClick={handleClearMessages}
                        />
                    </div>
                </li>
                {isLogin && isSaas && (
                    <li className="list-none">
                        <Menu username={username} relative isLogin={isLogin} />
                    </li>
                )}
            </ul>
        </header>
    );
}

export default JetBrainsChatHeader;
