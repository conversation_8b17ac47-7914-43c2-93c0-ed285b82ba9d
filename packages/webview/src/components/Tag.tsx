
interface ButtonProps {
    color: string;
    children: string;
}

const colorMapping = {
    blue: ['text-[var(--comate-link-color)]', 'rgba(3, 202, 238, .1)'],
    gold: ['text-[#EDD36A] ', 'rgba(237, 211, 106, 0.1)'],
};

export default function Tag({children, color}: ButtonProps) {
    const [textColor, backgroundColor] = colorMapping[color];

    return (
        <div
            className={`${textColor} rounded px-1 py-[1px] text-[10px]`}
            style={{backgroundColor: backgroundColor}}
        >
            {children}
        </div>
    );
}
