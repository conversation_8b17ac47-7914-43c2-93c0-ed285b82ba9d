import {EventMessage} from '@shared/protocols';
import {useCallback} from 'preact/hooks';
import {messageHandler} from '@/utils/messageHandler';
import CodeBlock from './Markdown/CodeBlock';
import Header from './Markdown/Header';

interface Props {
    filePath: string;
    // 代码块内容
    code: string;
    language: string;
    // 默认展示几行，剩下的处于收起状态
    numLinesToShow?: number;
    // 第一行的行号，如果不传则不展示行号
    startLine: number;
    endLine: number;
}

function CodeChunk({
    code,
    language,
    numLinesToShow = 3,
    startLine,
    endLine,
    filePath,
}: Props) {
    const handleOpenFile = useCallback(
        () => {
            messageHandler.send(
                EventMessage.ShowCodeBlockEvent,
                {
                    filePath,
                    startLine,
                    endLine,
                }
            );
        },
        [endLine, filePath, startLine]
    );

    const fileName = filePath.split('/').pop();
    const lines = (
        <span className="min-w-fit text-[var(--comate-descriptionForeground)]">
            Lines {startLine + 1}-{endLine + 1}
        </span>
    );

    return (
        <CodeBlock
            showActions
            showHeader
            noMargin
            inline={false}
            startLine={startLine}
            header={<Header title={fileName} extra={lines} onClick={handleOpenFile} />}
            className={`language-${language}`}
            numLinesToShow={numLinesToShow}
        >
            {code}
        </CodeBlock>
    );
}

export default CodeChunk;
