import {createContext} from 'preact';
import {useContext, useMemo, useState} from 'preact/compat';
import {UserQueryPromptType} from '@shared/protocols';

interface LastQueryContext {
    query: UserQueryPromptType;
    setQuery: React.Dispatch<React.SetStateAction<any>>;
}

// 创建 LastQueryContext
const LastQueryContext = createContext<LastQueryContext>(
    {
        query: {
            prompt: '',
            needContext: false,
            type: undefined,
            agent: '',
            slash: '',
            knowledgeList: [],
            messageOrder: {},
            supportAt: true,
            /** 是否开启对话意图识别 */
            chatIntentRecognition: true,
            responseReminder: '',
        },
        setQuery: () => {},
    }
);

// 创建 LastQueryProvider 组件
export const LastQueryProvider = ({children}) => {
    const [query, setQuery] = useState<UserQueryPromptType>({
        prompt: '',
        needContext: false,
        type: undefined,
        agent: '',
        slash: '',
        knowledgeList: [],
        messageOrder: {},
        supportAt: true,
        /** 是否开启对话意图识别 */
        chatIntentRecognition: false,
        responseReminder: '',
    });

    const value = useMemo(
        () => ({query, setQuery}),
        [query, setQuery]
    );

    return (
        <LastQueryContext.Provider value={value}>
            {children}
        </LastQueryContext.Provider>
    );
};

export const useLastQuery = () => {
    const context = useContext(LastQueryContext);

    return context.query;
};

export const useSetLastQuery = () => {
    const context = useContext(LastQueryContext);

    return context.setQuery;
};
