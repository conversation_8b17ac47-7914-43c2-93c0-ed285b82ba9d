.pagination-data {
    padding: 0;
    margin: 0;
}

.pagination-data li {
    list-style: none;
}

.rc-pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.rc-pagination-item-active {
    border: 1px solid #C7C7C77F !important;
}

.rc-pagination-item:hover,
.rc-pagination-prev button:hover,
.rc-pagination-next button:hover,
.rc-pagination-total-text a:hover,
.rc-pagination-total-text button:hover {
    border-radius: 2px;
    border: 1px solid #C7C7C77F;
    cursor: pointer;
}

.rc-pagination-next {
    margin-left: 8px;
    margin-right: 16px;
}

.rc-pagination-prev {
    margin-right: 4px;
}

.rc-pagination-prev button:after,
.rc-pagination-next button:after {
    content: none;
}

.rc-pagination-jump-next button,
.rc-pagination-jump-prev button,
.rc-pagination-item {
    min-width: 14px;
    height: 14px;
    border: 1px solid transparent;
    margin-left: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.rc-pagination-item a,
.rc-pagination-item button,
.rc-pagination-prev button,
.rc-pagination-next button,
.rc-pagination-total-text a,
.rc-pagination-total-text button {
    width: 100%;
    height: 100%;
    border: 1px solid transparent;
    padding: 0 4px;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #656f84 !important;
    box-sizing: border-box;
    white-space: nowrap;
    line-height: 1;
}

.rc-pagination-jump-next button:after,
.rc-pagination-jump-prev button:after {
    display: block;
    content: "•••";
}

.rc-pagination-jump-next button,
.rc-pagination-jump-prev button {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    cursor: pointer;
    color: #656f84;
}

.rc-pagination-disabled button:hover {
    cursor: not-allowed;
    /* 设置鼠标样式为禁用图标 */
    border: 1px solid transparent;
}