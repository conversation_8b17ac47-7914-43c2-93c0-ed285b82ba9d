// eslint-disable-next-line max-len
import RcPagination, {PaginationProps as RcPaginationProps} from 'rc-pagination';
import {FC} from 'preact/compat';
import './index.css';

interface ReassignProps {
    total: number;
    pageSize: number;
    current: number;
    itemRender: (
        page: number,
        type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next',
        element: React.ReactNode
    ) => React.ReactNode;
    onChange: (page: number, pageSize: number) => void;
}

export type PaginationProps = Omit<RcPaginationProps, keyof ReassignProps> & ReassignProps;
const Pagination = RcPagination as FC<PaginationProps>;


export default ({total, pageSize, current, itemRender, onChange}: PaginationProps) => {


    return (
        <Pagination
            className="pagination-data"
            align="end"
            onChange={onChange}
            total={total}
            current={current}
            pageSize={pageSize}
            showSizeChanger={false}
            itemRender={itemRender}
            showLessItems
        />
    );
};
