/* bca-disable */
import {ReactNode, useCallback} from 'preact/compat';
import rightIcon from '@/assets/right.svg';
import Loading from '@/components/Loading';
import {useDerivedState} from '../Tabs/useDerivedState';
import './AgentCollapsePanel.css';

interface Props {
    collapsible: boolean;
    title: string;
    content: string | ReactNode;
    expandIcon?: ReactNode;
    loading?: boolean;
    loadingText?: string;
    onChange?: (collapsible: boolean) => void;
}

export default function AgentCollapsePanel({
    collapsible,
    title,
    content,
    expandIcon,
    loading,
    loadingText,
    onChange,
}: Props) {
    const [isCollapsed, setIsCollapsed] = useDerivedState(collapsible);
    const toggleCollapse = useCallback(
        () => {
            setIsCollapsed(!isCollapsed);
            onChange && onChange(!isCollapsed);
        },
        [isCollapsed, setIsCollapsed, onChange]
    );

    return (
        /* eslint-disable max-len */
        <div className="panel-content">
            <div className="flex items-center justify-between cursor-pointer" onClick={toggleCollapse}>
                <div className="flex items-center gap-1 font-bold">
                    {expandIcon}
                    {title}
                </div>
                <div className="flex items-center gap-2">
                    {isCollapsed && (
                        <div className="flex items-center gap-1">
                            {loading && <Loading />}
                            {loading && loadingText && (
                                <span className="text-[var(--comate-link-color)]">{loadingText}</span>
                            )}
                        </div>
                    )}
                    <div>
                        {isCollapsed
                            ? <div className="w-3.5 h-3.5 rotate-90" dangerouslySetInnerHTML={{__html: rightIcon}} />
                            : <div className="w-3.5 h-3.5 -rotate-90" dangerouslySetInnerHTML={{__html: rightIcon}} />}
                    </div>
                </div>
            </div>
            {!isCollapsed && <div>{content}</div>}
        </div>
    );
}
