/* bca-disable */
import {useCallback} from 'preact/hooks';
import {ComponentChild} from 'preact';
import {ReactNode} from 'preact/compat';
import rightIcon from '@/assets/right.svg';

interface CollapseProps {
    title: ReactNode;
    children: ComponentChild;
    collapsed: boolean;
    noPadding?: boolean;
    onCollapsedChange: (collapsed: boolean) => void;
}

export default function CollapsePanel({
    title,
    children,
    collapsed,
    onCollapsedChange,
    noPadding = false,
}: CollapseProps) {
    const handleClick = useCallback(
        () => {
            onCollapsedChange(!collapsed);
        },
        [collapsed, onCollapsedChange]
    );

    return (
        <div className="flex flex-col">
            <div
                className="text-[13px] flex items-center gap-1 opacity-70 hover:cursor-pointer w-full"
                onClick={handleClick}
            >
                <div>
                    {collapsed
                        ? <div className="w-3.5 h-3.5" dangerouslySetInnerHTML={{__html: rightIcon}} />
                        : <div className="w-3.5 h-3.5 rotate-90" dangerouslySetInnerHTML={{__html: rightIcon}} />}
                </div>
                {title}
            </div>
            {collapsed
                ? null
                : (
                    <div className={`mt-3 ${noPadding ? 'mx-0' : 'ml-5'}`}>
                        {children}
                    </div>
                )}
        </div>
    );
}
