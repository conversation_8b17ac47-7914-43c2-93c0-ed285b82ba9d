/* eslint-disable max-len */
import {CSSProperties} from 'preact/compat';
import {CommonText} from '@/i18n/constants';
import {useTranslation} from '@/i18n/useTranslation';
import rightIcon from '@/assets/right.svg';

interface Props {
    collapsed: boolean;
    style?: CSSProperties;
    toggle: () => void;
    shouldCenter?: boolean;
}

export default function CollapseButton({collapsed, style, toggle, shouldCenter}: Props) {
    const {t} = useTranslation();
    return (
        <div
            style={style}
            className={`w-full text-xs text-[var(--comate-descriptionForeground)] flex bottom-2 ${
                shouldCenter ? 'justify-center' : ''
            }`}
            role="presentation"
        >
            <button onClick={toggle} className="flex justify-center items-center gap-1">
                {collapsed ? t(CommonText.EXPAND) : t(CommonText.COLLAPSE)}
                <span
                    className={`w-3.5 h-3.5 ${collapsed ? 'rotate-90' : '-rotate-90'}`}
                    dangerouslySetInnerHTML={{__html: rightIcon}}
                />
            </button>
        </div>
    );
}
