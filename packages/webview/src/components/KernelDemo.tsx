/* eslint-disable complexity */
import {useEffect, useState} from 'preact/hooks';
import {
    PT_KERNEL_DEMO_REQUEST,
    PT_WEBVIEW_DEMO_REQUEST,
    PT_WEBVIEW_STREAM_DEMO_REQUEST,
} from '@comate/kernel-shared/browser';
import {useKernel} from '@/hooks/useKernel';

export default function KernelDemo() {
    const {isReady, sendRequest, handleRequest, sendStreamRequest} = useKernel();
    const [inputValue, setInputValue] = useState('');
    const [requestData, setRequestData] = useState('');
    const [responseData, setResponseData] = useState('');
    const [kernelRequestData, setKernelRequestData] = useState('');

    const handleSend = async () => {
        if (!inputValue.trim()) {
            return;
        }

        try {
            setRequestData(inputValue);
            const response = await sendRequest(PT_WEBVIEW_DEMO_REQUEST, inputValue);
            setResponseData(JSON.stringify(response, null, 2));
            setInputValue('');
        }
        catch (error) {
            console.error('Send request failed:', error);
            setResponseData(`Error: ${error}`);
        }
    };

    const handleStreamSend = async () => {
        if (!inputValue.trim()) {
            return;
        }
        setRequestData(inputValue);
        setResponseData('');
        setInputValue('');
        for await (const element of sendStreamRequest(PT_WEBVIEW_STREAM_DEMO_REQUEST, {})) {
            if (element) {
                setResponseData(e => e + JSON.stringify(element, null, 2));
            }
        }
    };

    useEffect(
        () => {
            handleRequest(PT_KERNEL_DEMO_REQUEST, e => {
                setKernelRequestData(JSON.stringify(e, null, 2));
            });
        },
        [handleRequest]
    );

    return (
        <div>
            <div>
                Kernel 状态：{isReady ? '连上了🟢' : '断了🔴'}
            </div>

            <div className="mt-5">
                <input
                    type="text"
                    value={inputValue}
                    onChange={e => setInputValue(e.currentTarget.value)}
                    placeholder="请输入要发送的内容"
                    className="px-2 py-2 mr-3 border rounded"
                />
                <button
                    onClick={handleSend}
                    disabled={!isReady || !inputValue.trim()}
                    className={`px-4 py-2 rounded ${
                        isReady && inputValue.trim()
                            ? 'bg-blue-500 text-white hover:bg-blue-600 cursor-pointer'
                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                >
                    发送
                </button>
                <button
                    onClick={handleStreamSend}
                    disabled={!isReady || !inputValue.trim()}
                    className={`px-4 py-2 rounded ${
                        isReady && inputValue.trim()
                            ? 'bg-blue-500 text-white hover:bg-blue-600 cursor-pointer'
                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                >
                    流式消息
                </button>
            </div>

            {requestData && (
                <div className="mt-5">
                    <h3 className="font-medium">发送内容：</h3>
                    <pre className="p-3 rounded-md mt-2">
                        {requestData}
                    </pre>
                </div>
            )}

            {responseData && (
                <div className="mt-5">
                    <h3 className="font-medium">返回内容：</h3>
                    <pre className="p-3 rounded-md mt-2">
                        {responseData}
                    </pre>
                </div>
            )}

            {kernelRequestData && (
                <div className="mt-5">
                    <h3 className="font-medium">Kernel 的 Ping：</h3>
                    <pre className="p-3 rounded-md mt-2">
                        {kernelRequestData}
                    </pre>
                </div>
            )}
        </div>
    );
}
