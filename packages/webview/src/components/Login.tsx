/* eslint-disable complexity */
/* eslint-disable max-len */
/* bca-disable */
import {forwardRef} from 'preact/compat';
import {memo, useCallback, useRef, useEffect, useImperativeHandle} from 'preact/compat';
import {EventMessage} from '@shared/protocols';
import {useTranslation} from 'react-i18next';
import {showLicenseLoginVisible} from '@shared/utils/features';
import {
    WELCOME_LOGIN_TEXT,
    WELCOME_LOGIN_WITH_LICENSE,
} from '@/i18n/constants';
import {messageHandler} from '@/utils/messageHandler';
import {useExtensionConfig} from '@/hooks/useExtensionConfig';

const setLicense = () => messageHandler.send(EventMessage.OpenLicenseConfigEvent);

const Login = forwardRef(function Login(_, ref) {
    const {t} = useTranslation();
    const showLicenseLogin = showLicenseLoginVisible();
    const loginButtonRef = useRef<HTMLButtonElement>(null);
    const {config: {isPoc}} = useExtensionConfig();

    const focusLoginButton = useCallback(
        () => {
            if (loginButtonRef.current) {
                loginButtonRef.current.focus();
            }
        },
        []
    );

    const login = useCallback(
        () => {
            if (isPoc) {
                messageHandler.send(EventMessage.OpenLicenseConfigEvent);
            }
            else {
                messageHandler.send(EventMessage.LoginTriggerEvent);
            }
        },
        [isPoc]
    );

    useEffect(
        () => {
            focusLoginButton();
        },
        [focusLoginButton]
    );

    useImperativeHandle(ref, () => ({
        focusLoginButton,
    }));

    return (
        <div className="relative flex-1 w-full">
            <div className="absolute top-[20%] left-0 w-full px-4 flex gap-5 flex-col items-center">
                <button
                    aria-live="assertive"
                    ref={loginButtonRef}
                    onClick={login}
                    // eslint-disable-next-line max-len
                    className="text-white w-full mx-auto max-w-[180px] text-sm py-2 h-fit rounded-[4px] bg-[linear-gradient(88.14deg,#0091FF_0%,#03F5E8_100%)]"
                >
                    {t(WELCOME_LOGIN_TEXT)}
                </button>
                {!isPoc && showLicenseLogin && (
                    <div
                        className="flex text-sm gap-1.5 text-[var(--comate-link-color)] hover:cursor-pointer hover:opacity-80"
                        onClick={setLicense}
                    >
                        <span>{t(WELCOME_LOGIN_WITH_LICENSE)}</span>
                    </div>
                )}
            </div>
        </div>
    );
});

export default memo(Login);
