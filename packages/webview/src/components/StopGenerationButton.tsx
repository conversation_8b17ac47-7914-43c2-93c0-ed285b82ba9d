/* bca-disable */
import {useCallback} from 'preact/hooks';
import {useTranslation} from 'react-i18next';
import {DehydratedMessage} from '@shared/protocols';
import cx from 'classnames';
import {STOP_GENERATION_TEXT} from '@/i18n/constants';
import cancelIcon from '@/assets/cancel.svg';
import {useChatConfig} from './ChatConfigProvider';
import {formatOrder} from './ChatBox';

export function StopGenerationButtonUI(
    {id, onCancel, className}: {className?: string, id: number, onCancel: (messageId: number) => void}
) {
    const {t} = useTranslation();

    const handleCancel = useCallback(
        () => {
            onCancel(id);
        },
        [id, onCancel]
    );

    return (
        <div className={cx('flex justify-center', className)}>
            <div className="text-[var(--comate-descriptionForeground)] bg-[#888888]/20 rounded px-3 py-1">
                <button className="cursor-pointer flex gap-1 items-center hover:opacity-80" onClick={handleCancel}>
                    <span className="w-4 h-4" dangerouslySetInnerHTML={{__html: cancelIcon}}></span>
                    {t(STOP_GENERATION_TEXT)}
                </button>
            </div>
        </div>
    );
}

interface Props {
    messages: DehydratedMessage[];
    onCancel: (messageId: number) => void;
}

function StopGenerationButton({messages, onCancel}: Props) {
    const {messageOrder} = useChatConfig();
    const replyTo = messages[0]?.replyTo;
    const order = formatOrder(messages, typeof replyTo === 'number' ? messageOrder[replyTo] : undefined);

    return <StopGenerationButtonUI id={messages[order].id} onCancel={onCancel} className="mb-4" />;
}

export default StopGenerationButton;
