/* eslint-disable max-len */
/* bca-disable */

interface Props {
    title: string;
    url: string;
    content: string;
    onClick: () => void;
}

const numLinesToShow = 3;

export default function KnowledgeChunk({
    title,
    url,
    content,
    onClick,
}: Props) {
    const lines = content.split('\n');
    const shouldCollapse = lines.length > numLinesToShow;
    const displayContent = shouldCollapse ? lines.slice(0, numLinesToShow).join('\n') + ' ...' : content;

    return (
        <div className="flex flex-col">
            <div
                className={`text-[var(--comate-link-color)]${url ? ' cursor-pointer' : ''} mb-1`}
                onClick={url ? onClick : () => {}}
            >
                {title}
            </div>
            {/* 兼容检索内容中出现html标签的情况 */}
            <div
                className="opacity-80 whitespace-pre-wrap mb-1"
                key={content}
                dangerouslySetInnerHTML={{__html: displayContent}}
            />
        </div>
    );
}
