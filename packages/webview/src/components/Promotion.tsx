import {ReactNode} from 'preact/compat';
import {useTranslation} from 'react-i18next';
import bellIcon from '@/assets/bell.png';
import {PROMOTION_ACTION_TEXT} from '@/i18n/constants';

interface Props {
    text: ReactNode;
    onClick: () => void;
}

export default function Promotion({text, onClick}: Props) {
    const {t} = useTranslation();
    return (
        // eslint-disable-next-line max-len
        <div className="text-[13px] flex justify-between items-center w-full mt-[4px] mb-[30px] py-[7px] px-3 rounded gap-1 text-[var(--vscode-menu-foreground)] outline outline-1 outline-[var(--vscode-menu-border)] bg-[#0973e314]">
            <p className="truncate whitespace-nowrap flex items-center">
                <img src={bellIcon} className="inline-block w-4 my-[2px] mr-[8px]" />
                {text}
            </p>
            <span
                className="text-[var(--comate-link-color)] ml-[2px] hover:cursor-pointer flex-shrink-0"
                onClick={onClick}
            >
                {t(PROMOTION_ACTION_TEXT)}
            </span>
        </div>
    );
}
