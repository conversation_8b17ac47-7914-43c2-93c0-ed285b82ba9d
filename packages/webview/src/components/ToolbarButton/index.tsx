/* bca-disable */
import {HTMLAttributes, ReactNode} from 'preact/compat';
import cx from 'classnames';
import {TooltipProps} from 'rc-tooltip/lib/Tooltip';
import {pickBy} from 'lodash';
import Tooltip from '../Tooltip';
import './index.css';

interface Props {
    icon: string;
    iconClassName?: string;
    selected?: boolean;
    label?: ReactNode;
    disabled?: boolean;
    className?: string;
    title?: ReactNode;
    showArrow?: boolean;
    placement?: TooltipProps['placement'];
    mouseLeaveDelay?: number;
    mouseEnterDelay?: number;
    getTooltipContainer?: TooltipProps['getTooltipContainer'];
    onClick?: HTMLAttributes<HTMLButtonElement>['onClick'];
    ariaTitle?: string;
}

export default function ToolbarButton({
    icon,
    label,
    disabled,
    title,
    onClick,
    selected,
    className,
    iconClassName,
    placement,
    showArrow,
    mouseLeaveDelay,
    mouseEnterDelay,
    getTooltipContainer,
    ariaTitle,
    ...props
}: Props) {
    const ariaProps: HTMLAttributes<HTMLButtonElement> = pickBy({
        'aria-disabled': disabled,
        'aria-selected': selected,
    }, value => !!value);

    const children = (
        <div {...props}>
            <button
                onClick={disabled ? undefined : onClick}
                className={cx('toolbar-button', {'toolbar-button-labeled': !!label}, className)}
                aria-label={ariaTitle}
                {...ariaProps}
                role="button"
            >
                <div
                    className={cx('toolbar-button-icon', iconClassName)}
                    dangerouslySetInnerHTML={{__html: icon}}
                    aria-hidden="true"
                />
                {label && <span className="toolbar-button-label" aria-hidden="true">{label}</span>}
            </button>
        </div>
    );

    if (!title) {
        return children;
    }

    return (
        <Tooltip
            overlay={title}
            showArrow={showArrow}
            trigger={['hover']}
            placement={placement}
            getTooltipContainer={getTooltipContainer}
            mouseLeaveDelay={mouseLeaveDelay}
            mouseEnterDelay={mouseEnterDelay}
        >
            <span aria-hidden="true">{children}</span>
        </Tooltip>
    );
}
