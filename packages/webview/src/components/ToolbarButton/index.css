.toolbar-button {
    display: flex;
    align-items: center;
    min-width: 18px;
    cursor: pointer;
    height: 20px;
    border: 1px solid transparent;
    padding: 0 1px;
    border-radius: 2px;
}

.toolbar-button a:hover {
    color: inherit;
}

.toolbar-button a {
    outline: none !important;
}

.toolbar-button[aria-disabled] {
    cursor: not-allowed;
    color: var(--comate-disabled-iconColor);
}

.toolbar-button[aria-selected] {
    position: relative;
    background-image: linear-gradient(to right,#1887E2,var(--comate-link-color));
    background-origin: border-box;
    box-shadow: inset 0 100vw #1E344B;
}

.toolbar-button[aria-disabled] * {
    pointer-events: none;
}

.toolbar-button-icon {
    width: 16px;
    height: 16px;
    box-sizing: content-box;
}

.toolbar-button-label {
    margin-left: 4px;
    line-height: 16px;
}

.toolbar-button:not([aria-disabled]):not([aria-selected]):hover {
    background: var(--comate-icon-hoverBackground);
}
