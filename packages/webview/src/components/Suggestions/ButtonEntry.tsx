/* bca-disable */
import {useCallback} from 'preact/hooks';
import {useTranslation} from 'react-i18next';
import wandIcon from '@/assets/wand.svg';
import utIcon from '@/assets/ut.svg';
import docIcon from '@/assets/doc.svg';
import explainIcon from '@/assets/explain.svg';
import gearIcon from '@/assets/gear.svg';
import {Suggestion} from '@/interface';

const ICON_MAPPING = {
    ut: utIcon,
    doc: docIcon,
    utSetting: gearIcon,
    explain: explainIcon,
    default: wandIcon,
};

interface Props {
    item: Suggestion;
    onClick: (id: Suggestion['id']) => void;
}

function ButtonEntry({item, onClick}: Props) {
    const {displayTextKey, type} = item;
    const {t} = useTranslation();

    const handleClick = useCallback(
        () => {
            onClick(item.id);
        },
        [item.id, onClick]
    );

    return (
        <button
            // eslint-disable-next-line max-len
            className="text-left inline-block px-3 py-0.5 min-w-[176px] bg-gray-500/20 text-[var(--comate-descriptionForeground)] rounded whitespace-nowrap hover:cursor-pointer hover:opacity-80"
            onClick={handleClick}
        >
            <span className="w-4 h-[13px] mr-1.5 inline-block" dangerouslySetInnerHTML={{__html: ICON_MAPPING[type]}} />
            {t(displayTextKey)}
        </button>
    );
}

export default ButtonEntry;
