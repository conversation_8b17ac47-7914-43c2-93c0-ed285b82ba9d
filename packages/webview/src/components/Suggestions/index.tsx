import {Suggestion} from '@/interface';
import TextEntry from './TextEntry';
import ButtonEntry from './ButtonEntry';

interface Props {
    items: Suggestion[];
    onClick: (id: Suggestion['id']) => void;
    mode?: 'text' | 'button';
}

function Suggestions({items, onClick, mode = 'text'}: Props) {
    if (items.length === 0 || items.some(item => item.displayTextKey === '')) {
        return null;
    }

    if (mode === 'button') {
        return (
            <div className="flex flex-wrap gap-1 mr-2">
                {items.map(item => <ButtonEntry key={item.id} item={item} onClick={onClick} />)}
            </div>
        );
    }

    return (
        <div className={'flex justify-center flex-col gap-2 w-full mb-3'}>
            {items.map(item => <TextEntry key={item.id} item={item} onClick={onClick} />)}
        </div>
    );
}

export default Suggestions;
