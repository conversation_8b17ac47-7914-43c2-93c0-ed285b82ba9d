/* bca-disable */
import {useCallback} from 'preact/hooks';
import {useTranslation} from 'react-i18next';
import wandIcon from '@/assets/wand.svg';
import utIcon from '@/assets/ut.svg';
import docIcon from '@/assets/doc.svg';
import {Suggestion} from '@/interface';

const ICON_MAPPING = {
    ut: utIcon,
    doc: docIcon,
    help: docIcon,
    default: wandIcon,
};

interface Props {
    item: Suggestion;
    onClick: (id: Suggestion['id']) => void;
}

function TextEntry({item, onClick}: Props) {
    const {displayTextKey, type} = item;
    const {t} = useTranslation();

    const handleClick = useCallback(
        () => {
            onClick(item.id);
        },
        [item, onClick]
    );

    return (
        <div className="flex gap-1.5 text-[var(--comate-link-color)]">
            <span className="w-4 inline-flex" dangerouslySetInnerHTML={{__html: ICON_MAPPING[type]}}></span>
            <span
                className="truncate whitespace-nowrap hover:cursor-pointer hover:opacity-80"
                onClick={handleClick}
            >
                {t(displayTextKey)}
            </span>
        </div>
    );
}

export default TextEntry;
