/* eslint-disable max-len */
/* bca-disable */
import {JSX} from 'preact';
import {useCallback, useEffect, useRef, useState} from 'preact/hooks';
import {ChangeEvent, FC, ReactNode} from 'preact/compat';
import {EventMessage} from '@shared/protocols';
import RcSelect, {Option as RcOption, SelectProps as RcSelectProps} from 'rc-select';
import {OptionProps as RcOptionProps} from 'rc-select/lib/Option';
import 'rc-select/assets/index.css';
import closeIcon from '@/assets/close.svg';
import {messageHandler} from '@/utils/messageHandler';
import {useToast} from '@/hooks/useToast';
import './index.css';

interface ReassignProps {
    children: ReactNode;
}

export type SelectProps = Omit<RcSelectProps, keyof ReassignProps> & ReassignProps;
const Select = RcSelect as FC<SelectProps>;
export type OptionProps = Omit<RcOptionProps, keyof ReassignProps> & ReassignProps;
const Option = RcOption as FC<OptionProps>;

interface IProps {
    setVisible: (visible: boolean) => void;
}

const fieldNames = {
    dialect: '数据库类型',
    host: '数据库地址',
    port: '数据库端口',
    database: '数据库名称',
    username: '账号',
    password: '密码',
    path: '文件保存路径',
};

const FormButton = ({
    className,
    children,
    onClick,
}: {
    className?: string;
    children: JSX.Element | string;
    onClick: (visible: boolean) => void;
}) => (
    <button
        className={`${className} text-[#17C3E5] rounded bg-[rgba(23,195,229,0.15)] border border-solid border-[#17C3E5] hover:opacity-70`}
        onClick={() => onClick(true)}
        type="button"
    >
        {children}
    </button>
);

const FormTextField = ({
    className,
    value,
    onChange,
    ...restProps
}: {
    className?: string;
    value: any;
    onChange: (e: string) => void;
} & JSX.HTMLAttributes<HTMLInputElement>) => {
    const handleChange = useCallback(
        (e: ChangeEvent<HTMLInputElement>) => {
            onChange((e?.target as HTMLInputElement)?.value ?? '');
        },
        [onChange]
    );
    return (
        <div className={className}>
            <input
                {...restProps}
                className="w-full h-[24px] px-2 text-[#D4D4D4] placeholder:text-[rgba(212,212,212,0.5)] rounded bg-[var(--vscode-sideBar-background)] border border-[rgba(128,128,128,0.35)] focus:border-[#17C3E5] focus:outline-none focus:ring-0"
                placeholder={'请输入'}
                value={value}
                onChange={handleChange}
            />
        </div>
    );
};

interface Option {
    value: string;
    label: string;
}

interface SelectComponentProps {
    className: string;
    value: string;
    options: Option[];
    onChange?: (value: string) => void;
}

const FormSelect = ({className, value, options = [], onChange}: SelectComponentProps) => {
    const handleSelect = useCallback(
        (value: string) => {
            onChange?.(value);
        },
        [onChange]
    );

    return (
        <Select
            value={value}
            className={`${className} w-full h-[24px] text-[#D4D4D4] placeholder:text-[rgba(212,212,212
            ,255)] rounded bg-[var(--vscode-sideBar-background)] border border-[rgba(128,128,128,0.35)]
            focus:border-[#17C3E5] focus:outline-none focus:ring-0`}
            dropdownClassName="sql-form-item-dropdown"
            onSelect={handleSelect}
            placeholder="请选择"
        >
            {options.map(option => (
                <Option key={option.value} value={option.value}>{option.label}</Option>
            ))}
        </Select>
    );
};

export default ({setVisible}: IProps) => {
    const loading = useRef(false);
    const [actionText, setActionText] = useState('创建数据文件');

    const [dialect, setDialect] = useState<string>('MYSQL');
    const [host, setHost] = useState<string>('');
    const [port, setPort] = useState<string>('');
    const [database, setDatabase] = useState<string>('');
    const [username, setUsername] = useState<string>('');
    const [password, setPassword] = useState<string>('');
    const [path, setPath] = useState<string>('');

    const [dialectError, setDialectError] = useState<string>('');
    const [hostError, setHostError] = useState<string>('');
    const [portError, setPortError] = useState<string>('');
    const [databaseError, setDatabaseError] = useState<string>('');
    const [usernameError, setUsernameError] = useState<string>('');
    const [passwordError, setPasswordError] = useState<string>('');
    const [pathError, setPathError] = useState<string>('');

    const {toast} = useToast();

    const handleSubmit = useCallback(
        () => {
            const validate = async () => {
                let res = true;
                const fields = {
                    dialect: {value: dialect, setError: setDialectError},
                    host: {value: host, setError: setHostError},
                    port: {value: port, setError: setPortError},
                    database: {value: database, setError: setDatabaseError},
                    username: {value: username, setError: setUsernameError},
                    password: {value: password, setError: setPasswordError},
                    path: {value: path, setError: setPathError},
                };
                for (const key in fields) {
                    if (!fields[key].value) {
                        fields[key].setError(`${fieldNames[key]}不能为空`);
                        res = false;
                    }
                }
                if (!res) {
                    return Promise.reject(new Error('校验失败'));
                }
            };

            if (loading.current) {
                return;
            }
            setDialectError('');
            setHostError('');
            setPortError('');
            setDatabaseError('');
            setUsernameError('');
            setPasswordError('');
            setPathError('');

            validate().then(() => {
                setActionText('创建中，请稍后');
                loading.current = true;
                messageHandler.send(EventMessage.SubmitSqlConnectionEvent, {
                    dialect,
                    host,
                    port,
                    database,
                    username,
                    password,
                    path,
                }).then((res: any) => {
                    // setActionText(res.ok ? '创建成功' : '创建失败');
                    if (!res.ok) {
                        toast({
                            type: 'fail',
                            message: res.msg,
                        });
                    }
                }).finally(() => {
                    setActionText('创建数据文件');
                    loading.current = false;
                });
            }).catch(() => {});
        },
        [database, dialect, host, password, path, port, toast, username]
    );

    useEffect(
        () => {
            messageHandler.send(EventMessage.GetSqlConnectionEvent, {})
                .then((res: any) => {
                    res.dialect && setDialect(res.dialect);
                    res.host && setHost(res.host);
                    res.port && setPort(res.port);
                    res.database && setDatabase(res.database);
                    res.username && setUsername(res.username);
                    res.password && setPassword(res.password);
                    res.path && setPath(res.path);
                });
        },
        []
    );

    return (
        <div className="sql-container absolute top-0 w-full h-full p-4">
            <div className={'flex flex-row items-center justify-between mb-6'}>
                <div className={'text-[14px] leading-[22px] flex flex-row items-center'}>
                    SQL生成
                </div>
                <span
                    className="w-[16px] h-[16px] cursor-pointer"
                    onClick={() => setVisible(false)}
                    dangerouslySetInnerHTML={{__html: closeIcon}}
                >
                </span>
            </div>
            <div className="sql-form">
                <div className="sql-form-item mb-3">
                    <div className="flex">
                        <div className="sql-form-item-label shrink-0 basis-[102px] flex items-center">数据库类型：</div>
                        {/* <Select
                            className="sql-form-item-input min-w-0 basis-0 grow py-1 pl-3 pr-8 bg-[var(--vscode-input-background)] text-[var(--vscode-input-foreground)] focus:outline-[#6391F9]"
                            value={dialect}
                            onChange={setDialect}
                        >
                            <Select.Option value="MYSQL">mysql</Select.Option>
                            <Select.Option value="HIVE">hive</Select.Option>
                        </Select> */}
                        <FormSelect
                            className="sql-form-item-select min-w-0 basis-0 grow"
                            value={dialect}
                            options={[
                                {label: 'mysql', value: 'MYSQL'},
                                // {label: 'hive', value: 'HIVE'},
                            ]}
                            onChange={setDialect}
                        >
                            {/* <FormOption value="MYSQL">mysql</FormOption> */}
                            {/* <FormOption value="HIVE">hive</FormOption> */}
                        </FormSelect>
                    </div>
                    {dialectError && <div className="sql-form-item-error mt-1 text-[12px] text-[#E54552]">{dialectError}</div>}
                </div>
                <div className="sql-form-item mb-3">
                    <div className="flex align-center">
                        <div className="sql-form-item-label shrink-0 basis-[102px] flex items-center">数据库地址：</div>
                        <FormTextField className="sql-form-item-input min-w-0 basis-0 grow" value={host} onChange={setHost} />
                    </div>
                    {hostError && <div className="sql-form-item-error ml-[102px] mt-1 text-[12px] text-[#E54552]">{hostError}</div>}
                </div>
                <div className="sql-form-item mb-3">
                    <div className="flex">
                        <div className="sql-form-item-label shrink-0 basis-[102px] flex items-center">数据库端口：</div>
                        <FormTextField className="sql-form-item-input min-w-0 basis-0 grow" value={port} onChange={setPort} />
                    </div>
                    {portError && <div className="sql-form-item-error ml-[102px] mt-1 text-[12px] text-[#E54552]">{portError}</div>}
                </div>
                <div className="sql-form-item mb-3">
                    <div className="flex">
                        <div className="sql-form-item-label shrink-0 basis-[102px] flex items-center">数据库名称：</div>
                        <FormTextField className="sql-form-item-input min-w-0 basis-0 grow" value={database} onChange={setDatabase} />
                    </div>
                    {databaseError && <div className="sql-form-item-error ml-[102px] mt-1 text-[12px] text-[#E54552]">{databaseError}</div>}
                </div>
                <div className="sql-form-item mb-3">
                    <div className="flex">
                        <div className="sql-form-item-label shrink-0 basis-[102px] flex items-center">账号：</div>
                        <FormTextField className="sql-form-item-input min-w-0 basis-0 grow" value={username} onChange={setUsername} />
                    </div>
                    {usernameError && <div className="sql-form-item-error ml-[102px] mt-1 text-[12px] text-[#E54552]">{usernameError}</div>}
                </div>
                <div className="sql-form-item mb-3">
                    <div className="flex">
                        <div className="sql-form-item-label shrink-0 basis-[102px] flex items-center">密码：</div>
                        <FormTextField className="sql-form-item-input min-w-0 basis-0 grow" type="password" value={password} onChange={setPassword} />
                    </div>
                    {passwordError && <div className="sql-form-item-error ml-[102px] mt-1 text-[12px] text-[#E54552]">{passwordError}</div>}
                </div>
                <div className="sql-form-item mb-3">
                    <div className="flex">
                        <div className="sql-form-item-label shrink-0 basis-[102px] flex items-center">文件保存路径：</div>
                        <FormTextField className="sql-form-item-input min-w-0 basis-0 grow" value={path} onChange={setPath} />
                    </div>
                    {pathError && <div className="sql-form-item-error ml-[102px] mt-1 text-[12px] text-[#E54552]">{pathError}</div>}
                </div>
            </div>
            <div className="sql-action flex mx-4 mt-9">
                <FormButton className="sql-action-button w-full" onClick={handleSubmit}>{actionText}</FormButton>
            </div>
        </div>
    );
};
