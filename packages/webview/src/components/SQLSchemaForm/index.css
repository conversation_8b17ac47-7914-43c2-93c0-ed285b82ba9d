.sql-form-item-select.rc-select-single .rc-select-selector{
    padding: none;
    border: none;
}

.sql-form-item-select.rc-select-single .rc-select-selection-item{
    left: 0.5rem;
}

.sql-form-item-dropdown.rc-select-dropdown {
    margin-top: 4px;
    min-height: auto;
    border-radius: 4px;
    background: var(--vscode-dropdown-background);
    border: 1px solid var(--vscode-dropdown-border);
    box-shadow: 0px 4px 20px 0px #1E1E1E;
}

.sql-form-item-dropdown.rc-select-dropdown .rc-virtual-list-holder-inner {
    padding: 0.5rem;
}

.sql-form-item-dropdown.rc-select-dropdown .rc-select-item-option {
    padding: 0 0.5rem;
    height: 26px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    cursor: pointer;
}

.sql-form-item-dropdown.rc-select-dropdown .rc-select-item-option + .rc-select-item-option {
    margin-top: 0.25rem;
}

.sql-form-item-dropdown.rc-select-dropdown .rc-select-item-option.rc-select-item-option-active {
    background: rgba(127, 127, 127, 0.1);
}

.sql-form-item-dropdown.rc-select-dropdown .rc-select-item-option-content {
    color: #D4D4D4;
    font-size: 12px;
    line-height: 18px;
    text-align: left;
    cursor: pointer;
    user-select: none;
}

.sql-form-item-dropdown.rc-select-dropdown .rc-select-item-option-state {
    display: none;
}
