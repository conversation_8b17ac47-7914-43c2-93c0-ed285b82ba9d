import {useCallback} from 'preact/hooks';
import {useAgentContext} from '../AgentProvider';

interface ButtonProps {
    children: string;
    type: 'primary' | 'default';
    payload?: any;
    onClick?: (payload: ButtonProps['payload'], e?: any) => void;
}

export function Button({children, payload, onClick, type}: ButtonProps) {
    const {createConversationMessage} = useAgentContext();
    const sendMessage = useCallback(
        (e: any) => {
            payload && createConversationMessage('message-operation', payload);
            onClick?.(payload, e);
        },
        [createConversationMessage, onClick, payload]
    );

    return (
        <button
            className={`flex-1 px-2 border rounded-md leading-[22px] h-fit ${
                type === 'primary'
                    ? 'agent-primaryButton'
                    : 'agent-defaultButton'
            }`}
            onClick={sendMessage}
        >
            {children}
        </button>
    );
}
