import {AgentConversationInfo} from '@shared/protocols';
import {useCallback} from 'preact/hooks';
import {useToast} from '@/hooks/useToast';
import {useChatTabContext} from '@/hooks/useChatTab';
import {ChatTabKey} from '@/components/Tabs/ChatTab';
import {DropdownButton} from './DropdownButton';
import {Button} from '.';

export type ButtonId =
    | 'file-accept-all'
    | 'file-reject-all'
    | 'rescan'
    | 'return-to-chat'
    | 'file-accept-all-ut'
    | 'debug-run'
    | 'debug-abort'
    | 'debug-restart'
    | 'regenerate-chat';

interface ButtonOption {
    key: string;
    label: string;
    tip?: string;
}

interface Button {
    label: string;
    type?: 'primary' | 'default';
    onClick?: () => void;
    option?: ButtonOption;
}

interface ButtonGroupProps {
    agentConversationInfo: AgentConversationInfo;
    showButtons: ButtonId[];
}

export function ButtonGroup({showButtons}: ButtonGroupProps) {
    const returnToChat = useCallback(
        () => {
            // eslint-disable-next-line no-console
            console.log('returnToChat');
        },
        []
    );
    const {setActiveTabKey} = useChatTabContext();
    const {toast} = useToast();
    const handleButtonClick = useCallback(
        payload => {
            switch (payload.action) {
                case 'file-accept-all':
                case 'file-accept-all-ut':
                case 'file-accept-usedable-all':
                    toast({type: 'success', message: '已采纳全部文件'});
                    break;
                case 'file-reject-all':
                    toast({type: 'fail', message: '已放弃全部文件'});
                    break;
                case 'return-to-chat':
                    setActiveTabKey(ChatTabKey.CHAT);
                    break;
                default:
                    break;
            }
        },
        [setActiveTabKey, toast]
    );

    const buttons: Record<ButtonId, Button> = {
        'file-accept-all': {label: '全部采纳', type: 'primary'},
        'regenerate-chat': {label: '重新生成', type: 'primary'},
        'file-accept-all-ut': {
            label: '全部采纳',
            type: 'primary',
            option: {
                key: 'file-accept-usedable-all',
                label: '全部采纳验证通过',
                tip: '采纳所有已通过验证且无断言错误的测试用例，帮助有效提升代码覆盖率。',
            },
        },
        'file-reject-all': {label: '全部放弃'},
        rescan: {label: '重新扫描'},
        'return-to-chat': {label: '返回对话', onClick: returnToChat},
        'debug-run': {label: '运行验证', type: 'primary'},
        'debug-abort': {label: '结束任务'},
        'debug-restart': {label: '继续修复', type: 'primary'},
    };

    return (
        <div className="flex items-end space-x-4 w-full">
            {showButtons.map(buttonKey => {
                const button = buttons[buttonKey];

                if (button?.option) {
                    return (
                        <DropdownButton
                            key={buttonKey}
                            type={button.type || 'default'}
                            payload={{action: buttonKey, ...button.option}}
                            option={button.option}
                            onClick={handleButtonClick}
                        >
                            {button.label}
                        </DropdownButton>
                    );
                }
                return (
                    <Button
                        key={buttonKey}
                        type={button.type || 'default'}
                        payload={{action: buttonKey}}
                        onClick={handleButtonClick}
                    >
                        {button.label}
                    </Button>
                );
            })}
        </div>
    );
}
