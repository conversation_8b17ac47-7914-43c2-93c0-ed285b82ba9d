/* bca-disable */
import {useCallback, useState} from 'preact/hooks';
import rightIcon from '@/assets/right.svg';
import {useAgentContext} from '../AgentProvider';

export interface ButtonOption {
    key: string;
    label: string;
    tip?: string;
}

interface ButtonProps {
    children: string;
    type: 'primary' | 'default';
    payload?: any;
    option?: ButtonOption;
    onClick?: (payload: ButtonProps['payload'], e?: any) => void;
}

export function DropdownButton({children, payload, option, onClick, type}: ButtonProps) {
    const [expand, setExpand] = useState(false);
    const themeClass = type === 'primary'
        ? 'agent-primaryButton'
        : 'border-[#FFFFFF4D]';
    const {createConversationMessage} = useAgentContext();
    const sendMessage = useCallback(
        (e: any) => {
            onClick && onClick(payload, e);
            createConversationMessage('message-operation', payload);
        },
        [createConversationMessage, onClick, payload]
    );

    const sendExpandMessage = useCallback(
        (e: any) => {
            onClick && onClick({action: option?.key}, e);
            createConversationMessage('message-operation', {action: option?.key});
        },
        [createConversationMessage, onClick, option]
    );

    const toggleCollapse = useCallback(
        () => {
            setExpand(!expand);
        },
        [expand]
    );

    return (
        <div className="flex-1 leading-[22px]">
            {expand && (
                <button
                    onClick={sendExpandMessage}
                    className="border agent-defaultButton mb-[6px] p-1 w-full rounded-md"
                >
                    {option?.label}
                </button>
            )}
            <button
                style={
                    `${type === 'primary'
                        ? 'background-color: rgba(3, 202, 238, .15)'
                        : 'background-color: rgba(#FFFFFF, 0.8)'}`
                }
                className={`pl-2 border leading-[22px] rounded-md flex items-center w-full ${themeClass}`}
            >
                <div
                    className="flex-1 border-r 1px border-[var(--comate-link-color)] pr-1"
                    onClick={sendMessage}
                >
                    {children}
                </div>
                <div className="px-1" onClick={toggleCollapse}>
                    {expand
                        ? <div className="w-3.5 h-3.5 -rotate-90" dangerouslySetInnerHTML={{__html: rightIcon}} />
                        : <div className="w-3.5 h-3.5 rotate-90" dangerouslySetInnerHTML={{__html: rightIcon}} />}
                </div>
            </button>
        </div>

    );
}
