import FileIcon from '../../../FileIcon';

interface Props {
    files: string[];
    status: string;
}

export default function PanelContent({files, status}: Props) {

    const loading = status !== 'success';

    if (!files || files.length === 0) {
        return null;
    }

    return (
        <div>
            <div className="border border-[#515254] rounded-md p-[10px] mt-[10px] gap-2">
                {
                    files.map((file, index) => {
                        return (
                        // eslint-disable-next-line react/no-array-index-key
                            <div key={`${file}-${index}`} className="flex items-center gap-1">
                                <FileIcon filename={file} className="w-3 h-3 min-w-3 max-w-3" />
                                {file}
                            </div>
                        );
                    })
                }
            </div>
            {loading && <div className="text-[#7c7d80] text-sm mt-2">正在为您分析文件</div>}
        </div>
    );
}
