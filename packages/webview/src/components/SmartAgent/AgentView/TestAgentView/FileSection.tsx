import {AcceptState, ComposerCodeStatus} from '@shared/composer';
import {useCallback, useEffect, useRef, useState} from 'preact/hooks';
import cx from 'classnames';
import {EventMessage, TestBotBlockSection} from '@shared/protocols';
import {AcceptBtnGroup} from '@/components/Markdown/ComposerFileIndicator';
import Loading from '@/components/Loading';
import {messageHandler} from '@/utils/messageHandler';
import GradientCard from '../../GradientCard';
import {useTestAgentContext} from './TestAgentProvider';
import {getFileNameFromPath, toMd} from './utils';
import './FileSection.css';

export interface FileAction {
    action: 'file-view' | 'file-accept' | 'file-reject';
    filePath: string;
    id: number | string;
}

interface PreviewInfo {
    type: string;
    filePath?: string;
    content?: string;
}

interface Props {
    messageId: number | string;
    info: PreviewInfo;
    accepted: AcceptState;
    onFileClick?: (action: FileAction) => void;
}


const generateAction = (
    id: number | string,
    action: FileAction['action'],
    filePath: string,
    onFileClick?: Props['onFileClick']
) => {
    return (e: any) => {
        e.stopPropagation();
        onFileClick?.({id, filePath, action});
    };
};

export const FILE_NAME_SIGN_$ = '/$';

const getFontColor = (filePath?: string) => {
    return filePath?.startsWith(FILE_NAME_SIGN_$) ? '#8f8f8f' : undefined;
};


interface IndicatorProps extends TestBotBlockSection {
    messageId: number | string;
    label?: string;
    loading?: boolean;
    accepted: AcceptState;
    onFileClick?: Props['onFileClick'];
}

function Indicator(
    {messageId, absolutePath, label, loading, extraBadge, status, accepted, onFileClick}: IndicatorProps
) {
    const basename = getFileNameFromPath(absolutePath);
    const handleClick = useCallback(
        (e: any) => {
            const action = generateAction(messageId, 'file-view', absolutePath, onFileClick);
            action(e);
        },
        [absolutePath, messageId, onFileClick]
    );

    const handleActionButtonClick = useCallback(
        (action: FileAction) => {
            onFileClick && onFileClick(action);
        },
        [onFileClick]
    );


    const [showLabel, setLabelState] = useState(false);
    const previousAccepted = useRef<AcceptState>(accepted);
    useEffect(
        () => {
            if (previousAccepted.current === AcceptState.UNTOUCHED && accepted !== AcceptState.UNTOUCHED) {
                setLabelState(true);
                setTimeout(
                    () => {
                        setLabelState(false);
                    },
                    2000
                );
            }
            previousAccepted.current = accepted;
        },
        [accepted]
    );

    return (
        <div
            onClick={handleClick}
            className="testbot-file-indicator"
        >
            <div className="flex items-center gap-2 overflow-hidden">
                {/* <span className="testbot-file-dot" /> */}
                <span
                    className={cx('font-semibold max-w-[175px] overflow-hidden whitespace-nowrap text-ellipsis')}
                >
                    {basename}
                </span>
                {extraBadge && status === ComposerCodeStatus.DONE && (
                    // eslint-disable-next-line max-len
                    <span className="comate-test-agent-tag flex items-center justify-center rounded px-1 bg-[#FFFFFF0D] text-[#FA7E25] leading-4 text-[10px]">
                        {extraBadge.text}
                    </span>
                )}
            </div>
            <div className={`${loading ? 'min-w-12' : 'min-w-[72px]'} flex items-center`}>
                {loading
                    ? (
                        <div className="testbot-file-action">
                            <Loading className="w-4 h-4" />
                            <span>{label}</span>
                        </div>
                    )
                    : (
                        <AcceptBtnGroup
                            showLabel={showLabel}
                            messageId={messageId}
                            accepted={accepted}
                            onFileClick={handleActionButtonClick}
                            filePath={absolutePath}
                            enableFileOperation
                        />
                    )}
            </div>
        </div>
    );
}
function FileIndicator(props: IndicatorProps) {
    switch (props.status) {
        case ComposerCodeStatus.UNREADY:
            return <Indicator loading label="生成中" {...props} />;
        case ComposerCodeStatus.PROCESSING:
            return <Indicator loading label="优化中" {...props} />;
        case ComposerCodeStatus.CANCELLED:
            return <Indicator {...props} />;
        case ComposerCodeStatus.DONE:
            return <Indicator {...props} />;
    }
}


export default function FileSection(props: Props) {
    const {filePath} = props.info;
    const {testBotBlockSections} = useTestAgentContext();
    const section = filePath ? testBotBlockSections[filePath] : undefined;
    const mdToJetbrains = section?.buttons
        ? toMd(filePath || '', section?.buttons)
        : undefined;

    const handleButtonClick = useCallback(
        () => {
            messageHandler.send(EventMessage.ShowWebviewPanelEvent, {md: mdToJetbrains, title: '单测生成', viewColumn: 2});
        },
        [mdToJetbrains]
    );

    if (!section) {
        return null;
    }
    return (
        <GradientCard color={section.buttons?.length ? 'red' : 'secubot-gray'} className="my-1">
            <FileIndicator {...props} {...section} />
            <div className="mt-2 text-wrap" style={{color: getFontColor(filePath)}}>{section.content}</div>
            {section.buttons && (
                <div className="mt-2">
                    {
                        section.buttons.map((btn, index) => {
                            return (
                                <button
                                    onClick={handleButtonClick}
                                    // eslint-disable-next-line react/no-array-index-key
                                    key={`${index}-${btn.buttonText}`}
                                    className="flex items-center px-[6px] rounded bg-[#FF4E4E1A] text-[#FF4E4E]"
                                >
                                    {btn.buttonText}
                                </button>
                            );
                        })
                    }
                </div>
            )}
        </GradientCard>
    );
}

