.testbot-file-indicator[data-role="create"] {
    --linear-gradient-color: linear-gradient(112.91deg, rgba(63, 191, 97, 0.3) 0.89%, rgba(112, 217, 140, 0.3) 4.71%, rgba(124, 125, 128, 0.3) 26.72%);
    --dot-color: rgba(112, 217, 140, 1);
}

.testbot-file-indicator[data-role="edit"],
.testbot-file-indicator[data-role="rewrite"] {
    --linear-gradient-color: linear-gradient(113.12deg, rgba(255, 214, 1, 0.3) 0.9%, rgba(255, 194, 51, 0.3) 5.3%, rgba(124, 125, 128, 0.3) 27.92%);
    --dot-color: rgba(255, 194, 51, 1);
}

.testbot-file-indicator[data-role="delete"] {
    --linear-gradient-color: linear-gradient(112.91deg, rgba(255, 86, 100, 0.3) 0.89%, rgba(229, 69, 82, 0.3) 4.71%, rgba(124, 125, 128, 0.3) 26.72%);
    --dot-color: rgba(255, 78, 78, 1);
}

.testbot-file-indicator {
    display: flex;
    height: 22px;
    justify-content: space-between;
    /* padding: 0 12px; */
    /* margin-bottom: 8px; */
    position: relative;
    cursor: pointer;
}

/* .testbot-file-indicator::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 4px;
    padding: 1px;
    background: var(--linear-gradient-color);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: 1;
} */

.testbot-file-dot {
    display: block;
    width: 6px;
    height: 6px;
    border-radius: 100%;
    background-color: var(--dot-color);
}

.testbot-file-action {
    display: flex;
    gap: 8px;
    align-items: center;
    z-index: 2;
    color: #17C3E5;
}

.testbot-file-action-hyper-bar {
    display: inline-block;
    width: 1px;
    height: 12px;
    background-color: var(--vscode-textSeparator-foreground);
}

.testbot-file-action-group button:hover {
    opacity: 0.8;
}

@media screen and (max-width: 380px) {
    .comate-test-agent-tag {
        display: none !important;
    }
}
