import {useCallback, useEffect} from 'preact/compat';
import {JsxMarkdown} from '@/components/Markdown';
import {FileAction} from '@/components/Markdown/ComposerPreviewIndicator';
import AgentCollapsePanel from '@/components/CollapsePanel/AgentCollapsePanel';
import {LoadingText} from '@/components/LoadingText';
import {useAgentContext} from '../../AgentProvider';
import PanelContent from './PanelContent';
import {useTestAgentContext} from './TestAgentProvider';

interface Props {
    messageId: string;
    content: string;
}

const safeJsonParse = (data: string): any => {
    try {
        return JSON.parse(data);
    }
    catch (error) {
        console.error('Failed to parse testAgent JSON:', error);
        return {};
    }
};

// eslint-disable-next-line complexity
export default function TestAgentView({messageId, content: testAgentContent}: Props) {
    const testInfo = safeJsonParse(testAgentContent);
    const {createConversationMessage} = useAgentContext();
    // const isRunning = foregroundConversation?.status === WebviewAgentConversationStatus.Running;
    const {startIntroduction, analysisPanel, fileListTitle, content, loadingMsg, testbotMapping} = testInfo;
    // const introStreaming = isRunning && (!content || content.length === 0 || content.trim() === '');
    // const contentStreaming = isRunning && status !== 'success';
    const streamIntro = startIntroduction;
    const streamContent = content;
    const panelTitle = analysisPanel?.status === 'success' ? fileListTitle ?? '文件列表' : '分析文件中...';

    const {updateTestBotBlockSections} = useTestAgentContext();
    const handleFileClick = useCallback(
        (fileAction: FileAction) => {
            createConversationMessage('message-operation', fileAction);
        },
        [createConversationMessage]
    );

    useEffect(
        () => {
            if (testbotMapping) {
                updateTestBotBlockSections(testbotMapping);
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [testAgentContent]
    );

    return (
        <div className="flex flex-col gap-2">
            <JsxMarkdown
                role="assistant"
                messageId={messageId}
                content={streamIntro}
                onFileClick={handleFileClick}
            />
            {analysisPanel && analysisPanel.content && analysisPanel.content.length !== 0 && (
                <AgentCollapsePanel
                    collapsible={analysisPanel.status === 'success'}
                    title={panelTitle}
                    content={<PanelContent files={analysisPanel.content} status={analysisPanel.status} />}
                />
            )}
            <JsxMarkdown
                role="assistant"
                messageId={messageId}
                content={streamContent}
                onFileClick={handleFileClick}
            />
            {loadingMsg && <LoadingText content={loadingMsg} />}
        </div>
    );
}
