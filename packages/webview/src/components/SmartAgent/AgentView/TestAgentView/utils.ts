/* eslint-disable max-len */
import {TestBotBlockSection} from '@shared/protocols';

export function getFileNameFromPath(filePath) {
    // 找到最后一个斜杠（/ 或 \，考虑不同操作系统的路径分隔符）
    const lastSlashIndex = filePath.lastIndexOf('\\') === -1 ? filePath.lastIndexOf('/') : filePath.lastIndexOf('\\');

    // 如果没有找到斜杠，说明整个字符串可能就是一个文件名
    if (lastSlashIndex === -1) {
        return filePath;
    }

    // 提取斜杠之后的部分作为文件名
    return filePath.substring(lastSlashIndex + 1);
}

export const toMd = (absolutePath: string, buttons: TestBotBlockSection['buttons']) => {
    const bugs = buttons?.[0].clickMethodMessages.map(message => `
<div style="border: 1px solid #D9D9D9; padding: 20px; backgroundColor: #232323; border-radius: 4px; margin-bottom: 32px">
<div style="color: #17c3e5; margin-bottom: 12px">${message.title}</div>\n\n
${message.content}
</div>`) || [];
    return `## ${getFileNameFromPath(absolutePath)}\n\n${bugs.join('\n\n')}`;
};
