import {createContext} from 'preact';
import {useContext, useState} from 'preact/hooks';
import {TestBotBlockSection} from '@shared/protocols';

interface ContextValue {
    testBotBlockSections: Record<string, TestBotBlockSection>;
    updateTestBotBlockSections: (sections: Record<string, TestBotBlockSection>) => void;
}

const Context = createContext<ContextValue>({
    testBotBlockSections: {},
    updateTestBotBlockSections: () => {},
});

export const TestAgentProvider = ({...props}) => {
    const [testBotBlockSections, setTestBotBlockSections] = useState<Record<string, TestBotBlockSection>>({});

    return (
        <Context.Provider
            value={{
                testBotBlockSections,
                updateTestBotBlockSections: setTestBotBlockSections,
            }}
            {...props}
        />
    );
};

export function useTestAgentContext() {
    return useContext(Context);
}
