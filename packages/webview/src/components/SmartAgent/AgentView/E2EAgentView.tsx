import {useCallback, useMemo} from 'preact/compat';
import {WebviewAgentConversationStatus} from '@shared/protocols';
import JsxMarkdown from '@/components/Markdown';
import {FileAction} from '@/components/Markdown/ComposerFileIndicator';
import {isJetbrains} from '@/config';
import {LoadingText} from '@/components/LoadingText';
import {copyToClipboard} from '@/utils/clipboard';
import {useAgentContext} from '../AgentProvider';

interface Props {
    messageId: string;
    content: string;
    isLatest?: boolean;
}

export default function E2EAgentView({messageId, content, isLatest}: Props) {
    const {createConversationMessage, foregroundConversation} = useAgentContext();
    const handleFileClick = useCallback(
        (fileAction: FileAction) => {
            createConversationMessage('message-operation', fileAction);
        },
        [createConversationMessage]
    );

    const hanleExecuteTerminalShell = useCallback(
        (shell: string) => {
            createConversationMessage('message-operation', {
                action: 'execute-shell',
                shell,
                id: messageId,
            });
        },
        [createConversationMessage, messageId]
    );

    const insertToTerminal = useCallback(
        (shell: string) => {
            createConversationMessage('message-operation', {
                action: 'insert-shell',
                shell,
                id: messageId,
            });
        },
        [createConversationMessage, messageId]
    );

    const actions = useMemo(
        () => {
            const actions = {
                copy: copyToClipboard,
                insertIntoTerminal: insertToTerminal,
            };
            // jetbrains 暂不支持执行命令
            if (isJetbrains) {
                return actions;
            }
            return {...actions, executeTerminalShell: hanleExecuteTerminalShell};
        },
        [hanleExecuteTerminalShell, insertToTerminal]
    );

    const status = foregroundConversation?.status;

    if (!content && isLatest && status === WebviewAgentConversationStatus.Running) {
        return <LoadingText loading content="正在生成中" />;
    }

    return (
        <JsxMarkdown
            role="assistant"
            messageId={messageId}
            content={content.replace(/\\n/g, '\n')}
            // @ts-expect-error
            actions={actions}
            enableFileOperation={isLatest}
            onFileClick={handleFileClick}
        />
    );
}
