import {AgentMessage} from '@comate/plugin-shared-internals';
import '@comate/plugin-jsx/style.css';
import {WebviewAgentConversationType} from '@shared/protocols';
import {MentionHighlightText} from '@/components/ChatBox/MentionHighlightText';
import Markdown from '@/components/Markdown';
import E2EAgentView from '../AgentView/E2EAgentView';
import TestAgentView from '../AgentView/TestAgentView';
import {TestAgentProvider} from '../AgentView/TestAgentView/TestAgentProvider';
import DebugAgentView from '../DebugAgentView';
import {SecuBot} from '../SecuBotView';

interface Props {
    message: AgentMessage;
    type: WebviewAgentConversationType;
    isLatest?: boolean;
}

export default function Content({message, isLatest, type}: Props) {
    const {content} = message;
    if (message.role === 'user') {
        return (
            <div className={`${message.code ? 'mb-[-12px]' : ''}`}>
                <MentionHighlightText text={content} />
                {message.code && (
                    <Markdown
                        messageId={message.id}
                        content={message.code}
                        role={message.role}
                        numLinesToShow={5}
                    />
                )}
            </div>
        );
    }

    switch (type) {
        case WebviewAgentConversationType.TestBotConversation:
            return (
                <TestAgentProvider>
                    <TestAgentView messageId={message.id} content={content} />
                </TestAgentProvider>
            );
        case WebviewAgentConversationType.E2EBotConversation:
            return (
                <E2EAgentView
                    messageId={message.id}
                    content={message.content}
                    isLatest={isLatest}
                />
            );
        case WebviewAgentConversationType.DebugBotConversation:
            return (
                <DebugAgentView
                    messageId={message.id}
                    data={message.content as any}
                    isLatest={isLatest}
                />
            );
        case WebviewAgentConversationType.SecuBotConversation:
            return <SecuBot content={message.content} isLatest={isLatest} messageId={message.id} />;
        default:
            return <div>这里就是啥也没有</div>;
    }
}
