/* eslint-disable complexity */
/* eslint-disable max-len */
import {AgentMessage} from '@comate/plugin-shared-internals';
import {WebviewAgentConversationType} from '@shared/protocols';
import Header from './Header';
import Content from './Content';

interface Props {
    message: AgentMessage;
    username: string;
    avatarUrl: string;
    chineseName?: string;
    isLatest: boolean;
    type: WebviewAgentConversationType;
}

export default function Message({message, type, username, avatarUrl, chineseName, isLatest}: Props) {
    return (
        <div
            role="listitem"
            aria-label={message.content}
            aria-level={1}
            id={`message-list-${message.id}`}
            className="focus:outline-none message-content px-4 py-5 bg-[--comate-editor-background]"
            tabIndex={-1}
        >
            <Header
                role={message.role}
                username={username}
                chineseName={chineseName}
                avatarUrl={avatarUrl}
            />
            <Content message={message} type={type} isLatest={isLatest} />
            {/* <Footer /> */}
        </div>
    );
}
