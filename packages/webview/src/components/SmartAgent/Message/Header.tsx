/* eslint-disable max-len */
/* bca-disable */
import {useMemo} from 'preact/hooks';
import {WebviewAgentConversationType} from '@shared/protocols';
import {memo} from 'preact/compat';
import Avatar from '../../ChatBox/Avatar';
import {useAgentContext} from '../AgentProvider';

interface Props {
    avatarUrl: string;
    username: string;
    chineseName?: string;
    role: 'user' | 'assistant';
}

const commandMapping = {
    [WebviewAgentConversationType.SecuBotConversation]: '代码安全',
    [WebviewAgentConversationType.DebugBotConversation]: 'Debug',
    [WebviewAgentConversationType.TestBotConversation]: '生成单测',
};

function Header({role, username, chineseName, avatarUrl}: Props) {
    const {foregroundConversation} = useAgentContext();

    // 只有全栈编程（端到端）智能体需要展示智能体名称
    const agenDisplaytName = useMemo(
        () => {
            if (role === 'user') {
                return chineseName || username;
            }
            if (foregroundConversation?.type === 'E2EBotConversation') {
                return '@全栈编程';
            }
        },
        [role, foregroundConversation, chineseName, username]
    );

    const commandDisplayName = useMemo(
        () => {
            if (foregroundConversation) {
                return commandMapping[foregroundConversation.type];
            }
        },
        [foregroundConversation]
    );

    const shouldShowCommand = useMemo(
        () => role !== 'user' && commandDisplayName,
        [role, commandDisplayName]
    );

    return (
        <div className="flex w-full justify-between mb-3" aria-hidden="true">
            <div style={{alignItems: 'center', display: 'flex', gap: 8}}>
                <Avatar role={role} username={username} avatarUrl={avatarUrl} isSmartAgent smartAgentType={foregroundConversation?.type} aria-hidden="true" />
                {agenDisplaytName && <div>{agenDisplaytName}</div>}
                {shouldShowCommand && <div className="text-xs">/{commandDisplayName}</div>}
            </div>
        </div>
    );
}

export default memo(Header);
