export type DebugStatus = 'loading'
    | 'analyzing'
    | 'fixing'
    | 'operating'
    | 'running'
    | 'failed'
    | 'success'
    | 'error'
    | 'aborted'
    | 'cancelled'
    | 'exited';

export interface CodeContextItem {
    type: 'errorCode' | 'relatedCode';
    filePath: string;
    startLineNum: number;
    endLineNum: number;
}

export interface DebugAgentData {
    status: DebugStatus;
    processLog: string[];
    errorReason?: string;
    contexts?: CodeContextItem[];
    content?: string;
    resultText?: string;
}
