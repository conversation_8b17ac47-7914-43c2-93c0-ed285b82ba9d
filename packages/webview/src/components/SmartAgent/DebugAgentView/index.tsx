import {LoadingText} from '@/components/LoadingText';
import {JsxMarkdown} from '@/components/Markdown';
import DebugAgentMarkdown from './DebugAgentMarkdown';
import {AnalyzeSection} from './AnalyzeSection';
import {DebugAgentData, DebugStatus} from './types';

interface ResultProps {
    status: DebugStatus;
    messageId: string;
    processLog?: string[];
}

function Result({status, processLog, messageId}: ResultProps) {
    return (
        <>
            {processLog && processLog.length > 0 && (
                <JsxMarkdown
                    role="assistant"
                    messageId={messageId}
                    content={processLog.join('\n\n')}
                />
            )}
            {status === 'running' && <LoadingText content="正在运行" /> }
        </>
    );
}

interface Props {
    messageId: string;
    data?: DebugAgentData;
    isLatest?: boolean;
}

function MessageContent({messageId, data = {status: 'loading', processLog: []}, isLatest}: Props) {
    const {status, errorReason, contexts = [], content = '', processLog = []} = data;
    if (status === 'loading') {
        return (
            <LoadingText content="正在为您分析错误原因" />
        );
    }

    if (status === 'analyzing') {
        return (
            <AnalyzeSection
                messageId={messageId}
                errorReason={errorReason}
                contexts={contexts}
                processLog={processLog}
            />
        );
    }

    return (
        <>
            <AnalyzeSection
                messageId={messageId}
                errorReason={errorReason}
                contexts={contexts}
                processLog={processLog}
            />
            <DebugAgentMarkdown
                status={status}
                content={content}
                messageId={messageId}
                isLatest={isLatest}
            />
            <Result messageId={messageId} status={status} processLog={data.processLog} />
        </>
    );
}

export default function DebugAgentView(props: Props) {
    return (
        <div className="flex flex-col gap-2 text-[#E3E4E8] text-[13px]/[22px]">
            <div>
                好的，已经接收到您的任务，将对终端的报错日志进行分析和调试，找到问题的根本原因并提供解决方案。
            </div>
            <MessageContent {...props} />
        </div>
    );
}
