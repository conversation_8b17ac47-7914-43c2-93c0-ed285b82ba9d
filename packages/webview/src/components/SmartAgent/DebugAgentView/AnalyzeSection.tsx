/* eslint-disable max-len */
import {useCallback, useMemo} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import {JsxMarkdown} from '@/components/Markdown';
import GradientCard from '../GradientCard';
import StreamText from './StreamText';
import {CodeContextItem} from './types';
import {Section} from './Section';

function CodeRefer({data, color}: {data: CodeContextItem, color: 'red' | 'blue'}) {
    const {filePath, startLineNum, endLineNum} = data;
    const handleOpenFile = useCallback(
        () => {
            messageHandler.send(
                EventMessage.ShowCodeBlockEvent,
                {
                    filePath,
                    startLine: startLineNum,
                    endLine: endLineNum,
                }
            );
        },
        [startLineNum, filePath, endLineNum]
    );

    const fileName = filePath.split('/').pop();
    const lines = (
        <span className="min-w-fit text-[var(--comate-descriptionForeground)]">
            Line [{startLineNum + 1},{endLineNum + 1}]
        </span>
    );

    return (
        <GradientCard
            onClick={handleOpenFile}
            className="group hover:cursor-pointer text-white flex justify-between"
            color={color}
        >
            <div
                className="group-hover:opacity-80 whitespace-nowrap text-ellipsis overflow-hidden"
            >
                {fileName}
            </div>
            <div className="group-hover:opacity-80 min-w-fit">
                {lines}
            </div>
        </GradientCard>
    );
}

export function CodeContext({contexts}: {contexts: CodeContextItem[]}) {
    const {errorCodeBlocks, relatedCodeBlocks} = useMemo(
        () => {
            const errorCodeBlocks = contexts.filter(item => item.type === 'errorCode');
            const relatedCodeBlocks = contexts.filter(item => item.type === 'relatedCode');
            return {errorCodeBlocks, relatedCodeBlocks};
        },
        [contexts]
    );

    return (
        <Section titleClassName="mb-2" className="pb-4" title="查阅相关代码信息">
            {errorCodeBlocks.length > 0 && (
                <>
                    <div className="my-1 text-[#999A9E]"><span>报错行所在代码</span></div>
                    <div className="flex flex-col gap-2">
                        {errorCodeBlocks.map((item, index) => (
                            <CodeRefer color="blue" key={`error-code-${index + 1}`} data={item} />
                        ))}
                    </div>
                </>
            )}
            {relatedCodeBlocks.length > 0 && (
                <>
                    <div className="my-1 text-[#999A9E]"><span>相关代码</span></div>
                    <div className="flex flex-col gap-2">
                        {relatedCodeBlocks.map((item, index) => (
                            <CodeRefer color="blue" key={`related-code-${index + 1}`} data={item} />
                        ))}
                    </div>
                </>
            )}
        </Section>
    );
}

interface Props {
    loading?: boolean;
    messageId: string;
    processLog?: string[];
    errorReason?: string;
    contexts?: CodeContextItem[];
}

export function AnalyzeSection({errorReason, contexts, processLog = [], messageId, loading = false}: Props) {
    const showContext = contexts && contexts.length > 0;
    const showContextDescription = loading || showContext;

    return (
        <>
            {errorReason && (
                <>
                    <div>
                        <span className="font-medium">识别到错误：</span>
                    </div>
                    <GradientCard
                        color="blue"
                        className="my-1 text-white min-h-[42px]"
                    >
                        <JsxMarkdown
                            role="assistant"
                            messageId={messageId}
                            content={errorReason}
                        />
                    </GradientCard>
                </>
            )}
            {showContextDescription && (
                <div className="">
                    <StreamText text="我将查阅以下信息，了解更多细节..." disable={!!contexts || processLog.length > 0} />
                </div>
            )}
            {showContext && (
                <div className="my-1">
                    <CodeContext contexts={contexts} />
                </div>
            )}
        </>
    );
}
