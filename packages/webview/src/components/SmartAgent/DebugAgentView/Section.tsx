import {ReactNode} from 'preact/compat';

interface Props {
    title?: string;
    children: ReactNode;
    className?: string;
    titleClassName?: string;
}

export function Section(
    {title, children, className, titleClassName}: Props
) {
    return (
        <div className={`px-3 py-2.5 bg-white/[0.04] rounded ${className}`}>
            <div className={titleClassName}>
                <span className="font-medium text-[#E3E4E8]">{title}</span>
            </div>
            {children}
        </div>
    );
}
