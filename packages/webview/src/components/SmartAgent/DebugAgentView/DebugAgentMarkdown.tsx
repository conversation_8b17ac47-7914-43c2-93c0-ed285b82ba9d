import {useCallback, useMemo} from 'preact/hooks';
import {JsxMarkdown} from '@/components/Markdown';
import {FileAction} from '@/components/Markdown/ComposerFileIndicator';
import {copyToClipboard} from '@/utils/clipboard';
import {useAgentContext} from '../AgentProvider';
import {Section} from './Section';
import {DebugStatus} from './types';

interface Props {
    content: string;
    messageId: string;
    status: DebugStatus;
    isLatest?: boolean;
}

export const hasFixResult = (status: DebugStatus) => (
    status !== 'loading' && status !== 'analyzing' && status !== 'fixing'
);


function ButtonGroup({showButtons}: {showButtons: boolean}) {
    const {createConversationMessage} = useAgentContext();
    const acceptAll = useCallback(
        () => createConversationMessage('message-operation', {action: 'file-accept-all'}),
        [createConversationMessage]
    );
    const rejectAll = useCallback(
        () => createConversationMessage('message-operation', {action: 'file-reject-all'}),
        [createConversationMessage]
    );

    if (!showButtons) {
        return null;
    }

    return (
        <div className="flex py-1 gap-2.5 w-full">
            <button
                className="flex-1 py-1 rounded bg-[#17C3E526] text-[var(--comate-link-color)]"
                onClick={acceptAll}
            >
                全部采纳
            </button>
            <button
                className="flex-1 py-1 rounded agent-defaultButton"
                onClick={rejectAll}
            >
                全部放弃
            </button>
        </div>
    );
}

// 这是一个自定义的 Markdown 组件，会将所有 CodeBlock 集中起来，然后用一个固定的格式展示出来
export default function DebugAgentMarkdown({content, messageId, isLatest, status}: Props) {
    const {createConversationMessage} = useAgentContext();
    const handleFileClick = useCallback(
        (fileAction: FileAction) => {
            createConversationMessage('message-operation', fileAction);
        },
        [createConversationMessage]
    );

    const {description, detail, isMultipleCodeBlock} = useMemo(
        () => {
            const [description, ...chunks] = content.split('```');
            const detail = chunks.length > 0 ? `\`\`\`${chunks.join('```')}` : '';
            const replaceBlock = chunks.filter(v => v.trim().startsWith('replaceFrom'));
            return {description, detail, isMultipleCodeBlock: replaceBlock.length > 1};
        },
        [content]
    );

    const insertToTerminal = useCallback(
        (shell: string) => {
            createConversationMessage('message-operation', {
                action: 'insert-shell',
                shell,
                id: messageId,
            });
        },
        [createConversationMessage, messageId]
    );

    const actions = useMemo(
        () => {
            return {
                copy: copyToClipboard,
                insertIntoTerminal: insertToTerminal,
            };
        },
        [insertToTerminal]
    );

    const showActions = isMultipleCodeBlock && hasFixResult(status);

    if (!content) {
        return null;
    }

    return (
        <>
            <JsxMarkdown
                role="assistant"
                messageId={messageId}
                content={description}
                onFileClick={handleFileClick}
            />
            {detail && (
                <Section titleClassName="mb-3" className="my-1" title="修改文件">
                    <JsxMarkdown
                        role="assistant"
                        messageId={messageId}
                        content={detail}
                        // @ts-expect-error
                        actions={actions}
                        onFileClick={handleFileClick}
                        enableFileOperation={isLatest}
                    />
                    <ButtonGroup showButtons={showActions} />
                </Section>
            )}
        </>
    );
}
