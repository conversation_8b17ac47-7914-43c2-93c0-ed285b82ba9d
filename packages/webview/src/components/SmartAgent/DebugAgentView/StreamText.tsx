import {useState, useRef, useEffect} from 'preact/hooks';
import {Typewriter} from '@/utils/Typewriter';

export default function StreamText({text, disable}: { text: string, disable: boolean }) {
    const [content, setContent] = useState('');
    const typewriter = useRef<Typewriter>(
        new Typewriter({update: setContent}, '', 800)
    );

    useEffect(
        () => {
            if (disable) {
                typewriter.current.typeAllOut(text);
            }
            else {
                typewriter.current.typeString(text);
            }
        },
        [text, setContent, disable]
    );

    if (disable) {
        return <span>{text}</span>;
    }
    return <span>{content}</span>;
}
