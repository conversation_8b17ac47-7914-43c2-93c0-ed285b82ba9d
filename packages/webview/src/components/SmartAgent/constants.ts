import {WebviewAgentConversationType, WebviewAgentConversationStatus} from '@shared/protocols';

export const typeMapping = {
    [WebviewAgentConversationType.SecuBotConversation]: '代码安全',
    [WebviewAgentConversationType.DebugBotConversation]: 'Debug',
    [WebviewAgentConversationType.TestBotConversation]: '单测',
    [WebviewAgentConversationType.E2EBotConversation]: '全栈编程',
};

export const statusMapping = {
    [WebviewAgentConversationStatus.Ready]: '已就绪',
    [WebviewAgentConversationStatus.Running]: '执行中',
    [WebviewAgentConversationStatus.Failed]: '失败',
    [WebviewAgentConversationStatus.Completed]: '已完成',
    [WebviewAgentConversationStatus.Cancelled]: '已取消',
};

export const statusColorStyleMapping = {
    [WebviewAgentConversationStatus.Running]: 'text-orange-500',
    [WebviewAgentConversationStatus.Failed]: 'text-red-500',
    [WebviewAgentConversationStatus.Completed]: 'text-green-500',
    [WebviewAgentConversationStatus.Cancelled]: 'text-gray-500',
};
