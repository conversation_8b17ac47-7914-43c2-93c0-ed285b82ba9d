/* bca-disable */
import {WebviewAgentConversationType} from '@shared/protocols';
import e2eBotIcon from '@/assets/agent/e2eBot.svg';
import testBotIcon from '@/assets/agent/testBot.svg';
import debugBotIcon from '@/assets/agent/debugBot.svg';
import secuBotIcon from '@/assets/agent/secuBot.svg';

export const renderAgentIcon = (type: string, size?: number) => {
    const iconSize = size || 16;
    const className = size ? `w-[${iconSize}px] h-[${iconSize}px]` : 'w-4 h-4';

    switch (type) {
        case WebviewAgentConversationType.E2EBotConversation:
            return <div className={className} dangerouslySetInnerHTML={{__html: e2eBotIcon}} />;
        case WebviewAgentConversationType.DebugBotConversation:
            return <div className={className} dangerouslySetInnerHTML={{__html: debugBotIcon}} />;
        case WebviewAgentConversationType.TestBotConversation:
            return <div className={className} dangerouslySetInnerHTML={{__html: testBotIcon}} />;
        case WebviewAgentConversationType.SecuBotConversation:
            return <div className={className} dangerouslySetInnerHTML={{__html: secuBotIcon}} />;
        default:
            return null;
    }
};
