/* bca-disable */
import {useCallback, useEffect} from 'preact/hooks';
import {AgentConversationInfo, EventMessage, WebviewAgentConversationType, ApplyStatus} from '@shared/protocols';
import bg from '@/assets/agent/conversationListBackground.png';
import fg from '@/assets/agent/conversationListForeground.png';
import e2eBotIcon from '@/assets/agent/e2eBot.svg';
import arrowIcon from '@/assets/forwardArrow.svg';
import settingIcon from '@/assets/setting.svg';
import {messageHandler} from '@/utils/messageHandler';
import {useAgentContext} from './AgentProvider';
import {typeMapping, statusMapping, statusColorStyleMapping} from './constants';
import {renderAgentIcon} from './utils';
import './index.css';

function AgentConversation({
    id,
    status,
    type,
    agentConversations,
    lastQuery,
}: AgentConversationInfo & {agentConversations: AgentConversationInfo[]}) {
    const {updateForegroundConversation} = useAgentContext();
    const handleClick = useCallback(
        async () => {
            const foregroundConversation = agentConversations.find(conversation => conversation.id === id);
            if (foregroundConversation) {
                updateForegroundConversation(foregroundConversation);
            }
        },
        [id, agentConversations, updateForegroundConversation]
    );
    // TODO: 和沐容确认下浅色背景的变量值
    return (
        <div className="px-4 py-3 flex flex-col gap-[6px] hover:bg-[#888888]/20 cursor-pointer" onClick={handleClick}>
            <div className="flex items-center gap-1">
                <div className="bg-[#ffffff0c] py-[1px] px-[6px] rounded flex items-center gap-1 text-xs h-[22px]" aria-hidden="true">
                    {renderAgentIcon(type)}
                    {typeMapping[type] + '智能体'}
                </div>
                {/* eslint-disable-next-line max-len */}
                <div
                    className={`bg-[#ffffff0c] py-[1px] px-[6px] text-xs rounded h-[22px] flex items-center ${
                        statusColorStyleMapping[status]
                    }`}
                    aria-hidden="true"
                >
                    {statusMapping[status]}
                </div>
            </div>
            <div className="opacity-70"  aria-label={`点击回到任务:${lastQuery}，任务状态:${statusMapping[status]}`}>
                <span aria-hidden="true">{lastQuery || '点击回到任务'}</span>
            </div>
        </div>
    );
}

export default function AgentConversations() {
    const {
        agentStatus,
        agentApplyStatus,
        agentConversations,
        createConversation,
        updateLatestConversations,
    } = useAgentContext();

    useEffect(
        () => {
            // 列表页获取到某个智能体状态更新，拉取最新的列表
            messageHandler.listen(EventMessage.AgentConversationMessageUpdateEvent,
                ({type}) => {
                    if (type === 'conversation-status') {
                        updateLatestConversations();
                    }
                }
            );
        },
        [updateLatestConversations]
    );

    const handleSettingClick = useCallback(
        () => {
            messageHandler.send(EventMessage.SettingButtonClickEvent, 'baidu.comate.beta');
        },
        []
    );

    const handleCreateConversation = useCallback(
        async () => {
            createConversation({
                conversationId: '',
                messageType: 'add-conversation',
                conversationType: WebviewAgentConversationType.E2EBotConversation,
            });
        },
        [createConversation]
    );

    return (
        <div
            className="conversation-container"
            style={{backgroundImage: `url(${fg}), url(${bg})`}}
        >
            <div className="flex items-center justify-between px-4">
                <div className="my-2 text-sm">最近任务</div>
                <div className="my-2 flex items-center gap-2">
                    <div className="flex items-center gap-1 cursor-pointer" aria-label="配置智能体按钮" onClick={handleSettingClick}>
                        <div className="w-[20px] h-[20px]" aria-hidden="true" dangerouslySetInnerHTML={{__html: settingIcon}} />
                        <span aria-hidden="true">配置智能体</span>
                    </div>
                    {
                        // eslint-disable-next-line max-len
                        agentApplyStatus.fullStackIntelligenceApplyStatus === ApplyStatus.Approved && agentStatus.enableFullStackIntelligence && (
                            <>
                                <div className="h-3 w-[1px] bg-[var(--comate-editor-foreground)]" />
                                <div
                                    className="flex items-center gap-1 cursor-pointer"
                                    aria-label={'与全栈编程智能体对话按钮'}
                                    onClick={handleCreateConversation}
                                >
                                    <div className="w-[20px] h-[20px]" aria-hidden="true"  dangerouslySetInnerHTML={{__html: e2eBotIcon}} />
                                    <span aria-hidden="true">与全栈编程智能体对话</span>
                                    <div className="w-4 h-4" aria-hidden="true" dangerouslySetInnerHTML={{__html: arrowIcon}} />
                                </div>
                            </>
                        )
                    }
                </div>
            </div>
            <div>
                {agentConversations.map(conversation => {
                    return (
                        <AgentConversation
                            key={conversation.id}
                            agentConversations={agentConversations}
                            {...conversation}
                        />
                    );
                })}
            </div>
        </div>
    );
}
