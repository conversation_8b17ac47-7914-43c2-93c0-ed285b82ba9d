/* bca-disable */
import {useCallback, useRef} from 'preact/hooks';
import {AgentConversationInfo, WebviewAgentConversationStatus} from '@shared/protocols';
import stopIcon from '@/assets/stop.svg';
import ComponserInputbox from '../../ChatLab/ComponserInputbox';
import {useAgentContext} from '../AgentProvider';
import {ButtonGroup, ButtonId} from '../Button/ButtonGroup';

interface FooterContentProps {
    agentConversationInfo: AgentConversationInfo;
    showInput?: boolean;
    completedButtons?: ButtonId[];
    failedButtons?: ButtonId[];
    cancelledButtons?: ButtonId[];
}

export function FooterContent(
    {agentConversationInfo, showInput, completedButtons = [], cancelledButtons = [], failedButtons = []}:
        FooterContentProps
) {
    const {createConversationMessage} = useAgentContext();

    const stopGenerating = useCallback(
        () => {
            createConversationMessage('stop-generating', {});
        },
        [createConversationMessage]
    );

    const inputRef = useRef<{focus: () => any}>(null);
    const handleSubmit = useCallback(
        async (query, _agent, _slash, knowledgeList) => {
            createConversationMessage('add-message', {query, knowledgeList});
        },
        [createConversationMessage]
    );

    return (
        <div className="shrink-0 w-full p-1">
            {agentConversationInfo.status === WebviewAgentConversationStatus.Running
                && (
                    <div className="flex justify-end items-center px-4 py-3">
                        <button className="flex items-center gap-1 text-[#B4B5B8]" onClick={stopGenerating}>
                            <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: stopIcon}} />
                            停止生成
                        </button>
                    </div>
                )}
            {agentConversationInfo.status === WebviewAgentConversationStatus.Completed
                && !!completedButtons.length && (
                <div className="flex justify-between items-center px-4 py-3">
                    <ButtonGroup
                        agentConversationInfo={agentConversationInfo}
                        showButtons={completedButtons}
                    />
                </div>
            )}
            {agentConversationInfo.status === WebviewAgentConversationStatus.Cancelled
                && !!cancelledButtons.length && (
                <div className="flex justify-between items-center px-4 py-3">
                    <ButtonGroup
                        agentConversationInfo={agentConversationInfo}
                        showButtons={cancelledButtons}
                    />
                </div>
            )}
            {agentConversationInfo.status === WebviewAgentConversationStatus.Failed
                && !!failedButtons.length && (
                <div className="flex justify-between items-center px-4 py-3">
                    <ButtonGroup
                        agentConversationInfo={agentConversationInfo}
                        showButtons={failedButtons}
                    />
                </div>
            )}
            {showInput && (
                <ComponserInputbox
                    disabled={agentConversationInfo.status === WebviewAgentConversationStatus.Running}
                    ref={inputRef}
                    onSubmit={handleSubmit}
                />
            )}
        </div>
    );
}
