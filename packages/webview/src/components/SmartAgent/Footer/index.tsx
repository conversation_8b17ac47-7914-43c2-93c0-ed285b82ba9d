import {useMemo} from 'preact/hooks';
import {AgentConversationInfo, WebviewAgentConversationType} from '@shared/protocols';
import {useAgentContext} from '../AgentProvider';
import {ButtonId} from '../Button/ButtonGroup';
import {FILE_NAME_SIGN_$} from '../AgentView/TestAgentView/FileSection';
import {FooterContent} from './Content';

interface FooterProps {
    agentConversationInfo: AgentConversationInfo;
}

const safeSplitLine = (content: unknown) => (typeof content === 'string' ? content.split(/\n/) : []);

const defaultE2EAgentButtons: ButtonId[] = ['file-accept-all', 'file-reject-all'];
const failedE2EAgentButtons: ButtonId[] = ['regenerate-chat', 'return-to-chat'];
function E2EAgentFooter({agentConversationInfo}: FooterProps) {
    const {foregroundConversationMessages} = useAgentContext();
    const message = foregroundConversationMessages[foregroundConversationMessages.length - 1];

    const hasTask = safeSplitLine(message?.content).some(line => {
        return /```(create|edit|rewrite|delete):/.test(line);
    });

    return (
        <FooterContent
            showInput
            completedButtons={hasTask ? defaultE2EAgentButtons : undefined}
            cancelledButtons={failedE2EAgentButtons}
            agentConversationInfo={agentConversationInfo}
        />
    );
}

function SecuAgentFooter({agentConversationInfo}: FooterProps) {
    const {foregroundConversationMessages} = useAgentContext();
    const message = foregroundConversationMessages[foregroundConversationMessages.length - 1];

    const hasTask = safeSplitLine(message?.content).some(line => {
        return line.includes('"hasContent": true');
    });

    return (
        <FooterContent
            showInput={false}
            completedButtons={hasTask
                ? ['file-accept-all', 'file-reject-all', 'rescan', 'return-to-chat']
                : ['rescan', 'return-to-chat']}
            failedButtons={['rescan', 'return-to-chat']}
            cancelledButtons={['rescan', 'return-to-chat']}
            agentConversationInfo={agentConversationInfo}
        />
    );
}

function DebugBotAgentFooter({agentConversationInfo}: FooterProps) {
    const {foregroundConversationMessages} = useAgentContext();

    const completedButtons: ButtonId[] | undefined = useMemo(
        () => {
            const lastMessage = foregroundConversationMessages[foregroundConversationMessages.length - 1];
            if (lastMessage && lastMessage.role === 'assistant') {
                const data = (lastMessage.content ?? {}) as any;
                if (data.status === 'operating') {
                    return ['debug-run', 'debug-abort'];
                }
                else if (data.status === 'success' || data.status === 'error' || data.status === 'exited') {
                    return ['return-to-chat'];
                }
                else if (data.status === 'failed') {
                    return ['debug-restart', 'debug-abort'];
                }
            }
            return undefined;
        },
        [foregroundConversationMessages]
    );

    const cancelledButtons: ButtonId[] | undefined = useMemo(
        () => {
            const lastMessage = foregroundConversationMessages[foregroundConversationMessages.length - 1];
            if (lastMessage && lastMessage.role === 'assistant') {
                const data = (lastMessage.content ?? {}) as any;
                if (data.status === 'aborted') {
                    return ['return-to-chat'];
                }
            }
            return ['regenerate-chat', 'return-to-chat'];
        },
        [foregroundConversationMessages]
    );

    return (
        <FooterContent
            completedButtons={completedButtons}
            cancelledButtons={cancelledButtons}
            agentConversationInfo={agentConversationInfo}
        />
    );
}

function TestAgentFooter({agentConversationInfo}: FooterProps) {
    const {foregroundConversationMessages} = useAgentContext();
    const message = foregroundConversationMessages[foregroundConversationMessages.length - 1];

    // NOTE: 这里的要时刻注意正则是否变化：packages/webview/src/components/Markdown/index.tsx:117 by 2024-11-10
    const reanderContent = (() => {
        if (message?.content) {
            try {
                return JSON.parse(message?.content).content;
            }
            catch (e) {
                return '';
            }
        }
        return '';
    })();
    const hasAcceptedAllTask = safeSplitLine(reanderContent).filter(line => {
        const [,,,, filePathPossible] = /```(preview):(\w+):(\d):(.*)/.exec(line) ?? [];
        return filePathPossible && !filePathPossible.startsWith(FILE_NAME_SIGN_$);
    }).length > 0;

    return (
        <FooterContent
            completedButtons={hasAcceptedAllTask
                ? ['file-accept-all-ut', 'file-reject-all', 'return-to-chat']
                : ['return-to-chat']}
            cancelledButtons={hasAcceptedAllTask
                ? ['file-accept-all-ut', 'file-reject-all', 'return-to-chat']
                : ['return-to-chat']}
            agentConversationInfo={agentConversationInfo}
        />
    );
}

export function Footer({agentConversationInfo}: FooterProps) {
    const {type} = agentConversationInfo;

    switch (type) {
        case WebviewAgentConversationType.TestBotConversation:
            return <TestAgentFooter agentConversationInfo={agentConversationInfo} />;
        case WebviewAgentConversationType.E2EBotConversation:
            return <E2EAgentFooter agentConversationInfo={agentConversationInfo} />;
        case WebviewAgentConversationType.DebugBotConversation:
            return <DebugBotAgentFooter agentConversationInfo={agentConversationInfo} />;
        case WebviewAgentConversationType.SecuBotConversation:
            return <SecuAgentFooter agentConversationInfo={agentConversationInfo} />;
        default:
            return (
                <FooterContent
                    agentConversationInfo={agentConversationInfo}
                />
            );
    }
}
