import {WebviewAgentConversationType} from '@shared/protocols';
import {JsxMarkdown} from '../Markdown';
import Header from './Message/Header';

interface Props {
    type: WebviewAgentConversationType;
    username: string;
    avatarUrl: string;
}

export default function SmartAgentWelcome({type, avatarUrl, username}: Props) {
    switch (type) {
        case WebviewAgentConversationType.E2EBotConversation: {
            return (
                <div
                    role="listitem"
                    className={'focus:outline-none message-content px-4 py-5 mb-1 bg-[--comate-editor-background]'}
                    tabIndex={-1}
                >
                    <Header
                        role="assistant"
                        username={username}
                        avatarUrl={avatarUrl}
                    />
                    <JsxMarkdown
                        role="assistant"
                        messageId={-1}
                        content="你好，我是全栈编程智能体，我可以自主理解需求、拆解编码任务。为你完成具体编程任务完整实现。"
                        actions={{}}
                    />
                </div>
            );
        }
        default:
            return null;
    }
}
