import {createContext} from 'preact';
import {AgentMessage} from '@comate/plugin-shared-internals';
import {useContext, useState, useCallback, useEffect} from 'preact/hooks';
import {
    EventMessage,
    AgentConversationInfo,
    WebviewAgentPayload,
    AgentConfig,
    AgentApplyStatus,
    ApplyStatus,
} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import {useChatTabContext} from '@/hooks/useChatTab';
import {ChatTabKey} from '../Tabs/ChatTab';

export const getAgentStatusWithApply = (newAgentConfig: AgentConfig, agentApplyStatus: AgentApplyStatus | null) => {
    return {
        enableCompletionIntelligence: agentApplyStatus?.completionIntelligenceApplyStatus === ApplyStatus.Approved
            ? newAgentConfig.enableCompletionIntelligence
            : false,
        enableUTChatIntelligence: agentApplyStatus?.unitTestIntelligenceApplyStatus === ApplyStatus.Approved
            ? newAgentConfig.enableUTChatIntelligence
            : false,
        enableUTEditorIntelligence: agentApplyStatus?.unitTestIntelligenceApplyStatus === ApplyStatus.Approved
            ? newAgentConfig.enableUTEditorIntelligence
            : false,
        enableDebugIntelligence: agentApplyStatus?.debugIntelligenceApplyStatus === ApplyStatus.Approved
            ? newAgentConfig.enableDebugIntelligence
            : false,
        enableFullStackIntelligence: agentApplyStatus?.fullStackIntelligenceApplyStatus === ApplyStatus.Approved
            ? newAgentConfig.enableFullStackIntelligence
            : false,
        enableSecurityIntelligence: agentApplyStatus?.securityIntelligenceApplyStatus === ApplyStatus.Approved
            ? newAgentConfig.enableSecurityIntelligence
            : false,
    };
};
export const defaultStatus = {} as AgentConfig;
export const defaultApplyStatus = {} as AgentApplyStatus;

interface ContextValue {
    agentConversations: AgentConversationInfo[];
    foregroundConversation: AgentConversationInfo | undefined;
    foregroundConversationMessages: AgentMessage[];
    agentStatus: AgentConfig;
    agentApplyStatus: AgentApplyStatus;
    updateAgentConversations: (conversation: AgentConversationInfo[]) => void;
    updateForegroundConversation: (conversation: AgentConversationInfo) => void;
    updateLatestConversations: () => void;
    updateForegroundConversationMessages: (conversation: AgentConversationInfo, messages: AgentMessage[]) => void;
    clearForegroundConversationMessages: () => void;
    createConversation: (webviewPayload: WebviewAgentPayload) => void;
    createConversationMessage: (messageType: any, payload: any) => void;
    closeConversation: () => void;
    updateAgentStatus: (status: AgentConfig) => void;
    updateAgentApplyStatus: (status: AgentApplyStatus) => void;
}

const Context = createContext<ContextValue>({
    agentConversations: [],
    foregroundConversation: undefined,
    foregroundConversationMessages: [],
    agentStatus: defaultStatus,
    agentApplyStatus: defaultApplyStatus,
    updateAgentConversations: () => {},
    updateForegroundConversation: () => {},
    updateLatestConversations: () => {},
    updateForegroundConversationMessages: () => {},
    clearForegroundConversationMessages: () => {},
    createConversation: () => {},
    createConversationMessage: () => {},
    closeConversation: () => {},
    updateAgentStatus: () => {},
    updateAgentApplyStatus: () => {},
});

const EMPTY_MESSAGES = [];

export const AgentProvider = ({...props}) => {
    const [agentConversations, setAgentConversations] = useState<AgentConversationInfo[]>(EMPTY_MESSAGES);
    const [foregroundConversation, setForegroundConversation] = useState<AgentConversationInfo | undefined>();
    const [agentStatus, setAgentStatus] = useState<AgentConfig>(defaultStatus);
    const [agentApplyStatus, setAgentApplyStatus] = useState<AgentApplyStatus>(defaultApplyStatus);
    const [foregroundConversationMessages, setForegroundConversationMessages] =
        useState<AgentMessage[]>(EMPTY_MESSAGES);

    const {setActiveTabKey} = useChatTabContext();

    const clearForegroundConversationMessages = useCallback(
        () => {
            setForegroundConversationMessages(EMPTY_MESSAGES);
        },
        []
    );

    const updateForegroundConversationMessages = useCallback(
        (conversation: AgentConversationInfo, messages: AgentMessage[]) => {
            if (!foregroundConversation || conversation.id !== foregroundConversation?.id) {
                return;
            }
            setForegroundConversation(conversation);
            setForegroundConversationMessages(messages);
        },
        [foregroundConversation]
    );

    /**
     * 创建智能体
     */
    const handleCreateConversation = useCallback(
        async (agentPayload: WebviewAgentPayload) => {
            const conversation = await messageHandler.send(EventMessage.AgentConversationAddEvent, {agentPayload});
            clearForegroundConversationMessages();
            setForegroundConversation(conversation);
            setActiveTabKey(ChatTabKey.AGENT);
            if (agentPayload.payload) {
                messageHandler.send(EventMessage.AgentConversationNewMessageEvent, {
                    agentPayload: {
                        messageType: 'add-message',
                        conversationId: conversation.id,
                        payload: agentPayload.payload,
                    },
                });
            }
        },
        [clearForegroundConversationMessages, setActiveTabKey]
    );

    /**
     * 获取所有智能体
     */
    const updateLatestConversations = useCallback(
        async () => {
            messageHandler.send(EventMessage.AgentConversationFetchEvent).then(({allConversations}) => {
                setAgentConversations(allConversations);
            });
        },
        []
    );

    const foregroundConversationId = foregroundConversation?.id;
    /**
     * 发送单条消息
     */
    const createConversationMessage = useCallback(
        (messageType: any, payload: any) => {
            messageHandler.send(EventMessage.AgentConversationNewMessageEvent, {
                agentPayload: {
                    messageType,
                    conversationId: foregroundConversationId,
                    payload,
                },
            });
        },
        [foregroundConversationId]
    );

    const handleCloseConversation = useCallback(
        async () => {
            // TODO: 停止生成
            // await messageHandler.send(EventMessage.AgentConversationCloseEvent, foregroundConversation);
            setForegroundConversation(undefined);
            updateLatestConversations();
        },
        [updateLatestConversations]
    );

    useEffect(
        () => {
            messageHandler.send(EventMessage.AgentSwitchStatusEvent).then((
                {enableIntelligenceAgent, applyStatus}:
                {enableIntelligenceAgent: AgentConfig | null, applyStatus: AgentApplyStatus | null}
            ) => {
                if (enableIntelligenceAgent) {
                    const agent = getAgentStatusWithApply(enableIntelligenceAgent, applyStatus);
                    setAgentStatus(agent);
                }
                setAgentApplyStatus(applyStatus || defaultApplyStatus);
            });
        },
        []
    );

    useEffect(
        () => {
            messageHandler.listen(
                EventMessage.AgentSwitchChangeFromIdeEvent, (
                    {enableIntelligenceAgent, applyStatus}:
                    {enableIntelligenceAgent: AgentConfig | null, applyStatus: AgentApplyStatus | null}
                ) => {
                    if (enableIntelligenceAgent) {
                        const agent = getAgentStatusWithApply(enableIntelligenceAgent, applyStatus);
                        setAgentStatus(agent);
                    }
                    setAgentApplyStatus(applyStatus || defaultApplyStatus);

                }
            );
        },
        [agentApplyStatus]
    );

    return (
        <Context.Provider
            value={{
                agentConversations,
                foregroundConversationMessages,
                foregroundConversation,
                agentStatus,
                agentApplyStatus,
                updateAgentConversations: setAgentConversations,
                updateForegroundConversation: setForegroundConversation,
                updateLatestConversations,
                clearForegroundConversationMessages,
                updateForegroundConversationMessages,
                createConversation: handleCreateConversation,
                createConversationMessage,
                closeConversation: handleCloseConversation,
                updateAgentStatus: setAgentStatus,
                updateAgentApplyStatus: setAgentApplyStatus,
            }}
            {...props}
        />
    );
};

export function useAgentContext() {
    return useContext(Context);
}
