// import {useEffect} from 'preact/hooks';
// import {EventMessage, AgentConversationInfo} from '@shared/protocols';
// import {messageHandler} from '@/utils/messageHandler';
import {useAgentContext} from './AgentProvider';
import Messages from './Messages';
import InitPage from './InitPage';
import AgentConversations from './AgentConversations';
import {Footer} from './Footer';
import AgentLogin from './AgentLogin';

interface Props {
    avatarUrl: string;
    username: string;
    chineseName?: string;
    engineInitialized: boolean;
    isLogin: boolean;
}

export default function SmartAgent(props: Props) {
    const {
        agentConversations,
        foregroundConversation,
    } = useAgentContext();

    if (!props.isLogin) {
        return <AgentLogin />;
    }

    // 有前台任务，展示该智能体消息列表
    if (foregroundConversation) {
        return (
            <div className="flex flex-col h-full">
                <div className="flex-1 overflow-y-auto">
                    <Messages {...props} />
                </div>
                <Footer agentConversationInfo={foregroundConversation} />
            </div>
        );
    }

    // 没有前台任务，没有任务列表，展示初始化页面
    if (!agentConversations || agentConversations.length === 0) {
        return <InitPage engineInitialized={props.engineInitialized} />;
    }

    // 没有前台任务，展示所有任务列表
    return <AgentConversations />;
}
