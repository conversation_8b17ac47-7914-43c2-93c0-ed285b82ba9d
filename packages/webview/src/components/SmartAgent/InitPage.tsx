/* bca-disable */
/* eslint-disable max-len */
import {useCallback, useState, useEffect} from 'preact/hooks';
import {
    EventMessage,
    WebviewAgentConversationType,
    ApplyStatus,
    AgentConfig,
    AgentApplyStatus,
} from '@shared/protocols';
import {isInternal, isSaas} from '@/utils/features';
import darkBg from '@/assets/agent/darkAgentBackground.png';
import darkFg from '@/assets/agent/darkAgentForeground.png';
import darkBottomBg from '@/assets/agent/darkBottomBackground.png';
import lightBg from '@/assets/agent/lightAgentBackground.png';
import lightFg from '@/assets/agent/lightAgentForeground.png';
import lightBottomBg from '@/assets/agent/lightBottomBackground.png';
import darkTitle from '@/assets/agent/darkThemeTitle.png';
import darkAgent from '@/assets/agent/darkThemeAgent.png';
import lightTitle from '@/assets/agent/lightThemeTitle.png';
import lightAgent from '@/assets/agent/lightThemeAgent.png';
import e2eBotIcon from '@/assets/agent/e2eBot.svg';
import completionsBotIcon from '@/assets/agent/completionsBot.svg';
import testBotIcon from '@/assets/agent/testBot.svg';
import debugBotIcon from '@/assets/agent/debugBot.svg';
import secuBotIcon from '@/assets/agent/secuBot.svg';
import darkShinyIcon from '@/assets/agent/darkShiny.svg';
import lightShinyIcon from '@/assets/agent/lightShiny.svg';
import arrowIcon from '@/assets/forwardArrow.svg';
import {useExtensionConfig} from '@/hooks/useExtensionConfig';
import {useLog} from '@/hooks/useLog';
import {messageHandler} from '@/utils/messageHandler';
import {isJetbrains, isVSCode} from '@/config';
import Switch from '../Switch';
import Tag from '../Tag';
import {useDerivedState} from '../Tabs/useDerivedState';
import {useChatConfig} from '../ChatConfigProvider';
import {useAgentContext, getAgentStatusWithApply, defaultApplyStatus} from './AgentProvider';
import {Button} from './Button';
import './index.css';

// eslint-disable-next-line complexity
function FeatureOperation({
    enable,
    enableCreate,
    conversationType,
    applyStatus,
    createConversation,
    title,
}: {
    enable?: boolean;
    enableCreate?: boolean;
    conversationType: WebviewAgentConversationType;
    applyStatus: ApplyStatus | null;
    createConversation?: () => void;
    title?: string;
}) {
    const [checked, setChecked] = useDerivedState(!!enable);
    const {log} = useLog();
    const {config: {isPoc}} = useExtensionConfig();

    const handleSwitchChange = useCallback(
        () => {
            setChecked(!checked);
            messageHandler.send(EventMessage.AgentSwitchChangeEvent, {type: conversationType, enable: !checked});
            const logData = {
                category: 'smartAgent',
                label: 'agentSwitchChange',
                data: {conversationType, enable: !checked},
            };
            if (isVSCode) {
                messageHandler.send(EventMessage.UploadUserActionLog, logData);
            }
            else {
                log(logData);
            }
        },
        [conversationType, checked, setChecked, log]
    );

    if (isPoc) {
        if (enableCreate) {
            return (
                <div
                    className="text-[var(--comate-link-color)] flex items-center cursor-pointer"
                    aria-label="去对话按钮，点击后跳转到与全栈编程智能体对话页面"
                    onClick={createConversation}
                >
                    <span aria-hidden="true">去对话</span>
                    <div className="w-[14px] h-[14px]" aria-hidden="true" dangerouslySetInnerHTML={{__html: arrowIcon}} />
                </div>
            );
        }
        return null;
    }

    if (!applyStatus) {
        return null;
    }
    // 应该不会有reject的状态 兼容一下
    if (applyStatus === ApplyStatus.Applying || applyStatus === ApplyStatus.Rejected) {
        return <div>已申请</div>;
    }

    if (enable && enableCreate) {
        return (
            <div
                className="text-[var(--comate-link-color)] flex items-center cursor-pointer"
                aria-label="去对话按钮，点击后跳转到与全栈编程智能体对话页面"
                onClick={createConversation}
            >
                <span aria-hidden="true">去对话</span>
                <div className="w-[14px] h-[14px]" aria-hidden="true" dangerouslySetInnerHTML={{__html: arrowIcon}} />
            </div>
        );
    }

    return <Switch checked={checked} onChange={handleSwitchChange} aria-label={`${title}当前状态为${checked ? '开启' : '关闭'}，点击后${!checked ? '开启' : '关闭'}`} />;
}

function Feature({title, description, icon, enableCreate, enable, conversationType, payload, applyStatus}: {
    title: string;
    description: string;
    icon: string;
    payload?: any;
    enableCreate?: boolean;
    enable?: boolean;
    conversationType: WebviewAgentConversationType;
    applyStatus: ApplyStatus | null;
}) {
    const {createConversation} = useAgentContext();

    const handleCreateConversation = useCallback(
        async () => {
            createConversation({
                conversationId: '',
                payload,
                messageType: 'add-conversation',
                conversationType: conversationType ?? WebviewAgentConversationType.E2EBotConversation,
            });
        },
        [conversationType, createConversation, payload]
    );
    return (
        <div className="flex gap-3 h-16">
            <div className="w-6 h-6" aria-hidden="true" dangerouslySetInnerHTML={{__html: icon}} />
            <div className="flex flex-col gap-1 flex-1">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1" aria-hidden="true">
                        <div className="text-sm">{title}</div>
                        <Tag color="blue">实验室</Tag>
                        {!isInternal && <Tag color="gold">企业专享</Tag>}
                    </div>
                    <FeatureOperation
                        enable={enable}
                        enableCreate={enableCreate}
                        conversationType={conversationType}
                        applyStatus={applyStatus}
                        createConversation={handleCreateConversation}
                        title={title}
                    />
                </div>
                <p className="text-xs opacity-70 " aria-hidden="true">{description}</p>
            </div>
        </div>
    );
}

// eslint-disable-next-line complexity
export default function InitPage({engineInitialized}: {engineInitialized: boolean}) {
    const {config: {licenseTypeCode}} = useExtensionConfig();
    const {agentStatus, agentApplyStatus, updateAgentStatus, updateAgentApplyStatus} = useAgentContext();
    const [agentApplied, setAgentApplied] = useState<boolean>(false);
    const isIndividual = isSaas && ['INDIVIDUAL', 'TRIAL_INDIVIDUAL'].includes(licenseTypeCode);
    const buttonContent = isIndividual ? '开通企业并试用' : '申请试用';
    const {theme} = useChatConfig();
    const {log} = useLog();

    const handleButtonClick = useCallback(
        async () => {
            messageHandler.send(EventMessage.LinkClickEvent, 'https://comate.baidu.com/zh/survey?track=comateAgentApplyFromIDE');
            setAgentApplied(true);
            const logData = {
                category: 'smartAgent',
                label: 'agentApply',
                data: {licenseTypeCode},
            };
            if (isVSCode) {
                messageHandler.send(EventMessage.UploadUserActionLog, logData);
            }
            else {
                log(logData);
            }
        },
        [licenseTypeCode, log]
    );

    useEffect(
        () => {
            messageHandler.send(EventMessage.AgentSwitchStatusEvent).then((
                {enableIntelligenceAgent, applyStatus}:
                {enableIntelligenceAgent: AgentConfig, applyStatus: AgentApplyStatus | null}
            ) => {
                if (enableIntelligenceAgent) {
                    const agent = getAgentStatusWithApply(enableIntelligenceAgent, applyStatus);
                    updateAgentStatus(agent);
                }
                updateAgentApplyStatus(applyStatus || defaultApplyStatus);
            });
        },
        [updateAgentStatus, updateAgentApplyStatus]
    );

    const bg = theme === 'light' ? lightBg : darkBg;
    const fg = theme === 'light' ? lightFg : darkFg;
    const bottomBg = theme === 'light' ? lightBottomBg : darkBottomBg;
    const shinyIcon = theme === 'light' ? lightShinyIcon : darkShinyIcon;
    const shinyClass = theme === 'light' ? 'w-[26px] h-[26px] ml-[-2px]' : 'w-[30px] h-[30px] ml-[-4px]';

    const shouldShowCompletionBot = (isVSCode && isInternal) || isJetbrains;
    const shouldShowDebugBot = (isVSCode && isInternal) || isJetbrains;

    return (
        <div
            className="agent-container"
            style={{backgroundImage: `url(${fg}), url(${bg}), url(${bottomBg})`}}
        >
            <div className="text-[28px] leading-7 mt-16 flex items-center" aria-hidden="true">
                <img
                    className="h-[36px]"
                    src={theme === 'light' ? lightTitle : darkTitle}
                />
                <img
                    className="h-[36px]"
                    src={theme === 'light' ? lightAgent : darkAgent}
                />
                <div className={`${shinyClass} mt-[-16px]`} dangerouslySetInnerHTML={{__html: shinyIcon}} />
            </div>
            <p className="opacity-80" aria-hidden="true">独立规划，自我反思，代码助手全面升级智能体</p>
            <div className="mx-3 mt-10 flex flex-col gap-4">
                <Feature
                    title="全栈编程智能体"
                    description="自主理解需求、拆解任务，实现完整编程任务"
                    conversationType={WebviewAgentConversationType.E2EBotConversation}
                    icon={e2eBotIcon}
                    enableCreate={engineInitialized}
                    enable={agentStatus.enableFullStackIntelligence}
                    applyStatus={agentApplyStatus.fullStackIntelligenceApplyStatus}
                />
                {shouldShowCompletionBot && (
                    <Feature
                        title="编码智能体"
                        description="智能预判下一个开发位置，实现多点改写、跨文件编程"
                        icon={completionsBotIcon}
                        conversationType={WebviewAgentConversationType.CompletionBotConversation}
                        enable={agentStatus.enableCompletionIntelligence}
                        applyStatus={agentApplyStatus.completionIntelligenceApplyStatus}
                    />
                )}
                {shouldShowDebugBot && (
                    <Feature
                        title="Debug智能体"
                        description="错误信息逐步排查，自我修复验证，并基于反馈优化"
                        icon={debugBotIcon}
                        conversationType={WebviewAgentConversationType.DebugBotConversation}
                        enable={agentStatus.enableDebugIntelligence}
                        applyStatus={agentApplyStatus.debugIntelligenceApplyStatus}
                    />
                )}
                {isJetbrains && (
                    <Feature
                        title="单测智能体"
                        description="测试全面覆盖，自动结果验证，有效发现错误"
                        icon={testBotIcon}
                        conversationType={WebviewAgentConversationType.TestBotConversation}
                        enable={agentStatus.enableUTChatIntelligence || agentStatus.enableUTEditorIntelligence}
                        applyStatus={agentApplyStatus.unitTestIntelligenceApplyStatus}
                    />
                )}
                <Feature
                    title="安全智能体"
                    description="安全问题自感知，全流程扫描修复，修复一键采纳"
                    icon={secuBotIcon}
                    conversationType={WebviewAgentConversationType.SecuBotConversation}
                    enable={agentStatus.enableSecurityIntelligence}
                    applyStatus={agentApplyStatus.securityIntelligenceApplyStatus}
                />
            </div>
            {Object.values(agentApplyStatus).every(agent => !agent) && (
                <div className="mt-8 py-3 px-8 flex w-full">
                    <Button type="primary" onClick={handleButtonClick}>
                        {agentApplied ? '已申请' : buttonContent}
                    </Button>
                </div>
            )}
        </div>
    );
}
