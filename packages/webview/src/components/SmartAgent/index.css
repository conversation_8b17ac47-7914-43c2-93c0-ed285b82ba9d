.agent-container {
    background-size: 360px 360px, 100% auto, 320px 180px;
    background-position: right top, center top, left bottom;
    background-repeat: no-repeat;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    overflow: auto;
}

.gradient-text {
  position: relative;
  display: inline-block;
}

.gradient-text1, .gradient-text2 {
  display: block;
  background: radial-gradient(136.36% 207.69% at 30.95% -6.82%, #FEFFFF 0%, #FEFFFF 22.29%, rgba(255, 255, 255, 0.60) 47.9%, rgba(255, 255, 255, 0.05) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-text2 {
  position: relative;
  background: linear-gradient(199deg, #03F8E7 -24.61%, #6391F9 101.19%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-top: -42px;
  opacity: 0.7;
}

.conversation-container {
    background-size: 280px 280px, 100% auto;
    background-repeat: no-repeat;
    background-position:  right top, center top;
    height: 100%;
    padding-top: 4px;
}

.message-container {
    padding-top: 16px;
    background-repeat: no-repeat;
    background-size: 138px;
    background-position: -1px 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.agent-title {
    height: 24px;
    gap: 4px;
    border: 1px solid transparent;
    border-radius: 34px;
    position: relative;
    background-color: var(--comate-editor-background);
    background-clip: padding-box;
    display: flex;
    align-items: center;
    padding: 8px 8px;
  }

  .agent-title-text {
    background: linear-gradient(66.69deg, #17C3E5 28.85%, #7C63F9 98.48%);
    background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  }

  .init-title-text {
    background: linear-gradient(198.79deg, #03F8E7 -24.61%, #6391F9 101.19%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .agent-title::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    z-index: -1;
    margin: -1px;
    border-radius: inherit;
    background: linear-gradient(66.69deg, #17C3E5 28.85%, #7C63F9 98.48%);
  }

  /* 智能体灰色按钮边框、背景色 */
.agent-defaultButton {
  border-color: var(--comate-button-border);
  background-color: var(--comate-grayButton-background);
}

/* 智能体品牌色按钮边框、背景色 */
.agent-primaryButton {
  border-color: var(--comate-link-color);
  background-color: rgba(3, 202, 238, .15);
  color:var(--comate-link-color);
}
/* 智能体灰色按钮悬浮 */
.agent-defaultButton:hover {
  opacity: 0.7;
}
/* 智能体灰色按钮点击时 */
.agent-defaultButton:active {
  opacity: 0.5;
}
/* 智能体品牌色按钮悬浮时 */
.agent-primaryButton:hover {
  opacity: 0.7;
}
/* 智能体品牌色按钮点击时 */
.agent-primaryButton:active{
  opacity: 0.5;
}