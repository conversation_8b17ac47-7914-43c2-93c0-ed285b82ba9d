/* bca-disable */
import {useEffect, useRef} from 'preact/hooks';
import {EventMessage, WebviewAgentConversationStatus} from '@shared/protocols';
import closeIcon from '@/assets/closeOutline.svg';
import {messageHandler} from '@/utils/messageHandler';
import bg from '@/assets/agent/messageBackground.png';
import {useAgentContext} from './AgentProvider';
import {typeMapping} from './constants';
import Message from './Message';
import './index.css';
import SmartAgentWelcome from './SmartAgentWelcome';
import {renderAgentIcon} from './utils';
import useEnsureActiveElementInViewport from './hooks/useEnsureActiveElementInViewport';
interface Props {
    avatarUrl: string;
    username: string;
    chineseName?: string;
}

export default function Messages(props: Props) {
    const {
        foregroundConversation,
        foregroundConversationMessages,
        updateForegroundConversation,
        updateForegroundConversationMessages,
        closeConversation,
        clearForegroundConversationMessages,
    } = useAgentContext();

    const messageContainer = useRef<HTMLDivElement>(null);
    const anchor = useRef<HTMLDivElement>(null);

    // 自动滚动到最底部
    useEnsureActiveElementInViewport(
        anchor.current,
        messageContainer.current,
        {
            rootMargin: '1px',
            threshold: 0,
        },
        foregroundConversation?.status === WebviewAgentConversationStatus.Running
    );

    useEffect(
        () => {
            // 数据页获取到消息更新，更新消息
            messageHandler.listen(
                EventMessage.AgentConversationMessageUpdateEvent,
                ({messages, conversationInfo, type}) => {
                    if (conversationInfo.id !== foregroundConversation?.id) {
                        return;
                    }
                    if (type === 'conversation-messages' && messages) {
                        updateForegroundConversationMessages(conversationInfo, messages);
                    }
                    else if (type === 'conversation-status') {
                        updateForegroundConversation(conversationInfo);
                    }
                }
            );
        },
        [foregroundConversation?.id, updateForegroundConversation, updateForegroundConversationMessages]
    );

    useEffect(
        () => {
            // 切换到列表后，确保前台智能体返回一次消息
            if (foregroundConversation?.id) {
                clearForegroundConversationMessages();

                messageHandler.send(EventMessage.AgentConversationSetForegroundEvent, {
                    agentPayload: {
                        conversationId: foregroundConversation.id,
                    },
                });
            }

            requestAnimationFrame(
                () => {
                    // 获取一次消息后，滚动到底部
                    if (messageContainer.current) {
                        messageContainer.current.scrollTop = messageContainer.current.scrollHeight;
                    }
                }
            );
        },
        [clearForegroundConversationMessages, foregroundConversation?.id]
    );

    if (!foregroundConversation) {
        return null;
    }

    return (
        <div className="flex flex-col h-full">
            <div
                className="message-container py-0"
                style={{backgroundImage: `url(${bg})`}}
            >
                <div className="flex justify-between items-center w-full px-4">
                    <div className="flex-grow flex justify-center">
                        <div className="inline-block pt-4 pb-3 ">
                            <div className="agent-title" aria-hidden="true">
                                {renderAgentIcon(foregroundConversation.type, 14)}
                                <div className="agent-title-text">
                                    {typeMapping[foregroundConversation.type]} 智能体
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="text-right flex-none cursor-pointer" onClick={closeConversation} aria-label={'点击退出与全栈编程智能体对话'} role="button">
                        <div
                            className="w-6 h-6"
                            aria-hidden="true"
                            // bca-disable-line
                            dangerouslySetInnerHTML={{__html: closeIcon}}
                        />
                    </div>
                </div>
            </div>
            <div
                ref={messageContainer}
                className="overflow-y-auto"
            >
                {foregroundConversationMessages.length === 0 && (
                    <SmartAgentWelcome type={foregroundConversation.type} {...props} />
                )}
                {foregroundConversationMessages.map((message, i) => {
                    const isLatest = i === foregroundConversationMessages.length - 1;
                    return (
                        <Message
                            key={message.id}
                            type={foregroundConversation.type}
                            message={message}
                            isLatest={isLatest}
                            {...props}
                        />
                    );
                })}
                <span ref={anchor} />
            </div>
        </div>
    );
}
