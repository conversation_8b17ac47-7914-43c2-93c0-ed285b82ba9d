import {useCallback} from 'preact/hooks';
import {JsxMarkdown} from '@/components/Markdown';
import {FileAction} from '@/components/Markdown/ComposerFileIndicator';
import {useAgentContext} from '../AgentProvider';
import {SecuBotProvider} from './secuBotProvider';
interface Props {
    content: string;
    messageId: string;
    isLatest?: boolean;
}
export interface ComposerStatus {
    status: string;
    accepted: string;
    isLatest?: boolean;
}
export const SecuBot = ({content, messageId, isLatest = true}: Props) => {
    const {createConversationMessage} = useAgentContext();
    const handleFileClick = useCallback(
        (fileAction: FileAction) => {
            createConversationMessage('message-operation', fileAction);
        },
        [createConversationMessage]
    );

    return (
        <SecuBotProvider isLatest={isLatest} messageid={messageId}>
            <JsxMarkdown
                role="assistant"
                messageId={messageId}
                content={content}
                onFileClick={handleFileClick}
            />
        </SecuBotProvider>
    );
};
