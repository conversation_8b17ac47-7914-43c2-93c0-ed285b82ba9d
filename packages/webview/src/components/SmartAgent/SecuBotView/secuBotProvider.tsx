import {createContext} from 'preact';
import {ReactElement} from 'preact/compat';
import {useEffect, useState} from 'preact/hooks';
interface Props {
    children: ReactElement;
    messageid: string;
    isLatest: boolean;
}
interface MessageContextValue {
    messageId: string;
    isLastMessage: boolean;
    setMessageId: (id: string) => void;
    setIsLastMessage: (islast: boolean) => void;
}

export const SecuBotContext = createContext<MessageContextValue>({
    messageId: '',
    isLastMessage: true,
    setMessageId: () => {},
    setIsLastMessage: () => {},
});

export const SecuBotProvider = ({children, messageid, isLatest}: Props) => {
    const [messageId, setMessageId] = useState(String(messageid));
    const [isLastMessage, setIsLastMessage] = useState<boolean>(isLatest);
    useEffect(() => {
        setIsLastMessage(isLatest);
    }, [isLatest]);
    return (
        <SecuBotContext.Provider value={{messageId, setMessageId, isLastMessage, setIsLastMessage}}>
            {children}
        </SecuBotContext.Provider>
    );
};
