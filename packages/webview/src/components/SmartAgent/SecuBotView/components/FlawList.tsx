/* bca-disable */
interface Props {
    flaws: SecubotFlaw[];
    className?: string;
    showFixable?: boolean;
    filePath?: string;
}
import cx from 'classnames';
import {useCallback} from 'preact/hooks';
import {SecubotFlaw} from '@comate/plugin-shared-internals/dist/bridge/secubot';
import {EventMessage} from '@shared/protocols';
import repairableIcon from '@/assets/agent/repairableIcon.svg';
import {messageHandler} from '@/utils/messageHandler';
import {getFLawDetailMarkdown} from './FlawDetail/detailMarkdown';
import {FlawLevel} from './FlawLevel';
export const FlawList = ({flaws, className, showFixable = false, filePath}: Props) => {
    const handleButtonClick = useCallback(
        async (item: SecubotFlaw) => {
            messageHandler.send(
                EventMessage.ShowCodeBlockEvent,
                {
                    viewColumn: 1,
                    filePath: filePath,
                    startLine: item.start - 1,
                    endLine: item.end - 1,
                }
            );
            await new Promise(resolve => setTimeout(resolve, 100)); // todo 确保打开第一栏标签页再打开第二栏的漏洞详情
            messageHandler.send(EventMessage.PanelDataSendEvent, {type: 'secubot', data: item.details});
            messageHandler.send(EventMessage.ShowWebviewPanelEvent, {
                md: getFLawDetailMarkdown(item.details),
                title: '漏洞详情',
                viewColumn: 2,
            });
        },
        []
    );
    return (
        <div className={cx('flex flex-col gap-y-2', className)}>
            {flaws && flaws.length > 0 && flaws.map((item: SecubotFlaw) => {
                return (
                    <div
                        key={item.title + item.start + item.end}
                        className={cx(
                            // eslint-disable-next-line max-len
                            'flex justify-between font-pingfang text-[#999A9E] text-xs leading-tight font-normal text-left opacity-90',
                            className
                        )}
                    >
                        <div
                            onClick={() => {
                                handleButtonClick(item);
                            }}
                            // eslint-disable-next-line max-len
                            className="flex w-8 flex-1 gap-2 items-center whitespace-nowrap overflow-hidden cursor-pointer text-ellipsis"
                        >
                            <FlawLevel
                                wrapClassName="flex-shrink-0"
                                textClassName="px-0.5 text-[11px]"
                                flawType={item.flawType}
                            />
                            <div className="flex w-0 flex-1">
                                <div className="whitespace-nowrap overflow-hidden text-ellipsis">
                                    {item.title}
                                </div>
                                {(item.fixStatus === 'true' && showFixable) && (
                                    <div
                                        className="w-9 flex-shrink-0"
                                        dangerouslySetInnerHTML={{__html: repairableIcon}}
                                    />
                                )}
                            </div>
                        </div>
                        <div className="flex-shrink-0">
                            {`line [${item.start}-${item.end}]`}
                        </div>
                    </div>
                );
            })}
        </div>
    );
};
