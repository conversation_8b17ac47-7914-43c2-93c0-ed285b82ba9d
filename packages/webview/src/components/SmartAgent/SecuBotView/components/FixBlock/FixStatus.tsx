import {LoadingIcon} from '@comate/plugin-jsx';
import {useEffect, useState} from 'preact/hooks';

interface Props {
    fixStatus: string;
    cancellationToken?: boolean;
}
const statusDic = {
    reparing: '修复中...',
    verifying: '验证中...',
    fail: '修复失败',
    verifyFailed: '验证失败',
};
export const FixStatus = ({fixStatus, cancellationToken = false}: Props) => {
    const [content, setContent] = useState('修复中...');
    const [loading, setLoading] = useState(false);
    useEffect(() => {
        if (cancellationToken && ['repairing', 'verifying'].includes(fixStatus)) {
            setContent('已停止');
            return;
        }
        setContent(statusDic[fixStatus] || '修复中...');
        setLoading(['repairing', 'verifying'].includes(fixStatus) && !cancellationToken);
    }, [cancellationToken, fixStatus]);
    return (
        <div className="flex gap-1 items-center">
            {loading && <LoadingIcon></LoadingIcon>}
            <span
                className={`${
                    loading
                        ? 'text-[var(--comate-link-color)]'
                        : 'text-[#BFBFBF]'
                } whitespace-nowrap overflow-hidden text-ellipsis`}
            >
                {content}
            </span>
        </div>
    );
};
