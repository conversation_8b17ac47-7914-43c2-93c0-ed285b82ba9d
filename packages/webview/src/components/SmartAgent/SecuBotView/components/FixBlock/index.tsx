import {useCallback, useContext, useEffect, useState} from 'preact/hooks';
import {FileAction} from '@/components/Markdown/ComposerFileIndicator';
import {useAgentContext} from '@/components/SmartAgent/AgentProvider';
import {messageHandler} from '@/utils/messageHandler';
import {FlawList} from '../FlawList';
import {SecuBotContext} from '../../secuBotProvider';
import {Actions} from '../ActionButons';
import {SecubotFileFlaw, SecubotFlaw} from '@comate/plugin-shared-internals/dist/bridge/secubot';
import GradientCard from '@/components/SmartAgent/GradientCard';
import {FixStatus} from './FixStatus';
import {EventMessage} from '@shared/protocols';

export const FixBlock = ({
    cancellationToken,
    type,
    fixStatus,
    children,
    fileName,
    filePath,
    action,
    status,
    accepted,
}: SecubotFileFlaw) => {
    const {createConversationMessage} = useAgentContext();
    const {messageId} = useContext(SecuBotContext);
    const handleFileClick = useCallback(
        (fileAction: FileAction) => {
            if (fixStatus === 'success') {
                createConversationMessage('message-operation', fileAction);
                return;
            }
            messageHandler.send(
                EventMessage.ShowCodeBlockEvent,
                {
                    viewColumn: 1,
                    filePath: filePath,
                }
            );
        },
        [createConversationMessage]
    );
    const getGradient = useCallback((children: SecubotFlaw[]) => {
        let highestLevel = 'L';
        const levels = children.map(flaw => flaw.flawType);
        // 取具体漏洞中最高的等级作为 文件的整体漏洞等级
        for (let i = 0; i < levels.length; i++) {
            const currentLevel = levels[i];
            if (currentLevel === 'C') {
                highestLevel = 'C';
                break;
            }
            else if (currentLevel === 'H' && highestLevel !== 'C') {
                highestLevel = 'H';
            }
            else if (currentLevel === 'M' && highestLevel !== 'C' && highestLevel !== 'H') {
                highestLevel = 'M';
            }
        }
        switch (highestLevel) {
            case 'C':
                return 'secubot-red';
            case 'H':
                return 'secubot-orange';
            case 'M':
                return 'secubot-yellow';
            case 'L':
                return 'secubot-gray';
            default:
                return 'secubot-gray';
        }
    }, []);
    return (
        <GradientCard className="font-pingfang" color={getGradient(children)}>
            <div className="flex justify-between font-normal leading-6 mb-[14px]">
                <span
                    className="flex-1 w-0 overflow-hidden whitespace-nowrap text-ellipsis cursor-pointer"
                    onClick={() => handleFileClick({action: 'file-view', filePath, id: messageId})}
                >
                    {fileName}
                </span>
                {type === 'repair' && (
                    <div className="flex flex-shrink-0 items-center leading-6">
                        {fixStatus === 'success'
                            ? (
                                <Actions
                                    action={action}
                                    accepted={accepted}
                                    status={status}
                                    filePath={filePath}
                                />
                            )
                            : <FixStatus fixStatus={fixStatus} cancellationToken={cancellationToken} />}
                    </div>
                )}
            </div>
            <FlawList
                flaws={children}
                filePath={filePath}
            >
            </FlawList>
        </GradientCard>
    );
};
