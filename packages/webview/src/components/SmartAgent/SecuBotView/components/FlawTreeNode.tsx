/* bca-disable */
import {useCallback, useContext, useState} from 'preact/hooks';
import {useAgentContext} from '../../AgentProvider';
import {FileAction} from '@/components/Markdown/ComposerFileIndicator';
import {SecuBotContext} from '../secuBotProvider';
import rightIcon from '@/assets/right.svg';
import {Actions} from './ActionButons';
import {FlawList} from './FlawList';
import {SecubotFileFlaw} from '@comate/plugin-shared-internals/dist/bridge/secubot';
import {messageHandler} from '@/utils/messageHandler';
import {EventMessage} from '@shared/protocols';
interface Props {
    content: SecubotFileFlaw;
}
export const FlawTreeNode = ({content}: Props) => {
    const [isCollapsed, setIsCollapsed] = useState(false);
    const {messageId} = useContext(SecuBotContext);
    const {createConversationMessage} = useAgentContext();
    const handleFileClick = useCallback(
        (fileAction: FileAction) => {
            if (content.content !== '') {
                createConversationMessage('message-operation', fileAction);
                return;
            }
            messageHandler.send(
                EventMessage.ShowCodeBlockEvent,
                {
                    viewColumn: 1,
                    filePath: content.filePath,
                }
            );
        },
        [createConversationMessage]
    );
    return (
        <div className="my-2 rounded-tl-[0.25rem] flex flex-col gap-1 w-full">
            <div className="flex gap-1 justify-between items-center ">
                <div className="flex flex-1 w-2 gap-2 items-center justify-start">
                    <div className="flex-shrink-0 cursor-pointer">
                        {isCollapsed
                            ? (
                                <div
                                    className="w-3.5 h-3.5"
                                    dangerouslySetInnerHTML={{__html: rightIcon}}
                                    onClick={() => setIsCollapsed(!isCollapsed)}
                                />
                            )
                            : (
                                <div
                                    className="w-3.5 h-3.5 rotate-90"
                                    dangerouslySetInnerHTML={{__html: rightIcon}}
                                    onClick={() => setIsCollapsed(!isCollapsed)}
                                />
                            )}
                    </div>
                    <span
                        className="font-pingfang flex-1 w-0 cursor-pointer whitespace-nowrap overflow-hidden text-ellipsis"
                        onClick={() =>
                            handleFileClick({action: 'file-view', filePath: content.filePath, id: messageId})}
                    >
                        {content.fileName}
                    </span>
                </div>
                {content.content !== '' && (
                    <div className="flex items-center leading-6">
                        <Actions
                            action={content.action}
                            accepted={content.accepted}
                            status={content.status}
                            filePath={content.filePath}
                        />
                    </div>
                )}
            </div>
            {!isCollapsed && (
                <div className="py-1.5">
                    <FlawList className="ml-3" showFixable flaws={content.children} filePath={content.filePath} />
                </div>
            )}
        </div>
    );
};
