import {useCallback, useEffect, useRef, useState} from 'preact/hooks';
import {SecubotCard, SecubotFileFlaw} from '@comate/plugin-shared-internals/dist/bridge/secubot'; // 确保引入了定义了自定义类的CSS文件
import AgentCollapsePanel from '@/components/CollapsePanel/AgentCollapsePanel';
import {LoadingText} from '../../../../LoadingText';
import {FixBlock} from '../FixBlock';
import './index.css';
export const CollapsibleCard = ({
    cancellationToken = false,
    loading,
    type,
    bottomTip,
    children = [] as SecubotFileFlaw[],
}: SecubotCard) => {
    const prevLoadingRef = useRef(loading);
    const [isOpen, setIsOpen] = useState(true);
    const toggleCard = useCallback(
        () => {
            setIsOpen(!isOpen);
        },
        [isOpen]
    );
    useEffect(
        () => {
            if (cancellationToken) {
                setIsOpen(false);
            }
        },
        [cancellationToken]
    );
    // 当loading 从 true 变成 false 时 关闭card
    useEffect(
        () => {
            if (prevLoadingRef.current && !loading) {
                setIsOpen(false);
            }
            prevLoadingRef.current = loading;
        },
        [loading]
    );
    return (
        <div className="my-2">
            <AgentCollapsePanel
                loadingText={type === 'scan' ? '正在扫描漏洞' : '正在修复漏洞'}
                loading={loading && !cancellationToken}
                collapsible={!isOpen}
                onChange={toggleCard}
                title={type === 'scan' ? '漏洞扫描' : '漏洞修复'}
                content={
                    <>
                        <div className=" flex flex-col gap-y-2 mt-[14px] mb-[10px]">
                            {children.length > 0
                                && children.map((item: SecubotFileFlaw) => {
                                    return (
                                        <>
                                            {item.description && (
                                                <div
                                                    className="py-3 text-wrap whitespace-normal text-left"
                                                >
                                                    {item.description}
                                                </div>
                                            )}
                                            <div>
                                                <FixBlock cancellationToken={cancellationToken} {...item} type={type} />
                                            </div>
                                        </>
                                    );
                                })}
                        </div>
                        <div>
                            <LoadingText
                                loading={loading && !cancellationToken}
                                content={bottomTip}
                            />
                        </div>
                    </>
                }
            />
        </div>
    );
};

export default CollapsibleCard;
