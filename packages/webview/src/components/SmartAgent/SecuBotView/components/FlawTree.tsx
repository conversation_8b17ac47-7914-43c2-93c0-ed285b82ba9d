import {SecubotFileFlaw} from '@comate/plugin-shared-internals/dist/bridge/secubot';
import {FlawTreeNode} from './FlawTreeNode';
interface Props {
    type: string;
    children: SecubotFileFlaw[];
}
const FlawTree = ({children, type}: Props) => {
    return (
        <div className="my-3">
            {children.map((item: SecubotFileFlaw) => {
                return <FlawTreeNode content={item} />;
            })}
        </div>
    );
};

export default FlawTree;
