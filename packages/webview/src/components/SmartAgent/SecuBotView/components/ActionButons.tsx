/* bca-disable */
import {useCallback, useContext, useState} from 'preact/hooks';
import {FileAction} from '@/components/Markdown/ComposerFileIndicator';
import {useAgentContext} from '../../AgentProvider';
import AcceptIcon from '@/assets/agent/accept.svg';
import RefuseIcon from '@/assets/agent/refuse.svg';
import {SecuBotContext} from '../secuBotProvider';
interface Props {
    filePath: string;
    status?: string;
    accepted?: number;
    action?: string;
}

export const Actions = ({filePath, accepted}: Props) => {
    const {createConversationMessage} = useAgentContext();
    const {messageId, isLastMessage} = useContext(SecuBotContext);
    const handleFileClick = useCallback(
        (fileAction: FileAction) => {
            createConversationMessage('message-operation', fileAction);
        },
        [createConversationMessage]
    );
    const getStatusIcon = useCallback((accepted: number) => {
        switch (accepted) {
            case 1:
                return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: AcceptIcon}} />;
            case 2:
                return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: RefuseIcon}} />;
            default:
                return null;
        }
    }, [accepted]);
    return (
        isLastMessage && (
            <div className="flex items-center justify-end gap-x-2.5 text-[var(--comate-link-color)]  ">
                {getStatusIcon(accepted || 0)}
                <button
                    onClick={() => {
                        handleFileClick({action: 'file-accept', filePath, id: messageId});
                    }}
                >
                    <span className="whitespace-nowrap overflow-hidden text-ellipsis">
                        采纳
                    </span>
                </button>
                <button
                    onClick={() => {
                        handleFileClick({action: 'file-reject', filePath, id: messageId});
                    }}
                >
                    <span className="whitespace-nowrap overflow-hidden text-ellipsis">
                        放弃
                    </span>
                </button>
            </div>
        )
    );
};
