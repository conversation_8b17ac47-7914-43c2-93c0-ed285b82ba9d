import {useEffect, useState} from 'preact/hooks';
import cx from 'classnames';
interface Props {
    flawType: string;
    isShortCut?: boolean;
    wrapClassName?: string;
    textClassName?: string;
}
interface Style {
    border: string;
    color: string;
    text: string;
}
export const FlawLevel = ({flawType, isShortCut = true, wrapClassName, textClassName}: Props) => {
    const [style, setStyle] = useState<Style>();
    useEffect(() => {
        switch (flawType) {
            case 'C':
                setStyle({border: '#E545524D', color: '#E54552', text: isShortCut ? '严' : '严重'});
                break;
            case 'H':
                setStyle({border: '#FA7E254D', color: '#FA7E25', text: isShortCut ? '高' : '高危'});
                break;
            case 'M':
                setStyle({border: '#FFC2334D', color: '#FFC233', text: isShortCut ? '中' : '中危'});
                break;
            case 'L':
                setStyle({border: '#BFBFBF4D', color: '#BFBFBF', text: isShortCut ? '低' : '低危'});
                break;
            default:
                setStyle({border: '#BFBFBF4D', color: '#BFBFBF', text: isShortCut ? '低' : '低危'});
        }
    }, [flawType, isShortCut]);
    return (
        <div
            style={{borderColor: style?.border}}
            className={cx(`inline-block  border-[1px] rounded-[3.6px] text-[${style?.color}] `, wrapClassName)}
        >
            <span className={textClassName}>
                {style
                    ?.text}
            </span>
        </div>
    );
};
