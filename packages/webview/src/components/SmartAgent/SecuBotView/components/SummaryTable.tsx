import {useEffect, useState} from 'preact/hooks';
interface Props {
    type: string;
    sum: number[];
    fix: number[];
}
interface TableProps {
    name: string;
    values: number[];
}
const SummaryTable = ({sum, fix}: Props) => {
    if (sum.length <= 0 && fix.length <= 0) {
        return null;
    }
    const [data, setData] = useState<TableProps[]>([]);
    useEffect(() => {
        setData([
            {name: '总数', values: sum},
            {name: '修复', values: fix},
        ]);
    }, [sum, fix]);

    const columns = [
        {name: '严重', className: 'text-[#E54552]'},
        {name: '高危', className: 'text-[#FA7E25]'},
        {name: '中危', className: 'text-[#FFC233]'},
        {name: '低危', className: 'text-[#BFBFBF]'},
    ];

    return (
        <div className="mx-auto my-2 w-full rounded-[4px] comate-content-table-container ">
            <table className="bg-transparent w-full font-pingfang text-xs">
                <thead>
                    <tr className="text-[13px]">
                        <th className="  text-center h-7 leading-7">
                        </th>
                        {columns.map((col, index) => (
                            <th
                                key={index}
                                className={`font-normal text-center h-[28px] leading-[28px] ${col.className}`}
                            >
                                {col.name}
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody>
                    {data.map((row, rowIndex) => (
                        row.values && row.values.length > 0 && (
                            <tr key={rowIndex}>
                                <td className=" text-center custom-table  h-[28px] custom-table leading-[28px]">
                                    {row.name}
                                </td>
                                {row.values.map((value, colIndex) => (
                                    <td
                                        key={colIndex}
                                        className="custom-table text-center h-[28px] leading-[28px]"
                                    >
                                        {value}
                                    </td>
                                ))}
                            </tr>
                        )
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default SummaryTable;
