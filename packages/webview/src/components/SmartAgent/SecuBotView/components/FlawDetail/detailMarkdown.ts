import {SecubotFlawDetail} from '@comate/plugin-shared-internals/dist/bridge/secubot';
import {FlawDetailsRow} from './index';

export const getFLawDetailMarkdown = (details: SecubotFlawDetail) => {
    const rows: FlawDetailsRow[] = [
        {title: '漏洞编码', content: details.id},
        {title: '漏洞描述', content: details.description},
        {title: '修复建议', content: details.advice},
        {title: '引入路径', content: details.importPath},
    ];
    return `<div style="display: flex; flex-direction: column; gap: 5px">
<h1>${details.title}</h1>
    ${
        rows
            .filter(item => item.content)
            .map(item =>
                `<div style="line-height: 20px;font-size:13px">
            <p style="color: #999A9E">${item.title}</p>
        <p>${item.content}</p>
    </div>`
            )
            .join('\n')
    }
</div>
`;
};
