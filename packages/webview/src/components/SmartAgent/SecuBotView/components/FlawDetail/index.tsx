/* bca-disable */
import {SecubotFlawDetail} from '@comate/plugin-shared-internals/dist/bridge/secubot';
import './index.css';
import {FlawLevel} from '../FlawLevel';
export interface FlawDetailsRow {
    title: string;
    content?: string;
}
export const FlawDetail = ({id, description, references, title, flawType, advice, importPath}: SecubotFlawDetail) => {
    const rows: FlawDetailsRow[] = [
        {title: '漏洞编码', content: id},
        {title: '漏洞描述', content: description},
        {title: '修复建议', content: advice},
        {title: '引入路径', content: importPath},
    ];
    return (
        <div className="theme comate-article flex flex-col w-full h-full font-pingfang">
            <div className="text-wrap pt-10 px-[26px] pb-8">
                <span className="text-[28px] align-middle font-semibold">{title}</span>
                <FlawLevel
                    wrapClassName="ml-0.5"
                    textClassName="px-1.5 text-[14px]"
                    flawType={flawType || 'none'}
                    isShortCut={false}
                />
            </div>
            <div className="grid gap-y-6 px-6">
                {rows.map(item => {
                    return item.content && (
                        <div className="leading-5.5 gap-y-2 text-[13px]">
                            <div className=" text-[#999A9E]">{item.title}</div>
                            <div className="mt-2">
                                {item.content}
                            </div>
                        </div>
                    );
                })}
            </div>
            {references && (
                <div className="mt-5 mx-6 border-[1px] border-dashed border-[#323331]">
                </div>
            )}
            {references
                && (
                    <div className="mt-4 text-[13px]  px-6">
                        <div className=" text-[#999A9E] pb-4 ">
                            参考链接
                        </div>
                        <div className="grid gap-y-2">
                            {references.map(item => (
                                <div className="h-7 ">
                                    <div className="inline-block whitespace-nowrap text-ellipsis overflow-hidden rounded-full link-gradient">
                                        <a className="px-3 outline-0" href={item.url}>
                                            <span className=" leading-7 whitespace-nowrap overflow-hidden text-ellipsis">
                                                {item.title}
                                            </span>
                                        </a>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
        </div>
    );
};
