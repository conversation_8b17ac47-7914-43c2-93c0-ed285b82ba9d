import {FileAction} from '@/components/Markdown/ComposerFileIndicator';
import {LoadingText} from '../../LoadingText';
import CollapsibleCard from './components/Card';
import SummaryTable from './components/SummaryTable';
import FlawTree from './components/FlawTree';

interface Props {
    data: any;
    onFileClick?: (action: FileAction) => void;
}

export const SecuComponent = ({data}: Props) => {
    try {
        const objData = JSON.parse(data);
        switch (objData?.type) {
            case 'scan':
                return <CollapsibleCard {...objData} />;
            case 'repair':
                return <CollapsibleCard {...objData} />;
            case 'loadingText':
                return objData.cancellationToken ? null : <LoadingText {...objData} />;
            case 'summaryTable':
                return <SummaryTable {...objData} />;
            case 'flawTree':
                return <FlawTree {...objData} />;
            default:
                return <div>无效的组件类型</div>;
        }
    }
    catch {
        return null;
    }
};
