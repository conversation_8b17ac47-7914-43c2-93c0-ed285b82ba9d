import {useEffect, useState} from 'preact/hooks';

interface ObserverOptions {
    rootMargin?: string;
    threshold?: number;
}

export default function useEnsureActiveElementInViewport(
    targetElement: HTMLElement | null = null,
    rootElement: HTMLElement | null = null,
    options: ObserverOptions = {},
    open: boolean = true
) {
    const [isInView, setIsInView] = useState(false);
    const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(open); // 是否启用自动滚动

    useEffect(
        () => {
            if (!targetElement || !rootElement) {
                return;
            }

            const observerOptions = {
                root: rootElement,
                rootMargin: options.rootMargin || '0px',
                threshold: options.threshold || 0,
            };

            const observer = new IntersectionObserver(entries => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        setIsInView(true);
                    } else {
                        setIsInView(false);
                    }
                    isAutoScrollEnabled && targetElement.scrollIntoView({block: 'end', behavior: 'auto'});
                });
            }, observerOptions);

            observer.observe(targetElement);

            return () => {
                observer.unobserve(targetElement);
                observer.disconnect();
            };
        },
        [rootElement, options.rootMargin, options.threshold, targetElement, isAutoScrollEnabled]
    );

    if (targetElement && !isInView && isAutoScrollEnabled) {
        targetElement.scrollIntoView({block: 'end', behavior: 'auto'});
    }

    // open 参数改变时，重新启用自动滚动
    useEffect(
        () => {
            if (open) {
                setIsAutoScrollEnabled(true);
            }
        },
        [open]
    );


    // 监听滚动事件，根据用户滚动方向控制是否启用自动滚动
    useEffect(
        () => {
            if (!rootElement) {
                return;
            }

            const handleScroll = event => {
                if (event.deltaY < 0) {
                    setIsAutoScrollEnabled(false); // 向上滚动时，禁用自动滚动
                }

                // 判断是否到底部了
                const isBottom =
                    rootElement.scrollHeight - rootElement.scrollTop <= rootElement.clientHeight + 1; // 允许1像素误差
                if (isBottom && !isAutoScrollEnabled) {
                    setIsAutoScrollEnabled(true); // 滚动到底部时，重新启用自动滚动
                }
            };

            // 添加事件监听器
            rootElement.addEventListener('wheel', handleScroll);

            // 清理事件监听器
            return () => {
                rootElement.removeEventListener('wheel', handleScroll);
            };
        },
        [isAutoScrollEnabled, open, rootElement]
    ); // 空依赖数组，确保在组件挂载和卸载时运行


    return isInView;
}
