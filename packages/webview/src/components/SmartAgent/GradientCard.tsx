import {ComponentChildren} from 'preact';
import './GradientCard.css';

interface Props {
    children: ComponentChildren;
    className?: string;
    onClick?: (e: any) => void;
    color: 'blue' | 'red' | 'yellow' | 'secubot-red' | 'secubot-orange' | 'secubot-yellow' | 'secubot-gray';
}

export default function GradientCard({className, children, color, onClick}: Props) {
    return (
        <div className={`gradient-card gradient-card-${color} ${className}`} onClick={onClick}>
            {children}
        </div>
    );
}
