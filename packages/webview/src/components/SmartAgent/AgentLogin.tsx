/* bca-disable */
/* eslint-disable max-len */
import darkBg from '@/assets/agent/darkAgentBackground.png';
import darkFg from '@/assets/agent/darkAgentForeground.png';
import darkBottomBg from '@/assets/agent/darkBottomBackground.png';
import lightBg from '@/assets/agent/lightAgentBackground.png';
import lightFg from '@/assets/agent/lightAgentForeground.png';
import lightBottomBg from '@/assets/agent/lightBottomBackground.png';
import darkTitle from '@/assets/agent/darkThemeTitle.png';
import darkAgent from '@/assets/agent/darkThemeAgent.png';
import lightTitle from '@/assets/agent/lightThemeTitle.png';
import lightAgent from '@/assets/agent/lightThemeAgent.png';
import darkShinyIcon from '@/assets/agent/darkShiny.svg';
import lightShinyIcon from '@/assets/agent/lightShiny.svg';

import {useChatConfig} from '../ChatConfigProvider';
import Login from '../Login';

import './index.css';

export default function AgentLogin() {

    const {theme} = useChatConfig();

    const bg = theme === 'light' ? lightBg : darkBg;
    const fg = theme === 'light' ? lightFg : darkFg;
    const bottomBg = theme === 'light' ? lightBottomBg : darkBottomBg;
    const shinyIcon = theme === 'light' ? lightShinyIcon : darkShinyIcon;
    const shinyClass = theme === 'light' ? 'w-[26px] h-[26px] ml-[-2px]' : 'w-[30px] h-[30px] ml-[-4px]';

    return (
        <div
            className="agent-container"
            style={{backgroundImage: `url(${fg}), url(${bg}), url(${bottomBg})`}}
        >
            <div className="text-[28px] leading-7 mt-16 flex items-center">
                <img
                    className="h-[36px]"
                    src={theme === 'light' ? lightTitle : darkTitle}
                />
                <img
                    className="h-[36px]"
                    src={theme === 'light' ? lightAgent : darkAgent}
                />
                <div className={`${shinyClass} mt-[-16px]`} dangerouslySetInnerHTML={{__html: shinyIcon}} />
            </div>
            <p className="opacity-80">独立规划，自我反思，代码助手全面升级智能体</p>
            {/* login暂时没有适配无障碍 */}
            <Login />
        </div>
    );
}
