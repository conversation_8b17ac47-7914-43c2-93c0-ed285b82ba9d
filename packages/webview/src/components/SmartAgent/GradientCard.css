.gradient-card {
    padding: 10px 12px;
    position: relative;
    border-radius: 4px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.02)), linear-gradient(99.74deg, rgba(64, 128, 255, 0.02) 0%, rgba(14, 27, 53, 0.02) 21.44%);
}

.gradient-card::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 4px;
    padding: 1px;
    background: var(--linear-gradient-color);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: 1;
    pointer-events: none;
}

.gradient-card-blue::before {
    --linear-gradient-color: linear-gradient(112.91deg, rgb(165, 226, 255, 0.2) 0.89%, rgba(112, 217, 140, 0.2) 4.71%, rgba(124, 125, 128, 0.3) 26.72%);
}

.gradient-card-red::before {
    --linear-gradient-color: linear-gradient(112.91deg, rgba(255, 86, 100, 0.3) 0.89%, rgba(229, 69, 82, 0.3) 4.71%, rgba(124, 125, 128, 0.3) 26.72%);
}

.gradient-card-yellow::before {
    --linear-gradient-color: linear-gradient(113.12deg, rgba(255, 214, 1, 0.3) 0.9%, rgba(255, 194, 51, 0.3) 5.3%, rgba(124, 125, 128, 0.3) 27.92%);
}

.gradient-card-secubot-red::before {
    --linear-gradient-color: linear-gradient(112.91deg,rgba(255, 86, 100, 0.3) 0.89%,rgba(229, 69, 82, 0.3) 2.06%,rgba(124, 125, 128, 0.3) 26.72%);
}
.gradient-card-secubot-orange::before {
    --linear-gradient-color: linear-gradient(114.21deg, rgba(255, 143, 39, 0.3) 0.47%, rgba(207, 93, 23, 0.3) 4.73%, rgba(124, 125, 128, 0.3) 28.42%);
}
.gradient-card-secubot-yellow::before {
    --linear-gradient-color: linear-gradient(113.12deg, rgba(255, 214, 1, 0.3) 0.9%, rgba(255, 194, 51, 0.3) 5.3%, rgba(124, 125, 128, 0.3) 27.92%);
}
.gradient-card-secubot-gray::before {
    --linear-gradient-color: linear-gradient(113.12deg, rgba(220, 221, 226, 0.3) 0.9%, rgba(153, 154, 158, 0.3) 5.3%, rgba(135, 140, 156, 0.3) 27.92%);
}