/* eslint-disable complexity */
/* eslint-disable max-len */
/* bca-disable */
import {useState, useCallback, useEffect, useMemo} from 'preact/hooks';
import {EventMessage, DehydratedMessage} from '@shared/protocols';
import {differenceInDays, startOfDay} from 'date-fns';
import {ChatSession} from '@comate/plugin-shared-internals';
import {memo} from 'preact/compat';
import {messageHandler} from '@/utils/messageHandler';
import historyEmptyBox from '@/assets/historyEmptyBox.png';
import closeIcon from '../assets/closeHistory.svg';
import deleteIcon from '../assets/delete.svg';
import leftArrowIcon from '../assets/leftArrows.svg';
import rightArrowIcon from '../assets/rightArrows.svg';
import searchIcon from '../assets/searchHistroy.svg';
import clearIcon from '../assets/clearSearch.svg';
import {useChatConfig} from './ChatConfigProvider';
import Pagination from './Pagination';
import DatePicker from './DatePicker';

interface Props {
    setHistoryVisible: (visible: boolean) => void;
    messages: DehydratedMessage[];
    foucChatTabWhenFocus: () => void;
}

const prevIcon = (
    <span
        className="w-[14px] h-[14px] inline-block text-[var(--comate-editor-foreground)]"
        dangerouslySetInnerHTML={{__html: leftArrowIcon}}
    />
);
const nextIcon = (
    <span
        className="w-[14px] h-[14px] inline-block text-[var(--comate-editor-foreground)]"
        dangerouslySetInnerHTML={{__html: rightArrowIcon}}
    />
);

const processSession = (session: any): string => (
    typeof session === 'object' && session !== null ? JSON.stringify(session) : session
);

const getRelativeDateCategory = (timestamp: number): string => {
    const now = new Date();
    const date = new Date(timestamp);
    const startOfNowDay = startOfDay(now);
    const startOfDateDay = startOfDay(date);
    const daysDiff = differenceInDays(startOfNowDay, startOfDateDay);

    if (daysDiff === 0) {
        return '今天';
    }
    else if (daysDiff === 1) {
        return '昨天';
    }
    else if (daysDiff >= 2 && daysDiff <= 7) {
        return '前七天';
    }
    else if (daysDiff > 7 && daysDiff <= 30) {
        return '前30天';
    }
    else {
        return '更早以前'; // 文案是否要修改
    }
};

const PrevNextArrow = (
    page: number,
    type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next',
    originalElement: React.ReactNode
): React.ReactNode => {
    if (type === 'prev') {
        return <button>{prevIcon}</button> as React.ReactNode;
    }
    if (type === 'next') {
        return <button>{nextIcon}</button> as React.ReactNode;
    }
    return originalElement;
};

// 添加防抖函数
const useDebounce = <T = string>(value: T, delay: number): T => {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(
        () => {
            const timer = setTimeout(
                () => {
                    setDebouncedValue(value);
                },
                delay
            );

            return () => {
                clearTimeout(timer);
            };
        },
        [value, delay]
    );

    return debouncedValue;
};

const HistoryItem = memo(({
    sessionUuid,
    title,
    isLastMessageOfTheDay,
    isFirstMessageOfTheDay,
    relativeDateCategory,
    messages,
    inputValue,
    theme,
    onItemClick,
    onDeleteClick,
}: {
    sessionUuid: string;
    title: string;
    isLastMessageOfTheDay: string;
    isFirstMessageOfTheDay: boolean;
    relativeDateCategory: string;
    messages: DehydratedMessage[];
    inputValue: string;
    theme: string;
    onItemClick: (sessionUuid: string) => void;
    onDeleteClick: (sessionUuid: string) => void;
}) => {
    const handleClick = useCallback(
        () => {
            onItemClick(sessionUuid);
        },
        [sessionUuid, onItemClick]
    );

    const handleDelete = useCallback(
        (event: MouseEvent) => {
            event.stopPropagation();
            onDeleteClick(sessionUuid);
        },
        [sessionUuid, onDeleteClick]
    );

    const highlightedTitle = useMemo(
        () => {
            if (!inputValue) {
                return <span>{title}</span>;
            }

            return title.split(new RegExp(`(${inputValue})`, 'gi')).map((part, i) =>
                (part.toLowerCase() === inputValue.toLowerCase()
                    ? (
                        <span
                            key={`${part}-${'' + i}`}
                            className={'text-[var(--comate-link-color)]'}
                        >
                            {part}
                        </span>
                    )
                    : part)
            );
        },
        [title, inputValue]
    );

    return (
        <div className={`w-full px-4 pb-2 ${isLastMessageOfTheDay}`}>
            {isFirstMessageOfTheDay && (
                <div className="h-[30px] pl-2 text-[12px] opacity-50 font-regular pt-2 pb-1">
                    {relativeDateCategory}
                </div>
            )}
            <div
                className={'flex items-center justify-between px-2 py-1 cursor-pointer hover:bg-[var(--comate-listItem-hoverBackground)] group h-7'}
                onClick={handleClick}
            >
                <p className="flex-1 items-center text-[13px] overflow-hidden overflow-ellipsis whitespace-nowrap">
                    {messages.length > 0 && messages[0].sessionUuid === sessionUuid && (
                        <div
                            className={`text-[13px] ${theme === 'light' ? 'bg-[#999999]/15' : 'bg-[#888888]/20'
                            } w-9 h-5 mr-2.5 rounded inline-block text-center leading-[20px]`}
                        >
                            当前
                        </div>
                    )}
                    {highlightedTitle}
                </p>
                <div
                    className="icon-fill-color hidden group-hover:block w-4 h-4 cursor-pointer hover:bg-[var(--comate-icon-hoverBackground)] text-[var(--comate-editor-foreground)]"
                    dangerouslySetInnerHTML={{__html: deleteIcon}}
                    onClick={handleDelete}
                />
            </div>
        </div>
    );
});

function HistoryChat({setHistoryVisible, messages, foucChatTabWhenFocus}: Props) {
    const [historyChat, setHistoryChat] = useState<ChatSession[]>([]);
    const [size, setSize] = useState(10);
    const [current, setCurrent] = useState(1);
    const [isFocused, setIsFocused] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const debouncedInputValue = useDebounce(inputValue, 300); // 添加 300ms 的防抖
    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);

    useEffect(
        () => {
            (
                async () => {
                    const res = await messageHandler.send(EventMessage.ChatSessionListFetchEvent);
                    if (res) {
                        setHistoryChat(res);
                    }
                }
            )();
        },
        []
    );

    useEffect(
        () => {
            setCurrent(1); // 重置到第一页
        },
        [debouncedInputValue, dateRange]
    );

    // 删除历史会话
    const handleDeleteChat = useCallback(
        (sessionUuid: string) => {
            messageHandler.send(EventMessage.ChatSessionDeleteEvent, sessionUuid);
            messageHandler.listen(EventMessage.ChatSessionListFetchEvent, response => {
                setHistoryChat(response);
            });
        },
        []
    );

    const filteredHistoryChat = useMemo(
        (): ChatSession[] => {
            const [startDate, endDate] = dateRange;
            // 如果没有任何筛选条件，返回全部会话
            if (!debouncedInputValue && !startDate && !endDate) {
                return historyChat;
            }

            // 先按关键字筛选
            let filteredSessions = debouncedInputValue
                ? historyChat.filter(session =>
                    processSession(session.title).toLowerCase().includes(debouncedInputValue.toLowerCase()))
                : historyChat;

            // 再按时间范围筛选
            if (startDate || endDate) {
                filteredSessions = filteredSessions.filter(session => {
                    const sessionDate = new Date(session.utime);

                    // 只有开始时间
                    if (startDate && !endDate) {
                        return sessionDate >= startDate;
                    }
                    // 只有结束时间
                    if (!startDate && endDate) {
                        const endDatePlusOneDay = new Date(endDate);
                        endDatePlusOneDay.setDate(endDate.getDate() + 1);
                        return sessionDate <= endDatePlusOneDay;
                    }
                    // 同时有开始和结束时间
                    if (startDate && endDate) {
                        const endDatePlusOneDay = new Date(endDate);
                        endDatePlusOneDay.setDate(endDate.getDate() + 1);
                        return sessionDate >= startDate && sessionDate <= endDatePlusOneDay;
                    }
                    return true;
                });
            }

            return filteredSessions;
        },
        [dateRange, debouncedInputValue, historyChat]
    );

    // 拿到当前页历史记录
    const getCurrentPageHistoryChat = useCallback(
        (current: number, pageSize: number) => {
            const startIndex = (current - 1) * pageSize;
            const endIndex = current * pageSize;
            return filteredHistoryChat.slice(startIndex, endIndex);
        },
        [filteredHistoryChat]
    );
    const currentPageHistoryChat = useMemo(
        () => getCurrentPageHistoryChat(current, size),
        [current, size, getCurrentPageHistoryChat]
    );

    const PaginationChange = useCallback(
        (page: number, pageSize: number) => {
            setCurrent(page);
            setSize(pageSize);
        },
        [setCurrent, setSize]
    );

    // 点击历史对话，触发切换会话的函数
    const handleHistoryChatClick = useCallback(
        (sessionUuid: string) => {
            const logData = {
                category: 'switchChatClick',
                content: sessionUuid,
            };
            messageHandler.send(EventMessage.UploadUserActionLog, logData);
            messageHandler.send(EventMessage.SwitchChatSessionEvent, sessionUuid);
            foucChatTabWhenFocus();
            setHistoryVisible(false);
        },
        [foucChatTabWhenFocus, setHistoryVisible]
    );
    const {theme} = useChatConfig();

    const handleFocus = useCallback(
        () => {
            setIsFocused(true);
        },
        [setIsFocused]
    );

    const handleBlur = useCallback(
        () => {
            setIsFocused(false);
        },
        [setIsFocused]
    );

    const handleChange = useCallback(
        (e: any) => {
            setInputValue(e.target.value);
        },
        [setInputValue]
    );

    const handleClear = useCallback(
        () => {
            setInputValue('');
        },
        [setInputValue]
    );

    return (
        <div className="flex flex-col items-start w-full">
            <div className="flex items-center px-4 justify-between w-full h-[24px] mt-3">
                <p className="text-[14px] font-semibold" aria-hidden="true">历史会话</p>
                <span
                    className={'w-5 h-5 inline-block cursor-pointer hover:bg-[var(--comate-icon-hoverBackground)] p-0.5 rounded'}
                    dangerouslySetInnerHTML={{__html: closeIcon}}
                    aria-label="关闭历史会话面板"
                    role="button"
                    onClick={() => {
                        setHistoryVisible(false);
                    }}
                />
            </div>
            <div className="w-full px-4 mt-2 relative">
                <div
                    className={`flex items-center border h-8 bg-[var(--comate-editor-background)] rounded overflow-hidden w-full relative
                    ${isFocused ? 'border-[var(--comate-link-color)]' : 'border-[var(--comate-panel-border)]'}`}
                >
                    <span
                        className="w-[14px] h-[14px] inline-block mx-2"
                        // bca-disable-line
                        aria-hidden="true"
                        dangerouslySetInnerHTML={{__html: searchIcon}}
                    />
                    <input
                        placeholder="输入会话名称关键词搜索"
                        type="text"
                        value={inputValue}
                        style={{outline: 'none'}}
                        className="flex-grow border-none bg-transparent placeholder:opacity-50 placeholder:text-[var(--comate-editor-foreground)]"
                        onFocus={handleFocus}
                        onBlur={handleBlur}
                        onChange={handleChange}
                        aria-label="输入会话名称关键词搜索"
                        role="search"
                    />
                    {inputValue && (
                        <span
                            className="w-[14px] h-[14px] inline-block mx-2 cursor-pointer hover:bg-[var(--comate-icon-hoverBackground)] rounded"
                            dangerouslySetInnerHTML={{__html: clearIcon}}
                            onClick={handleClear}
                            aria-label="清空搜索关键词"
                            role="button"
                        />
                    )}
                </div>
            </div>
            <div className="w-full px-4 mt-2" aria-hidden="true">
                <DatePicker dateRange={dateRange} setDateRange={setDateRange} />
            </div>
            {filteredHistoryChat.length > 0
                ? (
                    <>
                        {currentPageHistoryChat.map((session, index) => (
                            <HistoryItem
                                key={session.sessionUuid}
                                sessionUuid={session.sessionUuid}
                                title={session.title}
                                isLastMessageOfTheDay={index === currentPageHistoryChat.length - 1
                                    || getRelativeDateCategory(currentPageHistoryChat[index + 1].utime) !== getRelativeDateCategory(session.utime)
                                    || (index + 1) % size === 0 ? 'message-content' : ''}
                                isFirstMessageOfTheDay={index === 0 || getRelativeDateCategory(currentPageHistoryChat[index - 1].utime) !== getRelativeDateCategory(session.utime)}
                                relativeDateCategory={getRelativeDateCategory(session.utime)}
                                messages={messages}
                                inputValue={inputValue}
                                theme={theme}
                                onItemClick={handleHistoryChatClick}
                                onDeleteClick={handleDeleteChat}
                            />
                        ))}
                    </>
                )
                : (
                    <div className="w-full px-4 pb-2 flex flex-col justify-center items-center mt-[300px]">
                        <img
                            style={{width: 154.4, height: 94.75, marginBottom: 16}}
                            src={historyEmptyBox}
                            alt={inputValue || (dateRange[0] && dateRange[1]) ? '暂未搜索到相关会话' : '暂无历史记录'}
                            aria-hidden="true"
                        />
                        <p className="text-[14px] text-center text-[#666666] font-regular pt-2 pb-1">
                            {inputValue || (dateRange[0] && dateRange[1]) ? '暂未搜索到相关会话' : '暂无历史记录'}
                        </p>
                    </div>
                )}
            {filteredHistoryChat.length > 0 && (
                <div className="w-full flex justify-end mr-4 my-3 items-center">
                    <Pagination
                        total={filteredHistoryChat.length}
                        current={current}
                        pageSize={size}
                        itemRender={PrevNextArrow}
                        onChange={PaginationChange}
                    />
                </div>
            )}
        </div>
    );
}

export default HistoryChat;
