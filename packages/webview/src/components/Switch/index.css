.comate-switch.rc-switch {
    --switch-height: 14px;
    --switch-width: 28px;
    --switch-label-size: 11px;
    width: var(--switch-width);
    height: var(--switch-height);
    border: none;
    box-shadow: none !important;
    box-sizing: content-box;
    background-color: rgb(169, 174, 184, .25);
}

.comate-switch.rc-switch::after,
.comate-switch.rc-switch-checked::after {
    top: 2px;
    width: calc(var(--switch-height) - 4px);
    height: calc(var(--switch-height) - 4px);
}

.comate-switch.rc-switch-checked {
    background-color: rgb(3, 202, 238, .25) !important;
}

.comate-switch::after {
    background: #B4B5B8;
}

.comate-switch.rc-switch-checked::after {
    left: calc(var(--switch-width) - var(--switch-height) - 2px + 3px);
    background: linear-gradient(-135deg, #03F8E7 0%, #6391F9 100%);
}

.rc-switch-inner-checked {
    font-size: var(--switch-label-size);
    left: 4px !important;
    top: calc((var(--switch-height) - 11px) / 2);
    line-height: 1;
}
