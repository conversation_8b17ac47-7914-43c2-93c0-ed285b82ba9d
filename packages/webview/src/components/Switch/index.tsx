import {CSSProperties, FC, HTMLAttributes, ReactNode} from 'preact/compat';
import RcSwitch from 'rc-switch';
import {SwitchChangeEventHandler, SwitchClickEventHandler} from 'rc-switch/lib';
import cx from 'classnames';
import 'rc-switch/assets/index.css';
import './index.css';

export interface Props extends Omit<HTMLAttributes<HTMLButtonElement>, 'onChange' | 'onClick'> {
    className?: string;
    prefixCls?: string;
    disabled?: boolean;
    checkedChildren?: ReactNode;
    unCheckedChildren?: ReactNode;
    onChange?: SwitchChangeEventHandler;
    onKeyDown?: HTMLAttributes<HTMLElement>['onKeyDown'];
    onClick?: SwitchClickEventHandler;
    tabIndex?: number;
    checked?: boolean;
    defaultChecked?: boolean;
    loadingIcon?: ReactNode;
    style?: CSSProperties;
    title?: string;
}

const Switch = RcSwitch as FC<Props>;
export default ({className, ...props}: Props) => {
    return <Switch {...props} className={cx('comate-switch', className)} />;
};
