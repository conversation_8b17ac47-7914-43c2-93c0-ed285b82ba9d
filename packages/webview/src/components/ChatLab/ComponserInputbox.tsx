/* eslint-disable max-lines */
/* eslint-disable complexity, max-len, max-statements */
/* bca-disable */
import {useCallback, useRef, useState, useMemo, useImperativeHandle, useEffect} from 'preact/hooks';
import {
    EventMessage,
    KnowledgeList,
    BuiltinAgent,
    AutoWorkKnowledgeType,
} from '@shared/protocols';
import {useTranslation} from 'react-i18next';
import {debounce} from 'lodash';
import {SlashType} from '@shared/constants';
import {forwardRef} from 'preact/compat';
import cx from 'classnames';
import {messageHandler} from '@/utils/messageHandler';
import flightIcon from '@/assets/flight.svg';
import {isJetbrains} from '@/config';
import {useEditorWatcher} from '@/hooks/useEditorWatcher';
import {useInputBoxSizeToggle} from '@/hooks/useInputBoxSizeToggle';
import ExtensionPopover from '../ExtensionPopover';
import {ExtensionPopoverConfig} from '../ExtensionPopover/types';
import Mentions, {hashDisplayTransformer} from '../Mentions';
import {apiGetKnowledgeList} from '../AutoWork/utils/api';
import {
    CURRENT_FILE_ITEM,
    FILE_KNOWLEDGE_ITEM,
    FOLDER_KNOWLEDGE_ITEM,
    TERMINAL_ITEM,
} from '../InputBox/constants';
import {useChatInput} from '../InputBox/hooks/useChatInput';
import {useChatResources} from '../InputBox/hooks/useChatResources';
import {computeFuzzySearch} from '../InputBox/utils/search';
import {appendIconProp, appendKnowledgeIconProp} from '../InputBox/KnowledgeIcon';
import {
    adjustTextAreaCursor,
    insertMentionTag,
    keyCodeTriggerAdjust,
    parseMarkupedText2PlainText,
    getTextAfterLastSymbol,
    getCorrectSelectionStart,
} from '../InputBox/utils/mention';
import {
    isEnterKeyPressed,
    isMainKeyPressed,
    isShiftKeyPressed,
} from '../InputBox/utils/is';
import ToolbarButton from '../ToolbarButton';
import '../InputBox/index.css';
import '../InputBox/ExtendedArea.css';
import {ExtensionPopoverType} from '../InputBox/utils/popover';
import InputContexts from '../InputBox/InputContexts';
import InputBoxSizeToggleButton from '../InputBox/InputBoxSizeToggleButton';
import {serializeAllKnowledges} from '../InputBox/utils';

interface Props {
    // rawMessage 是原始带mention的文本
    onSubmit: (
        text: string,
        agent?: string,
        slash?: string,
        knowledgeList?: KnowledgeList[],
        rawMessage?: string
    ) => void;
    disabled?: boolean;
}

const getCharBeforeCursor = (textArea: HTMLTextAreaElement) => {
    const selectionStart = textArea.selectionStart;
    return textArea.value[selectionStart - 1];
};

export default forwardRef<{focus: () => any}, Props>(function InputBox({onSubmit, disabled}, inputRef) {
    const {
        text: value,
        activePopoverType,
        knowledges: knowledgeList,
        knowledgeTypes,
        selectedKnowledgeType,
        onKnowledgeTypeSelect,
        onInputChange,
        onMentionInputChange,
        onPopoverTypeChange,
    } = useChatInput();
    const {setFuzzySearch} = useChatResources();

    const [recentFiles, setRecentFiles] = useState<string[]>([]);
    const [searchedKnowledgeItems, setSearchedKnowledgeItems] = useState<KnowledgeList[]>([]);
    const ref = useRef<HTMLTextAreaElement>(null);
    const valueRef = useRef<string>(value);
    valueRef.current = value;

    const {t} = useTranslation();

    const chatInputRef = useRef<HTMLDivElement>(null);

    const knowledgeAndFileList = useMemo(
        () => {
            const onlyUseKnowledge = selectedKnowledgeType === AutoWorkKnowledgeType.KNOWLEDGE;
            const useKeywordSearch = value.split('#')[1] && value.split('#')[1].length !== 0;
            const formatRecentFiles = recentFiles.map(item => ({
                id: item,
                name: item.split('/').pop()!,
                description: item,
            }));
            if (
                selectedKnowledgeType === AutoWorkKnowledgeType.FILE
                || selectedKnowledgeType === AutoWorkKnowledgeType.FOLDER
            ) {
                return formatRecentFiles;
            }

            if (onlyUseKnowledge && !useKeywordSearch) {
                return knowledgeList;
            }

            if (useKeywordSearch) {
                if (onlyUseKnowledge) {
                    return searchedKnowledgeItems;
                }
                return [...formatRecentFiles, ...searchedKnowledgeItems];
            }
            return formatRecentFiles;
        },
        [knowledgeList, recentFiles, selectedKnowledgeType, value, searchedKnowledgeItems]
    );

    const knowledgeTypesByWebviewConsumer = useMemo(
        () => {
            const items = [
                CURRENT_FILE_ITEM,
                FOLDER_KNOWLEDGE_ITEM,
                FILE_KNOWLEDGE_ITEM,
            ];
            if (!isJetbrains) {
                items.push(TERMINAL_ITEM);
            }

            const supported = [...items.map(item => item.id)];
            return knowledgeTypes.filter(({id}) => supported.includes(id)).map(appendIconProp);
        },
        [knowledgeTypes]
    );
    const resetFocusStateAfterMentionChange = useCallback<typeof onMentionInputChange>(
        (...args) => {
            // 为了能够校准mention的内部的光标状态
            onMentionInputChange(...args);
            ref.current!.blur();
            ref.current!.focus();
        },
        [onMentionInputChange]
    );

    const onSelectType = useCallback(
        async (type: string) => {
            if (
                type === AutoWorkKnowledgeType.TERMINAL
                || type === AutoWorkKnowledgeType.CURRENT_FILE
            ) {
                const {displayName = '', displayNameKey} = knowledgeTypes.find(item => item.id === type) ?? {};
                const label = displayNameKey ? t(displayNameKey) : displayName;
                resetFocusStateAfterMentionChange(insertMentionTag({display: label, id: type}));
            }
            else {
                // 这里还有选择文件、知识集、添加知识
                const recentFiles = await messageHandler.send(EventMessage.FilesFetchEvent, {type});
                onKnowledgeTypeSelect(({text}) => ({
                    // 如果是通过外部菜单点击的，要补充个#进来
                    text: text.endsWith('#') ? text : text + '#',
                    type,
                }));

                setRecentFiles(recentFiles);
                ref.current!.focus();
                onPopoverTypeChange('knowledge');
            }
        },
        [knowledgeTypes, onKnowledgeTypeSelect, onPopoverTypeChange, resetFocusStateAfterMentionChange, t]
    );

    const onSelectKnowledge = useCallback(
        (knowledgeId: string) => {
            const knowledgeInfo = knowledgeList.find(item => item.id === knowledgeId);
            let label = knowledgeInfo?.name || knowledgeId.split('/').pop()!;
            function combinedKnowledgeMentionId(knowledge: KnowledgeList) {
                const {type, retrievalType = 'TEXT', id} = knowledge;
                return `knowledge:${type}/${retrievalType}/${id}`;
            }
            // 不懂 knowledgeList 和 searchedKnowledgeItems 这两个的区别
            const knowledgeSearchedByKeyword = searchedKnowledgeItems.find(item => item.id === knowledgeId);
            if (knowledgeSearchedByKeyword) {
                label = knowledgeSearchedByKeyword.name;
                // eslint-disable-next-line no-param-reassign
                knowledgeId = combinedKnowledgeMentionId(knowledgeSearchedByKeyword);
            }

            if (knowledgeInfo) {
                onInputChange(insertMentionTag({display: label, id: combinedKnowledgeMentionId(knowledgeInfo)}));
            }
            else if (selectedKnowledgeType === AutoWorkKnowledgeType.FOLDER
                || selectedKnowledgeType === AutoWorkKnowledgeType.FILE
            ) {
                onInputChange(insertMentionTag({display: label, id: `${selectedKnowledgeType}:${knowledgeId}`}));
            }
            else {
                // 已知的 iapi 会走到这个分支
                onInputChange(insertMentionTag({display: label, id: knowledgeId}));
            }
            // 清空已选择的知识类型
            onKnowledgeTypeSelect(({text}) => ({text, type: undefined}));
            // 为了能够校准mention的内部的光标状态
            ref.current?.blur();
            ref.current?.focus();
        },
        [knowledgeList, searchedKnowledgeItems, selectedKnowledgeType, onKnowledgeTypeSelect, onInputChange]
    );

    const popoverConfig = useMemo(
        (): ExtensionPopoverConfig | undefined => {
            switch (activePopoverType) {
                case 'knowledge':
                    if (knowledgeAndFileList) {
                        return {
                            showFileIcon: true,
                            refHeight: 700,
                            items: knowledgeAndFileList.map(appendKnowledgeIconProp),
                            type: activePopoverType,
                            onSelect: id => {
                                return onSelectKnowledge(id);
                            },
                            searchValue: getTextAfterLastSymbol(value, '#'),
                            showPrefix: false,
                        };
                    }
                    break;
                case 'pound':
                    return {
                        items: knowledgeTypesByWebviewConsumer,
                        type: activePopoverType,
                        onSelect: id => {
                            return onSelectType(id);
                        },
                    };
                default:
                    return undefined;
            }
        },
        [
            activePopoverType,
            knowledgeAndFileList,
            knowledgeTypesByWebviewConsumer,
            value,
            onSelectKnowledge,
            onSelectType,
        ]
    );

    const [currentSelect, setCurrentSelect] = useState<string>();

    // 一坨屎
    useEffect(
        () => {
            if (popoverConfig && !currentSelect && popoverConfig.items[0]) {
                setCurrentSelect(popoverConfig.items[0].id);
            }
        },
        [currentSelect, popoverConfig]
    );

    const canSendMessage = useMemo(
        () => {
            if (popoverConfig) {
                return false;
            }
            return value.trim().length > 0;
        },
        [popoverConfig, value]
    );

    const handleSearchKnowledgeByKeyword = useMemo(
        () =>
            debounce(
                async (keyword: string) => {
                    const knowledgeList = await apiGetKnowledgeList({query: keyword});
                    setSearchedKnowledgeItems(knowledgeList);
                },
                100
            ),
        []
    );

    const handleSearchFilesByKeyword = useMemo(
        () =>
            debounce(
                async (value: string, keyword: string, type?: string) => {
                    const files = await messageHandler.send(EventMessage.FilesFetchEvent, {keyword, type});
                    // 异步获取搜索结果后，如果输入框的值已经改变，则不展示搜索结果
                    // eslint-disable-next-line max-depth
                    if (value !== valueRef.current || !files) {
                        return;
                    }
                    setRecentFiles(files);
                },
                200
            ),
        [setRecentFiles]
    );

    const handleChange = useCallback(
        async (e: {target: {value: string}}, markedValue: string) => {
            const prevChar = getCharBeforeCursor(ref.current!);
            const value = e.target.value;

            onInputChange(markedValue, {prevChar});
            setFuzzySearch(computeFuzzySearch(value, undefined, undefined));

            if (activePopoverType === 'knowledge' || value.includes('#')) {
                const chunks = value.split('#');
                const keyword = chunks[chunks.length - 1];
                if (keyword) {
                    if (selectedKnowledgeType === AutoWorkKnowledgeType.KNOWLEDGE) {
                        handleSearchKnowledgeByKeyword(keyword);
                    }
                    else {
                        handleSearchFilesByKeyword(value, keyword, selectedKnowledgeType);
                    }
                }
                else {
                    if (selectedKnowledgeType === AutoWorkKnowledgeType.IAPI) {
                        return;
                    }

                    onPopoverTypeChange('pound');
                }
            }
        },
        [
            onInputChange,
            setFuzzySearch,
            activePopoverType,
            selectedKnowledgeType,
            handleSearchKnowledgeByKeyword,
            onPopoverTypeChange,
            handleSearchFilesByKeyword,
        ]
    );
    const {fileContexts, popSelectedFile, onFileContextSelected} = useEditorWatcher();
    const [expand, , {close: minimizeInputboxSize}] = useInputBoxSizeToggle();
    const resetInputBox = useCallback(
        () => {
            onPopoverTypeChange(undefined);
            onInputChange('');
            onFileContextSelected([]);
            minimizeInputboxSize();
        },
        [onPopoverTypeChange, onInputChange, onFileContextSelected, minimizeInputboxSize]
    );
    const handleSubmit = useCallback(
        () => {
            if (disabled) {
                return;
            }

            if (canSendMessage) {
                const serializedAllKnowledges = serializeAllKnowledges(value).concat(fileContexts);

                onSubmit(
                    // 三方插件如果有 defaultUserMessage 要替换空的情况
                    parseMarkupedText2PlainText(value),
                    BuiltinAgent.Comate,
                    SlashType.COMPOSER,
                    serializedAllKnowledges,
                    value
                );
                resetInputBox();
            }
        },
        [canSendMessage, disabled, fileContexts, onSubmit, resetInputBox, value]
    );

    const handleKeyDown = useCallback(
        (event: KeyboardEvent) => {
            if (event.defaultPrevented || event.detail) {
                return;
            }
            const textarea = event.target as HTMLTextAreaElement;
            const selectionStart = textarea.selectionStart;
            const selectionEnd = textarea.selectionEnd;

            if (event.key === 'Backspace') {
                // 光标在最前面时按回退键，代表用户希望不选择任何插件或能力
                if (selectionStart === 0 && selectionStart === selectionEnd) {
                    // 假设输入框为空，退格行为表现为删除 fileContext
                    popSelectedFile();
                }
                else if (activePopoverType === 'knowledge' || activePopoverType === 'pound') {
                    const deletePreviousChar = selectionStart === selectionEnd;
                    const textWillDelete = deletePreviousChar
                        ? textarea.value[selectionStart - 1]
                        // 兼容框选删除
                        : textarea.value.substring(selectionStart, selectionEnd);
                    const start = deletePreviousChar ? selectionStart - 1 : selectionStart;
                    const end = selectionEnd;
                    const remainingText = textarea.value.slice(0, start) + textarea.value.slice(end);
                    const lastCharAfterDeletion = remainingText[remainingText.length - 1];
                    if (lastCharAfterDeletion === '#' && selectedKnowledgeType !== AutoWorkKnowledgeType.IAPI) {
                        onPopoverTypeChange('pound');
                        // 清空已选择的知识类型
                        onKnowledgeTypeSelect(({text}) => ({text, type: undefined}));
                        return;
                    }
                    if (textWillDelete.includes('#')) {
                        onPopoverTypeChange(undefined);
                    }
                }
            }

            // <S-Enter> <M-Enter> <C-Enter> 触发换行操作
            if (isEnterKeyPressed(event) && (isMainKeyPressed(event) || isShiftKeyPressed(event))) {
                if (selectionStart !== undefined && selectionEnd !== undefined && ref.current) {
                    const correctSelectionStart = getCorrectSelectionStart(value, selectionStart);
                    const newValue = value.substring(0, correctSelectionStart) + '\n'
                        + value.substring(correctSelectionStart);
                    onInputChange(newValue);
                    event.preventDefault();
                    // 修复在输入框中按下 shift + enter 后，光标位置不正确的问题，需要在下一个事件循环中设置光标位置
                    setTimeout(
                        () => {
                            textarea.setSelectionRange(selectionStart + 1, selectionStart + 1);
                        },
                        0
                    );
                }
                else {
                    onInputChange(value + '\n');
                    event.preventDefault();
                }
            }
            else if (isEnterKeyPressed(event) && !isMainKeyPressed(event)) {
                if (canSendMessage && !expand) {
                    handleSubmit();
                }
                else if (value.length > 0 && !popoverConfig) {
                    // when disabled, enter should create a new line
                    onInputChange(value + '\n');
                }
                event.preventDefault();
            }
            else if (keyCodeTriggerAdjust(event.key)) {
                adjustTextAreaCursor(textarea, event.key);
            }
        },
        [activePopoverType, popSelectedFile, selectedKnowledgeType, onPopoverTypeChange, onKnowledgeTypeSelect, value, onInputChange, canSendMessage, expand, popoverConfig, handleSubmit]
    );

    const handlePopoverVisibleChange = useCallback(
        (visible: boolean) => {
            onPopoverTypeChange(visible ? activePopoverType : undefined);
        },
        [activePopoverType, onPopoverTypeChange]
    );

    const handlePopoverTrigger = useCallback(
        (popoverType: ExtensionPopoverType) => {
            if (activePopoverType === popoverType) {
                onPopoverTypeChange(undefined);
            }
            else {
                onPopoverTypeChange(popoverType, {keepInputText: true});
            }
        },
        [activePopoverType, onPopoverTypeChange]
    );

    const popoverTrigger = useMemo(
        () => ({
            agent: () => handlePopoverTrigger('agent'),
            command: () => handlePopoverTrigger('command'),
            knowledgeType: () => handlePopoverTrigger('pound'),
        }),
        [handlePopoverTrigger]
    );

    const fixInputBoxDisplayConfusionInJetbrains = useCallback(
        () => {
            if (isJetbrains) {
                ref.current!.blur();
                ref.current!.focus();
            }
        },
        []
    );

    useImperativeHandle(inputRef, () => ({
        focus: () => ref.current?.focus(),
        selectKnowledge: (select: (k: KnowledgeList[]) => Array<{id: string, label: string}>) => {
            select(knowledgeList);
        },
    }));

    useEffect(
        () => {
            ref.current!.focus();
        },
        []
    );

    return (
        <div className="relative">
            {activePopoverType && popoverConfig && (
                <ExtensionPopover
                    onPopoverVisibleChange={handlePopoverVisibleChange}
                    currentSelect={currentSelect}
                    setCurrentSelect={setCurrentSelect}
                    {...popoverConfig}
                />
            )}
            <div
                className="relative flex flex-col max-h-[calc(100vh-268px)]"
                id="chatInputWrapper"
                ref={chatInputRef}
                style={{height: expand ? '70vh' : undefined}}
            >
                <InputContexts />
                <div className="w-full resize-none px-3">
                    <Mentions.TextArea
                        inputRef={ref}
                        placeholder="使用 # 选择文件，畅享多文件编辑"
                        value={value}
                        disablePasteRecognizeLink
                        onChange={handleChange}
                        onKeyDown={handleKeyDown}
                        onPaste={fixInputBoxDisplayConfusionInJetbrains}
                        displayTransform={hashDisplayTransformer}
                    />
                </div>
                <div className="comate-chat-extended-area flex justify-between items-center">
                    <button
                        className={cx(
                            'extended-btn',
                            'font-pingfang',
                            {active: activePopoverType === 'pound' || activePopoverType === 'knowledge'}
                        )}
                        onClick={popoverTrigger.knowledgeType}
                        aria-haspopup="listbox"
                        aria-owns="input_popover"
                        aria-autocomplete="list"
                        aria-controls="input_popover"
                    >
                        <span className="symbol">#</span>
                        <span className="text">{t('common.context.name')}</span>
                    </button>
                    <div className="inline-flex items-center">
                        <InputBoxSizeToggleButton />
                        <span
                            className="w-[1px] h-[15px] bg-[var(--comate-descriptionForeground)] mx-2 opacity-70"
                        />
                        <ToolbarButton
                            className={canSendMessage
                                ? 'text-[var(--comate-link-color)] hover:opacity-90'
                                : 'text-[var(--comate-descriptionForeground)]'}
                            icon={flightIcon}
                            onClick={handleSubmit}
                            ariaTitle="发送"
                            disabled={disabled || !canSendMessage}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
});
