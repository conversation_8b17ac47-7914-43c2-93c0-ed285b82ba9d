const STATUS_CONFIG_MAPPING = {
    PROCESSING: ['生成中', 'bg-[var(--comate-link-color)]'],
    SUCCESS: ['生成完成', 'bg-[#00CC6D ]'],
};

const mockDetail = {
    title: '智能生成详情',
    status: 'PROCESSING',
    description: '一段话告知用户下面的信息是什么，要干什么，文案需PM提供',
};

export default function CodeGenerationProcess({detail = mockDetail}: {detail?: any}) {
    const {title, status, description} = detail;
    const [statusName, statusColor] = STATUS_CONFIG_MAPPING[status];

    return (
        <div className="flex flex-col gap-6">
            <div className="flex items-center gap-2">
                <div className="text-lg">
                    {title}
                </div>
                <div className={`rounded-lg px-2 text-xs ${statusColor}`}>
                    {statusName}
                </div>
            </div>
            <div className="text-xs text-[var(--comate-descriptionForeground)]">
                {description}
            </div>
        </div>
    );
}
