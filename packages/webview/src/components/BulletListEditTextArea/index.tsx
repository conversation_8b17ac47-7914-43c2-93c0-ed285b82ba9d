/* eslint-disable max-len */
/* bca-disable */
import {useState, useCallback, useMemo} from 'preact/hooks';
import editIcon from '@/assets/edit.svg';
import addIcon from '@/assets/add.svg';

interface Props {
    label?: string;
    steps: string[];
    onChange?: (steps: string[]) => void;
}

export default function BulletListEditTextArea(props: Props) {
    const {
        label = '执行步骤',
        steps: initialSteps,
        onChange,
    } = props;
    const [isEditing, setEditing] = useState(false);
    const [steps, setSteps] = useState<string[]>(initialSteps);

    const textareaValue = useMemo(
        () => {
            return steps.map(step => '* ' + step).join('\n');
        },
        [steps]
    );

    const toggleEdit = useCallback(
        () => {
            setEditing(prevEditing => !prevEditing);
        },
        []
    );
    const addStep = useCallback(
        () => {
            setSteps(prevSteps => [...prevSteps, '']);
        },
        []
    );

    const handleCancel = useCallback(
        () => {
            toggleEdit();
            setSteps(initialSteps);
        },
        [toggleEdit, initialSteps]
    );

    const handleSubmit = useCallback(
        () => {
            toggleEdit();
            onChange && onChange(steps);
        },
        [toggleEdit, onChange, steps]
    );

    const handleInputChange = useCallback(
        (event: any) => {
            const value = event.target.value as string;
            const newSteps = value.split('\n').map(step => step.slice(2)).filter(step => step.length !== 0);
            setSteps(newSteps);
        },
        []
    );

    return (
        <div className="flex flex-col">
            <div className="flex items-center justify-between">
                {label}
                {isEditing
                    ? (
                        <div className="flex items-center">
                            <button onClick={handleSubmit} className="p-1 text-white">保存</button>
                            <button onClick={handleCancel} className="p-1 text-white">取消</button>
                        </div>
                    )
                    : (
                        <div className="flex items-center">
                            <div className="w-3 h-3 text-white" dangerouslySetInnerHTML={{__html: editIcon}}></div>
                            <button onClick={toggleEdit} className="p-1 text-white">修改</button>
                        </div>
                    )}
            </div>
            {isEditing
                ? (
                    <div className="w-full flex flex-col items-start">
                        <textarea
                            rows={4}
                            value={textareaValue}
                            onChange={handleInputChange}
                            className="w-full p-3 bg-transparent text-white border"
                        />
                        <div className="flex items-center">
                            <div className="w-3 h-3 text-white" dangerouslySetInnerHTML={{__html: addIcon}}></div>
                            <button onClick={addStep} className="p-1 bottom-3">添加一条步骤</button>
                        </div>
                    </div>
                )
                : (
                    <div className="p-2">
                        {steps.map(step => (
                            <p key={step} className="flex items-center gap-1">
                                <div className="flex flex-col justify-center w-3 h-3 m-0.25 border border-white bg-transparent rounded-full text-xs" />
                                {step}
                            </p>
                        ))}
                    </div>
                )}
        </div>
    );
}
