import {VirtualEditorMethodCall} from '@comate/plugin-shared-internals';
import {EventMessage} from '@shared/protocols';
import {useCallback, useState} from 'preact/hooks';
import {messageHandler} from '@/utils/messageHandler';

const options = [
    {action: 'getActiveDocument', params: {absolutePath: 'string'}},
    {action: 'openDocument', params: {absolutePath: 'string'}},
    {action: 'getDocument', params: {absolutePath: 'string'}},
    {action: 'closeDocument', params: {absolutePath: 'string'}},
    {action: 'openVirtualDiffDocument', params: {absolutePath: 'string', content: 'string', modified: 'string'}},
    {action: 'closeVirtualDiffDocument', params: {absolutePath: 'string'}},
    {action: 'replaceVirtualDiffModifiedDocument', params: {absolutePath: 'string', content: 'string'}},
    {action: 'getVirtualDiffDocument', params: {absolutePath: 'string'}},
    {action: 'replaceDocumentAndSave', params: {absolutePath: 'string', content: 'string'}},
    {action: 'saveDocumentWithReplaceContent', params: {absolutePath: 'string', content: 'string'}},
    {action: 'executeTerminalShell', params: {cmd: 'string', cwd: 'string'}},
    {action: 'getLatestOutputFromActiveTerminal', params: {}},
    {action: 'openUrlInEditorWebview', params: {url: 'string', title: 'string'}},
];

export default function OnlyUsedToTestEvent() {
    const [selected, setSelected] = useState(options[0].action);
    const [res, setResponse] = useState('');
    const [params, setParams] = useState<Record<string, string>>({});
    const [fromEngine, setFromEngine] = useState(true);

    const sendMessage = useCallback(
        async () => {
            if (fromEngine) {
                const res = await messageHandler.send(EventMessage.MockVirtualEditorEventFromEngine, {
                    action: selected as VirtualEditorMethodCall['action'],
                    payload: params as any,
                });
                setResponse(JSON.stringify(res, null, 2));
            }
            else {
                const res = await messageHandler.send(EventMessage.VirtualEditorEvent, {
                    action: selected as VirtualEditorMethodCall['action'],
                    payload: params as any,
                });
                setResponse(JSON.stringify(res, null, 2));
            }
        },
        [fromEngine, params, selected]
    );

    return (
        <div className="flex flex-col gap-2">
            <div className="flex gap-2">
                <input
                    type="checkbox"
                    checked={fromEngine}
                    onChange={e => setFromEngine((e.target as HTMLInputElement).checked)}
                />
                是否模拟从Engine返回消息
            </div>
            <span>事件：</span>
            <select value={selected} onChange={e => setSelected((e.target as HTMLSelectElement).value)}>
                {options.map(option => {
                    return <option key={option.action}>{option.action}</option>;
                })}
            </select>
            <span>参数：</span>
            {Object.keys(options.find(o => o.action === selected)!.params).map(
                key => (
                    <div key={key} className="flex">
                        <span>{key}：</span>
                        <textarea
                            key={key}
                            // @ts-ignore
                            onChange={e => setParams(prev => ({...prev, [key]: e.target.value}))}
                            value={params[key]}
                        />
                    </div>
                )
            )}
            <span>响应：</span>
            <pre>{res}</pre>
            <button onClick={sendMessage}>Send</button>
        </div>
    );
}
