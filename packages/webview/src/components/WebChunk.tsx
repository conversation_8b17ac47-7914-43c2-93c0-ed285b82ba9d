interface Props {
    id: number;
    title: string;
    onClick: () => void;
}

function WebChunk({id, title, onClick}: Props) {
    return (
        <div className="flex flex-col gap-1">
            <div
                className="opacity-70 cursor-pointer"
                onClick={onClick}
            >
                [{id}] {title}
            </div>
        </div>
    );
}

export default WebChunk;
