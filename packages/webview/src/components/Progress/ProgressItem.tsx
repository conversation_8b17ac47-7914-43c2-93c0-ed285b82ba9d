import {Task, TaskStatus} from '@shared/protocols';
import {memo} from 'preact/compat';
import {useTranslation} from 'react-i18next';
import loadIcon from '@/assets/load.png';
import successIcon from '@/assets/success.svg';
import tipsSuccessIcon from '@/assets/tipsSuccess.svg';
import warnIcon from '@/assets/warn.svg';
import initIcon from '@/assets/init.svg';
import {TaskIcon, Props as TaskIconProps} from './TaskIcon';
import {TaskDescription} from './TaskDescription';
import './ProgressItem.css';

export const STATUS_CONFIG_MAPPING = {
    [TaskStatus.INIT]: [initIcon, initIcon, 'text-white opacity-10'],
    [TaskStatus.PROCESSING]: [loadIcon, loadIcon, 'loading-icon opacity-100'],
    [TaskStatus.SUCCEED]: [successIcon, tipsSuccessIcon, 'opacity-100'],
    [TaskStatus.FAIL]: [warnIcon, warnIcon, 'bg-red-600'],
};

interface Props extends Task {
    size?: TaskIconProps['size'];
}

export default memo(function ProgressItem({desc, status, taskType, subTasks, size = 'middle'}: Props) {
    const {t} = useTranslation();
    const TASK_TYPE_MAPPING = {
        SEARCH: t('common.search'),
        PLAN: t('common.plan'),
        REASONING: t('common.reasoning'),
        ANALYZE: t('common.analyze'),
        THOUGHT: t('common.thought'),
        CODE_GENERATE: t('common.code.generate'),
        ANSWER: t('common.answer'),
    };

    return (
        <div>
            <div className="flex items-center text-[13px] opacity-90">
                <TaskIcon status={status} size={size} />
                <div className="opacity-70 mr-2 flex-shrink-0">{TASK_TYPE_MAPPING[taskType] || taskType}</div>
                <TaskDescription desc={desc} />
            </div>
            {subTasks && subTasks.length > 0 && (
                <div className="ml-6 w-full flex flex-col">
                    {subTasks.map((subTask, index) => (
                        <ProgressItem
                            /* eslint-disable-next-line react/no-array-index-key */
                            key={index}
                            {...subTask}
                        />
                    ))}
                </div>
            )}
        </div>
    );
});
