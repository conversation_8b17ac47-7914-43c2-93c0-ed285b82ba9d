/* bca-disable */
import {TaskStatus} from '@shared/protocols';
import loadIcon from '@/assets/load.png';
import successIcon from '@/assets/success.svg';
import warnIcon from '@/assets/warn.svg';
import initIcon from '@/assets/init.svg';

export interface Props {
    status: TaskStatus;
    size: 'small' | 'middle';
}

export const TaskIcon = ({status, size}: Props) => {
    const iconSize = size === 'small' ? 'w-3.5 h-3.5' : 'w-4 h-4';
    if (status === TaskStatus.INIT) {
        return (
            <div
                className={`opacity-10 mr-1 flex-shrink-0 ${iconSize}`}
                dangerouslySetInnerHTML={{__html: initIcon}}
            />
        );
    }

    if (status === TaskStatus.PROCESSING) {
        {/* orz 这里有坑 load和success的icon可能打包后path有覆盖，同时出现会有一个显示有问题 这里先用png了 */}
        return (
            <img
                className={`loading-icon opacity-100 mr-1 flex-shrink-0 ${iconSize}`}
                src={loadIcon}
            />
        );
    }

    if (status === TaskStatus.SUCCEED) {
        return (
            <div
                className={`${iconSize} mr-1 flex-shrink-0`}
                dangerouslySetInnerHTML={{__html: successIcon}}
            />
        );
    }

    return (
        <div
            className={`text-red-500  mr-1 flex-shrink-0 ${iconSize}`}
            dangerouslySetInnerHTML={{__html: warnIcon}}
        />
    );
};
