/* bca-disable */
import {useCallback} from 'preact/hooks';
import {EventMessage, OperateType} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import ActionButton, {ActionKeys} from './Markdown/ActionButton';

interface TagProps {
    operateType: OperateType;
}

function FileChangeTag({operateType}: TagProps) {
    switch (operateType) {
        case 'ADD':
            return (
                <span className="flex items-center min-w-fit">
                    <span className="w-1 h-1 bg-[#4B7EE5]" />
                    <span className="text-[#4B7EE5] pl-1 pr-2">
                        增加
                    </span>
                </span>
            );
        case 'CHANGE':
            return (
                <span className="flex items-center min-w-fit">
                    <span className="w-1 h-1 bg-[#F58300]" />
                    <span className="text-[#F58300] pl-1 pr-2">
                        修改
                    </span>
                </span>
            );
        case 'DELETE':
            return (
                <span className="flex items-center min-w-fit">
                    <span className="w-1 h-1 bg-[#E62C4B]" />
                    <span className="text-[#E62C4B] pl-1 pr-2">
                        删除
                    </span>
                </span>
            );
        default:
            return (
                <span className="flex items-center min-w-fit">
                    <span className="w-1 h-1 bg-gray-500" />
                    <span className="text-gray-500 pl-1 pr-2">
                        未知
                    </span>
                </span>
            );
    }
}

interface Props {
    operateType: OperateType;
    filePath: string;
    startLine?: number;
    endLine?: number;
    actions?: Record<string, (content?: string) => void>;
    content?: string;
    showActionIcon?: boolean;
}

function FileChangeItem({operateType, filePath, startLine, endLine, actions, content, showActionIcon = true}: Props) {
    const validActions = ActionKeys.filter(v => actions && actions[v]);

    const handleOpenFile = useCallback(
        () => {
            messageHandler.send(EventMessage.ShowCodeBlockEvent, {fileUri: filePath, startLine, endLine});
        },
        [endLine, filePath, startLine]
    );

    const handleActionClick = useCallback(
        (action: string) => {
            const callback = actions && actions[action];
            callback && callback(content);
        },
        [actions, content]
    );

    return (
        <div className="flex items-center w-full m-2 grow">
            <FileChangeTag operateType={operateType} />
            <span
                className="overflow-hidden overflow-ellipsis whitespace-nowrap hover:cursor-pointer"
                style={{direction: 'rtl', unicodeBidi: 'bidi-override'}}
                onClick={handleOpenFile}
            >
                {filePath.split('').reverse().join('')}
            </span>
            <span className="flex min-w-fit text-xs justify-end gap-2 flex-1 items-center ml-4">
                {validActions.map((action, index) => (
                    <>
                        {index !== 0
                            && <div className="w-[1px] h-[12px] bg-[var(--vscode-textSeparator-foreground)]" />}
                        <ActionButton action={action} showIcon={showActionIcon} onClick={handleActionClick} />
                    </>
                ))}
            </span>
        </div>
    );
}

export default FileChangeItem;
