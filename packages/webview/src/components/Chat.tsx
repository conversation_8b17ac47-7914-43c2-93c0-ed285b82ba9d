/* bca-disable */
/* eslint-disable max-len, max-lines, complexity */
import {useCallback, useEffect, useMemo, useRef, useState} from 'preact/hooks';
import {
    ColorTheme,
    DehydratedMessage,
    EventMessage,
    Feature,
    KnowledgeList,
    LicenseValidity,
    InputBoxMessageHistory,
    ToastMessage,
    WebviewAgentPayload,
    WebviewAgentConversationType,
} from '@shared/protocols';
import {useTranslation} from 'react-i18next';
import {memo} from 'preact/compat';
import {messageHandler} from '@/utils/messageHandler';
import JetBrainsChatHeader from '@/jetbrains/ChatHeader';
import {compose} from '@/utils/compose';
import {setDisplayLanguage} from '@/i18n/config';
import {
    MESSAGE_COPY_SUCCESS,
    MODAL_CLEAR_TITLE,
    MODAL__CLEAR_DESCRIPTION,
} from '@/i18n/constants';
import '@/i18n/init';
import {getHelpResponse} from '@/utils/getHelpResponse';
import {isInternal} from '@/utils/features';
import {I18nLangaugeProvider, useI18nLanguage} from '@/hooks/useLanguage';
import {ExtensionConfigProvider, useExtensionConfig} from '@/hooks/useExtensionConfig';
import {AccessToProvider, useAccessTo} from '@/hooks/useAccessTo';
import {ComposerMessageProvider} from '@/hooks/useComposerMessage';
import {ToastProvider, useToast} from '@/hooks/useToast';
import {TestEventProvider} from '@/hooks/useTestEvent';
import {ChatTabProvider, useChatTabContext} from '@/hooks/useChatTab';
import LogProvider from '@/hooks/useLog';
import {SmartApplyProvider} from '@/hooks/useSmartApplyStatus';
import {EditorWatcherProvider} from '@/hooks/useEditorWatcher';
import {InputBoxSizeToggleProvider} from '@/hooks/useInputBoxSizeToggle';
import {isJetbrains} from '@/config';
import warnIcon from '../assets/warning.svg';
import InputBox from './InputBox';
import ChatBox, {formatOrder} from './ChatBox';
import ChatConfigProvider, {MessageFeedback, WebviewConsumer} from './ChatConfigProvider';
import Welcome from './Welcome';
import Modal from './Modal';
import ChatTab, {ChatTabKey} from './Tabs/ChatTab';
import NotBound from './InputBox/NotBound';
import Suggestions from './ChatSuggestion';
import SuggestionProvider from './SuggestionProvider';
import {ChatInputProvider} from './InputBox/hooks/useChatInput';
import {InputModalProvider} from './Modal/InputModal';
import Message from './Message';
import UserGuide from './UserGuide';
import {BannerState} from './Banner';
import {useSetLastQuery, useLastQuery} from './LastQueryProvider';
import RecommendQuestion from './RecommendQuestion/RecommendQuestion';
import {ariaHelpers} from './Aria';
import HistoryChat from './HistoryChat';
import MoreOperations from './MoreOperations';
import SmartAgent from './SmartAgent';
import {AgentProvider, useAgentContext} from './SmartAgent/AgentProvider';
import SQLSchemaForm from './SQLSchemaForm';
import './Chat.css';
import KernelDemo from './KernelDemo';

interface UserName {
    configUsername?: string;
    chineseName?: string;
}

interface WelcomeRef {
    focusLoginButton: () => void;
}

enum PaddleKeyword {
    DEEP_LEARNING = '深度学习',
    PADDLE_CN = '飞桨',
    PADDLE_EN = 'paddle',
    PADDLE_EN_CAP = 'Paddle',
}

const clearMessages = () => {
    messageHandler.send(EventMessage.ClearSessionEvent);
    messageHandler.send(EventMessage.ComatePairClearHistory);
};

// TODO: 这些逻辑可以交给 provider 去处理
const groupMessagesByReplyId = (messages: DehydratedMessage[]) => {
    const questions = messages.filter(item => item.role === 'user');
    const answers = new Map<number, DehydratedMessage[]>();
    for (const msg of messages) {
        if (msg.replyTo === undefined) {
            continue;
        }
        if (!answers.has(msg.replyTo)) {
            answers.set(msg.replyTo, []);
        }
        answers.get(msg.replyTo)?.push(msg);
    }

    const result: DehydratedMessage[][] = [];
    for (const question of questions) {
        result.push([question]);
        const replies = answers.get(question.id);
        replies && result.push(replies);
    }
    return {messageGroups: result, questionCount: result.length};
};

const MAX_DISPLAY_COUNT = 16;
const COLLAPSE_THRESHOLD = 30;

// eslint-disable-next-line max-statements
function Chat() {
    const rootContainerRef = useRef<HTMLDivElement>(null);
    const messageContainerRef = useRef<HTMLDivElement>(null);
    const mainRef = useRef<HTMLDivElement>(null);
    const initStaleRef = useRef(false);
    const inputRef = useRef<HTMLTextAreaElement>(null);
    const messageBottomRef = useRef<HTMLDivElement>(null);
    const welcomeRef = useRef<WelcomeRef>(null);

    const [messageOrder, setMessageOrder] = useState<Record<number, number>>({});
    const [theme, setTheme] = useState<ColorTheme | undefined>();
    const [username, setUsername] = useState<string>('');
    const [chineseName, setChineseName] = useState<string | undefined>();
    const [messages, setMessages] = useState<DehydratedMessage[]>([]);
    const [refHeight, setRefHeight] = useState<number | undefined>(undefined);
    const [bannerState, setBannerState] = useState<BannerState | undefined>();
    const [clearModalVisible, setClearModalVisible] = useState<boolean>(false);
    const [moreOperationsVisible, setMoreOperationsVisible] = useState<boolean>(false);
    const [historyVisible, setHistoryVisible] = useState<boolean>(false);
    const [SQLVisible, setSQLVisible] = useState<boolean>(false);
    const overlayVisible = historyVisible || SQLVisible;
    const [isLatest, setIsLatest] = useState<boolean>(false);
    // eslint-disable-next-line max-len
    const [modalConfig, setModalConfig] = useState<
        {visible: boolean, title: string, description: string, modalKey: string}
    >();
    const [engineInitialized, setEngineInitialized] = useState(false);
    const [messageFeedback, setMessageFeedback] = useState<Record<number, MessageFeedback>>({});
    const [licenseValidity, setLicenseValidity] = useState<LicenseValidity | undefined>({isActive: false});
    const [avatarUrl, setAvatarUrl] = useState<string>('');
    const [isLogin, setIsLogin] = useState(true);
    const [messageVisible, setMessageVisible] = useState(false);
    const [inputBoxMessageHistory, setInputBoxMessageHistory] = useState<InputBoxMessageHistory[]>([]);
    const lastSuccessMessageRef = useRef<DehydratedMessage | undefined>();

    const setLastQuery = useSetLastQuery();
    const lastQuery = useLastQuery();

    const {t} = useTranslation();
    const {foregroundConversation, createConversation} = useAgentContext();
    const {activeTabKey, setActiveTabKey} = useChatTabContext();

    const webviewConsumer = $features.WEBVIEW_CONSUMER as WebviewConsumer;

    // 准备下线助理模式
    const enableComatePair = false;
    const onlyErrorResponse = useAccessTo(Feature.OnlyErrorResponseInChat);
    const enableUserGuideByLicense = useAccessTo(Feature.EnableUserGuide);

    const isLastMessageInProgress = useMemo(
        () => {
            const last = messages[messages.length - 1];
            return last?.status === 'inProgress';
        },
        [messages]
    );

    const {messageGroups, questionCount} = useMemo(
        () => groupMessagesByReplyId(messages),
        [messages]
    );
    const [displayMessages, setDisplayMessages] = useState<DehydratedMessage[][]>([]);

    useEffect(
        () => {
            if (questionCount > COLLAPSE_THRESHOLD) {
                setDisplayMessages(messageGroups.slice(-MAX_DISPLAY_COUNT));
            }
            else {
                setDisplayMessages(messageGroups);
            }
        },
        [messageGroups, questionCount]
    );

    const handleScroll = useCallback(
        () => {
            const messageContainer = mainRef.current;
            if (messageContainer) {
                const scrollTop = messageContainer.scrollTop;
                if (scrollTop === 0) {
                    const scrollHeightBefore = messageContainer.scrollHeight;

                    setDisplayMessages(
                        currentDisplayMessages => {
                            const currentLength = currentDisplayMessages.length;
                            const additionalLength = questionCount - currentLength - MAX_DISPLAY_COUNT;
                            if (additionalLength < 0) {
                                // 如果计算后的索引为负，则显示所有消息
                                return messageGroups.slice(-questionCount);
                            }
                            else {
                                // 否则，增加显示的消息数量
                                return messageGroups.slice(-currentLength - MAX_DISPLAY_COUNT);
                            }
                        }
                    );

                    requestAnimationFrame(
                        () => {
                            const scrollHeightAfter = messageContainer.scrollHeight;
                            const heightDifference = scrollHeightAfter - scrollHeightBefore;
                            messageContainer.scrollTop = heightDifference;
                        }
                    );
                }
            }
        },
        [messageGroups, questionCount]
    );

    useEffect(
        () => {
            const messageContainer = mainRef.current;
            if (messageContainer) {
                messageContainer.addEventListener('scroll', handleScroll);
                return () => {
                    messageContainer.removeEventListener('scroll', handleScroll);
                };
            }
        },
        [handleScroll]
    );

    const hasMessageInProgress = useMemo(
        () => {
            return onlyErrorResponse ? false : messages.some(item => item.status === 'inProgress');
        },
        [messages, onlyErrorResponse]
    );

    const closeClearModal = useCallback(
        () => {
            setClearModalVisible(false);
        },
        []
    );

    const showModel = useCallback(
        () => {
            setClearModalVisible(true);
        },
        []
    );

    const showSQL = useCallback(
        () => {
            setSQLVisible(true);
            setHistoryVisible(false);
        },
        []
    );

    const setOrder = useCallback(
        (id: number, order: number) => setMessageOrder(value => ({...value, [id]: order})),
        []
    );

    const setFeedback = useCallback(
        (id: number, feedback: MessageFeedback) => setMessageFeedback(value => ({...value, [id]: feedback})),
        []
    );

    const language = useI18nLanguage();

    const showRelatedToAt = useMemo(
        () => {
            // saas 版本验证license
            return ($features.PLATFORM === 'internal'
                || (licenseValidity?.isActive && licenseValidity?.keyType !== 'NONE'))
                // feature.jetbrains 是 jetbrains ide 传过来的
                // eslint-disable-next-line max-len
                && (engineInitialized || enableComatePair);
        },
        [licenseValidity, engineInitialized, enableComatePair]
    );

    const onRefreshInputHistory = useCallback(
        (history: InputBoxMessageHistory[]) => {
            messageHandler.send(EventMessage.InputHistoryRefreshEvent, history ?? []);
            setInputBoxMessageHistory(history);
        },
        []
    );

    const {config: {enablePrivateService, enableUserGuide, enableAgent, enableSecurity, isPoc}} = useExtensionConfig();

    const mockSubmit = useCallback(
        (mockMessage: DehydratedMessage[]) => {
            setMessages(
                messages => [
                    ...messages,
                    ...mockMessage,
                ]
            );
            // HACK: 默认 setMessages 后，不会更新右上角的清除按钮。
            // 这里使用了和 Comate Help 相同的实现方式，因而提醒后来人注意，HelpSendEvent 实际被用于 UpdateMessageLength
            messageHandler.send(EventMessage.HelpSendEvent);
        },
        []
    );
    const sendQuery = ariaHelpers.sendChatMessage;
    const sendResponse = ariaHelpers.send;
    const handleSubmit = useCallback(
        (
            value: string,
            agent?: string,
            slash?: string,
            knowledgeList?: KnowledgeList[],
            rawMessage?: string,
            extraData?: any
        ) => {
            const message = {
                prompt: value,
                messageOrder,
                agent: !agent && Object.values(PaddleKeyword).some(e => value.includes(e)) ? 'paddle' : agent,
                slash,
                knowledgeList,
                chatIntentRecognition: true,
                supportAt: !!showRelatedToAt,
            };
            // paddle关键词拦截 poc等poc的开放平台上线之后再去掉
            if (!enablePrivateService && !agent && Object.values(PaddleKeyword).some(e => value.includes(e))) {
                messageHandler.send(EventMessage.QuerySendEvent, {
                    prompt: value,
                    messageOrder,
                    agent: 'paddle',
                    slash,
                    knowledgeList,
                    chatIntentRecognition: true,
                    supportAt: !!showRelatedToAt,
                });
                setLastQuery(message);
                sendQuery(message);

                return;
            }

            if (agent === 'Comate' && slash === 'help') {
                setMessages(
                    messages => [
                        ...messages,
                        {
                            role: 'user',
                            content: t('suggestion.help.text1'),
                            type: 'help',
                            status: 'success',
                            stream: false,
                            // 这里设置里一个负的值，不影响正常的逻辑
                            id: -1000,
                            timestamp: Date.now() - 1,
                            actions: [],
                        },
                        {
                            role: 'assistant',
                            content: getHelpResponse({
                                supportAt: !!showRelatedToAt,
                                language: language,
                                enableSecurity,
                                isPoc,
                            }),
                            status: 'success',
                            type: 'help',
                            stream: false,
                            replyTo: -1000,
                            id: -999,
                            timestamp: Date.now(),
                            actions: [],
                        },
                    ]
                );
                messageHandler.send(EventMessage.HelpSendEvent);
                return;
            }

            // 插件内置能力，是否与一方插件合并在一起待确定，目前先把这个功能给注释掉了
            if (slash === 'clear') {
                // 清空对话框
                messageHandler.send(EventMessage.ClearSessionEvent);
            }
            else {
                messageHandler.send(EventMessage.QuerySendEvent, {
                    prompt: value,
                    messageOrder,
                    agent,
                    slash,
                    knowledgeList,
                    chatIntentRecognition: true,
                    supportAt: !!showRelatedToAt,
                    extraData,
                });
                setLastQuery(message);
                sendQuery(message);
            }

            // 将历史发送的消息存储，用户可以在输入框为空时利用上下键进行历史消息的回显
            onRefreshInputHistory([{value, agent, slash, rawMessage}, ...inputBoxMessageHistory].slice(0, 50));
        },
        [
            enablePrivateService,
            inputBoxMessageHistory,
            language,
            messageOrder,
            onRefreshInputHistory,
            sendQuery,
            setLastQuery,
            showRelatedToAt,
            t,
            enableSecurity,
            isPoc,
        ]
    );

    const updateBannerVersion = useCallback(
        (bannerVersion: number) => {
            setBannerState(pre => (pre ? {...pre, bannerVersion} : pre));
        },
        []
    );

    const handleColorThemeChange = useCallback(
        (theme: ColorTheme | undefined) => {
            if (theme) {
                if (isJetbrains || webviewConsumer === 'xcode') {
                    document.body.classList.remove('light');
                    document.body.classList.remove('dark');
                    document.body.classList.add(theme);
                }
                setTheme(theme);
            }
        },
        [webviewConsumer]
    );
    // 证书有效，但是没有绑定到passport或者iam，不能用开放平台
    const isLicenseNotBound = useMemo(
        () => {
            return $features.PLATFORM === 'saas' && licenseValidity?.keyType === 'NONE';
        },
        [licenseValidity]
    );

    // 提问的时候，滚动到底部
    useEffect(
        () => {
            if (rootContainerRef.current) {
                messageBottomRef.current?.scrollIntoView({
                    behavior: 'auto',
                    block: 'start',
                    inline: 'nearest',
                });
            }
        },
        [questionCount]
    );

    // 处理消息从没有滚动栏到有滚动栏的情况
    // https://github.com/sourcegraph/cody/blob/main/lib/ui/src/chat/Transcript.tsx#L91
    useEffect(
        () => {
            if (!rootContainerRef.current || !messageBottomRef.current) {
                return undefined;
            }
            let wasIntersecting = true;
            const observer = new IntersectionObserver(
                entries => {
                    for (const entry of entries) {
                        if (entry.rootBounds?.width === 0 || entries[0].rootBounds?.height === 0) {
                            continue;
                        }
                        if (wasIntersecting && !entry.isIntersecting) {
                            messageBottomRef.current?.scrollIntoView({
                                behavior: 'auto',
                                block: 'start',
                                inline: 'nearest',
                            });
                        }
                        wasIntersecting = entry.isIntersecting;
                    }
                },
                {
                    root: rootContainerRef.current,
                    threshold: 1,
                }
            );
            observer.observe(messageBottomRef.current);
            return () => {
                observer.disconnect();
            };
        },
        [rootContainerRef, messageBottomRef]
    );

    const foucChatTabWhenFocus = useCallback(
        () => setActiveTabKey(ChatTabKey.CHAT),
        [setActiveTabKey]
    );

    const handleActiveTabKeyChange = useCallback(
        (key: string) => {
            setActiveTabKey(key as ChatTabKey);
        },
        [setActiveTabKey]
    );

    const handleModalSubmit = useCallback(
        (key?: string) => {
            messageHandler.send(EventMessage.ModalSubmitEvent, key);
            if (modalConfig && key === modalConfig.modalKey) {
                setModalConfig({
                    ...modalConfig,
                    visible: false,
                });
            }
        },
        [modalConfig]
    );

    const closeModal = useCallback(
        (key?: string) => {
            if (modalConfig && key === modalConfig.modalKey) {
                setModalConfig({
                    ...modalConfig,
                    visible: false,
                });
            }
        },
        [modalConfig]
    );

    // 没看懂，这个为什么是messages[0]，从历史代码里复制过来的
    const replyTo = messages[0]?.replyTo;
    const order = formatOrder(messages, typeof replyTo === 'number' ? messageOrder[replyTo] : undefined);
    const currentMessageId = messages[order]?.id;
    const showStopGenerateBtn = messages.length > 0 && hasMessageInProgress;
    const inputBox = useMemo(
        () => {
            return isLicenseNotBound
                ? (
                    <NotBound
                        ref={inputRef}
                        submitDisabled={isLastMessageInProgress}
                        onSubmit={handleSubmit}
                    />
                )
                : (
                    <InputBox
                        showStopGenerateBtn={showStopGenerateBtn}
                        ref={inputRef}
                        refHeight={refHeight}
                        messages={messages}
                        submitDisabled={isLastMessageInProgress}
                        onFocus={foucChatTabWhenFocus}
                        onSubmit={handleSubmit}
                        mockSubmit={mockSubmit}
                        username={username}
                    />
                );
        },
        [
            isLicenseNotBound,
            isLastMessageInProgress,
            handleSubmit,
            showStopGenerateBtn,
            refHeight,
            messages,
            foucChatTabWhenFocus,
            mockSubmit,
            username,
        ]
    );

    const createNewChat = useCallback(
        (messagesLength: number) => {
            setHistoryVisible(false);
            setSQLVisible(false);
            setMoreOperationsVisible(false);
            if (activeTabKey === ChatTabKey.AGENT && enableAgent) {
                createConversation({
                    conversationId: '',
                    messageType: 'add-conversation',
                    conversationType: WebviewAgentConversationType.E2EBotConversation,
                });
            }
            else {
                foucChatTabWhenFocus();
                if (messagesLength === 0) {
                    setIsLatest(true);
                    setTimeout(() => {
                        setIsLatest(false);
                    }, 1000);
                }
                else {
                    messageHandler.send(EventMessage.SwitchChatSessionEvent);
                }
            }
        },
        [activeTabKey, enableAgent, createConversation, foucChatTabWhenFocus]
    );

    const handleHistoryChat = useCallback(
        () => {
            setHistoryVisible(true);
            setSQLVisible(false);
            setMoreOperationsVisible(false);
        },
        []
    );

    const lastSendQueryRef = useRef<DehydratedMessage | undefined>();

    // 监听自定义事件来切换 MoreOperations 组件的可见性
    useEffect(
        () => {
            moreOperationsVisible && document.addEventListener('click', () => setMoreOperationsVisible(false));
        },
        [moreOperationsVisible]
    );

    useEffect(
        () => {
            messageHandler.listen(EventMessage.ColorThemeChangeEvent, (theme: ColorTheme) => {
                handleColorThemeChange(theme);
            });

            messageHandler.listen(EventMessage.UsernameChangeEvent, ({configUsername, chineseName}: UserName) => {
                setUsername(configUsername ?? '');
                setChineseName(chineseName);
            });

            messageHandler.listen(EventMessage.MessagesRefreshEvent, (latestMessages: DehydratedMessage[]) => {
                // console.log(EventMessage.MessagesRefreshEvent);

                const lastMessage = latestMessages[latestMessages.length - 1];
                const lastQuery = latestMessages[latestMessages.length - 2];

                if (
                    lastMessage?.status === 'success' && lastMessage?.role === 'assistant'
                    && lastSuccessMessageRef.current?.id !== lastMessage?.id
                ) {
                    lastSuccessMessageRef.current = lastMessage;
                    sendResponse(lastMessage.content);
                }

                if (
                    lastMessage?.status === 'inProgress' && lastQuery?.role === 'user'
                    && lastQuery.id !== lastSendQueryRef.current?.id
                ) {
                    lastSendQueryRef.current = lastQuery;
                    sendQuery({prompt: lastQuery.content});
                }
                // 只有普通对话新发起一轮时更新activeTabKey
                if (activeTabKey !== ChatTabKey.CHAT && latestMessages.length !== messages.length) {
                    setActiveTabKey(ChatTabKey.CHAT);
                }

                initStaleRef.current = true;

                setMessages(latestMessages);
            });

            messageHandler.listen(EventMessage.ClearSessionEvent, () => {
                setClearModalVisible(true);
            });

            messageHandler.listen(EventMessage.MoreOperationsEvent, () => {
                setMoreOperationsVisible(prevMoreOperationsVisible => !prevMoreOperationsVisible);
            });

            messageHandler.listen(EventMessage.HistoryChatEvent, handleHistoryChat);

            messageHandler.listen(EventMessage.OpenPluginConfigPanelEvent, () => {
                setMoreOperationsVisible(false);
            });

            messageHandler.listen(EventMessage.CreateNewChatEvent, createNewChat);

            messageHandler.listen(EventMessage.CopyLogHistoryEvent, () => {
                setMessageVisible(true);
            });

            messageHandler.listen(
                EventMessage.ModalOpenEvent,
                (payload: {key: string, title: string, description: string}) => {
                    setModalConfig({
                        visible: true,
                        title: payload.title,
                        description: payload.description,
                        modalKey: payload.key,
                    });
                }
            );

            /** 如果侧边栏打开时，实时接收IDE助理插件推过来的消息 */
            // messageHandler.listen(
            //     EventMessage.ComatePairScanResultMessageStream,
            //     (payload: {message: ScanMessage, activeFileUri: string}) => {
            //         flushComatePairMessage({...payload, hasUnReadMessage: activeTabKeyRef.current !== ChatTabKey.PAIR});
            //     }
            // );

            /** 如果侧边栏未打开时，等到打开后同步一次完整的消息 */
            // messageHandler.listen(
            //     EventMessage.ComatePairScanResultMessageSyncRequest,
            //     (payload: ComatePairRepaintMessagePayload['payload']) => {
            //         refreshComatePairMessage({
            //             ...payload,
            //             hasUnReadMessage: !!messages.length && activeTabKeyRef.current !== ChatTabKey.PAIR,
            //         });
            //     }
            // );

            messageHandler.listen(
                EventMessage.InputFocusEvent,
                (opts: {activeTabKey: ChatTabKey}) => {
                    inputRef.current?.focus();
                    setActiveTabKey(opts?.activeTabKey);
                    welcomeRef.current?.focusLoginButton();
                }
            );

            messageHandler.listen(
                EventMessage.AgentConversationAddFromIdeEvent,
                (data: WebviewAgentPayload) => {
                    setActiveTabKey(ChatTabKey.AGENT);
                    createConversation(data);
                    // 如果是第一次打开 webview 且紧跟着调这个事件的话，因为初始化没完成，事件可能丢失
                    // 这里加个返回值方便调用方判断 webview 是否收到事件了
                    return true;
                }
            );

            const init = async () => {
                // 并发 send 初始化时所所需的信息，resolve 后分别 set 状态
                messageHandler.send(EventMessage.UsernameFetchEvent).then(({username, chineseName}) => {
                    setUsername(username ?? '');
                    setChineseName(chineseName);
                });
                messageHandler.send(EventMessage.MessagesFetchEvent).then(messages => {
                    if (!initStaleRef.current) {
                        setMessages(messages);
                    }
                });
                messageHandler.send(EventMessage.ColorThemeFetchEvent).then(theme => {
                    handleColorThemeChange(theme);
                });
                messageHandler.send(EventMessage.BannerVersionFetchEvent).then(bannerRes => {
                    setBannerState(bannerRes);
                });
                messageHandler.send(EventMessage.InputHistoryFetchEvent).then((inputHistory: unknown) => {
                    // intelliJ 中返回可能是 null，前端兼容一下
                    setInputBoxMessageHistory(Array.isArray(inputHistory) ? inputHistory : []);
                });
            };
            init();
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [activeTabKey, sendResponse]
    );

    useEffect(
        () => {
            if (rootContainerRef.current) {
                rootContainerRef.current.focus();
                const resizeObserver = new ResizeObserver(() => {
                    setRefHeight(rootContainerRef.current?.offsetHeight);
                });
                resizeObserver.observe(rootContainerRef.current);
                const refCopy = rootContainerRef.current;

                return () => {
                    resizeObserver.unobserve(refCopy);
                };
            }
        },
        [rootContainerRef]
    );

    useEffect(
        () => {
            messageHandler.listen(EventMessage.EngineInitEvent, () => {
                // eslint-disable-next-line no-console
                console.log('engine init');
                setEngineInitialized(true);
            });
        },
        []
    );

    useEffect(
        () => {
            async function init() {
                const engineInited = await messageHandler.send(EventMessage.WebViewInitEvent, {});
                setEngineInitialized(engineInited);
            }
            init();
        },
        []
    );

    useEffect(
        () => {
            if (!isInternal) {
                messageHandler.listen(EventMessage.LicenseValidityChangeEvent2, validity => {
                    setLicenseValidity(validity);
                });
                messageHandler.send(EventMessage.LicenseValidityFetchEvent2).then(validity => {
                    setLicenseValidity(validity);
                });
                messageHandler.send(EventMessage.AvatarFetchEvent).then((avatar?: string) => {
                    setAvatarUrl(avatar ?? '');
                });
                messageHandler.listen(EventMessage.AvatarChangeEvent, (avatar?: string) => {
                    setAvatarUrl(avatar ?? '');
                });
                messageHandler.send(EventMessage.LoginStatusFetchEvent).then((isLogin?: boolean) => {
                    setIsLogin(isLogin ?? true);
                });
                messageHandler.listen(EventMessage.LoginStatusChangeEvent, (isLogin?: boolean) => {
                    setIsLogin(isLogin ?? true);
                });

                messageHandler.send(EventMessage.LanguageFetchEvent).then((lng?: 'en' | 'zh') => {
                    setDisplayLanguage(lng);
                });
                messageHandler.listen(EventMessage.LanguageChangeEvent, (lng?: 'en' | 'zh') => {
                    setDisplayLanguage(lng);
                });
            }
        },
        []
    );

    const context = {
        webviewConsumer,
        currentMessageId,
        theme: theme ?? 'dark',
        messageOrder,
        setOrder,
        messageFeedback,
        setFeedback,
    };

    // TODO 这里有类型问题吗？
    const enableChat = isLogin === true;

    const showChatHeader = isJetbrains || webviewConsumer === 'xcode';

    const questions = messages.filter(item => item.role === 'user');
    const lastQuestion = questions[questions.length - 1];
    const shouldShowRecommendQuestion = activeTabKey === ChatTabKey.CHAT && enableChat
        && lastQuestion?.content === lastQuery?.prompt;
    const lastQuestionId = lastQuestion?.id;

    const {toast} = useToast();
    useEffect(
        () => {
            messageHandler.listen(
                EventMessage.ToastMessageChangeEvent,
                (toastMessage: ToastMessage) => {
                    toast(toastMessage);
                }
            );
        },
        [toast]
    );

    // 当对话发生变更时（用户发起提问）切换回对话模式
    useEffect(
        () => {
            if (lastQuestionId) {
                setHistoryVisible(false);
                setSQLVisible(false);
            }
        },
        [lastQuestionId]
    );

    const showChatTab = useMemo(
        () => {
            // JetBrains有单独的chatHeader
            if (isJetbrains) {
                return false;
            }
            // 如果前台有对话，不显示tab
            if (activeTabKey === ChatTabKey.AGENT && foregroundConversation) {
                return false;
            }
            return true;
        },
        [foregroundConversation, activeTabKey]
    );

    const mainContent = useMemo(
        () => {
            switch (activeTabKey) {
                case ChatTabKey.CHAT:
                    return (
                        <>
                            {(messages.length <= 0 || !enableChat) && (
                                <Welcome
                                    ref={welcomeRef}
                                    theme={theme}
                                    showLogin={!enableChat}
                                    bannerState={bannerState}
                                    setBannerVersion={updateBannerVersion}
                                    isGithubUser={licenseValidity?.keyType === 'GITHUB'}
                                />
                            )}
                            {enableChat && (
                                <div
                                    ref={messageContainerRef}
                                    role="list"
                                    className="messages-container"
                                >
                                    {displayMessages
                                        .map((list, index) => (
                                            <ChatBox
                                                key={list[0].id}
                                                messages={list}
                                                username={username}
                                                chineseName={chineseName}
                                                avatarUrl={avatarUrl}
                                                total={displayMessages.length}
                                                current={index + 1}
                                                isLastItem={index === displayMessages.length - 1}
                                            />
                                        ))}
                                </div>
                            )}
                            {shouldShowRecommendQuestion && (
                                <RecommendQuestion
                                    messageOrder={messageOrder}
                                    supportAt={!!showRelatedToAt}
                                    messages={messages}
                                    hasMessageInProgress={hasMessageInProgress}
                                />
                            )}
                            <div
                                ref={messageBottomRef}
                                className="message-scroll-anchor h-1 w-full -mt-1"
                            />
                        </>
                    );
                case ChatTabKey.AGENT:
                    return (
                        <SmartAgent
                            engineInitialized={engineInitialized}
                            username={username}
                            chineseName={chineseName}
                            avatarUrl={avatarUrl}
                            isLogin={isLogin}
                        />
                    );
                case ChatTabKey.KERNEL_DEMO:
                    return (
                        <KernelDemo />
                    );
                default:
                    return null;
            }
        },
        [
            isLogin,
            engineInitialized,
            activeTabKey,
            messages,
            enableChat,
            welcomeRef,
            theme,
            bannerState,
            licenseValidity,
            messageContainerRef,
            displayMessages,
            username,
            chineseName,
            avatarUrl,
            shouldShowRecommendQuestion,
            messageOrder,
            showRelatedToAt,
            hasMessageInProgress,
            messageBottomRef,
            updateBannerVersion,
        ]
    );

    const inChatPanel = activeTabKey === ChatTabKey.CHAT && enableChat;
    // TODO: 这俩能合并吗
    const showUserGuide = messages.length === 0 && enableUserGuide && enableUserGuideByLicense;
    return (
        <ChatConfigProvider context={context}>
            <SuggestionProvider messageOrder={messageOrder} supportAt={!!showRelatedToAt} onSubmit={handleSubmit}>
                <LogProvider context={{userName: username}}>
                    {moreOperationsVisible && <MoreOperations onSQLClick={showSQL} />}
                    {messages.length <= 0 && isLatest && (
                        <div className="flex items-center justify-center left-[50%] top-1 w-[156px] h-[42px] rounded fixed z-20 bg-[var(--comate-tooltip-background)] border border-solid border-[var(--comate-tooltip-border)] transform translate-x-[-50%]">
                            <span
                                className="w-4 h-4 ml-1 mr-1 inline-block"
                                dangerouslySetInnerHTML={{__html: warnIcon}}
                            />
                            <p className="text-[13px]">当前已是最新对话</p>
                        </div>
                    )}
                    {/* 历史对话等面板覆盖在对话列表上，关闭后可以恢复对话列表滚动状态的feature先回退至二选一模式 */}
                    {/* 需要全面整理项目中使用到的z-index，避免出现异常覆盖问题 */}
                    {overlayVisible && (
                        [
                            historyVisible && (
                                <HistoryChat
                                    setHistoryVisible={setHistoryVisible}
                                    messages={messages}
                                    foucChatTabWhenFocus={foucChatTabWhenFocus}
                                />
                            ),
                            SQLVisible && <SQLSchemaForm setVisible={setSQLVisible} />,
                        ]

                    )}
                    {!overlayVisible && (
                        <div
                            className="relative h-screen flex flex-col bg-[var(--comate-editor-background)] border-x border-[var(--comate-panel-border)]"
                            ref={rootContainerRef}
                        >
                            {showChatHeader && (
                                <JetBrainsChatHeader
                                    messages={messages}
                                    onClearMessages={showModel}
                                    onSQLClick={showSQL}
                                    isLogin={isLogin}
                                    username={username}
                                    createNewChat={createNewChat}
                                    handleHistoryChat={handleHistoryChat}
                                />
                            )}
                            {showChatTab && (
                                <ChatTab
                                    activeKey={activeTabKey}
                                    onChange={handleActiveTabKeyChange}
                                    username={username}
                                    isLogin={isLogin}
                                />
                            )}
                            <main
                                ref={mainRef}
                                className="h-full overflow-x-hidden overflow-y-auto z-10"
                                role="presentation"
                            >
                                {mainContent}
                            </main>
                            {inChatPanel && (
                                <div className="grow-0 px-4 pt-2 pb-4">
                                    <div className="mb-[10px] overflow-hidden">
                                        {showUserGuide && (
                                            <UserGuide
                                                messageOrder={messageOrder}
                                                supportAt={!!showRelatedToAt}
                                                refHeight={refHeight}
                                            />
                                        )}
                                    </div>
                                    <div className="bottom-0 w-full">
                                        <Suggestions messages={messages} hasMessageInProgress={hasMessageInProgress} />
                                        {inputBox}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}


                    <div>
                        {clearModalVisible && (
                            <Modal
                                title={t(MODAL_CLEAR_TITLE)}
                                description={t(MODAL__CLEAR_DESCRIPTION)}
                                closeModal={closeClearModal}
                                onOk={clearMessages}
                            />
                        )}
                        {(modalConfig && modalConfig.visible) && (
                            <Modal
                                title={modalConfig.title}
                                description={modalConfig.description}
                                modalKey={modalConfig.modalKey}
                                closeModal={closeModal}
                                onOk={handleModalSubmit}
                            />
                        )}
                    </div>
                    <Message
                        content={t(MESSAGE_COPY_SUCCESS)}
                        messageVisible={messageVisible}
                        setMessageVisible={setMessageVisible}
                    />
                </LogProvider>
            </SuggestionProvider>
        </ChatConfigProvider>
    );
}

export default compose([
    InputBoxSizeToggleProvider,
    TestEventProvider,
    SmartApplyProvider,
    AccessToProvider,
    EditorWatcherProvider,
    ToastProvider,
    ExtensionConfigProvider,
    I18nLangaugeProvider,
    ComposerMessageProvider,
    ChatInputProvider,
    InputModalProvider,
    ChatTabProvider,
    AgentProvider,
])(memo(Chat));
