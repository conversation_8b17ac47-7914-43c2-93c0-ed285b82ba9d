import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import {withTimeout} from '@/utils/promise';

interface Res {
    result: {
        success: boolean;
        message: string;
    };
    args: {
        from: string;
        to?: string;
        fileUri: string;
    };
}

export const replaceCode = (fileUri: string, from: string, to: string, logId: string) => {
    const action = EventMessage.ComatePairFileContentReplace;
    messageHandler.send(action, {fileUri, from, to, logId});

    const wait = new Promise<void>(
        (resolve, reject) => {
            messageHandler.listen(
                action,
                (res: Res) => {
                    if (
                        res.args.fileUri === fileUri
                        && res.args.from === from
                        && res.args.to === to
                    ) {
                        if (res.result.success) {
                            resolve();
                        }
                        else {
                            reject(new Error(res.result.message));
                        }
                    }
                }
            );
        }
    );

    return withTimeout(wait, 1000);
};

export const checkCodeExist = (fileUri: string, from: string) => {
    const action = EventMessage.ComatePairFileContentCheckExist;
    messageHandler.send(action, {fileUri, from});

    const wait = new Promise<void>(
        (resolve, reject) => {
            messageHandler.listen(
                action,
                (res: Res) => {
                    if (
                        res.args.fileUri === fileUri
                        && res.args.from === from
                    ) {
                        if (res.result.success) {
                            resolve();
                        }
                        else {
                            reject(new Error(res.result.message));
                        }
                    }
                }
            );
        }
    );

    return withTimeout(wait, 1000);
};

export const copyCode = (content: string, logId: string) => {
    const action = EventMessage.ComatePairFileContentCopy;
    messageHandler.send(action, {content, logId});
};
