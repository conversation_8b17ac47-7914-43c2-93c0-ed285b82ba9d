/**
 * 判断一个元素是否在视窗里，在scrollIntoView的场景可以使用，防止点一次scroll一次
 * @param {HTMLElment} 元素
 */
export const isInViewPort = (element: HTMLElement | null) => {
    if (!element) {
        return false;
    }

    const viewWidth = window.innerWidth || document.documentElement.clientWidth;
    const viewHeight = window.innerHeight || document.documentElement.clientHeight;
    const {top, right, bottom, left} = element.getBoundingClientRect();

    return top >= 0 && left >= 0 && right <= viewWidth && bottom <= viewHeight;
};

export const ensureActiveElementInViewport = (element: HTMLElement | null, options?: ScrollIntoViewOptions) => {
    if (!element || isInViewPort(element)) {
        return;
    }

    const scrollIntoViewOptions = options || {};
    element.scrollIntoView(scrollIntoViewOptions);
};
