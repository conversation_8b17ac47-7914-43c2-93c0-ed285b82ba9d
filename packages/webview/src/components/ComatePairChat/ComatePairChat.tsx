/* eslint-disable max-len */
/* bca-disable */
import ReactMarkdown from 'react-markdown';
import {DrawElement, SimpleCodeBlock} from '@comate/plugin-shared-internals';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import {partial} from 'lodash';
import {useCallback, useEffect, useRef, useState} from 'preact/hooks';
import {EventMessage, Feature} from '@shared/protocols';
import {ReactNode} from 'preact/compat';
import {isCodeBlock, isReplaceMethod} from '@shared/utils/scan';
import {messageHandler} from '@/utils/messageHandler';
import fileDetailIcon from '@/assets/fileDetail.svg';
import loadIcon from '@/assets/load.png';
import acceptIcon from '@/assets/acceptLine.svg';
import cancelIcon from '@/assets/cancel.svg';
import {toastMessage} from '@/utils/message';
import CodeBlock from '../Markdown/CodeBlock';
import ToolbarButton from '../ToolbarButton';
import {TooltipWithIcon} from '../Tooltip/TooltipWithIcon';
import Avatar from '../ChatBox/Avatar';
import {ScanMessage, useComatePairChatContext} from './useComatePairChat';
import {useTypewriteStyleContent} from './useTypewriteStyleContent';
import ComatePairCodeBlock from './ComatePairCodeBlock';
import ComatePairCollapse from './ComatePairCollapse';
import {replaceCode} from './execCodeBlockAction';
import './ComatePairChat.css';
import ComatePairEmpytPlaceholder from './Empty';
import {ensureActiveElementInViewport} from './ensureActiveElementInViewport';

function normalizeChunks(content: DrawElement | DrawElement[]) {
    if (!Array.isArray(content)) {
        return [content];
    }

    const normalized = Array.from(content).reverse().reduce<DrawElement[]>(
        (result, element: DrawElement) => {
            // 如果倒装后的数组，前一个和后一个都是字符串，就合并
            if (typeof element === 'string' && typeof result.at(-1) === 'string') {
                // @ts-expect-error
                result[result.length - 1] = element + '\n\n' + result[result.length - 1];
            }
            else {
                result.push(element);
            }
            return result;
        },
        []
    );

    normalized.reverse();
    return normalized;
}

function Markdown({content}: {content: string}) {
    const handleLinkClick = useCallback(
        (href: string | undefined) => {
            if (!href) {
                return;
            }
            messageHandler.send(EventMessage.LinkClickEvent, href);
        },
        []
    );

    return (
        <ReactMarkdown
            className="break-all"
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
            components={{
                code({children, node, className, position, ...rest}: any) {
                    return (
                        <CodeBlock
                            showHeader
                            inline={!className}
                            className={className}
                            {...rest}
                        >
                            {children}
                        </CodeBlock>
                    );
                },
                a({href, children}) {
                    return (
                        <a
                            className="text-[var(--comate-link-color)] hover:text-[var(--comate-link-color)] hover:cursor-pointer hover:underline"
                            onClick={partial(handleLinkClick, href)}
                        >
                            {children}
                        </a>
                    );
                },
            }}
        >
            {content}
        </ReactMarkdown>
    );
}

function MessageWrapper(
    {children, message, disabled}: {children: ReactNode, message: ScanMessage, disabled?: boolean}
) {
    const {flushComatePairMessage, activeFileUri} = useComatePairChatContext();
    const handleCancel = useCallback(
        () => {
            flushComatePairMessage({
                hasUnReadMessage: false,
                activeFileUri,
                message: {...message, loading: false},
            });
            messageHandler.send(EventMessage.ComatePairScanProgressCancel, {taskId: message.taskId});
        },
        [activeFileUri, flushComatePairMessage, message]
    );

    const [visible, setVisible] = useState<boolean>(false);
    const acceptAllCodes = useCallback(
        async () => {
            if (!Array.isArray(message.content)) {
                return;
            }
            try {
                for await (const content of message.content) {
                    if (isCodeBlock(content)) {
                        const codeBlock = content as SimpleCodeBlock & {
                            id: string;
                            diffLines: {add: number[], remove: number[]};
                        };
                        const {
                            from,
                            to,
                        } = codeBlock.acceptMethods!.find(isReplaceMethod)!;

                        await replaceCode(message.fileUri, from, to, content.id);
                    }
                }
                toastMessage.info('批量采纳成功~');
            }
            catch (ex) {
                if (ex instanceof Error) {
                    setVisible(true);
                }
            }
        },
        [message]
    );

    return (
        <div className="message-content py-2 pl-2">
            <div className="flex w-full justify-between items-center mb-3">
                <div style={{alignItems: 'center', display: 'flex', gap: 8}}>
                    <Avatar role="assistant" agent={Feature.AutoWork} />
                    <p className="opacity-60">Comate</p>
                </div>
                {!disabled && (
                    <TooltipWithIcon
                        visible={visible}
                        overlay="当前编码现场已变化，可以复制后使用"
                        type="warning"
                        countdown={5}
                        placement="bottomRight"
                        onVisibleChange={setVisible}
                    >
                        <ToolbarButton
                            title="采纳全部"
                            disabled={!!visible}
                            icon={acceptIcon}
                            label="采纳"
                            ariaTitle="采纳全部"
                            onClick={acceptAllCodes}
                            placement="top"
                        />
                    </TooltipWithIcon>
                )}
            </div>
            {children}
            {message.loading && (
                <div className="mt-4 text-[var(--comate-descriptionForeground)]">
                    <button className="cursor-pointer flex gap-1 items-center hover:opacity-80" onClick={handleCancel}>
                        <span className="w-4 h-4" dangerouslySetInnerHTML={{__html: cancelIcon}}></span>
                        停止生成
                    </button>
                </div>
            )}
        </div>
    );
}

interface Props {
    content: string;
    streaming: boolean;
    children: (props: {content: string}) => ReactNode;
}

function TypewriteWrapper({content, streaming, children}: Props) {
    const typingContent = useTypewriteStyleContent(content, streaming);
    const previousContent = useRef('');

    const anchor = useRef<HTMLDivElement>(null);

    // 先简单处理下，在更新时，默认滚动，如果用户向上滚了，则不再自动滚动
    const lockScroll = useRef(false);

    useEffect(
        () => {
            if (previousContent.current !== typingContent) {
                if (anchor.current && !lockScroll.current) {
                    ensureActiveElementInViewport(anchor.current, {block: 'end'});
                }
            }
            previousContent.current = typingContent;
        },
        [streaming, typingContent]
    );

    useEffect(
        () => {
            const scrollContainer = anchor.current?.closest('main');
            let scrollTop: number = 0;
            const handleScroll = e => {
                if (e.target.scrollTop < scrollTop) {
                    lockScroll.current = true;
                }
                scrollTop = e.target.scrollTop;
            };
            if (scrollContainer && streaming) {
                scrollContainer.addEventListener('scroll', handleScroll);
            }
            return () => scrollContainer?.removeEventListener('scroll', handleScroll);
        },
        [streaming]
    );

    return (
        <div className="comate-pair-md">
            {children({content: typingContent})}
            <span ref={anchor} />
        </div>
    );
}

function renderElement(
    element: DrawElement,
    fileInfo: {fileName: string, fileUri: string},
    index: number,
    streaming: boolean
) {
    if (typeof element === 'string') {
        return (
            <TypewriteWrapper content={element} streaming={streaming}>
                {({content}) => <Markdown key={index} content={content} />}
            </TypewriteWrapper>
        );
    }
    if (element.type === 'p') {
        // `Markdown`默认是会出一个`<p>`标签的
        return (
            <TypewriteWrapper content={element.children} streaming={streaming}>
                {({content}) => <Markdown key={index} content={content} />}
            </TypewriteWrapper>
        );
    }
    if (element.type === 'code-block') {
        return (
            <>
                {/* @ts-expect-error */}
                <span style={{display: 'none'}}>{element.id}</span>
                <ComatePairCodeBlock
                    fileInfo={fileInfo}
                    element={element as any}
                />
            </>
        );
    }
    return null;
}

export default function ComatePairChat() {
    const {messages} = useComatePairChatContext();
    const viewFile = useCallback(
        (fileUri: string) => {
            messageHandler.send(EventMessage.LinkClickEvent, fileUri);
        },
        []
    );

    useEffect(
        () => {
            messageHandler.send(EventMessage.ComatePairScanResultMessageSyncRequest);
        },
        []
    );

    if (messages.length === 0) {
        return <ComatePairEmpytPlaceholder />;
    }

    return (
        <div className="px-2 py-1">
            {messages.map(message => {
                const {fileName, fileUri, messages} = message;
                const loading = messages.some(message => message.loading);
                return (
                    <ComatePairCollapse
                        key={fileUri}
                        collapsed={false}
                        title={loading
                            ? (
                                <div className="flex items-center">
                                    <img
                                        className="loading-icon opacity-100 flex-shrink-0 w-3.5 h-3.5 mr-1"
                                        src={loadIcon}
                                    />
                                    {fileName}
                                </div>
                            )
                            : fileName}
                        external={
                            <ToolbarButton
                                icon={fileDetailIcon}
                                onClick={partial(viewFile, fileUri)}
                                title="查看文件"
                                ariaTitle="查看文件"
                                placement="bottomRight"
                            />
                        }
                    >
                        {messages.map((message, messageIndex) => {
                            const isLatestMessage = messageIndex === (messages.length - 1);
                            const contents = normalizeChunks(message.content);
                            return (
                                <MessageWrapper key={fileUri} disabled={loading} message={message}>
                                    {contents.map((element, i) => {
                                        const isLatestChunk = i === contents.length - 1;
                                        return (
                                            <div className="mr-2" key={message.taskId}>
                                                {renderElement(
                                                    element,
                                                    {
                                                        fileName: message.fileName,
                                                        fileUri: message.fileUri,
                                                    },
                                                    i,
                                                    loading && isLatestMessage && isLatestChunk
                                                )}
                                            </div>
                                        );
                                    })}
                                </MessageWrapper>
                            );
                        })}
                    </ComatePairCollapse>
                );
            })}
        </div>
    );
}
