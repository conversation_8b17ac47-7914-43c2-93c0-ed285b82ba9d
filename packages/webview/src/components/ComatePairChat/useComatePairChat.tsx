import {DrawElement} from '@comate/plugin-shared-internals';
import {EventMessage} from '@shared/protocols';
import {noop} from 'lodash';
import {createContext} from 'preact';
import {useCallback, useContext, useEffect, useMemo, useReducer} from 'preact/hooks';
import {messageHandler} from '@/utils/messageHandler';

export interface ScanMessage {
    taskId: string;
    fileName: string;
    fileUri: string;
    loading: boolean;
    content: DrawElement | DrawElement[];
}

interface ComatePairFlushMessagePayload {
    type: 'COMATE_PAIR_MESSAGE_FLUSH';
    payload: {
        hasUnReadMessage: boolean;
        activeFileUri: string;
        message: ScanMessage;
    };
}

export interface ComatePairRepaintMessagePayload {
    type: 'COMATE_PAIR_MESSAGE_REPAINT';
    payload: {
        hasUnReadMessage: boolean;
        activeFileUri: string;
        messages: Array<{
            fileUri: string;
            fileName: string;
            messages: ScanMessage[];
        }>;
    };
}

interface ComatePairToggleFilterFlagPayload {
    type: 'COMATE_PAIR_TOGGLE_ONLY_SHOW_ACTIVE_FILE';
    payload: {
        checked: boolean;
    };
}

interface ComatePairChangeActiveFileUriPayload {
    type: 'COMATE_PAIR_CHANGE_ACTIVE_FILE_URI';
    payload: {
        activeFileUri: string;
    };
}

type Action =
    | ComatePairFlushMessagePayload
    | ComatePairRepaintMessagePayload
    | ComatePairToggleFilterFlagPayload
    | ComatePairChangeActiveFileUriPayload;

export interface FileScanMessageGroup {
    fileUri: string;
    fileName: string;
    messages: ScanMessage[];
}

export const useComatePairChat = () => {
    const [comatePairMessages, dispatchComatePair] = useReducer<
        {
            messages: FileScanMessageGroup[];
            hasUnReadMessage: boolean;
            activeFileUri: string;
            onlyShowActiveFileAdvice: boolean;
        },
        Action
    >(
        (prevState, action) => {
            switch (action.type) {
                case 'COMATE_PAIR_CHANGE_ACTIVE_FILE_URI': {
                    return {
                        ...prevState,
                        activeFileUri: action.payload.activeFileUri,
                    };
                }
                case 'COMATE_PAIR_TOGGLE_ONLY_SHOW_ACTIVE_FILE': {
                    const checked = action.payload.checked;
                    return {
                        ...prevState,
                        onlyShowActiveFileAdvice: checked,
                    };
                }
                case 'COMATE_PAIR_MESSAGE_FLUSH': {
                    const {message, hasUnReadMessage} = action.payload;
                    const {taskId, fileName, fileUri, content, loading} = message;
                    const existedFile = prevState.messages.find(file => file.fileUri === fileUri);
                    if (existedFile) {
                        const existedMessage = existedFile.messages.find(message => message.taskId === taskId);
                        const messages = prevState.messages.map(task => {
                            if (task.fileUri === fileUri) {
                                return {
                                    ...task,
                                    messages: existedMessage
                                        ? task.messages.map(message => {
                                            if (message.taskId === taskId) {
                                                return {...message, loading, content: content};
                                            }
                                            return message;
                                        })
                                        : task.messages.concat(message),
                                };
                            }
                            return task;
                        });
                        return {
                            ...prevState,
                            messages,
                            activeFileUri: action.payload.activeFileUri,
                            hasUnReadMessage,
                        };
                    }
                    return {
                        ...prevState,
                        activeFileUri: action.payload.activeFileUri,
                        messages: [...prevState.messages, {fileUri, fileName, messages: [message]}],
                    };
                }
                case 'COMATE_PAIR_MESSAGE_REPAINT': {
                    const {messages, hasUnReadMessage} = action.payload;
                    return {
                        ...prevState,
                        hasUnReadMessage,
                        activeFileUri: action.payload.activeFileUri,
                        messages,
                    };
                }
            }
        },
        {messages: [], hasUnReadMessage: false, activeFileUri: '', onlyShowActiveFileAdvice: false}
    );

    const flushComatePairMessage = useCallback(
        (payload: ComatePairFlushMessagePayload['payload']) => {
            dispatchComatePair({
                type: 'COMATE_PAIR_MESSAGE_FLUSH',
                payload,
            });
        },
        []
    );

    const refreshComatePairMessage = useCallback(
        (payload: ComatePairRepaintMessagePayload['payload']) => {
            dispatchComatePair({
                type: 'COMATE_PAIR_MESSAGE_REPAINT',
                payload,
            });
        },
        []
    );

    const messages = useMemo(
        () => {
            if (comatePairMessages.onlyShowActiveFileAdvice) {
                return comatePairMessages.messages.filter(
                    ({fileUri}) => fileUri === comatePairMessages.activeFileUri
                );
            }
            return comatePairMessages.messages;
        },
        [comatePairMessages]
    );

    const handleOnlyShowActiveFileFlagChange = useCallback(
        (checked: boolean) => {
            dispatchComatePair({
                type: 'COMATE_PAIR_TOGGLE_ONLY_SHOW_ACTIVE_FILE',
                payload: {checked},
            });
        },
        []
    );

    useEffect(
        () => {
            messageHandler.listen(
                EventMessage.ComatePairActiveDocumentChange,
                (payload: {activeFileUri: string}) => {
                    dispatchComatePair({
                        type: 'COMATE_PAIR_CHANGE_ACTIVE_FILE_URI',
                        payload,
                    });
                }
            );
        },
        []
    );

    return useMemo(
        () => ({
            onlyShowActiveFileAdvice: comatePairMessages.onlyShowActiveFileAdvice,
            messages,
            activeFileUri: comatePairMessages.activeFileUri,
            hasUnReadedAutoWorkChatMessage: comatePairMessages.hasUnReadMessage,
            flushComatePairMessage,
            refreshComatePairMessage,
            handleOnlyShowActiveFileFlagChange,
        }),
        [
            messages,
            comatePairMessages,
            flushComatePairMessage,
            refreshComatePairMessage,
            handleOnlyShowActiveFileFlagChange,
        ]
    );
};

const context = createContext<ReturnType<typeof useComatePairChat>>({
    messages: [],
    activeFileUri: '',
    onlyShowActiveFileAdvice: false,
    hasUnReadedAutoWorkChatMessage: false,
    handleOnlyShowActiveFileFlagChange: noop,
    flushComatePairMessage: noop,
    refreshComatePairMessage: noop,
});

export const ComatePairChatProvider = context.Provider;
export const useComatePairChatContext = () => useContext(context);
