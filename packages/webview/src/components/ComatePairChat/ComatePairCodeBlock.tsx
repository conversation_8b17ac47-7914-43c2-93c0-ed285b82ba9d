import {ReplaceContentAcceptMethod, SimpleCodeBlock} from '@comate/plugin-shared-internals';
import {useCallback, useMemo, useState} from 'preact/hooks';
import CodeBlock from '../Markdown/CodeBlock';
import {CheckboxChangeEvent, CheckboxWithLabel} from '../Checkbox';
import {checkCodeExist, copyCode, replaceCode} from './execCodeBlockAction';

interface Props {
    element: SimpleCodeBlock & {id: string, diffLines: {add: number[], remove: number[]}};
    renderHeaderTitle?: (language: string) => string;
    fileInfo: {
        fileName: string;
        fileUri: string;
    };
}

/** 按第一行缩进移除所有前面的空格 */
const removeLeadingSpace = (text: string) => {
    const matchTabIndent = /^[\t]+/.exec(text);
    const matchSpaceIndent = /^[\s]+/.exec(text);
    const indent = matchTabIndent
        ? {type: 'tab', num: matchTabIndent[0].length || 0}
        : {type: 'space', num: matchSpaceIndent?.[0].length || 0};
    const regex = new RegExp(`^${indent.type === 'tab' ? '\\t' : '\\s'}{${indent.num}}`);
    return text.split('\n').map(line => line.replace(regex, '')).join('\n');
};

export default function ComatePairCodeBlock({element, fileInfo}: Props) {
    const {
        from,
        to,
    } = element.acceptMethods?.find(({method}) => method === 'replaceContent') as ReplaceContentAcceptMethod;
    const [canReplace, setCanReplace] = useState<boolean>(true);

    const [checked, setDiffFlagChecked] = useState(true);
    const handleDiffViewToggle = useCallback(
        (e: CheckboxChangeEvent) => {
            setDiffFlagChecked(e.target.checked);
        },
        []
    );

    const onMouseEnter = useCallback(
        async () => {
            try {
                await checkCodeExist(fileInfo.fileUri, from);
                setCanReplace(true);
            }
            catch (ex) {
                setCanReplace(false);
            }
        },
        [fileInfo, from]
    );

    const accept = useCallback(
        async () => {
            await replaceCode(fileInfo.fileUri, from, to, element.id);
        },
        [element.id, fileInfo, from, to]
    );

    const copy = useCallback(
        (content: string) => {
            // 如果勾选显示变更，且复制的内容长度比原内容长，则复制to的内容，兼容划词选中改动场景
            if (checked && content.length > Math.max(from.length, to.length)) {
                copyCode(to, element.id);
            }
            else {
                copyCode(content, element.id);
            }
        },
        [checked, element.id, from, to]
    );

    const actions = useMemo<Record<string, any>>(
        () => {
            return canReplace ? {accept, copy} : {copy};
        },
        [accept, canReplace, copy]
    );

    return (
        <CodeBlock
            inline={false}
            showHeader
            showActions
            title={
                <CheckboxWithLabel
                    label="显示变更"
                    name="diffViewChecked"
                    onChange={handleDiffViewToggle}
                    checked={checked}
                />
            }
            diffLines={checked ? element.diffLines : undefined}
            attributes={{onMouseEnter}}
            // TODO 换成动态的语言
            className={`language-${element.language} code-with-max-content-width`}
            actions={actions}
        >
            {checked ? element.children : removeLeadingSpace(to)}
        </CodeBlock>
    );
}
