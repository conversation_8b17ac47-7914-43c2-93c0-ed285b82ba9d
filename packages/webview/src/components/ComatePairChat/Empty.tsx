import comatePairEmptyBgSrc from '@/assets/comate-pair-empty-bg.png';

export default function ComatePairEmpytPlaceholder() {
    return (
        <div
            className="flex flex-col justify-center items-center"
            style={{marginTop: 165}}
        >
            <img
                style={{width: 203, height: 151, marginBottom: 8}}
                src={comatePairEmptyBgSrc}
                alt="欢迎使用Comate助理模式"
            />
            <p style={{textAlign: 'center'}}>
                知你所想、懂你所需
                <br />
                伴随你的编码过程，实时获取精准代码建议
            </p>
            <div className="mt-1">
                <small className="text-[var(--comate-disabled-iconColor)]">
                    目前仅支持Python、Go语言，更多能力正在开发中，参考
                </small>
                <a
                    className="text-[var(--comate-link-color)] ml-1"
                    href="https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/Hb6OQz5Jc7/7935fb34d45441"
                >
                    <small>助理模式尝鲜手册</small>
                </a>
            </div>
        </div>
    );
}
