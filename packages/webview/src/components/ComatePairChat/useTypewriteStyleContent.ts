import {useEffect, useRef, useState} from 'preact/hooks';
import {Typewriter} from '@/utils/Typewriter';

export const useTypewriteStyleContent = (initialState: string, streaming: boolean) => {
    const [content, setContent] = useState(initialState);
    const typewriter = useRef(
        new Typewriter(
            {
                update: setContent,
            },
            initialState ?? ''
        )
    );

    useEffect(
        () => {
            if (streaming) {
                typewriter.current.typeString(initialState);
            }
            else {
                typewriter.current.typeAllOut(initialState, {useIncomingTextFirst: true});
            }
        },
        [initialState, streaming]
    );

    return content;
};
