/* bca-disable */
import {useCallback} from 'preact/hooks';
import {ComponentChild} from 'preact';
import {ReactNode} from 'preact/compat';
import rightIcon from '@/assets/right.svg';
import {useDerivedState} from '../Tabs/useDerivedState';
import './ComatePairCollapse.css';

interface CollapseProps {
    title: ReactNode;
    children: ComponentChild;
    collapsed: boolean;
    external?: ReactNode;
    onCollapsedChange?: (collapsed: boolean) => void;
}

const stopPropagation = (e: any) => e.stopPropagation();

export default function ComatePairCollapse({
    title,
    children,
    collapsed,
    onCollapsedChange,
    external,
}: CollapseProps) {
    const [internalCollapsed, setCollapsed] = useDerivedState(collapsed);
    const handleClick = useCallback(
        () => {
            setCollapsed(!internalCollapsed);
            onCollapsedChange?.(!internalCollapsed);
        },
        [internalCollapsed, onCollapsedChange, setCollapsed]
    );

    return (
        <div className={`comate-collapse ${internalCollapsed ? '' : 'comate-collapse-open'}`}>
            <div onClick={handleClick} className="comate-collapse-header flex justify-between items-center">
                <div className="flex items-center">
                    <div className="comate-collapse-icon" dangerouslySetInnerHTML={{__html: rightIcon}} />
                    {title}
                </div>
                {external && <div className="comate-collapse-external" onClick={stopPropagation}>{external}</div>}
            </div>
            {internalCollapsed
                ? null
                : (
                    <div className="comate-collapse-panel">
                        {children}
                    </div>
                )}
        </div>
    );
}
