import {createContext, ComponentChild} from 'preact';
import {Suggestion, UserQueryPromptType} from '@shared/protocols';
import {useContext, useState, useCallback} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import {TranslationKey} from '@/i18n/init';
import {useTranslation} from '@/i18n/useTranslation';
import {DEFAULT_SUGGESTIONS, UNIT_TEST_SUGGESTIONS} from './FollowUpSuggestion';
import {useChatInput} from './InputBox/hooks/useChatInput';

interface ContextValue {
    suggestions: Suggestion[];
    updateSuggestions?: (suggestions: Suggestion[]) => void;
    clearSuggestions: () => void;
    resetSuggestions: () => void;
    onSuggestionClick?: (id: Suggestion['id']) => void;
    supportAt: boolean;
}

const defaultSuggestions = [];

const Context = createContext<ContextValue>({
    supportAt: true,
    suggestions: defaultSuggestions,
    clearSuggestions: () => {},
    resetSuggestions: () => {},
});

interface Props {
    children: ComponentChild;
    messageOrder: Record<number, number>;
    supportAt: boolean;
    onSubmit: (value: string, agent?: string, slash?: string) => void;
}

function translateSuggestionPrompt(t: (key: TranslationKey) => string, suggestion?: Record<string, any>) {
    if (!suggestion || !suggestion.promptTranslationKey) {
        return suggestion;
    }
    const prompt = t(suggestion.promptTranslationKey);
    return {...suggestion, prompt};
}

export default function SuggestionProvider({children, messageOrder, supportAt, onSubmit}: Props) {
    const [suggestions, setSuggestions] = useState<Suggestion[]>(defaultSuggestions);
    const {t} = useTranslation();

    const {onCommandSelect} = useChatInput();
    const handleSuggestionClick = useCallback(
        (id: number) => {
            const suggest = suggestions.find(item => item.id === id);
            // 找不到推荐 走默认
            if (!suggest) {
                const target = id === 2
                    ? UNIT_TEST_SUGGESTIONS[0]
                    : DEFAULT_SUGGESTIONS.find(item => item.id === id);
                messageHandler.send(
                    EventMessage.QuerySendEvent,
                    {...translateSuggestionPrompt(t, target), messageOrder, supportAt}
                );
                return;
            }

            if (suggest.onClick) {
                suggest.onClick();
                return;
            }

            if (suggest.type === 'help') {
                onSubmit('', 'Comate', 'help');
                return;
            }

            // 有需要自动聚焦的
            if (suggest.autoFocus && suggest.slash && suggest.agent) {
                onCommandSelect(`${suggest.slash}@${suggest.agent}`);
            }

            if (suggest.slash) {
                messageHandler.send(EventMessage.SuggestionClickEvent, {
                    prompt: {
                        slash: suggest.slash,
                        prompt: suggest.prompt,
                        agent: suggest.agent,
                        type: 'default',
                        supportAt,
                        messageOrder,
                    } as UserQueryPromptType,
                });
                return;
            }

            messageHandler.send(
                EventMessage.QuerySendEvent,
                {...translateSuggestionPrompt(t, suggest), messageOrder, supportAt}
            );
        },
        [messageOrder, onCommandSelect, onSubmit, suggestions, supportAt, t]
    );

    const clearSuggestions = useCallback(
        () => {
            setSuggestions([]);
        },
        []
    );

    const resetSuggestions = useCallback(
        () => {
            setSuggestions(defaultSuggestions);
        },
        []
    );

    const updateSuggestions = useCallback(
        (suggestions: Suggestion[]) => {
            setSuggestions(suggestions);
        },
        []
    );

    // useEffect(
    //     () => {
    //         messageHandler.listen(
    //             EventMessage.SuggestionsRefreshEvent,
    //             (payload: {suggestions: Suggestion[], type: string}) => {
    //                 updateSuggestions(payload.suggestions);
    //             }
    //         );
    //     },
    //     [updateSuggestions]
    // );

    return (
        <Context.Provider
            value={{
                suggestions,
                clearSuggestions,
                resetSuggestions,
                updateSuggestions,
                supportAt,
                onSuggestionClick: handleSuggestionClick,
            }}
        >
            {children}
        </Context.Provider>
    );
}

export function useSuggestion() {
    return useContext(Context);
}
