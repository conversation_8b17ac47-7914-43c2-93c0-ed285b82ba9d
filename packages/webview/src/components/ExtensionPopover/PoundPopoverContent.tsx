/* bca-disable */
import {useTranslation} from 'react-i18next';
import iconRightArrow from '@/assets/rightArrows.svg';
import Popover from '../Tooltip/Popover';
import {KnowledgeCascadeMapping} from '../InputBox/KnowledgeIcon';
import {HighlightText} from './HighlightText';
import {findPositionInOrder} from './findPosition';
import ItemHover from './HoverInfo';
import {PopoverContentProps} from './types';

export function PoundPopoverContent(props: PopoverContentProps) {
    const {
        items,
        selectItem,
        searchValue,
        selectedRef,
        currentSelect,
        popoverWidth,
    } = props;
    const {t} = useTranslation();

    return (
        <>
            {items.map(item => {
                const index = items.indexOf(item);
                const isActive = currentSelect === item.id;
                const isDisabled = !!item.disabled;
                const name = item.displayNameKey ? t(item.displayNameKey) : (item.displayName ?? item.name);
                const commonProps = {
                    ref: item.id === currentSelect ? selectedRef : undefined,
                    disabled: isDisabled,
                    selected: isActive,
                    description: isDisabled ? item.disabledReason : item.description,
                    onClick: () => selectItem(item),
                    statusIcon: KnowledgeCascadeMapping[item.id] && (
                        <div
                            aria-hidden="true"
                            className="w-4 h-4 shrink-0"
                            dangerouslySetInnerHTML={{__html: iconRightArrow}}
                        >
                        </div>
                    ),
                };

                return (
                    <ItemHover
                        key={item.id}
                        content={item.hoverContent}
                        onHover={item.onHover}
                        onHoverLeave={item.onHoverLeave}
                        width={popoverWidth ? popoverWidth * 0.6 : undefined}
                    >
                        <Popover.Item
                            {...commonProps}
                            elementRef={commonProps.ref}
                            tabIndex={index}
                            id={`input_popover_${item.id}`}
                            role="option"
                            aria-label={name}
                            selected={isActive}
                        >
                            <div className="flex items-center gap-1 overflow-hidden">
                                <span>#</span>
                                <span className="ellipsis">
                                    <HighlightText
                                        text={name}
                                        highlightPosition={item.highlightPosition
                                            || findPositionInOrder(name, searchValue || '')}
                                    />
                                </span>
                            </div>
                        </Popover.Item>
                    </ItemHover>
                );
            })}
        </>
    );
}

export default PoundPopoverContent;
