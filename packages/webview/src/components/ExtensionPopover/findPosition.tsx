/**
 * 返回 searchValue 包含的全部字符位置
 * @example findPositionInOrder('babc', 'ab') => [0, 1, 2]
 */
export function findPositionDisorder(text: string, searchValue: string | undefined) {
    function findAllPositions(s1: string, s2: string): number[] {
        const result: number[] = [];
        let i = 0;
        while (i < s1.length) {
            const found = s1.indexOf(s2, i);
            if (found === -1) {
                break;
            }
            result.push(found);
            i = found + 1;
        }
        return result;
    }
    return searchValue
        ?.split('')
        .flatMap(v => findAllPositions(text.toLowerCase(), v.toLowerCase()));
}
/**
 * 返回一组 searchValue 字符位置，但顺序必须与 searchValue 相同
 * @example findPositionInOrder('babc', 'ab') => [1, 2]
 */
export function findPositionInOrder(text: string, searchValue: string): number[] {
    const textArr = text.toLowerCase().split('');
    const searchValueArr = searchValue.toLowerCase().split('');
    const result: number[] = [];
    let index = 0;
    for (const char of searchValueArr) {
        while (index < textArr.length) {
            if (textArr[index] === char) {
                result.push(index);
                index++;
                break;
            }
            index++;
        }
    }
    return result;
}
