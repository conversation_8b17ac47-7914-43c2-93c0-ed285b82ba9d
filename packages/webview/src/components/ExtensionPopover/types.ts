import {ReactElement} from 'preact/compat';
import {MutableRef} from 'preact/hooks';
import {ExtensionPopoverType} from '../InputBox/utils/popover';

export interface Item {
    id: string;
    name: string;
    owner?: {
        name?: string;
        displayName?: string;
    };
    disabled?: boolean;
    disabledReason?: string;
    displayName?: string;
    displayNameKey?: string;
    description?: string;
    descriptionKey?: string;
    icon?: string;
    /** 高亮位置，undefined 将根据搜索词进行默认高亮，[] 表示不高亮 */
    highlightPosition?: number[];
    hoverContent?: ReactElement;
    onHover?: () => void;
    onHoverLeave?: () => void;
    operations?: ReactElement;
}

export interface ExtensionPopoverConfig {
    items: Item[];
    refWidth?: number;
    refHeight?: number;
    currentKnowledge?: Array<{ id: string, label: string }>;
    type: ExtensionPopoverType;
    showFileIcon?: boolean;
    title?: string;
    searchValue?: string;
    showPrefix?: boolean;
    onPopoverVisibleChange?: (visible: boolean) => void;
    onSelect: (value: string, type?: 'agent' | 'command') => void;
    selectedKnowledgeType?: string;
}

export interface ExtensionPopoverProps extends ExtensionPopoverConfig {
    currentSelect?: string | undefined;
    setCurrentSelect?: (id?: string) => void;
}

export interface PopoverContentProps extends ExtensionPopoverProps {
    /** hover 弹框用到了， 为啥不写死最大宽度？ */
    popoverWidth?: number;
    selectedRef: MutableRef<HTMLDivElement | null>;
    selectItem: (item: Item) => void;
}
