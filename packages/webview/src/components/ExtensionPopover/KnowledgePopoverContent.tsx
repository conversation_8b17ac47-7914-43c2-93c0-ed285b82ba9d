/* bca-disable */
import {useCallback, useMemo} from 'preact/hooks';
import checkIcon from '@/assets/check.svg';
import Popover from '../Tooltip/Popover';
import {KnowledgeIconMapping} from '../InputBox/KnowledgeIcon';
import {useFileContext} from '../FileContextPopover/hooks/useFileContext';
import FileListItem from '../FileContextPopover/FileListItem';
import {useChatInput} from '../InputBox/hooks/useChatInput';
import {extractMentionsFromMarkupedText} from '../InputBox/utils/mention';
import {HighlightText} from './HighlightText';
import {findPositionInOrder} from './findPosition';
import {Item, PopoverContentProps} from './types';

function Icon({selectedKnowledgeType, icon}: {selectedKnowledgeType?: string, icon?: string}) {
    const builtinIcon = icon ?? (selectedKnowledgeType && KnowledgeIconMapping[selectedKnowledgeType]);
    if (!builtinIcon) {
        return null;
    }
    return (
        <div
            aria-hidden="true"
            dangerouslySetInnerHTML={{__html: builtinIcon}}
            className="w-3 h-3 min-w-3"
        />
    );
}

function FilePopoverContent(props: PopoverContentProps) {
    const {
        items,
        selectItem,
        selectedRef,
        searchValue,
        currentSelect,
    } = props;

    const {selectedFiles: currentSelectedContexts} = useFileContext();

    const clickItemHandler = useCallback(
        (item: Item) => () => selectItem(item),
        [selectItem]
    );

    return (
        <>
            {items.map(item => {
                const {name} = item;
                const isPreselected = currentSelect === item.id;
                const ref = isPreselected ? selectedRef : null;

                return (
                    <FileListItem
                        ref={ref}
                        filePath={item.id}
                        key={item.id}
                        isPreselected={isPreselected}
                        selected={currentSelectedContexts.includes(item.id)}
                        handleSelectFile={clickItemHandler(item)}
                        id={item.id}
                    >
                        <HighlightText
                            text={name}
                            highlightPosition={findPositionInOrder(name, searchValue || '')}
                        />
                    </FileListItem>
                );
            })}
        </>
    );
}

function KnowledgePopoverContent(props: PopoverContentProps) {
    const {
        items,
        selectItem,
        searchValue,
        selectedRef,
        currentSelect,
        currentKnowledge,
        selectedKnowledgeType,
    } = props;
    const isFilePopover = typeof selectedKnowledgeType === 'undefined' || selectedKnowledgeType === 'file';
    const {text} = useChatInput();

    const mentions = useMemo(
        () => extractMentionsFromMarkupedText(text).map(item => {
            const [type, id] = item.id.split(':');
            if (type === 'knowledge') {
                return id.split('/').pop()!;
            }
            return id;
        }),
        [text]
    );

    const clickItemHandler = useCallback(
        (item: Item) => () => selectItem(item),
        [selectItem]
    );

    return (
        <>
            {items.length === 0 && (
                <div className="p-2 text-center">
                    🤕 暂未搜索到相关知识
                </div>
            )}
            {isFilePopover
                ? <FilePopoverContent {...props} />
                : items.map((item, index) => (
                    <Popover.Item
                        tabIndex={index}
                        key={item.id}
                        ref={item.id === currentSelect ? selectedRef : null}
                        id={`input_popover_${item.id}`}
                        role="option"
                        aria-label={item.name}
                        selected={item.id === currentSelect || !!currentKnowledge?.find(({id}) => id === item.id)}
                        disabled={item.disabled}
                        description={item.description}
                        onClick={clickItemHandler(item)}
                        statusIcon={mentions.find(id => id === item.id) && (
                            <div
                                className="w-4 h-4 text-[#17C3E5]"
                                dangerouslySetInnerHTML={{__html: checkIcon}}
                            />
                        )}
                    >
                        <div className="flex items-center gap-1 overflow-hidden">
                            <Icon selectedKnowledgeType={selectedKnowledgeType} icon={item.icon} />
                            <span className="ellipsis">
                                <HighlightText
                                    text={item.name}
                                    highlightPosition={findPositionInOrder(item.name, searchValue || '')}
                                />
                            </span>
                        </div>
                    </Popover.Item>
                ))
            }
        </>
    );
}

export default KnowledgePopoverContent;
