import {FC} from 'preact/compat';

interface Props {
    /** 展示文案 */
    text: string;
    /** 高亮位置 */
    highlightPosition?: number[];
}
export const HighlightText: FC<Props> = ({text, highlightPosition}) => {
    const positions = new Set(highlightPosition);
    return (
        <>
            {text.split('').map((v, index) => {
                const isHighlight = positions.has(index);
                return (
                    // eslint-disable-next-line react/jsx-key
                    <span className={isHighlight ? 'text-[var(--comate-link-color)]' : ''}>
                        {v}
                    </span>
                );
            })}
        </>
    );
};
