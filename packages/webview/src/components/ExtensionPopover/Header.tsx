/* bca-disable */
import {useCallback} from 'preact/hooks';
import iconLeftArrow from '@/assets/leftArrows.svg';
import {useChatInput} from '../InputBox/hooks/useChatInput';
import {ExtensionPopoverType} from '../InputBox/utils/popover';

interface Props {
    title: string;
    type: ExtensionPopoverType;
}

function Header({title, type}: Props) {
    const {onPopoverTypeChange, onKnowledgeTypeSelect} = useChatInput();

    const handleBack = useCallback(
        (e: any) => {
            onKnowledgeTypeSelect(({text}) => ({text, type: undefined}));
            onPopoverTypeChange('pound');
            e.stopPropagation();
        },
        [onPopoverTypeChange, onKnowledgeTypeSelect]
    );

    if (type === 'knowledge') {
        return (
            <div onClick={handleBack} className="flex w-fit items-center gap-1 cursor-pointer">
                <div
                    className="w-4 h-4 shrink-0"
                    dangerouslySetInnerHTML={{__html: iconLeftArrow}}
                />
                <span>{title}</span>
            </div>
        );
    }

    return (
        <span>{title}</span>
    );
}

export default Header;
