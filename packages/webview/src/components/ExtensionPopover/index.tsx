/* eslint-disable complexity, max-len */
import {useCallback, useEffect, useRef, useState} from 'preact/hooks';
import comateLogo from '@/assets/comate.png';
import {useInputBoxSizeToggle} from '@/hooks/useInputBoxSizeToggle';
import {classnames} from '../InputBox/ExtendedArea';
import Popover from '../Tooltip/Popover';
import {ExtensionPopoverProps} from './types';
import Content from './Content';
import './index.css';
import Header from './Header';

function ExtensionPopover(props: ExtensionPopoverProps) {
    const {
        type,
        items,
        title,
        onPopoverVisibleChange,
        onSelect,
        currentSelect,
        setCurrentSelect,
    } = props;
    const popoverRef = useRef<HTMLDivElement>(null);
    const currentSelectRef = useRef(currentSelect);
    currentSelectRef.current = currentSelect;
    const selectedRef = useRef<HTMLDivElement | null>(null);
    const [inputBoxExpanded] = useInputBoxSizeToggle();

    const scrollToSelected = useCallback(
        () => {
            if (selectedRef.current) {
                selectedRef.current.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                });
            }
        },
        []
    );

    const setCurrentSelectWithPosition = useCallback(
        (id: string) => {
            currentSelectRef.current = id;
            setCurrentSelect?.(id);
        },
        [setCurrentSelect]
    );

    const selectItem = useCallback(
        (item: ExtensionPopoverProps['items'][0]) => {
            if (item.disabled) {
                return;
            }

            const avatar = item.icon === '' ? comateLogo : item.icon;
            const icon = item.hasOwnProperty('icon') ? avatar : undefined;
            onPopoverVisibleChange?.(false);
            onSelect(item.id, icon ? 'agent' : 'command');
        },
        [onPopoverVisibleChange, onSelect]
    );

    useEffect(
        () => {
            function handleKeyDown(e: KeyboardEvent) {
                // preventDefault只对于方向键和Enter健有效，Tab需要响应默认行为，例如焦点切换
                const moveToNextItem = (computeNextIndex: (i: number) => number, boundary: number) => {
                    e.preventDefault();
                    const index = items.findIndex(item => item.id === currentSelectRef.current);
                    let nextItem = items[computeNextIndex(index)];
                    // 如果下一个选项是禁用的，就继续往下找，直到找到最后一个位置
                    while (nextItem.disabled && items.at(boundary) !== nextItem) {
                        nextItem = items[computeNextIndex(items.indexOf(nextItem))];
                    }
                    if (!nextItem.disabled) {
                        setCurrentSelectWithPosition(nextItem.id);
                    }
                };
                // NOTE: 是否正在使用输入法
                const isComposing = e.isComposing;
                const len = items.length;
                // 当按下下方向键时，聚焦到下一个选项
                if (
                    (e.key === 'ArrowDown' && !isComposing)
                    || (e.key === 'Tab' && !e.shiftKey && !isComposing)
                    || (e.key === 'n' && e.ctrlKey && !isComposing)
                ) {
                    moveToNextItem(i => (i + 1) % len, items.length - 1);
                    e.stopPropagation();
                }
                // 当按下上方向键时，聚焦到上一个选项
                else if (
                    (e.key === 'ArrowUp' && !isComposing)
                    || (e.key === 'Tab' && e.shiftKey && !isComposing)
                    || (e.key === 'p' && e.ctrlKey && !isComposing)
                ) {
                    moveToNextItem(i => {
                        if (i === -1) {
                            // 未选中时，ctrl+p 选中最后一个选项
                            return len - 1;
                        }
                        return (i - 1 + len) % len;
                    }, 0);
                    e.stopPropagation();
                }
                else if (e.key === 'Enter') {
                    const selectedItem = items.find(item => item.id === currentSelectRef.current);
                    if (selectedItem) {
                        e.preventDefault();
                        selectItem(selectedItem);
                    }
                    e.stopPropagation();
                }
            }
            document.addEventListener('keydown', handleKeyDown);

            return () => {
                document.removeEventListener('keydown', handleKeyDown);
            };
        },
        [items, selectItem, setCurrentSelectWithPosition]
    );

    useEffect(
        () => {
            scrollToSelected();
        },
        [scrollToSelected, currentSelect]
    );

    useEffect(
        () => {
            const closeWhenEscPressed = (event: KeyboardEvent) => {
                if (event.key === 'Escape') {
                    setCurrentSelect?.(undefined);
                    onPopoverVisibleChange?.(false);
                }
            };
            document.addEventListener('keydown', closeWhenEscPressed);
            return () => document.removeEventListener('keydown', closeWhenEscPressed);
        },
        [onPopoverVisibleChange, setCurrentSelect]
    );

    useEffect(
        () => {
            const closeWhenClickOutside = (e: MouseEvent) => {
                const target = e.target as HTMLElement;
                // 点击输入框下的拓展按钮也不要关闭，会走切换逻辑
                if (!popoverRef.current?.contains(target) && !target.closest(`.${classnames.btn}`)) {
                    onPopoverVisibleChange?.(false);
                }
            };
            document.addEventListener('click', closeWhenClickOutside);
            return () => document.removeEventListener('click', closeWhenClickOutside);
        },
        [onPopoverVisibleChange]
    );

    const [width, setWidth] = useState<number>();
    useEffect(
        () => {
            function fn() {
                const width = popoverRef.current?.offsetWidth;
                setWidth(width);
            }
            fn();
            window.addEventListener('resize', fn);
            return () => window.removeEventListener('resize', fn);
        },
        []
    );

    return (
        <Popover.Overlay
            ref={popoverRef}
            role="listbox"
            id="input_popover"
            size="full"
            data-role={inputBoxExpanded ? 'collapsed' : 'expanded'}
            className="absolute z-50 left-0"
            style={{bottom: `calc(100% + ${type === 'agent' ? 38 : 8}px)`}}
            header={title && <Header title={title} type={type} />}
        >
            <Content
                {...props}
                selectItem={selectItem}
                selectedRef={selectedRef}
                popoverWidth={width}
            />
        </Popover.Overlay>
    );
}

export default ExtensionPopover;
