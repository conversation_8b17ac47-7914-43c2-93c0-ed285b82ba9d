import {FC, ReactElement, ReactNode} from 'preact/compat';
import RcTooltip from 'rc-tooltip';
import {TooltipProps} from 'rc-tooltip/lib/Tooltip';
import cx from 'classnames';
import './index.css';

interface ReassignProps {
    children: ReactElement;
    overlay: ReactNode;
    arrowContent?: ReactNode;
    showArrow?: boolean;
    placement?: string;
}

export type Props = Omit<TooltipProps, keyof ReassignProps> & ReassignProps & {
    width?: number;
};
const Tooltip = RcTooltip as FC<Props>;

const getTooltipContainer = (e: HTMLElement) => e.closest('main')!;

export default (props: Props) => {
    const {showArrow = true, placement = 'bottomRight', width, ...restProps} = props; // 设置默认为有箭头，方向top
    if (!props.overlay) {
        return props.children;
    }

    return (
        <Tooltip
            showArrow={showArrow}
            placement={placement}
            getTooltipContainer={getTooltipContainer}
            mouseEnterDelay={0.2}
            mouseLeaveDelay={0.2}
            {...restProps}
            overlayClassName={cx('comate-tooltip', props.overlayClassName)}
            overlayStyle={{
                width: width || '60%',
            }}
        />
    );
};
