/**
 * @file 命令悬浮提示组件
 */

import {FC, ReactElement, useCallback, useEffect, useRef} from 'preact/compat';
import Tooltip from './Tooltip';

interface Props {
    children: any;
    content: ReactElement | undefined;
    width?: number;
    onHover: (() => void) | undefined;
    onHoverLeave: (() => void) | undefined;
}

const ItemHover: FC<Props> = props => {
    // 给我一个弹窗
    const {children, content, width, onHover, onHoverLeave} = props;
    const visibleRef = useRef(false);

    const handleVisibleChange = useCallback(
        (visible: boolean) => {
            visibleRef.current = visible;
            if (visible) {
                onHover?.();
            }
            else {
                onHoverLeave?.();
            }
        },
        [onHover, onHoverLeave]
    );

    useEffect(
        () => {
            // 如果组件整个被释放，可能没时间触发 onHoverLeave 事件，所以增加一个释放时调起的逻辑
            return () => {
                if (visibleRef.current) {
                    onHoverLeave?.();
                }
            };
        },
        [onHoverLeave]
    );

    return (
        <Tooltip
            prefixCls="agent-tooltip"
            overlay={(
                <div>
                    {content}
                </div>
            )}
            onVisibleChange={handleVisibleChange}
            width={width}
        >
            {children}
        </Tooltip>
    );
};

export default ItemHover;
