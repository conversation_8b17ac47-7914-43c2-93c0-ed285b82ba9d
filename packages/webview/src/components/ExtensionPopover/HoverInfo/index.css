.comate-tooltip {
    position: absolute;
    z-index: 9999;
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.15);
}

.agent-tooltip {
    width: 100%;
}
.agent-tooltip-content {
    width: 100%;
}
.agent-tooltip-inner {
    border-radius: 4px;
    background: rgba(59, 59, 61);
    padding: 0;
    border: 0;
    box-shadow: 0px 5px 12px 0px #00000019, 0px 3px 6px 0px #00000019, 0px 1px 2px 0px #00000019;
    /* min-height: 6.25rem; */
    /* width: 60%; */
    min-width: 12rem;
    max-width: 36rem;
}

.agent-tooltip-hidden {
    display: none !important;
}

.tooltip-with-icon .agent-tooltip-inner {
    padding: 8px 12px;
    color: inherit;
    background: var(--comate-editor-background);
}

.tooltip-warning .tooltip-icon {
    color: #F58300;
}

.tooltip-with-icon .tooltip-icon {
    width: 16px;
    height: 16px;
}

/* ==== 以下为复制过来的 tooltip 箭头，需要实现4个方向 ====*/
.agent-tooltip-arrow,
.agent-tooltip-arrow-inner {
    display: none;
    /* position: absolute; */
    /* width: 0; */
    /* height: 0; */
    /* border-color: transparent; */
    /* border-style: solid; */
}

.agent-tooltip-placement-top .agent-tooltip-arrow,
.agent-tooltip-placement-topLeft .agent-tooltip-arrow,
.agent-tooltip-placement-topRight .agent-tooltip-arrow {
    transform: translate(-50%, 5px);
    border-width: 6px 6px 0;
    border-top-color: var(--comate-descriptionForeground);
}

.agent-tooltip-placement-top .agent-tooltip-arrow-inner,
.agent-tooltip-placement-topLeft .agent-tooltip-arrow-inner,
.agent-tooltip-placement-topRight .agent-tooltip-arrow-inner {
    bottom: 1px;
    margin-left: -6px;
    border-width: 6px 6px 0;
    border-top-color: #fff;
}

.agent-tooltip-placement-top .agent-tooltip-arrow {
    left: 50%;
}

.agent-tooltip-placement-topLeft .agent-tooltip-arrow {
    left: 15%;
}

.agent-tooltip-placement-topRight .agent-tooltip-arrow {
    right: 15%;
}

.agent-tooltip-placement-right .agent-tooltip-arrow,
.agent-tooltip-placement-rightTop .agent-tooltip-arrow,
.agent-tooltip-placement-rightBottom .agent-tooltip-arrow {
    left: -5px;
    margin-top: -6px;
    border-width: 6px 6px 6px 0;
    border-right-color: var(--comate-descriptionForeground);
    transform: translate(calc(-100% + 1px));
}

.agent-tooltip-placement-right .agent-tooltip-arrow-inner,
.agent-tooltip-placement-rightTop .agent-tooltip-arrow-inner,
.agent-tooltip-placement-rightBottom .agent-tooltip-arrow-inner {
    left: 1px;
    margin-top: -6px;
    border-width: 6px 6px 6px 0;
    border-right-color: #fff;
}

.agent-tooltip-placement-right .agent-tooltip-arrow {
    top: 50%;
}

.agent-tooltip-placement-rightTop .agent-tooltip-arrow {
    top: 15%;
    margin-top: 0;
}

.agent-tooltip-placement-rightBottom .agent-tooltip-arrow {
    bottom: 15%;
}

.agent-tooltip-placement-left .agent-tooltip-arrow,
.agent-tooltip-placement-leftTop .agent-tooltip-arrow,
.agent-tooltip-placement-leftBottom .agent-tooltip-arrow {
    right: -5px;
    margin-top: -6px;
    border-width: 6px 0 6px 6px;
    border-left-color: var(--comate-descriptionForeground);
    transform: translate(calc(100% - 1px));
}

.agent-tooltip-placement-left .agent-tooltip-arrow-inner,
.agent-tooltip-placement-leftTop .agent-tooltip-arrow-inner,
.agent-tooltip-placement-leftBottom .agent-tooltip-arrow-inner {
    right: 1px;
    margin-top: -6px;
    border-width: 6px 0 6px 6px;
    border-left-color: #fff;
}

.agent-tooltip-placement-left .agent-tooltip-arrow {
    top: 50%;
}

.agent-tooltip-placement-leftTop .agent-tooltip-arrow {
    top: 15%;
    margin-top: 0;
}

.agent-tooltip-placement-leftBottom .agent-tooltip-arrow {
    bottom: 15%;
}

.agent-tooltip-placement-bottom .agent-tooltip-arrow,
.agent-tooltip-placement-bottomLeft .agent-tooltip-arrow,
.agent-tooltip-placement-bottomRight .agent-tooltip-arrow {
    transform: translate(-50%, -5px);
    border-width: 0 6px 6px;
    border-bottom-color: var(--comate-descriptionForeground);
    ;
}

.agent-tooltip-placement-bottom .agent-tooltip-arrow-inner,
.agent-tooltip-placement-bottomLeft .agent-tooltip-arrow-inner,
.agent-tooltip-placement-bottomRight .agent-tooltip-arrow-inner {
    top: 1px;
    margin-left: -6px;
    border-width: 0 6px 6px;
    border-bottom-color: #fff;
}

.agent-tooltip-placement-bottom .agent-tooltip-arrow {
    left: 50%;
}

.agent-tooltip-placement-bottomLeft .agent-tooltip-arrow {
    left: 15%;
}

.agent-tooltip-placement-bottomRight .agent-tooltip-arrow {
    right: 15%;
}
