/* eslint-disable complexity */
/* bca-disable */
import {useTranslation} from 'react-i18next';
import classNames from 'classnames';
import {UT} from '@shared/constants';
import {BuiltinAgent} from '@shared/protocols';
import {PROMPT_TEMPLATE_AGENT} from '@shared/agents';
import {CommonText} from '@/i18n/constants';
import comateLogo from '@/assets/comate.png';
import FileIcon from '../FileIcon';
import EllipsisText from '../EllipsisText';
import {SmartAgentIcon} from '../SmartAgentIcon';
import {useAgentContext} from '../SmartAgent/AgentProvider';
import Popover from '../Tooltip/Popover';
import {HighlightText} from './HighlightText';
import {findPositionInOrder} from './findPosition';
import ItemHover from './HoverInfo';
import './index.css';
import {PopoverContentProps} from './types';
import PoundPopoverContent from './PoundPopoverContent';
import KnowledgePopoverContent from './KnowledgePopoverContent';

const perfixMap = {
    agent: '',
    command: '/',
    knowledge: '#',
};

const Icon = ({icon, showFileIcon, filename}) => {
    if (icon) {
        if (icon.startsWith('<svg')) {
            return <div aria-hidden="true" dangerouslySetInnerHTML={{__html: icon}} className="w-[14px]" />;
        }
        else {
            return <img aria-hidden="true" src={icon} className="w-4" />;
        }
    }
    else if (showFileIcon) {
        return <FileIcon filename={filename} className="w-3 h-3 min-w-3 max-w-3" />;
    }
    return null;
};

export function PopoverContent(props: PopoverContentProps) {
    const {
        type,
        items,
        selectItem,
        searchValue,
        selectedRef,
        showFileIcon,
        currentSelect,
        currentKnowledge,
        popoverWidth,
        showPrefix = true,
    } = props;
    const {t} = useTranslation();
    const {agentStatus} = useAgentContext();

    return (
        <>
            {items
                // 兼容三方插件定制需求 有些插件(比如飞桨)存在不希望展示名称的能力
                // 过滤掉名字为空的能力选项
                .filter(item => item.id !== 'chat@paddle')
                .map(item => {
                    const index = items.indexOf(item);
                    const isActive = currentSelect === item.id;
                    const isSelected = currentKnowledge?.find(({id}) => id === item.id);
                    const isDisabled = !!item.disabled;
                    const isAgentWithSlashPrefix = item.icon && (type === 'command');
                    const disableTooltips = !item.hoverContent;

                    const icon = item.hasOwnProperty('icon') ? (item.icon === '' ? comateLogo : item.icon) : undefined;
                    const name = item.displayNameKey ? t(item.displayNameKey) : (item.displayName ?? item.name);
                    const showSmartAgentBadge = item.id === BuiltinAgent.AutoWork;
                    const showSecuAgentBadge = item.name === 'codeScan'
                        && agentStatus.enableSecurityIntelligence === true;
                    const commonProps = {
                        ref: isActive ? selectedRef : undefined,
                        disabled: isDisabled,
                        selected: isSelected || isActive,
                        className: 'group',
                        description: (
                            <span
                                className={classNames(
                                    'text-xs overflow-hidden comate-extension-popover-intro',
                                    item.operations && 'hover:shrink-0'
                                )}
                            >
                                {isDisabled
                                    ? <EllipsisText value={item.disabledReason} />
                                    : (
                                        <p
                                            className={classNames({'group-hover:hidden': item.operations})}
                                        >
                                            {item.owner && Boolean(item.owner.displayName) && (
                                                <EllipsisText
                                                    value={`@${item.owner.displayName}`}
                                                    disableTooltips={disableTooltips}
                                                />
                                            )}
                                            {isSelected && t(CommonText.SELECTED)}
                                        </p>
                                    )}
                                {item.operations && (
                                    <span className="hidden items-center gap-2 group-hover:flex justify-end">
                                        {item.operations}
                                    </span>
                                )}
                            </span>
                        ),
                        onClick: () => selectItem(item),
                    };

                    const content = (
                        <>
                            <span
                                className={classNames(
                                    'flex items-center gap-1',
                                    item.operations ? 'overflow-hidden' : 'shrink-0'
                                )}
                            >
                                <Icon icon={icon} showFileIcon={showFileIcon} filename={name} />
                                <p
                                    title={disableTooltips ? undefined : name}
                                    className="inline items-center"
                                    style={isSelected ? {overflow: 'hidden'} : undefined}
                                >
                                    <div className="inline-flex items-center">
                                        {!isAgentWithSlashPrefix && showPrefix && perfixMap[type] && (
                                            <span className="text-[var(--comate-link-color)]">
                                                {perfixMap[type]}
                                            </span>
                                        )}
                                        <HighlightText
                                            text={name}
                                            highlightPosition={item.highlightPosition
                                                || findPositionInOrder(name, searchValue || '')}
                                        />
                                        {/* 自定义指令不会展示智能体图标 */}
                                        {item?.owner?.name !== PROMPT_TEMPLATE_AGENT.name && (
                                            showSmartAgentBadge || item.name === UT || showSecuAgentBadge
                                        ) && (
                                            <SmartAgentIcon className="ml-2" />
                                        )}
                                    </div>
                                </p>
                            </span>
                        </>
                    );

                    return disableTooltips
                        ? (
                            <Popover.Item
                                key={item.id}
                                {...commonProps}
                                tabIndex={index}
                                id={`input_popover_${item.id}`}
                                role="option"
                                aria-label={name}
                                selected={isActive}
                            >
                                {content}
                            </Popover.Item>
                        )
                        : (
                            <ItemHover
                                key={item.id}
                                content={item.hoverContent}
                                onHover={item.onHover}
                                onHoverLeave={item.onHoverLeave}
                                width={popoverWidth ? popoverWidth * 0.6 : undefined}
                            >
                                <Popover.Item
                                    {...commonProps}
                                    elementRef={commonProps.ref}
                                    tabIndex={index}
                                    id={`input_popover_${item.id}`}
                                    role="option"
                                    aria-label={name}
                                    selected={isActive}
                                >
                                    {content}
                                </Popover.Item>
                            </ItemHover>
                        );
                })}
        </>
    );
}

function Content(props: PopoverContentProps) {
    if (props.type === 'pound') {
        return <PoundPopoverContent {...props} />;
    }

    if (props.type === 'knowledge') {
        return <KnowledgePopoverContent {...props} />;
    }

    // 剩下 agent、command、smart 类型的弹框内容没拆出来，后面页面大改时再弄吧
    return <PopoverContent {...props} />;
}

export default Content;
