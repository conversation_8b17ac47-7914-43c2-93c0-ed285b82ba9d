.model-selector-tooltip {
    position: absolute;
}
.model-selector-tooltip.rc-tooltip.rc-tooltip-zoom-appear,
.model-selector-tooltip.rc-tooltip.rc-tooltip-zoom-enter {
  opacity: 0;
}
.model-selector-tooltip.rc-tooltip.rc-tooltip-zoom-enter,
.model-selector-tooltip.rc-tooltip.rc-tooltip-zoom-leave {
  display: block;
}
.model-selector-tooltip .rc-tooltip-zoom-enter,
.model-selector-tooltip .rc-tooltip-zoom-appear {
  opacity: 0;
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.18, 0.89, 0.32, 1.28);
  animation-play-state: paused;
}
.model-selector-tooltip .rc-tooltip-zoom-leave {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.6, -0.3, 0.74, 0.05);
  animation-play-state: paused;
}
.model-selector-tooltip .rc-tooltip-zoom-enter.rc-tooltip-zoom-enter-active,
.model-selector-tooltip .rc-tooltip-zoom-appear.rc-tooltip-zoom-appear-active {
  animation-name: rcToolTipZoomIn;
  animation-play-state: running;
}
.model-selector-tooltip .rc-tooltip-zoom-leave.rc-tooltip-zoom-leave-active {
  animation-name: rcToolTipZoomOut;
  animation-play-state: running;
}
@keyframes rcToolTipZoomIn {
  0% {
    opacity: 0;
    transform-origin: 50% 50%;
    transform: scale(0, 0);
  }
  100% {
    opacity: 1;
    transform-origin: 50% 50%;
    transform: scale(1, 1);
  }
}
@keyframes rcToolTipZoomOut {
  0% {
    opacity: 1;
    transform-origin: 50% 50%;
    transform: scale(1, 1);
  }
  100% {
    opacity: 0;
    transform-origin: 50% 50%;
    transform: scale(0, 0);
  }
}
.model-selector-tooltip.rc-tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  visibility: visible;
  font-size: 12px;
  line-height: 1.5;
}
.model-selector-tooltip.rc-tooltip-hidden {
  display: none;
}
.model-selector-tooltip .rc-tooltip-placement-top,
.model-selector-tooltip .rc-tooltip-placement-topLeft,
.model-selector-tooltip .rc-tooltip-placement-topRight {
  padding: 5px 0 9px 0;
}
.model-selector-tooltip .rc-tooltip-placement-right,
.model-selector-tooltip .rc-tooltip-placement-rightTop,
.model-selector-tooltip .rc-tooltip-placement-rightBottom {
  padding: 0 5px 0 9px;
}
.model-selector-tooltip .rc-tooltip-placement-bottom,
.model-selector-tooltip .rc-tooltip-placement-bottomLeft,
.model-selector-tooltip .rc-tooltip-placement-bottomRight {
  padding: 9px 0 5px 0;
}
.model-selector-tooltip .rc-tooltip-placement-left,
.model-selector-tooltip .rc-tooltip-placement-leftTop,
.model-selector-tooltip .rc-tooltip-placement-leftBottom {
  padding: 0 9px 0 5px;
}
.model-selector-tooltip .rc-tooltip-inner {
  padding: 0px;
  color: #fff;
  text-align: left;
  text-decoration: none;
  background-color: #3B3D42;
  border-radius: none;
  box-shadow: none;
  min-height: 34px;
  border: none;
}
.model-selector-tooltip .rc-tooltip-arrow {
  display: none;
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.model-selector-tooltip .rc-tooltip-placement-top .rc-tooltip-arrow,
.model-selector-tooltip .rc-tooltip-placement-topLeft .rc-tooltip-arrow,
.model-selector-tooltip .rc-tooltip-placement-topRight .rc-tooltip-arrow {
  bottom: 4px;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #373737;
}
.model-selector-tooltip .rc-tooltip-placement-top .rc-tooltip-arrow {
  left: 50%;
}
.model-selector-tooltip .rc-tooltip-placement-topLeft .rc-tooltip-arrow {
  left: 15%;
}
.model-selector-tooltip .rc-tooltip-placement-topRight .rc-tooltip-arrow {
  right: 15%;
}
.model-selector-tooltip .rc-tooltip-placement-right .rc-tooltip-arrow,
.model-selector-tooltip .rc-tooltip-placement-rightTop .rc-tooltip-arrow,
.model-selector-tooltip .rc-tooltip-placement-rightBottom .rc-tooltip-arrow {
  left: 4px;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #373737;
}
.model-selector-tooltip .rc-tooltip-placement-right .rc-tooltip-arrow {
  top: 50%;
}
.model-selector-tooltip .rc-tooltip-placement-rightTop .rc-tooltip-arrow {
  top: 15%;
  margin-top: 0;
}
.model-selector-tooltip .rc-tooltip-placement-rightBottom .rc-tooltip-arrow {
  bottom: 15%;
}
.model-selector-tooltip .rc-tooltip-placement-left .rc-tooltip-arrow,
.model-selector-tooltip .rc-tooltip-placement-leftTop .rc-tooltip-arrow,
.model-selector-tooltip .rc-tooltip-placement-leftBottom .rc-tooltip-arrow {
  right: 4px;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #373737;
}
.model-selector-tooltip .rc-tooltip-placement-left .rc-tooltip-arrow {
  top: 50%;
}
.model-selector-tooltip .rc-tooltip-placement-leftTop .rc-tooltip-arrow {
  top: 15%;
  margin-top: 0;
}
.model-selector-tooltip .rc-tooltip-placement-leftBottom .rc-tooltip-arrow {
  bottom: 15%;
}
.model-selector-tooltip .rc-tooltip-placement-bottom .rc-tooltip-arrow,
.model-selector-tooltip .rc-tooltip-placement-bottomLeft .rc-tooltip-arrow,
.model-selector-tooltip .rc-tooltip-placement-bottomRight .rc-tooltip-arrow {
  top: 4px;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #373737;
}
.model-selector-tooltip .rc-tooltip-placement-bottom .rc-tooltip-arrow {
  left: 50%;
}
.model-selector-tooltip .rc-tooltip-placement-bottomLeft .rc-tooltip-arrow {
  left: 15%;
}
.model-selector-tooltip .rc-tooltip-placement-bottomRight .rc-tooltip-arrow {
  right: 15%;
}
