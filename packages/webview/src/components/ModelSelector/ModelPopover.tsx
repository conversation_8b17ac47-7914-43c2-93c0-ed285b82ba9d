import {ModelRes} from '@/api';
import Popover from '../Tooltip/Popover';

const descMap = {
    'ernie-4.0-turbo-128k': '更强',
    'ernie-speed-128k': '更快',
    'ernie-3.5-128k': '均衡',
};

interface Props {
    modelList: ModelRes[];
    changeModel: (id: string) => void;
    activeModel: string;
}

export default function ModelPopover({
    modelList,
    changeModel,
    activeModel,
}: Props) {
    return (
        <Popover.Overlay
            role="listbox"
            id="popover"
            aria-label="选择模型列表"
        >
            {modelList.map(v => (
                <Popover.Item
                    className="justify-between"
                    // eslint-disable-next-line react/jsx-no-bind
                    onClick={() => changeModel(v.modelId)}
                    selected={activeModel === v.modelId}
                    key={v.modelId}
                    role="option"
                    aria-selected={activeModel === v.modelId}
                    id={`popover_${v.modelId}`}
                >
                    <span className="text-xs">{v.displayName}</span>
                    {descMap[v.modelId] && <Popover.Tag>{descMap[v.modelId]}</Popover.Tag>}
                </Popover.Item>
            ))}
        </Popover.Overlay>
    );
}
