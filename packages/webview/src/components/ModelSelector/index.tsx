/* eslint-disable max-len */
/* bca-disable */
import RcTooltip from 'rc-tooltip';
import {TooltipProps} from 'rc-tooltip/lib/Tooltip';
import {FC, ReactElement, ReactNode, useMemo} from 'preact/compat';
import {EventMessage} from '@shared/protocols';
import {useEffect, useState, useCallback} from 'preact/hooks';
import {modelListApi, ModelRes} from '@/api';
import {useExtensionConfig} from '@/hooks/useExtensionConfig';
import {isInternal} from '@/utils/features';
import {messageHandler} from '@/utils/messageHandler';
import rightIcon from '@/assets/right.svg';
import ModelPopover from './ModelPopover';
import './popover.css';

interface ReassignProps {
    children: ReactElement;
    overlay: ReactNode;
    arrowContent?: ReactNode;
}

type Props = Omit<TooltipProps, keyof ReassignProps> & ReassignProps;
const Tooltip = RcTooltip as FC<Props>;
const getTooltipContainer = (e: HTMLElement) => e.parentNode as HTMLElement;

interface ModelSelectorProps {
    username: string;
    focusCount?: number;
}

export default function ModelSelector({username, focusCount}: ModelSelectorProps) {
    const {config} = useExtensionConfig();
    const [modelList, setModelList] = useState<ModelRes[]>([{
        modelId: 'ernie-3.5-128k',
        modelName: 'ernie-3.5-128k',
        displayName: 'ERNIE-3.5-128k',
        isDefault: true,
    }]);
    const [defaultModel, setDefaultModel] = useState('');
    const [visible, setVisible] = useState(false);
    const modelDisplayName = useMemo(
        () => modelList.find(v => v.modelId === defaultModel)?.displayName || '',
        [
            defaultModel,
            modelList,
        ]
    );

    const getDefaultSelect = useCallback(
        async (newList: any) => {
            const res = await messageHandler.send(
                EventMessage.DefaultModelSelectEvent,
                newList
            );
            if (res.chat) {
                setDefaultModel(prev => prev || res.chat!);
            }
        },
        []
    );

    const fetchData = useCallback(
        async (key: string) => {
            try {
                const res = await modelListApi({key, type: 'CHAT'});
                if (res) {
                    setModelList(res);
                }
                getDefaultSelect(res);
            }
            catch {
                //
            }
        },
        [getDefaultSelect]
    );

    const changeModel = useCallback(
        async (id: string) => {
            {
                await messageHandler.send(
                    EventMessage.UpdateDefaultModelSelectEvent,
                    {
                        id,
                        type: 'CHAT',
                    }
                );
                setDefaultModel(id);
                setVisible(false);
            }
        },
        []
    );

    const handleVisibleChange = useCallback(
        newVisible => {
            setVisible(newVisible);
        },
        []
    );

    useEffect(
        () => {
            if (isInternal && username) {
                fetchData(username);
            }
            else if (config.license) {
                fetchData(config.license);
            }
        },
        [config.license, fetchData, focusCount, username]
    );

    if (!modelDisplayName) {
        return null;
    }

    return (
        <Tooltip
            visible={visible}
            onVisibleChange={handleVisibleChange}
            trigger="click"
            placement="topRight"
            overlayClassName="model-selector-tooltip"
            getTooltipContainer={getTooltipContainer}
            align={{
                offset: [0, -8], // 设置偏移量 [水平偏移, 垂直偏移]
            }}
            overlay={
                <ModelPopover
                    modelList={modelList}
                    changeModel={changeModel}
                    activeModel={defaultModel}
                />
            }
        >
            <div
                className="flex items-center cursor-pointer gap-1 select-none max-w-[50%]"
                aria-label={`${visible ? '收起' : `切换模型按钮，当前模型${modelDisplayName}，点击可更换大模型`}`}
                role="button"
                aria-expanded={visible}
                aria-haspopup="listbox"
                aria-controls="popover"
                tabIndex={0}
                style={{outline: 'none'}}
                // 当弹窗打开时,设置 aria-activedescendant 为第一个选项
                aria-activedescendant={visible ? `popover_${defaultModel}` : undefined}
            >
                <div className="ellipsis" aria-hidden="true">{modelDisplayName}</div>
                {visible
                    ? <div className="w-3.5 h-3.5 -rotate-90" aria-hidden="true" dangerouslySetInnerHTML={{__html: rightIcon}} />
                    : <div className="w-3.5 h-3.5 rotate-90" aria-hidden="true" dangerouslySetInnerHTML={{__html: rightIcon}} />}
            </div>
        </Tooltip>
    );
}
