/* bca-disable */
import {getIcon} from 'material-file-icons';
import {CSSProperties} from 'preact/compat';

interface Props {
    filename: string;
    style?: CSSProperties;
    className?: string;
}

function FileIcon({filename, style, className}: Props) {
    return (
        <div
            style={style}
            className={className}
            aria-hidden="true"
            dangerouslySetInnerHTML={{__html: getIcon(filename).svg}}
        />
    );
}

export default FileIcon;
