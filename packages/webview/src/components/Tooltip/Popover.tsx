import cx from 'classnames';
import {HTMLAttributes, ReactNode, forwardRef} from 'preact/compat';
import {pickBy} from 'lodash';
import {ComponentChildren} from 'preact';
import {MutableRef} from 'preact/hooks';
import './Popover.css';

type Size = 'middle' | 'full';
type Props = Omit<HTMLAttributes<HTMLDivElement>, 'size' | 'ref'>;
const Overlay = forwardRef<HTMLDivElement, Props & {size?: Size, footer?: ReactNode, header?: ReactNode}>(
    function Overlay({className, header, footer, size = 'middle' as const, children, ...props}, ref) {
        return (
            <div {...props} ref={ref} className={cx('popover-overlay', `popover-overlay-${size}`, 'z-100', className)}>
                {header && (
                    <div data-role="header">
                        {header}
                    </div>
                )}
                <div data-role="scrollable">
                    {children}
                </div>
                {footer && (
                    <div data-role="footer">
                        {footer}
                    </div>
                )}
            </div>
        );
    }
);

function Tag(props: Props) {
    return <div {...props} data-role="tag" />;
}

export interface PopoverItemProps {
    disabled?: boolean;
    selected?: boolean;
    /** 这是一个trick prop, 因为在插件popover里，tooltip把children的ref劫持了，所以需要一个别名 */
    elementRef?: MutableRef<HTMLDivElement | null>;
    description?: ComponentChildren;
    statusIcon?: ComponentChildren;
    descriptionClassName?: string;
}

const Item = forwardRef<HTMLDivElement, Props & PopoverItemProps>(
    function Item({disabled, selected, description, children, className, statusIcon, elementRef, ...props}, ref) {
        const mergedRef = elementRef || ref;
        const ariaProps: HTMLAttributes<HTMLDivElement> = pickBy({
            'aria-disabled': disabled,
            'aria-selected': selected,
        }, value => !!value);

        if (description || statusIcon) {
            return (
                <div {...props} ref={mergedRef} className={className} data-role="item" {...ariaProps}>
                    <div data-role="item-title" aria-hidden="true">
                        {children}
                    </div>
                    <div data-role="item-description" aria-hidden="true">
                        {description}
                    </div>
                    {statusIcon && (
                        <div data-role="item-status-icon" aria-hidden="true">
                            {statusIcon}
                        </div>
                    )}
                </div>
            );
        }
        return <div {...props} ref={mergedRef} className={className} data-role="item" {...ariaProps}>{children}</div>;
    }
);

export default {Overlay, Tag, Item};
