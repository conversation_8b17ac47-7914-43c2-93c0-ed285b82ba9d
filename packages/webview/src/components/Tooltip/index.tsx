import {FC, ReactElement, ReactNode} from 'preact/compat';
import RcTooltip from 'rc-tooltip';
import {TooltipProps} from 'rc-tooltip/lib/Tooltip';
import cx from 'classnames';
import './index.css';

interface ReassignProps {
    children: ReactElement;
    overlay: ReactNode;
    arrowContent?: ReactNode;
    showArrow?: boolean;
    placement?: string;
    getTooltipContainer?: (node: HTMLElement) => HTMLElement;
}

export type Props = Omit<TooltipProps, keyof ReassignProps> & ReassignProps;
const Tooltip = RcTooltip as FC<Props>;

const defaultGetTooltipContainer = (e: HTMLElement) => e.closest('main')!;

export default ({getTooltipContainer = defaultGetTooltipContainer, ...props}: Props) => {
    const {showArrow = true, placement = 'top', ...restProps} = props; // 设置默认为有箭头，方向top
    if (!props.overlay) {
        return props.children;
    }

    return (
        <Tooltip
            showArrow={showArrow}
            placement={placement}
            getTooltipContainer={getTooltipContainer}
            mouseLeaveDelay={0.2}
            {...restProps}
            overlayClassName={cx('comate-tooltip', props.overlayClassName)}
        />
    );
};
