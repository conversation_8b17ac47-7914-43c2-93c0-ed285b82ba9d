import {ReactNode, useEffect, useRef, useState} from 'preact/compat';

interface CountdownProps {
    milesecond: number;
    children: (n: number) => ReactNode;
    onCountdown?: (n: number) => void;
}
export function Countdown({milesecond, onCountdown, children}: CountdownProps) {
    const intervalRef = useRef<number>(0);
    const [current, setMilesecond] = useState(milesecond);
    useEffect(
        () => {
            setMilesecond(milesecond);
            let next = milesecond;
            window.clearInterval(intervalRef.current);
            intervalRef.current = window.setInterval(
                () => {
                    next = Math.max(0, next - 1000);
                    if (next === 0) {
                        window.clearInterval(intervalRef.current);
                    }
                    setMilesecond(next);
                    onCountdown?.(next);
                },
                1000
            );
            return () => window.clearInterval(intervalRef.current);
        },
        [milesecond, onCountdown]
    );
    return <>{children(current)}</>;
}
