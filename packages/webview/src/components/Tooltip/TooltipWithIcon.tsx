/* bca-disable */
import {useCallback, useMemo} from 'preact/compat';
import confirmIcon from '@/assets/confirm.svg';
import iconClose from '@/assets/close.svg';
import {useDerivedState} from '../Tabs/useDerivedState';
import ToolbarButton from '../ToolbarButton';
import Tooltip, {Props} from '.';
import {Countdown} from './Countdown';

interface TooltipWithIconProps extends Props {
    type: 'warning';
    countdown: number;
}

export const TooltipWithIcon = ({
    overlay,
    visible,
    type,
    onVisibleChange,
    countdown,
    ...props
}: TooltipWithIconProps) => {
    const [internalVisible, setInternalVisible] = useDerivedState(visible);
    const closeTooltip = useCallback(
        () => {
            setInternalVisible(false);
            onVisibleChange?.(false);
        },
        [onVisibleChange, setInternalVisible]
    );

    const onCountdown = useCallback(
        (num: number) => {
            if (num === 0) {
                closeTooltip();
            }
        },
        [closeTooltip]
    );

    const mergedOverlay = useMemo(
        () => {
            return (
                <div className="flex items-center">
                    <div className="tooltip-icon" dangerouslySetInnerHTML={{__html: confirmIcon}}></div>
                    <div className="tooltip-content mr-5 ml-1">
                        {overlay}
                    </div>
                    <Countdown milesecond={countdown * 1000} onCountdown={onCountdown}>
                        {(num: number) => {
                            return num === 0
                                ? null
                                : (
                                    <span className="mr-1 text-[var(--comate-button-secondaryForeground)]">
                                        {Math.floor(num / 1000)}s
                                    </span>
                                );
                        }}
                    </Countdown>
                    <ToolbarButton icon={iconClose} onClick={closeTooltip} ariaTitle="关闭工具栏"/>
                </div>
            );
        },
        [closeTooltip, countdown, onCountdown, overlay]
    );

    return (
        <Tooltip
            {...props}
            showArrow
            destroyTooltipOnHide
            overlay={mergedOverlay}
            overlayClassName={`tooltip-with-icon tooltip-${type}`}
            visible={internalVisible}
        />
    );
};
