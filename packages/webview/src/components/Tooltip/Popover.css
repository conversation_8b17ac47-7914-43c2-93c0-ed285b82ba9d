.vscode-dark,
body.dark {
    --popover-text-color: #b4b5b8;
    --popover-description-color: #D4D4D480;
    --popover-ovrlay-background: linear-gradient(110.55deg, rgba(53, 59, 59, 0.99) 9.07%, rgba(45, 45, 47, 0.97) 38.57%);
    --popover-border-color: #454545;
    --popover-box-shadow: 0px 0px 30px 0px #0000004D;
    --popover-item-hover-background: linear-gradient(90deg, rgba(219, 233, 255, 0.1) 0%, rgba(131, 140, 153, 0.05) 100%);
}

.vscode-light,
body.light {
    --popover-text-color: #131313;
    --popover-description-color: #00000080;
    --popover-ovrlay-background: white;
    --popover-border-color: #C8C8C8;
    --popover-box-shadow: 0px 0px 30px 0px #0000004D;
    --popover-item-hover-background: linear-gradient(90deg, rgba(66, 66, 66, 0.1) 0%, rgba(139, 139, 139, 0.0611765) 100%);
}

.popover-overlay {
    --max-height: 316px;
    --padding-x: 8px;
    position: relative;
    color: var(--popover-text-color);
    padding: var(--padding-x) 0;
    border: 1px solid var(--popover-border-color);
    border-radius: 4px;
    background: var(--popover-ovrlay-background);
    box-shadow: var(--popover-box-shadow);
}

.popover-overlay[data-role="collapsed"] {
    --max-height: 194px;
}

.popover-overlay[data-role="collapsed"]:has([data-role="footer"]) {
    --max-height: 160px;
}

.popover-overlay:before {
    content: '';
    background: linear-gradient(132.36deg, rgba(23, 195, 229, 0.08) 0%, rgba(23, 195, 229, 0) 48.17%);
    width: 170px;
    height: 98px;
    max-height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    pointer-events: none;
}

.popover-overlay [data-role="scrollable"] {
    overflow: auto;
    padding: 0 8px;
    max-height: calc(var(--max-height) - 16px);
}

.popover-overlay-middle {
    width: 268px;
}

.popover-overlay-full {
    width: 100%;
}

.popover-overlay [data-role="tag"] {
    padding: 0 4px;
    border-radius: 4px;
    height: 18px;
    background: rgba(255, 255, 255, 0.1);
}

.popover-overlay [data-role="header"] {
    font-size: 14px;
    line-height: 18px;
    padding: 4px 12px;
    margin-bottom: 4px;
}

.popover-overlay [data-role="footer"] {
    border-top: 1px solid var(--popover-border-color);
    height: 40px;
    padding: 4px 8px;
    margin-bottom: -8px;
    margin-top: 8px;
    display: flex;
    align-items: center;
}

.popover-overlay [data-role="item"] {
    padding: 0 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 26px;
    border-radius: 4px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 12px;
    gap: 12px;
}

.popover-overlay [data-role="item-title"] {
    /*
        flex: 2 1 auto 的含义：
        - flex-grow: 2   (如果还有多余空间，title 比 description 多分一点)
        - flex-shrink: 1 (如果空间不够，可以被压缩)
        - flex-basis: auto (初始大小由内容决定)

        这样当容器宽度足够时，.title 显示内容的原本大小；
        当容器变窄时，会和 .description 按一定比例一起“压缩”。
    */
    flex: 2 0 auto;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.popover-overlay [data-role="item-description"] {
    /* 同理：flex-grow: 1, flex-shrink: 1, flex-basis: auto */
    flex: 1 1 auto;
    color: var(--popover-description-color);
    text-align: end;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

@media screen and (max-width: 280px) {
    .popover-overlay [data-role="item-title"] {
        flex: 1;
    }

    .popover-overlay [data-role="item-description"] {
        display: none;
    }
}


.popover-overlay [data-role="item-description"]:has([data-role="ltr-label"]) {
    text-align: start;
    direction: rtl;
}

.popover-overlay [data-role="item-description"] [data-role="ltr-label"] {
    direction: ltr;
    unicode-bidi: embed;
}

.popover-overlay [data-role="item-status-icon"] {
    color: var(--popover-description-color);
    margin-left: 4px;
    flex-shrink: 0;
}

.popover-overlay [data-role="item-description"] + [data-role="item-status-icon"] {
    /* 应该item直接有gap, 当有description时，icon需要贴着前面，重置掉margin */
    margin-left: -10px;
}

.popover-overlay [data-role="item"] + [data-role="item"] {
    margin-top: 4px;
}

.popover-overlay [data-role="item-wrapper"] + [data-role="item-wrapper"] {
    margin-top: 4px;
}

.popover-overlay [data-role="item"]:not([aria-disabled="true"]) {
    cursor: pointer;
}

.popover-overlay [data-role="item"][aria-disabled="true"] {
    color: var(--comate-disabled-iconColor);
    cursor: not-allowed;
}

.popover-overlay [data-role="item"]:not([aria-disabled="true"]):hover,
.popover-overlay [data-role="item"][aria-selected="true"] {
    background: var(--popover-item-hover-background);
}
