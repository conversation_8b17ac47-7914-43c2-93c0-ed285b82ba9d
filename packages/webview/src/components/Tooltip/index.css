.comate-tooltip {
    position: absolute;
    z-index: 9999;
    box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.15);
}

.rc-tooltip-inner {
    min-width: 32px;
    max-width: max-content;
    padding: 4px 8px;
    color: var(--comate-descriptionForeground);
    text-align: start;
    text-decoration: none;
    word-wrap: break-word;
    background-color:var(--comate-editor-background);
    border-radius: 4px;
    font-size: 12px;
    box-sizing: border-box;
    border: 1px solid var(--comate-tooltip-border);
    background-color: var(--comate-tooltip-background);
}

.rc-tooltip-hidden {
    display: none;
}

.tooltip-with-icon .rc-tooltip-inner {
    padding: 8px 12px;
    color: inherit;
    background: var(--comate-editor-background);
}

.tooltip-warning .tooltip-icon {
    color: #F58300;
}

.tooltip-with-icon .tooltip-icon {
    width: 16px;
    height: 16px;
}

/* ==== 以下为复制过来的 tooltip 箭头，需要实现4个方向 ====*/
.rc-tooltip-arrow,.rc-tooltip-arrow-inner {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}

.rc-tooltip-placement-top .rc-tooltip-arrow,
.rc-tooltip-placement-topLeft .rc-tooltip-arrow,
.rc-tooltip-placement-topRight .rc-tooltip-arrow {
    transform: translate(-50%, 5px);
    border-width: 6px 6px 0;
    border-top-color: var(--comate-tooltip-background);
}

.rc-tooltip-placement-top .rc-tooltip-arrow-inner,
.rc-tooltip-placement-topLeft .rc-tooltip-arrow-inner,
.rc-tooltip-placement-topRight .rc-tooltip-arrow-inner {
    bottom: 1px;
    margin-left: -6px;
    border-width: 6px 6px 0;
    border-top-color: #fff;
}

.rc-tooltip-placement-top .rc-tooltip-arrow {
    left: 50%;
}

.rc-tooltip-placement-topLeft .rc-tooltip-arrow {
    left: 15%;
}

.rc-tooltip-placement-topRight .rc-tooltip-arrow {
    right: 15%;
}

.rc-tooltip-placement-right .rc-tooltip-arrow,
.rc-tooltip-placement-rightTop .rc-tooltip-arrow,
.rc-tooltip-placement-rightBottom .rc-tooltip-arrow {
    left: -5px;
    margin-top: -6px;
    border-width: 6px 6px 6px 0;
    border-right-color:  var(--comate-tooltip-background);
    transform: translate(calc(-100% + 1px));
}

.rc-tooltip-placement-right .rc-tooltip-arrow-inner,
.rc-tooltip-placement-rightTop .rc-tooltip-arrow-inner,
.rc-tooltip-placement-rightBottom .rc-tooltip-arrow-inner {
    left: 1px;
    margin-top: -6px;
    border-width: 6px 6px 6px 0;
    border-right-color: #fff;
}

.rc-tooltip-placement-right .rc-tooltip-arrow {
    top: 50%;
}

.rc-tooltip-placement-rightTop .rc-tooltip-arrow {
    top: 15%;
    margin-top: 0;
}

.rc-tooltip-placement-rightBottom .rc-tooltip-arrow {
    bottom: 15%;
}

.rc-tooltip-placement-left .rc-tooltip-arrow,
.rc-tooltip-placement-leftTop .rc-tooltip-arrow,
.rc-tooltip-placement-leftBottom .rc-tooltip-arrow {
    right: -5px;
    margin-top: -6px;
    border-width: 6px 0 6px 6px;
    border-left-color:  var(--comate-tooltip-background);
    transform: translate(calc(100% - 1px));
}

.rc-tooltip-placement-left .rc-tooltip-arrow-inner,
.rc-tooltip-placement-leftTop .rc-tooltip-arrow-inner,
.rc-tooltip-placement-leftBottom .rc-tooltip-arrow-inner {
    right: 1px;
    margin-top: -6px;
    border-width: 6px 0 6px 6px;
    border-left-color: #fff;
}

.rc-tooltip-placement-left .rc-tooltip-arrow {
    top: 50%;
}

.rc-tooltip-placement-leftTop .rc-tooltip-arrow {
    top: 15%;
    margin-top: 0;
}

.rc-tooltip-placement-leftBottom .rc-tooltip-arrow {
    bottom: 15%;
}

.rc-tooltip-placement-bottom .rc-tooltip-arrow,
.rc-tooltip-placement-bottomLeft .rc-tooltip-arrow,
.rc-tooltip-placement-bottomRight .rc-tooltip-arrow {
    transform: translate(-50%,-5px);
    border-width: 0 6px 6px;
    border-bottom-color:  var(--comate-tooltip-background);
}

.rc-tooltip-placement-bottom .rc-tooltip-arrow-inner,
.rc-tooltip-placement-bottomLeft .rc-tooltip-arrow-inner,
.rc-tooltip-placement-bottomRight .rc-tooltip-arrow-inner {
    top: 1px;
    margin-left: -6px;
    border-width: 0 6px 6px;
    border-bottom-color: #fff;
}

.rc-tooltip-placement-bottom .rc-tooltip-arrow {
    left: 50%;
}

.rc-tooltip-placement-bottomLeft .rc-tooltip-arrow {
    left: 15%;
}

.rc-tooltip-placement-bottomRight .rc-tooltip-arrow {
    right: 15%;
}
