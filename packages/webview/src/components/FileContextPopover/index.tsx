import {useCallback, useEffect, useMemo, useState} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import {debounce} from 'lodash';
import {messageHandler} from '@/utils/messageHandler';
import {useInputBoxSizeToggle} from '@/hooks/useInputBoxSizeToggle';
import {extractFilename} from '../InputBox/utils/mention';
import {HighlightText} from '../ExtensionPopover/HighlightText';
import {findPositionInOrder} from '../ExtensionPopover/findPosition';
import {useFileContext} from './hooks/useFileContext';
import Content from './Content';
import Popover from './Popover';
import Footer from './Footer';

interface Props {
    visible: boolean;
    onClose: () => void;
    classname?: string;
}

function FileContextPopover({visible, onClose, classname}: Props) {
    const [keyword, setKeyword] = useState('');
    const [selectableFileList, setSelectableFileList] = useState<string[]>([]);
    const {selectedFiles, onDeselectFile, onSelectFile} = useFileContext();
    const [inputBoxExpanded] = useInputBoxSizeToggle();

    const onSearch = useMemo(
        () =>
            debounce(
                async (keyword?: string) => {
                    const files = await messageHandler.send(EventMessage.FilesFetchEvent, {type: 'file', keyword});
                    setSelectableFileList(files ?? []);
                    setKeyword(keyword ?? '');
                },
                200
            ),
        [setSelectableFileList]
    );

    const handleSelectFile = useCallback(
        (relativePath: string, select: boolean) => {
            if (select) {
                onSelectFile(relativePath);
            }
            else {
                onDeselectFile(relativePath);
            }
        },
        [onDeselectFile, onSelectFile]
    );

    const renderFileName = useCallback(
        (filePath: string) => {
            const filename = extractFilename(filePath);
            return (
                <HighlightText
                    text={filename}
                    highlightPosition={findPositionInOrder(filename, keyword)}
                />
            );
        },
        [keyword]
    );

    useEffect(
        () => {
            async function init() {
                const files = await messageHandler.send(EventMessage.FilesFetchEvent, {type: 'file'});
                setSelectableFileList(files ?? []);
            }
            if (visible) {
                init();
            }
        },
        [visible]
    );

    if (!visible) {
        return null;
    }

    return (
        <Popover
            data-role={inputBoxExpanded ? 'collapsed' : 'expended'}
            closePopover={onClose}
            footer={<Footer onSearch={onSearch} />}
            className={classname}
        >
            <Content
                fileRelativePaths={selectableFileList}
                handleSelectFile={handleSelectFile}
                selectedFileRelativePaths={selectedFiles}
                renderFileName={renderFileName}
            />
        </Popover>
    );
}

export default FileContextPopover;
