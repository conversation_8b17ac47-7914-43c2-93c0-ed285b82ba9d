/* eslint-disable max-len */
/* bca-disable */
import {useCallback, useEffect, useRef, useState} from 'preact/compat';
import closeIcon from '@/assets/closeCircle.svg';
import searchIcon from '@/assets/search.svg';

interface Props {
    onSearch: (keyword: string) => void;
}

function Footer({onSearch}: Props) {
    const inputRef = useRef<HTMLInputElement>(null);
    const [value, setValue] = useState<string>('');
    const handleInputChange = useCallback(
        (e: any) => {
            const value = e.target?.value;
            onSearch(value);
            setValue(value);
        },
        [onSearch]
    );

    const clear = useCallback(
        (e: any) => {
            onSearch('');
            setValue('');
            e.stopPropagation();
        },
        [onSearch]
    );

    const showCloseIcon = !!value;

    useEffect(
        () => {
            if (inputRef.current) {
                inputRef.current.focus();
            }
        },
        []
    );

    return (
        <div className="flex items-center gap-1 w-full">
            <div
                className="w-4 h-4 flex-shrink-0"
                aria-hidden="true"
                dangerouslySetInnerHTML={{__html: searchIcon}}
            />
            <input
                aria-label="您可以在此输入搜索文件名，也可通过VO+方向键进入列表进行选择，按下VO+空格键确认选择，如不需要关联文件，可按下ESC退出。"
                ref={inputRef}
                value={value}
                onChange={handleInputChange}
                placeholder="输入文件名进行搜索"
                className="flex-1 w-full rounded block bg-transparent text-[var(--comate-input-foreground)] py-1 focus:outline-none placeholder:text-[var(--comate-input-placeholderForeground)] placeholder:opacity-60"
            />
            {showCloseIcon
                ? (
                    <div
                        onClick={clear}
                        aria-label="清空搜索结果"
                        className="cursor-pointer min-w-4 w-4 h-4 text-[#d4d4d4b3]"
                        dangerouslySetInnerHTML={{__html: closeIcon}}
                    />
                )
                : null
            }
        </div>
    );
}

export default Footer;

