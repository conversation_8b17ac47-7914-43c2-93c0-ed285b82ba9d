/* eslint-disable max-len */
/* bca-disable */
import {ReactNode, useCallback, useMemo} from 'preact/compat';
import FileListItem from './FileListItem';
import {useSelectItem} from './hooks/useSelectItem';

interface Item {
    relativePath: string;
    selected: boolean;
    isCurrentFile: boolean;
}

interface Props {
    items: Item[];
    handleSelectFile: (relativePath: string, select: boolean) => void;
    renderFileName?: (relativePath: string) => ReactNode;
}

function FileList({items, renderFileName, handleSelectFile}: Props) {
    const itemsWithId = useMemo(
        () => items.map(v => ({...v, id: v.relativePath})),
        [items]
    );

    const onEnterSelect = useCallback(
        (target: Item & {id: string}) => handleSelectFile(target.relativePath, !target.selected),
        [handleSelectFile]
    );

    const [selectedItemId, selectedItemRef] = useSelectItem(itemsWithId, onEnterSelect);

    return (
        <div className="flex flex-col">
            {itemsWithId.length > 0
                ? itemsWithId.map(({id, relativePath, selected, isCurrentFile}) => (
                    <FileListItem
                        ref={selectedItemId === id ? selectedItemRef : null}
                        key={relativePath}
                        isCurrentFile={isCurrentFile}
                        isPreselected={selectedItemId === id}
                        selected={selected}
                        filePath={relativePath}
                        handleSelectFile={handleSelectFile}
                    >
                        {renderFileName?.(relativePath)}
                    </FileListItem>
                ))
                : (
                    <div className="p-2 text-center">
                        🤕 暂未搜索到相关知识
                    </div>
                )
            }
        </div>
    );
}

export default FileList;
