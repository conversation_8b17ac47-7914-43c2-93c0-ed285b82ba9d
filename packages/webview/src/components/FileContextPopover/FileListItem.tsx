/* eslint-disable max-len */
/* bca-disable */
import {forwardRef, ReactNode, useCallback} from 'preact/compat';
import {useTranslation} from '@/i18n/useTranslation';
import {FileContextText} from '@/i18n/constants';
import checkIcon from '@/assets/check.svg';
import Popover from '../Tooltip/Popover';
import FileIcon from '../FileIcon';
import FileToolTip from './FileTooltip';

function parseFilePath(filePath: string, isWin: boolean) {
    const fileName = filePath.split(/(\\|\/)/).pop() ?? '';
    const folder = filePath.slice(0, filePath.length - fileName.length);
    const relativeRoot = isWin ? '.\\' : './';
    return {fileName, folder: folder || relativeRoot};
}

interface ListItemProps {
    filePath: string;
    selected: boolean;
    isPreselected?: boolean;
    isCurrentFile?: boolean;
    children?: ReactNode;
    handleSelectFile: (relativePath: string, select: boolean) => void;
    id?: string;
}

const FileListItem = forwardRef<HTMLDivElement, ListItemProps>(
    function ListItem({filePath = '', children, isPreselected, isCurrentFile, selected, handleSelectFile, id}, ref) {
        const {t} = useTranslation();

        const handleClick = useCallback(
            () => handleSelectFile(filePath, !selected),
            [filePath, handleSelectFile, selected]
        );
        const isWindows = window.navigator.userAgent.includes('Windows');
        const {fileName, folder} = parseFilePath(filePath, isWindows);
        return (
            // 多套了一层 div，修复 tooltip 导致 ref 无法获取的问题
            // ref 直接给 Popover.Item 的话会拿不到，还不确定是啥原因
            <div ref={ref} data-role="item-wrapper">
                <FileToolTip filePath={filePath}>
                    <Popover.Item
                        title={fileName}
                        className="min-h-[26px]"
                        onClick={handleClick}
                        selected={isPreselected}
                        description={<span data-role="ltr-label">{folder}</span>}
                        statusIcon={selected && (
                            <div
                                className="w-4 h-4 text-[#17C3E5]"
                                dangerouslySetInnerHTML={{__html: checkIcon}}
                            />
                        )}
                        id={`input_popover_${id}`}
                        role="option"
                        tabIndex={0}
                        aria-label={fileName}
                    >
                        <div className="flex gap-1 overflow-hidden items-center">
                            <FileIcon filename={fileName} className="w-4 h-4 min-w-4" />
                            <div className="flex-1 overflow-hidden overflow-ellipsis whitespace-nowrap">
                                {children ? children : <span>{fileName}</span>}
                            </div>
                            {isCurrentFile && (
                                <Popover.Tag className="text-[11px]/[18px]">
                                    <span>{t(FileContextText.TAG_CURRENT)}</span>
                                </Popover.Tag>
                            )}
                        </div>
                    </Popover.Item>
                </FileToolTip>
            </div>
        );
    }
);

export default FileListItem;
