import {HTMLAttributes, ReactNode, useEffect, useRef} from 'preact/compat';
import TooltipPopover from '../Tooltip/Popover';

type Props = Omit<HTMLAttributes<HTMLDivElement>, 'size' | 'ref'> & {closePopover?: () => void, footer?: ReactNode};

export default function Popover({closePopover, ...props}: Props) {
    const ref = useRef<HTMLDivElement>(null);

    useEffect(
        () => {
            const closeWhenEscPressed = (event: KeyboardEvent) => {
                if (event.key === 'Escape') {
                    closePopover?.();
                }
            };
            document.addEventListener('keydown', closeWhenEscPressed);
            return () => document.removeEventListener('keydown', closeWhenEscPressed);
        },
        [closePopover]
    );

    useEffect(
        () => {
            const closeWhenClickOutside = (e: MouseEvent) => {
                const target = e.target as HTMLElement;
                if (ref.current && !ref.current.contains(target)) {
                    closePopover?.();
                }
            };
            document.addEventListener('click', closeWhenClickOutside);
            return () => document.removeEventListener('click', closeWhenClickOutside);
        },
        [closePopover]
    );

    return <TooltipPopover.Overlay ref={ref} size="full" {...props} />;
}
