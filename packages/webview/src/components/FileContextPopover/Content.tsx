import {useMemo} from 'preact/hooks';
import {ReactNode} from 'preact/compat';
import {uniq} from 'lodash';
import FileList from './FileList';

interface Props {
    fileRelativePaths: string[];
    selectedFileRelativePaths: string[];
    renderFileName?: (relativePath: string) => ReactNode;
    handleSelectFile: (relativePath: string, select: boolean) => void;
}

function Content({
    renderFileName,
    handleSelectFile,
    fileRelativePaths,
    selectedFileRelativePaths,
}: Props) {
    const filesList = useMemo(
        () => {
            return uniq(fileRelativePaths).map(path => ({
                isCurrentFile: false,
                relativePath: path,
                selected: selectedFileRelativePaths.includes(path),
            }));
        },
        [fileRelativePaths, selectedFileRelativePaths]
    );

    return (
        <FileList
            items={filesList}
            renderFileName={renderFileName}
            handleSelectFile={handleSelectFile}
        />
    );
}

export default Content;
