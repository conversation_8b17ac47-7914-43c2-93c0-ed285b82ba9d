import {useCallback, useEffect, useRef, useState} from 'preact/hooks';

export function useSelectItem<T extends {id: string}>(
    list: T[],
    onSelect: (target: T) => void
) {
    const [selectedItemId, setSelectedItemId] = useState<string | undefined>();
    const selectedItemIdRef = useRef(selectedItemId);
    const selectedItemRef = useRef<HTMLDivElement | null>(null);

    const scrollToSelected = useCallback(
        () => {
            if (selectedItemRef.current) {
                selectedItemRef.current.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                });
            }
        },
        []
    );

    useEffect(
        () => {
            // eslint-disable-next-line complexity
            function handleKeyDown(e: KeyboardEvent) {
                // preventDefault只对于方向键和Enter健有效，Tab需要响应默认行为，例如焦点切换
                const moveToNextItem = (index: number) => {
                    e.preventDefault();
                    const nextItem = list[index];
                    const id = nextItem?.id;
                    selectedItemIdRef.current = id;
                    setSelectedItemId(id);
                };
                const index = list.findIndex(item => item.id === selectedItemIdRef.current);
                // NOTE: 是否正在使用输入法
                const isComposing = e.isComposing;
                const len = list.length;
                // 当按下下方向键时，聚焦到下一个选项
                if (
                    (e.key === 'ArrowDown' && !isComposing)
                    || (e.key === 'Tab' && !e.shiftKey && !isComposing)
                    || (e.key === 'n' && e.ctrlKey && !isComposing)
                ) {
                    moveToNextItem((index + 1) % len);
                    e.stopPropagation();
                }
                // 当按下上方向键时，聚焦到上一个选项
                else if (
                    (e.key === 'ArrowUp' && !isComposing)
                    || (e.key === 'Tab' && e.shiftKey && !isComposing)
                    || (e.key === 'p' && e.ctrlKey && !isComposing)
                ) {
                    moveToNextItem(index === -1 ? len - 1 : (index - 1 + len) % len);
                    e.stopPropagation();
                }
                else if (e.key === 'Enter') {
                    const selectedItem = list.find(item => item.id === selectedItemIdRef.current);
                    if (selectedItem) {
                        e.preventDefault();
                        onSelect(selectedItem);
                    }
                    e.stopPropagation();
                }
            }
            document.addEventListener('keydown', handleKeyDown);
            // 每次更新列表时，重置选中项
            setSelectedItemId(undefined);
            selectedItemIdRef.current = undefined;
            return () => {
                document.removeEventListener('keydown', handleKeyDown);
            };
        },
        [list, onSelect]
    );

    useEffect(
        () => {
            scrollToSelected();
        },
        [scrollToSelected, selectedItemId]
    );

    return [selectedItemId, selectedItemRef] as const;
}
