import {without} from 'lodash';
import {useCallback, useMemo} from 'preact/hooks';
import {useChatInput} from '@/components/InputBox/hooks/useChatInput';
import {extractFilename, extractMentionsFromMarkupedText, mention2Text} from '@/components/InputBox/utils/mention';
import {useEditorWatcher} from '@/hooks/useEditorWatcher';

export function useFileContext() {
    const {text, onInputChange} = useChatInput();
    const {activeEditor, selectedFileRelativePaths, onFileContextSelected, onMuteActiveEditor} = useEditorWatcher();

    const fileContextsFromInputMention = useMemo(
        () => {
            const mentions = extractMentionsFromMarkupedText(text);
            return mentions.map(mention => mention.id.split(':')[1]);
        },
        [text]
    );

    const onSelectFile = useCallback(
        (filepath: string) => {
            onFileContextSelected(selected => {
                if (selected.includes(filepath)) {
                    return selected;
                }
                return [...selected, filepath];
            });
        },
        [onFileContextSelected]
    );

    const activeRelativePath = activeEditor?.visible ? activeEditor.relativePath : '';
    const onDeselectFile = useCallback(
        (relativePath: string) => {
            // 反选时需要考虑是菜单里选中的还是通过输入框#选中的，如果是#选中的，则需要更新chat的query
            if (selectedFileRelativePaths.includes(relativePath)) {
                onFileContextSelected(without(selectedFileRelativePaths, relativePath));
            }
            else if (relativePath === activeRelativePath) {
                onMuteActiveEditor();
            }
            else {
                const filename = extractFilename(relativePath);
                filename && onInputChange(
                    text => text.replace(mention2Text({display: filename, id: `file:${relativePath}`}), '')
                );
            }
        },
        [selectedFileRelativePaths, activeRelativePath, onFileContextSelected, onMuteActiveEditor, onInputChange]
    );

    const selectedFiles = useMemo(
        () => {
            const paths = [...fileContextsFromInputMention, ...selectedFileRelativePaths];
            if (activeRelativePath) {
                return [activeRelativePath, ...paths];
            }
            return paths;
        },
        [fileContextsFromInputMention, selectedFileRelativePaths, activeRelativePath]
    );

    return {
        selectedFiles,
        onSelectFile,
        onDeselectFile,
    };
}
