/* bca-disable */
import {useMemo} from 'preact/hooks';
import folderIcon from '@/assets/folder.svg';
import FileIcon from '../FileIcon';
import Tooltip, {Props} from '../Tooltip';
import Popover from '../Tooltip/Popover';

interface FileTooltipProps extends Omit<Props, 'overlay'> {
    filePath: string;
}

interface FileTreeItemProps {
    type: string;
    value: string;
    index: number;
}

function FileTreeItem({type, value, index}: FileTreeItemProps) {
    const paddingLeft = (index + 1) * 8;

    return (
        <div
            style={{paddingLeft}}
            className="px-2 h-5 mt-1 flex gap-1 items-center"
        >
            {type === 'folder'
                ? (
                    <div
                        className="mt-0.5 min-w-3 w-3 h-3"
                        dangerouslySetInnerHTML={{__html: folderIcon}}
                    />
                )
                : <FileIcon className="w-3 h-3 min-w-3" filename={value} />
            }
            <span className="overflow-hidden overflow-ellipsis whitespace-nowrap">{value}</span>
        </div>
    );
}

export default function FileToolTip({filePath, ...props}: FileTooltipProps) {
    const fileTreeItems = useMemo(
        () => {
            const folderOrFiles = filePath.split(/[/\\]/).filter(v => v);
            const filename = folderOrFiles.pop() ?? '';
            const folders = folderOrFiles.map(v => ({type: 'folder', value: v}));
            if (folders.length > 5) {
                return [
                    ...folders.slice(0, 2),
                    {type: 'folder', value: '...'},
                    ...folders.slice(-2),
                    {type: 'file', value: filename},
                ];
            }
            return [...folders, {type: 'file', value: filename}];
        },
        [filePath]
    );

    if (fileTreeItems.length < 2) {
        return props.children;
    }

    return (
        <Tooltip
            trigger="hover"
            placement="topRight"
            mouseEnterDelay={1}
            overlayInnerStyle={{padding: 0, border: 'none'}}
            overlay={
                <Popover.Overlay
                    className="pt-1 pb-2 flex flex-col gap-1 w-fit max-w-[200px] min-w-[128px] overflow-hidden"
                >
                    {fileTreeItems.map((item, index) => (
                        <FileTreeItem
                            key={`file-tree-item-${index + 1}`}
                            type={item.type}
                            value={item.value}
                            index={index}
                        />
                    ))}
                </Popover.Overlay>
            }
            {...props}
        />
    );
}
