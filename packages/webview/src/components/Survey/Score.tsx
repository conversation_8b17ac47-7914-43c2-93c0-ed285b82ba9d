import {FunctionComponent, VNode} from 'preact';
import {useState} from 'preact/hooks';

interface Props {
    rating?: number;
    onChange?: (rating: number) => void;
    readOnly?: boolean;
    maxRating?: number;
}

const Score: FunctionComponent<Props> = ({
    rating = 0,
    onChange,
    readOnly = false,
    maxRating = 5,
}: Props): VNode => {
    const [currentRating, setCurrentRating] = useState(rating);

    const handleStarClick = (value: number) => {
        if (!readOnly) {
            setCurrentRating(value);
            onChange?.(value);
        }
    };

    return (
        <div className="flex">
            {Array.from({length: maxRating}, (_, index) => index + 1).map(starIndex => (
                <div
                    key={starIndex}
                    className={`cursor-pointer select-none relative inline-flex items-center justify-center ${
                        readOnly ? 'cursor-default' : ''
                    } ${currentRating >= starIndex ? 'text-yellow-500' : 'text-gray-300'} mr-1 text-[15px]`}
                    onClick={() => handleStarClick(starIndex)}
                    onMouseEnter={() => {
                        if (!readOnly) {
                            setCurrentRating(starIndex);
                        }
                    }}
                    onMouseLeave={() => {
                        if (!readOnly) {
                            setCurrentRating(rating);
                        }
                    }}
                >
                    ★
                </div>
            ))}
        </div>
    );
};

export default Score;
