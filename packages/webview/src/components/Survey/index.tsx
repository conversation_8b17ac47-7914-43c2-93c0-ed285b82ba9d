/* eslint-disable max-len */
import {useCallback, useEffect, useState} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import platform from '@shared/platform';
import {useTranslation} from 'react-i18next';
import {messageHandler} from '@/utils/messageHandler';
import {CommonText, SURVEY_INPUT_PLACEHOLDER, SURVEY_TITLE} from '@/i18n/constants';
import {safePromise} from '@/utils/common';
import Score from './Score';

const currentSurveyVersion = 1;

export default () => {
    const [score, setScore] = useState(0);
    const [suggestion, setSuggestion] = useState<string>();
    const [submiting, setSubmiting] = useState(false);
    const [version, setVersion] = useState<number>();
    const {t} = useTranslation();

    useEffect(
        () => {
            const init = async () => {
                try {
                    const version = await safePromise(messageHandler.send(EventMessage.SurveyVersionFetchEvent));
                    setVersion(version || 0);
                }
                catch (e) {
                    // eslint-disable-next-line
                    console.log(e);
                }
            };

            init();
        },
        []
    );

    const onSuggestionChange = useCallback(
        (e: any) => {
            setSuggestion(e?.target?.value);
        },
        []
    );

    const close = useCallback(
        () => {
            messageHandler.send(EventMessage.SurveyVersionRefreshEvent, currentSurveyVersion);
            setVersion(currentSurveyVersion);
        },
        []
    );

    const onSubmit = useCallback(
        () => {
            setSubmiting(true);
            messageHandler.send(
                EventMessage.SurveySendEvent,
                {
                    score,
                    suggestion,
                    platform: $features.PLATFORM,
                    consumer: $features.WEBVIEW_CONSUMER,
                    version: currentSurveyVersion,
                }
            );
            setTimeout(
                () => {
                    setSubmiting(false);
                    close();
                },
                1200
            );
        },
        [close, score, suggestion]
    );

    if (
        $features.PLATFORM === 'poc' || version === undefined || version >= currentSurveyVersion
        || $features.WEBVIEW_CONSUMER !== 'vscode'
    ) {
        return null;
    }

    return (
        <div className="bg-[#0973e314] flex flex-col rounded mb-4 p-2 pr-[14px] text-[var(--comate-editor-foreground)] gap-2">
            <div className="flex items-center">
                <div className="mr-4 text-[12px]">
                    {t(SURVEY_TITLE, {brand: platform.resolve('brand')})}
                </div>
                <Score rating={score} onChange={setScore} />
            </div>
            {$features.PLATFORM === 'saas' && (
                <textarea
                    placeholder={t(SURVEY_INPUT_PLACEHOLDER)}
                    className="rounded bg-[transparent] focus:outline-[var(--comate-link-color)] text-[12px] p-2 w-full outline outline-1 outline-[var(--vscode-menu-border)] flex-auto"
                    rows={2}
                    value={suggestion}
                    onChange={onSuggestionChange}
                />
            )}
            <div className="flex gap-4 text-[var(--comate-link-color)] hover:cursor-pointer text-[13px] justify-end">
                <button className="text-[var(--comate-descriptionForeground)] hover:opacity-80" onClick={close}>
                    {t(CommonText.CLOSE)}
                </button>
                <button
                    disabled={score === 0}
                    onClick={onSubmit}
                    className="hover:opacity-80 disabled:cursor-not-allowed disabled:text-[var(--comate-descriptionForeground)]"
                >
                    {submiting ? t(CommonText.SUBMITTING) : t(CommonText.SUBMIT)}
                </button>
            </div>
        </div>
    );
};
