/* eslint-disable max-len */
import {noop} from 'lodash';
import {ComponentProps, createContext} from 'preact';
import {CSSProperties, ReactNode} from 'preact/compat';
import {useCallback, useContext, useEffect, useRef, useState} from 'preact/hooks';
import {useTranslation} from 'react-i18next';
import classNames from 'classnames';
import {CommonText, FORM_ERROR_DESCRIPTION_FORMAT} from '@/i18n/constants';
import {parseUrlIfPossible} from '../InputBox/utils/is';
import {stopPropagation} from './utils';
import './InputModal.css';
interface ModalProps {
    title?: ReactNode;
    placeholder?: string;
    onOk?: (text: string) => any;
}

interface ContextProps {
    open: boolean;
    openModal: (modalProps: ModalProps) => void;
    closeModal: () => void;
    modalProps: ModalProps;
}

const InputModalContext = createContext<ContextProps>({
    open: false,
    openModal: noop,
    closeModal: noop,
    modalProps: {},
});

export const useInputModal = () => useContext(InputModalContext);

/**
 * InputModal包含一个输入框，用户一些输入字符串的场景比如URL，暂时不做复杂的扩展例如Form
 * 如果后续有统一的FormModal, 可以替换
 */
export function InputModal({style}: {style?: CSSProperties}) {
    const {t} = useTranslation();
    const inputRef = useRef<HTMLInputElement>(null);
    const {open, closeModal, modalProps} = useInputModal();
    const [errorMessage, setErrorMessage] = useState('');

    const resetErrorMessage = useCallback(
        () => setErrorMessage(''),
        []
    );

    const closeModalAndReset = useCallback(
        () => {
            closeModal();
            setErrorMessage('');
        },
        [closeModal]
    );

    const handleOk = useCallback(
        async () => {
            const input = inputRef.current!;
            const value = input.value;
            try {
                if (parseUrlIfPossible(value)) {
                    await modalProps.onOk?.(value);
                    closeModalAndReset();
                }
                else {
                    setErrorMessage(t(FORM_ERROR_DESCRIPTION_FORMAT, {property: 'URL'}));
                }
            }
            catch (ex) {
                setErrorMessage((ex as Error).message);
            }
        },
        [closeModalAndReset, modalProps, t]
    );

    useEffect(
        () => {
            const closeWhenEscPressed = (event: KeyboardEvent) => {
                if (event.key === 'Escape') {
                    closeModalAndReset();
                }
                else if (event.key === 'Enter') {
                    handleOk();
                }
            };
            const input = inputRef.current;
            if (input) {
                input.addEventListener('keydown', closeWhenEscPressed);
                return () => input.removeEventListener('keydown', closeWhenEscPressed);
            }
        },
        [open, closeModalAndReset, handleOk]
    );

    useEffect(
        () => {
            if (open) {
                inputRef.current?.focus();
            }
            else {
                closeModalAndReset();
            }
        },
        [closeModalAndReset, open]
    );

    if (!open) {
        return null;
    }

    return (
        // 这个wrapper是为了技能让modal贴着下面的输入框，又能浮动不占任何高度
        <div style={{position: 'absolute', width: '100%'}}>
            <div
                className="input-modal z-20"
                aria-modal="true"
                role="alertdialog"
                style={style}
                onClick={stopPropagation}
            >
                <div className="modal-header">
                    <h3>{modalProps.title}</h3>
                </div>
                <div className={classNames('modal-body', {'has-error': !!errorMessage})}>
                    <input ref={inputRef} placeholder={modalProps.placeholder} onChange={resetErrorMessage} />
                    {errorMessage && <p>{errorMessage}</p>}
                </div>
                <div className="modal-footer">
                    <button className="modal-footer-secondary-btn" onClick={closeModal}>
                        {t(CommonText.CANCEL)}
                    </button>
                    <button className="modal-footer-primary-btn" onClick={handleOk}>
                        {t(CommonText.OK)}
                    </button>
                </div>
            </div>
        </div>
    );
}

export const InputModalProvider = ({
    children,
    ...props
}: Omit<ComponentProps<typeof InputModalContext.Provider>, 'value'>) => {
    const [open, setOpen] = useState(false);
    const modalPropsRef = useRef<ModalProps>({});

    const openModal = useCallback(
        (modalProps: ModalProps) => {
            modalPropsRef.current = modalProps;
            setOpen(true);
        },
        []
    );

    const closeModal = useCallback(
        () => setOpen(false),
        []
    );

    return (
        <InputModalContext.Provider
            {...props}
            value={{open, openModal, closeModal, modalProps: modalPropsRef.current}}
        >
            {children}
            {open && (
                <div className="relative z-10" onClick={closeModal}>
                    <div className="fixed inset-0 bg-[var(--comate-inputOverlay-background)] opacity-70" />
                </div>
            )}
        </InputModalContext.Provider>
    );
};
