/* eslint-disable max-len */
import {useCallback} from 'preact/hooks';
import {useTranslation} from 'react-i18next';
import {CommonText} from '@/i18n/constants';
import {stopPropagation} from './utils';

interface Props {
    title: string;
    description?: string;
    modalKey?: string;
    closeModal: (modalKey?: string) => void;
    onOk: (modalKey?: string) => void;
}

function Modal({closeModal, onOk, title, description, modalKey}: Props) {
    const {t} = useTranslation();
    const handleOk = useCallback(
        () => {
            onOk(modalKey);
            closeModal(modalKey);
        },
        [modalKey, closeModal, onOk]
    );

    const handleClose = useCallback(
        () => {
            closeModal(modalKey);
        },
        [modalKey, closeModal]
    );

    return (
        <div className="relative z-20">
            <div className="fixed inset-0 bg-[var(--comate-inputOverlay-background)] opacity-70" />
            <div onClick={handleClose} className="fixed inset-0 z-10 overflow-y-auto">
                <div className="flex min-h-full items-center justify-center p-4 text-center">
                    <div
                        onClick={stopPropagation}
                        className="w-[310px] shadow-md bg-[var(--comate-notifications-background)] relative transform overflow-hidden rounded px-4 pb-4 pt-5 translate-y-0 border border-[var(--comate-panel-border)]"
                    >
                        <div>
                            <div className="text-center">
                                <h3 className="text-base font-semibold leading-6">
                                    {title}
                                </h3>
                                <p className="mt-2 text-sm text-[var(--comate-descriptionForeground)]">{description}</p>
                            </div>
                        </div>
                        <div className="mt-5 flex gap-2 justify-center items-center">
                            <button
                                className="w-28 py-1 text-center rounded text-[var(--comate-button-secondaryForeground)] bg-[var(--vscode-button-secondaryBackground)] hover:bg-[var(--comate-button-secondaryHoverBackground)]"
                                onClick={handleClose}
                            >
                                {t(CommonText.CANCEL)}
                            </button>
                            <button
                                className="w-28 py-1 text-center rounded text-[var(--comate-button-foreground)] bg-[var(--vscode-button-background)] hover:bg-[var(--comate-button-hoverBackground)]"
                                onClick={handleOk}
                            >
                                {t(CommonText.OK)}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Modal;
