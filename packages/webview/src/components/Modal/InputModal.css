.input-modal[aria-modal="true"] {
    display: flex;
    flex-direction: column;
    padding: 20px 18px;
    border-radius: 4px;
    margin-bottom: 4px;
    background: var(--comate-list-background);
    position: absolute;
    width: 100%;
    box-shadow: 0 0 8px rgba(0,0,0, .12);
    bottom: 0;
}

.input-modal[aria-modal="true"] .modal-header {
}

.input-modal[aria-modal="true"] .modal-header h3 {
    font-size: 14px;
    margin: 0;
}

.input-modal[aria-modal="true"] .modal-body {
    margin: 12px 0;
}

.input-modal[aria-modal="true"] .modal-body.has-error input {
    box-shadow: 0 0 0 1px #ff4d4f !important;
}

.input-modal[aria-modal="true"] .modal-body.has-error input + p {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 2px;
    padding-left: 12px;
    position: absolute;
}

.input-modal[aria-modal="true"] .modal-body input {
    padding: 7px 12px;
    border: none;
    font-size: 13px;
    line-height: 18px;
    border-radius: 4px;
    background-color: var(--comate-tooltip-background);
    width: 100%;
    outline: none;
}

.input-modal[aria-modal="true"] .modal-footer {
    display: flex;
    justify-content: flex-end;
}

.input-modal[aria-modal="true"] .modal-footer button {
    border-radius: 4px;
    box-shadow: none;
    border: none;
    width: 68px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-modal[aria-modal="true"] .modal-footer .modal-footer-secondary-btn {
    background: transparent;
    margin-right: 8px;
}

.input-modal[aria-modal="true"] .modal-footer .modal-footer-secondary-btn:hover {
    background: var(--comate-button-secondaryHoverBackground);
}

.input-modal[aria-modal="true"] .modal-footer .modal-footer-primary-btn {
    background: linear-gradient(50.69deg, #6391F9 0%, #03F8E7 100%);
    color: white;
}

.input-modal[aria-modal="true"] .modal-footer .modal-footer-primary-btn:hover {
    background: linear-gradient(50.69deg, rgb(99, 145, 249, .9) 0%, rgba(3, 248, 231, .9) 100%);
}