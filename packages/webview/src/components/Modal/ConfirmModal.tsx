/* eslint-disable max-len */
import {useCallback} from 'preact/hooks';
import {useTranslation} from 'react-i18next';
import {CommonText} from '@/i18n/constants';
import warnIcon from '@/assets/warn.svg';
import {stopPropagation} from './utils';

interface Props {
    description?: string;
    modalKey?: string;
    closeModal: (modalKey?: string) => void;
    onOk: (modalKey?: string) => void;
}

function ConfirmModal({closeModal, onOk, description, modalKey}: Props) {
    const {t} = useTranslation();
    const handleOk = useCallback(
        () => {
            onOk(modalKey);
            closeModal(modalKey);
        },
        [modalKey, closeModal, onOk]
    );

    const handleClose = useCallback(
        () => {
            closeModal(modalKey);
        },
        [modalKey, closeModal]
    );

    return (
        <div className="absolute z-100 left-0 bottom-full max-w-full w-full">
            <div className="fixed z-100 inset-0 bg-[#000000]/50 backdrop-blur-sm" />
            <div onClick={handleClose} className="fixed inset-0 z-100 overflow-y-auto">
                <div className="flex min-h-full items-center justify-center p-4">
                    <div
                        onClick={stopPropagation}
                        className="w-[242px] relative transform overflow-hidden rounded px-4 py-4 translate-y-0 shadow-[0_0_12px_0_rgba(0,0,0,0.15)] border border-[var(--comate-button-border)] bg-[var(--comate-tooltip-background)]"
                    >
                        <div className="flex items-start gap-1">
                            <i
                                className="w-4 h-4 pt-[2px] pb-[2px] flex-shrink-0"
                                // bca-disable-line
                                dangerouslySetInnerHTML={{__html: warnIcon}}
                            />
                            <div className="text-sm">
                                {description}
                            </div>
                        </div>
                        <div className="mt-5 flex gap-3 justify-center items-center">
                            <button
                                // TODO: --vscode-commandCenter-background和--vscode-editorGhostText-foreground是近似色，后续需要修改
                                className="w-[90px] leading-[22px] text-center rounded bg-[var(--vscode-commandCenter-background)] border border-[var(--vscode-editorGhostText-foreground)]"
                                onClick={handleClose}
                            >
                                {t(CommonText.CANCEL)}
                            </button>
                            <button
                                className="w-[90px] leading-[22px] text-center rounded text-[#E54552] bg-[#E54552]/15 border border-[#E54552]"
                                onClick={handleOk}
                            >
                                {t(CommonText.DELETE)}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default ConfirmModal;
