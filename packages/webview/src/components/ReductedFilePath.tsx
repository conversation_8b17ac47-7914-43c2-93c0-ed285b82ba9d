import {useMemo} from 'preact/hooks';

const styleRtl = {
    direction: 'rtl',
};

const styleLtr = {
    direction: 'ltr',
    'unicode-bidi': 'bidi-override',
};

interface Props {
    className?: string;
    filePath: string;
    onClick?: () => void;
}

function ReductedFilePath({className, filePath, onClick}: Props) {
    const reductedPath = useMemo(
        () => {
            const maxSegments = 4;
            const segments = filePath.split('/');
            const visibleSegments = segments.slice(Math.max(0, segments.length - maxSegments), segments.length);
            const reducted = visibleSegments.join('/');
            return `${segments.length > maxSegments ? '.../' : ''}${reducted}`;
        },
        [filePath]
    );

    return (
        <p style={styleRtl} className="text-ellipsis whitespace-nowrap overflow-hidden text-left">
            <span
                style={styleLtr}
                onClick={onClick}
                className={className}
            >
                {reductedPath}
            </span>
        </p>
    );
}

export default ReductedFilePath;
