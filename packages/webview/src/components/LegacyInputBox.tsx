/* eslint-disable max-len */
/* bca-disable */
import {JSX} from 'preact';
import {forwardRef, memo} from 'preact/compat';
import {useTranslation} from 'react-i18next';
import {useCallback, useEffect, useImperativeHandle, useRef, useState} from 'preact/hooks';
import {InputBoxMessageHistory} from '@shared/protocols';
import flightIcon from '@/assets/flight.svg';
import {INPUT_LEGACY_PLACEHOLDER} from '@/i18n/constants';
import {isEnterKeyPressed, isMainKeyPressed} from './InputBox/utils/is';

interface Props {
    submitDisabled?: boolean;
    inputBoxMessageHistory?: InputBoxMessageHistory[];
    onSubmit: (text: string) => void;
    onChange?: (text: string) => void;
}

interface RefProps {
    focus: () => void;
}

const InputBox = forwardRef<RefProps, Props>(
    function InputBox({submitDisabled, inputBoxMessageHistory, onSubmit, onChange}, inputRef) {
        const {t} = useTranslation();
        const isWindows = window.navigator.userAgent.includes('Windows');
        const metaKey = isWindows ? 'Ctrl' : '⌘';
        const placeholder = t(INPUT_LEGACY_PLACEHOLDER, {metaKey});
        const [value, setValue] = useState<string>('');
        // 目前展示的历史记录，默认为 -1 则不展示，第 0 条为最近发送的一条，上限存储 50 条
        const currentHistoryIndex = useRef(-1);
        const ref = useRef<HTMLTextAreaElement>(null);

        const disabled = value.trim().length <= 0 || submitDisabled;

        const resizeTextArea = () => {
            // 直接设置高度可能 scrollHeight 还未更新
            setTimeout(
                () => {
                    const textarea = ref.current;
                    if (textarea) {
                        textarea.style.height = '0px';
                        textarea.style.overflow = textarea.scrollHeight > 300 ? 'scroll' : 'hidden';
                        textarea.style.height = `${Math.min(textarea.scrollHeight, 300)}px`;
                    }
                },
                0
            );
        };

        const updateValue = useCallback(
            (value: string) => {
                setValue(value);
                onChange && onChange(value);
                resizeTextArea();
            },
            [onChange]
        );

        const handleChange = useCallback(
            (e: any) => {
                updateValue(e.target.value);
            },
            [updateValue]
        );

        const handleSubmit = useCallback(
            () => {
                if (!disabled) {
                    onSubmit(value);
                    updateValue('');
                }
            },
            [disabled, onSubmit, updateValue, value]
        );

        const handleKeyDown = useCallback(
            // eslint-disable-next-line complexity
            (event: JSX.TargetedKeyboardEvent<HTMLTextAreaElement>) => {
                if (event.defaultPrevented) {
                    return;
                }
                const isUpKeyPressed = event.key === 'ArrowUp';
                const isDownKeyPressed = event.key === 'ArrowDown';

                if (isEnterKeyPressed(event) && isMainKeyPressed(event)) {
                    // enter + shift
                    const selectionStart = ref.current?.selectionStart;
                    const selectionEnd = ref.current?.selectionEnd;
                    if (selectionStart !== undefined && selectionEnd !== undefined && ref.current) {
                        const newValue = value.substring(0, selectionStart) + '\n' + value.substring(selectionEnd);
                        updateValue(newValue);
                        event.preventDefault();
                        // 修复在输入框中按下 shift + enter 后，光标位置不正确的问题，需要在下一个事件循环中设置光标位置
                        setTimeout(
                            () => {
                                ref.current?.setSelectionRange(selectionStart + 1, selectionStart + 1);
                            },
                            0
                        );
                    }
                    else {
                        updateValue(value + '\n');
                        event.preventDefault();
                    }
                }
                else if (isEnterKeyPressed(event) && !isMainKeyPressed(event)) {
                    const canSendValue = value.trim().length > 0;
                    // when empty，should not create a new line
                    if (disabled && value) {
                        // when disabled, enter should create a new line
                        updateValue(value + '\n');
                    }
                    else if (canSendValue) {
                        handleSubmit();
                    }
                    event.preventDefault();
                }
                if ((isUpKeyPressed || isDownKeyPressed) && inputBoxMessageHistory) {
                    // 按上键且光标在文字最前面，则可以向上翻找一条历史
                    if (isUpKeyPressed && ref.current?.selectionStart === 0) {
                        currentHistoryIndex.current = Math.min(
                            currentHistoryIndex.current + 1,
                            inputBoxMessageHistory.length - 1
                        );
                    }
                    // 按下键且光标在文字最后面，则可以向下翻找一条历史
                    else if (isDownKeyPressed && ref.current?.selectionStart === ref.current?.value.length) {
                        currentHistoryIndex.current = Math.max(currentHistoryIndex.current - 1, 0);
                    }
                    updateValue(inputBoxMessageHistory[currentHistoryIndex.current].value);
                    event.preventDefault();
                }
                else {
                    currentHistoryIndex.current = -1;
                }
            },
            [inputBoxMessageHistory, value, updateValue, disabled, handleSubmit]
        );

        useEffect(
            () => {
                // 初始化时执行一次，设置下 textarea 的高度
                resizeTextArea();
                if (ref.current) {
                    ref.current.focus();
                    const resizeObserver = new ResizeObserver(resizeTextArea);
                    resizeObserver.observe(ref.current);
                    const refCopy = ref.current;

                    return () => {
                        resizeObserver.unobserve(refCopy);
                    };
                }
            },
            []
        );

        useImperativeHandle(inputRef, () => ({
            focus: () => ref.current?.focus(),
        }));

        return (
            <div className="relative">
                <textarea
                    ref={ref}
                    className="bg-[var(--comate-tooltip-background)] w-full rounded block text-[var(--vscode-menu-foreground)] py-2 pl-3 pr-8 resize-none outline outline-1 outline-[var(--comate-input-foreground,#88888833)] outline-offset-[-1px] focus-within:outline-[var(--vscode-focusBorder)] focus:outline-[var(--vscode-focusBorder)] placeholder:text-[var(--comate-input-placeholderForeground)] placeholder:opacity-90"
                    placeholder={placeholder}
                    value={value}
                    onInput={handleChange}
                    onKeyDown={handleKeyDown}
                />
                <button
                    className={`absolute right-[13px] bottom-[10px] ${
                        disabled ? 'text-[var(--comate-descriptionForeground)]' : 'text-[var(--comate-link-color)] hover:opacity-90'
                    }`}
                    onClick={handleSubmit}
                    disabled={disabled}
                >
                    <div className="w-5" dangerouslySetInnerHTML={{__html: flightIcon}} />
                </button>
            </div>
        );
    }
);

export default memo(InputBox);
