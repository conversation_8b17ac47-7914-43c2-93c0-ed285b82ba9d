/* eslint-disable max-len */
import {useCallback, useMemo, useState} from 'preact/hooks';

export interface BannerState {
    bannerVersion: number;
    activityVersion: number;
    bannerUrl: string;
    actionUri: string;
    bannerShow: boolean;
}

interface Props {
    bannerState?: BannerState;
    onClose: () => void;
    onClick: () => void;
}

const DEFAULT_BANNER_URL = 'https://comate.baidu.com/zh';

// 侧边栏的广告位
export default function Banner({bannerState, onClick, onClose}: Props) {
    const [isHovered, setIsHovered] = useState(false);

    const onMouseEnter = useCallback(
        () => setIsHovered(true),
        [setIsHovered]
    );

    const onMouseLeave = useCallback(
        () => setIsHovered(false),
        [setIsHovered]
    );

    const url = useMemo(
        () => {
            const actionUri = bannerState?.actionUri ?? DEFAULT_BANNER_URL;
            return actionUri.startsWith('//chat:') ? undefined : actionUri;
        },
        [bannerState?.actionUri]
    );

    if (!bannerState || !bannerState.bannerUrl) {
        return null;
    }

    return (
        <div
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
            className="w-full flex justify-center items-center flex-col relative"
        >
            <a
                onClick={onClick}
                className="focus:outline-none cursor-pointer block w-full"
                href={url}
            >
                <img
                    className="w-full max-h-[40px] object-cover object-center rounded cursor-pointer"
                    src={bannerState.bannerUrl}
                />
            </a>
            <div
                onClick={onClose}
                className={`${
                    isHovered ? '' : 'invisible'
                } bg-[#888888]/20 cursor-pointer rounded-full w-[17px] h-[17px] absolute right-[10px]`}
            >
                <div className="absolute bg-[var(--comate-descriptionForeground)] left-2 top-1 w-[1px] h-[9px] rotate-45">
                </div>
                <div className="absolute bg-[var(--comate-descriptionForeground)] top-2 left-1 w-[9px] h-[1px] rotate-45">
                </div>
            </div>
        </div>
    );
}
