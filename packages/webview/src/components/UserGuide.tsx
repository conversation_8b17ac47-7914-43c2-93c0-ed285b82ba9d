/* bca-disable */
/* eslint-disable max-len */
import {useEffect, useState, useCallback} from 'preact/hooks';
import {partial} from 'lodash';
import {useTranslation} from 'react-i18next';
import {EventMessage, ConfigItem} from '@shared/protocols';
import classNames from 'classnames';
import {messageHandler} from '@/utils/messageHandler';
import arrowIcon from '@/assets/arrow.svg';
import changeIcon from '@/assets/change.svg';
import {GUIDE_WELCOME_TEXT, GUIDE_INSTRUCTION_TEXT, GUIDE_CHANGE_TEXT} from '@/i18n/constants';
import {getExampleKnowledgeList} from './InputBox/hooks/AgentHover/utils';
import {useChatInput, WorkspaceStatus} from './InputBox/hooks/useChatInput';
import {parseMarkupedText2PlainText} from './InputBox/utils/mention';
import {MentionHighlightText} from './ChatBox/MentionHighlightText';
import {useSetLastQuery} from './LastQueryProvider';
import {ariaHelpers} from './Aria';

interface Props {
    messageOrder: Record<number, number>;
    supportAt: boolean;
    refHeight?: number;
}

const getGuideChoiceByRule = (guide: ConfigItem, workspaceStatus?: WorkspaceStatus) => {
    if (!guide.rule || !guide.alternativeChoice) {
        return {choice: guide.primaryChoice, usePrimaryChoice: true};
    }
    switch (guide.rule) {
        case 'REPO': {
            if (workspaceStatus?.workspaceOpened) {
                return {choice: guide.primaryChoice, usePrimaryChoice: true};
            }
            else {
                return {choice: guide.alternativeChoice, usePrimaryChoice: false};
            }
        }
        default:
            return {choice: guide.primaryChoice, usePrimaryChoice: true};
    }
};

export default function UserGuide({messageOrder, supportAt, refHeight}: Props) {
    const isWindows = window.navigator.userAgent.includes('Windows');
    const [userGuides, setUserGuides] = useState<ConfigItem[]>([]);
    const [disabled, setDisabled] = useState<boolean>(false);
    const {workspaceStatus} = useChatInput();
    const {t} = useTranslation();

    const introductionHidden = isWindows || (refHeight && refHeight < 720);
    const setLastQuery = useSetLastQuery();

    useEffect(
        () => {
            messageHandler.send(EventMessage.UserGuideFetchEvent).then(userGuide => {
                setUserGuides(userGuide ?? []);
            });
        },
        []
    );

    const handleGuideClick = useCallback(
        (id: number) => {
            try {
                const guide = userGuides.find(guide => guide.uuid === id);
                if (guide) {
                    const {choice, usePrimaryChoice} = getGuideChoiceByRule(guide, workspaceStatus);
                    const repoContext = guide.repoContext;
                    // 代码类型的knowledgeList需要特殊处理 通过解析repoContext获取knowledgeList
                    const knowledgeList = repoContext
                        ? getExampleKnowledgeList(
                            repoContext.type,
                            repoContext.suggestedQuery.displayQuery,
                            repoContext.suggestedQuery.references
                        )
                        : choice?.knowledgeList;
                    const prompt = repoContext
                        ? parseMarkupedText2PlainText(repoContext.suggestedQuery.displayQuery)
                        : choice?.promptQuery;
                    const message = {
                        prompt: prompt || '',
                        messageOrder,
                        agent: choice?.agent,
                        slash: choice?.slash,
                        knowledgeList,
                        supportAt,
                        // 暂时没想好其他responseReminder的处理方式，先实现autoWork
                        responseReminder: usePrimaryChoice ? undefined : guide.reason,
                        // 首页引导无需携带划选代码作为提问上下文
                        disableCode: true,
                    };
                    messageHandler.send(EventMessage.QuerySendEvent, message);
                    ariaHelpers.sendChatMessage(message);

                    setLastQuery(message);

                    const logData = {
                        category: 'userGuide',
                        label: guide.type,
                        content: guide.displayQuery,
                        actions: usePrimaryChoice ? 'primary' : 'alternative',
                    };
                    messageHandler.send(EventMessage.UploadUserActionLog, logData);
                }
            }
            catch (ex) {
                console.error(ex);
            }
        },
        [userGuides, workspaceStatus, messageOrder, supportAt, setLastQuery]
    );

    const handleGuideChange = useCallback(
        async () => {
            try {
                setDisabled(true);
                const userGuide = await messageHandler.send(EventMessage.UserGuideChangeEvent);
                setUserGuides(userGuide ?? []);
                setDisabled(false);
                const logData = {category: 'userGuideChange'};
                messageHandler.send(EventMessage.UploadUserActionLog, logData);
            }
            catch (ex) {
                setDisabled(false);
            }
        },
        []
    );

    const hasUserGuide = userGuides.length !== 0;
    const [animationActived, setAnimationActived] = useState(false);
    useEffect(
        () => {
            if (hasUserGuide) {
                setAnimationActived(true);
            }
        },
        [hasUserGuide]
    );

    if (!hasUserGuide) {
        return null;
    }

    return (
        <div
            className={classNames('comate-chat-userguide max-h-[414px] overflow-auto fade-in z-10', {
                'fade-in-active': animationActived,
            })}
        >
            {!introductionHidden && <div className="text-sm mb-1 user-select-none">{t(GUIDE_WELCOME_TEXT)}</div>}
            {introductionHidden
                ? (
                    <div className="flex justify-end w-full mb-2 text-xs opacity-40">
                        <button
                            className="flex items-center gap-1 hover:bg-[var(--comate-icon-hoverBackground)] p-[2px] rounded"
                            onClick={handleGuideChange}
                            disabled={disabled}
                        >
                            <div className="w-[14px] h-[14px]" dangerouslySetInnerHTML={{__html: changeIcon}} />
                            {t(GUIDE_CHANGE_TEXT)}
                        </button>
                    </div>
                )
                : (
                    <div className="relative opacity-50">
                        <span className="inline-block text-xs mb-2 leading-[22px] after:content-[''] after:inline-block after:w-[48px] after:h-0 user-select-none" aria-hidden="true">
                            {t(GUIDE_INSTRUCTION_TEXT)}
                        </span>
                        <button
                            className="absolute right-0 bottom-2 text-xs"
                            onClick={handleGuideChange}
                            aria-label="更换问题指引"
                            disabled={disabled}
                        >
                            <div className="flex items-center gap-1 hover:bg-[var(--comate-icon-hoverBackground)] p-[2px] rounded">
                                <div className="w-[14px] h-[14px]" dangerouslySetInnerHTML={{__html: changeIcon}} />
                                {t(GUIDE_CHANGE_TEXT)}
                            </div>
                        </button>
                    </div>
                )}
            <div className="flex flex-col items-center gap-2 ug-dynamic-height">
                {userGuides.map((guide, index) => {
                    return (
                        <div
                            key={guide.uuid}
                            className={`p-3 w-full rounded cursor-pointer group border border-[var(--comate-card-border)] hover:bg-[var(--comate-icon-hoverBackground)] user-guide-item ugi-${index}`}
                            onClick={partial(handleGuideClick, guide.uuid)}
                        >
                            <div className="flex items-center justify-between">
                                <h3 className="text-sm mb-2 overflow-hidden overflow-ellipsis line-clamp-1" aria-hidden="true">
                                    {guide.title}
                                </h3>
                                <div
                                    className="w-3 h-3 opacity-0 group-hover:opacity-100"
                                    aria-hidden="true"
                                    dangerouslySetInnerHTML={{__html: arrowIcon}}
                                />
                            </div>
                            <p className="text-xs overflow-hidden overflow-ellipsis flex flex-col-reverse">
                                <MentionHighlightText
                                    className="line-clamp-2"
                                    stringClassName="opacity-70 break-all"
                                    text={guide.repoContext
                                        ? parseMarkupedText2PlainText(guide.repoContext.suggestedQuery.displayQuery)
                                        : guide.displayQuery}
                                />
                            </p>
                        </div>
                    );
                })}
            </div>
        </div>
    );
}
