/* eslint-disable max-len */
import RcTooltip from 'rc-tooltip';
import {TooltipProps} from 'rc-tooltip/lib/Tooltip';
import {FC, useState, ReactElement, ReactNode, useCallback, useMemo} from 'preact/compat';
import {useTranslation} from 'react-i18next';
import {EventMessage} from '@shared/protocols';
import {BASE_HEADER_LOGOUT, BASE_HEADER_PROFILE} from '@/i18n/constants';
import {messageHandler} from '@/utils/messageHandler';
import oneWayIcon from '@/assets/oneWay.svg';
import freeIcon from '@/assets/free.png';
import vipIcon from '@/assets/vip.png';
import {useExtensionConfig} from '@/hooks/useExtensionConfig';
import {isJetbrains} from '@/config';
import {useChatConfig} from '../ChatConfigProvider';
import './menu.css';

interface ReassignProps {
    children: ReactElement;
    overlay: ReactNode;
    arrowContent?: ReactNode;
}

type Props = Omit<TooltipProps, keyof ReassignProps> & ReassignProps;
const Tooltip = RcTooltip as FC<Props>;
const getTooltipContainer = () => document.querySelector('main')!;
interface MenuProps {
    username?: string;
    relative?: boolean;
    isLogin: boolean;
}

const vipLevel = {
    // eslint-disable-next-line max-len
    containerClassName:
        'bg-[linear-gradient(42.69deg,#F2C99D_0%,#FAEBDC_100%)] h-5 rounded-[90px] ml-[10px] pl-[25px] pr-[10px]',
    textClassName: 'text-[#804A2E] text-[12px] text-[smaller]',
    iconClassName: 'h-5 absolute left-[10px]',
    icon: vipIcon,
    message: '',
};
const freeLevel = {
    // eslint-disable-next-line max-len
    containerClassName:
        'bg-[linear-gradient(42.69deg,#AFBBD1_0%,#E3E9F5_100%)] h-5 rounded-[90px] ml-[10px] pl-[20px] pr-[10px]',
    textClassName: 'text-[#3E4D6A] text-[12px] text-[smaller]',
    iconClassName: 'h-5 absolute left-[15px]',
    icon: freeIcon,
    message: '',
};
const studentLevel = {
    // eslint-disable-next-line max-len
    containerClassName:
        'bg-[linear-gradient(42.69deg,#AFD1D1_0%,#E3F5F5_100%)] h-5 rounded-[90px] ml-[10px] pl-[20px] pr-[10px]',
    textClassName: 'text-[#3E6A6A] text-[12px] text-[smaller]',
    iconClassName: 'h-5 absolute left-[15px]',
    icon: freeIcon,
    message: '',
};
export default function Menu({username, relative, isLogin}: MenuProps) {
    const {t, i18n} = useTranslation();
    const [visible, setVisible] = useState(false);
    const {
        config: {licenseTypeCode, licenseTypeId, licenseType, enablePrivateService, privateServiceHost},
    } = useExtensionConfig();
    const level = useMemo(
        () => {
            switch (licenseTypeId) {
                case 1:
                    return {...vipLevel, message: licenseType};
                case 2:
                    return {...freeLevel, message: licenseType};
                case 3:
                    return {...studentLevel, message: licenseType};
                default:
                    return {...freeLevel, message: licenseType};
            }
        },
        [licenseType, licenseTypeId]
    );
    const {theme} = useChatConfig();

    const showUserProfile = useMemo(
        () =>
            [
                'EDUCATIONAL',
                'INDIVIDUAL',
                'TRIAL_INDIVIDUAL',
                'INTERNATIONAL',
            ]
                .includes(licenseTypeCode),
        [licenseTypeCode]
    );

    const {overlayInnerStyle, infoItemClassName, svgClassName} = useMemo(
        () => {
            if (theme === 'dark') {
                return {
                    overlayInnerStyle: {},
                    infoItemClassName: 'hover:bg-[#505153]',
                    svgClassName: 'fill-[white]',
                };
            }
            return {
                overlayInnerStyle: {
                    background: 'white',
                    borderColor: '#F2F2F2',
                },
                infoItemClassName: 'hover:bg-[#F2F2F2] text-[black]',
                svgClassName: 'fill-[black]',
            };
        },
        [theme]
    );

    const handleVisibleChange = useCallback(
        newVisible => {
            setVisible(newVisible);
        },
        []
    );
    const language = i18n.languages[0];
    const handleLoginOut = () => {
        messageHandler.send(EventMessage.LoginOutEvent);
        setVisible(false);
    };
    const handleToProfile = () => {
        const link = enablePrivateService ? privateServiceHost : `https://comate.baidu.com/${language}/user`;
        messageHandler.send(EventMessage.LinkClickEvent, link);
        setVisible(false);
    };

    if (!isLogin) {
        return null;
    }

    return (
        <div className={`right-3 flex h-full gap-2 ${relative ? '' : 'absolute'}`}>
            <Tooltip
                visible={visible}
                onVisibleChange={handleVisibleChange}
                overlayClassName="menu-tooltip"
                overlayInnerStyle={overlayInnerStyle}
                getTooltipContainer={getTooltipContainer}
                trigger="click"
                placement="bottom"
                align={{
                    offset: [-20, 0], // 设置偏移量 [水平偏移, 垂直偏移]
                }}
                overlay={
                    <div className="min-w-[120px] text-[13px]">
                        <div className="flex my-2">
                            <div className={level.containerClassName}>
                                <img className={level.iconClassName} src={level.icon} />
                                <span className={level.textClassName}>{level.message}</span>
                            </div>
                        </div>
                        {showUserProfile && (
                            <div
                                // eslint-disable-next-line max-len
                                className={`h-[30px] flex items-center rounded ${infoItemClassName} cursor-pointer justify-between px-3 py-1 group`}
                                onClick={handleToProfile}
                            >
                                <span>{t(BASE_HEADER_PROFILE)}</span>
                                <div
                                    className={`w-4 group-hover:block hidden ${svgClassName}`}
                                    // bca-disable-line
                                    dangerouslySetInnerHTML={{__html: oneWayIcon}}
                                />
                            </div>
                        )}
                        <div
                            // eslint-disable-next-line max-len
                            className={`h-[30px] flex items-center rounded ${infoItemClassName} cursor-pointer justify-between px-3 py-1`}
                            onClick={handleLoginOut}
                        >
                            {t(BASE_HEADER_LOGOUT)}
                        </div>
                    </div>
                }
            >
                <div className="cursor-pointer h-full flex items-center">
                    {licenseTypeId === 1 && <img className="w-4 mr-1" src={vipIcon} />}
                    <div className={`whitespace-nowrap overflow-hidden overflow-ellipsis ${isJetbrains ? 'max-w-32' : ''}`}>
                        {username || ''}
                    </div>

                </div>
            </Tooltip>
        </div>
    );
}
