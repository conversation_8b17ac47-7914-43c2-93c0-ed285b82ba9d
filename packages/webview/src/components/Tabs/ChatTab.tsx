import {ComponentProps} from 'preact';
import {useCallback, useMemo, useState} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import {useTranslation} from 'react-i18next';
import {compact} from 'lodash';
import {FeatureName, isFeatureVisible} from '@shared/utils/features';
import refreshIcon from '@/assets/refresh.svg';
import addIcon from '@/assets/add.svg';
import {messageHandler} from '@/utils/messageHandler';
import {isInternal, isSaas} from '@/utils/features';
import {BASE_HEADER_CHAT, BASE_HEADER_PAIR} from '@/i18n/constants';
import {isJetbrains, isVSCode} from '@/config';
import {useExtensionConfig} from '@/hooks/useExtensionConfig';
import {useComatePairChatContext} from '../ComatePairChat/useComatePairChat';
import ToolbarButton from '../ToolbarButton';
import Modal from '../Modal';
import {CheckboxChangeEvent, CheckboxWithLabel} from '../Checkbox';
import {useAgentContext} from '../SmartAgent/AgentProvider';
import {Tabs} from '.';
import Menu from './Menu';

export enum ChatTabKey {
    CHAT = 'CHAT',
    PAIR = 'PAIR',
    LAB = 'LAB',
    AGENT = 'AGENT',
    KERNEL_DEMO = 'KERNEL_DEMO',
}

type ChatTabProps = Omit<ComponentProps<typeof Tabs>, 'items'> & {
    username?: string;
    isLogin: boolean;
};

const isDevelopment = $features.ENVIRONMENT === 'development';
export default function ChatTab(props: ChatTabProps) {
    const {
        hasUnReadedAutoWorkChatMessage,
        onlyShowActiveFileAdvice,
        handleOnlyShowActiveFileFlagChange,
    } = useComatePairChatContext();
    const {updateLatestConversations} = useAgentContext();
    const {config: {enableAgent}} = useExtensionConfig();
    const {onChange} = props;
    const [modalVisible, setModalVisible] = useState(false);
    // 准备下线助理模式
    const enableComatePair = false;
    const {t} = useTranslation();
    const itemWithDot = useMemo(
        () => {
            const chat = {
                label: t(BASE_HEADER_CHAT),
                key: ChatTabKey.CHAT,
            };
            const pair = {
                label: t(BASE_HEADER_PAIR),
                key: ChatTabKey.PAIR,
                dot: hasUnReadedAutoWorkChatMessage,
            };
            const agent = {
                label: '智能体助理',
                key: ChatTabKey.AGENT,
            };
            const kernelDemo = {
                label: 'Kernel Demo',
                key: ChatTabKey.KERNEL_DEMO,
            };
            return compact([
                chat,
                (isFeatureVisible(FeatureName.AGENT_BOT) && enableAgent) ? agent : null,
                isInternal && !isJetbrains && enableComatePair ? pair : null,
                isDevelopment ? kernelDemo : null,
                // TODO: 智能体是否增加小流量控制和版本控制
            ]);
        },
        [enableComatePair, hasUnReadedAutoWorkChatMessage, t, enableAgent]
    );

    const handleTabChange = useCallback(
        (key: string) => {
            onChange && onChange(key);
            if (key === ChatTabKey.AGENT) {
                updateLatestConversations();
            }
        },
        [onChange, updateLatestConversations]
    );

    const confirmRefreshMessage = useCallback(
        () => {
            setModalVisible(true);
        },
        []
    );

    const refreshMessage = useCallback(
        () => {
            messageHandler.send(EventMessage.ComatePairScanResultMessageSyncRequest);
        },
        []
    );

    const hanldeChange = useCallback(
        (e: CheckboxChangeEvent) => {
            handleOnlyShowActiveFileFlagChange((e.target as any).checked);
        },
        [handleOnlyShowActiveFileFlagChange]
    );

    const createNewComposerConversation = useCallback(
        () => {
            messageHandler.send(EventMessage.ChatLabActionEvent, {action: 'clear'});
        },
        []
    );

    const consumerClass = isJetbrains
        ? undefined
        : 'border-b border-[var(--comate-panel-border,#2B2C2A)]';

    return (
        <>
            <div
                // eslint-disable-next-line max-len
                className={`flex justify-between border-[var(--vscode-menu-border,#88888833)] relative ${consumerClass}`}
                style={{borderStyle: 'solid', marginTop: 5}}
            >
                <Tabs items={itemWithDot} {...props} onChange={handleTabChange} />
                {props.activeKey !== ChatTabKey.PAIR && isSaas && isVSCode && (
                    <Menu
                        username={props.username}
                        isLogin={props.isLogin}
                    />
                )}
                {props.activeKey === ChatTabKey.PAIR && (
                    <div className="absolute right-3" style={{top: 8}}>
                        <div className="flex items-center">
                            <CheckboxWithLabel
                                label="仅查看当前文件"
                                name="onlyShowActiveFile"
                                onChange={hanldeChange}
                                checked={onlyShowActiveFileAdvice}
                            />
                            <ToolbarButton
                                title="刷新，清除失效的记录"
                                placement="bottomRight"
                                icon={refreshIcon}
                                onClick={confirmRefreshMessage}
                                ariaTitle="刷新，清除失效的记录"
                            />
                        </div>
                    </div>
                )}
                {props.activeKey === ChatTabKey.LAB && (
                    <div className="absolute right-3" style={{top: 8}}>
                        <ToolbarButton
                            icon={addIcon}
                            onClick={createNewComposerConversation}
                            label="新建会话"
                            ariaTitle="新建会话"
                        />
                    </div>
                )}
            </div>
            {modalVisible && (
                <Modal
                    title="刷新后，已失效的文件结果将被清除，仅保留可用结果！"
                    description="确定刷新结果吗？"
                    onOk={refreshMessage}
                    // eslint-disable-next-line react/jsx-no-bind
                    closeModal={() => setModalVisible(false)}
                />
            )}
        </>
    );
}
