import {ReactNode} from 'preact/compat';
import {useCallback, useEffect} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import {useDerivedState} from './useDerivedState';
import './tabs.css';

interface TabItem {
    label: ReactNode;
    children?: ReactNode;
    /**
     * 是否显示红点
     */
    dot?: boolean;
    key: string;
}

interface Props {
    items: TabItem[];
    activeKey?: string;
    onChange?: (activeTabKey: string) => any;
}

export function Tabs({items, activeKey, onChange}: Props) {
    const [activeTabKey, setActiveTabKey] = useDerivedState(activeKey);

    const handleTabClick = useCallback(
        (activeTabKey: string) => {
            onChange?.(activeTabKey);
            setActiveTabKey(activeTabKey);
        },
        [onChange, setActiveTabKey]
    );

    return (
        <div className="comate-tabs">
            {items.map(tab => (
                <button
                    key={tab.key}
                    className={`${activeTabKey === tab.key ? 'comate-tab-active' : ''}`}
                    onClick={() => handleTabClick(tab.key)}
                    data-tab={tab.key}
                >
                    {tab.label}
                    {tab.dot ? <sup className="dot" /> : null}
                </button>
            ))}
            {items.map(tab => (
                tab.children && (
                    <div
                        key={tab.key}
                        className={`${activeTabKey === tab.key ? '' : 'hidden'}`}
                        id="tab1"
                    >
                        {tab.children}
                    </div>
                )
            ))}
        </div>
    );
}
