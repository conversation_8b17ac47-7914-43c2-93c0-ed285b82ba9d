body {
    overflow-x: hidden;
}
.menu-tooltip.rc-tooltip.rc-tooltip-zoom-appear,
.menu-tooltip.rc-tooltip.rc-tooltip-zoom-enter {
  opacity: 0;
}
.menu-tooltip.rc-tooltip.rc-tooltip-zoom-enter,
.menu-tooltip.rc-tooltip.rc-tooltip-zoom-leave {
  display: block;
}
.menu-tooltip .rc-tooltip-zoom-enter,
.menu-tooltip .rc-tooltip-zoom-appear {
  opacity: 0;
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.18, 0.89, 0.32, 1.28);
  animation-play-state: paused;
}
.menu-tooltip .rc-tooltip-zoom-leave {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.6, -0.3, 0.74, 0.05);
  animation-play-state: paused;
}
.menu-tooltip .rc-tooltip-zoom-enter.rc-tooltip-zoom-enter-active,
.menu-tooltip .rc-tooltip-zoom-appear.rc-tooltip-zoom-appear-active {
  animation-name: rcToolTipZoomIn;
  animation-play-state: running;
}
.menu-tooltip .rc-tooltip-zoom-leave.rc-tooltip-zoom-leave-active {
  animation-name: rcToolTipZoomOut;
  animation-play-state: running;
}
@keyframes rcToolTipZoomIn {
  0% {
    opacity: 0;
    transform-origin: 50% 50%;
    transform: scale(0, 0);
  }
  100% {
    opacity: 1;
    transform-origin: 50% 50%;
    transform: scale(1, 1);
  }
}
@keyframes rcToolTipZoomOut {
  0% {
    opacity: 1;
    transform-origin: 50% 50%;
    transform: scale(1, 1);
  }
  100% {
    opacity: 0;
    transform-origin: 50% 50%;
    transform: scale(0, 0);
  }
}
.menu-tooltip.rc-tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  visibility: visible;
  font-size: 12px;
  line-height: 1.5;
}
.menu-tooltip.rc-tooltip-hidden {
  display: none;
}
.menu-tooltip .rc-tooltip-placement-top,
.menu-tooltip .rc-tooltip-placement-topLeft,
.menu-tooltip .rc-tooltip-placement-topRight {
  padding: 5px 0 9px 0;
}
.menu-tooltip .rc-tooltip-placement-right,
.menu-tooltip .rc-tooltip-placement-rightTop,
.menu-tooltip .rc-tooltip-placement-rightBottom {
  padding: 0 5px 0 9px;
}
.menu-tooltip .rc-tooltip-placement-bottom,
.menu-tooltip .rc-tooltip-placement-bottomLeft,
.menu-tooltip .rc-tooltip-placement-bottomRight {
  padding: 9px 0 5px 0;
}
.menu-tooltip .rc-tooltip-placement-left,
.menu-tooltip .rc-tooltip-placement-leftTop,
.menu-tooltip .rc-tooltip-placement-leftBottom {
  padding: 0 9px 0 5px;
}
.menu-tooltip .rc-tooltip-inner {
  padding: 6px;
  color: #fff;
  text-align: left;
  text-decoration: none;
  background-color: #3B3D42;
  border-radius: 6px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.17);
  min-height: 34px;
  border: 1px solid #5A5C62;
}
.menu-tooltip .rc-tooltip-arrow {
  display: none;
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.menu-tooltip .rc-tooltip-placement-top .rc-tooltip-arrow,
.menu-tooltip .rc-tooltip-placement-topLeft .rc-tooltip-arrow,
.menu-tooltip .rc-tooltip-placement-topRight .rc-tooltip-arrow {
  bottom: 4px;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #373737;
}
.menu-tooltip .rc-tooltip-placement-top .rc-tooltip-arrow {
  left: 50%;
}
.menu-tooltip .rc-tooltip-placement-topLeft .rc-tooltip-arrow {
  left: 15%;
}
.menu-tooltip .rc-tooltip-placement-topRight .rc-tooltip-arrow {
  right: 15%;
}
.menu-tooltip .rc-tooltip-placement-right .rc-tooltip-arrow,
.menu-tooltip .rc-tooltip-placement-rightTop .rc-tooltip-arrow,
.menu-tooltip .rc-tooltip-placement-rightBottom .rc-tooltip-arrow {
  left: 4px;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #373737;
}
.menu-tooltip .rc-tooltip-placement-right .rc-tooltip-arrow {
  top: 50%;
}
.menu-tooltip .rc-tooltip-placement-rightTop .rc-tooltip-arrow {
  top: 15%;
  margin-top: 0;
}
.menu-tooltip .rc-tooltip-placement-rightBottom .rc-tooltip-arrow {
  bottom: 15%;
}
.menu-tooltip .rc-tooltip-placement-left .rc-tooltip-arrow,
.menu-tooltip .rc-tooltip-placement-leftTop .rc-tooltip-arrow,
.menu-tooltip .rc-tooltip-placement-leftBottom .rc-tooltip-arrow {
  right: 4px;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #373737;
}
.menu-tooltip .rc-tooltip-placement-left .rc-tooltip-arrow {
  top: 50%;
}
.menu-tooltip .rc-tooltip-placement-leftTop .rc-tooltip-arrow {
  top: 15%;
  margin-top: 0;
}
.menu-tooltip .rc-tooltip-placement-leftBottom .rc-tooltip-arrow {
  bottom: 15%;
}
.menu-tooltip .rc-tooltip-placement-bottom .rc-tooltip-arrow,
.menu-tooltip .rc-tooltip-placement-bottomLeft .rc-tooltip-arrow,
.menu-tooltip .rc-tooltip-placement-bottomRight .rc-tooltip-arrow {
  top: 4px;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #373737;
}
.menu-tooltip .rc-tooltip-placement-bottom .rc-tooltip-arrow {
  left: 50%;
}
.menu-tooltip .rc-tooltip-placement-bottomLeft .rc-tooltip-arrow {
  left: 15%;
}
.menu-tooltip .rc-tooltip-placement-bottomRight .rc-tooltip-arrow {
  right: 15%;
}
