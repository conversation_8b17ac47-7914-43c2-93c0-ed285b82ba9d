import {useEffect, useRef} from 'preact/hooks';
import {DehydratedMessage} from '@shared/protocols';
import Suggestions from './Suggestions';
import {useSuggestion} from './SuggestionProvider';

interface Props {
    messages: DehydratedMessage[];
    hasMessageInProgress: boolean;
}

export default function ChatSuggestion({messages, hasMessageInProgress}: Props) {
    const prevMessagesRef = useRef<DehydratedMessage[]>();
    const {suggestions, onSuggestionClick, clearSuggestions, resetSuggestions} = useSuggestion();

    useEffect(
        () => {
            const prevMessages = prevMessagesRef.current;
            if ((!prevMessages || !prevMessages.length) && messages.length) {
                clearSuggestions(); // 从空到非空
            }
            else if ((prevMessages && prevMessages.length) && !messages.length) {
                resetSuggestions(); // 从非空到空
            }
            prevMessagesRef.current = messages;
        },
        [messages, clearSuggestions, resetSuggestions]
    );

    if (hasMessageInProgress) {
        return null;
    }

    return <Suggestions items={suggestions} onClick={onSuggestionClick!} />;
}
