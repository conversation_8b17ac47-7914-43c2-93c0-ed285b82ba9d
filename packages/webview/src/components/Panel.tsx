import {useEffect, useState} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import {FlawDetail} from './SmartAgent/SecuBotView/components/FlawDetail';

export default function Panel() {
    const [panelData, setPanelData] = useState<{type: string, data: any}>();
    useEffect(
        () => {
            function handleMessage(event) {
                const message = event.data;
                if (message.scope === EventMessage.PanelDataListenEvent) {
                    setPanelData(message.data);
                }
            }
            window.addEventListener('message', handleMessage);
            messageHandler.send(EventMessage.PanelReadyEvent);
            return () => {
                window.removeEventListener('message', handleMessage);
            };
        },
        []
    );

    switch (panelData?.type) {
        case 'secubot':
            return <FlawDetail {...panelData.data} />;
        case 'testbot':
            return (
                <div>测试一下单测双屏</div>
            );
        default:
            return null;
    }


}
