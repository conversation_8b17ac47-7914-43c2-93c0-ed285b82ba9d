/* bca-disable */
import {ReactNode, useCallback, useEffect, useMemo, useRef} from 'preact/compat';
import successIcon from '@/assets/successCircleOutline.svg';
import closeIcon from '@/assets/close.svg';
import Tooltip, {Props} from '../Tooltip';
import './index.css';

interface PopConfirmProps extends Omit<Props, 'overlay'> {
    type: 'success' | 'error' | 'warning';
    title?: ReactNode;
    content?: ReactNode;
    setVisible: (visible: boolean) => void;
}

export const PopConfirm = ({
    title,
    content,
    visible,
    setVisible,
    type,
    ...props
}: PopConfirmProps) => {
    const popoverRef = useRef<HTMLDivElement>(null);
    const closeTooltip = useCallback(
        () => {
            setVisible(false);
        },
        [setVisible]
    );

    const mergedOverlay = useMemo(
        () => {
            const icon = type === 'success' ? successIcon : successIcon;
            return (
                <div className="relative">
                    <div
                        className="popconfirm-icon-close absolute top-[-4px] right-[-4px]"
                        dangerouslySetInnerHTML={{__html: closeIcon}}
                        onClick={closeTooltip}
                    />
                    <div className="flex justify-center items-start gap-2">
                        <div className="popconfirm-icon" dangerouslySetInnerHTML={{__html: icon}}></div>
                        <div className="popconfirm-content">
                            <div>
                                {title}
                            </div>
                            <div>
                                {content}
                            </div>
                        </div>
                    </div>
                </div>
            );
        },
        [type, closeTooltip, title, content]
    );

    useEffect(
        () => {
            const closeWhenClickOutside = (e: MouseEvent) => {
                const target = e.target as HTMLElement;
                if (!popoverRef.current?.contains(target)) {
                    setVisible(false);
                }
            };
            document.addEventListener('click', closeWhenClickOutside);
            return () => document.removeEventListener('click', closeWhenClickOutside);
        },
        [setVisible]
    );

    return (
        <Tooltip
            {...props}
            showArrow
            destroyTooltipOnHide
            overlay={mergedOverlay}
            overlayClassName={`popconfirm popconfirm-${type}`}
            visible={visible}
        />
    );
};
