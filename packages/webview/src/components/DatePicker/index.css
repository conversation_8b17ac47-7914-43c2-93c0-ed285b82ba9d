body.vscode-dark,
body.dark {
    --comate-link-color-15:rgba(23, 195, 229, 0.15);
    --comate-link-color-8: rgba(23, 195, 229, 0.08);
    --comate-rowIcon-color:rgba(212,212,212);

}

.vscode-light,
body.light {
    --comate-link-color-15:rgba(0, 167, 209, 0.15);
    --comate-link-color-8: rgba(0, 167, 209, 0.08);
    --comate-rowIcon-color:rgba(0,0,0,0.5);
}
.react-datepicker{
  margin-top: -6px;
  border: 1px solid var(--comate-tooltip-border);
  border-radius: 4px;
  overflow: hidden;
  min-width: 180px;
  max-width: 576px !important;
}

.react-datepicker__month-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range),
.react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,
.react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range){
  background-color: var(--comate-link-color-8);
}

.react-datepicker-wrapper{
    width: 100%;
    min-width: 180px;
}

.react-datepicker__month-container,
.react-datepicker__year--container {
    background-color: var(--comate-editor-background);
    border: var(--comate-tooltip-border);
    color: var(--comate-editor-foreground);
    height: 100%;
}
.react-datepicker__month{
    border: var(--comate-tooltip-border);
    margin-top: 0;
}
.react-datepicker__day-name{
  height: 32px;
}
.react-datepicker__day:not(.react-datepicker__day-name){
  height: 28px;
}
.react-datepicker__day-name, .react-datepicker__day, .react-datepicker__time-name{
    color: var(--comate-editor-foreground);
}

/* 非当前月日期的样式 */
.react-datepicker__day--outside-month{
  opacity: 0.5;
}
/* 禁用日期的样式 */
.react-datepicker__day--disabled,
.react-datepicker__month-text--disabled,
.react-datepicker__year-text--disabled{
  opacity: 0.3;
  cursor: not-allowed !important;
}
.react-datepicker__year-text--disabled:hover{
  background-color: transparent !important;
}

/* hover日期的样式 */
.react-datepicker__day:not([aria-disabled=true]):hover,
.react-datepicker__month-text:not([aria-disabled=true]):hover,
.react-datepicker__year-text:not(.react-datepicker__year-text--disabled):hover{
  outline: 1px solid var(--comate-link-color);
  background-color:var(--comate-link-color-15)
}

.react-datepicker__day--keyboard-selected,
.react-datepicker__month-text--keyboard-selected{
    outline: 1px solid var(--comate-link-color);
    background-color: transparent;
    color:var(--comate-link-color);
    border-radius: 4px;
}

.react-datepicker__day--today,
.react-datepicker__month-text--today,
.react-datepicker__year-text--today{
  font-weight:normal;
}

/* 范围内的日期样式 */
 .react-datepicker__day--in-range,
 .react-datepicker__month-text--in-range,
 .react-datepicker__year-text--in-range{
  background-color:var(--comate-link-color-8) !important;
  color:var(--comate-link-color) !important;
}

.react-datepicker__year-text--today{
  outline: 1px solid var(--comate-link-color);
  background-color: transparent;
  color:var(--comate-link-color);
  border-radius: 4px;
}

/* 开始、结束日期样式 */
.react-datepicker__day--in-selecting-range.react-datepicker__day--selecting-range-start,
.react-datepicker__day--in-selecting-range.react-datepicker__day--selecting-range-end,
.react-datepicker__year-text--selecting-range-start,
.react-datepicker__year-text--selecting-range-end,
.react-datepicker__day--range-start,
.react-datepicker__day--range-end,
.react-datepicker__month-text--range-start,
.react-datepicker__month-text--range-end,
.react-datepicker__year-text--range-start,
.react-datepicker__year-text--range-end{
  outline: 1px solid var(--comate-link-color);
  background-color:var(--comate-link-color-15);
  color:var(--comate-link-color);
}

  .react-datepicker-popper {
    width: calc(100% - 32px) !important;
    min-width: 180px !important;
  }

  .react-datepicker {
    width: 100% !important;
  }

  .react-datepicker__header {
    width: 100%;
    background-color: var(--comate-editor-background);
    border: none;
  }

.react-datepicker__day-names{
  opacity: 0.3;
    margin-left: 0.4em;
    margin-right: 0.4em;
}

.react-datepicker__day,
.react-datepicker__day-name {
    width: calc((100% - (6 * 4px)) / 7); /* 假设间距是2px */
    margin: 2px; /* 假设间距是2px */
}

/* 月*/
.react-datepicker__month-container {
  width: 100%;
}
.react-datepicker__month-wrapper {
  display: flex;
  justify-content: space-between;
  padding: 0 8px 8px 8px;
}

.react-datepicker__month .react-datepicker__month-text {
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 年 */
.react-datepicker__year-wrapper {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  grid-row-gap: 16px;
  padding: 0 8px 8px 8px;
  max-width: none !important;
}
.react-datepicker__year-text {
  width: auto !important;
  margin: 16px;
}

.react-datepicker__year-text--in-selecting-range:not(.react-datepicker__day--in-range, .react-datepicker__month-text--in-range, .react-datepicker__quarter-text--in-range, .react-datepicker__year-text--in-range){
  background-color: var(--comate-link-color-8) !important;
}

/* 确保容器有足够的宽度 */
.react-datepicker__year {
  margin: 0 !important;
  width: 100% !important;
}

.react-datepicker__year .react-datepicker__year-text{
  display: flex;
  justify-content: center;
  align-items: center;
  height: 28px;
}

.input-outline{
  outline: none !important;
}