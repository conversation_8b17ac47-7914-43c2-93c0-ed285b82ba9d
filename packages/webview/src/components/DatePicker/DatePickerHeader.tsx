/* eslint-disable max-len */
/* bca-disable */
import {subDays} from 'date-fns';
import monthLeftArrowIcon from '../../assets/pickerMonthLeftRow.svg';
import monthRightArrowIcon from '../../assets/pickerMonthRightRow.svg';
import yearLeftArrowIcon from '../../assets/pickerYearLeftRow.svg';
import yearRightArrowIcon from '../../assets/pickerYearRightRow.svg';
import {useMemo} from 'preact/hooks';

export interface DatePickerHeaderProps {
    date: Date;
    changeYear: (year: number) => void;
    changeMonth: (month: number) => void;
    decreaseMonth: () => void;
    increaseMonth: () => void;
    decreaseYear: () => void;
    increaseYear: () => void;
    prevYearButtonDisabled: boolean;
    nextYearButtonDisabled: boolean;
    prevMonthButtonDisabled: boolean;
    nextMonthButtonDisabled: boolean;
    setDateRange: (dateRange: [Date | null, Date | null]) => void;
    isYearPickerVisible: boolean;
    isMonthYearPickerVisible: boolean;
    setIsMonthYearPickerVisible: (isMonthYearPickerVisible: boolean) => void;
    setIsYearPickerVisible: (isYearPickerVisible: boolean) => void;
}

const prevIcon = (
    <span
        className="w-[14px] h-[14px] inline-block text-[var(--comate-rowIcon-color)]"
        // bca-disable-line
        dangerouslySetInnerHTML={{__html: monthLeftArrowIcon}}
    />
);
const nextIcon = (
    <span
        className="w-[14px] h-[14px] inline-block text-[var(--comate-rowIcon-color)]"
        // bca-disable-line
        dangerouslySetInnerHTML={{__html: monthRightArrowIcon}}
    />
);

const prevYearIcon = (
    <span
        className="w-[14px] h-[14px] inline-block text-[var(--comate-rowIcon-color)]"
        // bca-disable-line
        dangerouslySetInnerHTML={{__html: yearLeftArrowIcon}}
    />
);
const nextYearIcon = (
    <span
        className="w-[14px] h-[14px] inline-block text-[var(--comate-rowIcon-color)]"
        // bca-disable-line
        dangerouslySetInnerHTML={{__html: yearRightArrowIcon}}
    />
);

function DatePickerHeader({
    date,
    decreaseMonth,
    increaseMonth,
    decreaseYear,
    increaseYear,
    prevYearButtonDisabled,
    nextYearButtonDisabled,
    prevMonthButtonDisabled,
    nextMonthButtonDisabled,
    setDateRange,
    isYearPickerVisible,
    isMonthYearPickerVisible,
    setIsMonthYearPickerVisible,
    setIsYearPickerVisible,
}: DatePickerHeaderProps) {
    const handleClick = (days: number) => {
        const today = new Date();
        const startDate = subDays(today, days - 1);
        setDateRange([startDate, today]);
    };

    const renderQuickSelectionButtons = useMemo(
        () => (
            <div className="mb-2.5 ml-3 flex gap-2 mt-[-6px] text-nowrap text-[12px] h-5 w-[50%] opacity-70">
                <button
                    className="px-[6px] rounded cursor-pointer hover:bg-[var(--comate-link-color-15)] hover:text-[var(--comate-link-color)]"
                    onClick={() => handleClick(7)}
                >
                    近七天
                </button>
                <button
                    className="px-[6px] rounded cursor-pointer hover:bg-[var(--comate-link-color-15)] hover:text-[var(--comate-link-color)]"
                    onClick={() => handleClick(30)}
                >
                    近30天
                </button>
                <button
                    className="px-[6px] rounded cursor-pointer hover:bg-[var(--comate-link-color-15)] hover:text-[var(--comate-link-color)]"
                    onClick={() => handleClick(90)}
                >
                    近三个月
                </button>
            </div>
        ),
        [handleClick]
    );

    return (
        <div className="react-datepicker__header ">
            {renderQuickSelectionButtons}
            <div className="w-full px-3 flex text-center items-center justify-between h-10 border-y-[1px] border-y-[var(--comate-tooltip-border)]">
                {isYearPickerVisible
                    ? (
                        // 年份选择界面的头部
                        <>
                            <button
                                onClick={decreaseYear}
                                disabled={prevYearButtonDisabled}
                                className={`${prevYearButtonDisabled && 'cursor-not-allowed'} h-[14px]`}
                            >
                                {prevYearIcon}
                            </button>
                            <span className="my-2.5 font-[600] text-[14px]`}">
                                {`${date.getFullYear() - 8}年 - ${date.getFullYear() + 7}年`}
                            </span>
                            <button
                                onClick={increaseYear}
                                disabled={nextYearButtonDisabled}
                                className={`${nextYearButtonDisabled && 'cursor-not-allowed'}  h-[14px]`}
                            >
                                {nextYearIcon}
                            </button>
                        </>
                    )
                    : isMonthYearPickerVisible
                    ? (
                        // 月份选择界面的头部
                        <>
                            <button
                                onClick={decreaseYear}
                                disabled={prevYearButtonDisabled}
                                className={`${prevYearButtonDisabled && 'cursor-not-allowed'}  h-[14px]`}
                            >
                                {prevYearIcon}
                            </button>

                            <div>
                                <span
                                    className="my-0.5 cursor-pointer font-[600] text-[14px] hover:text-[var(--comate-link-color)]"
                                    onClick={() => {
                                        setIsYearPickerVisible(true);
                                        setIsMonthYearPickerVisible(false);
                                    }}
                                >
                                    {`${date.getFullYear()}年 `}
                                </span>
                                <span className="my-0.5 font-[600] text-[14px]">
                                    {`${String(date.getMonth() + 1).padStart(2, '0')}月`}
                                </span>
                            </div>

                            <button
                                onClick={increaseYear}
                                disabled={nextYearButtonDisabled}
                                className={`${nextYearButtonDisabled && 'cursor-not-allowed'} h-[14px]`}
                            >
                                {nextYearIcon}
                            </button>
                        </>
                    )
                    : (
                        // 日期选择界面的头部
                        <>
                            <div className="h-[14px]">
                                <button
                                    onClick={decreaseYear}
                                    disabled={prevYearButtonDisabled}
                                    className={`${prevYearButtonDisabled && 'cursor-not-allowed'} h-[14px]`}
                                >
                                    {prevYearIcon}
                                </button>
                                <button
                                    onClick={decreaseMonth}
                                    disabled={prevMonthButtonDisabled}
                                    className={`${prevMonthButtonDisabled && 'cursor-not-allowed'}  h-[14px]`}
                                >
                                    {prevIcon}
                                </button>
                            </div>
                            <div>
                                <span
                                    className="my-0.5 cursor-pointer font-[600] text-[14px] hover:text-[var(--comate-link-color)]"
                                    onClick={() => {
                                        setIsYearPickerVisible(true);
                                        setIsMonthYearPickerVisible(false);
                                    }}
                                >
                                    {`${date.getFullYear()}年 `}
                                </span>
                                <span
                                    className="my-0.5 cursor-pointer font-[600] text-[14px] hover:text-[var(--comate-link-color)]"
                                    onClick={() => {
                                        setIsMonthYearPickerVisible(true);
                                        setIsYearPickerVisible(false);
                                    }}
                                >
                                    {`${String(date.getMonth() + 1).padStart(2, '0')}月`}
                                </span>
                            </div>

                            <div className="h-[14px]">
                                <button
                                    onClick={increaseMonth}
                                    disabled={nextMonthButtonDisabled}
                                    className={`${nextMonthButtonDisabled && 'cursor-not-allowed'}  h-[14px]`}
                                >
                                    {nextIcon}
                                </button>
                                <button
                                    onClick={increaseYear}
                                    disabled={nextYearButtonDisabled}
                                    className={`${nextYearButtonDisabled && 'cursor-not-allowed'}  h-[14px]`}
                                >
                                    {nextYearIcon}
                                </button>
                            </div>
                        </>
                    )}
            </div>
        </div>
    );
}

export default DatePickerHeader;
