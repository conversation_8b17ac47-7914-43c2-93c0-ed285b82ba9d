// eslint-disable-next-line max-len
import {FC, useState, useEffect, useRef} from 'react';
import RcDatePicker, {DatePickerProps as RcDatePickerProps} from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import {format, parse, isValid} from 'date-fns';
import {zhCN} from 'date-fns/locale';
import calendarIcon from '../../assets/calendar.svg';
import clearIcon from '../../assets/clearSearch.svg';
import './index.css';
import DatePickerHeader from './DatePickerHeader';
import {DatePickerHeaderProps} from './DatePickerHeader';

interface CustomDatePickerProps {
    dateRange: [Date | null, Date | null];
    setDateRange: (dateRange: [Date | null, Date | null]) => void;
}

export type DatePickerProps = Omit<RcDatePickerProps, keyof CustomDatePickerProps> & CustomDatePickerProps;
const DatePicker = RcDatePicker as FC<DatePickerProps>;

export default ({dateRange, setDateRange}: DatePickerProps) => {
    const [startDate, endDate] = dateRange;
    const [isFocused, setIsFocused] = useState(false);
    const [isMonthYearPickerVisible, setIsMonthYearPickerVisible] = useState(false);
    const [isYearPickerVisible, setIsYearPickerVisible] = useState(false);
    const [isManualInput, setIsManualInput] = useState(false);
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const datePickerRef = useRef<HTMLDivElement>(null);

    const handleClear = (event: MouseEvent) => {
        event.stopPropagation();
        setDateRange([null, null]);
        setIsFocused(false);
        setIsCalendarOpen(false);
        setIsYearPickerVisible(false);
        setIsMonthYearPickerVisible(false);
    };

    useEffect(
        () => {
            const handleClickOutside = (event: MouseEvent) => {
                if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
                    setIsFocused(false);
                    setIsCalendarOpen(false);
                    // 在失焦时重置选择器状态
                    setIsYearPickerVisible(false);
                    setIsMonthYearPickerVisible(false);
                }
                if (datePickerRef.current && datePickerRef.current.contains(event.target as Node)) {
                    setIsFocused(true);
                }
            };

            document.addEventListener('mousedown', handleClickOutside);
            return () => {
                document.removeEventListener('mousedown', handleClickOutside);
            };
        },
        []
    );

    // 日期格式化函数
    const formatDate = date => {
        if (!date) {
            return '';
        }
        return format(date, 'yyyy-MM-dd');
    };

    // 日期解析函数
    const parseDate = dateString => {
        if (!dateString) {
            return null;
        }
        // 使用正则表达式检查日期字符串的格式
        const dateRegex = [
            /^(\d{4})-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/,
            /^(\d{4})\.(0[1-9]|1[0-2])\.(0[1-9]|[12]\d|3[01])$/,
            /^(\d{4})\/(0[1-9]|1[0-2])\/(0[1-9]|[12]\d|3[01])$/,
        ];

        // 使用 some 方法检查是否至少有一个正则表达式匹配
        if (!dateRegex.some(regex => regex.test(dateString))) {
            return null;
        }
        // 根据匹配到的格式进行解析
        let parsedDate: Date | null = null;
        if (dateRegex[0].test(dateString)) {
            parsedDate = parse(dateString, 'yyyy-MM-dd', new Date());
        }
        else if (dateRegex[1].test(dateString)) {
            parsedDate = parse(dateString.replace(/\./g, '-'), 'yyyy-MM-dd', new Date());
        }
        else if (dateRegex[2].test(dateString)) {
            parsedDate = parse(dateString.replace(/\//g, '-'), 'yyyy-MM-dd', new Date());
        }

        // 验证解析后的日期是否有效
        return isValid(parsedDate) ? parsedDate : null;
    };

    // 处理手动输入
    const handleDateInput = (value, isStart) => {
        setIsManualInput(true); // 标记正在手动输入
        const parsedDate = parseDate(value);

        if (!parsedDate || parsedDate > new Date()) {
            return;
        }

        if (isStart) {
            if (!endDate || parsedDate <= endDate) {
                setDateRange([parsedDate, endDate]);
            }
        }
        else {
            if (!startDate || parsedDate >= startDate) {
                setDateRange([startDate, parsedDate]);
            }
        }
    };

    const renderCustomHeader = (headerProps: DatePickerHeaderProps) => (
        <DatePickerHeader
            {...headerProps}
            setDateRange={setDateRange}
            setIsMonthYearPickerVisible={setIsMonthYearPickerVisible}
            setIsYearPickerVisible={setIsYearPickerVisible}
            isYearPickerVisible={isYearPickerVisible}
            isMonthYearPickerVisible={isMonthYearPickerVisible}
        />
    );

    const onClickOutside = () => {
        if (!datePickerRef.current?.contains(document.activeElement)) {
            setIsCalendarOpen(false);
        }
    };

    const onChange = (dates: [Date | null, Date | null]) => {
        // 只有在非手动输入状态下才处理日历选择
        if (!isManualInput) {
            !isMonthYearPickerVisible && !isYearPickerVisible
                && setDateRange(dates);
            isYearPickerVisible && setIsYearPickerVisible(false);
            isYearPickerVisible && setIsMonthYearPickerVisible(true);
            isMonthYearPickerVisible && setIsMonthYearPickerVisible(false);
        }
    };

    return (
        <div ref={datePickerRef}>
            <DatePicker
                open={isCalendarOpen}
                onInputClick={() => {setIsCalendarOpen(true)}}
                onClickOutside={onClickOutside}
                popperClassName="custom-popper"
                maxDate={new Date()}
                openToDate={startDate || new Date()}
                renderCustomHeader={renderCustomHeader}
                showMonthYearPicker={isMonthYearPickerVisible}
                showYearPicker={isYearPickerVisible}
                showPopperArrow={false}
                selectsRange
                startDate={startDate}
                endDate={endDate}
                onChange={onChange}
                locale={zhCN}
                yearItemNumber={16}
                customInput={
                    <div
                        tabIndex={0}
                        className={`flex items-center border bg-[var(--comate-editor-background)] rounded overflow-hidden w-full relative
                                    h-8 cursor-pointer ${isFocused ? 'border-[var(--comate-link-color)]' : 'border-[var(--comate-panel-border)]'
                                }
                        `}
                    >
                        {/* 图标元素，使用绝对定位放置在预留空间内 */}
                        <span
                            className="w-[14px] h-[14px] inline-block mx-2"
                            // bca-disable-line
                            dangerouslySetInnerHTML={{__html: calendarIcon}}
                        />

                        {/* 起始时间输入框 */}
                        <input
                            onClick={e => {
                                setIsCalendarOpen(true);
                                (e.target as HTMLInputElement).focus();
                            }}
                            onChange={e => {
                                handleDateInput((e.target as HTMLInputElement)?.value, true);
                            }}
                            onBlur={e => {
                                const parsedDate = parseDate((e.target as HTMLInputElement).value);
                                if (!parsedDate) {
                                    (e.target as HTMLInputElement).value = formatDate(startDate);
                                }
                                setIsManualInput(false); // 重置手动输入状态
                            }}
                            onFocus={() => setIsManualInput(true)}
                            className="input-outline flex-1 cursor-text border-none bg-transparent min-w-0 placeholder:opacity-50 placeholder:text-[var(--comate-editor-foreground)]"
                            placeholder="开始时间"
                            value={formatDate(startDate)}
                        />

                        {/* 中间波浪线 */}
                        <span className="mx-2 opacity-50">~</span>

                        {/* 终止时间输入框 */}

                        <input
                            onClick={e => {
                                (e.target as HTMLInputElement).focus();
                                setIsCalendarOpen(true);
                            }}
                            onChange={e => {
                                handleDateInput((e.target as HTMLInputElement)?.value, false);
                            }}
                            onFocus={() => setIsManualInput(true)}
                            onBlur={e => {
                                const parsedDate = parseDate((e.target as HTMLInputElement).value);
                                if (!parsedDate) {
                                    (e.target as HTMLInputElement).value = formatDate(endDate);
                                }
                                setIsManualInput(false); // 重置手动输入状态
                            }}
                            className="input-outline flex-1 cursor-text border-none bg-transparent min-w-0 placeholder:opacity-50 placeholder:text-[var(--comate-editor-foreground)]"
                            placeholder="结束时间"
                            value={formatDate(endDate)}
                        />

                        {(startDate || endDate) && (
                            <span
                                className="w-[14px] h-[14px] inline-block mx-2 cursor-pointer hover:bg-[var(--comate-icon-hoverBackground)] rounded"
                                // bca-disable-line
                                dangerouslySetInnerHTML={{__html: clearIcon}}
                                onClick={handleClear}
                            />
                        )}
                    </div>
                }
            />
        </div>
    );
};
