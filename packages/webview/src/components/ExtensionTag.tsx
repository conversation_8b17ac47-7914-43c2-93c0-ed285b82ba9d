/* bca-disable */
/* eslint-disable max-len */
import {forwardRef} from 'preact/compat';

interface Props {
    refWidth?: number;
    currentCommand: string | undefined;
}

const ExtensionTag = forwardRef<HTMLSpanElement, Props>(
    function ExtensionTag({refWidth, currentCommand}: Props, ref) {
        return (
            <span ref={ref} className="absolute left-3 top-0 flex items-center">
                {currentCommand && (
                    <div
                        style={{left: 32}}
                        className="font-pingfang inline-flex min-w-fit whitespace-nowrap top-[8px] rounded px-1 bg-gradient-to-r from-[#6391F9] to-[#02CABD] bg-clip-text text-transparent"
                    >
                        <span className="pr-1">/</span>
                        <span style={{maxWidth: (refWidth ?? 300) - 48 - 40}} className="truncate">
                            {currentCommand}
                        </span>
                    </div>
                )}
            </span>
        );
    }
);

export default ExtensionTag;
