/* bca-disable */
import cx from 'classnames';
import loadingIcon from '@/assets/agent/loading.svg';
import loadingFinishedIcon from '@/assets/agent/lodingFinished.svg';
interface Props {
    className?: string;
    width?: number;
    content: string;
    loading?: boolean;
}
export const LoadingText = ({content, loading = true, className}: Props) => {
    return (
        <div className={cx(className)}>
            {loading
                ? (
                    <div className="text-wrap whitespace-normal">
                        <span
                            className="w-4 h-4 inline-block align-text-bottom shark-txt animate-shark bg-[#999A9E] bg-clip-text text-transparent"
                            dangerouslySetInnerHTML={{__html: loadingIcon}}
                        >
                        </span>
                        <span className="ml-1 shark-txt animate-shark bg-[#999A9E] bg-clip-text text-transparent">
                            {content}
                        </span>
                    </div>
                )
                : (
                    <div className="text-wrap whitespace-normal">
                        <span
                            className="w-4 h-4 inline-block align-middle"
                            dangerouslySetInnerHTML={{__html: loadingFinishedIcon}}
                        >
                        </span>
                        <span className="text-[#999A9E] ml-1">
                            {content}
                        </span>
                    </div>
                )}
        </div>
    );
};
