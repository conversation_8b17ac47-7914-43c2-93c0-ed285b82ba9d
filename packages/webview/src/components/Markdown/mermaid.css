body.light .chat-mermaid,
body.vscode-light .chat-mermaid {
    --mermaid-background: #ebeced;
    --mermaid-flowchart-node-text-color: #fff;
    --mermaid-flowchart-node-fill-color: #81A2EB;
    --mermaid-flowchart-node-stroke-color: #4080FF;
    --mermaid-flowchart-edge-line-color: #8F8F8F;
    --mermaid-flowchart-edge-line-width: 1.5px;
    --mermaid-flowchart-edge-label-color: #8F8F8F;
}

body.dark .chat-mermaid,
body.vscode-dark .chat-mermaid {
    --mermaid-background: #2a2a2b;
    --mermaid-flowchart-node-text-color: #fff;
    /* 设计给的 rgba(98, 155, 255, 0.4); 但是时序图在透明度下，线条会穿过去，只在dark模式下有这个问题 */
    --mermaid-flowchart-node-fill-color: #405780;
    --mermaid-flowchart-node-stroke-color: rgba(98, 155, 255, 0.4);
    --mermaid-flowchart-edge-line-color: #7C7D80;
    --mermaid-flowchart-edge-line-width: 1.5px;
    --mermaid-flowchart-edge-label-color: #999A9E;
}

.chat-mermaid {
    display: flex;
    justify-content: center;
    background: var(--mermaid-background);
    border-radius: 4px;
    padding: 20px 0;
}

.chat-mermaid svg {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;
}

.chat-mermaid text.actor > tspan {
    fill: var(--mermaid-flowchart-node-text-color) !important;
}

.chat-mermaid .nodeLabel {
    color: var(--mermaid-flowchart-node-text-color) !important;
    font-size: 13px;
}

.chat-mermaid .messageText,
.chat-mermaid .edgeLabel {
    color: var(--mermaid-flowchart-edge-label-color) !important;
    fill: var(--mermaid-flowchart-edge-label-color) !important;
    background-color: var(--mermaid-background) !important;
    font-size: 11px;
    padding: 4px;
}

.chat-mermaid [class*="messageLine"],
.chat-mermaid .flowchart-link {
    stroke: var(--mermaid-flowchart-edge-label-color) !important;
}

.chat-mermaid #mermaid #mermaid_classDiagram-lollipopStart
.chat-mermaid #mermaid #mermaid_classDiagram-lollipopEnd,
.chat-mermaid #mermaid #mermaid_classDiagram-dependencyStart,
.chat-mermaid #mermaid #mermaid_classDiagram-dependencyEnd,
.chat-mermaid #mermaid #mermaid_classDiagram-compositionStart,
.chat-mermaid #mermaid #mermaid_classDiagram-compositionEnd,
.chat-mermaid #mermaid #mermaid_classDiagram-extensionStart,
.chat-mermaid #mermaid #mermaid_classDiagram-extensionEnd,
.chat-mermaid #mermaid #mermaid_classDiagram-aggregationStart,
.chat-mermaid #mermaid #mermaid_classDiagram-aggregationEnd,
.chat-mermaid #mermaid #mermaid_flowchart-pointStart,
.chat-mermaid #mermaid #mermaid_flowchart-pointEnd {
    fill: var(--mermaid-flowchart-edge-label-color) !important;
    stroke: var(--mermaid-flowchart-edge-label-color) !important;
}

.chat-mermaid #computer path,
.chat-mermaid #clock path,
.chat-mermaid #database path,
.chat-mermaid #arrowhead path,
.chat-mermaid #sequencenumber path,
.chat-mermaid #crosshead path,
.chat-mermaid #filled-head path {
    fill: var(--mermaid-flowchart-edge-label-color) !important;
    stroke: var(--mermaid-flowchart-edge-label-color) !important;
    transform: scale(0.5) translate(8px, 5px);
}

#mermaid #mermaid_flowchart-pointStart path,
#mermaid #mermaid_flowchart-pointEnd path {
    transform: scale(0.5) translate(8px, 5px);
}

#mermaid .edge-thickness-normal {
    stroke-width: 1.5px !important;
}

.chat-mermaid .actor,
.chat-mermaid .divider,
.chat-mermaid .node rect,
.chat-mermaid .node circle,
.chat-mermaid .node ellipse,
.chat-mermaid .node polygon,
.chat-mermaid .node path {
    fill: var(--mermaid-flowchart-node-fill-color) !important;
    stroke: var(--mermaid-flowchart-node-stroke-color) !important;
}

.mermaid-container:not(:hover) .mermaid-btn {
    display: none;
}