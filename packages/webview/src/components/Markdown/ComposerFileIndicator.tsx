import {AcceptState, ComposerCodeStatus, ComposerFileAction} from '@shared/composer';
import {useCallback, useEffect, useMemo, useRef, useState} from 'preact/hooks';
import cx from 'classnames';
import checkIcon from '@/assets/check.svg';
import closeIcon from '@/assets/close.svg';
import Loading from '../Loading';
import {ComposerPreviewIndicator, FileAction} from './ComposerPreviewIndicator';
import './ComposerFileIndicator.css';

export {FileAction};
interface Props {
    messageId: number | string;
    action: ComposerFileAction;
    content: string;
    filePath: string;
    accepted: AcceptState;
    status: ComposerCodeStatus;
    enableFileOperation?: boolean;
    onFileClick?: (action: FileAction) => void;
}

const generateAction = (
    onFileClick: Props['onFileClick'],
    id: number | string,
    action: FileAction['action'],
    filePath: string
) => {
    return (e: any) => {
        e.stopPropagation();
        onFileClick?.({id, filePath: decodeURI(filePath), action});
    };
};

function AcceptStatus({accepted, showLabel}: {showLabel?: boolean, accepted: AcceptState}) {
    switch (accepted) {
        case AcceptState.ACCEPT:
            return (
                <div className="flex items-center gap-1 text-green-500">
                    <div
                        className="composer-file-action-status w-4 h-4 text-green-500"
                        // bca-disable-line
                        dangerouslySetInnerHTML={{__html: checkIcon}}
                    />
                    {showLabel && <span>已采纳</span>}
                </div>
            );
        case AcceptState.REJECT:
            return (
                <div className="flex items-center gap-1 text-xs text-red-500">
                    <div
                        className="composer-file-action-status w-4 h-4 "
                        // bca-disable-line
                        dangerouslySetInnerHTML={{__html: closeIcon}}
                    />
                    {showLabel && <span>已放弃</span>}
                </div>
            );
        default:
            return <span className="composer-file-action-status composer-file-action-untouched-dot bg-green-500" />;
    }
}

export function AcceptBtnGroup({
    onFileClick,
    messageId,
    accepted,
    filePath,
    showLabel,
    enableFileOperation,
}: {showLabel?: boolean} & Pick<Props, 'onFileClick' | 'messageId' | 'accepted' | 'filePath' | 'enableFileOperation'>) {
    const {accept, abandon} = useMemo(
        () => ({
            accept: generateAction(onFileClick, messageId, 'file-accept', filePath),
            abandon: generateAction(onFileClick, messageId, 'file-reject', filePath),
        }),
        [filePath, messageId, onFileClick]
    );

    const showOperation = onFileClick && enableFileOperation;
    return (
        <div className="composer-file-action">
            <AcceptStatus accepted={accepted} showLabel={showLabel} />
            {(accepted !== AcceptState.UNTOUCHED && showOperation) && (
                <span className="composer-file-action-hyper-bar" />
            )}
            {showOperation && (
                <div className="composer-file-action-group flex items-center gap-2.5">
                    <button className="composer-file-action" data-role="accept" onClick={accept}>
                        采纳
                    </button>
                    <button className="composer-file-action" data-role="reject" onClick={abandon}>
                        放弃
                    </button>
                </div>
            )}
        </div>
    );
}

function Indicator({messageId, filePath, accepted, action, onFileClick, enableFileOperation, status}: Props) {
    const basename = decodeURI(filePath).split(/(\\|\/)/).pop();
    const loading = status === ComposerCodeStatus.PROCESSING;
    const label = status === ComposerCodeStatus.UNREADY
        ? '生成中'
        : loading ? (action === 'create' ? '创建中' : '更新中') : null;
    const isCancelled = status === ComposerCodeStatus.CANCELLED;

    const handleClick = useCallback(
        (e: any) => {
            const action = generateAction(onFileClick, messageId, 'file-view', filePath);
            action(e);
        },
        [filePath, messageId, onFileClick]
    );

    const [showLabel, setLabelState] = useState(false);
    const previousAccepted = useRef<AcceptState>(accepted);
    useEffect(
        () => {
            if (previousAccepted.current === AcceptState.UNTOUCHED && accepted !== AcceptState.UNTOUCHED) {
                setLabelState(true);
                setTimeout(
                    () => {
                        setLabelState(false);
                    },
                    2000
                );
            }
            previousAccepted.current = accepted;
        },
        [accepted]
    );

    return (
        <div
            onClick={handleClick}
            className="composer-file-indicator"
            data-role={action}
        >
            <div className="flex items-center gap-2 overflow-hidden">
                <span className="composer-file-dot" />
                <span
                    className={cx('font-semibold overflow-hidden text-ellipsis', {'line-through': action === 'delete'})}
                >
                    {basename}
                </span>
            </div>
            {isCancelled ? <div className="composer-file-action-description">已停止</div> : (
                loading
                    ? (
                        <div className="composer-file-action">
                            <Loading className="w-4 h-4" />
                            <span>{label}</span>
                        </div>
                    )
                    : (
                        <AcceptBtnGroup
                            enableFileOperation={enableFileOperation}
                            showLabel={showLabel}
                            messageId={messageId}
                            accepted={accepted}
                            onFileClick={onFileClick}
                            filePath={filePath}
                        />
                    )
            )}
        </div>
    );
}

export default function ComposerFileIndicator(props: Props) {
    if (props.action === 'preview') {
        return (
            <ComposerPreviewIndicator
                content={props.content}
                messageId={props.messageId}
                accepted={props.accepted}
                onFileClick={props.onFileClick}
            />
        );
    }

    return <Indicator {...props} />;
}
