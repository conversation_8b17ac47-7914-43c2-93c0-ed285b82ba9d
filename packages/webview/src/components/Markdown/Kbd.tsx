import cx from 'classnames';
import {CSSProperties} from 'preact/compat';

interface Props {
    symbol: string;
    className?: string;
    style?: CSSProperties;
}

export default function Kbd({symbol, className, style}: Props) {
    return (
        <kbd
            className={cx(
                // eslint-disable-next-line max-len
                'w-[22px] h-[22px] flex shrink-0 justify-center items-center rounded text-sm text-[var(--comate-descriptionForeground)] bg-[#888888]/20 border-none shadow-none',
                className
            )}
            style={style}
        >
            {symbol}
        </kbd>
    );
}
