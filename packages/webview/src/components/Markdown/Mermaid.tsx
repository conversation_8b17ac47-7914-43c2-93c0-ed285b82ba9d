import {
    useRef,
    useEffect,
    memo,
    useLayoutEffect,
    useCallback,
    useState,
    HTMLProps,
} from 'preact/compat';
import mermaid from 'mermaid';
import {throttle} from 'lodash';
import classNames from 'classnames';
import {useTranslation} from 'react-i18next';
import copyIcon from '@/assets/copy.svg';
import checkIcon from '@/assets/check.svg';
import {MARKDOWN_CODE_ACTION_COPY} from '@/i18n/constants';
import Tooltip from '../Tooltip';
import './mermaid.css';

const update = throttle(async (children: string, updator: (svg: string) => void) => {
    const canParse = await mermaid.parse(children);
    if (canParse) {
        const {svg} = await mermaid.render('mermaid', children);
        const document = new DOMParser().parseFromString(svg, 'text/html');
        const nodes = document.querySelectorAll('.node.flowchart-label rect');
        for (const node of Array.from(nodes)) {
            node.setAttribute('rx', '4');
            node.setAttribute('ry', '4');
        }
        updator(document.body.innerHTML);
    }
}, 100);

interface Props {
    children: string;
    actions: Record<string, (content?: string, language?: string) => void>;
}

const withSuccessIconFeedback = (icon: string) => {
    return ({onClick, ...props}: HTMLProps<HTMLDivElement>) => {
        const [checked, setChecked] = useState(false);
        const wrapperedClick = useCallback(
            async e => {
                if (!onClick) {
                    return;
                }

                await onClick(e);
                setChecked(true);
                setTimeout(
                    () => {
                        setChecked(false);
                    },
                    2000
                );
            },
            [onClick]
        );
        if (checked) {
            return (
                <div
                    {...props}
                    className={classNames(props.className, 'text-green-500', 'cursor-not-allowed')}
                    // bca-disable-line
                    dangerouslySetInnerHTML={{__html: checkIcon}}
                />
            );
        }
        // bca-disable-line
        return <div {...props} onClick={wrapperedClick} dangerouslySetInnerHTML={{__html: icon}} />;
    };
};

const CopyIcon = withSuccessIconFeedback(copyIcon);

export default memo(function Mermaid({children, actions}: Props) {
    const ref = useRef<HTMLDivElement>(null);
    const {t} = useTranslation();
    useEffect(
        () => {
            if (ref.current) {
                // 所有样式都迁移到样式表里，防止兼容性问题
                mermaid.mermaidAPI.initialize({
                    flowchart: {
                        padding: 6,
                        curve: 'linear',
                    },
                    startOnLoad: false,
                });
            }
        },
        [ref]
    );

    const handleClick = useCallback(
        () => actions['copy'](children),
        [actions, children]
    );

    useLayoutEffect(
        () => {
            update(children, svg => {
                // bca-disable-line
                ref.current!.innerHTML = svg;
            });
        },
        [children]
    );

    return (
        <div className="relative mermaid-container my-2">
            <Tooltip overlay={t(MARKDOWN_CODE_ACTION_COPY)}>
                <CopyIcon className="mermaid-btn w-4 h-4 absolute top-3 right-3 cursor-pointer" onClick={handleClick} />
            </Tooltip>
            <div className="chat-mermaid" ref={ref} />
        </div>
    );
});
