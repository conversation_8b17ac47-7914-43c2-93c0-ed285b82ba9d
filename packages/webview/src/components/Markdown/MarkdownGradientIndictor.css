.markdown-gradient-indictor {
    display: flex;
    justify-content: space-between;
    padding: 0 12px;
    position: relative;
    cursor: pointer;
    align-items: center;
    background: linear-gradient(
            0deg,
            rgba(255, 255, 255, 0.02),
            rgba(255, 255, 255, 0.02)
        ),
        linear-gradient(
            99.74deg,
            rgba(64, 128, 255, 0.02) 0%,
            rgba(14, 27, 53, 0.02) 21.44%
        );
}

.markdown-gradient-indictor::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 4px;
    padding: 1px;
    background: var(
        --linear-gradient-color,
        linear-gradient(
            112.91deg,
            rgba(63, 191, 97, 0.3) 0.89%,
            rgba(112, 217, 140, 0.3) 4.71%,
            rgba(124, 125, 128, 0.3) 26.72%
        )
    );
    -webkit-mask: linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: 1;
    pointer-events: none;
}
