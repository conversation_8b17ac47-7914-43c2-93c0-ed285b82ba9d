.composer-file-indicator[data-role="create"] {
    --linear-gradient-color: linear-gradient(112.91deg, rgba(63, 191, 97, 0.3) 0.89%, rgba(112, 217, 140, 0.3) 4.71%, rgba(124, 125, 128, 0.3) 26.72%);
    --dot-color: rgba(112, 217, 140, 1);
}

.composer-file-indicator[data-role="replaceFrom"],
.composer-file-indicator[data-role="edit"],
.composer-file-indicator[data-role="rewrite"] {
    --linear-gradient-color: linear-gradient(113.12deg, rgba(255, 214, 1, 0.3) 0.9%, rgba(255, 194, 51, 0.3) 5.3%, rgba(124, 125, 128, 0.3) 27.92%);
    --dot-color: rgba(255, 194, 51, 1);
}

.composer-file-indicator[data-role="delete"] {
    --linear-gradient-color: linear-gradient(112.91deg, rgba(255, 86, 100, 0.3) 0.89%, rgba(229, 69, 82, 0.3) 4.71%, rgba(124, 125, 128, 0.3) 26.72%);
    --dot-color: rgba(255, 78, 78, 1);
}

.composer-file-indicator {
    display: flex;
    height: 42px;
    justify-content: space-between;
    padding: 0 12px;
    margin: 8px 0;
    position: relative;
    cursor: pointer;
    align-items: center;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.02)), linear-gradient(99.74deg, rgba(64, 128, 255, 0.02) 0%, rgba(14, 27, 53, 0.02) 21.44%);
}

.composer-file-indicator::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 4px;
    padding: 1px;
    background: var(--linear-gradient-color);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    z-index: 1;
}

.composer-file-dot {
    display: block;
    width: 6px;
    height: 6px;
    min-width: 6px;
    border-radius: 100%;
    background-color: var(--dot-color);
}

.composer-file-action {
    display: flex;
    gap: 8px;
    align-items: center;
    z-index: 2;
    color: #17C3E5;
}

.composer-file-action-description {
    color: var(--comate-descriptionForeground);
}

.composer-file-action-hyper-bar {
    display: inline-block;
    width: 1px;
    height: 12px;
    background-color: var(--vscode-textSeparator-foreground);
}

.composer-file-action-group button:hover {
    opacity: 0.8;
}

.composer-file-indictor-url-preview {
    background: linear-gradient(231.62deg, rgba(32, 152, 243, 0.2) 1.52%, rgba(32, 152, 243, 0.0697005) 16.57%, rgba(32, 152, 243, 0) 36.31%);
    padding: 12px 12px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    margin: 8px 0;
    color: #17C3E5;
    border: 2px solid;
    border-image:
        linear-gradient(
            to bottom,
            var(--vscode-editor-hoverHighlightBackground),
            var(--vscode-keybindingLabel-border)
        )
        2;
    clip-path: inset(0 round 0.25rem);
    background-position: right;
    background-repeat: no-repeat;
    background-size: contain;
}

.composer-file-indictor-url-preview a {
    cursor: pointer;
}

.composer-file-indictor-url-preview button {
    border-radius: 4px;
    border: 1px solid rgba(38, 204, 255, 0.5);
    height: 22px;
    line-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;
}