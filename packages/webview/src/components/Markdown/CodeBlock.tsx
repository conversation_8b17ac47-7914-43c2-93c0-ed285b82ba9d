/* eslint-disable max-len */
import {useCallback, useMemo, useState} from 'preact/hooks';
import {LANGUAGE_DISPLAY_NAME_MAP} from '@shared/constants';
import {mapToHighlighterLanguage} from '@shared/languages';
import {ComponentProps} from 'preact';
import {HTMLAttributes, lazy, Suspense} from 'preact/compat';
import {omit} from 'lodash';
import {useToggle} from '@/hooks';
import {useChatConfig} from '../ChatConfigProvider';
// import HighLighter from './HighLighter';
import CollapseButton from '../CollapsePanel/CollapseButton';
import HeaderWithActions from './HeaderWithActions';
import oneDark from './highlighterStyles/dark';
import oneLight from './highlighterStyles/light';
import './CodeBlock.css';

const HighLighter = lazy(
    () => import(/* webpackChunkName: "high_lighter" */ './HighLighter')
);

const getCustomStyle = ({
    showHeader,
    showCollapseToggle,
}: {
    showHeader: boolean;
    showCollapseToggle: boolean;
}) => {
    const style: Record<string, any> = {
        fontSize: '12px',
        margin: 0,
        borderTopLeftRadius: '4px',
        borderTopRightRadius: '4px',
        borderBottomLeftRadius: '4px',
        borderBottomRightRadius: '4px',
    };
    if (showHeader) {
        style.borderTopLeftRadius = '0px';
        style.borderTopRightRadius = '0px';
    }
    if (showCollapseToggle) {
        style.paddingBottom = '28px';
    }
    return style;
};

type codeBlockActions =
    | 'replaceToFile'
    | 'copy'
    | 'accept'
    | 'diff'
    | 'newFile';
interface Props {
    messageId?: string;
    inline: boolean;
    children: preact.ComponentChildren;
    hidden?: boolean;
    className?: string;
    actions?: Record<string, (content?: string, language?: string) => void>;
    numLinesToShow?: number;
    showHeader?: boolean;
    showActions?: boolean;
    startLine?: number;
    title?: ComponentProps<typeof HeaderWithActions>['title'];
    diffLines?: ComponentProps<typeof HighLighter>['diffLines'];
    header?: preact.ComponentChildren;
    noMargin?: boolean;
    attributes?: HTMLAttributes<HTMLDivElement>;
    jsxControl?: {
        actions?: codeBlockActions[];
        replaceToFileData?: {
            filePath: string;
            from: string;
            to: string;
            replaceAll?: boolean;
        };
        insertToFileData?: {
            filePath: string;
            position: {line: number, character: number};
            newText: string;
            metadata?: {needsConfirmation: boolean};
        };
    };
}

function CodeBlock(props: Props) {
    const {
        messageId,
        hidden,
        showHeader = false,
        showActions,
        inline,
        className,
        children,
        actions,
        numLinesToShow = 1000,
        startLine,
        header,
        noMargin = false,
        attributes,
        title,
        jsxControl,
        ...rest
    } = props;
    const {theme} = useChatConfig();

    const content = String(children).replace(/\n$/, '');

    const numOfLines = content.split('\n').length;

    const showCollapseToggle = numOfLines > numLinesToShow;

    const [collapsed, toggle] = useToggle(true);
    const language = useMemo(
        () => /language-(\w+)/.exec(className || '')?.[1],
        [className]
    );
    // const processedActions2 = (actions && language !== 'shell' && language !== 'bash' && language !== 'sh')
    //     ? (({insertIntoTerminal, ...rest}) => rest)(actions)
    //     : actions;

    const processedActions = useMemo(
        () => {
            if (!actions) {
                return [];
            }
            const isTerminalLanguage = language && ['shell', 'bash', 'sh'].includes(language);

            // 只筛选jsxControl中声明的actions，没有声明的都不加上去
            if (jsxControl?.actions?.length) {
                const {actions: jsxActions} = jsxControl;
                return jsxActions.reduce((acc, jsxAction) => {
                    acc[jsxAction] = actions[jsxAction];
                    return acc;
                }, {});
            }
            else {
                return isTerminalLanguage
                    ? omit(actions, ['viewFile', 'showFileInsertDiff', 'showFileReplaceDiff', 'replaceToFile', 'insertToFile'])
                    : omit(actions, [
                        'viewFile',
                        'showFileInsertDiff',
                        'showFileReplaceDiff',
                        'replaceToFile',
                        'insertToFile',
                        'insertIntoTerminal',
                        'executeTerminalShell',
                    ]);
            }
        },
        [actions, jsxControl, language]
    );

    const highlighterLanguage = mapToHighlighterLanguage(language);

    const style = useMemo(
        () => (theme === 'light' ? oneLight : oneDark),
        [theme]
    );

    const headerTitle = useMemo(
        () => {
            if (title) {
                return title;
            }
            return LANGUAGE_DISPLAY_NAME_MAP[language!] ?? language;
        },
        [language, title]
    );

    const customStyle = useMemo(
        () => getCustomStyle({showCollapseToggle, showHeader}),
        [showCollapseToggle, showHeader]
    );

    const finalizedHeader = useMemo(
        () => {
            if (inline) {
                return null;
            }
            if (showHeader) {
                if (header) {
                    return header;
                }
                return (
                    <HeaderWithActions
                        messageId={messageId}
                        showActions={showActions}
                        title={headerTitle}
                        actions={processedActions}
                        replaceToFileData={jsxControl?.replaceToFileData}
                        insertToFileData={jsxControl?.insertToFileData}
                        content={content}
                        language={language}
                    />
                );
            }
            return null;
        },
        [
            messageId,
            inline,
            showHeader,
            header,
            showActions,
            headerTitle,
            processedActions,
            content,
            language,
            jsxControl,
        ]
    );

    const handleCopyCode = useCallback(
        (content: string) => {
            if (actions?.copy) {
                actions.copy(content);
            }
        },
        [actions]
    );

    const getScrollbarWidth = () => {
        // 不加memo了，保持切换滚动条状态鲜活
        const outer = document.createElement('div');
        outer.style.visibility = 'hidden';
        outer.style.width = '100px';
        document.body.appendChild(outer);

        const widthNoScroll = outer.offsetWidth;
        outer.style.overflow = 'scroll';

        const inner = document.createElement('div');
        inner.style.width = '100%';
        outer.appendChild(inner);

        const widthWithScroll = inner.offsetWidth;
        if (outer.parentNode) {
            outer.parentNode.removeChild(outer);
        }

        return widthNoScroll - widthWithScroll;
    };

    const handleMouseOver = useCallback(
        () => {
            const scrollbarWidth = getScrollbarWidth(); // 0是隐藏 非0是有滚动条
            if (scrollbarWidth > 0) {
                return;
            }
            document.querySelector('html')?.style.setProperty('scrollbar-color', 'auto');
        },
        []
    );

    const handleMouseLeave = useCallback(
        () => {
            document.querySelector('html')?.style.setProperty('scrollbar-color', 'rgba(121, 121, 121, 0.4) #1e1e1e');
        },
        []
    );

    if (inline) {
        return (
            <code
                {...rest}
                className={`${className} inline-code`}
                role="presentation"
            >
                {children}
            </code>
        );
    }
    return (
        hidden ? null : (
            <div
                className={`focus:outline-none ${noMargin ? '' : 'my-3'} message-content-code relative`}
                {...attributes}
                role="region"
                tabIndex={1}
                aria-label={`${language || ''}代码：${content}`}
            >
                {finalizedHeader}
                <Suspense fallback={null}>
                    <HighLighter
                        {...rest}
                        className={`${className} comate-code-content`}
                        showCollapseToggle={showCollapseToggle}
                        collapsed={collapsed}
                        numLinesToShow={numLinesToShow}
                        customStyle={customStyle}
                        // @ts-expect-error
                        style={style}
                        language={highlighterLanguage || 'text'}
                        onDidCopy={handleCopyCode}
                        role="presentation"
                        aria-hidden="true"
                        onMouseOver={handleMouseOver}
                        onMouseLeave={handleMouseLeave}
                    >
                        {children ? content : ''}
                    </HighLighter>
                </Suspense>
                {showCollapseToggle && (
                    <CollapseButton style={{position: 'absolute'}} collapsed={collapsed} toggle={toggle} shouldCenter />
                )}
            </div>
        )
    );
}

export default CodeBlock;
