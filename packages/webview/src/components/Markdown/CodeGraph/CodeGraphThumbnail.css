body.vscode-dark,
body.dark {
    --thumbnail-title-color: #fff;
    --thumbnail-background: linear-gradient(0deg, rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0.04)), linear-gradient(99.74deg, rgba(64, 128, 255, 0.04) 0%, rgba(14, 27, 53, 0.04) 21.44%);
    --thumbnail-description-background: #3d3d3d;
    --thumbnail-description-color: #e3e4e8;
}

.vscode-light,
body.light {
    --thumbnail-title-color: #3B3B3B;
    --thumbnail-background: linear-gradient(0deg, rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0.04)), linear-gradient(99.74deg, rgba(64, 128, 255, 0.04) 0%, rgba(14, 27, 53, 0.04) 21.44%);
    --thumbnail-description-background: #e6e6e6;
    --thumbnail-description-color: #5c5c5c;
}

.thumbnail {
    background: var(--thumbnail-background);
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 6px 12px 16px 12px;
    border-radius: 4px;
    gap: 4px;
}

.thumbnail [data-role="title"] {
    height: 30px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--thumbnail-title-color);
    flex-shrink: 0;
    font-weight: bold;
}

.thumbnail [data-role="description"] {
    background:var(--thumbnail-description-background);
    color: var(--thumbnail-description-color);
    border-radius: 4px;
    position: absolute;
    bottom: 24px;
    left: 20px;
    padding: 0 4px;
}

