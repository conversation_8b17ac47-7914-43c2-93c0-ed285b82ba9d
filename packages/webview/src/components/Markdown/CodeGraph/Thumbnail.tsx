import {ReactNode, useCallback} from 'preact/compat';
import {JSXInternal} from 'preact/src/jsx';
import classNames from 'classnames';
import rightIcon from '@/assets/right.svg';
import ToolbarButton from '@/components/ToolbarButton';
import {useDerivedState} from '@/components/Tabs/useDerivedState';

interface Props {
    className?: string;
    collapsed?: boolean;
    title: string;
    description: string;
    children: ReactNode;
    onClick?: () => any;
    onCollapse?: (collapsed: boolean) => any;
}

export function Thumbnail({className, collapsed, description, title, children, onClick, onCollapse}: Props) {
    const [internalCollapsed, setInternalCollapsed] = useDerivedState<boolean>(
        typeof collapsed === 'undefined' ? false : collapsed
    );
    const toggleCollapse = useCallback<JSXInternal.MouseEventHandler<HTMLButtonElement>>(
        e => {
            e.stopPropagation();
            onCollapse?.(!internalCollapsed);
            setInternalCollapsed(!internalCollapsed);
        },
        [internalCollapsed, onCollapse, setInternalCollapsed]
    );

    const header = (
        <div data-role="title">
            <div>{title}</div>
            <ToolbarButton
                icon={rightIcon}
                onClick={toggleCollapse}
                ariaTitle={internalCollapsed ? '展开' : '折叠'}
                iconClassName={internalCollapsed ? undefined : 'rotate-90'} />
        </div>
    );

    if (internalCollapsed) {
        return (
            <div className={classNames('thumbnail h-[42px]', className)} onClick={onClick}>
                {header}
            </div>
        );
    }

    return (
        <div
            className={classNames('thumbnail h-[240px]', className)}
            onClick={onClick}
        >
            {header}
            <div className="flex-1 cursor-pointer w-full">
                {children}
            </div>
            <div data-role="description">{description}</div>
        </div>
    );
}
