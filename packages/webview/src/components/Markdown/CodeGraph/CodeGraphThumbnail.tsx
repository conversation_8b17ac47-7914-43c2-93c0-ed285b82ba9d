/* bca-disable */
import * as echarts from 'echarts';
import {memo, useCallback} from 'preact/compat';
import {EventMessage} from '@shared/protocols';
import GraphChart from '@/components/Chart/GraphChart';
import {messageHandler} from '@/utils/messageHandler';
import {useChatConfig} from '@/components/ChatConfigProvider';
import MarkdownGradientIndictor from '../MarkdownGradientIndictor';
import {Thumbnail} from './Thumbnail';
import './CodeGraphThumbnail.css';

function CodeGraphThumbnail({messageId, data, collapsed}: {collapsed: boolean, data: any, messageId: number}) {
    const {theme} = useChatConfig();
    const scale = 0.6;
    const colors = ['24, 181, 255', '42, 115, 254', '235, 185, 0', '49, 200, 88', '143, 143, 143'];
    const fontColor = ['rgba(59, 59, 59, 1)', 'rgb(212, 212, 212)'];
    const categoryConfig = [
        {symbolSize: 110, value: 110, fontSize: 15, color: `rgb(${colors[0]})`},
        {symbolSize: 39, value: 39, fontSize: 13, color: `rgb(${colors[1]})`},
        {symbolSize: 30, value: 30, fontSize: 12, color: `rgb(${colors[2]})`},
        {symbolSize: 22, value: 22, fontSize: 11, color: `rgb(${colors[3]})`},
        {symbolSize: 16, value: 16, fontSize: 10, color: `rgb(${colors[4]})`},
    ];

    const darkTheme = {
        labelStyle: [
            {fontSize: 15, color: '#FCFDFF'},
            ...colors.slice(1).map((color, i) => {
                const index = i + 1;
                const height = categoryConfig[index].symbolSize * scale - 4;
                const fontSize = Math.floor(categoryConfig[index].fontSize * scale);
                const paddingY = Math.floor((height - fontSize) / 2);
                const borderRadius = height;
                const paddingRight = Math.floor(height / 2);
                const paddingLeft = paddingRight * 2.5;
                const distance = -1 * height;
                return {
                    color: fontColor[1],
                    backgroundColor: `rgba(${color}, 0.2)`,
                    borderColor: `rgba(${color}, 0.6)`,
                    borderWidth: 1,
                    borderType: 'solid',
                    padding: [paddingY, paddingRight, paddingY, paddingLeft],
                    borderRadius,
                    fontSize,
                    distance,
                };
            }),
        ],
        lineStyle: colors.map(color => [`rgba(${color}, 0.3)`, `rgba(${color}, 1)`]),
    };
    const themeVariables = {
        light: {...darkTheme, labelStyle: darkTheme.labelStyle.map(style => ({...style, color: fontColor[0]}))},
        dark: darkTheme,
    };

    const themeConfig = themeVariables[theme];

    const nodeKeyById = data.nodes.reduce((acc, n) => {
        acc[n.id] = n;
        return acc;
    }, {});

    const nodes = data.nodes.map(node => {
        const {value, symbolSize} = categoryConfig[node.category];
        const itemStyle = node.category === 0
            ? {
                borderColor: `rgba(${colors[0]}, 0.3)}`,
                borderWidth: 1,
                color: new echarts.graphic.RadialGradient(
                    0.5,
                    0.5,
                    0.5, // x, y 中心点和半径
                    [
                        {offset: 0, color: 'rgba(24,181,255,1)'}, // 内部不透明颜色
                        {offset: 0.84, color: 'rgba(24,181,255,1)'}, // 停止透明渐变
                        {offset: 0.86, color: 'rgba(24,181,255,0.2)'}, // 第二层
                    ]
                ),
            }
            : {};
        return {
            ...node,
            label: {
                show: node.category !== 0,
                position: node.category === 0 ? 'inside' : 'right',
                ...themeConfig.labelStyle[node.category],
            },
            itemStyle,
            value: value * scale,
            symbolSize: symbolSize * scale,
        };
    });

    const categories = data.categories.map((category, i) => {
        return {...category, itemStyle: {color: categoryConfig[i].color}};
    });

    const links = data.links.map(link => {
        const sourceCategoryIndex = nodeKeyById[link.source].category;
        const targetCategoryIndex = nodeKeyById[link.target].category;
        const color = themeConfig.lineStyle[sourceCategoryIndex];
        const gradient = new echarts.graphic.LinearGradient(1, 0, 0, 0, [
            {offset: 0, color: color[1]},
            {offset: 1, color: color[0]},
        ]);
        return {
            ...link,
            lineStyle: {
                color: gradient,
                type: sourceCategoryIndex === 0 && targetCategoryIndex === 1 ? 'dashed' : undefined,
                width: 2,
                curveness: 0,
            },
        };
    });

    const option: echarts.EChartsOption = {
        tooltip: {
            show: false,
        },
        silent: true,
        backgroundColor: 'transparent',
        legend: [],
        series: [
            {
                type: 'graph',
                layout: 'force',
                animationEasingUpdate: 'quinticInOut',
                data: nodes,
                links: links,
                edgeSymbol: ['none', 'arrow'],
                edgeSymbolSize: [0, 4],
                categories: categories,
                roam: false,
                label: {show: true},
                labelLayout: {
                    hideOverlap: true,
                },
                force: {
                    gravity: 0.01,
                    repulsion: 400,
                    layoutAnimation: false,
                    edgeLength: [0, 40],
                },
            } as any as echarts.GraphSeriesOption,
        ],
    };

    const showCodeGraph = useCallback(
        () => {
            messageHandler.send(EventMessage.ChatCodeGraphEvent, {action: 'view', messageId});
        },
        [messageId]
    );

    const toggleThumbnailCollapsed = useCallback(
        (collapsed: boolean) => {
            messageHandler.send(EventMessage.ChatCodeGraphEvent, {action: 'collapse', collapsed, messageId});
        },
        [messageId]
    );

    return (
        <div>
            <p className="my-1">已生成代码关系图谱，帮助您更清晰的展示代码关系：</p>
            <Thumbnail
                title="代码关系图谱"
                description="点击查看图谱详情"
                collapsed={collapsed}
                onCollapse={toggleThumbnailCollapsed}
            >
                <MarkdownGradientIndictor className="h-full" onClick={showCodeGraph}>
                    <GraphChart
                        options={option}
                        theme={theme}
                        style={{width: '100%', height: '100%', pointerEvents: 'none'}}
                    />
                </MarkdownGradientIndictor>
            </Thumbnail>
        </div>
    );
}

export default memo(CodeGraphThumbnail, (prevProps, nextProps) => {
    return prevProps.messageId === nextProps.messageId
        && JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data)
        && prevProps.collapsed === nextProps.collapsed;
});
