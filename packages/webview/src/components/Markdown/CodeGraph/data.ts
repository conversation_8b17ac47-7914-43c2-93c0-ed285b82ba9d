/* eslint-disable max-lines */
export const graph = {
    categories: [
        {
            id: 'currentClass',
            name: '当前类',
        },
        {
            id: 'parentClass',
            name: '父类',
        },
        {
            id: 'class',
            name: '类',
        },
        {
            id: 'method',
            name: '方法',
        },
        {
            id: 'field',
            name: '字段',
        },
    ],
    links: [
        {
            source: '349706e85efb9620a5e465f9cf78a56d',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'd71ca779ef875f064b15d2b6e4b8de7f',
            target: 'da15b75eda4e0ec7e2b8c5f93c5a1a8b',
            labelName: 'RETURN_TYPE',
        },
        {
            source: '4692d714bad585f896788805a2290aeb',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '275a2c46c4b9634cf6a094f3a8c9ae31',
            labelName: 'HAS_METHOD',
        },
        {
            source: '8a3dee9760088bd21b76a3f7a2d59c61',
            target: '3ff4b8a63e6a3005a43b3431db07a330',
            labelName: 'RETURN_TYPE',
        },
        {
            source: '6270b57bd5a6f3bfc8ed1697e180dc0c',
            target: 'abcef0ee7bb3d2b241dbea8b6ad7df8a',
            labelName: 'IMPLEMENTS',
        },
        {
            source: 'cda953b37369b866a1a03f4ff1847769',
            target: '4e1065d53e3fca568e4cd2ff26be7dd6',
            labelName: 'RETURN_TYPE',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '8a3dee9760088bd21b76a3f7a2d59c61',
            labelName: 'HAS_METHOD',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '2a9ae7441916df97d02cb985aab3eda4',
            labelName: 'HAS_METHOD',
        },
        {
            source: '6e5568577f088a2f80d5bfa02c50475f',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'efc8f37c546cab069654158f2b505399',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '6270b57bd5a6f3bfc8ed1697e180dc0c',
            target: 'a3dbf696cc281a9655900635eee5f1b7',
            labelName: 'IMPLEMENTS',
        },
        {
            source: '210e62f4b627f1aab883ba28979f2f5a',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: 'e161474a1b3e4100ea3b8797d30b64cc',
            labelName: 'HAS_METHOD',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '35f5b3c4a38e992602f1c62f7932da29',
            labelName: 'HAS_METHOD',
        },
        {
            source: 'efc8f37c546cab069654158f2b505399',
            target: '4e1065d53e3fca568e4cd2ff26be7dd6',
            labelName: 'CALL',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '66f700aeb10d4655c89ac0aca78c3d69',
            target: '6759af472b2bb5e9b7c3c397a652b1d4',
            labelName: 'CALL',
        },
        {
            source: '0fb2af0c60e4badf9529413cfb0812b6',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '6fe44b173b5e34f2780e15d34968d64d',
            labelName: 'HAS_METHOD',
        },
        {
            source: '0fb2af0c60e4badf9529413cfb0812b6',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'CALL',
        },
        {
            source: '6894b5cbdef3717687051392b4c877cc',
            target: '4e1065d53e3fca568e4cd2ff26be7dd6',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '2b1fd39a115aa8f1a0fcc23337b7f2ca',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: 'f8066b477a8694133e246f84585e5111',
            labelName: 'HAS_METHOD',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: '253620168bda6395b37557466d5baa51',
            labelName: 'CALL',
        },
        {
            source: 'd848f9d99a8423663e4a8eb3303e0e80',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '210e62f4b627f1aab883ba28979f2f5a',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '6270b57bd5a6f3bfc8ed1697e180dc0c',
            labelName: 'EXTENDS',
        },
        {
            source: '2b1fd39a115aa8f1a0fcc23337b7f2ca',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: '4692d714bad585f896788805a2290aeb',
            target: '6759af472b2bb5e9b7c3c397a652b1d4',
            labelName: 'CALL',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: '6759af472b2bb5e9b7c3c397a652b1d4',
            labelName: 'CALL',
        },
        {
            source: '2a9ae7441916df97d02cb985aab3eda4',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: 'b8bb3df724fea9869697c67ad9a01cef',
            labelName: 'HAS_METHOD',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '66f700aeb10d4655c89ac0aca78c3d69',
            labelName: 'HAS_METHOD',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '2b1fd39a115aa8f1a0fcc23337b7f2ca',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: 'efc8f37c546cab069654158f2b505399',
            labelName: 'HAS_METHOD',
        },
        {
            source: '********************************',
            target: 'b1af4cf8460c7ca7980b6dc6aab3acb3',
            labelName: 'IS_TYPE',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '6894b5cbdef3717687051392b4c877cc',
            labelName: 'HAS_METHOD',
        },
        {
            source: '66f700aeb10d4655c89ac0aca78c3d69',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'd71ca779ef875f064b15d2b6e4b8de7f',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '63a97571e46b9460be741fb3fd37ad48',
            labelName: 'IMPLEMENTS',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: 'd71ca779ef875f064b15d2b6e4b8de7f',
            labelName: 'HAS_METHOD',
        },
        {
            source: 'f8066b477a8694133e246f84585e5111',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '2b1fd39a115aa8f1a0fcc23337b7f2ca',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '349706e85efb9620a5e465f9cf78a56d',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'dfa92bf2f14770a9aea7929aad9ae907',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '6e5568577f088a2f80d5bfa02c50475f',
            labelName: 'HAS_METHOD',
        },
        {
            source: 'ca52fba9825cbee108800a0a2bc9be96',
            target: '3ff4b8a63e6a3005a43b3431db07a330',
            labelName: 'IS_TYPE',
        },
        {
            source: '2b1fd39a115aa8f1a0fcc23337b7f2ca',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: '35f5b3c4a38e992602f1c62f7932da29',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '2b1fd39a115aa8f1a0fcc23337b7f2ca',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '1d743bcae413f47206dcf8d97d776eb3',
            target: 'b1af4cf8460c7ca7980b6dc6aab3acb3',
            labelName: 'CALL',
        },
        {
            source: '6e5568577f088a2f80d5bfa02c50475f',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '2b1fd39a115aa8f1a0fcc23337b7f2ca',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '66f700aeb10d4655c89ac0aca78c3d69',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'dfa92bf2f14770a9aea7929aad9ae907',
            labelName: 'CALL',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'b863fe4c1c36e0e838c0ea5462234f97',
            labelName: 'CALL',
        },
        {
            source: '35f5b3c4a38e992602f1c62f7932da29',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'CALL',
        },
        {
            source: '6270b57bd5a6f3bfc8ed1697e180dc0c',
            target: 'b863fe4c1c36e0e838c0ea5462234f97',
            labelName: 'IMPLEMENTS',
        },
        {
            source: 'f8066b477a8694133e246f84585e5111',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '35f5b3c4a38e992602f1c62f7932da29',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '4692d714bad585f896788805a2290aeb',
            target: 'dfa92bf2f14770a9aea7929aad9ae907',
            labelName: 'CALL',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '6e5568577f088a2f80d5bfa02c50475f',
            target: '4e1065d53e3fca568e4cd2ff26be7dd6',
            labelName: 'CALL',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '98c899e3576c489aa1b2b892ebfb587e',
            labelName: 'HAS_FIELD',
        },
        {
            source: '35f5b3c4a38e992602f1c62f7932da29',
            target: 'a657ca6e57e4a368fee7b2634d0bde1e',
            labelName: 'CALL',
        },
        {
            source: '349706e85efb9620a5e465f9cf78a56d',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '210e62f4b627f1aab883ba28979f2f5a',
            target: '63a97571e46b9460be741fb3fd37ad48',
            labelName: 'OVERRIDE',
        },
        {
            source: '0fb2af0c60e4badf9529413cfb0812b6',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'RETURN_TYPE',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: '6e5568577f088a2f80d5bfa02c50475f',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '210e62f4b627f1aab883ba28979f2f5a',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '35f5b3c4a38e992602f1c62f7932da29',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: '253620168bda6395b37557466d5baa51',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: 'e4d72d051f3080e94a24d830b9204f2d',
            labelName: 'HAS_FIELD',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '05aecb33ef3e4ff9a6f066cb32efb3c8',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'CALL',
        },
        {
            source: '0fb2af0c60e4badf9529413cfb0812b6',
            target: '5803cd7481a27206adaa05b50fdd15d3',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '05aecb33ef3e4ff9a6f066cb32efb3c8',
            labelName: 'HAS_METHOD',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: 'c0f490afb70743ea7362f491029f185a',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'e161474a1b3e4100ea3b8797d30b64cc',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: '4692d714bad585f896788805a2290aeb',
            target: '253620168bda6395b37557466d5baa51',
            labelName: 'CALL',
        },
        {
            source: 'c0f490afb70743ea7362f491029f185a',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '2b1fd39a115aa8f1a0fcc23337b7f2ca',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: 'b8bb3df724fea9869697c67ad9a01cef',
            target: '3ff4b8a63e6a3005a43b3431db07a330',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'e4d72d051f3080e94a24d830b9204f2d',
            target: '4e1065d53e3fca568e4cd2ff26be7dd6',
            labelName: 'IS_TYPE',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: 'd848f9d99a8423663e4a8eb3303e0e80',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '1d743bcae413f47206dcf8d97d776eb3',
            target: '63a97571e46b9460be741fb3fd37ad48',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: 'cda953b37369b866a1a03f4ff1847769',
            labelName: 'HAS_METHOD',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'aafa49baa009424811ef0c90f2654ba5',
            labelName: 'CALL',
        },
        {
            source: '2a9ae7441916df97d02cb985aab3eda4',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '35f5b3c4a38e992602f1c62f7932da29',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'CALL',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: '3ff4b8a63e6a3005a43b3431db07a330',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '349706e85efb9620a5e465f9cf78a56d',
            labelName: 'HAS_METHOD',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: 'c0f490afb70743ea7362f491029f185a',
            labelName: 'HAS_METHOD',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '0fb2af0c60e4badf9529413cfb0812b6',
            labelName: 'HAS_METHOD',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: '6759af472b2bb5e9b7c3c397a652b1d4',
            labelName: 'CALL',
        },
        {
            source: '66f700aeb10d4655c89ac0aca78c3d69',
            target: 'a657ca6e57e4a368fee7b2634d0bde1e',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '********************************',
            labelName: 'HAS_FIELD',
        },
        {
            source: '35f5b3c4a38e992602f1c62f7932da29',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'RETURN_TYPE',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: '253620168bda6395b37557466d5baa51',
            labelName: 'CALL',
        },
        {
            source: '98c899e3576c489aa1b2b892ebfb587e',
            target: 'b1af4cf8460c7ca7980b6dc6aab3acb3',
            labelName: 'IS_TYPE',
        },
        {
            source: 'd71ca779ef875f064b15d2b6e4b8de7f',
            target: 'b1af4cf8460c7ca7980b6dc6aab3acb3',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '2b1fd39a115aa8f1a0fcc23337b7f2ca',
            labelName: 'HAS_METHOD',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '4692d714bad585f896788805a2290aeb',
            target: '3ff4b8a63e6a3005a43b3431db07a330',
            labelName: 'CALL',
        },
        {
            source: 'efc8f37c546cab069654158f2b505399',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '4692d714bad585f896788805a2290aeb',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '4692d714bad585f896788805a2290aeb',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: '4e1065d53e3fca568e4cd2ff26be7dd6',
            labelName: 'CALL',
        },
        {
            source: 'd71ca779ef875f064b15d2b6e4b8de7f',
            target: '5803cd7481a27206adaa05b50fdd15d3',
            labelName: 'CALL',
        },
        {
            source: 'efc8f37c546cab069654158f2b505399',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: 'c0f490afb70743ea7362f491029f185a',
            target: 'a657ca6e57e4a368fee7b2634d0bde1e',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'd71ca779ef875f064b15d2b6e4b8de7f',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '4692d714bad585f896788805a2290aeb',
            target: '4e1065d53e3fca568e4cd2ff26be7dd6',
            labelName: 'CALL',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: 'b863fe4c1c36e0e838c0ea5462234f97',
            labelName: 'CALL',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'b863fe4c1c36e0e838c0ea5462234f97',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '919a29ea5ce361553e52f90aa4325c01',
            labelName: 'HAS_METHOD',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '210e62f4b627f1aab883ba28979f2f5a',
            labelName: 'HAS_METHOD',
        },
        {
            source: 'efc8f37c546cab069654158f2b505399',
            target: '6759af472b2bb5e9b7c3c397a652b1d4',
            labelName: 'CALL',
        },
        {
            source: 'e161474a1b3e4100ea3b8797d30b64cc',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'efc8f37c546cab069654158f2b505399',
            target: '1f07232a174e1a6c45b7ccea7d33f957',
            labelName: 'CALL',
        },
        {
            source: '275a2c46c4b9634cf6a094f3a8c9ae31',
            target: '6759af472b2bb5e9b7c3c397a652b1d4',
            labelName: 'CALL',
        },
        {
            source: '35f5b3c4a38e992602f1c62f7932da29',
            target: '6759af472b2bb5e9b7c3c397a652b1d4',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '4692d714bad585f896788805a2290aeb',
            labelName: 'HAS_METHOD',
        },
        {
            source: '919a29ea5ce361553e52f90aa4325c01',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: '210e62f4b627f1aab883ba28979f2f5a',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: 'd848f9d99a8423663e4a8eb3303e0e80',
            labelName: 'HAS_METHOD',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: 'e8210a85e712a09ce57edb0592baac60',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: '1d743bcae413f47206dcf8d97d776eb3',
            labelName: 'HAS_METHOD',
        },
        {
            source: '66f700aeb10d4655c89ac0aca78c3d69',
            target: '4e1065d53e3fca568e4cd2ff26be7dd6',
            labelName: 'CALL',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: '3ff4b8a63e6a3005a43b3431db07a330',
            labelName: 'CALL',
        },
        {
            source: 'd71ca779ef875f064b15d2b6e4b8de7f',
            target: '26fc92bdd786c75edcff3cdabfddcd92',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '6fe44b173b5e34f2780e15d34968d64d',
            target: '4e1065d53e3fca568e4cd2ff26be7dd6',
            labelName: 'CALL',
        },
        {
            source: 'c0f490afb70743ea7362f491029f185a',
            target: '4e1065d53e3fca568e4cd2ff26be7dd6',
            labelName: 'CALL',
        },
        {
            source: '05aecb33ef3e4ff9a6f066cb32efb3c8',
            target: '84b268cafb8193c5a1e259e236b1f137',
            labelName: 'ARGUMENT_TYPE',
        },
        {
            source: '********************************',
            target: '63a97571e46b9460be741fb3fd37ad48',
            labelName: 'IS_TYPE_GENERIC',
        },
        {
            source: '05aecb33ef3e4ff9a6f066cb32efb3c8',
            target: '5803cd7481a27206adaa05b50fdd15d3',
            labelName: 'CALL',
        },
        {
            source: 'fff9b80efb0d966b728064a700451850',
            target: 'ca52fba9825cbee108800a0a2bc9be96',
            labelName: 'HAS_FIELD',
        },
    ],
    nodes: [
        {
            id: 'dfa92bf2f14770a9aea7929aad9ae907',
            name: 'Event',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: '3ff4b8a63e6a3005a43b3431db07a330',
            name: 'JobEventQueueRepository',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: '26fc92bdd786c75edcff3cdabfddcd92',
            name: 'CompletionResult',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: '2b1fd39a115aa8f1a0fcc23337b7f2ca',
            name: 'copyInteractionInfo',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '6759af472b2bb5e9b7c3c397a652b1d4',
            name: 'BaseChainNode',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: 'e8210a85e712a09ce57edb0592baac60',
            name: 'JobAggregate',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: 'b863fe4c1c36e0e838c0ea5462234f97',
            name: 'MessageExecutor',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 1,
        },
        {
            id: '05aecb33ef3e4ff9a6f066cb32efb3c8',
            name: 'getCurrentUseMessage',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '6e5568577f088a2f80d5bfa02c50475f',
            name: 'pending',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '98c899e3576c489aa1b2b892ebfb587e',
            name: 'TOOL_EXECUTOR_REGISTRY',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 4,
        },
        {
            id: 'fff9b80efb0d966b728064a700451850',
            name: 'BaseJobExecutor',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 0,
        },
        {
            id: '349706e85efb9620a5e465f9cf78a56d',
            name: 'flushJobContext',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '66f700aeb10d4655c89ac0aca78c3d69',
            name: 'hasCancelled',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '6270b57bd5a6f3bfc8ed1697e180dc0c',
            name: 'BaseExecutor',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 1,
        },
        {
            id: '4692d714bad585f896788805a2290aeb',
            name: 'finish',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: 'b1af4cf8460c7ca7980b6dc6aab3acb3',
            name: 'BaseRegistry',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: 'e4d72d051f3080e94a24d830b9204f2d',
            name: 'jobBuildRepository',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 4,
        },
        {
            id: 'a657ca6e57e4a368fee7b2634d0bde1e',
            name: 'JobState',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: 'b8bb3df724fea9869697c67ad9a01cef',
            name: 'setJobEventQueueRepository',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: 'd848f9d99a8423663e4a8eb3303e0e80',
            name: 'isFirstChat',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: 'a3dbf696cc281a9655900635eee5f1b7',
            name: 'LLMExecutor',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 1,
        },
        {
            id: '4e1065d53e3fca568e4cd2ff26be7dd6',
            name: 'JobBuildRepository',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: 'efc8f37c546cab069654158f2b505399',
            name: 'cancel',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: 'd71ca779ef875f064b15d2b6e4b8de7f',
            name: 'invokeTool',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '919a29ea5ce361553e52f90aa4325c01',
            name: 'initMessage',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '84b268cafb8193c5a1e259e236b1f137',
            name: 'JobContextEntity',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: '********************************',
            name: 'JOB_EXECUTOR_REGISTRY',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 4,
        },
        {
            id: '6894b5cbdef3717687051392b4c877cc',
            name: 'setJobBuildRepository',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '63a97571e46b9460be741fb3fd37ad48',
            name: 'JobExecutor',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 1,
        },
        {
            id: '1f07232a174e1a6c45b7ccea7d33f957',
            name: 'JobBuildEntity',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: '1d743bcae413f47206dcf8d97d776eb3',
            name: 'registry',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '5803cd7481a27206adaa05b50fdd15d3',
            name: 'MessageHolder',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: '2a9ae7441916df97d02cb985aab3eda4',
            name: 'appendContextFromMessage',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '8a3dee9760088bd21b76a3f7a2d59c61',
            name: 'getJobEventQueueRepository',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '0fb2af0c60e4badf9529413cfb0812b6',
            name: 'getMessageContent',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '253620168bda6395b37557466d5baa51',
            name: 'JobUpdateEvent',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: 'abcef0ee7bb3d2b241dbea8b6ad7df8a',
            name: 'SensitiveExecutor',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 1,
        },
        {
            id: '275a2c46c4b9634cf6a094f3a8c9ae31',
            name: 'handleException',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: 'da15b75eda4e0ec7e2b8c5f93c5a1a8b',
            name: 'ToolResult',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
        {
            id: 'cda953b37369b866a1a03f4ff1847769',
            name: 'getJobBuildRepository',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: 'c0f490afb70743ea7362f491029f185a',
            name: 'modifyJobState',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '35f5b3c4a38e992602f1c62f7932da29',
            name: 'loadJobContext',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '210e62f4b627f1aab883ba28979f2f5a',
            name: 'execute',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: 'ca52fba9825cbee108800a0a2bc9be96',
            name: 'jobEventQueueRepository',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 4,
        },
        {
            id: 'e161474a1b3e4100ea3b8797d30b64cc',
            name: 'getSenderSessionId',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: '6fe44b173b5e34f2780e15d34968d64d',
            name: 'waitHumanFeedback',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: 'f8066b477a8694133e246f84585e5111',
            name: 'doExecute',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 3,
        },
        {
            id: 'aafa49baa009424811ef0c90f2654ba5',
            name: 'MessageContentEntity',
            symbolSize: null,
            x: null,
            y: null,
            value: null,
            category: 2,
        },
    ],
};
