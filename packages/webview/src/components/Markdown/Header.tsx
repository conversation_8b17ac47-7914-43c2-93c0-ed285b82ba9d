/* eslint-disable max-len */
import {ComponentChild} from 'preact';
import {ReactNode} from 'preact/compat';

interface Props {
    /**
     * 代码块可以设置字符串，或者是带链接的对象
     */
    title?: string | ReactNode;
    extra?: ComponentChild;
    onClick?: () => void;
}

function Header({title, extra, onClick}: Props) {
    const groupStyle = onClick ? 'group hover:cursor-pointer' : '';
    const groupHoverStyle = onClick ? 'group-hover:opacity-80' : '';

    return (
        <div
            onClick={onClick}
            className={`comate-code-header ${groupStyle} flex justify-between text-xs bg-[var(--comate-editor-background)] items-center px-3 py-1.5 rounded-t-[4px] border-b border-[#888888]/20`}
            style={{
                position: 'sticky',
                top: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.4)',
            }}
        >
            <div
                className={`${groupHoverStyle} whitespace-nowrap text-ellipsis overflow-hidden text-[var(--comate-descriptionForeground)]`}
            >
                {title ?? null}
            </div>
            <div className={`${groupHoverStyle} min-w-fit`}>
                {extra ?? null}
            </div>
        </div>
    );
}

export default Header;
