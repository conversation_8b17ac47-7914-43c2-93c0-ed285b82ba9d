/* eslint-disable complexity */
/* bca-disable */
import {useCallback, useEffect, useMemo, useRef, useState} from 'preact/hooks';
import {useTranslation} from 'react-i18next';
import cx from 'classnames';
import {debounce, fromPairs} from 'lodash';
import {SmartApplyStatus} from '@shared/protocols';
import currentFileIcon from '@/assets/currentFile.svg';
import copyIcon from '@/assets/copy.svg';
import acceptIcon from '@/assets/accept.svg';
import diffIcon from '@/assets/diff.svg';
import checkIcon from '@/assets/check.svg';
import newFileIcon from '@/assets/newFile.svg';
import terminalIcon from '@/assets/terminal.svg';
import runInTerminal from '@/assets/runInTerminal.svg';
import shinyIcon from '@/assets/shiny.svg';
import refreshIcon from '@/assets/wand.svg';

import closeIcon from '@/assets/close.svg';
import {
    MARKDOWN_CODE_ACTION_ACCEPT,
    MARKDOWN_CODE_ACTION_COPY,
    MARKDOWN_CODE_ACTION_DIFF,
    MARKDOWN_CODE_ACTION_INSERTINTOTERMINAL,
    MARKDOWN_CODE_ACTION_NEWFILE,
    MARKDOWN_CODE_ACTION_REPLACETOFILE,
    MARKDOWN_CODE_ACTION_SUFFIX,
    MARKDOWN_CODE_ACTION_INSERTTOFILE,
    MARKDOWN_CODE_ACTION_SHOWFILEINSERTDIFF,
    MARKDOWN_CODE_ACTION_SHOWFILEREPLACEDIFF,
    MARKDOWN_CODE_ACTION_VIEWFILE,
    MARKDOWN_CODE_ACTION_SMARTAPPLY,
    MARKDOWN_CODE_ACTION_REAPPLY,
    MARKDOWN_CODE_ACTION_REJECT,
    MARKDOWN_CODE_ACTION_PROCESSING,
    MARKDOWN_CODE_ACTION_RUNINTERMINAL,
} from '@/i18n/constants';
import Tooltip from '../Tooltip';
import Loading from '../Loading';

export const ActionDefinitions = [
    {key: 'smartApply', displayName: '采纳', displayNameKey: MARKDOWN_CODE_ACTION_SMARTAPPLY},
    {key: 'reApply', displayName: '重新生成', displayNameKey: MARKDOWN_CODE_ACTION_REAPPLY},
    {key: 'smartApplyAccept', displayName: '采纳', displayNameKey: MARKDOWN_CODE_ACTION_ACCEPT},
    {key: 'smartApplyReject', displayName: '放弃', displayNameKey: MARKDOWN_CODE_ACTION_REJECT},
    {key: 'insertIntoTerminal', displayName: '插入到终端中', displayNameKey: MARKDOWN_CODE_ACTION_INSERTINTOTERMINAL},
    {key: 'executeTerminalShell', displayName: '运行', displayNameKey: MARKDOWN_CODE_ACTION_RUNINTERMINAL},
    {key: 'accept', displayName: '采纳', displayNameKey: MARKDOWN_CODE_ACTION_ACCEPT},
    {key: 'replaceToFile', displayName: '采纳', displayNameKey: MARKDOWN_CODE_ACTION_REPLACETOFILE},
    {key: 'insertToFile', displayName: '插入', displayNameKey: MARKDOWN_CODE_ACTION_INSERTTOFILE},
    {key: 'copy', displayName: '复制', displayNameKey: MARKDOWN_CODE_ACTION_COPY},
    {key: 'diff', displayName: '查看变更', displayNameKey: MARKDOWN_CODE_ACTION_DIFF},
    {key: 'showFileInsertDiff', displayName: '查看变更', displayNameKey: MARKDOWN_CODE_ACTION_SHOWFILEINSERTDIFF},
    {key: 'showFileReplaceDiff', displayName: '查看变更', displayNameKey: MARKDOWN_CODE_ACTION_SHOWFILEREPLACEDIFF},
    {key: 'newFile', displayName: '新建文件', displayNameKey: MARKDOWN_CODE_ACTION_NEWFILE},
    {key: 'viewFile', displayName: '查看文件', displayNameKey: MARKDOWN_CODE_ACTION_VIEWFILE},
] as const;

export type SupportedCodeAction = typeof ActionDefinitions[number]['key'];
export const ActionKeys: SupportedCodeAction[] = ActionDefinitions.map(v => v.key);
const ActionMapping = fromPairs(ActionDefinitions.map(def => [def.key, def]));

const renderIcon = (action: SupportedCodeAction) => {
    switch (action) {
        case 'diff':
        case 'showFileInsertDiff':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: diffIcon}} />;
        case 'showFileReplaceDiff':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: diffIcon}} />;
        case 'copy':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: copyIcon}} />;
        case 'accept':
        case 'smartApplyAccept':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: acceptIcon}} />;
        case 'smartApplyReject':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: closeIcon}} />;
        case 'newFile':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: newFileIcon}} />;
        case 'insertIntoTerminal':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: terminalIcon}} />;
        case 'executeTerminalShell':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: runInTerminal}} />;
        case 'replaceToFile':
        case 'insertToFile':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: acceptIcon}} />;
        case 'viewFile':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: currentFileIcon}} />;
        case 'smartApply':
            // eslint-disable-next-line max-len
            return <div className="w-4 h-4 text-[var(--comate-link-color)]" dangerouslySetInnerHTML={{__html: shinyIcon}} />;
        case 'reApply':
            // eslint-disable-next-line max-len
            return <div className="w-4 h-4 text-[var(--comate-link-color)]" dangerouslySetInnerHTML={{__html: refreshIcon}} />;
        default:
            return null;
    }
};

interface Props {
    status?: SmartApplyStatus;
    action: SupportedCodeAction;
    showIcon?: boolean;
    onClick: (action: SupportedCodeAction) => void | Promise<void>;
    maxHiddenClass?: string;
}

function ActionButton({status, action, showIcon = true, onClick, maxHiddenClass}: Props) {
    const previousAction = useRef(action);
    const [showSuccessLabel, setSuccessLabelVisibility] = useState(false);
    const {t} = useTranslation();
    const [showTooltip, setShowTooltip] = useState(false);

    const handleMouseEnter = () => {
        if (!maxHiddenClass) {
            return;
        }
        const maxPx = /\d+/.exec(maxHiddenClass);
        if (maxPx) {
            const screenWidth = window.innerWidth;
            setShowTooltip(screenWidth < parseInt(maxPx[0], 10));
        }
    };

    const handleMouseLeave = () => {
        setShowTooltip(false);
    };

    if (action !== previousAction.current) {
        previousAction.current = action;
        setSuccessLabelVisibility(false);
    }

    const isSmartApply = action === 'smartApply';
    const applied = isSmartApply && status === SmartApplyStatus.APPLIED;
    const finalizeAction: SupportedCodeAction = applied ? 'reApply' : action;
    const loading = isSmartApply && status === SmartApplyStatus.APPLING;
    const tooltipText = loading ? t(MARKDOWN_CODE_ACTION_PROCESSING) : t(ActionMapping[action].displayNameKey);

    const handleActionClick = useCallback(
        async () => {
            if (loading || showSuccessLabel) {
                return;
            }

            try {
                await onClick(action);
                if (!['diff', 'smartApply', 'reApply'].includes(action)) {
                    setSuccessLabelVisibility(true);
                }
            }
            catch (ex) {
                //
            }
        },
        [loading, showSuccessLabel, action, onClick]
    );

    const deboncedhandleActionClick = useMemo(
        () => debounce(handleActionClick, 300, {leading: true}),
        [handleActionClick]
    );

    useEffect(
        () => {
            if (showSuccessLabel) {
                const id = setTimeout(
                    () => {
                        setSuccessLabelVisibility(false);
                        clearTimeout(id);
                    },
                    5 * 1000
                );
            }
        },
        [showSuccessLabel]
    );

    return (
        <Tooltip overlay={!showSuccessLabel && showTooltip ? tooltipText : null}>
            <a
                role="button"
                aria-label={t(ActionMapping[action].displayNameKey)}
                tabIndex={0}
                href="#"
                key={action}
                className={cx(
                    'chatbox-operation-button',
                    'focus:outline-none',
                    'hover:text-inherit',
                    'text-inherit',
                    'text-decoration-none',
                    'flex',
                    'gap-1',
                    'items-center',
                    'relative',
                    'rounded',
                    'p-0.5',
                    'bg-transparent',
                    {
                        'hover:opacity-40': !showSuccessLabel,
                        'hover:cursor-pointer': !showSuccessLabel && !loading,
                        'hover:bg-[#FFFFFF19]': !showSuccessLabel,
                        'hover:cursor-not-allowed': loading,
                    }
                )}
                onClick={deboncedhandleActionClick}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
            >
                {showSuccessLabel
                    ? (
                        <>
                            {/* eslint-disable-next-line react/no-danger */}
                            <div className="w-4 h-4 text-green-500" dangerouslySetInnerHTML={{__html: checkIcon}} />
                            <span>
                                {t(ActionMapping[finalizeAction].displayNameKey) + t(MARKDOWN_CODE_ACTION_SUFFIX)}
                            </span>
                        </>
                    )
                    : (
                        <>
                            {showIcon && (loading
                                ? <Loading className="w-4 h-4" />
                                : renderIcon(finalizeAction))}
                            <span className={cx({'text-[var(--comate-link-color)]': isSmartApply})}>
                                {loading ? '执行中' : t(ActionMapping[finalizeAction].displayNameKey)}
                            </span>
                        </>
                    )}
            </a>
        </Tooltip>
    );
}

export default ActionButton;
