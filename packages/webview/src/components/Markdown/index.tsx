/* eslint-disable max-len */
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import {FC, Suspense, lazy, memo} from 'preact/compat';
import {useCallback, useMemo, useRef} from 'preact/hooks';
import {AcceptState, ComposerCodeStatus, ComposerFileAction} from '@shared/composer';
import {partial} from 'lodash';
import {JsxMarkdownProps} from '@comate/plugin-jsx';
import useActivityCheckIn from '@/hooks/useActivityCheckIn';
import {SecuComponent} from '../SmartAgent/SecuBotView/secuComponent';
import CodeBlock from './CodeBlock';
import {FileAction} from './ComposerFileIndicator';

const LazyMermaid = lazy(() => import(/* webpackChunkName: "mermaid" */ './Mermaid'));
const ComposerFileIndicator = lazy(() => import(/* webpackChunkName: "mermaid" */ './ComposerFileIndicator'));

interface Props {
    role: 'user' | 'assistant';
    enableFileOperation?: boolean;
    onFileClick?: (action: FileAction) => void;
    content: string;
    messageId: number | string;
    actions?: Record<string, () => void>;
    numLinesToShow?: number;
}

const codeBlockStart = /^```([a-zA-Z]*)\n/;

function isOnlyCodeBlock(markdown: string | undefined): [boolean, string, string] {
    if (typeof markdown !== 'string') {
        return [false, '', ''];
    }
    const match = codeBlockStart.exec(markdown);
    if (!match) {
        return [false, '', ''];
    }
    const rest = markdown.slice(match[0].length);
    const language = match[1];

    const isOnlyCodeBlock = !rest.includes('```');
    return [isOnlyCodeBlock, rest, language];
}

function normalizeMarkdownList(markdown: string): string {
    // jsx数组对象直接返回，其它情况走正则过滤
    if (typeof markdown !== 'string') {
        return markdown;
    }
    // 处理数字列表标记
    markdown = markdown.replace(/(^|\n)(\d+\.\s?)/g, '\n\n$2');
    // 处理符号列表标记，如 "* Item" 或 "- Item"
    markdown = markdown.replace(/(^|\n)([*-]\s?)/g, '\n\n$2');

    return markdown;
}

function figureoutRealContent(content: string): string {
    if (/^{.*}\n$/.test(content)) {
        return content.slice(0, -1);
    }
    return content;
}
// const MyComponent: React.ElementType<JsxMarkdownProps> = ({role}) => {
//     return <div>Hello, {role}!</div>;
// };

const remarkPlugins = [remarkGfm];
const keepUrl = (url: string) => url;
export const JsxMarkdown: FC<
    JsxMarkdownProps & Pick<Props, 'messageId' | 'onFileClick' | 'enableFileOperation'>
> = ({messageId, role, content, actions, numLinesToShow, onFileClick, enableFileOperation}) => {
    // TODO: react-markdown + highlighter 实现打字机效果的时候滑动起来会卡顿
    // https://github.com/rehypejs/rehype-react/issues/38
    // 这里用临时方案处理一下，如果输出只有代码块的话就直接显示代码块
    // 后面看下能不能替换成 marked + highlight.js
    const [onlyCodeBlock, codeBlockContent, language] = isOnlyCodeBlock(content);
    const activityCheckIn = useActivityCheckIn();
    const showActions = role === 'assistant';

    const handleLinkClick = useCallback(
        (href: string | undefined) => {
            if (!href) {
                return;
            }
            activityCheckIn(href);
        },
        [activityCheckIn]
    );

    const actionRef = useRef<Record<string, () => void>>({});
    actionRef.current = actions!;

    const components = useMemo(
        () => ({
            a: ({href, children}: any) => {
                return (
                    <a
                        className="text-[var(--comate-link-color)] hover:text-[var(--comate-link-color)] hover:cursor-pointer hover:underline"
                        onClick={partial(handleLinkClick, href)}
                    >
                        {children}
                    </a>
                );
            },
            code: ({children, node, className, position, ...rest}: any) => {
                const composerCode =
                    /^language-(create|edit|rewrite|delete|preview|replaceFrom):(UNREADY|PROCESSING|DONE|CANCELLED):(0|1|2):(.*)/
                        .exec(className);
                // 刚开始输出```的时候，先不渲染代码块，有一个例外,composer删除代码块的例子里没有children
                if (!children && !composerCode) {
                    return null;
                }
                if (composerCode) {
                    const action = composerCode[1] as ComposerFileAction;
                    const status = composerCode[2] as ComposerCodeStatus;
                    const accepted = Number(composerCode[3]) as AcceptState;
                    const filePath = composerCode[4];
                    const content = figureoutRealContent(children);
                    return (
                        <Suspense fallback={null}>
                            <ComposerFileIndicator
                                enableFileOperation={enableFileOperation}
                                messageId={messageId}
                                content={content}
                                accepted={accepted}
                                action={action}
                                filePath={filePath}
                                status={status}
                                onFileClick={onFileClick}
                            />
                        </Suspense>
                    );
                }
                const match = /language-(\w+)/.exec(className || '');
                if (match?.[1] === 'mermaid') {
                    return (
                        <Suspense fallback={null}>
                            <LazyMermaid actions={actionRef.current}>{children}</LazyMermaid>
                        </Suspense>
                    );
                }

                if (match?.[1].startsWith('secubot')) {
                    return (
                        <Suspense fallback={null}>
                            <SecuComponent
                                onFileClick={onFileClick}
                                data={children}
                            />
                        </Suspense>
                    );
                }

                return (
                    <CodeBlock
                        messageId={messageId}
                        inline={!className && !String(children).includes('\n')}
                        actions={actionRef.current}
                        showActions={showActions}
                        showHeader={role !== 'user'}
                        numLinesToShow={numLinesToShow}
                        className={className}
                        {...rest}
                    >
                        {children}
                    </CodeBlock>
                );
            },
            table: ({children}: any) => {
                return (
                    <div className="comate-content comate-content-table-container">
                        <table>{children}</table>
                    </div>
                );
            },
        }),
        [
            enableFileOperation,
            handleLinkClick,
            showActions,
            role,
            numLinesToShow,
            messageId,
            onFileClick,
        ]
    );

    // 用户的输入全展示，svg之类的不渲染
    const rehypePlugins = useMemo(
        // https://github.com/syntax-tree/hast-util-sanitize/blob/main/lib/index.js#L669-L710
        // 这段代码会过滤掉文件链接，protocols: {href: null} 代表 href 的属性不做任何过滤
        () => (role === 'assistant' ? [rehypeRaw, () => rehypeSanitize({protocols: {href: null}})] : []),
        [role]
    );
    if (onlyCodeBlock) {
        return (
            <CodeBlock
                inline={false}
                className={`language-${language}`}
                actions={actions}
                showActions={showActions}
                showHeader={role !== 'user'}
                numLinesToShow={numLinesToShow}
            >
                {codeBlockContent}
            </CodeBlock>
        );
    }
    const normalizedContent = normalizeMarkdownList(content);
    return (
        <ReactMarkdown
            className="break-all message-content-markdown"
            remarkPlugins={remarkPlugins}
            rehypePlugins={rehypePlugins}
            // [filename](fileLink:lineNum) 如果文件路径是第一层且没有相对路径的情况下，parse后默认会丢，强制把url传下去
            urlTransform={keepUrl}
            components={components}
        >
            {normalizedContent}
        </ReactMarkdown>
    );
};

function Markdown(
    {role, content, actions, numLinesToShow, messageId, onFileClick, enableFileOperation}: Props
) {
    return (
        <JsxMarkdown
            role={role}
            messageId={messageId}
            content={content}
            actions={actions}
            numLinesToShow={numLinesToShow}
            enableFileOperation={enableFileOperation}
            onFileClick={onFileClick}
        />
    );
}

export default memo(Markdown, (prevProps, nextProps) => {
    return prevProps.content === nextProps.content
        && prevProps.enableFileOperation === nextProps.enableFileOperation
        // codeblock上的actions是会在markdown不变的基础上变化的，仅比较种类
        && Object.keys(prevProps.actions || {}).join() === Object.keys(nextProps.actions || {}).join();
});
