import {useCallback, useMemo} from 'preact/hooks';
import {AcceptState} from '@shared/composer';
import {virtualEditor} from '@/utils/virtualEditor';
import bg from '@/assets/agent/previewBackground.png';
import TestBotFileSection from '@/components/SmartAgent/AgentView/TestAgentView/FileSection';

export interface FileAction {
    action: 'file-view' | 'file-accept' | 'file-reject';
    filePath: string;
    id: number | string;
}

interface Props {
    content: string;
    messageId: number | string;
    accepted: AcceptState;
    onFileClick?: (action: FileAction) => void;
}
export function ComposerPreviewIndicator({content, messageId, accepted, onFileClick}: Props) {
    const previewInfo = useMemo(
        () => {
            try {
                return JSON.parse(content);
            }
            catch {
                return {type: 'unknown'};
            }
        },
        [content]
    );

    const handlePreviewClick = useCallback(
        (e: any) => {
            e.preventDefault();
            virtualEditor.openUrlInEditorWebview({url: previewInfo.content, title: '预览'});
        },
        [previewInfo]
    );

    switch (previewInfo.type) {
        case 'previewUrl':
            return (
                <div className="flex flex-col">
                    <span className="whitespace-pre-line">
                        ✨已为你生成符合要求的页面，以下是链接，去预览一下效果吧😊：
                    </span>
                    <div className="composer-file-indictor-url-preview" style={{backgroundImage: `url(${bg})`}}>
                        <a onClick={handlePreviewClick}>{previewInfo.content}</a>
                        <button onClick={handlePreviewClick}>预览网页</button>
                    </div>
                </div>
            );
        case 'testbot':
            return (
                <TestBotFileSection
                    messageId={messageId}
                    accepted={accepted}
                    info={previewInfo}
                    onFileClick={onFileClick}
                />
            );
        default:
            return null;
    }
}
