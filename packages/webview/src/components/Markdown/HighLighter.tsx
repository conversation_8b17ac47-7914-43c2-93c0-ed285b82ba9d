import {memo} from 'preact/compat';
import {identity} from 'lodash';
import {Prism as Syntax<PERSON>ighlight<PERSON>, SyntaxHighlighterProps} from 'react-syntax-highlighter';
import {useCallback, useEffect, useRef} from 'preact/hooks';

interface HighlighterProps extends SyntaxHighlighterProps {
    collapsed: boolean;
    numLinesToShow: number;
    children: string;
    onDidCopy?: (content: string) => void;
    /**
     * 高亮删除行和新增行，参考文档
     * https://react-syntax-highlighter.github.io/react-syntax-highlighter/demo/diff.html
     */
    diffLines?: {
        add: number[];
        remove: number[];
    };
}

function Hightlighter({
    collapsed,
    numLinesToShow,
    customStyle,
    onDidCopy,
    diffLines,
    ...props
}: HighlighterProps) {
    const ref = useRef<HTMLDivElement>(null);

    const PreWithRef = useCallback(
        props => <div {...props} ref={ref} />,
        []
    );

    const lineProps = useCallback(
        (lineNumber: number) => {
            if (collapsed && lineNumber > numLinesToShow) {
                return {
                    style: {display: 'none'},
                };
            }

            if (diffLines?.add.includes(lineNumber)) {
                return {
                    style: {
                        backgroundColor: 'var(--vscode-diffEditor-insertedLineBackground)',
                        // color: 'var(--vscode-diffEditor-insertedTextBackground)',
                        display: 'block',
                    },
                };
            }
            else if (diffLines?.remove.includes(lineNumber)) {
                return {
                    style: {
                        backgroundColor: 'var(--vscode-diffEditor-removedLineBackground)',
                        // color: 'var(--vscode-diffEditor-removedTextBackground)',
                        display: 'block',
                    },
                };
            }
            return {};
        },
        [collapsed, diffLines?.add, diffLines?.remove, numLinesToShow]
    );

    useEffect(
        () => {
            let copyHandler: ((e: ClipboardEvent) => void) | null = null;
            const node = ref.current;
            if (node && onDidCopy) {
                copyHandler = () => {
                    const selection = document.getSelection()?.toString();
                    if (selection && selection.trim().length > 0) {
                        onDidCopy(selection);
                    }
                };
                node.addEventListener('copy', copyHandler);
            }
            return () => {
                if (copyHandler && node) {
                    node.removeEventListener('copy', copyHandler);
                }
            };
        },
        [onDidCopy]
    );

    return (
        // @ts-expect-error Type 'bigint' is not assignable to type 'ReactNode'.
        <SyntaxHighlighter
            {...props}
            wrapLines
            showLineNumbers
            lineProps={lineProps}
            lineNumberStyle={{display: 'none'}}
            customStyle={customStyle}
            // @ts-expect-error
            // eslint-disable-next-line react/jsx-no-bind
            PreTag={PreWithRef}
        />
    );
}

export default memo(
    Hightlighter,
    (prevProps, nextProps) => {
        return [
            prevProps.children === nextProps.children,
            prevProps.language === nextProps.language,
            prevProps.collapsed === nextProps.collapsed,
            prevProps.numLinesToShow === nextProps.numLinesToShow,
            prevProps.diffLines === nextProps.diffLines,
            prevProps.style === nextProps.style,
        ]
            .every(identity);
    }
);
