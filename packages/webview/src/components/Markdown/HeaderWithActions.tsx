/* eslint-disable max-len */
import {useCallback, useMemo} from 'preact/hooks';
import {ComponentProps, memo} from 'preact/compat';
import {without} from 'lodash';
import {SmartApplyStatus} from '@shared/protocols';
import ellipsisIcon from '@/assets/ellipsis.svg';
import {useSmartApplyStatus} from '@/hooks/useSmartApplyStatus';
import ToolbarButton from '../ToolbarButton';
import ActionButton, {ActionKeys, SupportedCodeAction} from './ActionButton';
import Header from './Header';

interface Props {
    content: string;
    messageId?: string;
    language?: string;
    actions?: Record<string, (content?: any, language?: string, extra?: any) => void>;
    title?: ComponentProps<typeof Header>['title'];
    replaceToFileData?: {
        filePath: string;
        from: string;
        to: string;
        replaceAll?: boolean;
    };
    insertToFileData?: {
        filePath: string;
        position: {line: number, character: number};
        newText: string;
        metadata?: {needsConfirmation: boolean};
    };
    showActions?: boolean;
}

function getExtra(actions: SupportedCodeAction, replaceToFileData: Props['replaceToFileData'], insertToFileData: Props['insertToFileData']) {
    switch (actions) {
        case 'insertToFile':
        case 'showFileInsertDiff':
        case 'newFile':
        case 'viewFile':
            return insertToFileData;
        case 'replaceToFile':
        case 'showFileReplaceDiff':
        case 'diff':
            return replaceToFileData;
    }
}

const getTooltipContainer = (e: HTMLElement) => e.closest('[role="toolbar"]') as HTMLElement;
function HeaderWithActions(
    {messageId, showActions, actions, title, content, language, replaceToFileData, insertToFileData}: Props
) {
    const {smartApply, acceptedOrReject, getSmartApplyStatus} = useSmartApplyStatus();
    const status = useMemo(
        () => {
            return actions?.['smartApply'] ? getSmartApplyStatus(messageId!, content) : SmartApplyStatus.UNTOUCHED;
        },
        [actions, content, getSmartApplyStatus, messageId]
    );

    const handleActionClick = useCallback(
        async (action: SupportedCodeAction) => {
            const callback = actions && actions[action];
            if (action === 'smartApply') {
                const uniqKey = smartApply(messageId!, content);
                callback?.(content, language, {uniqKey});
                return;
            }
            else if (action === 'smartApplyAccept' || action === 'smartApplyReject') {
                acceptedOrReject(messageId!, content, action === 'smartApplyAccept');
                return;
            }
            const extra = getExtra(action, replaceToFileData, insertToFileData);
            if (callback) {
                await callback(content, language, extra);
            }
        },
        [actions, insertToFileData, replaceToFileData, content, language, smartApply, messageId, acceptedOrReject]
    );

    // 如果包含智能粘贴，则外面仅保留复制和智能粘贴
    const [flatActions, collapsedActions] = useMemo(
        () => {
            const acceptGroup = ['smartApplyAccept', 'smartApplyReject'] as SupportedCodeAction[];
            if (status === SmartApplyStatus.INQUIRE) {
                return [acceptGroup];
            }
            const validActions = ActionKeys.filter(v => actions && actions[v]);
            const flatActionKeys: SupportedCodeAction[] = validActions.includes('insertIntoTerminal')
                ? ['smartApply', 'copy', 'insertIntoTerminal']
                : ['smartApply', 'copy'];
            return validActions.includes('smartApply')
                ? [flatActionKeys, without(validActions, ...flatActionKeys, ...acceptGroup)]
                : [validActions];
        },
        [actions, status]
    );

    const actionSizes = {
        1: '',
        2: 'max-[260px]:[&_span]:hidden',
        3: 'max-[320px]:[&_span]:hidden',
        4: 'max-[400px]:[&_span]:hidden',
    };

    const flatActionCount = flatActions.length + 1;
    const maxHiddenClass = flatActionCount > 4
        ? 'max-[500px]:[&_span]:hidden'
        : actionSizes[flatActionCount] || '';

    const extra = showActions
        ? (
            <div
                role="toolbar"
                tabIndex={0}
                aria-label="代码工具栏"
                className={`flex text-xs justify-end gap-2 flex-1 items-center message-content-code-actions ${maxHiddenClass}`}
            >
                {flatActions.map(action => (
                    <ActionButton
                        key={action}
                        action={action}
                        status={status}
                        onClick={handleActionClick}
                        maxHiddenClass={maxHiddenClass}
                    />
                ))}
                {collapsedActions?.length && (
                    <ToolbarButton
                        icon={ellipsisIcon}
                        placement="bottomRight"
                        showArrow={false}
                        getTooltipContainer={getTooltipContainer}
                        title={
                            <div className="flex flex-col gap-1">
                                {collapsedActions.map(action => (
                                    <ActionButton
                                        key={action}
                                        action={action}
                                        status={status}
                                        onClick={handleActionClick}
                                        maxHiddenClass={maxHiddenClass}
                                    />
                                ))}
                            </div>
                        }
                        ariaTitle="更多操作"
                    />
                )}
            </div>
        )
        : null;

    return <Header title={title} extra={extra} />;
}

export default memo(HeaderWithActions);
