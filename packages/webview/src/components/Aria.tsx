let timer: NodeJS.Timeout | null = null;

const send = (message: string) => {
    timer && clearTimeout(timer);
    timer = setTimeout(
        () => {
            const element = document.getElementById('aria-live-polite');
            // console.log(99991, element);
            if (element) {
                element.innerText = message;
            }
        },
        200
    );
};

const isWindows = window.navigator.userAgent.includes('Windows');

export const ariaHelpers = {
    send,
    sendChatMessage: (message: {prompt: string}) => {
        // eslint-disable-next-line max-len
        send(`${message.prompt}, 已发送聊天消息。正在生成回答，生成完成后会自动朗读，如果需要对回答进行操作，请使用${isWindows ? 'Ctrl' : 'Command'} + L快捷键快速定位到回答, 若要停止生成，请按ctrl+c`);
    },
};
