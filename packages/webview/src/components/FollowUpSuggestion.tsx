import {memo} from 'preact/compat';
import {Suggestion} from '@/interface';
import {SuggestionText} from '@/i18n/constants';
import Suggestions from './Suggestions';

const helpItem = {prompt: '/help 你能做些什么呢？', displayTextKey: SuggestionText.HELP1};

export const DEFAULT_SUGGESTIONS: Suggestion[] = [
    ...(
        $features.PLATFORM === 'poc'
            ? []
            : [{
                id: -1,
                displayTextKey: helpItem.displayTextKey,
                prompt: helpItem.prompt,
                promptTranslationKey: helpItem.displayTextKey,
                type: 'help',
                needContext: false,
            }] as const
    ),
    ...(
        // internal 保留生成单测的引导
        $features.PLATFORM === 'internal'
            ? [{
                id: 0,
                displayTextKey: SuggestionText.GENERATE_UT,
                prompt: '为下面这段代码生成单测',
                promptTranslationKey: SuggestionText.UT_PROMPT,
                type: 'ut',
                needContext: true,
            }] as const
            : []
    ),
    {
        id: 1,
        displayTextKey: SuggestionText.GENERATE_DOC,
        prompt: '为下面这段代码生成注释',
        promptTranslationKey: SuggestionText.DOC_PROMPT,
        type: 'doc',
        needContext: true,
    },
];

export const UNIT_TEST_SUGGESTIONS: Suggestion[] = [
    {
        id: 2,
        displayTextKey: SuggestionText.UT_SETTING,
        prompt: '修改生成单测的配置',
        type: 'utSetting',
    },
];

interface Props {
    showUnitTestSuggestion: boolean;
    handleClick: (id: number) => void;
}

function FollowUpSuggestion({showUnitTestSuggestion, handleClick}: Props) {
    return (
        <div className="mt-5 mb-4 px-2 flex animate-appear">
            <div className="pt-0.5 whitespace-nowrap text-[var(--comate-descriptionForeground)]">你可能还需要：</div>
            <Suggestions
                items={showUnitTestSuggestion ? UNIT_TEST_SUGGESTIONS : DEFAULT_SUGGESTIONS}
                onClick={handleClick}
                mode="button"
            />
        </div>
    );
}

export default memo(FollowUpSuggestion);
