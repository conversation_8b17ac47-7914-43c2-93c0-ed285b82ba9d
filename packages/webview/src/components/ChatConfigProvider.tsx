import {createContext, ComponentChild} from 'preact';
import {ColorTheme} from '@shared/protocols';
import {useContext} from 'preact/hooks';

export type MessageFeedback = 'like' | 'dislike' | undefined;

export type WebviewConsumer = 'vscode' | 'jetbrains' | 'xcode' | 'vs';

interface ContextValue {
    webviewConsumer: WebviewConsumer;
    theme: ColorTheme;
    // 当前消息的 ID
    currentMessageId: number | undefined;
    messageOrder: Record<number, number>;
    setOrder: (id: number, order: number) => void;
    messageFeedback: Record<number, MessageFeedback>;
    setFeedback: (id: number, feedback: MessageFeedback) => void;
}

const Context = createContext<ContextValue>({
    webviewConsumer: $features.WEBVIEW_CONSUMER,
    currentMessageId: undefined,
    theme: 'dark',
    messageOrder: {},
    setOrder: () => {},
    messageFeedback: {},
    setFeedback: () => {},
});

interface Props {
    context: ContextValue;
    children: ComponentChild;
}

export default function ChatConfigProvider({context, children}: Props) {
    return (
        <Context.Provider value={context}>
            {children}
        </Context.Provider>
    );
}

export function useChatConfig() {
    return useContext(Context);
}
