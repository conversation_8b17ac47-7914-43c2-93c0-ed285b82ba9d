/* eslint-disable max-len */
/* bca-disable */
import {useState, useMemo} from 'preact/hooks';
import {partial} from 'lodash';
import closeIcon from '@/assets/close.svg';
import FileIcon from '../components/FileIcon';

interface Props {
    currentKnowledge: Array<{id: string, label: string}>;
    refWidth?: number;
    onDelete: (knowledge: string) => void;
}

export function calculateMaxTagNum(
    currentKnowledge: Props['currentKnowledge'],
    containerWidth: number,
    maxWidth: number
) {
    const tags = currentKnowledge.map(item => item.label);
    return tags.reduce(
        (acc, tag) => {
            try {
                const tagCalculateWidth = Array.from(tag).reduce(
                    (acc: number, char: string) => {
                        return acc + (char.charCodeAt(0) > 256 ? 12 : 7);
                    },
                    30
                );
                const tagWidth = Math.min(tagCalculateWidth, maxWidth);

                const newTotalWidth = acc.totalWidth + tagWidth;
                const exceedContainerWidth = newTotalWidth > (containerWidth - 22);
                const newCurLineNum = exceedContainerWidth ? acc.curLineNum + 1 : acc.curLineNum;
                if (newCurLineNum > 2) {
                    return acc;
                }
                const newTotalWidthReset = exceedContainerWidth ? tagWidth : newTotalWidth;
                const newCountTag = exceedContainerWidth && newCurLineNum > 2 ? acc.countTag : acc.countTag + 1;

                return {
                    totalWidth: newTotalWidthReset,
                    curLineNum: newCurLineNum,
                    countTag: newCountTag,
                    maxDisplayNum: newCountTag,
                };
            }
            catch (ex) {
                return acc;
            }
        },
        {totalWidth: 0, curLineNum: 1, countTag: 0, maxDisplayNum: 0}
    );
}

function KnowledgeTagWrapper({currentKnowledge, refWidth, onDelete}: Props) {
    const maxWidth = (refWidth ?? 360) * 0.3;
    const [isHidden, setIsHidden] = useState(true);
    const maxDisplayNum = useMemo(
        () => {
            const {maxDisplayNum} = calculateMaxTagNum(currentKnowledge, refWidth ?? 360, maxWidth);
            return maxDisplayNum;
        },
        [currentKnowledge, refWidth, maxWidth]
    );

    const hidden = isHidden || (currentKnowledge.length <= maxDisplayNum) ? 'hidden' : '';

    if (currentKnowledge.length === 0) {
        return <></>;
    }
    return (
        <div
            onMouseLeave={partial(setIsHidden, true)}
            className="absolute bottom-1 w-full pt-1 border-2 solid border-t-[var(--vscode-widget-shadow)] border-l-transparent border-r-transparent border-b-transparent border-opacity-60 flex flex-wrap gap-y-1"
        >
            <div
                style={{width: '-webkit-fill-available'}}
                className={`absolute bottom-2 mx-1 my-3 p-1 bg-[var(--comate-inputOverlay-background)] shadow-[0_0_8px_2px_var(--vscode-widget-shadow)] rounded-md border-solid border-[var(--comate-separator-border)] translate-y-[-1px] z-[1] ${hidden}`}
            >
                {currentKnowledge.slice(maxDisplayNum).map(item => {
                    return (
                        <div
                            key={item}
                            title={item.label}
                            className="option rounded-md px-2 py-1 flex items-center justify-between gap-2 whitespace-nowrap overflow-hidden hover:bg-[var(--comate-listItem-hoverBackground)] hover:cursor-pointer"
                        >
                            <div className="flex items-center gap-[2px]">
                                <FileIcon filename={item.label} className="w-3 h-3 min-w-3 max-w-3" />
                                {item.label}
                            </div>
                            <span
                                className="w-3 min-w-[12px] relative text-[var(--comate-input-foreground)] cursor-pointer"
                                dangerouslySetInnerHTML={{__html: closeIcon}}
                                onClick={partial(onDelete, item.id)}
                            />
                        </div>
                    );
                })}
            </div>
            {currentKnowledge.slice(0, maxDisplayNum).map(item => {
                return (
                    <span
                        key={item.id}
                        style={{maxWidth: maxWidth}}
                        className="text-[var(--comate-input-foreground)] text-xs rounded justify-between items-center overflow-hidden group flex bg-[var(--comate-optionButton-hoverBackground)] opacity-80 px-1 mx-1"
                    >
                        <span>
                            <FileIcon filename={item.label} className="w-3 h-3 min-w-fit mr-[2px]" />
                        </span>
                        <span className="truncate">
                            {item.label}
                        </span>
                        <span
                            className="w-[10px] min-w-[8px] hidden relative text-[var(--comate-input-foreground)] group-hover:inline cursor-pointer left-[2px]"
                            dangerouslySetInnerHTML={{__html: closeIcon}}
                            onClick={partial(onDelete, item.id)}
                        />
                    </span>
                );
            })}
            {currentKnowledge.length > maxDisplayNum && (
                <span
                    onMouseOver={partial(setIsHidden, false)}
                    className="text-[var(--comate-input-foreground)] text-xs rounded justify-between items-center overflow-hidden group flex bg-[var(--comate-optionButton-hoverBackground)] opacity-80 px-1 mx-1 flex-shrink-0 hover:cursor-default"
                >
                    +{currentKnowledge.length - maxDisplayNum}
                </span>
            )}
        </div>
    );
}

export default KnowledgeTagWrapper;
