.vscode-dark,
body.dark {
    --vscode-textSeparator-foreground:rgba(255, 255, 255, 0.18);

    --chat-input-outer-background: linear-gradient(180deg, rgba(31, 31, 31, 0.9) 0.91%, #333333 107.53%);
    --chat-input-border-color: linear-gradient(118.16deg, #26CCFF 0.8%, #7C7D80 20.58%);
}

.vscode-light,
body.light {
    --vscode-textSeparator-foreground:rgba(0, 0, 0, 0.18);

    --chat-input-outer-background: linear-gradient(180deg, rgba(242, 242, 242, 0) 0.91%, #F3F3F3 107.53%);
    --chat-input-border-color:linear-gradient(118.16deg, #26CCFF 0.8%, #B4B4B4 20.58%)
}

.messages-container {
    overflow-anchor: none;
    background-color: var(--comate-editor-background);
}

.message-scroll-anchor {
    overflow-anchor: auto;
}

button:focus {
    outline: none;
}

.fade-in {
    opacity: 0;
    transform: translateY(20vh);
    visibility: hidden;
    transition: opacity 0.4s ease-in-out, transform 0.8s ease-in-out;
    will-change: opacity, visibility;
}

.fade-in.fade-in-active {
    opacity: 1;
    transform: none;
    visibility: visible;
}

.chat-input-wrapper {
    background: var(--chat-input-outer-background);
    backdrop-filter: blur(5px);
}

@media screen and (max-width: 300px) {
    .comate-chat-userguide {
        display: none;
    }
}
