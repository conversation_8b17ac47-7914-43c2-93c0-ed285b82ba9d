/* bca-disable */
import {Step, TaskStatus} from '@shared/protocols';
import {useMemo} from 'preact/hooks';
import Progress from '../Progress';
import {STATUS_CONFIG_MAPPING} from '../Progress/ProgressItem';

export default function StepItem({desc, status, tasks}: Step) {
    const icon = useMemo(
        () => {
            if (status === TaskStatus.INIT) {
                return <div className="rounded-full opacity-10 w-4 h-4 border-4 border-white" />;
            }
            const [statusIcon, , statusIconBgColor] = STATUS_CONFIG_MAPPING[status];
            return <div className={`${statusIconBgColor} w-4 h-4`} dangerouslySetInnerHTML={{__html: statusIcon}} />;
        },
        [status]
    );

    return (
        <div className="flex flex-col gap-2">
            <div className="flex gap-2 items-center text-white mt-1 text-[13px] opacity-90">
                {icon}
                {desc}
            </div>
            <Progress tasks={tasks} />
        </div>
    );
}
