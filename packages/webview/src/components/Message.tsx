import {useEffect} from 'preact/hooks';

interface Props {
    content: string;
    messageVisible: boolean;
    setMessageVisible: (visible: boolean) => void;
    // 自动关闭的延时，单位秒。设为 0 时不自动关闭，默认值为 3
    duration?: number;
}

function Message({content, messageVisible, setMessageVisible, duration = 3}: Props) {
    useEffect(
        () => {
            // eslint-disable-next-line @typescript-eslint/init-declarations
            let timer: NodeJS.Timeout | string | number | undefined;
            if (messageVisible) {
                timer = setTimeout(
                    () => setMessageVisible(false),
                    duration * 1000
                );
            }

            return () => clearTimeout(timer);
        },
        [duration, messageVisible, setMessageVisible]
    );

    if (messageVisible) {
        return (
            <div className="fixed top-16 left-1/2 transform -translate-x-1/2 text-[var(--comate-descriptionForeground)]">
                <span className="bg-[#888888]/20 p-1 rounded">
                    {content}
                </span>
            </div>
        );
    }
    return null;
}

export default Message;
