import {FC, ReactNode} from 'preact/compat';
import RcCheckbox, {CheckboxProps} from 'rc-checkbox';
import './index.css';

// 需要重新定义的属性
interface ReassignProps {}

export type Props = Omit<CheckboxProps, keyof ReassignProps> & ReassignProps;
export {CheckboxChangeEvent} from 'rc-checkbox';
const Checkbox = RcCheckbox as FC<Props>;

export default (props: Props) => {
    return <Checkbox {...props} />;
};

export function CheckboxWithLabel({label, ...props}: Omit<Props, 'name'> & {label: ReactNode, name: string}) {
    return (
        <label className="flex items-center">
            <Checkbox {...props} />
            <span className="ml-1" style={{lineHeight: '16px', display: 'inline-block'}}>{label}</span>
        </label>
    );
}
