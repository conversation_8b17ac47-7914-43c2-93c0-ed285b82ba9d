/* Default state */
.rc-checkbox {
    white-space: nowrap;
    cursor: pointer;
    outline: none;
    display: inline-block;
    position: relative;
    line-height: 1;
    height: 16px;
    vertical-align: middle;
    --ant-control-interactive-size: 16px;
    --ant-line-width-bold: 2px;
    --ant-motion-duration-fast: 0.1s;
    --ant-motion-duration-mid: 0.2s;
    --ant-motion-duration-slow: 0.3s;
    --ant-motion-ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);
    --ant-motion-ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);
    --ant-motion-ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
    --ant-motion-ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
    --ant-motion-ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);
    --ant-motion-ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);
    --ant-motion-ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    --ant-motion-ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
    --ant-color-white: #fff;
    --ant-line-type: solid;
    --ant-line-width: 2px;
    --ant-color-bg-container: transparent;
    --ant-color-border: var(--vscode-menu-border);
    --ant-border-radius-sm: 0px;
}

.rc-checkbox:hover .rc-checkbox-inner,
.rc-checkbox-input:focus + .rc-checkbox-inner {
    border-color: var(--comate-link-color);
}

.rc-checkbox-inner {
    box-sizing: border-box;
    display: block;
    width: var(--ant-control-interactive-size);
    height: var(--ant-control-interactive-size);
    direction: ltr;
    background-color: var(--ant-color-bg-container);
    border: var(--ant-line-width) var(--ant-line-type) var(--ant-color-border);
    border-radius: var(--ant-border-radius-sm);
    border-collapse: separate;
    transition: all var(--ant-motion-duration-slow);
}

.rc-checkbox-input {
    position: absolute;
    left: 0;
    z-index: 9999;
    cursor: pointer;
    opacity: 0;
    top: 0;
    bottom: 0;
    right: 0;
}

/* Checked state */
.rc-checkbox-checked:hover .rc-checkbox-inner {
    border-color: var(--comate-link-color);
}

.rc-checkbox-checked .rc-checkbox-inner {
    border-color: var(--comate-link-color);
    background-color: var(--comate-link-color);
}

.rc-checkbox-inner:after {
    box-sizing: border-box;
    position: absolute;
    top: 50%;
    inset-inline-start: 25%;
    display: table;
    width: calc(var(--ant-control-interactive-size) / 14* 5);
    height: calc(var(--ant-control-interactive-size) / 14* 8);
    border: var(--ant-line-width-bold) solid var(--ant-color-white);
    border-top: 0;
    border-inline-start: 0;
    transform: rotate(45deg) scale(0) translate(-50%, -50%);
    opacity: 0;
    content: "";
    transition: all var(--ant-motion-duration-fast) var(--ant-motion-ease-in-back), opacity var(--ant-motion-duration-fast);
}

.rc-checkbox-checked .rc-checkbox-inner:after {
    opacity: 1;
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    transition: all var(--ant-motion-duration-mid) var(--ant-motion-ease-out-back) var(--ant-motion-duration-fast);
}

/* disabled 的部分未验收
.rc-checkbox-disabled.rc-checkbox-checked:hover .rc-checkbox-inner {
    border-color: var(--vscode-menu-border);
}

.rc-checkbox-disabled.rc-checkbox-checked .rc-checkbox-inner {
    background-color: #f3f3f3;
    border-color: var(--vscode-menu-border);
}

.rc-checkbox-disabled.rc-checkbox-checked .rc-checkbox-inner:after {
    border-color: #cccccc;
}

.rc-checkbox-disabled:hover .rc-checkbox-inner {
    border-color: var(--vscode-menu-border);
}

.rc-checkbox-disabled .rc-checkbox-inner {
    border-color: var(--vscode-menu-border);
    background-color: #f3f3f3;
}

.rc-checkbox-disabled .rc-checkbox-inner:after {
    border-color: #f3f3f3;
}

.rc-checkbox-disabled .rc-checkbox-inner-input {
    cursor: default;
} */
