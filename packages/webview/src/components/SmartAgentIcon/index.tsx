/* bca-disable */
import cx from 'classnames';
import darkShinyIcon from '@/assets/agent/darkShiny.svg';
import lightShinyIcon from '@/assets/agent/lightShiny.svg';
import './index.css';
import {useChatConfig} from '../ChatConfigProvider';
interface Props {
    className?: string;
}
export const SmartAgentIcon = ({className}: Props) => {
    const {theme} = useChatConfig();
    return (
        <div
            className={cx(
                'w-16 h-6',
                ' justify-center gap-0.5 text-sm smart-agent-icon rounded-[44px] ',
                className
            )}
        >
            <div className="px-2 h-full flex-row items-center">
                <div
                    className="w-3.5 h-3.5 inline-block align-middle"
                    dangerouslySetInnerHTML={{__html: theme === 'dark' ? darkShinyIcon : lightShinyIcon}}
                >
                </div>
                <span className="agent-gradient-text align-middle">智能体</span>
            </div>
        </div>
    );
};
