import {EventMessage, AutoWorkAPIProxyCollection} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';

/**
 * 用一个固定的代理，来协调WebView的请求
 * @param api `AutoWorkAPIProxyCollection`的key
 * @returns `AutoWorkAPIProxyCollection[key]['response']`中定义的返回值
 */
const createProxyAPI = <K extends keyof AutoWorkAPIProxyCollection>(api: K) => {
    return async (params?: Omit<AutoWorkAPIProxyCollection[K]['params'], 'userName'>) => {
        const response = await messageHandler.send(
            EventMessage.AutoWorkAPIProxy,
            {api, params}
        );
        return response as AutoWorkAPIProxyCollection[K]['response'];
    };
};

/** 把一个链接转换成知识的uuid */
export const apiGetGenerateLinkToUuid = createProxyAPI('generateLinkToUuid');
/** 获取插件的配置 */
export const apiGetExtensionConfig = createProxyAPI('getExtensionConfig');
/** 通过关键字查询知识集 */
export const apiGetKnowledgeList = createProxyAPI('fetchKnowledgeList');
/** 获取小流量的状态 */
export const apiGetAccessTo = createProxyAPI('getAccessTo');
/** 设置对话意图识别的开关状态 */
export const apiPostChatintentRecognition = createProxyAPI('setChatIntentRecognition');
/** 获取对话意图识别的开关状态 */
export const apiGetChatintentRecognition = createProxyAPI('getChatIntentRecognition');
