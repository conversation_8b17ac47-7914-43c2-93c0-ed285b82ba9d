/* eslint-disable complexity */
/* eslint-disable max-len */
/* bca-disable */
import {ComponentChild} from 'preact';
import {ReactNode, forwardRef} from 'preact/compat';
import {memo, useCallback} from 'preact/compat';
import {ColorTheme, EventMessage} from '@shared/protocols';
import {useTranslation} from 'react-i18next';
// import comateLogoLight from '@/assets/comate-logo-light.png';
// import comateLogoDark from '@/assets/comate-logo-dark.png';
import comateNyLogoLight from '@/assets/comate-logo-light-ny.png';
import comateNyLogoDark from '@/assets/comate-logo-dark-ny.png';
import {
    WELCOME_DESCRIPTIONS_PART1,
    WELCOME_DESCRIPTIONS_PART2,
    WELCOME_DESCRIPTIONS_SEPARATOR,
} from '@/i18n/constants';
import {messageHandler} from '@/utils/messageHandler';
import useActivityCheckIn from '@/hooks/useActivityCheckIn';
import {isJetbrains} from '@/config';
import Keystroke from './Keystroke';
import {useChatConfig} from './ChatConfigProvider';
import Banner, {BannerState} from './Banner';
import Login from './Login';

interface Props {
    showLogin: boolean;
    bannerState?: BannerState;
    isGithubUser: boolean;
    setBannerVersion: (version: number) => void;
    promotion?: ComponentChild;
    activityPicUrl?: string;
    activityId?: string;
    activityUrl?: string;
    theme?: ColorTheme;
}

interface BrandProps {
    promotion?: ReactNode;
    description?: ReactNode;
}

export function Brand({promotion, description}: BrandProps) {
    const {theme} = useChatConfig();
    const isTest = $features.ENVIRONMENT === 'test';
    const newYearStyle = {
        position: 'relative',
        width: '21.25rem',
        height: '7rem',
    };

    return (
        <div className="flex flex-col justify-center max-h-[260px] min-h-[100px] h-full items-center comate-logo">
            {promotion}
            <img
                className="w-72 mb-7 object-contain"
                src={theme === 'light' ? comateNyLogoLight : comateNyLogoDark}
                style={newYearStyle}
            />
            {isTest && (
                <h1 className="text-xs font-semibold text-[var(--comate-editor-foreground)] mt-1">
                    <span className="text-red-500">Test</span>
                </h1>
            )}
            {description}
        </div>
    );
}

const Welcome = forwardRef(function Welcome({
    showLogin,
    bannerState,
    setBannerVersion,
    promotion,
}: Props, ref) {
    const {t} = useTranslation();
    const activityCheckIn = useActivityCheckIn();
    const showBanner = $features.PLATFORM !== 'poc'
        && bannerState
        && bannerState.bannerShow
        && bannerState.bannerVersion !== bannerState.activityVersion;
    const activityVersion = bannerState?.activityVersion;

    const closeBanner = useCallback(
        () => {
            setBannerVersion(activityVersion || -1);
            messageHandler.send(EventMessage.BannerVersionRefreshEvent, activityVersion);
        },
        [activityVersion, setBannerVersion]
    );
    const uri = bannerState?.actionUri || '';
    const clickBanner = useCallback(
        () => {
            activityCheckIn(uri);
        },
        [activityCheckIn, uri]
    );

    return (
        <>
            <div
                className="bg-[linear-gradient(180deg,#2098F3_0%,#2098F300_100%)] opacity-30 absolute left-0 right-0 top-[38px] h-[400px]"
                style={{pointerEvents: 'none'}}
            />
            <div className="h-full flex flex-col">
                {showBanner && <Banner bannerState={bannerState} onClick={clickBanner} onClose={closeBanner} />}
                <div className="h-full flex flex-col pt-5 overflow-hidden" aria-hidden="true">
                    <Brand
                        promotion={promotion}
                        description={(
                            <div className="text-[13px] flex justify-center items-center gap-1 flex-wrap user-select-none comate-description" aria-hidden="true">
                                <p className="opacity-40">
                                    {t(WELCOME_DESCRIPTIONS_PART1)}
                                </p>
                                <Keystroke value="cmd" />
                                {isJetbrains && <Keystroke value="shift" />}
                                <Keystroke value="y" />
                                <p className="opacity-40">
                                    {t(WELCOME_DESCRIPTIONS_SEPARATOR)}
                                </p>
                                <p className="opacity-40">
                                    {t(WELCOME_DESCRIPTIONS_PART2)}
                                </p>
                            </div>
                        )}
                    />
                    {showLogin === true && (
                        <Login ref={ref} />
                    )}
                </div>
            </div>
        </>
    );
});

export default memo(Welcome);
