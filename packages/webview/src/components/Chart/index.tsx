import {useEffect, useRef, useImperativeHandle, forwardRef, HTMLAttributes} from 'preact/compat';
import {init, EChartsType} from 'echarts/core';
import {EChartsOption} from 'echarts';
import {MutableRef} from 'preact/hooks';
import {throttle} from 'lodash';

export interface ChartProps extends Omit<HTMLAttributes<HTMLDivElement>, 'ref'> {
    options: EChartsOption;
    theme: 'light' | 'dark';
}

const Chart = forwardRef<{chart: MutableRef<EChartsType | null>}, ChartProps>(({options, theme, ...props}, ref) => {
    const containerRef = useRef<HTMLDivElement | null>(null);
    const chart = useRef<EChartsType | null>(null);

    useImperativeHandle(ref, () => ({
        chart,
    }));

    useEffect(
        () => {
            const resizeHandler = throttle(() => chart.current?.resize(), 500);
            if (containerRef.current) {
                const container = containerRef.current;
                chart.current = init(container, theme);
                chart.current.setOption(options);
                window.addEventListener('resize', resizeHandler);
            }
            return () => {
                chart.current?.dispose();
                window.removeEventListener('resize', resizeHandler);
            };
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        []
    );

    return <div ref={containerRef} {...props} />;
});

export default Chart;
