import {Graph<PERSON>hart as EchartsGraph<PERSON>hart} from 'echarts/charts';
import {GraphicComponent, TooltipComponent, LegendComponent} from 'echarts/components';
import {CanvasRenderer} from 'echarts/renderers';
import * as echarts from 'echarts/core';
import Chart, {ChartProps} from '.';

echarts.use([EchartsGraphChart, GraphicComponent, TooltipComponent, LegendComponent, CanvasRenderer]);

export default function Graph<PERSON>hart(props: ChartProps) {
    return <Chart {...props} />;
}
