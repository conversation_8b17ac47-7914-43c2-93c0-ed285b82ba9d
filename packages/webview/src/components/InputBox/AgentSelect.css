.vscode-dark,
body.dark {
    --agent-select-background: rgba(255, 255, 255, 0.1);
    --agent-select-color: rgba(252, 253, 255, 1);
    --agent-select-btn-color: rgba(255, 255, 255, 0.5);
}

.vscode-light,
body.light {
    --agent-select-background: rgba(0, 0, 0, 0.1);
    --agent-select-color: rgba(0, 0, 0, 1);
    --agent-select-btn-color: rgba(0, 0, 0, 0.5);
}

.agent-select {
    display: flex;
    flex-shrink: 0;
    height: 24px;
    border-radius: 24px;
    align-items: center;
    padding: 0 8px;
    cursor: pointer;
    gap: 4px;
    font-size: 12px;
    background-color: var(--agent-select-background);
    color: var(--agent-select-color);
}

.agent-select [data-role="button"] {
    color: var(--agent-select-btn-color);
}