/* eslint-disable complexity, max-len */
/* bca-disable */
import {JSX} from 'preact';
import {forwardRef, memo} from 'preact/compat';
import {useTranslation} from 'react-i18next';
import {useCallback, useEffect, useImperativeHandle, useRef, useState} from 'preact/hooks';
import platform from '@shared/platform';
import {noop} from 'lodash';
import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import {INPUT_LEGACY_PLACEHOLDER, INPUT_NOTBOUND_PLACEHOLDER} from '@/i18n/constants';
import flightIcon from '@/assets/flight.svg';
import ExtensionPopover from '../ExtensionPopover';
import {isEnterKeyPressed, isMainKeyPressed} from './utils/is';

interface Props {
    submitDisabled?: boolean;
    onSubmit: (text: string) => void;
    onChange?: (text: string) => void;
    refHeight?: number;
}

interface RefProps {
    focus: () => void;
}
const InputBox = forwardRef<RefProps, Props>(
    function InputBox({submitDisabled, onSubmit, onChange, refHeight}, inputRef) {
        const isWindows = window.navigator.userAgent.includes('Windows');
        const metaKey = isWindows ? 'Ctrl' : '⌘';

        const [value, setValue] = useState<string>('');
        const ref = useRef<HTMLTextAreaElement>(null);
        const {t} = useTranslation();
        const [isShowExtensionPopover, setIsShowExtensionPopover] = useState(false);

        const disabled = value.trim().length <= 0 || submitDisabled;

        const resizeTextArea = () => {
            // 直接设置高度可能 scrollHeight 还未更新
            setTimeout(
                () => {
                    const textarea = ref.current;
                    if (textarea) {
                        textarea.style.height = '0px';
                        textarea.style.overflow = textarea.scrollHeight > 300 ? 'scroll' : 'hidden';
                        textarea.style.height = `${Math.min(textarea.scrollHeight, 300)}px`;
                    }
                },
                0
            );
        };

        const updateValue = useCallback(
            (value: string) => {
                setValue(value);
                onChange && onChange(value);
                resizeTextArea();
            },
            [onChange]
        );

        const handleChange = useCallback(
            (e: any) => {
                setIsShowExtensionPopover(['@', '/', '#'].includes(e.target.value.trim()));
                updateValue(e.target.value);
            },
            [updateValue]
        );

        const handleSubmit = useCallback(
            () => {
                if (!disabled) {
                    if (isShowExtensionPopover) {
                        const url = platform.resolve('feedbackUrl');
                        messageHandler.send(
                            EventMessage.LinkClickEvent,
                            url
                        );
                    }
                    else {
                        onSubmit(value);
                    }
                    updateValue('');
                }
                setIsShowExtensionPopover(false);
            },
            [disabled, onSubmit, updateValue, value, isShowExtensionPopover]
        );

        const handleKeyDown = useCallback(
            (event: JSX.TargetedKeyboardEvent<HTMLTextAreaElement>) => {
                if (event.defaultPrevented) {
                    return;
                }
                if (isEnterKeyPressed(event) && isMainKeyPressed(event)) {
                    // enter + shift
                    updateValue(value + '\n');
                    event.preventDefault();
                }
                else if (isEnterKeyPressed(event) && !isMainKeyPressed(event)) {
                    const canSendValue = value.trim().length > 0;
                    // when empty，should not create a new line
                    if (disabled && value) {
                        // when disabled, enter should create a new line
                        updateValue(value + '\n');
                    }
                    else if (canSendValue) {
                        handleSubmit();
                    }
                    event.preventDefault();
                }
            },
            [updateValue, value, disabled, handleSubmit]
        );

        useEffect(
            () => {
                // 初始化时执行一次，设置下 textarea 的高度
                resizeTextArea();
                if (ref.current) {
                    ref.current.focus();
                    const resizeObserver = new ResizeObserver(resizeTextArea);
                    resizeObserver.observe(ref.current);
                    const refCopy = ref.current;

                    return () => {
                        resizeObserver.unobserve(refCopy);
                    };
                }
            },
            []
        );

        useEffect(
            () => {
                messageHandler.listen(EventMessage.CurrentQueryEvent, () => {
                    return {
                        prompt: value,
                        agent: '',
                        slash: '',
                        knowledgeList: [],
                    };
                });
            },
            [value]
        );

        useImperativeHandle(inputRef, () => ({
            focus: () => ref.current?.focus(),
            input: value => {
                setValue(value);
                setIsShowExtensionPopover(true);
            },
        }));

        const onSelectAgent = noop;
        const [currentSelect, setCurrentSelect] = useState<string | undefined>('goToComateSite');

        return (
            <div className="relative">
                {isShowExtensionPopover && (
                    <ExtensionPopover
                        items={[{id: 'goToComateSite', name: t(INPUT_NOTBOUND_PLACEHOLDER)}]}
                        currentSelect={currentSelect}
                        setCurrentSelect={setCurrentSelect}
                        type="agent"
                        refHeight={refHeight}
                        onSelect={onSelectAgent}
                        searchValue={value}
                    />
                )}

                <textarea
                    ref={ref}
                    className="w-full rounded block bg-transparent text-[var(--comate-input-foreground)] py-2 pl-3 pr-8 resize-none outline outline-1 outline-[var(--vscode-menu-border,#88888833)] outline-offset-[-1px] focus:outline-[#6391F9] placeholder:text-[var(--comate-input-placeholderForeground)] placeholder:opacity-90"
                    placeholder={t(INPUT_LEGACY_PLACEHOLDER, {metaKey})}
                    value={value}
                    onInput={handleChange}
                    onKeyDown={handleKeyDown}
                />
                <button
                    className={`absolute right-[13px] bottom-[10px] ${
                        disabled ? 'text-[var(--comate-descriptionForeground)]' : 'text-[var(--comate-link-color)] hover:opacity-90'
                    }`}
                    onClick={handleSubmit}
                    disabled={disabled}
                >
                    <div className="w-5" dangerouslySetInnerHTML={{__html: flightIcon}}></div>
                </button>
            </div>
        );
    }
);

export default memo(InputBox);
