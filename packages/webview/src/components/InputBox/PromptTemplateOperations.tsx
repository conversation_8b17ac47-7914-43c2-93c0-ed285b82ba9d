import {useCallback, useState} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import editIcon from '@/assets/edit.svg';
import deleteIcon from '@/assets/delete.svg';
import Tooltip from '../Tooltip';
import {AgentListWithItemType} from './hooks/useChatResources';

interface Props {
    promptTemplate: AgentListWithItemType;
    setCurrentPromptTemplate: (promptTemplate: AgentListWithItemType) => void;
}

export default function PromptTemplateOperations({promptTemplate, setCurrentPromptTemplate}: Props) {

    const handleEdit = useCallback(
        event => {
            event.stopPropagation();
            messageHandler.send(EventMessage.PromptTemplateEditEvent, promptTemplate.name);
        },
        [promptTemplate.name]
    );

    const handleDelete = useCallback(
        event => {
            event.stopPropagation();
            setCurrentPromptTemplate(promptTemplate);
        },
        [promptTemplate, setCurrentPromptTemplate]
    );

    return (
        <>
            <Tooltip
                overlay="编辑指令"
                mouseEnterDelay={1}
            >
                <span
                    // eslint-disable-next-line max-len
                    className="inline-flex items-center justify-center cursor-pointer w-4 h-4 rounded-[2px] hover:bg-[#D4D4D4]/15"
                    onClick={handleEdit}
                >
                    <i
                        style={{width: 14, height: 14, lineHeight: '14px'}}
                        // bca-disable-line
                        dangerouslySetInnerHTML={{__html: editIcon}}
                    />
                </span>
            </Tooltip>
            <Tooltip
                overlay="删除指令"
                mouseEnterDelay={1}
            >
                <span
                    // eslint-disable-next-line max-len
                    className="inline-flex items-center justify-center cursor-pointer w-4 h-4 rounded-[2px] hover:bg-[#D4D4D4]/15"
                    onClick={handleDelete}
                >
                    <i
                        style={{width: 14, height: 14, lineHeight: '14px'}}
                        // bca-disable-line
                        dangerouslySetInnerHTML={{__html: deleteIcon}}
                    />
                </span>
            </Tooltip>
        </>
    );
}

export const usePromptTemplateOperations = () => {
    const [currentPromptTemplate, setCurrentPromptTemplate] = useState<AgentListWithItemType>();

    const onDeletePromptTemplate = useCallback(
        () => {
            messageHandler.send(EventMessage.PromptTemplateDeleteEvent, currentPromptTemplate?.name);
            setCurrentPromptTemplate(undefined);
        },
        [currentPromptTemplate]
    );

    const resetCurrentPromptTemplate = useCallback(
        () => {
            setCurrentPromptTemplate(undefined);
        },
        []
    );
    return {
        currentPromptTemplate,
        setCurrentPromptTemplate,
        resetCurrentPromptTemplate,
        onDeletePromptTemplate,
    };
};
