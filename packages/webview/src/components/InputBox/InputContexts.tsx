/* bca-disable */
import {useCallback, useMemo, useRef, useImperativeHandle} from 'preact/hooks';
import {forwardRef} from 'preact/compat';
import {EventMessage} from '@shared/protocols';
import {uniqBy} from 'lodash';
import {useEditorWatchListener, useEditorWatcher} from '@/hooks/useEditorWatcher';
import {messageHandler} from '@/utils/messageHandler';
import addIcon from '@/assets/add.svg';
import {useToggle} from '@/hooks';
import FileContextPopover from '../FileContextPopover';
import Tooltip from '../Tooltip';
import FileToolTip from '../FileContextPopover/FileTooltip';
import {ContextGroup, ContextGroupProps, ContextTag, ITag} from './ContextTag';
import {useChatInput} from './hooks/useChatInput';
import {extractFilename, extractMentionsFromMarkupedText, mention2Text} from './utils/mention';
import {linkKnowledgeId2Url} from './utils/is';

function CurrentFileContextTag() {
    const {activeEditor, onMuteActiveEditor} = useEditorWatcher();
    const [start, end] = activeEditor?.selection || [0, 0];
    const description = activeEditor?.selection ? `[${start}, ${end}]` : null;
    const handleFileClick = useCallback(
        (tag: {id: string}) => {
            messageHandler.send(
                EventMessage.LinkClickEvent,
                activeEditor!.selection ? `${tag.id}#L${start}-${end}` : tag.id
            );
        },
        [activeEditor, end, start]
    );

    if (!activeEditor?.visible) {
        return null;
    }

    const currentTag = {
        name: activeEditor.name,
        id: activeEditor.relativePath,
        type: 'file' as const,
        description,
    };

    return (
        <FileToolTip filePath={activeEditor.relativePath} placement="top">
            <ContextTag
                tag={currentTag}
                onClick={handleFileClick}
                onClose={onMuteActiveEditor}
                className="max-w-[calc(100vw-32px-24px-20px-4px-2px)]"
            />
        </FileToolTip>
    );
}

export interface InputContextsRef {
    getClientHeight: () => number;
}

export default forwardRef<InputContextsRef>(
    function InputContexts(_, ref) {
        const {activeEditor} = useEditorWatcher();
        const {text, onInputChange} = useChatInput();
        const [popoverVisible, togglePopover, {close: closePopover}] = useToggle(false);
        const {selectedFileRelativePaths, onFileContextSelected} = useEditorWatcher();
        const buttonRef = useRef(null);
        const contexts = useMemo(
            () => {
                const mentions = extractMentionsFromMarkupedText(text);
                const activeEditorPath = activeEditor?.visible ? activeEditor.relativePath : undefined;
                const mentionFiles = mentions
                    .filter(mention => mention.id !== activeEditorPath)
                    .map(mention => ({
                        name: mention.display,
                        id: mention.id.replace(/^file:/, ''),
                        type: mention.id.split(':')[0] as 'file',
                    }));
                const selectedFiles = selectedFileRelativePaths
                    .filter(path => path !== activeEditorPath)
                    .map(relativePath => ({
                        name: extractFilename(relativePath),
                        id: relativePath,
                        type: 'file' as const,
                    }));
                return uniqBy([...mentionFiles, ...selectedFiles], 'id').filter(file => file.id !== activeEditorPath!);
            },
            [activeEditor, selectedFileRelativePaths, text]
        );

        const handleClick = useCallback(
            (tag: ITag) => {
                if (tag.type === 'file') {
                    const filePath = tag.id.replace(/^(.*):/, '');
                    messageHandler.send(EventMessage.LinkClickEvent, filePath);
                }
                else if (tag.type === 'link') {
                    const url = linkKnowledgeId2Url(tag.id);
                    messageHandler.send(EventMessage.LinkClickEvent, url);
                }
            },
            []
        );

        const handleChange = useCallback<NonNullable<ContextGroupProps['onChange']>>(
            (tags, removed) => {
                if (selectedFileRelativePaths.includes(removed.id)) {
                    onFileContextSelected(selected => selected.filter(path => path !== removed.id));
                }
                else {
                    onInputChange(text => {
                        const contextPrefixReg = /^(file|repo|webSearch|link|knowledge|folder|iapi|terminal)(:|$)/;
                        const hasContextPrefix = contextPrefixReg.test(removed.id);
                        const id = hasContextPrefix ? removed.id : `file:${removed.id}`;
                        return text.replace(mention2Text({display: removed.name, id}), `#${removed.name}`);
                    });
                }
            },
            [onInputChange, selectedFileRelativePaths, onFileContextSelected]
        );

        const containerRef = useRef<HTMLDivElement>(null);
        const innerContainerRef = useRef<HTMLDivElement>(null);

        useImperativeHandle(ref, () => ({
            getClientHeight: () => containerRef.current?.clientHeight || 0,
        }));

        useEditorWatchListener(text);

        const close = () => {
            closePopover();
            if (buttonRef.current) {
                buttonRef.current.focus();
            }
        };
        return (
            <>
                <div
                    ref={containerRef}
                    className="relative mx-3 mb-2 min-h-[24px] flex-shrink-0"
                >
                    <div ref={innerContainerRef} className="flex gap-1 flex-wrap">
                        <Tooltip
                            placement="topLeft"
                            overlay={
                                <div className="w-[180px]">输入 # 或点击当前按钮，可添加相关知识，结果生成更准确</div>
                            }
                        >
                            <div
                                onClick={togglePopover}
                                aria-label="添加关联文件"
                                role="button"
                                className="context-tag context-tag-active cursor-pointer"
                                tabIndex={0}
                                ref={buttonRef}
                                style={{outline: 'none'}}
                            >
                                <span
                                    dangerouslySetInnerHTML={{__html: addIcon}}
                                    className="w-3 h-3"
                                    aria-hidden="true"
                                />
                            </div>
                        </Tooltip>
                        {!activeEditor?.visible && !contexts.length && (
                            <div
                                className="ml-1 text-[var(--comate-descriptionForeground)]"
                                aria-hidden="true"
                            >
                                添加知识
                            </div>
                        )}
                        <CurrentFileContextTag />
                        <ContextGroup
                            tags={contexts}
                            onClick={handleClick}
                            onChange={handleChange}
                        />
                    </div>
                </div>
                <FileContextPopover
                    classname="absolute left-0 bottom-[calc(100%+8px)]"
                    visible={popoverVisible}
                    onClose={close}
                />
            </>
        );
    }
);
