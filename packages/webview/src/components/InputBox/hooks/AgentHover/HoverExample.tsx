import {ReactElement} from 'preact/compat';
import {DehydratedMessage, KnowledgeList} from '@shared/protocols';
import {
    parseMarkupedText2PlainText,
    splitMarkupedMentionText2Chunk,
} from '../../utils/mention';
import {HoverBus} from './HoverBus';
import {match, getExampleKnowledgeList} from './utils';
import './HoverExample.css';

function parseReferenceContent(query: string) {
    const plainText = parseMarkupedText2PlainText(query);
    return splitMarkupedMentionText2Chunk(plainText).map(chunk => {
        if (typeof chunk === 'string') {
            return {type: 'text', content: chunk};
        }
        return {type: 'highlight', content: chunk.display};
    });
}

type Message = Partial<DehydratedMessage> & {content: string};
export class HoverExample {
    constructor(
        private readonly name: string,
        private readonly commandId: string | undefined,
        private readonly query: string,
        private readonly mock: ((bus: HoverBus, agent: {id: string}) => DehydratedMessage[]) | undefined,
        private readonly reference?: KnowledgeList[]
    ) {}
    static ofQuery(params: {commandName: string, commandId: string, query: string}) {
        return new HoverExample(params.commandName, params.commandId, params.query, undefined);
    }
    static ofReferQuery(params: {name: string, id: string, query: string, references: KnowledgeList[]}) {
        return new HoverExample(params.name, params.id, params.query, undefined, params.references);
    }
    static ofMock(params: {
        commandName: string;
        commandId: string;
        query: string;
        mock: {query: Message, response: Message};
    }) {
        return new HoverExample(params.commandName, params.commandId, params.query, (_, agent): DehydratedMessage[] => {
            return [
                {
                    role: 'user',
                    type: 'help',
                    status: 'success',
                    stream: false,
                    // 这里设置里一个负的值，不影响正常的逻辑
                    id: -1000,
                    timestamp: Date.now() - 1,
                    actions: [],
                    agent: agent.id,
                    slash: params.commandId,
                    ...params.mock.query,
                },
                {
                    role: 'assistant',
                    // agent: agent.id,
                    status: 'success',
                    type: params.commandId as any,
                    stream: false,
                    replyTo: -1000,
                    id: -999,
                    timestamp: Date.now(),
                    actions: [],
                    ...params.mock.response,
                },
            ];
        });
    }
    render(bus: HoverBus, agent: {id: string}): ReactElement {
        const {theme} = bus;
        const textColor = match(theme, {
            dark: () => '#B4B5B8',
            light: () => '#616264',
        });
        const highlightColor = match(theme, {
            dark: () => '#00d5ff',
            light: () => '#5cbdce',
        });

        const exampleContent = this.reference
            ? parseReferenceContent(this.query)
            : [
                {type: 'highlight', content: '/ ' + this.name},
                {type: 'text', content: this.query},
            ];

        const knowledgeList = this.reference && this.commandId
            ? getExampleKnowledgeList(this.commandId, this.query, this.reference)
            : undefined;

        const title = this.reference
            ? parseMarkupedText2PlainText(this.query)
            : `${this.name} ${this.query}`;

        return (
            <div
                title={title}
                className={'comate-plugin-hover-example'}
                onClick={() => {
                    const time = Date.now();
                    bus.logAction({
                        type: 'clickExample',
                        time,
                        agentId: agent.id,
                        exampleId: `${agent.id}|${this.commandId}|${this.query.slice(0, 10)}`,
                    });
                    if (this.mock) {
                        return bus.onClickMockExample({
                            commandId: this.reference ? undefined : this.commandId,
                            agentId: agent.id,
                            query: this.query,
                            mockMessage: this.mock(bus, agent),
                        });
                    }
                    else {
                        return bus.onClickExample({
                            commandId: this.reference ? undefined : this.commandId,
                            agentId: agent.id,
                            query: this.query,
                            knowledgeList,
                        });
                    }
                }}
            >
                {exampleContent.map(v =>
                    match(v.type, {
                        highlight: () => (
                            <span
                                style={{
                                    color: highlightColor,
                                    margin: '0 0.25rem',
                                }}
                            >
                                {v.content}
                            </span>
                        ),
                        text: () => (
                            <span
                                style={{color: textColor}}
                                className="overflow-hidden text-ellipsis whitespace-nowrap"
                            >
                                {v.content}
                            </span>
                        ),
                    })
                )}
            </div>
        );
    }
}
