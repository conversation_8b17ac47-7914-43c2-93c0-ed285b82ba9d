import {AgentHoverContent} from './AgentHoverContent';
import {HoverExample} from './HoverExample';

// NOTE: 文档与负责人：
// https://ku.baidu-int.com/embed/embd9fece3218911798/dstZggbBhAVqBqwcnl/viwaZFZi6XMVe
/** 插件悬浮提示对应关系表 */
const AgentHoverContentMap: Record<string, AgentHoverContent | undefined> = {
    'dev-tools': AgentHoverContent.of({
        title: '工具箱',
        id: 'dev-tools',
        description: '开箱即用的开发工具，包括 JSON 格式化、JSON 转 TS、JSON 与 YAML 互转、Base64 编码&解码 等。',
        examples: [
            HoverExample.ofQuery({
                commandName: '插件介绍',
                commandId: 'dev-tools-help',
                query: '',
            }),
            HoverExample.ofQuery({
                commandId: 'json-format',
                commandName: 'JSON 格式化',
                query: '{"name": "Comate", "age": "3"}',
            }),
            HoverExample.ofQuery({
                commandId: 'base64-decode',
                commandName: 'Base64解码',
                // eslint-disable-next-line max-len
                query:
                    '5Z+65LqO5paH5b+D5aSn5qih5Z6L77yM57uT5ZCI55m+5bqm57yW56iL5aSn5pWw5o2u77yM5Li65L2g55Sf5oiQ5LyY6LSo57yW56iL5Luj56CB44CC5paH5b+D5b+r56CBIC0gQmFpZHUgQ29tYXRl77yM5pu05oeC5L2g55qEQUnnvJbnqIvkvJnkvLTvvIznoJTlj5HmlYjnjofmj5DljYflpb3luK7miYvjgIIKCgoK',
            }),
        ],
    }),
    git: AgentHoverContent.of({
        title: 'Git',
        id: 'git',
        description: '支持创建 GitHub、Gitee、GitLab Issue、生成 Git 提交信息等。',
        examples: [
            HoverExample.ofQuery({
                commandName: '生成提交',
                commandId: 'commitMessage',
                query: '',
            }),
            HoverExample.ofQuery({
                commandName: '创建Issue',
                commandId: 'createIssue',
                query: '',
            }),
        ],
    }),
    Comate: AgentHoverContent.of({
        title: 'Comate',
        id: 'Comate',
        description: '你的智能编码助手 Baidu Comate'
            + '可以随时为你生成代码、解释代码、优化代码、解决构建与执行问题，也可以解答本地代码库相关问题或其他编码问题。',
        examples: [
            HoverExample.ofMock({
                commandName: '代码解释',
                commandId: 'explain',
                query: '请解释如下代码 #include <iostream>',
                mock: {
                    query: {content: '请解释如下代码\n', code: '```cpp\n#include <iostream>\n```'},
                    // eslint-disable-next-line max-len
                    response: {
                        content:
                            '当然，这行代码是C++程序中的一个非常基础的组成部分，用于包含（或称为“引入”）标准输入输出流库（`iostream`）。这个库提供了进行输入输出操作的功能，比如从控制台读取数据（输入）和向控制台输出数据（输出）。\n\n具体来说，`#include <iostream>` 这行代码做了以下几件事情：\n\n1. **预处理指令**：`#include` 是一个预处理指令，它在编译程序之前由预处理器执行。预处理器负责处理以 `#` 开头的指令，这些指令告诉编译器在编译之前应该做什么。\n\n2. **包含头文件**：`<iostream>` 是一个头文件，它包含了C++标准库中用于输入输出的类和函数的声明。这些声明使得程序能够使用如 `std::cin`（用于输入）和 `std::cout`（用于输出）等对象。\n\n3. **命名空间**：在 `<iostream>` 头文件中定义的类和函数默认位于 `std` 命名空间中。因此，当你想要使用这些类（如 `std::istream`、`std::ostream`）或函数（如 `std::cin`、`std::cout`）时，你需要通过 `std::` 前缀来指定它们所属的命名空间，除非你使用了 `using namespace std;` 语句来避免每次都写 `std::` 前缀。\n\n4. **编译过程**：虽然 `#include <iostream>` 指令本身不直接参与编译过程，但它通过引入必要的声明，使得编译器能够理解和处理程序中使用的输入输出相关的代码。\n\n简而言之，`#include <iostream>` 是C++程序中用于包含标准输入输出流库的一个预处理指令，它使得程序能够使用如 `std::cout` 和 `std::cin` 等输入输出功能。',
                    },
                },
            }),
            HoverExample.ofQuery({
                commandName: '函数注释',
                commandId: 'functionComment',
                query: '',
            }),
            HoverExample.ofMock({
                commandName: '调优建议',
                commandId: 'optimize',
                query: '请优化下面的代码：int a=\'c\';',
                mock: {
                    query: {content: '请优化下面的函数：', code: '```cpp\nint a = \'c\';\n```'},
                    // eslint-disable-next-line max-len
                    response: {
                        content:
                            '# 问题与优化点\n\n1. **问题**：变量 `a` 被声明为 `int` 类型，但初始化时却使用了字符 `\'c\'`。虽然这在C++中是合法的（因为字符字面量会被隐式转换为整数，即字符的ASCII码），但这样的代码可读性较差，且可能引起混淆。\n\n2. **优化点**：\n   - 如果 `a` 应该存储字符的ASCII码，则保持原样但添加注释说明。\n   - 如果 `a` 实际上是用于存储字符，则应将类型改为 `char`。\n\n# 修复后的代码片段\n\n```cpp\n    // 如果a用于存储字符的ASCII码\n    int a = \'c\'; // \'c\' 的ASCII码被赋值给a\n\n    // 或者，如果a应该存储字符\n    char aChar = \'c\'; // 使用char类型存储字符\'c\'\n```\n\n# 优化总结\n\n- 提高了代码的可读性和明确性，避免了类型使用上的混淆。\n- 保留了原代码的意图（若原意是存储ASCII码），同时提供了另一种更清晰的存储字符的方式。',
                    },
                },
            }),
        ],
    }),
    iapi: AgentHoverContent.of({
        title: 'iAPI',
        id: 'iapi',
        description: 'API 研发智能化助手 iAPI，可以为生成接口实现代码、接口代码注释等。',
        examples: [
            HoverExample.ofQuery({
                commandName: '接口实现代码',
                commandId: 'genServerCode',
                query: 'https://iapi.baidu-int.com/apidoc/project-345188/api-3471004',
            }),
        ],
    }),
    jarvis: AgentHoverContent.of({
        title: 'Jarvis',
        id: 'jarvis',
        description:
            'Jarvis官方插件，支持生成Higgs函数计算代码和海若API代码、部署Jarvis EKS线下应用、一键搭建EOS环境、一键完成marspro配置热更新、Jarvis FAQ智能问答。',
        examples: [
            HoverExample.ofQuery({
                commandName: '插件介绍',
                commandId: 'jarvis-help',
                query: '',
            }),
            HoverExample.ofQuery({
                commandName: '应用部署',
                commandId: 'jarvisAppDeploy',
                query: '',
            }),
        ],
    }),
    bfc: AgentHoverContent.of({
        id: 'bfc',
        title: 'BFC',
        description: '自动化用例生成助手，可以生成基于BFC框架的自动化用例以及异常用例',
        examples: [
            HoverExample.ofQuery({
                commandName: '插件介绍',
                commandId: 'bfc-help',
                query: '',
            }),
        ],
    }),
    devaux: AgentHoverContent.of({
        title: 'DevAux',
        id: 'devaux',
        description: '研发效能助手DevAux。支持静态代码扫描，算子代码生成，算子自测等功能。',
        examples: [],
    }),
    paddle: AgentHoverContent.of({
        title: '飞桨',
        id: 'paddle',
        description: '飞桨深度学习编程助手，支持PaddlePaddle框架的智能问答、代码生成、代码转换',
        examples: [
            HoverExample.ofQuery({
                commandName: '代码生成',
                commandId: 'chat',
                query: '实现ResNet50的训练评估',
            }),
            HoverExample.ofQuery({
                commandName: '代码转换',
                commandId: 'code-convert',
                query: '',
            }),
            HoverExample.ofQuery({
                commandName: '插件介绍',
                commandId: 'paddle-help',
                query: '',
            }),
        ],
    }),
    testmate: AgentHoverContent.of({
        title: 'TestMate',
        id: 'testmate',
        description: '自动化用例编写助手，提供自动化用例生成，自动化用例改写和扩展，以及根据自然语言生成用例功能。',
        examples: [
            HoverExample.ofQuery({
                commandName: '生成测试代码',
                commandId: 'genTestCode',
                query: '生成随机数',
            }),
            HoverExample.ofQuery({
                commandName: '生成测试代码',
                commandId: 'genTestCode',
                query: 'https://iapi.baidu-int.com/web/project/353470/apis/api-3919705',
            }),
            HoverExample.ofQuery({
                commandName: '生成测试代码',
                commandId: 'genTestCode',
                query: '校验返回值为200，https://iapi.baidu-int.com/web/project/353470/apis/api-3919705',
            }),
        ],
    }),
    smartapp: AgentHoverContent.of({
        title: '百度智能小程序',
        id: 'smartapp',
        description: '小程序官方插件，支持代码生成，智能问答。',
        examples: [
            HoverExample.ofQuery({
                commandName: '插件介绍',
                commandId: 'smartapp-help',
                query: '',
            }),
            HoverExample.ofQuery({
                commandName: '智能问答',
                commandId: 'chat',
                query: '百度小程序如何实现一个搜索框组件？',
            }),
        ],
    }),
    appdev: AgentHoverContent.of({
        title: 'AppDev',
        id: 'appdev',
        description: '可获得移动端通用组件、工具、平台、端组件负责人信息。支持智能问答和代码优化建议。',
        examples: [
            HoverExample.ofQuery({
                commandId: 'qa',
                commandName: '智能问答',
                query: 'Android云控组件怎么用？',
            }),
            HoverExample.ofQuery({
                commandId: 'qa',
                commandName: '智能问答',
                query: '如何添加新的端能力？',
            }),
            HoverExample.ofQuery({
                commandId: 'qa',
                commandName: '智能问答',
                query: 'Android如何使用EasyBox切换组件切换源码和进制？',
            }),
        ],
    }),
    gdp: AgentHoverContent.of({
        id: 'gdp',
        title: 'GDP',
        description: 'Go 语言开发助手，支持 Go 和 GDP 框架相关的智能问答与代码生成能力。',
        examples: [
            HoverExample.ofQuery({
                commandName: '智能问答',
                commandId: 'chat',
                query: '日志中出现 context canceled 的错误',
            }),
            HoverExample.ofQuery({
                commandName: '代码生成',
                commandId: 'codeGenerator',
                // eslint-disable-next-line max-len
                query:
                    '有一个第三方服务，它提供了http get 和post 接口， get 接口描述为 path：/abc/h/get， 服务是通过bns 对外提供服务，参数有 a=str，b=trt 两个。post 接口描述：path：abc/h/post ，参数有c=str这个参数，也get 一样的通过同一个bns 进行或者， 帮我写一个客户端代码，访问get 和post 接口，返回的结果get 结果json为{"A":string,"B":int,"C":string} ，post 结果json 为 {"A":string,”D”:int,”E”:int}',
            }),
        ],
    }),
    tor: AgentHoverContent.of({
        title: 'TOR助理',
        id: 'tor',
        description:
            '不仅具备一系列通用能力，如"智能找知识库文档"、"代码变量命名"等，还能结合业务需求实现定制化的辅助。',
        examples: [
            HoverExample.ofQuery({
                commandName: '找知识库文档',
                commandId: 'docSearch',
                query: '找我编辑的文档',
            }),
            HoverExample.ofQuery({
                commandName: '插件介绍',
                commandId: 'tor-help',
                query: '',
            }),
            HoverExample.ofQuery({
                commandName: '代码变量起名',
                commandId: 'codeNameCreate',
                query: '给插件变量注册起个名字',
            }),
        ],
    }),
    icafe: AgentHoverContent.of({
        id: 'icafe',
        title: 'iCafe',
        description: '通过自然语言智能查询iCafe卡片，支持一键复制卡片编号&标题，一键提交代码绑卡',
        examples: [
            HoverExample.ofQuery({
                commandName: '插件介绍',
                commandId: 'icafe-help',
                query: '',
            }),
            HoverExample.ofQuery({
                commandName: '查询卡片',
                commandId: 'searchBySelf',
                query: '查询我最近14天负责的卡片',
            }),
            HoverExample.ofQuery({
                commandName: '查询卡片 按标题查询',
                commandId: 'findByTitle',
                query: '优化',
            }),
        ],
    }),
    weiyun: AgentHoverContent.of({
        id: 'weiyun',
        title: '微云助手',
        description: '通过插件附加微云能力，提供微云一键发布能力',
        examples: [
            HoverExample.ofQuery({
                commandName: '插件介绍',
                commandId: 'weiyun-help',
                query: '',
            }),
            HoverExample.ofQuery({
                commandName: '应用部署',
                commandId: 'weiyunDeploy',
                query: 'dev',
            }),
        ],
    }),
};

export function getHoverContent(agent: string) {
    return AgentHoverContentMap[agent];
}
