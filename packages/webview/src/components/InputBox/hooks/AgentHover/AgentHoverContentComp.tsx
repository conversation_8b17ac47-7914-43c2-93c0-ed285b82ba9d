import {FC} from 'preact/compat';
import {HoverBus} from './HoverBus';
import {match} from './utils';
import {HoverExample} from './HoverExample';

interface Props {
    bus: HoverBus;
    title: string;
    description: string;
    id: string;
    example: HoverExample[];
}

const AgentHoverContentComp: FC<Props> = ({bus, title, id, description, example}) => {
    const {theme} = bus;

    const backgroundColor = match(theme, {
        light: () => 'white',
        dark: () => '#3b3b3d',
    });
    const fontColor = match(theme, {
        light: () => '#3b3b3d',
        dark: () => '#B4B5B8',
    });
    const exampleTitleColor = match(theme, {
        light: () => '#0e0e0e',
        dark: () => '#FCFDFF',
    });

    const pluginInfo = (
        <div>
            <h1
                style={{
                    margin: 0,
                    padding: 0,
                    color: fontColor,
                    fontWeight: 'regular',
                    fontSize: '13px',
                    lineHeight: '20px',
                    letterSpacing: '0px',
                    textAlign: 'left',
                }}
            >
                {title}
            </h1>
            <div
                style={{
                    color: fontColor,
                    fontWeight: 'regular',
                    fontSize: '13px',
                    lineHeight: '20px',
                    letterSpacing: '0px',
                    textAlign: 'left',
                }}
            >
                {description}
            </div>
        </div>
    );
    const pluginExample = (
        <div
            style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '12px',
            }}
        >
            <div
                style={{
                    color: exampleTitleColor,
                    fontWeight: 'regular',
                    fontSize: '13px',
                    lineHeight: '20px',
                    letterSpacing: '0px',
                    textAlign: 'left',
                }}
            >
                你可以试着问我
            </div>
            {/* 示例问题列表 */}
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'start',
                    gap: '8px',
                    rowGap: '8px',
                    flexWrap: 'wrap',
                    width: '100%',
                    overflowX: 'hidden',
                    // padding: '0 8px',
                }}
            >
                {example.map(e => e.render(bus, {id: id}))}
            </div>
        </div>
    );

    return (
        <div
            style={{
                width: '100%',
                position: 'relative',
                overflow: 'hidden',
                borderRadius: '4px',
                backgroundColor,
            }}
        >
            {/* 背景光晕1 */}
            <div
                style={{
                    position: 'absolute',
                    width: '160px',
                    height: '180px',
                    left: '-67px',
                    top: '-29px',
                    background: '#03f1c971',
                    pointerEvents: 'none',
                    filter: 'blur(200px)',
                }}
            />
            {/* 背景光晕2 */}
            <div
                style={{
                    position: 'absolute',
                    width: '110px',
                    height: '120px',
                    left: '13px',
                    top: '-35px',
                    background: '#4080ff8f',
                    pointerEvents: 'none',
                    filter: 'blur(120px)',
                }}
            />
            {/* 功能 */}
            <div
                style={{
                    boxSizing: 'border-box',
                    width: '100%',
                    borderRadius: '4px',
                    border: '1px solid #a6abb534',
                    boxShadow: '0px 5px 12px 0px #00000019, 0px 3px 6px 0px #00000019, 0px 1px 2px 0px #00000019',
                    padding: '16px',
                    margin: 0,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                }}
            >
                {pluginInfo}
                {example.length ? pluginExample : null}
            </div>
        </div>
    );
};

export default AgentHoverContentComp;
