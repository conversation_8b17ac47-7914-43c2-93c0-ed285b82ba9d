import {ContextType, KnowledgeList} from '@shared/protocols';
import {extractMentionsFromMarkupedText} from '../../utils/mention';

export function match<T extends string | number | symbol, R>(k: T, mapping: Record<T, () => R>) {
    return mapping[k]?.();
}

function extractReferenceFromQuery(query: string, type: ContextType) {
    const mentions = extractMentionsFromMarkupedText(query);
    return mentions.map(v => ({
        id: v.id,
        name: v.display,
        type,
    }));
}

export function getExampleKnowledgeList(
    id: string,
    query: string,
    references: KnowledgeList[] = []
): KnowledgeList[] | undefined {
    switch (id) {
        case 'currentFile':
            return [{
                id: 'currentFile',
                name: 'currentFile',
                type: ContextType.FILE,
            }];
        case 'webSearch':
            return [{
                id: 'web_search',
                name: '网络检索',
                type: ContextType.WEB,
            }];
        case 'file':
            return extractReferenceFromQuery(query, ContextType.FILE);
        case 'folder':
            return extractReferenceFromQuery(query, ContextType.FOLDER);
        case 'repo':
            return extractReferenceFromQuery(query, ContextType.REPO);
        default:
            return references;
    }
}
