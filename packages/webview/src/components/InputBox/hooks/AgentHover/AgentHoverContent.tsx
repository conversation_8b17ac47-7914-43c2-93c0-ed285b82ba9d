import {ReactElement} from 'preact/compat';
import {HoverBus} from './HoverBus';
import {HoverExample} from './HoverExample';
import AgentHoverContentComp from './AgentHoverContentComp';

export class AgentHoverContent {
    constructor(
        private readonly title: string,
        private readonly id: string,
        private readonly description: string,
        private readonly example: HoverExample[]
    ) {}
    static of(params: {title: string, id: string, description: string, examples: HoverExample[]}) {
        return new AgentHoverContent(params.title, params.id, params.description, params.examples);
    }
    render(bus: HoverBus): ReactElement {
        return (
            <AgentHoverContentComp
                bus={bus}
                id={this.id}
                title={this.title}
                description={this.description}
                example={this.example}
            />
        );
    }
}
