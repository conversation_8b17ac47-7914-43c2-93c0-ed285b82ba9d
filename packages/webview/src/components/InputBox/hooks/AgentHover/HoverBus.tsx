import type {ColorTheme, DehydratedMessage, KnowledgeList} from '@shared/protocols';

export interface ClickExample {
    commandId: string | undefined;
    agentId: string;
    query: string;
    knowledgeList?: KnowledgeList[];
}
export interface ClickMockExample {
    commandId: string | undefined;
    agentId: string;
    query: string;
    /** 演示用对话内容 */
    mockMessage: DehydratedMessage[];
}
export type LogPluginHoverContent =
    & {
        // 事件发生时间
        time: number;
        // 距离弹窗被打开过去的毫秒数
        timeOffset?: number;
        agentId?: string;
        commandId?: string;
        exampleId?: string;
    }
    & (
        | {
            type: 'hover';
        }
        | {
            type: 'hoverLeave';
        }
        | {
            type: 'clickExample';
        }
    );
export interface HoverBus {
    /** 触发真实示例，会实际请求相关插件 */
    onClickExample(example: ClickExample): void;
    /** 触发模拟示例，只是播放已有对话，无实际请求 */
    onClickMockExample(mockExample: ClickMockExample): void;
    /** 打点 */
    logAction(content: LogPluginHoverContent): void;
    theme: ColorTheme;
}
