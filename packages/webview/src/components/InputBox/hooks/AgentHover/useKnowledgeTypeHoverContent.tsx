import {useCallback, useEffect, useState} from 'preact/hooks';
import {BuiltinAgent, EventMessage, KnowledgeTypeExampleItem, KnowledgeTypeExampleRefer} from '@shared/protocols';
import {Item} from '@/components/ExtensionPopover/types';
import {messageHandler} from '@/utils/messageHandler';
import {ExtensionPopoverType} from '../../utils/popover';
import {KnowledgeType} from '../useChatInput';
import {disabledWebAndIAPIWhenInactive} from '../useChatResources';
import {HoverBus} from './HoverBus';
import AgentHoverContentComp from './AgentHoverContentComp';
import {HoverExample} from './HoverExample';

const nameMapping = {
    currentFile: '当前文件',
    file: '文件',
    repo: '当前代码库',
    folder: '目录',
    knowledge: '知识集',
    iapi: 'API',
    web: '网页',
    webSearch: '网络检索',
};

function filterDisplayExample(list: KnowledgeTypeExampleRefer[]) {
    if (list.length <= 3) {
        return list;
    }
    const indexes = new Set<number>();
    while (indexes.size < 3) {
        indexes.add(Math.floor(Math.random() * list.length));
    }
    return Array.from(indexes).map(i => list[i]);
}

/**
 * 这个 hook 提供了 #知识 相关指令的 hover 弹框内容
 * 当 activePopoverType 是 pound 时通过 RepoGuideFetchEvent 事件获取具体例子
 * @returns
 */
export const useKnowledgeTypeHoverContent = (
    hoverBus: HoverBus,
    activePopoverType: ExtensionPopoverType | undefined
) => {
    const [configMapping, setConfigMapping] = useState<Record<string, KnowledgeTypeExampleItem>>({});

    const hoverConfigForKnowledgeType = useCallback(
        (knowledgeType: KnowledgeType, value: string): Item => {
            const knowledgeTypeWithDisabled = disabledWebAndIAPIWhenInactive(knowledgeType, value);
            const knowledgeTypeId = knowledgeTypeWithDisabled.id;
            const exampleConfig = configMapping[knowledgeTypeId];
            if (!exampleConfig) {
                return knowledgeTypeWithDisabled;
            }
            const name = nameMapping[knowledgeTypeId] ?? '';
            const hoverStartTimeRef: {current?: number} = {};
            const displayExample = filterDisplayExample(exampleConfig.suggested_quires ?? []);
            const examples = displayExample.map(v => {
                return HoverExample.ofReferQuery({
                    name,
                    id: knowledgeTypeId,
                    query: v.displayQuery,
                    references: v.references,
                });
            });
            const config: Pick<Item, 'hoverContent' | 'onHover' | 'onHoverLeave'> = {
                hoverContent: (
                    <AgentHoverContentComp
                        bus={hoverBus}
                        id={BuiltinAgent.Comate}
                        title={name}
                        description={exampleConfig.description}
                        example={knowledgeType.disabled ? [] : examples}
                    />
                ),
                onHover: () => {
                    hoverStartTimeRef.current = Date.now();
                    hoverBus.logAction({
                        type: 'hover',
                        time: hoverStartTimeRef.current,
                        timeOffset: 0,
                        agentId: BuiltinAgent.Comate,
                        commandId: knowledgeTypeId,
                    });
                },
                onHoverLeave: () => {
                    const time = Date.now();
                    const startTime = hoverStartTimeRef.current;
                    hoverBus.logAction({
                        type: 'hoverLeave',
                        time,
                        timeOffset: startTime ? time - startTime : -1,
                        agentId: BuiltinAgent.Comate,
                        commandId: knowledgeTypeId,
                    });
                },
            };
            return {...knowledgeTypeWithDisabled, ...config};
        },
        [configMapping, hoverBus]
    );

    useEffect(
        () => {
            // #知识 的时候拿一下最新的例子，代码库相关的例子是跟 repoId 绑定的，随时可能会变
            if (activePopoverType === 'pound') {
                messageHandler.send(EventMessage.RepoGuideFetchEvent).then(res => {
                    const mapping = {};
                    const knowledgeTypeConfig = (res ?? []) as KnowledgeTypeExampleItem[];
                    for (const item of knowledgeTypeConfig) {
                        mapping[item.type] = item;
                    }
                    setConfigMapping(mapping);
                });
            }
        },
        [activePopoverType]
    );
    return [hoverConfigForKnowledgeType];
};
