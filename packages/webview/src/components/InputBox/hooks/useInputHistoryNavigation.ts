import {Ref, useCallback, useEffect, useRef, useState} from 'preact/hooks';
import {EventMessage, InputBoxMessageHistory} from '@shared/protocols';
import {SlashType} from '@shared/constants';
import {PROMPT_TEMPLATE_AGENT} from '@shared/agents';
import {safePromise} from '@/utils/common';
import {messageHandler} from '@/utils/messageHandler';
import {ExtensionPopoverConfig} from '@/components/ExtensionPopover/types';
import {buildCommandList} from '../utils';
import {useChatInput} from './useChatInput';

function useHistoryNavigation(
    refreshKey: number,
    valueRef: Ref<HTMLTextAreaElement>,
    setIsFromHistory: (isFromHistory: boolean) => void,
    popoverConfig?: ExtensionPopoverConfig | null
) {
    // 目前展示的历史记录，默认为 -1 则不展示，第 0 条为最近发送的一条，上限存储 50 条
    const currentHistoryIndex = useRef(-1);
    const {onInputChange, onAgentSelect, onCommandSelect, activePopoverType, onPopoverTypeChange} = useChatInput();
    const [history, setHistory] = useState<InputBoxMessageHistory[]>([]);

    useEffect(
        () => {
            const init = async () => {
                const inputHistory = await safePromise(
                    messageHandler.send(EventMessage.InputHistoryFetchEvent)
                );
                inputHistory && setHistory(inputHistory);
            };

            init();
        },
        [refreshKey]
    );

    const handleHistoryNavigation = useCallback(
        async (history: InputBoxMessageHistory[], index: number, event: KeyboardEvent) => {
            const currentContent = history[index];
            if (currentContent.slash && currentContent.agent === PROMPT_TEMPLATE_AGENT.id) {
                onCommandSelect(currentContent.slash + '@' + currentContent.agent);
            }
            else if (currentContent.slash && currentContent.slash !== SlashType.IAPI) {
                onCommandSelect(currentContent.slash);
                const commandId = buildCommandList().find(item => item.name === currentContent.slash);
                // NOTE: Command 这边需要注意，onCommandSelect 应该传入的是 id，和选中时展示的内容有差异，要进行一次转换
                // 历史遗留原因，只有 Comate 和 AutoWork 需要转换，Comate+ 直接使用 '指令@插件' 的方式来拼成id
                onCommandSelect(commandId?.id ?? currentContent.slash + '@' + currentContent.agent);
            }
            else {
                onAgentSelect(currentContent.agent);
            }
            // 这是一个兼容方案，理论是记录的应该总是rawMessage，不需要value
            onInputChange(currentContent.rawMessage || currentContent.value);
            setIsFromHistory(true);
            onPopoverTypeChange(undefined);
            event.preventDefault();
        },
        [onAgentSelect, onCommandSelect, onInputChange, onPopoverTypeChange, setIsFromHistory]
    );

    // eslint-disable-next-line complexity
    return async (event: KeyboardEvent, currentRef: HTMLTextAreaElement | null) => {
        const isUpKeyPressed = event.key === 'ArrowUp';
        const isDownKeyPressed = event.key === 'ArrowDown';

        if ((isUpKeyPressed || isDownKeyPressed) && history && (!activePopoverType || !popoverConfig)) {
            // 按上键且光标在文字最前面，则可以向上翻找一条历史
            if (isUpKeyPressed && currentRef?.selectionStart === 0) {
                currentHistoryIndex.current = Math.min(
                    currentHistoryIndex.current + 1,
                    history.length - 1
                );
                handleHistoryNavigation(
                    history,
                    currentHistoryIndex.current,
                    event
                );
            }
            // 按下键且光标在文字最后面，则可以向下翻找一条历史
            else if (isDownKeyPressed && currentRef?.selectionStart === currentRef?.value.length) {
                if (currentHistoryIndex.current === -1) {
                    // 最后一条消息时不做任何处理
                    return;
                }
                const isLastest = currentHistoryIndex.current === 0;
                currentHistoryIndex.current = Math.max(currentHistoryIndex.current - 1, 0);
                await handleHistoryNavigation(
                    history,
                    currentHistoryIndex.current,
                    event
                );
                !isLastest && valueRef.current?.setSelectionRange(0, 0);
            }
        }
        else {
            currentHistoryIndex.current = -1;
        }
    };
}

export default useHistoryNavigation;
