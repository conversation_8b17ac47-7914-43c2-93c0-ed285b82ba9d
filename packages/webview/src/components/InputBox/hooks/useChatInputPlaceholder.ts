/* eslint-disable complexity */
import {
    CODE_EXPLAIN,
    FUNCTION_COMMENT,
    FUNCTION_SPLIT,
    INLINE_COMMENT,
    OPTIMIZE,
    AUTO_TEST,
    IAPI,
    UT,
} from '@shared/constants';
import {useTranslation} from 'react-i18next';
import {
    INPUT_CLEAR_PLACEHOLDER,
    INPUT_CODE_PLACEHOLDER,
    INPUT_COMMAND_PLACEHOLDER,
    INPUT_COMMON_PLACEHOLDER,
    INPUT_DEFAULT_PLACEHOLDER,
    INPUT_HELP_PLACEHOLDER,
    INPUT_AUTO_TEST_PLACEHOLDER,
    INPUT_IAPI_INTERNAL_PLACEHOLDER,
    INPUT_IAPI_SAAS_PLACEHOLDER,
    INPUT_UT_PLACEHOLDER,
} from '@/i18n/constants';
import {useChatInput} from './useChatInput';

const isInternal = $features.PLATFORM === 'internal';

export const useChatInputPlaceholder = () => {
    const {t} = useTranslation();
    const {selectedAgentId, selectedCommand, commands, suggestionCapability} = useChatInput();

    if (!selectedAgentId && suggestionCapability) {
        // 兼容三方插件定制需求 有些插件(比如飞桨)存在不希望展示名称的能力
        // 防止出现 "推荐使用插件 @飞桨/" 多一个/的问题
        return suggestionCapability.displayName
            ? `推荐使用插件 @${suggestionCapability.owner.displayName}/${suggestionCapability.displayName} `
            : `推荐使用插件 @${suggestionCapability.owner.displayName} `;
    }

    if (!selectedAgentId) {
        return t(INPUT_DEFAULT_PLACEHOLDER);
    }

    if (!selectedCommand && !commands?.length) {
        return t(INPUT_COMMAND_PLACEHOLDER);
    }

    const detail = commands?.find(item => item.id === selectedCommand);
    if (detail?.placeholder) {
        return detail.placeholder;
    }

    switch (selectedCommand) {
        case FUNCTION_COMMENT + '@Comate':
        case INLINE_COMMENT + '@Comate':
        case CODE_EXPLAIN + '@Comate':
        case FUNCTION_SPLIT + '@Comate':
        case OPTIMIZE + '@Comate':
            return t(INPUT_CODE_PLACEHOLDER);
        case 'clear@Comate':
            return t(INPUT_CLEAR_PLACEHOLDER);
        case 'help@Comate':
            return t(INPUT_HELP_PLACEHOLDER);
        case AUTO_TEST + '@Comate':
            return t(INPUT_AUTO_TEST_PLACEHOLDER);
        case IAPI + '@Comate':
            return t(isInternal ? INPUT_IAPI_INTERNAL_PLACEHOLDER : INPUT_IAPI_SAAS_PLACEHOLDER);
        case UT + '@Comate':
            return t(INPUT_UT_PLACEHOLDER);
        default:
            return t(INPUT_COMMON_PLACEHOLDER);
    }
};
