import {noop} from 'lodash';
import {ComponentProps, createContext} from 'preact';
import {Dispatch, useContext, useMemo, useReducer} from 'preact/hooks';
import {AgentListWithId, CommandListWithId, Feature, KnowledgeList} from '@shared/protocols';
import {ProviderCapabilityInfo} from '@comate/plugin-shared-internals';
import {E2EBOT_AGENT} from '@shared/agents';
import {useExtensionConfig} from '@/hooks/useExtensionConfig';
import {useI18nLanguage} from '@/hooks/useLanguage';
import {useAccessTo} from '@/hooks/useAccessTo';
import e2eBotIcon from '@/assets/agent/e2eBot.png';
import {buildCommandList} from '../utils';
import {ExtensionPopoverType} from '../utils/popover';
import {BUILTIN_AGENT, KNOWLEDGE_ITEM} from '../constants';

export enum ActionType {
    INPUT_TEXT_CHANGED = 'INPUT_TEXT_CHANGED',
    MENTION_INPUT_TEXT_CHANGED = 'MENTION_INPUT_TEXT_CHANGED',
    AGENT_SELECTED = 'AGENT_SELECTED',
    COMMAND_SELECTED = 'COMMAND_SELECTED',
    KNOWLEDGE_TYPE_SELECTED = 'KNOWLEDGE_TYPE_SELECTED',
    QUERY_SELECTOR = 'QUERY_SELECTOR',
    UPDATE_AGENTS = 'UPDATE_AGENTS',
    FUZZY_SEARCH = 'FUZZY_SEARCH',
    UPDATE_COMMANDS = 'UPDATE_COMMANDS',
    UPDATE_KNOWLEDGES = 'UPDATE_KNOWLEDGES',
    UPDATE_KNOWLEDGE_TYPE = 'UPDATE_KNOWLEDGE_TYPE',
    POPOVER_TYPE_CHANGED = 'POPOVER_TYPE_CHANGED',
    UPDATE_WORKSPACE_STATUS = 'UPDATE_WORKSPACE_STATUS',
    UPDATE_COMMANDS_OF_AGENT = 'UPDATE_COMMANDS_OF_AGENT',
    UPDATE_FILE_CONTEXT = 'UPDATE_FILE_CONTEXT',
    POP_FILE_CONTEXT = 'POP_FILE_CONTEXT',
}

export interface KnowledgeType {
    id: string;
    name: string;
    disabled?: boolean;
    disabledReason?: string;
    displayName: string;
    displayNameKey?: string;
}

export interface WorkspaceStatus {
    fileOpened: boolean;
    workspaceOpened: boolean;
}

interface State {
    agents: AgentListWithId[];
    commands: CommandListWithId[];
    knowledgeTypes: KnowledgeType[];
    knowledges: KnowledgeList[];
    activePopoverType?: ExtensionPopoverType;
    /** 输入框内的文本 */
    text: string;
    /** 选中的agentId */
    selectedAgentId?: string;
    /** 选中的命令, ?command是否应该跟agent绑死 */
    selectedCommand?: string;
    /** 选中的知识类型，如当前文件，代码库，目录等 */
    selectedKnowledgeType?: string;
    suggestionCapability?: ProviderCapabilityInfo;
    /** 内部属性，用户处理agent、command是通过关键字搜索选中的，还是通过点击选中的 */
    _keepInputText?: boolean;
    workspaceStatus?: WorkspaceStatus;
}

type Action =
    | {
        type: ActionType.INPUT_TEXT_CHANGED;
        payload: {
            text: string | ((text: string) => string);
            extra?: {prevChar?: string};
        };
    }
    | {
        type: ActionType.MENTION_INPUT_TEXT_CHANGED;
        payload: (text: string) => string;
    }
    | {type: ActionType.AGENT_SELECTED, payload: {id: string | undefined}}
    | {type: ActionType.COMMAND_SELECTED, payload: {command: string | undefined}}
    | {type: ActionType.UPDATE_AGENTS, payload: any}
    | {type: ActionType.UPDATE_COMMANDS, payload: any}
    | {type: ActionType.UPDATE_KNOWLEDGE_TYPE, payload: {knowledgeTypes: KnowledgeType[]}}
    | {type: ActionType.UPDATE_KNOWLEDGES, payload: any}
    | {type: ActionType.POPOVER_TYPE_CHANGED, payload: {type?: ExtensionPopoverType, opts?: {keepInputText?: boolean}}}
    | {type: ActionType.QUERY_SELECTOR, payload?: ProviderCapabilityInfo}
    // eslint-disable-next-line max-len
    | {
        type: ActionType.KNOWLEDGE_TYPE_SELECTED;
        payload: {type?: string, text: string} | ((prevState: State) => {type?: string, text: string});
    }
    | {type: ActionType.UPDATE_WORKSPACE_STATUS, payload?: WorkspaceStatus}
    | {type: ActionType.UPDATE_COMMANDS_OF_AGENT, payload?: {commands: CommandListWithId[], agentName: string}};

const matchExactAgentName = (text: string, agentNames: string[]) => {
    const name = text.slice(1, -1);
    if (text.startsWith('@') && text.endsWith(' ') && agentNames.includes(name)) {
        return name;
    }
};

// export 是为了写单测
// eslint-disable-next-line complexity, max-statements
export const reducer = (prevState: State, action: Action) => {
    const merge = (state: Partial<State>) => {
        // console.log('receive', state, 'merged', {...prevState, ...state})
        return {...prevState, ...state};
    };
    const actionType = action.type;
    switch (actionType) {
        case ActionType.INPUT_TEXT_CHANGED: {
            const {text: textUpdator, extra} = action.payload;
            const updatedText = typeof textUpdator === 'function' ? textUpdator(prevState.text) : textUpdator;
            if (updatedText === prevState.text) {
                return prevState;
            }

            if (updatedText === '@') {
                // 处理 agent
                if (prevState.text) {
                    return merge({text: updatedText});
                }
                else {
                    return merge({text: updatedText, selectedCommand: undefined, activePopoverType: 'agent'});
                }
            }
            else if (updatedText === '/' || updatedText === '、') {
                // 处理 comand
                if (prevState.selectedCommand) {
                    return merge({text: updatedText});
                }
                else {
                    return merge({text: updatedText, activePopoverType: 'command'});
                }
            }
            else if (extra?.prevChar === '#') {
                // 处理 #唤起知识类型
                return merge({text: updatedText, activePopoverType: 'pound'});
            }
            else if (updatedText.includes('#') && updatedText.split('#').pop()) {
                // 超过10个字的粘贴不触发#
                if (updatedText.length - prevState.text.length > 10) {
                    return merge({text: updatedText});
                }
                // 处理 #后输入关键字进行混合搜索
                return merge({text: updatedText, activePopoverType: 'knowledge'});
            }
            else if (extra?.prevChar === ' ') {
                const selectedAgentId = matchExactAgentName(updatedText, prevState.agents.map(item => item.name));
                // 如果完全匹配agent就直接选择，并清空
                if (selectedAgentId && !prevState.selectedAgentId) {
                    return merge({text: '', selectedAgentId, activePopoverType: undefined});
                }
                return merge({text: updatedText, activePopoverType: undefined});
            }
            else if (updatedText) {
                return merge({text: updatedText, activePopoverType: prevState.activePopoverType || 'smart'});
            }
            // 有插件但是没有命令的时候，展示命令列表
            if (prevState.selectedAgentId && updatedText === '') {
                return merge({text: updatedText, activePopoverType: 'smart'});
            }
            // 清空时，关闭所有弹窗
            return merge({text: updatedText, activePopoverType: undefined});
        }
        case ActionType.AGENT_SELECTED: {
            const keepInputText = prevState._keepInputText;
            // 选中agent后自动打开浮层
            const agent = prevState.agents.find(({id}) => action.payload.id === id);
            const hasCommands = !!agent?.capabilities?.length;
            return merge({
                selectedAgentId: action.payload.id,
                selectedCommand: undefined,
                activePopoverType: (hasCommands && !keepInputText) ? 'command' : undefined,
                text: (action.payload.id && !keepInputText) ? '' : prevState.text,
                _keepInputText: false,
            });
        }
        case ActionType.COMMAND_SELECTED: {
            const selectedCommand = action.payload.command;
            const keepInputText = prevState._keepInputText;
            if (!selectedCommand) {
                // 如果单独清空command, 则不需要处理agent
                return merge({selectedCommand, activePopoverType: undefined});
            }
            // 用户可以不选agent直接选command
            const agent = prevState.commands.find(item => item.id === selectedCommand)?.owner.name;
            return merge({
                selectedCommand,
                selectedAgentId: agent,
                text: keepInputText ? prevState.text : '',
                _keepInputText: false,
            });
        }
        case ActionType.KNOWLEDGE_TYPE_SELECTED: {
            const {type, text} = typeof action.payload === 'function'
                ? action.payload(prevState)
                : action.payload;
            if (!type) {
                return merge({selectedKnowledgeType: type, text});
            }
            return merge({
                selectedCommand: prevState.selectedCommand,
                selectedKnowledgeType: type,
                text,
            });
        }
        case ActionType.UPDATE_AGENTS: {
            return merge({agents: action.payload});
        }
        case ActionType.UPDATE_COMMANDS: {
            return merge({commands: action.payload});
        }
        case ActionType.UPDATE_COMMANDS_OF_AGENT: {
            const commands = (action.payload?.commands ?? [])
                .concat(prevState.commands.filter(({owner}) => action.payload?.agentName !== owner.name));
            return merge({commands});
        }
        case ActionType.UPDATE_KNOWLEDGES: {
            return merge({knowledges: action.payload});
        }
        case ActionType.UPDATE_KNOWLEDGE_TYPE: {
            return merge({knowledgeTypes: action.payload.knowledgeTypes});
        }
        case ActionType.POPOVER_TYPE_CHANGED: {
            return merge({activePopoverType: action.payload.type, _keepInputText: action.payload.opts?.keepInputText});
        }
        case ActionType.MENTION_INPUT_TEXT_CHANGED: {
            return merge({
                text: action.payload(prevState.text),
            });
        }
        case ActionType.QUERY_SELECTOR: {
            return merge({suggestionCapability: action.payload});
        }
        case ActionType.UPDATE_WORKSPACE_STATUS: {
            return merge({workspaceStatus: action.payload});
        }
        default: {
            try {
                // 这里会检查没有实现的ActionType
                const exhaustiveCheck: never = actionType;
                throw new Error(exhaustiveCheck);
            }
            catch (ex) {
                return prevState;
            }
        }
    }
};

interface Value {
    text: string;
    agents: AgentListWithId[];
    knowledges: any[];
    knowledgeTypes: KnowledgeType[];
    customized: boolean;
    commands: CommandListWithId[];
    activePopoverType?: ExtensionPopoverType;
    suggestionCapability?: ProviderCapabilityInfo;
    dispatch: Dispatch<Action>;
    selectedCommand?: string;
    selectedAgentId?: string;
    selectedKnowledgeType?: string;
    workspaceStatus?: WorkspaceStatus;
    onPopoverTypeChange: (type?: ExtensionPopoverType, opts?: {keepInputText?: boolean}) => void;
    onAgentSelect: (agentId: string | undefined) => void;
    onCommandSelect: (command: string | undefined) => void;
    onKnowledgeUpdate: (knowledges: KnowledgeList[]) => void;
    onKnowledgeTypeSelect: (
        type:
            | {type?: string, text: string}
            | ((prevState: State) => {type?: string, text: string})
    ) => void;
    onInputChange: (text: string | ((text: string) => string), extra?: {prevChar?: string}) => void;
    onMentionInputChange: (text: (text: string) => string) => void;
    onQuerySelector: (suggestionCapability?: ProviderCapabilityInfo) => void;
    onWorkspaceStatusUpdate: (workspaceStatus: WorkspaceStatus) => void;
}

const defaultContextValue: Value = {
    agents: [],
    commands: buildCommandList(),
    knowledges: [],
    knowledgeTypes: [],
    activePopoverType: undefined,
    dispatch: noop,
    onInputChange: noop,
    onCommandSelect: noop,
    onAgentSelect: noop,
    onKnowledgeUpdate: noop,
    onKnowledgeTypeSelect: noop,
    onMentionInputChange: noop,
    onPopoverTypeChange: noop,
    onQuerySelector: noop,
    onWorkspaceStatusUpdate: noop,
    text: '',
    suggestionCapability: undefined,
    customized: false,
    workspaceStatus: undefined,
};
const context = createContext<Value>(defaultContextValue);
const defaultAgents = [BUILTIN_AGENT, {...E2EBOT_AGENT, icon: e2eBotIcon}];

export const ChatInputProvider = (props: Omit<ComponentProps<typeof context.Provider>, 'value'>) => {
    const [state, dispatch] = useReducer(reducer, defaultContextValue);

    const lng = useI18nLanguage();
    const {config: {customized, enablePlugin}} = useExtensionConfig();
    const enableComatePlus = useAccessTo(Feature.ComatePlus);

    const {
        text,
        agents,
        commands,
        knowledges,
        knowledgeTypes,
        selectedCommand,
        activePopoverType,
        selectedKnowledgeType,
        selectedAgentId,
        suggestionCapability,
        workspaceStatus,
    } = state;

    // 英文版本和混合云/私有化用户需要屏蔽掉第三方 agent
    const agentsFilterByLng = useMemo(
        () => ((lng === 'en' || customized || !enablePlugin || !enableComatePlus) ? [] : agents),
        [lng, customized, enablePlugin, enableComatePlus, agents]
    );

    // 英文版本和混合云/私有化用户需要屏蔽掉第三方 agent 的command
    const commandsFilterByLng = useMemo(
        () => {
            return (lng === 'en' || customized || !enablePlugin || !enableComatePlus)
                ? buildCommandList([], [], commands)
                : commands;
        },
        [lng, customized, enablePlugin, enableComatePlus, commands]
    );

    // 英文版本需要屏蔽掉知识引用
    const knowledgeTypesFilterByLng = useMemo(
        () => (lng === 'en'
            ? knowledgeTypes.filter(v => v.id !== KNOWLEDGE_ITEM.id)
            : knowledgeTypes),
        [knowledgeTypes, lng]
    );

    const value = useMemo<Value>(
        () => ({
            text: text,
            activePopoverType,
            selectedCommand,
            selectedAgentId,
            selectedKnowledgeType,
            agents: agentsFilterByLng.length ? agentsFilterByLng : defaultAgents,
            commands: commandsFilterByLng,
            knowledges,
            knowledgeTypes: knowledgeTypesFilterByLng,
            suggestionCapability,
            customized,
            workspaceStatus,
            dispatch,
            onQuerySelector: (topCapability?: ProviderCapabilityInfo) => {
                dispatch({type: ActionType.QUERY_SELECTOR, payload: topCapability});
            },
            onCommandSelect: command => {
                dispatch({type: ActionType.COMMAND_SELECTED, payload: {command}});
            },
            onAgentSelect: agentId => {
                dispatch({type: ActionType.AGENT_SELECTED, payload: {id: agentId}});
            },
            onKnowledgeTypeSelect: updator => {
                dispatch({type: ActionType.KNOWLEDGE_TYPE_SELECTED, payload: updator});
            },
            onInputChange: (updator, extra) => {
                dispatch({type: ActionType.INPUT_TEXT_CHANGED, payload: {text: updator, extra}});
            },
            onMentionInputChange: updator => {
                dispatch({type: ActionType.MENTION_INPUT_TEXT_CHANGED, payload: updator});
            },
            onPopoverTypeChange: (type, opts) => {
                dispatch({type: ActionType.POPOVER_TYPE_CHANGED, payload: {type, opts}});
            },
            onKnowledgeUpdate: knowledges => {
                dispatch({type: ActionType.UPDATE_KNOWLEDGES, payload: knowledges});
            },
            onWorkspaceStatusUpdate: workspaceStatus => {
                dispatch({type: ActionType.UPDATE_WORKSPACE_STATUS, payload: workspaceStatus});
            },
        }),
        [
            activePopoverType,
            agentsFilterByLng,
            commandsFilterByLng,
            knowledgeTypesFilterByLng,
            knowledges,
            selectedAgentId,
            selectedCommand,
            selectedKnowledgeType,
            suggestionCapability,
            text,
            customized,
            workspaceStatus,
        ]
    );

    return <context.Provider value={value} {...props} />;
};

/**
 * 因为`ChatInput`除了自身的事件外，还是有外部事件，包括引导、或者是通过编辑区、extension在填充对话，将状态提升到context上来
 */
export const useChatInput = () => {
    return useContext(context);
};
