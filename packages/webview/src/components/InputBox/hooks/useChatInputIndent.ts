import {useEffect, useState} from 'preact/hooks';
import {RefObject} from 'preact';
import {useChatInput} from './useChatInput';

export const useChatInputIndent = (
    input: RefObject<HTMLTextAreaElement>,
    commandTextRef: RefObject<HTMLSpanElement>
) => {
    const {selectedAgentId, selectedCommand} = useChatInput();
    const [indent, setIndent] = useState(0);
    useEffect(
        () => {
            const tmp = commandTextRef.current?.offsetWidth || 0;
            const textarea = input.current;
            if (textarea) {
                // Jetbrains 未启动时，textarea.offsetWidth 为 0
                if (tmp > Math.max(textarea.offsetWidth - 42, 0)) {
                    setIndent(0);
                }
                else {
                    setIndent(tmp);
                }
            }
        },
        [commandTextRef, input, selectedAgentId, selectedCommand]
    );

    return {indent};
};
