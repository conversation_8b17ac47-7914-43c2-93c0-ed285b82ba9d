import {vi} from 'vitest';
import {act, renderHook} from '@testing-library/preact';
import {EventMessage} from '@shared/protocols';
import {useChatInput, ChatInputProvider} from '../useChatInput';
import {useChatInputPlaceholder} from '../useChatInputPlaceholder';
import {useChatResources} from '../useChatResources';

vi.mock('@/assets/file.svg', () => ({default: ''}));
vi.mock('@/assets/folder.svg', () => ({default: ''}));
vi.mock('@/assets/knowledge.svg', () => ({default: ''}));
vi.mock('@/assets/repo.svg', () => ({default: ''}));
vi.mock('@/assets/currentFile.svg', () => ({default: ''}));
vi.mock('@/assets/add.svg', () => ({default: ''}));

vi.mock('@/utils/messageHandler', () => ({
    messageHandler: {
        listen: (eventNotUsed: string, handler: any) => {
            setTimeout(
                () => {
                    if (eventNotUsed === EventMessage.UpdateChatCompletionEvent) {
                        const gitAgent = {
                            name: 'git',
                            capabilities: [
                                {
                                    type: 'Skill',
                                    name: 'commitMessage',
                                    placeholder: '无需输入内容，回车为你生成提交信息',
                                },
                                {
                                    type: 'Skill',
                                    name: 'createIssue',
                                    placeholder: '无需输入内容，回车为你生成一个Issue',
                                },
                            ],
                            id: 'git',
                        };
                        const agents = [gitAgent];
                        const commands = gitAgent.capabilities.map(c => ({
                            ...c,
                            id: `${c.name}@${gitAgent.name}`,
                            owner: gitAgent,
                        }));
                        const knowledges = [];
                        handler([agents, commands, knowledges]);
                    }
                },
                0
            );
        },
        send: async () => ({}),
    },
}));

const sleep = (ms: number) => new Promise(r => setTimeout(r, ms));

describe('useChatInput', () => {
    it('测试一次选择git插件后，placeholder展示是否正常', async () => {
        const wrapper = ({children}) => <ChatInputProvider>{children}</ChatInputProvider>;
        const {result} = renderHook(
            () => {
                const chatInput = useChatInput();
                const placeholder = useChatInputPlaceholder();
                const chatResource = useChatResources();
                return {chatInput, placeholder, chatResource};
            },
            {wrapper}
        );

        await sleep(100);
        expect(result.current.placeholder).toBe('使用 / 调起快捷指令、@ 调起插件或 # 引用知识');
        act(() => result.current.chatInput.onAgentSelect('git'));
        expect(result.current.chatInput.selectedAgentId).toBe('git');
        expect(result.current.placeholder).toBe('输入 / 调起快捷指令');
        act(() => result.current.chatInput.onCommandSelect('commitMessage@git'));
        expect(result.current.chatInput.selectedCommand).toBe('commitMessage@git');
        expect(result.current.chatInput.selectedAgentId).toBe('git');
        expect(result.current.placeholder).toBe('无需输入内容，回车为你生成提交信息');
    });

    it('测试输入框通过@关键字加空格自动选择agent', async () => {
        const wrapper = ({children}) => <ChatInputProvider>{children}</ChatInputProvider>;
        const {result} = renderHook(
            () => {
                const chatInput = useChatInput();
                const placeholder = useChatInputPlaceholder();
                const chatResource = useChatResources();
                return {chatInput, placeholder, chatResource};
            },
            {wrapper}
        );

        await sleep(100);
        act(() => result.current.chatInput.onInputChange('@git ', {prevChar: ' '}));
        expect(result.current.chatInput.selectedAgentId).toBe('git');
        expect(result.current.chatInput.text).toBe('');
    });

    it('测试选中agent、command后清除command, 应该保留agent', async () => {
        const wrapper = ({children}) => <ChatInputProvider>{children}</ChatInputProvider>;
        const {result} = renderHook(
            () => {
                const chatInput = useChatInput();
                const placeholder = useChatInputPlaceholder();
                const chatResource = useChatResources();
                return {chatInput, placeholder, chatResource};
            },
            {wrapper}
        );

        await sleep(100);
        act(() => result.current.chatInput.onAgentSelect('git'));
        act(() => result.current.chatInput.onCommandSelect('commitMessage@git'));
        expect(result.current.chatInput.selectedAgentId).toBe('git');
        expect(result.current.chatInput.selectedCommand).toBe('commitMessage@git');
        act(() => result.current.chatInput.onCommandSelect(undefined));
        expect(result.current.chatInput.selectedAgentId).toBe('git');
        expect(result.current.chatInput.selectedCommand).toBe(undefined);
    });

    it('测试选中agent、command后设置agent, 应该清除command', async () => {
        const wrapper = ({children}) => <ChatInputProvider>{children}</ChatInputProvider>;
        const {result} = renderHook(
            () => {
                const chatInput = useChatInput();
                const placeholder = useChatInputPlaceholder();
                const chatResource = useChatResources();
                return {chatInput, placeholder, chatResource};
            },
            {wrapper}
        );

        await sleep(100);
        act(() => result.current.chatInput.onAgentSelect('git'));
        act(() => result.current.chatInput.onCommandSelect('commitMessage@git'));
        expect(result.current.chatInput.selectedAgentId).toBe('git');
        expect(result.current.chatInput.selectedCommand).toBe('commitMessage@git');
        act(() => result.current.chatInput.onAgentSelect(undefined));
        act(() => result.current.chatInput.onInputChange('Hello world'));
        expect(result.current.chatInput.selectedAgentId).toBe(undefined);
        expect(result.current.chatInput.selectedCommand).toBe(undefined);
        expect(result.current.chatInput.text).toBe('Hello world');
    });

    it('测试#后直接输入关键字进行混合搜索', async () => {
        const wrapper = ({children}) => <ChatInputProvider>{children}</ChatInputProvider>;
        const {result} = renderHook(
            () => {
                const chatInput = useChatInput();
                const placeholder = useChatInputPlaceholder();
                const chatResource = useChatResources();
                return {chatInput, placeholder, chatResource};
            },
            {wrapper}
        );

        await sleep(100);
        act(() => result.current.chatInput.onInputChange('#hello#world'));
        expect(result.current.chatInput.activePopoverType).toBe('knowledge');
        expect(result.current.chatInput.text).toBe('#hello#world');
    });
});
