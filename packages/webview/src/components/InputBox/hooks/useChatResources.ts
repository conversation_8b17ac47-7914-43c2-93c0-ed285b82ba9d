/**
 * 拆出来的原因是因为这个文件涉及到事件的监听，也许会遇到跨设备兼容的情况，如果有可以尝试用useChatResources.jetbrain.ts
 */
import {
    AgentList,
    AgentListWithId,
    AutoWorkKnowledgeType,
    EventMessage,
    Feature,
    WebviewAgentConversationType,
} from '@shared/protocols';
import {useCallback, useEffect, useMemo, useState} from 'preact/hooks';
import Fuse, {FuseResult, FuseResultMatch, RangeTuple} from 'fuse.js';
import {useTranslation} from 'react-i18next';
import {compact, uniqBy} from 'lodash';
import {FeatureName, isFeatureVisible} from '@shared/utils/features';
import {OPTIMIZE, UT} from '@shared/constants';
import {PROMPT_TEMPLATE_AGENT, UT_COMMAND} from '@shared/agents';
import {messageHandler} from '@/utils/messageHandler';
import {
    AGENT_AUTOWORK_REFER_CODEBASE_DISABLE_REASON,
    AGENT_AUTOWORK_REFER_WEB_SEARCH_DISABLE_REASON,
    AGENT_AUTOWORK_REFER_API_DISABLE_REASON,
} from '@/i18n/constants';
import {useAccessTo} from '@/hooks/useAccessTo';
import {useExtensionConfig} from '@/hooks/useExtensionConfig';
import {withTimeout} from '@/utils/promise';
import {buildAgentList, buildCommandList} from '../utils';
import {
    CODEBASE_KNOWLEDGE_ITEM,
    FILE_KNOWLEDGE_ITEM,
    FOLDER_KNOWLEDGE_ITEM,
    IAPI_KNOWLEDGE_ITEM,
    KNOWLEDGE_ITEM,
    TERMINAL_ITEM,
    WEB_KNOWLEDGE_ITEM,
    WEB_SEARCH_ITEM,
} from '../constants';
import {fuseMatchesToPositions} from '../utils/search';
import {ActionType, KnowledgeType, useChatInput} from './useChatInput';
import {getPinyin, getPY} from './pinyin';

export type AgentListWithItemType = AgentListWithId & {
    itemType: 'agent' | 'command';
    behaveAsBuiltIn?: boolean;
    /** itemType === 'command'时有owner字段 */
    owner?: AgentList;
};

export const disabledWebAndIAPIWhenInactive = (knowledge: KnowledgeType, text: string) => {
    if (knowledge.id === AutoWorkKnowledgeType.IAPI) {
        return {...knowledge, disabled: text.includes('link:API-')};
    }
    else if (knowledge.id === AutoWorkKnowledgeType.WEB_SEARCH) {
        return {...knowledge, disabled: text.includes('$[网络检索](webSearch)')};
    }
    return knowledge;
};

type FuzzySearch = null | {type: 'agent' | 'command' | 'knowledge' | 'smart', keyword: string};
export const useChatResources = () => {
    const {t} = useTranslation();
    const [fuzzySearch, setFuzzySearch] = useState<FuzzySearch>(null);
    const {
        selectedAgentId,
        selectedCommand,
        agents: _remoteAgents,
        commands: _remoteCommands,
        dispatch,
    } = useChatInput();
    const enableIAPI = useAccessTo(Feature.EnableIAPI);
    const {config: {enableSearchFromInternet}} = useExtensionConfig();

    const remoteAgents = _remoteAgents;
    const remoteCommands = _remoteCommands;

    useEffect(
        () => {
            messageHandler.listen(
                EventMessage.UpdateChatCompletionEvent,
                async ([info, pluginConfigs]) => {
                    const [agentListWithId, commandListWithId, knowledgeList, promptTemplateList] = info;
                    // 兼容jetbrains企业用户登录时，会发送一次空消息。如果没有任何agent，则不做任何处理
                    // if (!agentListWithId.length && !knowledgeList.length) {
                    //     return;
                    // }

                    // 需要内建到一方的插件 逻辑可以挪到 engine
                    const builtInPlugins = Object.keys(pluginConfigs).filter(key =>
                        pluginConfigs[key].meta?.behaveAsBuiltIn
                    );

                    // eslint-disable-next-line no-console
                    console.log('update chat completion', info, pluginConfigs, builtInPlugins);

                    const agents = await buildAgentList(agentListWithId, builtInPlugins);
                    dispatch({
                        type: ActionType.UPDATE_AGENTS,
                        payload: agents,
                    });
                    dispatch({
                        type: ActionType.UPDATE_COMMANDS,
                        payload: buildCommandList(
                            commandListWithId,
                            builtInPlugins,
                            remoteCommands,
                            promptTemplateList
                        ),
                    });
                    dispatch({type: ActionType.UPDATE_KNOWLEDGES, payload: knowledgeList});
                }
            );
        },
        [dispatch, remoteCommands]
    );

    useEffect(
        () => {
            messageHandler.listen(
                EventMessage.UpdateKnowledgeListEvent,
                knowledgeList => {
                    // eslint-disable-next-line no-console
                    console.log('update chat knowledge list', knowledgeList);
                    dispatch({type: ActionType.UPDATE_KNOWLEDGES, payload: knowledgeList});
                }
            );
        },
        [dispatch]
    );

    useEffect(
        () => {
            messageHandler.listen(
                EventMessage.PromptTemplateListUpdateEvent,
                promptTemplateList => {
                    const selectedCommandObj = selectedCommand
                        ? remoteCommands?.find(command => command.id === selectedCommand)
                        : undefined;
                    const agentNameOfSelectedCommand = selectedCommandObj?.owner?.name;

                    const upcomingCommands = (promptTemplateList ?? []).map(promptTemplate => {
                        return {
                            id: `${promptTemplate.uuid}@${PROMPT_TEMPLATE_AGENT.id}`,
                            name: promptTemplate.uuid,
                            displayName: promptTemplate.name,
                            description: promptTemplate.description,
                            owner: PROMPT_TEMPLATE_AGENT,
                        };
                    });

                    // 这里是为了应对选中的自定义prompt被删除的情况，所以需要把它也加入新查询到的自定义prompt列表
                    const commands = agentNameOfSelectedCommand === PROMPT_TEMPLATE_AGENT.name
                        ? uniqBy([...upcomingCommands, selectedCommandObj], 'id')
                        : upcomingCommands;

                    dispatch({
                        type: ActionType.UPDATE_COMMANDS_OF_AGENT,
                        payload: {
                            commands,
                            agentName: PROMPT_TEMPLATE_AGENT.name,
                        },
                    });
                }
            );
        },
        [dispatch, remoteCommands, selectedCommand]
    );

    useEffect(
        () => {
            messageHandler.listen(
                EventMessage.AgentCommandVisibleEvent,
                ({type, visible}: {type: WebviewAgentConversationType, visible: boolean}) => {
                    // eslint-disable-next-line no-console
                    if (type === WebviewAgentConversationType.TestBotConversation) {
                        if (visible) {
                            const testCommandVisible = remoteCommands.find(command => command.name === UT);
                            // 如果单测智能体可用，将单测生成指令插入到『调优建议』后
                            const insertIdx = remoteCommands.findIndex(command => command.name === OPTIMIZE);
                            const updatedCommands = [
                                ...remoteCommands.slice(0, insertIdx + 1),
                                UT_COMMAND,
                                ...remoteCommands.slice(insertIdx + 1),
                            ];
                            const commands = testCommandVisible ? remoteCommands : updatedCommands;
                            dispatch({type: ActionType.UPDATE_COMMANDS, payload: commands});
                        }
                        else {
                            const updatedCommands = remoteCommands.filter(command => command.name !== UT);
                            dispatch({type: ActionType.UPDATE_COMMANDS, payload: updatedCommands});
                        }
                    }
                }
            );
        },
        [dispatch, remoteCommands]
    );

    const updateKnowledgeTypeByIDEEvent = useCallback(
        (workspaceStatus: {fileOpened: boolean, workspaceOpened: boolean}) => {
            const allKnowledgeTypes = compact([
                {
                    ...FILE_KNOWLEDGE_ITEM,
                    disabled: !workspaceStatus.workspaceOpened,
                    disabledReason: t(AGENT_AUTOWORK_REFER_CODEBASE_DISABLE_REASON),
                },
                {
                    ...FOLDER_KNOWLEDGE_ITEM,
                    disabled: !workspaceStatus.workspaceOpened,
                    disabledReason: t(AGENT_AUTOWORK_REFER_CODEBASE_DISABLE_REASON),
                },
                {
                    ...CODEBASE_KNOWLEDGE_ITEM,
                    disabled: !workspaceStatus.workspaceOpened,
                    disabledReason: t(AGENT_AUTOWORK_REFER_CODEBASE_DISABLE_REASON),
                },
                TERMINAL_ITEM,
            ]);
            if (enableSearchFromInternet) {
                allKnowledgeTypes.push(
                    WEB_KNOWLEDGE_ITEM,
                    {
                        ...WEB_SEARCH_ITEM,
                        disabledReason: t(AGENT_AUTOWORK_REFER_WEB_SEARCH_DISABLE_REASON),
                    }
                );
            }
            if (isFeatureVisible(FeatureName.HASH_MENU_KNOWLEDGE_SET)) {
                allKnowledgeTypes.push(
                    KNOWLEDGE_ITEM
                );
            }
            if (enableIAPI && enableSearchFromInternet) {
                allKnowledgeTypes.push(
                    {
                        ...IAPI_KNOWLEDGE_ITEM,
                        disabledReason: t(AGENT_AUTOWORK_REFER_API_DISABLE_REASON),
                    }
                );
            }

            dispatch({
                type: ActionType.UPDATE_KNOWLEDGE_TYPE,
                payload: {
                    knowledgeTypes: allKnowledgeTypes,
                },
            });
            dispatch({type: ActionType.UPDATE_WORKSPACE_STATUS, payload: workspaceStatus});
        },
        [dispatch, enableIAPI, t, enableSearchFromInternet]
    );

    useEffect(
        () => {
            withTimeout(messageHandler.send(EventMessage.WorkspaceStatusEvent, {}), 3000)
                .then(updateKnowledgeTypeByIDEEvent)
                .catch(() => updateKnowledgeTypeByIDEEvent({fileOpened: true, workspaceOpened: true}));
            messageHandler.listen(
                EventMessage.WorkspaceStatusEvent,
                (workspaceStatus: {fileOpened: boolean, workspaceOpened: boolean}) => {
                    updateKnowledgeTypeByIDEEvent(workspaceStatus);
                }
            );
        },
        [updateKnowledgeTypeByIDEEvent]
    );
    const translateDisplay = useCallback(
        <T extends {displayNameKey?: string, displayName?: string, name: string, description?: string}>(arr: T[]) => {
            const result = arr.map(item => {
                const searchName = item.displayNameKey ? t(item.displayNameKey) : (item.displayName ?? item.name);
                const descriptionPinyin = item.description ? getPinyin(item.description) : undefined;
                const [pinyin, chinesePositions] = getPinyin(searchName);
                return {
                    ...item,
                    searchName,
                    pinyin,
                    chinesePositions,
                    py: getPY(searchName),
                    descriptionPinyin,
                };
            });
            return result;
        },
        [t]
    );

    /** 如果组件要拿过滤后的list可以从这个hook取，useChatInput里是完整的 */
    const agents = useMemo(
        (): AgentListWithItemType[] => {
            if (fuzzySearch?.type === 'agent' && fuzzySearch.keyword) {
                const fuse = new Fuse(
                    translateDisplay(remoteAgents),
                    {
                        keys: ['name', 'searchName', 'description', 'pinyin', 'py'],
                        threshold: 0.2,
                    }
                );
                const result = fuse.search(fuzzySearch.keyword);
                return result.map(item => ({...item.item, itemType: 'agent'}));
            }
            return remoteAgents.map(v => ({...v, itemType: 'agent'}));
        },
        [fuzzySearch, remoteAgents, translateDisplay]
    );

    const commands = useMemo(
        (): AgentListWithItemType[] => {
            // 如果没选中agent，输入/，把agents展示在commands后面, 如果选中了，过滤出commands
            const selectedAgentCommands: AgentListWithItemType[] = selectedAgentId
                ? remoteCommands
                    .filter(item => item.owner.name === selectedAgentId)
                    .map((v): AgentListWithItemType => ({...v, itemType: 'command'}))
                : [
                    ...remoteCommands.map((v): AgentListWithItemType => ({...v, itemType: 'command'})),
                    ...remoteAgents
                        .map((v): AgentListWithItemType => ({...v, itemType: 'agent'}))
                        .filter(v => !v.behaveAsBuiltIn), // 排除到内建到一方的，不展示
                ];

            if (fuzzySearch?.type === 'command' && fuzzySearch.keyword) {
                const fuse = new Fuse(
                    translateDisplay<typeof selectedAgentCommands[number]>(selectedAgentCommands),
                    {
                        // descriptionPinyin是为了自定义指令支持description的拼音搜索
                        keys: ['name', 'searchName', 'description', 'pinyin', 'py', 'descriptionPinyin'],
                        threshold: 0.2,
                    }
                );
                const result = fuse.search(fuzzySearch.keyword);

                return result.map(item => item.item);
            }

            return selectedAgentCommands;
        },
        [fuzzySearch, remoteAgents, remoteCommands, selectedAgentId, translateDisplay]
    );

    const smartResult = useMemo(
        (): Array<FuseResult<AgentListWithItemType> & {highlightPosition: number[]}> => {
            if (
                !fuzzySearch
                // 避免大段文本卡顿
                || fuzzySearch.keyword.length > 30
            ) {
                return [];
            }
            if (fuzzySearch?.type === 'smart') {
                // 如果没选中agent，输入/，把agents展示在commands后面
                const selectedAgentCommands: AgentListWithItemType[] = selectedAgentId
                    ? remoteCommands
                        .filter(item => item.owner.name === selectedAgentId)
                        .map(v => ({...v, itemType: 'command'} as const))
                    : [
                        ...remoteCommands.map(v => ({...v, itemType: 'command'} as const)),
                        ...remoteAgents
                            .map((v): AgentListWithItemType => ({...v, itemType: 'agent'}))
                            .filter(v => !v.behaveAsBuiltIn), // 排除到内建到一方的，不展示
                    ];
                const fuse = new Fuse(translateDisplay<typeof selectedAgentCommands[number]>(selectedAgentCommands), {
                    keys: [
                        'name',
                        'searchName',
                        {name: 'description', weight: 0.2},
                        'pinyin',
                        'py',
                        // descriptionPinyin是为了自定义指令支持description的拼音搜索
                        {name: 'descriptionPinyin', weight: 0.2},
                    ],
                    // 包含匹配位置，智能模式确认时，需据此删除匹配后的文本
                    includeMatches: true,
                    // useExtendedSearch的特殊语法会微调模糊匹配的结果，提高匹配精准度
                    // 如果想使用默认的模糊匹配，请去掉此项配置
                    useExtendedSearch: true,
                });
                if (!fuzzySearch.keyword) {
                    return selectedAgentCommands.map(item => ({
                        item,
                        score: 0,
                        matches: [],
                        highlightPosition: [],
                        refIndex: 0,
                    }));
                }
                // useExtendedSearch的特殊语法： '是include-match
                const result = fuse.search(`'${fuzzySearch.keyword}`);

                return result.map(v => {
                    return {
                        ...v,
                        highlightPosition: fuseMatchesToPositions(
                            v
                                .matches
                                // 高亮支持 拼音首字母匹配、拼音全词匹配 和 全词匹配
                                // 当前为模糊匹配后的include-match微调
                                ?.reduce(
                                    (acc, cur) => {
                                        if (cur.key === 'searchName') {
                                            return [...acc, cur];
                                        }
                                        if (cur.key === 'pinyin' && !acc.find(v => v.key === 'py')) {
                                            return [
                                                ...acc,
                                                {
                                                    ...cur,
                                                    indices: cur.indices.map(
                                                        range => [
                                                            v.item.chinesePositions[range[0]],
                                                            v.item.chinesePositions[range[1]],
                                                        ]
                                                    ) as RangeTuple[],
                                                },
                                            ];
                                        }
                                        if (cur.key === 'py' && !acc.find(v => v.key === 'pinyin')) {
                                            return [...acc, cur];
                                        }
                                        return acc;
                                    },
                                    [] as FuseResultMatch[]
                                )
                                .flatMap(
                                    v => v.indices
                                )
                        ),
                    };
                });
            }
            return [];
        },
        [fuzzySearch, remoteAgents, remoteCommands, selectedAgentId, translateDisplay]
    );

    return {
        fuzzySearch,
        agents,
        commands,
        smartResult,
        setFuzzySearch,
    };
};
