import {AutoWorkKnowledgeType} from '@shared/protocols';
import {
    AGENT_AUTOWORK_REFER_ADD,
    AGENT_AUTOWORK_REFER_CURRENT_CODEBASE,
    AGENT_AUTOWORK_REFER_DOCS,
    AGENT_AUTOWORK_REFER_EDITOR,
    AGENT_AUTOWORK_REFER_FILE,
    AGENT_AUTOWORK_REFER_FOLDER,
    AGENT_AUTOWORK_REFER_WEB,
    KNOWLEDGE_ITEM_IAPI_DESCRIPTION,
    AGENT_AUTOWORK_REFER_WEB_SEARCH,
    AGENT_AUTOWORK_REFER_TERMINAL,
} from '@/i18n/constants';
export {BUILTIN_AGENT, QUICK_COMMANDS, BUILTIN_COMMANDS, PROMPT_TEMPLATE_AGENT} from '@shared/agents';

interface KnowledgeItemInfo {
    id: string;
    displayName: string;
    displayNameKey?: string;
    description?: string;
    descriptionKey?: string;
    name: string;
}

// 知识类型
export const CURRENT_FILE_ITEM: KnowledgeItemInfo = {
    id: AutoWorkKnowledgeType.CURRENT_FILE,
    displayName: '当前文件',
    displayNameKey: AGENT_AUTOWORK_REFER_EDITOR,
    name: '当前文件',
};

export const TERMINAL_ITEM: KnowledgeItemInfo = {
    id: AutoWorkKnowledgeType.TERMINAL,
    displayName: '终端',
    displayNameKey: AGENT_AUTOWORK_REFER_TERMINAL,
    name: '终端',
};

export const WEB_KNOWLEDGE_ITEM: KnowledgeItemInfo = {
    id: AutoWorkKnowledgeType.WEB,
    displayName: '网页',
    displayNameKey: AGENT_AUTOWORK_REFER_WEB,
    name: '网页',
};

export const IAPI_KNOWLEDGE_ITEM: KnowledgeItemInfo = {
    id: AutoWorkKnowledgeType.IAPI,
    displayName: 'API',
    name: 'API',
    description: '基于 API 文档生成调用代码、Mock、自动化测试等',
    descriptionKey: KNOWLEDGE_ITEM_IAPI_DESCRIPTION,
};

export const CODEBASE_KNOWLEDGE_ITEM: KnowledgeItemInfo = {
    id: AutoWorkKnowledgeType.REPO,
    displayName: '当前代码库',
    displayNameKey: AGENT_AUTOWORK_REFER_CURRENT_CODEBASE,
    name: '当前代码库',
};

export const FOLDER_KNOWLEDGE_ITEM: KnowledgeItemInfo = {
    id: AutoWorkKnowledgeType.FOLDER,
    displayName: '目录',
    displayNameKey: AGENT_AUTOWORK_REFER_FOLDER,
    name: '目录',
};

export const FILE_KNOWLEDGE_ITEM: KnowledgeItemInfo = {
    id: AutoWorkKnowledgeType.FILE,
    displayName: '文件',
    displayNameKey: AGENT_AUTOWORK_REFER_FILE,
    name: '文件',
};

export const KNOWLEDGE_ITEM: KnowledgeItemInfo = {
    id: AutoWorkKnowledgeType.KNOWLEDGE,
    displayName: '知识集',
    displayNameKey: AGENT_AUTOWORK_REFER_DOCS,
    name: '知识集',
};

export const WEB_SEARCH_ITEM: KnowledgeItemInfo = {
    id: AutoWorkKnowledgeType.WEB_SEARCH,
    displayName: '网络检索',
    displayNameKey: AGENT_AUTOWORK_REFER_WEB_SEARCH,
    name: '网络检索',
};

export const NAVIGATE_KNOWLEDGE_ITEMS: KnowledgeItemInfo = {
    id: AutoWorkKnowledgeType.ADD_KNOWLEDGE,
    displayName: '新增知识集',
    displayNameKey: AGENT_AUTOWORK_REFER_ADD,
    name: '新增知识集',
};
