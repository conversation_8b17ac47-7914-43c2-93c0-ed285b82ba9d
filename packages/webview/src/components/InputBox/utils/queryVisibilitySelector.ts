import {SubmitHandler} from './suggestion';

export enum ComatePlusAgent {
    PADDLE = 'paddle',
    GDP = 'gdp',
}

function randomQuery(queries: string[]) {
    return queries[Math.floor(Math.random() * queries.length)];
}

// 粒度细化到插件的function
export const paddleQueries = {
    chat: {
        queries: [
            '使用Paddle实现矩阵乘',
            '用Paddle实现csr与dense的稀疏矩阵乘',
            '使用飞桨实现傅里叶变换',
            '用飞桨帮我实现一个Self-Attention网络',
            '创建一个函数来设置Paddle随机种子',
            '飞桨中如何进行梯度裁剪？',
            '用Paddle实现ResNet的模型训练与评估',
            '飞桨如何指定模型路径创建一个推理predictor',
        ],
        getSuggestion: (agent: string, capability: string, onSubmit: SubmitHandler) => {
            const prompt = randomQuery(paddleQueries.chat.queries);
            return {
                id: 1,
                type: 'default',
                autoFocus: true,
                agent,
                slash: capability,
                prompt,
                displayTextKey: prompt,
                onClick: () => {
                    onSubmit?.(prompt, agent, capability);
                },
            };
        },
    },
    // 点击后 实际上进行无query的代码转换功能
    'code-convert': {
        queries: [
            '将使用其他深度学习框架的代码转换为飞桨',
        ],
        getSuggestion: (agent: string, capability: string, onSubmit: SubmitHandler | undefined) => {
            const prompt = randomQuery(paddleQueries['code-convert'].queries);
            return {
                id: 1,
                type: 'default',
                autoFocus: true,
                agent,
                slash: capability,
                prompt: '',
                displayTextKey: prompt,
                onClick: () => {
                    onSubmit?.('', agent, capability);
                },
            };
        },
    },
};

// 有需要再细化到具体的function
export const gpdQueries = {
    queries: [
        'baidu gdp 生成接口，实现从表 arch 获取 id 最大的记录',
        'GDP crypto 生成脚本，对字符串进行加密',
        '发送多条消息 [AAA, BBB, CCC] 到 plusar 队列中',
        '从 bigpipe 消费 topic 名为 student 的内容',
    ],
    // 点击后 实际上跳转到codeGenerator功能
    getSuggestion: (agent: string, capability: string, onSubmit: SubmitHandler | undefined) => {
        const prompt = randomQuery(gpdQueries.queries);
        return {
            id: 1,
            type: 'default',
            agent,
            slash: capability,
            prompt,
            displayTextKey: prompt,
            autoFocus: true,
            onClick: () => {
                onSubmit?.(prompt, agent, 'codeGenerator');
            },
        };
    },
};

export const suggestForGDP = (agent: string, capability: string, onSubmit: SubmitHandler | undefined) =>
    gpdQueries.getSuggestion(agent, capability, onSubmit);

export const suggestForPaddle = (agent: string, capability: string, onSubmit: SubmitHandler | undefined) =>
    paddleQueries[capability]?.getSuggestion(agent, capability, onSubmit);
