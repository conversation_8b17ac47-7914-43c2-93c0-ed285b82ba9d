import {placeholderChar} from '@shared/protocols';
import {uniqBy} from 'lodash';

export interface Mention {
    id: string;
    display: string;
}

export {placeholderChar};

export const mention2Text = (mention: Mention) => {
    return `$[${mention.display}](${mention.id})`;
};

/**
 * 提取文案中的mentions
 * @param text eg: 我要查询 $[__当前文件__](__currentFile__) 和 $[__代码库__](__repo__)
 * @returns [{id: currentFile, display: 当前文件}, {id: repo, display: 代码库}]
 */
export const splitMentionText = (text: string) => {
    const {chunks} = Array.from(text).reduce<{processed: string, chunks: Array<Mention | string>}>(
        ({processed, chunks}, char, i) => {
            const concated = processed + char;
            const result = /\$\[(.*)\]\((.*)\)/.exec(concated);
            if (result) {
                const text = concated.replace(result[0], '');
                if (text) {
                    chunks.push(text);
                }
                chunks.push({id: result[2], display: result[1]});
                return {processed: '', chunks};
            }
            if (i === text.length - 1) {
                if (concated) {
                    chunks.push(concated);
                }
                return {processed: '', chunks};
            }
            return {processed: concated, chunks};
        },
        {processed: '', chunks: []}
    );

    return chunks;
};

/**
 * 提取文案中的mentions
 * @param text eg: 我要查询 $[__当前文件__](__currentFile__) 和 $[__代码库__](__repo__)
 * @returns [{id: currentFile, display: 当前文件}, {id: repo, display: 代码库}]
 */
export const extractMentionsFromMarkupedText = (text: string) => {
    return uniqBy(splitMentionText(text).filter(t => typeof t !== 'string'), 'id') as Mention[];
};

/**
 * 把文案中的mention占位符替换成纯文本
 * @param text eg: 我要查询 $[__当前文件__](__currentFile__) 和 $[__代码库__](__repo__)
 * @returns 我要查询 当前文件 和 代码库
 */
export const parseMarkupedText2PlainText = (markupedText: string = '') => {
    const {plainText} = Array.from(markupedText).reduce<{processed: string, plainText: string}>(
        ({processed, plainText}, char) => {
            const concated = processed + char;
            const regex = /\$\[(.*)\]\((.*)\)/;
            const result = regex.exec(concated);
            if (result) {
                return {
                    processed: '',
                    plainText: (plainText + char).replace(
                        /\$\[(.*)\]\((.*)\)$/,
                        `${placeholderChar}#${result[1]}${placeholderChar}`
                    ),
                };
            }
            return {processed: concated, plainText: plainText + char};
        },
        {processed: '', plainText: ''}
    );

    return plainText;
};

export const serializeMarkupedText = (parts: Array<string | Mention>) => {
    return parts.map(p => (typeof p === 'string' ? p : mention2Text(p))).join(' ');
};

export const getTextBeforeLastSymbol = (text: string, symbol: string) => {
    const lastHashSymbolIndex = text.lastIndexOf(symbol);
    if (lastHashSymbolIndex === -1) {
        return text;
    }

    return text.substring(0, lastHashSymbolIndex);
};

export const getTextAfterLastSymbol = (text: string, symbol: string) => {
    const lastSymbolIndex = text.lastIndexOf(symbol);
    if (lastSymbolIndex === -1) {
        return text;
    }

    return text.substring(lastSymbolIndex + symbol.length);
};

export const insertMentionTag = (mention: Mention) => (previousSerializedText: string) => {
    const beforeText = getTextBeforeLastSymbol(previousSerializedText, '#');
    return beforeText
        ? `${beforeText}${beforeText.endsWith(' ') ? '' : ' '}${mention2Text(mention)} `
        : `${mention2Text(mention)} `;
};

const keycodes = ['ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight'] as const;
type DirectionKeyCode = typeof keycodes[number];
export const keyCodeTriggerAdjust = (key: string): key is DirectionKeyCode => {
    return keycodes.includes(key as DirectionKeyCode);
};

export const adjustTextAreaCursor = (textarea: HTMLTextAreaElement, key: DirectionKeyCode) => {
    const value = textarea.value;
    const moveUp = ['ArrowLeft', 'ArrowUp'].includes(key);
    const forwardText = moveUp
        ? value.substring(0, textarea.selectionStart - 1)
        : value.substring(textarea.selectionStart + 1, value.length);
    // eslint-disable-next-line @typescript-eslint/prefer-regexp-exec
    const hiddenSymbolCount = forwardText.match(new RegExp(placeholderChar, 'g'))?.length || 0;
    const isOdd = hiddenSymbolCount % 2 === 1;
    if (isOdd) {
        if (moveUp) {
            const index = forwardText.lastIndexOf(placeholderChar) - 1;
            textarea.selectionStart = index === -1 ? 0 : index;
        }
        else {
            const beforeTextLength = value.substring(0, textarea.selectionStart + 1).length;
            const index = forwardText.indexOf(placeholderChar) + 1;
            textarea.selectionStart = beforeTextLength + index;
        }
    }
};

export const splitMarkupedMentionText2Chunk = (markupedText: string) => {
    const {chunks, processed} = Array.from(markupedText).reduce<{processed: string, chunks: Array<string | Mention>}>(
        ({processed, chunks}, char) => {
            const concated = processed + char;
            const regex = new RegExp(`${placeholderChar}(.*)${placeholderChar}$`);
            const result = regex.exec(concated);
            if (result) {
                chunks.push(concated.replace(result[0], ''));
                chunks.push({id: '', display: result[1]});
                return {
                    processed: '',
                    chunks,
                };
            }
            return {processed: concated, chunks};
        },
        {processed: '', chunks: []}
    );

    if (processed) {
        return [...chunks, processed];
    }

    return chunks;
};

export const getCorrectSelectionStart = (text: string, selectionStart: number) => {
    const chunks = splitMentionText(text);
    interface StringChunk {
        type: 'text';
        start: number;
        end: number;
        content: string;
    }
    interface MentionChunk {
        type: 'mention';
        start: number;
        end: number;
        content: Mention;
    }
    const chunksWithRange = chunks.reduce<Array<StringChunk | MentionChunk>>(
        (result, chunk) => {
            const last = result[result.length - 1];
            const previousEnd = last?.end || 0;
            if (typeof chunk === 'string') {
                result.push({
                    type: 'text',
                    start: previousEnd,
                    end: previousEnd + chunk.length,
                    content: chunk,
                });
            }
            else {
                result.push({
                    type: 'mention',
                    start: previousEnd,
                    end: previousEnd + chunk.display.length + 3,
                    content: chunk,
                });
            }
            return result;
        },
        []
    );

    const matched = chunksWithRange.find(({start, end}) => start <= selectionStart && selectionStart < end);
    const index = chunksWithRange.indexOf(matched!);
    return chunksWithRange.slice(0, index === -1 ? chunksWithRange.length : index + 1).reduce(
        (result, chunk, i, origin) => {
            const previousLabelLength = origin.slice(0, i).reduce(
                (result, chunk) => {
                    return result + (chunk.type === 'text' ? chunk.content.length : chunk.content.display.length + 3);
                },
                0
            );

            const valueLength = chunk.type === 'text' ? chunk.content.length : mention2Text(chunk.content).length;
            const contentLength = i === origin.length - 1
                ? result + (selectionStart - previousLabelLength)
                : result + valueLength;
            return contentLength;
        },
        0
    );
};

export const extractFilename = (filePath: string) => filePath.split(/(\\|\/)/).pop() ?? '';
