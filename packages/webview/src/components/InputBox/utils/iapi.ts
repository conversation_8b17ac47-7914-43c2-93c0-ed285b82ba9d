import {EventMessage} from '@shared/protocols';
import {SlashType} from '@shared/constants';
import {messageHandler} from '@/utils/messageHandler';

interface Knowledge {
    id: string;
    type: string;
    display?: string;
}

/**
 * 使用 #API 知识时，需要进行 slash 修正和打点
 * slash 修正的条件是：使用了接口知识(即：type：API) or 用户在 #API 时自行输入了需求(即：mention 中的 id 以 API_CUSTOM_INPUT 为前缀开头)
 * @param knowledges 所有的知识项
 * @param value 输入框文本
 */
export const matchedRevertSlash2IAPI = (knowledges: Knowledge[] = [], value: string) => {
    const apiKnowledgeList = knowledges.filter(item => item.type === 'API' || item.id?.startsWith('API_CUSTOM_INPUT'));
    let slash = SlashType.IAPI;
    if (apiKnowledgeList.length > 0) {
        // 对 api 打点记录
        apiKnowledgeList.forEach(item => {
            const isApiDoc = item.id?.startsWith('API_CUSTOM_INPUT');
            const params = {
                type: isApiDoc ? 'doc' : 'api',
                content: item?.display || '',
            };

            messageHandler.send(EventMessage.TrackClickSearchedAPIEvent, params);
        });

        // 对 query 进行打点
        const replacedQuery = value.replace(/\$\[(.*)\]\((.*)\)/, '').trimStart();
        if (replacedQuery) {
            const params = {
                type: 'query',
                content: replacedQuery,
            };

            messageHandler.send(EventMessage.TrackClickSearchedAPIEvent, params);
        }

        // 有 #API 知识，且没有网页、网页检索、知识集三种知识走「API生成代码」指令，其余走AutoWork 的「ASK」
        if (knowledges.some(item => ['URL', 'WEB', 'SYSTEM'].includes(item.type))) {
            slash = SlashType.ASK_V2;
        }
    }

    return {
        isNeedRevertSlash2IAPI: apiKnowledgeList.length > 0,
        newSlash: slash,
        filteredKnowledges: knowledges.filter(item => !item.id?.startsWith('API_CUSTOM_INPUT')),
    };
};
