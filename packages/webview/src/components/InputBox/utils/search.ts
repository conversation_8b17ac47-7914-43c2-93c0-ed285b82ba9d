import {range, uniq} from 'lodash';

export const computeFuzzySearch = (value: string, currentAgent?: string, currentCommand?: string) => {
    // 下面这个等同于判断，是否第一个字符是唤醒词，且有关键词
    const keyword = value.slice(1);
    if (value === '' && currentAgent) {
        return {keyword: value, type: 'smart'} as const;
    }
    if (value.includes('#')) {
        return null;
    }
    else if (value.startsWith('@')) {
        if (!currentAgent) {
            return {keyword, type: 'agent'} as const;
        }
    }
    else if ((value.startsWith('/') || value.startsWith('、')) && !currentCommand) {
        return {keyword, type: 'command'} as const;
    }

    return {keyword: value, type: 'smart'} as const;
};
/**
 * 把起止位置的序列转成位置列表
 * @example fuseMatchesToPositions([[0, 3], [5, 7]]) => [0, 1, 2, 3, 5, 6, 7]
 */
export function fuseMatchesToPositions(matches: ReadonlyArray<[start: number, end: number]> | undefined): number[] {
    if (matches === undefined) {
        return [];
    }
    let result: number[] = [];
    for (const match of matches) {
        result = [...result, ...range(match[0], match[1] + 1)];
    }
    return uniq(result);
}
