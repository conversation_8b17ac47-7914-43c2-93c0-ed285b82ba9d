import {FuseResultMatch} from 'fuse.js';
import {flatMap, filter, map, flow, min} from 'lodash';

/* ======== 工具函数 ======== */
export const getStringCharLength = (str?: string) => {
    if (!str) {
        return 0;
    }
    let len = 0;
    for (let i = 0; i < str.length; i++) {
        const c = str.charCodeAt(i);
        // 先特殊处理下 m、i、l 这几个字符，感觉后面得根据每个字符给一个宽度
        if (c === 0x006d) {
            len += 1.7;
        }
        else if (c === 0x0069 || c === 0x006c) {
            len += 0.7;
        }
        else if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
            len++;
        }
        else {
            len += 2;
        }
    }
    return len;
};

/**
 * TypeScript 工具函数，自带类型守卫，可用于 filter 排除空值
 */
function nonNullable<T>(v: T): v is NonNullable<T> {
    return v !== undefined && v !== null;
}

/* ======== 常用匹配策略 ======== */
/**
 * 删除匹配位置后的字符串
 * @example
 * const fuze = new Fuse({name: 'asdf'}, {keys: ['name']})
 * const matches = fuze.search('was')[0].matches // 匹配到了 'as'
 * deleteTextAfterMatch('was', matches) => 'w'
 * deleteTextAfterMatch('wasas', matches) => 'was' // 有多个匹配以后输入的为准
 */
export function deleteTextAfterMatch(text: string, match: readonly FuseResultMatch[]): string {
    const firstMatchIndex = flow([
        match =>
            flatMap(match, matches => {
                const str = matches?.indices.map(([start, end]) => matches.value?.substring(start, end + 1));
                return str;
            }),
        match => filter(match, nonNullable),
        match =>
            map(match, str => {
                const index = text.toLowerCase().lastIndexOf(str.toLowerCase());
                return index >= 0 ? index : text.length;
            }),
        match => min(match),
    ])(match);
    return text.slice(0, firstMatchIndex);
}

/**
 * 当完全匹配的时候删除，不完全匹配的时候全部保留
 */
export function deleteTextAfterMatchIfFull(text: string, match: readonly FuseResultMatch[]): string {
    try {
        const allMatchString = match
            .flatMap(m => m.indices.map(([start, end]) => m.value?.slice(start, end + 1)))
            .filter(nonNullable);
        const strSet = new Set(allMatchString.flatMap(s => s.toLowerCase().split('')));

        if (text.split('').every(chr => strSet.has(chr.toLowerCase()))) {
            return '';
        }
        return text;
    }
    catch (err) {
        console.error(err);
        return '';
    }
}
