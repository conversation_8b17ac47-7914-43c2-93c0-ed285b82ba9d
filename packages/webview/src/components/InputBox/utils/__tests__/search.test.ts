import {computeFuzzySearch} from '../search';

describe('computeFuzzySearch', () => {
    it('输入@或/或、这三个唤醒词时，只要后面没跟关键词就不搜索', () => {
        expect(computeFuzzySearch('@')).toBe(null);
        expect(computeFuzzySearch('/')).toBe(null);
        expect(computeFuzzySearch('、')).toBe(null);
    });
    it('输入@后面有关键词，搜索agent', () => {
        expect(computeFuzzySearch('@git', undefined)).toEqual({type: 'agent', keyword: 'git'});
        expect(computeFuzzySearch('@', 'git')).toBe(null);
    });
    it('输入/和、后面有关键词，搜索command', () => {
        expect(computeFuzzySearch('/unitTest', undefined, undefined)).toEqual({type: 'command', keyword: 'unitTest'});
        expect(computeFuzzySearch('/', undefined, 'unitTest')).toBe(null);
        expect(computeFuzzySearch('、unitTest', undefined, undefined)).toEqual({type: 'command', keyword: 'unitTest'});
        expect(computeFuzzySearch('、', undefined, 'unitTest')).toBe(null);
    });
    it('有agent和command的情况下不触发搜索', () => {
        expect(computeFuzzySearch('/', 'comate', 'unitTest')).toBe(null);
        expect(computeFuzzySearch('@', 'comate', 'unitTest')).toBe(null);
        expect(computeFuzzySearch('abcd', undefined, undefined)).toBe(null);
    });
});
