import {
    linkKnowledgeId2Uuid,
    linkKnowledgeId2Url,
} from '../is';

describe('linkKnowledgeId2Uuid', () => {
    it('测试从链接知识里提取uuid', () => {
        const t = linkKnowledgeId2Uuid;
        const iapiUuid = '123456';
        const uuid = 'e5510d81-a724-49cd-9b1a-cb4e13a27c28';
        const url = 'https://www.baidu.com';
        expect(t(`link:${uuid}:${url}`)).toEqual(uuid);
        expect(t(`link:IAPI-${iapiUuid}:${url}`)).toEqual('IAPI-' + iapiUuid);
    });
});

describe('测试从链接知识里提取url', () => {
    it('测试从带mention的文本中提取出来mention对象', () => {
        const t = linkKnowledgeId2Url;
        const iapiUuid = '123456';
        const uuid = 'e5510d81-a724-49cd-9b1a-cb4e13a27c28';
        const url = 'https://www.baidu.com';
        expect(t(`link:${uuid}:${url}`)).toEqual(url);
        expect(t(`link:IAPI-${iapiUuid}:${url}`)).toEqual(url);
    });
});
