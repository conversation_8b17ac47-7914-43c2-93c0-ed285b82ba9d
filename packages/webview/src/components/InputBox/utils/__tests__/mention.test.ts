import {
    extractMentionsFromMarkupedText,
    parseMarkupedText2PlainText,
    serializeMarkupedText,
    getTextBeforeLastSymbol,
    insertMentionTag,
    placeholderChar,
    getCorrectSelectionStart,
} from '../mention';

describe('extractMentionsFromMarkupedText', () => {
    it('测试从带mention的文本中提取出来mention对象', () => {
        const t = extractMentionsFromMarkupedText;
        expect(t('解析$[当前文件](currentFile)的内容')).toEqual([{id: 'currentFile', display: '当前文件'}]);
    });
    it('测试从带mention的文本中提取出来mention对象', () => {
        const t = extractMentionsFromMarkupedText;
        expect(t('解析$[当前文件](currentFile)的内容'.repeat(2))).toEqual([{id: 'currentFile', display: '当前文件'}]);
    });
});

describe('parseMarkupedText2PlainText', () => {
    it('测试把带mention的文本格式化成普通文本', () => {
        const fullText =
            // eslint-disable-next-line max-len
            '$[当前文件](currentFile) $[代码库](repo) $[AutoComplete](packages/designs/src/components/AutoComplete) $[index.tsx](packages/designs/src/components/AutoComplete/index.tsx) $[BOS官方开发文档](51b85064-7121-4a1f-9ab1-a2cda282f21d)';
        const wrapper = (i: string) => `${placeholderChar}${i}${placeholderChar}`;
        expect(parseMarkupedText2PlainText(fullText)).toBe([
            wrapper('#当前文件'),
            wrapper('#代码库'),
            wrapper('#AutoComplete'),
            wrapper('#index.tsx'),
            wrapper('#BOS官方开发文档'),
        ]
            .join(' '));
    });
});

describe('serializeMarkupedText', () => {
    it('测试从一个数据结构转换成带mention的文本', () => {
        const t = serializeMarkupedText;
        expect(t(['解析', {id: 'currentFile', display: '当前文件'}, '的内容'])).toBe(
            '解析 $[当前文件](currentFile) 的内容'
        );
    });
});

describe('getTextBeforeLastSymbol', () => {
    it('测试获取某个字符前面的文本', () => {
        expect(getTextBeforeLastSymbol('#', '#')).toBe('');
        expect(getTextBeforeLastSymbol('解析#的内容', '#')).toBe('解析');
        expect(getTextBeforeLastSymbol('解析文件的内容', '#')).toBe('解析文件的内容');
    });
});

describe('insertMentionTag', () => {
    it('测试在一段文本中插入一个mention', () => {
        expect(insertMentionTag({id: 'currentFile', display: '当前文件'})('解析#当')).toBe(
            '解析 $[当前文件](currentFile) '
        );
    });
});

describe('getCorrectSelectionStart', () => {
    it('无mention时获取正确的selectionStart', () => {
        const text =
            '$[查询服务详情](link:API-235d4188-cc21-40de-aa67-43364caeb7d9:https://iapi.baidu-int.com/apidoc/project-355213/api-4031241) 生成接口';
        expect(getCorrectSelectionStart(text, 12)).toBe(120);
    });

    it('有mention获取正确的selectionStart', () => {
        const text = serializeMarkupedText(['解析', {id: 'currentFile', display: '当前文件'}, '的内容']);
        expect(getCorrectSelectionStart(text, 1)).toBe(1);
        expect(getCorrectSelectionStart(text, 11)).toBe(24);
    });

    it('有mention获取正确的selectionStart', () => {
        const text = serializeMarkupedText(
            // eslint-disable-next-line
            [
                {id: 'repo', display: '当前代码库'},
                '123',
                {id: '30072414-8300-4454-a3c0-7844d04dfa89', display: 'i3'},
                '123',
            ]
        );
        expect(getCorrectSelectionStart(text, 21)).toBe(65);
    });

    it('无mention时获取正确的selectionStart', () => {
        const text = '解析当前文件的内容';
        expect(getCorrectSelectionStart(text, 7)).toBe(7);
    });
});
