import {KnowledgeList, Suggestion} from '@shared/protocols';
import {ComatePlusAgent, suggestForGDP, suggestForPaddle} from './queryVisibilitySelector';

export type SubmitHandler = (
    text: string,
    agent?: string,
    slash?: string,
    knowledgeList?: KnowledgeList[],
    rawMessage?: string
) => void;

/**
 * 从 webview 抽离出来的函数，用于生成 InputBox 上方的建议
 * @param {ComatePlusAgent} agent 三方插件的 agent
 * @param {string} capability 三方插件的能力
 * @param {function} onSubmit 点击采纳建议的回调函数
 * @returns {Suggestion[]} 构建好的建议列表
 */
export function constructSuggestionList(
    agent: ComatePlusAgent,
    capability: string,
    onSubmit?: SubmitHandler
): Suggestion[] {
    const suggestions: Suggestion[] = [];

    switch (agent) {
        // 默认走智能问答
        case ComatePlusAgent.PADDLE:
            suggestions.push(suggestForPaddle(agent, capability, onSubmit) as Suggestion);
            break;
        // 自动能力选择走`智能问答`能力 文案采纳走`代码生成`能力
        case ComatePlusAgent.GDP:
            suggestions.push(suggestForGDP(agent, capability, onSubmit) as Suggestion);
            break;
        // 冗余设计 理论上不会走到这里
        default:
            break;
    }

    return suggestions;
}
