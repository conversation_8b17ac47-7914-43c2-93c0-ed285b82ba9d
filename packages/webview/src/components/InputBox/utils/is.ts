import {knowledgeScopes} from '@shared/protocols';
import i18n from 'i18next';
import {FORM_ERROR_DESCRIPTION_FORMAT} from '@/i18n/constants';

export const parseUrlIfPossible = (input: string) => {
    try {
        const url = input.split(' ')[0];
        if (/http(s?):\/\//.test(url)) {
            return url;
        }

        throw new Error(i18n.t(FORM_ERROR_DESCRIPTION_FORMAT, {property: 'URL'}));
    }
    catch (ex) {
        return '';
    }
};

export const isLinkKnowledge = (input: string) => input.startsWith('link:');

const uuidV4Regex = /(^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}):/i;

export const linkKnowledgeId2Uuid = (input: string) => {
    const maybeUuid = input.replace(/^link:/, '');
    if (maybeUuid.startsWith('API-')) {
        const uuid = uuidV4Regex.exec(maybeUuid.replace('API-', ''))?.[1];
        if (uuid) {
            return uuid.replace('API-', '');
        }
        return '';
    }
    if (maybeUuid.startsWith('API_PROJECT-')) {
        const uuid = /^(API_PROJECT-\d+):/.exec(maybeUuid)?.[1];
        if (uuid) {
            return uuid.replace('API_PROJECT-', '');
        }
        return '';
    }
    // 如果用正则的话，随着scope越来越多，可读性会越来越差
    const scope = knowledgeScopes.find(prefix => maybeUuid.startsWith(prefix)) || '';
    const uuid = uuidV4Regex.exec(maybeUuid.replace(scope || '', ''))?.[1];
    return `${scope}${uuid}`;
};

export const linkKnowledgeId2Url = (input: string) => {
    const maybeUuid = input.replace(/^link:/, '');
    if (maybeUuid.startsWith('API_PROJECT-')) {
        const uuid = /^(API_PROJECT-\d+):/.exec(maybeUuid)?.[1];
        if (uuid) {
            return maybeUuid.replace(/^(API_PROJECT-\d+):/, '');
        }
        return '';
    }
    const scope = knowledgeScopes.find(prefix => maybeUuid.startsWith(prefix));
    return maybeUuid.replace(scope || '', '').replace(uuidV4Regex, '');
};

/**
 * 校验是否是合法的iAPI url格式
 */
export const validateIAPIURLFormat = (url: string) => {
    if (!url) {
        throw new Error(i18n.t(FORM_ERROR_DESCRIPTION_FORMAT, {property: 'URL'}));
    }

    const isTest = $features.ENVIRONMENT === 'test';
    const matched = [
        new RegExp(`^https://iapi${isTest ? '-test' : ''}.baidu-int.com/web/project/[0-9]+$`),
        new RegExp(`^https://iapi${isTest ? '-test' : ''}.baidu-int.com/web/project/[0-9]+/apis/(.*)+$`),
    ]
        .some(regex => regex.test(url));

    if (!matched) {
        throw new Error(i18n.t(FORM_ERROR_DESCRIPTION_FORMAT, {property: 'iAPI URL'}));
    }
};

// 当用户处于中文输入法（合成输入）时，按回车不希望触发我们的发送事件，所以需要在这个地方额外判断一下
export const isEnterKeyPressed = (event: any) => {
    return !event.isComposing && event.key === 'Enter';
};

export const isMainKeyPressed = (event: any) => {
    return event.metaKey || event.ctrlKey;
};

export const isShiftKeyPressed = (event: any) => {
    return event.shiftKey;
};

export const linkKnowledgeId2Type = (input: string) => {
    if (!input.startsWith('link:')) {
        return undefined;
    }
    const maybeUuid = input.replace(/^link:/, '');
    const scope = knowledgeScopes.find(prefix => maybeUuid.startsWith(prefix));
    return scope ? scope.replace('-', '') : 'URL';
};
