.comate-chat-extended-area {
    position: relative;
    height: 24px;
    padding: 0 8px;
    margin-top: 8px;
}

.comate-chat-extended-area .button-group {
    position: absolute;
    left: 8px;
    top: 0;
    display: flex;
    align-items: center;
    height: 100%;
    max-width: calc(100% - 16px);
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    z-index: 1;
}

.comate-chat-extended-area .floating-controls {
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    height: 100%;
    margin-right: 8px;
    z-index: 2;
    border-radius: 4px;
    /* background: linear-gradient(
        to right,
        var(--comate-tooltip-background) 0%,
        var(--comate-tooltip-background) 20px,
        #2098F333 100%
    ); */
}

.comate-chat-extended-area .extended-btn {
    height: 22px;
    padding: 0 4px;
    border-radius: 4px;
    color: var(--comate-disabled-iconColor);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: padding 0.3s ease;
}

.comate-chat-extended-area .extended-btn .symbol,
.comate-chat-extended-area .extended-btn .text {
    display: inline-block;
    transition: max-width 0.3s ease, opacity 0.3s ease, margin 0.3s ease;
}

.comate-chat-extended-area .extended-btn .text {
    max-width: 100px;
    overflow: hidden;
    margin-left: 2px;
}

.comate-chat-extended-area .bar {
    display: inline-block;
    margin: 0 4px;
    height: 10px;
    width: 1px;
    background: var(--comate-disabled-iconColor);
    opacity: 0.5;
    flex-shrink: 0;
}

.comate-chat-extended-area .extended-btn:hover,
.comate-chat-extended-area .extended-btn.active {
    background-color: var(--comate-optionButton-hoverBackground);
    color: var(--comate-link-color);
}

.comate-chat-extended-area .comate-intent {
    margin-right: 8px;
    color: var(--comate-disabled-iconColor);
}

.comate-intent .comate-intent-icon svg {
    fill: currentColor;
}

.comate-intent.active .comate-intent-label {
    color: transparent;
    background-image: linear-gradient(-135deg, #03F8E7 0%, #6391F9 100%);
    background-clip: text;
    -webkit-background-clip: text;
}

.comate-intent.active .comate-intent-icon svg {
    fill: url(#intent-gradient);
}

.comate-chat-extended-area .button-group::-webkit-scrollbar {
    display: none;
}

.comate-chat-extended-area .button-group {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

@media screen and (max-width: 200px) {
    .comate-chat-extended-area .extended-btn .text {
        max-width: 0;
        opacity: 0;
        margin-left: 0;
    }
    .comate-chat-extended-area .extended-btn .symbol {
        min-width: 15px;
    }
    .comate-chat-extended-area .extended-btn {
        padding: 0 5px;
    }
}
