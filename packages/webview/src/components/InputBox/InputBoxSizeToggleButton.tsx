import fullscreenCollapseIcon from '@/assets/fullscreen-collapse.svg';
import fullscreenExpandIcon from '@/assets/fullsreen-expand.svg';
import {useInputBoxSizeToggle} from '@/hooks/useInputBoxSizeToggle';
import ToolbarButton from '../ToolbarButton';

export default function InputBoxSizeToggleButton() {
    const [expand, toggleExpand] = useInputBoxSizeToggle();

    return (
        <ToolbarButton
            aria-hidden="true"
            title={expand ? '最小化' : '最大化'}
            icon={expand ? fullscreenCollapseIcon : fullscreenExpandIcon}
            onClick={toggleExpand}
        />
    );
}
