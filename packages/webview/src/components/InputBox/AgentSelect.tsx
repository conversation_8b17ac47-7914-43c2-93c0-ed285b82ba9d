/* bca-disable */
import {useCallback} from 'preact/hooks';
import {BuiltinAgent} from '@shared/protocols';
import classNames from 'classnames';
import {useAgent} from '@/hooks/useAgent';
import comateLogo from '@/assets/comate.png';
import rightIcon from '@/assets/right.svg';
import {useChatInput} from './hooks/useChatInput';
import './AgentSelect.css';

export default function AgentSelect({activeItem}) {
    const {selectedAgentId} = useChatInput();
    const agent = useAgent(selectedAgentId || BuiltinAgent.Comate)!;

    const {activePopoverType, onPopoverTypeChange} = useChatInput();
    const toggle = useCallback(
        () => {
            if (activePopoverType === 'agent') {
                onPopoverTypeChange(undefined);
            }
            else {
                onPopoverTypeChange('agent', {keepInputText: true});
            }
        },
        [activePopoverType, onPopoverTypeChange]
    );


    return (
        <div
            className="agent-select"
            onClick={toggle}
            role="button"
            aria-label={`切换插件按钮，当前插件为${agent.displayName}, 点击可切换插件`}
            aria-activedescendant={`input_popover_${activeItem}`}
            tabIndex={0}
            style={{outline: 'none'}}
        >
            {agent.icon
                ? <img className="w-4" src={agent.icon} aria-hidden="true" />
                : <img className="w-4" src={comateLogo} aria-hidden="true" />}
            <div className="truncate" aria-hidden="true">
                {agent.displayName}
            </div>

            <div
                className={classNames(
                    'w-2.5 h-2.5 flex-shrink-0',
                    {
                        '-rotate-90': activePopoverType === 'agent',
                        'rotate-90': activePopoverType !== 'agent',
                    }
                )}
                dangerouslySetInnerHTML={{__html: rightIcon}}
                data-role="button"
                aria-hidden="true"
            />
        </div>
    );
}
