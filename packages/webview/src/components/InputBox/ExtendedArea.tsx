import cx from 'classnames';
import {useCallback, useEffect, useMemo, useState} from 'preact/hooks';
import {useTranslation} from 'react-i18next';
import {EventMessage} from '@shared/protocols';
import flightIcon from '@/assets/flight.svg';
import cancelIcon from '@/assets/cancel.svg';
import {messageHandler} from '@/utils/messageHandler';
import {isVSCode} from '@/config';
import {isInternal} from '@/utils/features';
import {useInputBoxSizeToggle} from '@/hooks/useInputBoxSizeToggle';
import ToolbarButton from '../ToolbarButton';
import {PopConfirm} from '../PopConfirm';
import {useExtensionConfig} from '../../hooks/useExtensionConfig';
import {useChatConfig} from '../ChatConfigProvider';
import {useChatInput} from './hooks/useChatInput';
import {ExtensionPopoverType} from './utils/popover';
import CreatePromptTemplateButton from './CreatePromptTemplateButton';
import InputBoxSizeToggleButton from './InputBoxSizeToggleButton';
import './ExtendedArea.css';

interface Props {
    showStopGenerateBtn?: boolean;
    disabled?: boolean;
    onSubmit: () => any;
    activeItem?: string;
}

export const classnames = {
    btn: 'extended-btn',
};

export function ExtendedArea({disabled, onSubmit, activeItem, showStopGenerateBtn}: Props) {
    const {t} = useTranslation();
    const {activePopoverType, onPopoverTypeChange} = useChatInput();
    const {config: {enableIntentRecognition}} = useExtensionConfig();

    const handlePopoverTrigger = useCallback(
        (popoverType: ExtensionPopoverType) => {
            if (activePopoverType === popoverType) {
                onPopoverTypeChange(undefined);
            }
            else {
                onPopoverTypeChange(popoverType, {keepInputText: true});
            }
        },
        [activePopoverType, onPopoverTypeChange]
    );

    const popoverTrigger = useMemo(
        () => ({
            agent: () => handlePopoverTrigger('agent'),
            command: () => handlePopoverTrigger('command'),
            knowledgeType: () => handlePopoverTrigger('pound'),
        }),
        [handlePopoverTrigger]
    );

    const {currentMessageId} = useChatConfig();
    const handleCancel = useCallback(
        () => {
            if (currentMessageId) {
                messageHandler.send(EventMessage.ResponseCancelEvent, currentMessageId);
            }
        },
        [currentMessageId]
    );

    const [confirmVisible, setConfirmVisible] = useState(false);
    useEffect(
        () => {
            messageHandler.listen(
                EventMessage.PromptTemplateSaveEvent,
                () => {
                    setConfirmVisible(true);
                }
            );
        },
        []
    );

    const [expand, toggleExpand] = useInputBoxSizeToggle();
    return (
        <div className="comate-chat-extended-area">
            <div
                className="button-group"
            >
                <PopConfirm
                    visible={confirmVisible}
                    setVisible={setConfirmVisible}
                    type="success"
                    title="指令保存成功"
                    content="可点击 /指令 按钮或直接输入 “/” 使用哦～"
                >
                    <button
                        className={cx(classnames.btn, 'font-pingfang', {active: activePopoverType === 'command'})}
                        onClick={popoverTrigger.command}
                        aria-haspopup="listbox"
                        aria-owns="input_popover"
                        aria-autocomplete="list"
                        aria-controls="input_popover"
                        {...(activeItem ? {'aria-activedescendant': `input_popover_${activeItem}`} : {})}
                    >
                        <span className="symbol">/</span>
                        <span className="text">{t('common.command.name')}</span>
                    </button>
                </PopConfirm>
                {isVSCode && isInternal && <CreatePromptTemplateButton />}
            </div>
            <div className="floating-controls">
                {showStopGenerateBtn
                    ? <ToolbarButton label={'停止生成 Ctrl+C'} icon={cancelIcon} onClick={handleCancel} ariaTitle="停止生成" />
                    : (
                        <>
                            <InputBoxSizeToggleButton />
                            <span
                                className="w-[1px] h-[15px] bg-[var(--comate-descriptionForeground)] mx-2 opacity-70"
                            />
                            <ToolbarButton
                                className={disabled ? '' : 'text-[var(--comate-link-color)]'}
                                icon={flightIcon}
                                onClick={onSubmit}
                                disabled={disabled}
                                ariaTitle="发送"
                            />
                        </>
                    )}
            </div>
        </div>
    );
}
