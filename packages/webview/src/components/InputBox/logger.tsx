import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';

export enum LogCategory {
    showChatPanel = 'showChatPanel',
    queryVisibility = 'queryVisibility',
    ChatVisibilityChange = 'ChatVisibilityChange',
    onDidChangeState = 'ondidchangestate',
    getEngineWebviewInitDataFailed = 'GetEngineWebviewInitDataFailed',
    comatePair = 'comate-pair',
    selectPluginOrCommand = 'selectPluginOrCommand',
    hoverPluginOrCommand = 'hoverPluginOrCommand',
    cancelGenerate = 'cancelGenerate',
    AutoDebug = 'autoDebug',
    TrackUUIDGenerateFailed = 'TrackUUIDGenerateFailed',
    Diff = 'diff',
}
export interface LogUploaderEvent {
    category: LogCategory;
    label?: string;
    content?: any;
    action?: string;
    source?: string;
}
export function log(info: LogUploaderEvent) {
    messageHandler.send(EventMessage.UploadUserActionLog, info);
}
/**
 * 关于用户选中插件的情况统计
 * https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/DBLKIO3bMt/-TA1LHspK8tAd9#anchor-20a24980-10e0-11ef-b1b1-ff42cceb65bd
 */
export function logPluginSelect(content: {
    /** 选中选项的id */
    targetId: string;
    /** 用户此时输入的内容 */
    inputText: string;
    /** 弹窗种类 */
    activePopoverType: string;
    /** 当前选中的插件 */
    currentAgent?: string;
    /** 剔除选中内容后剩余的文本 */
    restText?: string;
}) {
    return log({
        category: LogCategory.selectPluginOrCommand,
        content: content,
    });
}

export function logPluginHover(content: unknown) {
    return log({
        category: LogCategory.hoverPluginOrCommand,
        content,
    });
}

export interface LogCancelGenerateContent {
    hotkey: boolean;
}
export function logCancelGenerate(content: LogCancelGenerateContent) {
    return log({
        category: LogCategory.cancelGenerate,
        content,
    });
}
