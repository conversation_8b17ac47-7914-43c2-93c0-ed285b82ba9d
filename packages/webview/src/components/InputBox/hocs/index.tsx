import {ComponentType} from 'preact';
import {isJetbrains, isVSCode} from '@/config';

/**
 * 控制组件是否在 vscode 和 jetbrains 中显示
 * @params {Object} opts 配置项
 * @params {Boolean} opts.hiddenInVSCode 是否在 vscode 中隐藏
 * @params {Boolean} opts.hiddenInJetbrains 是否在 jetbrains 中隐藏
 */
export const withPlatformControl = <T extends object>(opts: {hiddenInVSCode?: boolean, hiddenInJetbrains: boolean}) => {
    return (ComponentIn: ComponentType<T>) => (props: T) => {
        if (isJetbrains && opts.hiddenInJetbrains) {
            return null;
        }

        if (isVSCode && opts.hiddenInVSCode) {
            return null;
        }

        return <ComponentIn {...props} />;
    };
};
