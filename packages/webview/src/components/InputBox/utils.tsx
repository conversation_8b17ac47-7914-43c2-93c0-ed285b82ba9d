import {Dictionary, uniqBy} from 'lodash';
import {
    AgentListWithId,
    BuiltinAgent,
    CommandListWithId,
    ContextType,
    KnowledgeList,
    PromptTemplateList,
} from '@shared/protocols';
import {FeatureName, isFeatureVisible, showThirdPartyCommandsVisible} from '@shared/utils/features';
import {E2EBOT_AGENT} from '@shared/agents';
import e2eBotIcon from '@/assets/agent/e2eBot.png';
import {isVSCode} from '@/config';
import {isInternal} from '@/utils/features';
import {BUILTIN_AGENT, BUILTIN_COMMANDS, PROMPT_TEMPLATE_AGENT} from './constants';
import {extractMentionsFromMarkupedText, Mention} from './utils/mention';
import {linkKnowledgeId2Type, linkKnowledgeId2Url, linkKnowledgeId2Uuid} from './utils/is';

export async function buildAgentList(agentListWithId: AgentListWithId[] = [], builtInPlugins?: string[]) {
    const omitAutoWork = !isFeatureVisible(FeatureName.AGENT_BOT);
    const agents: any[] = [
        BUILTIN_AGENT,
        omitAutoWork ? undefined : {...E2EBOT_AGENT, icon: e2eBotIcon},
        (isVSCode && isInternal) ? PROMPT_TEMPLATE_AGENT : undefined,
        ...agentListWithId,
    ]
        .filter(item => item !== undefined);

    const comate = agents.find(({name}) => name === BuiltinAgent.Comate);
    // 找到需要builtIn的三方插件 关联到一方 并打上标记
    if (comate && builtInPlugins?.length) {
        for (const thirdPartyAgent of agents) {
            if (builtInPlugins.includes(thirdPartyAgent.name)) {
                comate.capabilities = comate.capabilities?.concat(
                    thirdPartyAgent.capabilities?.filter(capability => capability?.type === 'Skill')
                );
                thirdPartyAgent.behaveAsBuiltIn = true;
            }
        }
    }

    return uniqBy(agents, 'id');
}

// eslint-disable-next-line complexity
export function buildCommandList(
    thirdPartyCommands?: CommandListWithId[],
    builtInPlugins?: string[],
    originalCommands?: CommandListWithId[],
    promptTemplateList?: PromptTemplateList[]
) {
    const showThirdPartyCommands = showThirdPartyCommandsVisible();

    if (thirdPartyCommands?.length) {
        for (const thirdPartyCommand of thirdPartyCommands) {
            // 排除需要builtIn的三方插件 并关联到一方
            if (builtInPlugins?.includes(thirdPartyCommand.owner.name)) {
                thirdPartyCommand.originOwner = thirdPartyCommand.owner;
                thirdPartyCommand.owner = BUILTIN_AGENT;

                // 保持插入在后面 但在 /help 前
                // eslint-disable-next-line no-unused-expressions
                originalCommands
                    ? originalCommands.splice(originalCommands.length - 1, 0, thirdPartyCommand)
                    : BUILTIN_COMMANDS.splice(BUILTIN_COMMANDS.length - 1, 0, thirdPartyCommand);
            }
        }
    }

    const result: CommandListWithId[] = [];

    if (promptTemplateList?.length) {
        const promptTemplateCommandList = promptTemplateList.map((promptTemplate: PromptTemplateList) => {
            return {
                id: `${promptTemplate.uuid}@${PROMPT_TEMPLATE_AGENT.id}`,
                name: promptTemplate.uuid,
                displayName: promptTemplate.name,
                description: promptTemplate.description,
                owner: PROMPT_TEMPLATE_AGENT,
            } as CommandListWithId;
        });
        result.push(...promptTemplateCommandList);
    }

    result.push(...(originalCommands || BUILTIN_COMMANDS));

    if (showThirdPartyCommands) {
        result.push(...(thirdPartyCommands?.length ? thirdPartyCommands : []));
    }

    return uniqBy(result, 'id');
}

export const partitionByType = (mentions: Mention[], existKnowledgeKeyById: Dictionary<any>) => {
    const linkKnowledge: Mention[] = [];
    const otherKnowledge: Mention[] = [];

    for (const mention of mentions) {
        if (mention.id.startsWith('link:')) {
            linkKnowledge.push(mention);
        }
        else {
            otherKnowledge.push(mention);
        }
    }
    const categorizeKnowledge = (item: Mention) => {
        const {id, display} = item;
        if (id.startsWith('knowledge:')) {
            const [knowledgeType, ...rest] = id.replace('knowledge:', '').split('/');
            return {id: rest.join('/'), name: display, type: knowledgeType};
        }
        return id === 'webSearch' ? {name: '网络检索', type: 'WEB'} : {id, name: id, type: 'FILE'};
    };
    const serializedKnowledge = otherKnowledge.map(item => existKnowledgeKeyById[item.id] ?? categorizeKnowledge(item));

    return {linkKnowledge, serializedKnowledge};
};

export const serializeAllKnowledges = (query: string): KnowledgeList[] => {
    const mentions = extractMentionsFromMarkupedText(query);
    const serializedAllKnowledges = mentions
        // eslint-disable-next-line complexity
        .map(mention => {
            const [type, mentionId] = mention.id.split(':');
            // 通过#IAPI菜单，输入检索关键字，选择最后一项自定义文本，提交后会走到这个分支
            if (mention.id.startsWith('API_CUSTOM_INPUT-')) {
                return {id: mention.id, name: mention.display, type};
            }
            else if (type === 'link') {
                return {
                    type: linkKnowledgeId2Type(mention.id)!,
                    id: linkKnowledgeId2Uuid(mention.id),
                    url: linkKnowledgeId2Url(mention.id),
                    name: mention.display,
                };
            }
            else if (type === 'knowledge') {
                const [knowledgeType, retrievalType, ...rest] = mentionId.split('/');
                if (retrievalType === 'LOCAL_CODE' || retrievalType === 'TEXT') {
                    return {id: rest.join('/'), retrievalType, name: mention.display, type: knowledgeType};
                }
                // 兼容下历史数据
                return {
                    id: [retrievalType, ...rest].join('/'),
                    name: mention.display,
                    type: knowledgeType,
                };
            }
            else if (type === 'webSearch') {
                return {id: '', name: '网络检索', type: 'WEB'};
            }
            else if (mention.id === 'repo') {
                return {id: mention.id, name: mention.display, type: 'REPO'};
            }
            else if (type === 'folder') {
                return {id: mentionId, name: mentionId, type: ContextType.FOLDER};
            }
            else if (type === 'file') {
                return {id: mentionId, name: mentionId, type: ContextType.FILE};
            }
            else if (type === 'folder') {
                return {id: mentionId, name: mentionId, type: ContextType.FOLDER};
            }
            else if (mention.id === 'terminal') {
                return {id: mention.id, name: mention.display, type: 'TERMINAL'};
            }
            return {id: mention.id, name: mention.display, type: 'FILE'};
        });

    return serializedAllKnowledges as KnowledgeList[];
};

export const extractFileContextPathsFromMentions = (text: string) => {
    const mentions = extractMentionsFromMarkupedText(text);
    return mentions
        .filter(({id}) => id.startsWith('file:'))
        .map(({id}) => id.replace(/^file:/, ''));
};
