.vscode-dark,
body.dark {
    --context-tag-background: #3d3d3d;
    --context-tag-active-background: rgba(212, 212, 212, 0.3);
    --context-tag-color: #d4d4d4;
    --context-tag-description-color: rgba(212, 212, 212, 0.5);
}

.vscode-light,
body.light {
    --context-tag-background: rgba(85, 85, 85, 0.12);
    --context-tag-active-background: rgba(85, 85, 85, 0.3);
    --context-tag-color: rgba(0, 0, 0, 1);
    --context-tag-description-color: rgba(0, 0, 0, 0.5);
}

.context-tag {
    background: var(--context-tag-background);
    color: var(--context-tag-color);
    border-radius: 4px;
    padding: 0 4px;
    display: flex;
    height: 20px;
    gap: 4px;
    max-width: 240px;
    align-items: center;
    font-size: 12px;
    flex-shrink: 0;
}

.context-tag:hover {
    background: rgba(23, 195, 229, 0.122)
}

.context-tag-active {
    background-color: var(--context-tag-active-background);
    color: var(--comate-editor-foreground);
}

.context-tag [data-role="icon"] {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
}

.context-tag [data-role="name"] {
    overflow: hidden;
    white-space: pre;
    text-overflow: ellipsis;
}

.context-tag [data-role="description"] {
    color: var(--context-tag-description-color);
    flex-shrink: 0;
}

.context-tag [data-role="close-button"] {
    color: var(--context-tag-description-color);
    flex-shrink: 0;
    cursor: pointer;
    width: 12px;
    height: 12px;
}