import {useCallback} from 'preact/hooks';
import {SwitchChangeEventHandler} from 'rc-switch';
import classNames from 'classnames';
import {useExtensionConfig} from '@/hooks/useExtensionConfig';
import intentIcon from '@/assets/intent.svg';
import Switch from '../Switch';
import Tooltip from '../Tooltip';

interface Props {
    className: string;
}

export default function ChatIntentSwitch({className}: Props) {
    const {config: {chatIntentRecognition}, patchChatintentRecognition} = useExtensionConfig();

    const toggleChatintentRecognition = useCallback<SwitchChangeEventHandler>(
        checked => {
            patchChatintentRecognition(checked);
        },
        [patchChatintentRecognition]
    );

    return (
        <Tooltip
            overlay="开启后自动从全库代码、官方文档、或调用网络检索以提取相关上下文知识，增强生成效果"
            overlayClassName="max-w-[240px]"
            mouseEnterDelay={1}
        >
            <div
                className={classNames('flex items-center', {active: chatIntentRecognition}, className)}
                style={{width: 76}}
                aria-hidden="true"
            >
                <i
                    className={`${className}-icon`}
                    // bca-disable-line
                    dangerouslySetInnerHTML={{__html: intentIcon}}
                    style={{width: 16, height: 16, flexShrink: 0}}
                />
                <span
                    className={`${className}-label`}
                    style={{marginLeft: 2, marginRight: 4}}
                >
                    高级
                </span>
                <Switch checked={chatIntentRecognition} onChange={toggleChatintentRecognition} />
            </div>
        </Tooltip>
    );
}
