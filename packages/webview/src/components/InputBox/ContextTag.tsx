/* bca-disable */
import {ReactNode, useCallback} from 'preact/compat';
import cx from 'classnames';
import closeIcon from '@/assets/close.svg';
import FileIcon from '../FileIcon';
import FileToolTip from '../FileContextPopover/FileTooltip';
import {KnowledgeIconMapping} from './KnowledgeIcon';
import './ContextTag.css';

export interface ITag {
    type: 'file' | 'repo' | 'webSearch' | 'link' | 'knowledge' | 'folder' | 'iapi' | 'terminal';
    name: string;
    description?: ReactNode;
    readonly?: boolean;
    id: string;
}

interface Props {
    tag: ITag;
    className?: string;
    onClick?: (tag: ITag) => any;
    onClose?: (tag: ITag) => any;
}

function Tag({icon, className, name, description, onClick, onClose, type, ...props}) {
    return (
        <div className={cx('context-tag', className)} {...props}>
            {icon && <div data-role="icon" onClick={onClick} aria-hidden="true">{icon}</div>}
            <div
                aria-hidden="true"
                data-role="name"
                className={cx({
                    'hover:text-[var(--comate-link-color)]': !!onClick,
                    'hover:underline': !!onClick,
                    'cursor-pointer': !!onClick,
                })}
                onClick={onClick}
            >
                {name}
            </div>
            <div data-role="description" aria-hidden="true">{description}</div>
            {onClose && (
                <button
                    onClick={onClose}
                    aria-label={`当前关联${type}: ${name},点击可取消关联`}
                    data-role="close-button"
                >
                    <span dangerouslySetInnerHTML={{__html: closeIcon}} aria-hidden="true" />
                </button>
            )}
        </div>
    );
}

const tagIconTypeConverter = {
    link: 'web',
};

// eslint-disable-next-line complexity
export function ContextTag({className, tag, onClick, onClose, ...props}: Props) {
    const {type, name, description} = tag;
    const handleClick = useCallback(
        () => {
            onClick?.(tag);
        },
        [onClick, tag]
    );

    const handleClose = useCallback(
        (e: any) => {
            onClose?.(tag);
            e.stopPropagation();
        },
        [onClose, tag]
    );

    const tagProps = {
        ...props,
        icon: (
            <span
                className="w-4 h-4"
                dangerouslySetInnerHTML={{__html: KnowledgeIconMapping[tagIconTypeConverter[type] || type]}}
            />
        ),
        className,
        name,
        description,
        onClose: onClose ? handleClose : undefined,
        onClick: handleClick,
    };
    switch (type) {
        case 'file':
            return <Tag {...tagProps} icon={<FileIcon filename={name} className="w-4 h-4" />} type={'文件'} />;
        case 'folder':
        case 'repo':
        case 'webSearch':
        case 'knowledge':
        case 'terminal':
        case 'iapi':
            return <Tag {...tagProps} onClick={undefined} type={type} />;
        case 'link':
            return <Tag {...tagProps} type={'链接'} />;
        default:
            return <Tag {...tagProps} icon={<FileIcon filename={name} className="w-4 h-4" />} type={'文件'} />;
    }
}

export interface ContextGroupProps {
    tags: ITag[];
    itemClassName?: string;
    onClick: (tag: ITag) => any;
    onChange?: (tags: ITag[], removed: ITag) => any;
}

export function ContextGroup({itemClassName, tags, onClick, onChange}: ContextGroupProps) {
    const handleTagRemove = useCallback(
        (tag: ITag) => {
            const tagsRemine = tags.filter(({id}) => tag.id !== id);
            onChange!(tagsRemine, tag);
        },
        [onChange, tags]
    );

    return (
        <>
            {tags.map(tag => (
                <FileToolTip
                    key={tag.id}
                    filePath={tag.id}
                    placement="top"
                >
                    <ContextTag
                        tag={tag}
                        aria-hidden="true"
                        onClick={onClick}
                        onClose={onChange ? handleTagRemove : undefined}
                        className={itemClassName}
                    />
                </FileToolTip>
            ))}
        </>
    );
}
