:root {
    --inputbox-min-height: 152px;
}

.comate-chat-input textarea {
    height: 19px;
    color: var(--comate-input-foreground);
    background-color: transparent;
    resize: none;
    width: 100%;
    display: block;
    caret-color: var(--comate-link-color);
    /* 为了解决jetbrain的兼容性问题 */
    outline: none !important;
}

.comate-chat-input--mention {
    color: var(--comate-link-color);
    background-color: rgba(3, 202, 238, .15);
    position: relative;
    z-index: 1;
    pointer-events: none;
    border-radius: 4px;
    pointer-events: all;
}

.comate-chat-input .comate-chat-input__highlighter {
    border: none !important;
}

#chatInputWrapper {
    border-radius: 4px;
    padding: 8px 0;
    background: transparent;
    transition: background 1s linear;
    /* background: var(--comate-separator-border, #ffffff66); */
    background: var(--chat-input-border-color);
}

#chatInputWrapper::before {
    content: "";
    position: absolute;
    top: 1px;
    left: 1px;
    bottom: 1px;
    right: 1px;
    border-radius: 4px;
    background-color: var(--comate-tooltip-background);
}

#chatInputWrapper:focus-within {
    background: linear-gradient(-135deg, #03f8e7 0%, #6391f9 100%);
}

/* 以下代码兼容侧边栏宽度无法展示完整的输入框placeholder时，提供一个最小高度 */
@media screen and (max-width: 340px) {
    .comate-chat-input {
        min-height: 36px;
    }

    :root {
        --inputbox-min-height: 168px;
    }
}
