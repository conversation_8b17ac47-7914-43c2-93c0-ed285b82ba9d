/* eslint-disable complexity, max-len, max-lines, max-statements */
/* bca-disable */
import {CSSProperties, forwardRef, memo, Ref} from 'preact/compat';
import {useCallback, useEffect, useRef, useState, useMemo, useImperativeHandle} from 'preact/hooks';
import {
    EventMessage,
    KnowledgeList,
    DehydratedMessage,
    AutoWorkKnowledgeType,
    InputBoxMessageHistory,
    APIItem,
    SearchAPIParamType,
    AgentListWithId,
    VPC_PLUGIN_CONFIG_LINK,
    Feature,
    WebviewAgentConversationType,
    BuiltinAgent,
    ContextType,
} from '@shared/protocols';
import {JSXInternal} from 'preact/src/jsx';
import {useTranslation} from 'react-i18next';
import {debounce, uniqBy} from 'lodash';
import {ProviderCapabilityInfo} from '@comate/plugin-shared-internals';
import {FeatureName, isFeatureVisible} from '@shared/utils/features';
import {E2EBOT_AGENT, PROMPT_TEMPLATE_AGENT, UT_COMMAND} from '@shared/agents';
import {messageHandler} from '@/utils/messageHandler';
import {
    AGENT_AUTOWORK_REFER_WEB,
    AGENT_AUTOWORK_REFER_WEB_PLACEHOLDER,
    INPUT_STOP_GENERATION_MAC_PLACEHOLDER,
    INPUT_STOP_GENERATION_WIN_PLACEHOLDER,
    KNOWLEDGE_ITEM_IAPI_SEARCH_DOC_DESCRIPTION,
    AGENT_AUTOWORK_REFER_KNOWLEDGE_DISABLE_REASON,
} from '@/i18n/constants';
import recommendAPIs from '@/utils/recommendAPIs.json';
import {useI18nLanguage} from '@/hooks/useLanguage';
import {useExtensionConfig} from '@/hooks/useExtensionConfig';
import {useAccessTo} from '@/hooks/useAccessTo';
import {useEditorWatcher} from '@/hooks/useEditorWatcher';
import {useInputBoxSizeToggle} from '@/hooks/useInputBoxSizeToggle';
import {isJetbrains} from '@/config';
import {isInternal} from '@/utils/features';
import {Item, ExtensionPopoverConfig} from '../ExtensionPopover/types';
import ExtensionPopover from '../ExtensionPopover';
import ExtensionTag from '../ExtensionTag';
import {useChatConfig} from '../ChatConfigProvider';
import Mentions, {hashDisplayTransformer} from '../Mentions';
import {useSuggestion} from '../SuggestionProvider';
import {InputModal, useInputModal} from '../Modal/InputModal';
import {transformPastedText} from '../Mentions/utils';
import {apiGetKnowledgeList} from '../AutoWork/utils/api';
import {useAgentContext} from '../SmartAgent/AgentProvider';
import ConfirmModal from '../Modal/ConfirmModal';
import ModelSelector from '../ModelSelector';
import {
    QUICK_COMMANDS,
    NAVIGATE_KNOWLEDGE_ITEMS,
    CODEBASE_KNOWLEDGE_ITEM,
    FILE_KNOWLEDGE_ITEM,
    FOLDER_KNOWLEDGE_ITEM,
    KNOWLEDGE_ITEM,
    WEB_KNOWLEDGE_ITEM,
    WEB_SEARCH_ITEM,
    IAPI_KNOWLEDGE_ITEM,
} from './constants';
import {useChatInput} from './hooks/useChatInput';
import {useChatInputPlaceholder} from './hooks/useChatInputPlaceholder';
import {useChatResources} from './hooks/useChatResources';
import {computeFuzzySearch} from './utils/search';
import {useChatInputIndent} from './hooks/useChatInputIndent';
import {appendIconProp, appendKnowledgeIconProp} from './KnowledgeIcon';
import {
    adjustTextAreaCursor,
    extractMentionsFromMarkupedText,
    getTextBeforeLastSymbol,
    insertMentionTag,
    keyCodeTriggerAdjust,
    parseMarkupedText2PlainText,
    getTextAfterLastSymbol,
    getCorrectSelectionStart,
} from './utils/mention';
import {matchedRevertSlash2IAPI} from './utils/iapi';
import './index.css';
import {ComatePlusAgent} from './utils/queryVisibilitySelector';
import {constructSuggestionList} from './utils/suggestion';
import {
    isEnterKeyPressed,
    isMainKeyPressed,
    isShiftKeyPressed,
} from './utils/is';
import {ExtendedArea} from './ExtendedArea';
import useHistoryNavigation from './hooks/useInputHistoryNavigation';
import {deleteTextAfterMatchIfFull} from './utils/string';
import {getHoverContent} from './hooks/AgentHover';
import {HoverBus} from './hooks/AgentHover/HoverBus';
import {logCancelGenerate, logPluginHover, logPluginSelect} from './logger';
import {useKnowledgeTypeHoverContent} from './hooks/AgentHover/useKnowledgeTypeHoverContent';
import {AgentHoverContent} from './hooks/AgentHover/AgentHoverContent';
import PromptTemplateOperations, {usePromptTemplateOperations} from './PromptTemplateOperations';
import InputContexts, {InputContextsRef} from './InputContexts';
import AgentSelect from './AgentSelect';
import {serializeAllKnowledges} from './utils';

const autoSelectAgentSuggestion: ComatePlusAgent[] = [
    ComatePlusAgent.PADDLE,
    ComatePlusAgent.GDP,
];

function sleep(ms: number) {
    return new Promise<unknown>(res => {
        setTimeout(res, ms);
    });
}

// 快捷命令不需要输入问题，直接发送
const quickCommand = QUICK_COMMANDS.map(v => v.id);

interface Props {
    showStopGenerateBtn?: boolean;
    messages: DehydratedMessage[];
    // rawMessage 是原始带mention的文本
    onSubmit: (
        text: string,
        agent?: string,
        slash?: string,
        knowledgeList?: KnowledgeList[],
        rawMessage?: string,
        extraData?: any
    ) => void;
    mockSubmit?: (message: DehydratedMessage[]) => void;
    submitDisabled?: boolean;
    refHeight?: number;
    inputBoxMessageHistory?: InputBoxMessageHistory[];
    inputRef?: Ref<HTMLTextAreaElement>;
    onFocus?: JSXInternal.FocusEventHandler<HTMLTextAreaElement>;
    username: string;
}

interface RefProps {
    focus: () => void;
}

const getCharBeforeCursor = (textArea: HTMLTextAreaElement) => {
    const selectionStart = textArea.selectionStart;
    return textArea.value[selectionStart - 1];
};

function firstEnabledItem(items: Item[]) {
    return items.find(({disabled}) => !disabled)?.id;
}

const InputBox = forwardRef<RefProps, Props>(function InputBox({
    messages,
    submitDisabled,
    refHeight,
    onSubmit,
    mockSubmit,
    onFocus,
    username,
    showStopGenerateBtn,
}, inputRef) {
    const {currentMessageId} = useChatConfig();
    const {config: {enablePrivateService, privateServiceHost, enableAgent, enableSearchFromInternet, isPoc}} = useExtensionConfig();
    const isWindows = window.navigator.userAgent.includes('Windows');
    const language = useI18nLanguage();
    const {
        text: value,
        commands,
        activePopoverType,
        knowledges: knowledgeList,
        knowledgeTypes,
        selectedCommand: currentCommand,
        selectedAgentId: currentAgent,
        selectedKnowledgeType,
        customized,
        onKnowledgeTypeSelect,
        onAgentSelect,
        onCommandSelect,
        onKnowledgeUpdate,
        onInputChange,
        onMentionInputChange,
        onPopoverTypeChange,
        onQuerySelector,
        suggestionCapability,
    } = useChatInput();
    const {agents, commands: fuzzySearchCommands, smartResult, fuzzySearch, setFuzzySearch} = useChatResources();
    const {agentStatus, createConversation} = useAgentContext();
    const [focusCount, setFocusCount] = useState(0);

    // 这里是给popover提供的Agents过滤
    const fuzzySearchAgents = useMemo(
        () => {
            const visibleAgent = agents.filter(v =>
                // 排除掉未开启的智能体
                !(v.name === E2EBOT_AGENT.name && (!agentStatus.enableFullStackIntelligence || !enableAgent))
                // 排除掉需要内建到一方的 不展示
                && !v.behaveAsBuiltIn
            );
            return visibleAgent;
        },
        [agents, agentStatus, enableAgent]
    );

    const smartResultWithFilterCommands = useMemo(
        () => {
            const visibleCommands = smartResult.filter(v => {
                // 排除掉未开启的智能体
                if (v.item.name === UT_COMMAND.name && (!agentStatus.enableUTChatIntelligence || !enableAgent)) {
                    return false;
                }
                if (v.item.name === E2EBOT_AGENT.name && (!agentStatus.enableFullStackIntelligence || !enableAgent)) {
                    return false;
                }

                return true;
            });
            return visibleCommands;
        },
        [smartResult, agentStatus, enableAgent]
    );

    const fuzzySearchWithFilterCommands = useMemo(
        // 排除掉未开启的智能体
        () => {
            const visibleCommands = fuzzySearchCommands.filter(v => {
                if (v.name === UT_COMMAND.name && (!agentStatus.enableUTChatIntelligence || !enableAgent)) {
                    return false;
                }
                if (v.name === E2EBOT_AGENT.name && (!agentStatus.enableFullStackIntelligence || !enableAgent)) {
                    return false;
                }
                return true;
            });
            return visibleCommands;
        },
        [fuzzySearchCommands, agentStatus, enableAgent]
    );

    const enableIAPI = useAccessTo(Feature.EnableIAPI);

    const [recentFiles, setRecentFiles] = useState<string[]>([]);
    const [refreshKey, setRefreshKey] = useState(0);
    const [, setIsFromHistory] = useState(false);
    // const [chatInputHeight, setChatInputHeight] = useState(0);
    const [searchedAPIs, setSearchedAPIs] = useState<APIItem[]>([]);
    const [historyAPIs, setHistoryAPIs] = useState<APIItem[]>([]);
    const [searchedKnowledgeItems, setSearchedKnowledgeItems] = useState<KnowledgeList[]>([]);
    const ref = useRef<HTMLTextAreaElement>(null);
    const valueRef = useRef<string>(value);
    const {t} = useTranslation();

    const commandTextRef = useRef<HTMLSpanElement>(null);
    const chatInputRef = useRef<HTMLDivElement>(null);
    const inputKeyword = useRef<string>('');

    const knowledgeAndFileList = useMemo(
        () => {
            const onlyUseKnowledge = selectedKnowledgeType === AutoWorkKnowledgeType.KNOWLEDGE;
            const useKeywordSearch = value.split('#')[1] && value.split('#')[1].length !== 0;
            const formatRecentFiles = recentFiles.map(item => ({
                id: item,
                name: item.split('/').pop()!,
                description: item,
            }));
            if (
                selectedKnowledgeType === AutoWorkKnowledgeType.FILE
                || selectedKnowledgeType === AutoWorkKnowledgeType.FOLDER
            ) {
                return formatRecentFiles;
            }

            // #API 场景下，展示通过关键字搜索的结果
            if (selectedKnowledgeType === AutoWorkKnowledgeType.IAPI) {
                return searchedAPIs.map(item => {
                    const {id, title, projectName} = item;
                    return {
                        id,
                        name: title,
                        description: projectName,
                    };
                });
            }

            if (onlyUseKnowledge && !useKeywordSearch) {
                return knowledgeList;
            }

            if (useKeywordSearch) {
                if (onlyUseKnowledge) {
                    return searchedKnowledgeItems;
                }
                return [...formatRecentFiles, ...searchedKnowledgeItems];
            }
            return formatRecentFiles;
        },
        [knowledgeList, recentFiles, selectedKnowledgeType, value, searchedAPIs, searchedKnowledgeItems]
    );

    const knowledgeTypesByWebviewConsumer = useMemo(
        () => {
            if (isJetbrains) {
                const items = [
                    FILE_KNOWLEDGE_ITEM,
                    FOLDER_KNOWLEDGE_ITEM,
                    CODEBASE_KNOWLEDGE_ITEM,
                    WEB_KNOWLEDGE_ITEM,
                    WEB_SEARCH_ITEM,
                ];
                const supported = knowledgeTypes.filter(item => items.find(({id}) => item.id === id));
                if (isFeatureVisible(FeatureName.HASH_MENU_KNOWLEDGE_SET)) {
                    supported.push(KNOWLEDGE_ITEM);
                }
                if (isFeatureVisible(FeatureName.HASH_MENU_API) && enableIAPI && enableSearchFromInternet) {
                    supported.push(IAPI_KNOWLEDGE_ITEM);
                }
                return supported.map(appendIconProp);
            }

            return knowledgeTypes.filter(type => type.id !== AutoWorkKnowledgeType.TERMINAL).map(appendIconProp);
        },
        [knowledgeTypes, enableIAPI, enableSearchFromInternet]
    );

    useEffect(
        () => {
            valueRef.current = value;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [value, currentCommand, knowledgeList]
    );

    const extensionTitle = useMemo(
        () => {
            if (!selectedKnowledgeType) {
                return undefined;
            }
            const knowledgeType = knowledgeTypes.find(item => item.id === selectedKnowledgeType);
            const {displayName, displayNameKey} = knowledgeType ?? {};
            return displayNameKey ? t(displayNameKey) : displayName;
        },
        [knowledgeTypes, selectedKnowledgeType, t]
    );

    const resetFocusStateAfterMentionChange = useCallback<typeof onMentionInputChange>(
        (...args) => {
            // 为了能够校准mention的内部的光标状态
            onMentionInputChange(...args);
            ref.current!.blur();
            ref.current!.focus();
        },
        [onMentionInputChange]
    );

    const {openModal} = useInputModal();
    const onSelectType = useCallback(
        async (type: string) => {
            // 代码库和文件是直接选中，不需要二级菜单
            if (
                type === AutoWorkKnowledgeType.REPO || type === AutoWorkKnowledgeType.CURRENT_FILE
                || type === AutoWorkKnowledgeType.WEB_SEARCH
            ) {
                const {displayName = '', displayNameKey} = knowledgeTypes.find(item => item.id === type) ?? {};
                const label = displayNameKey ? t(displayNameKey) : displayName;
                resetFocusStateAfterMentionChange(insertMentionTag({display: label, id: type}));
            }
            else if (type === AutoWorkKnowledgeType.WEB) {
                openModal({
                    title: t(AGENT_AUTOWORK_REFER_WEB),
                    placeholder: t(AGENT_AUTOWORK_REFER_WEB_PLACEHOLDER),
                    onOk: debounce(async text => {
                        if (text) {
                            const mentionText = await transformPastedText(text);
                            resetFocusStateAfterMentionChange(text => {
                                const beforeText = getTextBeforeLastSymbol(text, '#');
                                return beforeText + mentionText;
                            });
                        }
                    }, 200),
                });
            }
            else if (type === AutoWorkKnowledgeType.IAPI) {
                onKnowledgeTypeSelect(({text}) => ({
                    text: text.endsWith('#') ? text : text + '#',
                    type,
                }));

                ref.current!.focus();
                setTimeout(() => {
                    setSearchedAPIs(historyAPIs.length ? historyAPIs : recommendAPIs[language]);
                    onPopoverTypeChange('knowledge');
                }, 10);
            }
            else {
                // 这里还有选择文件、知识集、添加知识
                const recentFiles = await messageHandler.send(EventMessage.FilesFetchEvent, {type});
                onKnowledgeTypeSelect(({text}) => ({
                    // 如果是通过外部菜单点击的，要补充个#进来
                    text: text.endsWith('#') ? text : text + '#',
                    type,
                }));
                if (type === 'knowledge' && isJetbrains) {
                    onKnowledgeUpdate(recentFiles);
                }
                else {
                    setRecentFiles(recentFiles);
                }
                ref.current!.focus();
                onPopoverTypeChange('knowledge');
            }
        },
        [
            knowledgeTypes,
            onKnowledgeTypeSelect,
            onKnowledgeUpdate,
            onPopoverTypeChange,
            openModal,
            resetFocusStateAfterMentionChange,
            t,
            historyAPIs,
            language,
        ]
    );

    const selectAgent = useCallback(
        async (agentId: string | undefined) => {
            onAgentSelect(agentId);
            ref.current?.focus();
            await sleep(10);
            onInputChange('');
            const fuzzySearch = computeFuzzySearch('', agentId, undefined);
            setFuzzySearch(fuzzySearch);
            onPopoverTypeChange(fuzzySearch?.type);
        },
        [onAgentSelect, onInputChange, onPopoverTypeChange, setFuzzySearch]
    );

    const selectCommand = useCallback(
        async (id: string, type?: 'agent' | 'command', restText?: string) => {
            if (type === 'agent') {
                onAgentSelect(id);
                ref.current?.focus();
                await sleep(10);
                onInputChange(restText || '');
                const fuzzySearch = computeFuzzySearch(restText || '', id, undefined);
                setFuzzySearch(fuzzySearch);
                onPopoverTypeChange(fuzzySearch?.type);
                return;
            }
            if (type === 'command') {
                onCommandSelect(id);
                ref.current?.focus();
                await sleep(10);
                onInputChange(restText || '');
                if (quickCommand.includes(id)) {
                    const commandInfo = commands.find(cmd => cmd.id === id);
                    if (commandInfo) {
                        onSubmit('', commandInfo.owner.name, commandInfo.name);
                    }
                }
            }
        },
        [commands, onAgentSelect, onCommandSelect, onInputChange, onPopoverTypeChange, onSubmit, setFuzzySearch]
    );

    const onSelectKnowledge = useCallback(
        (knowledgeId: string) => {
            if (knowledgeId === 'navigateToKnowledge') {
                if (isInternal) {
                    messageHandler.send(EventMessage.LinkClickEvent, 'https://comate.baidu-int.com/cop/knowledge/list');
                }
                else {
                    if (enablePrivateService && privateServiceHost) {
                        messageHandler.send(EventMessage.LinkClickEvent, privateServiceHost + VPC_PLUGIN_CONFIG_LINK);
                        return;
                    }
                    // TODO: 临时方案，后续需要统一跳转逻辑
                    if ($features.WEBVIEW_CONSUMER === 'vscode') {
                        messageHandler.send(EventMessage.OpenPluginConfigPanelEvent, true);
                    }
                    else {
                        messageHandler.send(EventMessage.LinkClickEvent, 'https://comate.baidu.com/cop/knowledge/list');
                    }
                }
                return;
            }

            const knowledgeInfo = knowledgeList.find(item => item.id === knowledgeId);
            let label = knowledgeInfo?.name || knowledgeId.split('/').pop()!;
            messageHandler.send(EventMessage.SelectedFilesRecordEvent, knowledgeId);

            if (selectedKnowledgeType === AutoWorkKnowledgeType.IAPI) {
                const apiItem = searchedAPIs.find(item => item.id === knowledgeId);
                if (apiItem) {
                    // 记录到「最近使用的API」中(START)
                    const filteredHistoryAPIs = historyAPIs.filter(item => item.id !== knowledgeId);
                    if (filteredHistoryAPIs.length >= 5) {
                        filteredHistoryAPIs.shift();
                    }

                    filteredHistoryAPIs.push(apiItem);
                    setHistoryAPIs(filteredHistoryAPIs);
                    // 记录到「最近使用的API」中(END)

                    // 构建 mention 的 id
                    const {id, title, url} = apiItem;
                    label = title;
                    // #API 搜索到的知识，暂时默认都是API类型的 ContextType
                    // eslint-disable-next-line no-param-reassign
                    knowledgeId = `link:API-${id}:${url}`;

                    // 如果用户选择了自己输入的文本，则为 knowledge 的 id 赋予「API_CUSTOM_INPUT」作为前缀
                    if (id === title) {
                        // eslint-disable-next-line no-param-reassign
                        knowledgeId = `API_CUSTOM_INPUT-${label}`;
                    }
                }
            }

            function combinedKnowledgeMentionId(knowledge: KnowledgeList) {
                const {type, retrievalType = 'TEXT', id} = knowledge;
                return `knowledge:${type}/${retrievalType}/${id}`;
            }
            // 不懂 knowledgeList 和 searchedKnowledgeItems 这两个的区别
            const knowledgeSearchedByKeyword = searchedKnowledgeItems.find(item => item.id === knowledgeId);
            if (knowledgeSearchedByKeyword) {
                label = knowledgeSearchedByKeyword.name;
                // eslint-disable-next-line no-param-reassign
                knowledgeId = combinedKnowledgeMentionId(knowledgeSearchedByKeyword);
            }

            if (knowledgeInfo) {
                onInputChange(insertMentionTag({display: label, id: combinedKnowledgeMentionId(knowledgeInfo)}));
            }
            else if (selectedKnowledgeType === AutoWorkKnowledgeType.FOLDER
                || selectedKnowledgeType === AutoWorkKnowledgeType.FILE
                || !selectedKnowledgeType
            ) {
                const scheme = selectedKnowledgeType || AutoWorkKnowledgeType.FILE;
                onInputChange(insertMentionTag({display: label, id: `${scheme}:${knowledgeId}`}));
            }
            else {
                // 已知的 iapi 会走到这个分支
                onInputChange(insertMentionTag({display: label, id: knowledgeId}));
            }
            // 清空已选择的知识类型
            onKnowledgeTypeSelect(({text}) => ({text, type: undefined}));
            // 为了能够校准mention的内部的光标状态
            ref.current?.blur();
            ref.current?.focus();
        },
        [
            knowledgeList,
            selectedKnowledgeType,
            searchedKnowledgeItems,
            onInputChange,
            onKnowledgeTypeSelect,
            enablePrivateService,
            privateServiceHost,
            searchedAPIs,
            historyAPIs,
        ]
    );

    useEffect(
        () => {
            messageHandler.listen(EventMessage.WebviewConsoleLogEvent, params => {
                // eslint-disable-next-line no-console
                console.log(params);
            });
        },
        []
    );

    const [currentSelect, setCurrentSelect] = useState<string>();
    type PopoverConfig = ExtensionPopoverConfig & {defaultSelectedItemId?: string};
    const knowledgeItems = useMemo(
        () => {
            const disabledReason = isJetbrains ? t(AGENT_AUTOWORK_REFER_KNOWLEDGE_DISABLE_REASON) : undefined;
            const navigateItems = customized
                ? {...NAVIGATE_KNOWLEDGE_ITEMS, disabled: isJetbrains, disabledReason}
                : NAVIGATE_KNOWLEDGE_ITEMS;

            // saas，关闭知识上传入口按钮，customized情况仅在saas存在，上面行逻辑待清理
            const items = selectedKnowledgeType === 'knowledge' && (isInternal || isPoc)
                ? [...knowledgeAndFileList, navigateItems]
                : knowledgeAndFileList;
            return items;
        },
        [customized, knowledgeAndFileList, selectedKnowledgeType, t, isPoc]
    );

    const {theme} = useChatConfig();
    const hoverBus = useMemo(
        (): HoverBus => {
            return {
                onClickExample(example) {
                    const {agentId, commandId, query, knowledgeList} = example;
                    // 触发对话
                    onSubmit(
                        // 三方插件如果有 defaultUserMessage 要替换空的情况
                        parseMarkupedText2PlainText(query),
                        agentId, // agent
                        commandId, // slash
                        knowledgeList,
                        query
                    );

                    // 修改输入框当前命令
                    if (commandId) {
                        const commandWithAgent = commandId.replace(`@${agentId}`, '') + `@${agentId}`;
                        onCommandSelect(commandWithAgent);
                    }
                    else {
                        onAgentSelect(agentId);
                    }
                },
                onClickMockExample(mockExample) {
                    const {agentId, commandId} = mockExample;
                    // 模拟触发对话
                    mockSubmit?.(
                        mockExample.mockMessage
                    );

                    // 修改输入框当前命令
                    if (commandId) {
                        const commandWithAgent = commandId.replace(`@${agentId}`, '') + `@${agentId}`;
                        onCommandSelect(commandWithAgent);
                    }
                    else {
                        onAgentSelect(agentId);
                    }
                },
                logAction(content) {
                    logPluginHover(content);
                },
                theme,
            };
        },
        [mockSubmit, onAgentSelect, onCommandSelect, onSubmit, theme]
    );

    const [hoverConfigForKnowledgeType] = useKnowledgeTypeHoverContent(hoverBus, activePopoverType);

    const hoverConfigForAgent = useCallback(
        (agent: AgentListWithId & {itemType: 'agent' | 'command'}) => {
            if (agent.itemType === 'command') {
                return {};
            }
            const agentId = agent.id;
            const comp = getHoverContent(agentId) || AgentHoverContent.of({
                id: agentId,
                title: agent.displayName ?? agent.name,
                description: agent.description || '',
                examples: [],
            });
            const hoverStartTimeRef: {current?: number} = {};
            const config: Pick<Item, 'hoverContent' | 'onHover' | 'onHoverLeave'> = {
                hoverContent: comp?.render(hoverBus),
                onHover: () => {
                    hoverStartTimeRef.current = Date.now();
                    hoverBus.logAction({
                        type: 'hover',
                        time: hoverStartTimeRef.current,
                        timeOffset: 0,
                        agentId,
                    });
                },
                onHoverLeave: () => {
                    const time = Date.now();
                    const startTime = hoverStartTimeRef.current;
                    hoverBus.logAction({
                        type: 'hoverLeave',
                        time,
                        timeOffset: startTime ? time - startTime : -1,
                        agentId,
                    });
                },
            };
            return config;
        },
        [hoverBus]
    );

    const {currentPromptTemplate, setCurrentPromptTemplate, resetCurrentPromptTemplate, onDeletePromptTemplate} =
        usePromptTemplateOperations();

    const popoverConfig = useMemo(
        (): PopoverConfig | undefined => {
            const keyword = fuzzySearch?.keyword;

            switch (activePopoverType) {
                case 'agent':
                    if (fuzzySearchAgents.length > 0) {
                        return {
                            items: fuzzySearchAgents.map(v => {
                                return {
                                    ...v,
                                    ...hoverConfigForAgent(v),
                                };
                            }),
                            type: activePopoverType,
                            onSelect: agentId => {
                                // 打点
                                logPluginSelect({
                                    targetId: agentId,
                                    inputText: value,
                                    activePopoverType,
                                });
                                return selectAgent(agentId);
                            },
                            refHeight: refHeight,
                            searchValue: keyword,
                            defaultSelectedItemId: firstEnabledItem(fuzzySearchAgents),
                        };
                    }
                    break;
                case 'command':
                    if (fuzzySearchWithFilterCommands.length > 0) {
                        return {
                            items: fuzzySearchWithFilterCommands.map(v => {
                                return {
                                    ...v,
                                    ...hoverConfigForAgent(v),
                                    operations: v.owner?.name === PROMPT_TEMPLATE_AGENT.name
                                        ? (
                                            <PromptTemplateOperations
                                                promptTemplate={v}
                                                setCurrentPromptTemplate={setCurrentPromptTemplate}
                                            />
                                        )
                                        : undefined,
                                };
                            }),
                            type: activePopoverType,
                            onSelect: (id, type) => {
                                // 打点
                                logPluginSelect({
                                    targetId: id,
                                    inputText: value,
                                    activePopoverType,
                                    currentAgent,
                                });
                                return selectCommand(id, type);
                            },
                            refHeight: refHeight,
                            searchValue: keyword,
                            defaultSelectedItemId: firstEnabledItem(fuzzySearchWithFilterCommands),
                        };
                    }
                    break;
                case 'knowledge':
                    if (knowledgeAndFileList) {
                        return {
                            showFileIcon: true,
                            refHeight: 700,
                            title: extensionTitle,
                            items: knowledgeItems.map(appendKnowledgeIconProp),
                            type: activePopoverType,
                            onSelect: id => {
                                // 打点
                                logPluginSelect({
                                    targetId: id,
                                    inputText: value,
                                    activePopoverType,
                                });
                                return onSelectKnowledge(id);
                            },
                            searchValue: getTextAfterLastSymbol(value, '#'),
                            showPrefix: false,
                            defaultSelectedItemId: firstEnabledItem(knowledgeItems),
                            selectedKnowledgeType,
                        };
                    }
                    break;
                case 'pound':
                    return {
                        items: knowledgeTypesByWebviewConsumer.map(v => ({
                            ...hoverConfigForKnowledgeType(v, value),
                        })),
                        type: activePopoverType,
                        onSelect: id => {
                            // 打点
                            logPluginSelect({
                                targetId: id,
                                inputText: value,
                                activePopoverType,
                            });
                            return onSelectType(id);
                        },
                        defaultSelectedItemId: firstEnabledItem(knowledgeTypesByWebviewConsumer),
                    };
                case 'smart': {
                    // 只用空白字符且没有选择插件
                    if (!currentAgent && /^\s*$/.test(value)) {
                        return;
                    }
                    // NOTE: 已经有命令了，就无需再展示智能提示
                    if (smartResultWithFilterCommands.length > 0 && !currentCommand) {
                        const handleSelect = (id: string, type?: 'agent' | 'command') => {
                            const currentSelect = smartResultWithFilterCommands.find(v => v.item.id === id);
                            const matches = currentSelect?.matches;
                            const restText = matches && deleteTextAfterMatchIfFull(value, matches);
                            // 打点
                            logPluginSelect({
                                targetId: id,
                                inputText: value,
                                activePopoverType,
                                restText,
                            });
                            return selectCommand(id, type, restText);
                        };
                        return {
                            items: smartResultWithFilterCommands.map(
                                (v: (typeof smartResultWithFilterCommands)[number]) => {
                                    return {
                                        ...v.item,
                                        highlightPosition: v.highlightPosition,
                                        ...hoverConfigForAgent(v.item),
                                        operations: v.item.owner?.name === PROMPT_TEMPLATE_AGENT.name
                                            ? (
                                                <PromptTemplateOperations
                                                    promptTemplate={v.item}
                                                    setCurrentPromptTemplate={setCurrentPromptTemplate}
                                                />
                                            )
                                            : undefined,
                                    };
                                }
                            ),
                            type: activePopoverType,
                            onSelect: handleSelect,
                            refHeight: refHeight,
                            searchValue: keyword,
                            defaultSelectedItemId: undefined,
                        };
                    }
                    return;
                }
                default:
                    return undefined;
            }
        },
        [
            fuzzySearch?.keyword,
            activePopoverType,
            fuzzySearchAgents,
            fuzzySearchWithFilterCommands,
            knowledgeAndFileList,
            knowledgeTypesByWebviewConsumer,
            refHeight,
            hoverConfigForAgent,
            value,
            selectAgent,
            currentAgent,
            selectCommand,
            extensionTitle,
            knowledgeItems,
            onSelectKnowledge,
            hoverConfigForKnowledgeType,
            onSelectType,
            smartResultWithFilterCommands,
            currentCommand,
            setCurrentPromptTemplate,
            selectedKnowledgeType,
        ]
    );
    useEffect(
        () => {
            if (!popoverConfig) {
                setCurrentSelect(undefined);
                return;
            }
            const currentSelectNotInList = popoverConfig.items.every(v => v.id !== currentSelect);
            if (popoverConfig.defaultSelectedItemId && currentSelectNotInList) {
                setCurrentSelect(popoverConfig.defaultSelectedItemId);
            }
        },
        [currentSelect, popoverConfig]
    );

    const navigateHistory = useHistoryNavigation(refreshKey, ref, setIsFromHistory, popoverConfig);

    const canSendMessage = useMemo(
        () => {
            if (submitDisabled) {
                return false;
            }
            if (popoverConfig && currentSelect && popoverConfig.items.some(v => v.id === currentSelect)) {
                return false;
            }
            if (currentCommand) {
                return true;
            }
            return value.trim().length > 0;
        },
        [submitDisabled, popoverConfig, currentSelect, currentCommand, value]
    );

    const handleSearchAPIByKeyword = useMemo(
        () =>
            debounce(
                async (params: SearchAPIParamType) => {
                    const apiList = await messageHandler.send(EventMessage.SearchAPIByKeywordEvent, params);
                    if (inputKeyword.current !== params.apiKeyword) {
                        return;
                    }

                    const defaultItem = {
                        id: inputKeyword.current,
                        title: inputKeyword.current,
                        projectName: t(KNOWLEDGE_ITEM_IAPI_SEARCH_DOC_DESCRIPTION),
                        url: '',
                    };
                    setSearchedAPIs([...(apiList || []), defaultItem]);
                },
                100
            ),
        [t]
    );

    const handleSearchKnowledgeByKeyword = useMemo(
        () =>
            debounce(
                async (keyword: string) => {
                    const knowledgeList = await apiGetKnowledgeList({query: keyword});
                    setSearchedKnowledgeItems(knowledgeList);
                },
                100
            ),
        []
    );

    const handleSearchFilesByKeyword = useMemo(
        () =>
            debounce(
                async (value: string, keyword: string, type?: string) => {
                    const files = await messageHandler.send(EventMessage.FilesFetchEvent, {keyword, type});
                    // 异步获取搜索结果后，如果输入框的值已经改变，则不展示搜索结果
                    // eslint-disable-next-line max-depth
                    if (value !== valueRef.current || !files) {
                        return;
                    }
                    setRecentFiles(files);
                },
                200
            ),
        [setRecentFiles]
    );

    const handleChange = useCallback(
        async (e: {target: {value: string}}, markedValue: string) => {
            const prevChar = getCharBeforeCursor(ref.current!);
            const value = e.target.value;

            onInputChange(markedValue, {prevChar});
            setFuzzySearch(computeFuzzySearch(value, currentAgent, currentCommand));

            if (activePopoverType === 'knowledge' || value.includes('#')) {
                const chunks = value.split('#');
                const keyword = chunks[chunks.length - 1];
                if (keyword) {
                    // #API 知识时，根据输入实时调用接口返回查询接口的结果
                    if (selectedKnowledgeType === AutoWorkKnowledgeType.IAPI) {
                        const params = {
                            apiKeyword: keyword,
                            num: 5,
                            byVector: false,
                        };

                        inputKeyword.current = keyword;
                        // 输入过程中，展示 API 的优先级：历史 API > 默认 API
                        const apis = historyAPIs.length ? historyAPIs : recommendAPIs[language];
                        const defaultItem = {
                            id: keyword,
                            title: keyword,
                            projectName: t(KNOWLEDGE_ITEM_IAPI_SEARCH_DOC_DESCRIPTION),
                            url: '',
                        };
                        setSearchedAPIs([...apis, defaultItem]);
                        handleSearchAPIByKeyword(params);
                    }
                    else if (selectedKnowledgeType === AutoWorkKnowledgeType.KNOWLEDGE) {
                        handleSearchKnowledgeByKeyword(keyword);
                    }
                    else {
                        handleSearchFilesByKeyword(value, keyword, selectedKnowledgeType);
                    }
                }
                else {
                    if (selectedKnowledgeType === AutoWorkKnowledgeType.IAPI) {
                        return;
                    }

                    onPopoverTypeChange('pound');
                }
            }
        },
        [
            onInputChange,
            setFuzzySearch,
            currentAgent,
            currentCommand,
            activePopoverType,
            selectedKnowledgeType,
            historyAPIs,
            language,
            t,
            handleSearchAPIByKeyword,
            handleSearchKnowledgeByKeyword,
            onPopoverTypeChange,
            handleSearchFilesByKeyword,
        ]
    );

    const {fileContexts, popSelectedFile, onFileContextSelected, selectedFileRelativePaths} = useEditorWatcher();
    const [expand, , {close: minimizeInputboxSize}] = useInputBoxSizeToggle();
    const resetInputBox = useCallback(
        () => {
            onInputChange('');
            onFileContextSelected([]);
            minimizeInputboxSize();
        },
        [onInputChange, onFileContextSelected, minimizeInputboxSize]
    );
    const handleSubmit = useCallback(
        () => {
            if (!canSendMessage) {
                return;
            }

            let serializedAllKnowledges = uniqBy(serializeAllKnowledges(value).concat(fileContexts), t => `${t.type}-${t.id}`);

            if (currentAgent === BuiltinAgent.AutoWork) {
                // 跳转全栈编程智能体
                createConversation({
                    conversationType: WebviewAgentConversationType.E2EBotConversation,
                    payload: {
                        query: parseMarkupedText2PlainText(value),
                        knowledgeList: serializedAllKnowledges,
                    },
                    messageType: 'add-conversation',
                    conversationId: '',
                });
                onPopoverTypeChange(undefined);
                resetInputBox();
                setRefreshKey(refreshKey + 1);
                return;
            }

            if (suggestionCapability) {
                // 有推荐的情况下 看用户发的消息
                messageHandler.send(EventMessage.QueryVisibilitySelectorLogEvent, {
                    suggestionCommand: `${suggestionCapability.name}@${suggestionCapability.owner.name}`,
                    // 最终的插件和功能
                    currentAgent,
                    currentCommand,
                    queryScore: suggestionCapability.queryScore,
                    // 用户发的消息
                    value,
                    type: 'query',
                });
            }

            const realCommand = commands.find(item => item.id === currentCommand);

            // eslint-disable-next-line @typescript-eslint/init-declarations
            let realAgent: string | undefined;
            // 如果是三方归到一方的插件，需要修正到正确的agent
            if (realCommand?.originOwner) {
                realAgent = realCommand.originOwner?.name;
            }

            let slash = realCommand?.name;
            const {
                isNeedRevertSlash2IAPI,
                newSlash,
                filteredKnowledges,
            } = matchedRevertSlash2IAPI(serializedAllKnowledges, value);

            if (isNeedRevertSlash2IAPI) {
                slash = newSlash;
            }

            if (
                slash === 'codeScan'
                && agentStatus.enableSecurityIntelligence === true
                // 自定义指令也可能存在slash为codeScan的可能，需要把它剔除掉
                && ((realAgent || currentAgent) !== PROMPT_TEMPLATE_AGENT.name)
            ) {
                createConversation({
                    conversationType: WebviewAgentConversationType.SecuBotConversation,
                    payload: {
                        query: parseMarkupedText2PlainText(value.trim()) || '为我的代码库扫描可能的安全漏洞',
                        knowledgeList: serializedAllKnowledges,
                    },
                    messageType: 'add-conversation',
                    conversationId: '',
                });
                onPopoverTypeChange(undefined);
                resetInputBox();
                setRefreshKey(refreshKey + 1);
                return;
            }
            // 解决fileContexts可能不包含选择的当前文件，避免影响其他逻辑，先为目前受影响的插件做特殊处理，后续再做统一处理。 TODO: 统一处理knowledgeList和fileContexts这一块的逻辑。
            if (currentAgent === 'paddle' || currentAgent === 'ievalue') {
                const extraKnowledge = selectedFileRelativePaths.map((relativePath: string) => {
                    const basename = decodeURI(relativePath).split(/(\\|\/)/).pop()!;
                    return {id: relativePath, name: basename, type: ContextType.FILE};
                });
                serializedAllKnowledges = uniqBy(serializedAllKnowledges.concat(extraKnowledge), 'id');
            }
            onSubmit(
                // 三方插件如果有 defaultUserMessage 要替换空的情况
                parseMarkupedText2PlainText(value || realCommand?.defaultUserMessage),
                realAgent || currentAgent, // agent
                slash, // slash
                // @ts-expect-error knowledge 和 knowledgeList 什么关系
                isNeedRevertSlash2IAPI ? filteredKnowledges : serializedAllKnowledges,
                value,
                {commandDisplayName: realCommand?.displayName}
            );
            onPopoverTypeChange(undefined);
            resetInputBox();
            setRefreshKey(refreshKey + 1);
        },
        [
            canSendMessage,
            value,
            fileContexts,
            currentAgent,
            suggestionCapability,
            commands,
            agentStatus.enableSecurityIntelligence,
            onSubmit,
            onPopoverTypeChange,
            resetInputBox,
            refreshKey,
            createConversation,
            currentCommand,
            selectedFileRelativePaths,
        ]
    );
    const stopGeneration = useCallback(
        () => {
            if (currentMessageId) {
                messageHandler.send(EventMessage.ResponseCancelEvent, currentMessageId);
            }
        },
        [currentMessageId]
    );

    const hasMessageInProgress = useMemo(
        () => {
            return messages.some(item => item.status === 'inProgress');
        },
        [messages]
    );

    const handleKeyDown = useCallback(
        (event: KeyboardEvent) => {
            if (event.defaultPrevented || event.detail) {
                return;
            }
            const textarea = event.target as HTMLTextAreaElement;
            const selectionStart = textarea.selectionStart;
            const selectionEnd = textarea.selectionEnd;

            if (event.key === 'Backspace') {
                // 光标在最前面时按回退键，代表用户希望不选择任何插件或能力
                if (selectionStart === 0 && selectionStart === selectionEnd) {
                    if (currentCommand) {
                        onCommandSelect(undefined);
                        setTimeout(() => {
                            const fuzzySearch = computeFuzzySearch('', currentAgent, undefined);
                            setFuzzySearch(fuzzySearch);
                            onPopoverTypeChange(fuzzySearch?.type);
                        }, 10);
                        return;
                    }
                    else {
                        // 假设输入框为空，退格行为表现为删除 fileContext
                        popSelectedFile();
                    }
                }
                else if (activePopoverType === 'knowledge' || activePopoverType === 'pound') {
                    const deletePreviousChar = selectionStart === selectionEnd;
                    const textWillDelete = deletePreviousChar
                        ? textarea.value[selectionStart - 1]
                        // 兼容框选删除
                        : textarea.value.substring(selectionStart, selectionEnd);
                    const start = deletePreviousChar ? selectionStart - 1 : selectionStart;
                    const end = selectionEnd;
                    const remainingText = textarea.value.slice(0, start) + textarea.value.slice(end);
                    const lastCharAfterDeletion = remainingText[remainingText.length - 1];
                    if (lastCharAfterDeletion === '#' && selectedKnowledgeType !== AutoWorkKnowledgeType.IAPI) {
                        onPopoverTypeChange('pound');
                        // 清空已选择的知识类型
                        onKnowledgeTypeSelect(({text}) => ({text, type: undefined}));
                        return;
                    }
                    if (textWillDelete.includes('#')) {
                        onPopoverTypeChange(undefined);
                    }
                }
            }
            if (event.key === 'Escape') {
                setCurrentSelect(undefined);
                event.preventDefault();
            }
            if (hasMessageInProgress && event.key === 'c' && event.ctrlKey) {
                logCancelGenerate({hotkey: true});
                stopGeneration();
                event.preventDefault();
            }
            // <S-Enter> <M-Enter> <C-Enter> 触发换行操作
            if (isEnterKeyPressed(event) && (isMainKeyPressed(event) || isShiftKeyPressed(event))) {
                if (selectionStart !== undefined && selectionEnd !== undefined && ref.current) {
                    const correctSelectionStart = getCorrectSelectionStart(value, selectionStart);
                    const newValue = value.substring(0, correctSelectionStart) + '\n'
                        + value.substring(correctSelectionStart);
                    onInputChange(newValue);
                    event.preventDefault();
                    // 修复在输入框中按下 shift + enter 后，光标位置不正确的问题，需要在下一个事件循环中设置光标位置
                    setTimeout(
                        () => {
                            textarea.setSelectionRange(selectionStart + 1, selectionStart + 1);
                        },
                        0
                    );
                }
                else {
                    onInputChange(value + '\n');
                    event.preventDefault();
                }
            }
            else if (isEnterKeyPressed(event) && !isMainKeyPressed(event)) {
                if (canSendMessage && !expand) {
                    handleSubmit();
                    // 清除完对话或查看完帮助说明，删除命令
                    if (currentCommand === 'clear@Comate' || currentCommand === 'help@Comate') {
                        onCommandSelect(undefined);
                    }
                }
                else if (value.length > 0 && !popoverConfig) {
                    // when disabled, enter should create a new line
                    onInputChange(value + '\n');
                }
                event.preventDefault();
            }
            else if (keyCodeTriggerAdjust(event.key)) {
                adjustTextAreaCursor(textarea, event.key);
            }
            navigateHistory(event, ref.current);
        },
        [hasMessageInProgress, navigateHistory, activePopoverType, popSelectedFile, currentCommand, currentAgent, onCommandSelect, setFuzzySearch, onPopoverTypeChange, selectedKnowledgeType, onKnowledgeTypeSelect, stopGeneration, value, onInputChange, canSendMessage, expand, popoverConfig, handleSubmit]
    );

    const handleFocus = useCallback<NonNullable<typeof onFocus>>(
        e => {
            messageHandler.send(EventMessage.HandleInputFocusEvent, {
                value: (e.target as HTMLTextAreaElement)?.value,
            });
            onFocus?.(e);
            setFocusCount(count => count + 1);
        },
        [onFocus]
    );

    const handleReplaceLinkMentionWithText = useCallback(
        (from: string | RegExp, to: string) => {
            if (typeof from === 'object') {
                onInputChange(text => text.replace(from, to));
                // 为了解决替换后光标位置不对，导致内部解析失败的问题
                ref.current!.blur();
                ref.current!.focus();
                onPopoverTypeChange(undefined);
            }
            else {
                onInputChange(text => text.replace(from, to));
                // 解决unlink网页后，打开能力选择面板的问题（DevOps-iScan-17396）
                onCommandSelect(undefined);
            }
        },
        [onInputChange, onPopoverTypeChange, onCommandSelect]
    );

    useEffect(
        () => {
            messageHandler.listen(EventMessage.CurrentQueryEvent, () => {
                const knowledges = extractMentionsFromMarkupedText(value);
                return {
                    prompt: parseMarkupedText2PlainText(value),
                    agent: currentAgent,
                    slash: commands.find(item => item.id === currentCommand)?.name,
                    knowledgeList: knowledges.map(({id}) => id).map(v =>
                        knowledgeList?.find(x => x.id === v) || {id: v, name: v, type: 'FILE'}
                    ),
                };
            });
        },
        [commands, currentAgent, currentCommand, knowledgeList, value]
    );

    const {updateSuggestions} = useSuggestion();

    useEffect(
        () => {
            messageHandler.listen(EventMessage.QueryVisibilitySelector, (topCapability: ProviderCapabilityInfo) => {
                if (topCapability) {
                    onQuerySelector(topCapability);

                    const suggestions = constructSuggestionList(
                        topCapability.owner.name as ComatePlusAgent,
                        topCapability.name,
                        onSubmit
                    );

                    if (updateSuggestions) {
                        updateSuggestions(suggestions);
                    }

                    // 如果已经选择了agent/知识 不强制应用推荐
                    if (
                        autoSelectAgentSuggestion.some(e => e === topCapability.owner.name)
                        && !currentAgent // 没有选择agent
                        && !(value && knowledgeList?.length) // 没有选择知识
                    ) {
                        const suggestionCommand = `${topCapability.name}@${topCapability.owner.name}`;
                        onCommandSelect(suggestionCommand);

                        messageHandler.send(EventMessage.QueryVisibilitySelectorLogEvent, {
                            suggestionCommand,
                            queryScore: topCapability.queryScore,
                            type: 'suggestion',
                        });
                    }
                }
                else {
                    onQuerySelector(undefined);

                    // 分数不够时，清空建议
                    if (updateSuggestions) {
                        updateSuggestions([]);
                    }
                    // onAgentSelect(undefined);
                }
            });
        },
        [
            value,
            currentAgent,
            knowledgeList,
            suggestionCapability,
            onSubmit,
            onQuerySelector,
            onCommandSelect,
            updateSuggestions,
        ]
    );

    const handlePopoverVisibleChange = useCallback(
        (visible: boolean) => {
            onPopoverTypeChange(visible ? activePopoverType : undefined);
        },
        [activePopoverType, onPopoverTypeChange]
    );

    const {indent} = useChatInputIndent(ref, commandTextRef);

    useImperativeHandle(inputRef, () => ({
        focus: () => ref.current?.focus(),
        selectKnowledge: (select: (k: KnowledgeList[]) => Array<{id: string, label: string}>) => {
            select(knowledgeList);
        },
    }));

    const placeholder = useChatInputPlaceholder();
    const currentCommandDisplayText = useMemo(
        () => {
            const {displayName, displayNameKey} = commands.find(item => item.id === currentCommand) ?? {};

            return displayNameKey ? t(displayNameKey) : displayName;
        },
        [commands, currentCommand, t]
    );
    const stopGenerationPlaceholder = useMemo(
        () => {
            return isWindows ? t(INPUT_STOP_GENERATION_WIN_PLACEHOLDER) : t(INPUT_STOP_GENERATION_MAC_PLACEHOLDER);
        },
        [isWindows, t]
    );

    const finalPlaceholder = hasMessageInProgress ? stopGenerationPlaceholder : placeholder;

    const inputContextsRef = useRef<InputContextsRef>(null);
    const inputStyle = useMemo(
        (): CSSProperties => ({
            'text-indent': `${indent}px`,
        }),
        [indent]
    );

    useEffect(
        () => {
            const inputWrapperHeight = chatInputRef.current?.clientHeight;
            if (inputWrapperHeight! > document.body.clientHeight / 2) {
                document.body.classList.add('input-box-size-extended');
            }
            else {
                document.body.classList.remove('input-box-size-extended');
            }
            return () => document.body.classList.remove('input-box-size-extended');
        },
        [value]
    );

    return (
        <div className="relative">
            {currentPromptTemplate && (
                <ConfirmModal
                    closeModal={resetCurrentPromptTemplate}
                    description={`是否删除"${currentPromptTemplate?.displayName}"指令?`}
                    onOk={onDeletePromptTemplate}
                />
            )}
            <InputModal />
            <div className="flex justify-between items-center py-2 gap-3">
                <AgentSelect activeItem={activePopoverType && popoverConfig ? currentSelect : undefined} />
                {(!currentAgent || currentAgent === BuiltinAgent.Comate) && (
                    <ModelSelector username={username} focusCount={focusCount} />
                )}
            </div>
            <div
                className="relative flex flex-col max-h-[calc(100vh-268px)] min-h-[100px]"
                id="chatInputWrapper"
                ref={chatInputRef}
                style={{height: expand ? '70vh' : undefined, maxHeight: '70vh'}}
                // style={{overflow: 'hidden'}}
            >
                {activePopoverType && popoverConfig && (
                    <ExtensionPopover
                        onPopoverVisibleChange={handlePopoverVisibleChange}
                        currentSelect={currentSelect}
                        setCurrentSelect={setCurrentSelect}
                        {...popoverConfig}
                    />
                )}
                <InputContexts ref={inputContextsRef} />
                <div className="w-full resize-none flex-1 relative px-3 overflow-auto">
                    <ExtensionTag
                        currentCommand={currentCommandDisplayText}
                        ref={commandTextRef}
                    />
                    <Mentions.TextArea
                        inputRef={ref}
                        inputStyle={inputStyle}
                        placeholder={finalPlaceholder}
                        hasMessageInProgress={hasMessageInProgress}
                        value={value}
                        expanded={!!(activePopoverType && popoverConfig)}
                        activeItem={activePopoverType && popoverConfig ? currentSelect : undefined}
                        onReplace={handleReplaceLinkMentionWithText}
                        onFocus={handleFocus}
                        onChange={handleChange}
                        onKeyDown={handleKeyDown}
                        displayTransform={hashDisplayTransformer}
                    />
                </div>
                <ExtendedArea
                    showStopGenerateBtn={showStopGenerateBtn}
                    disabled={!canSendMessage}
                    onSubmit={handleSubmit}
                    activeItem={activePopoverType && popoverConfig ? currentSelect : undefined}
                />
            </div>
        </div>
    );
});

export default memo(InputBox);
