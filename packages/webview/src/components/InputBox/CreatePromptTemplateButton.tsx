import {useCallback} from 'preact/hooks';
import {EventMessage} from '@shared/protocols';
import addIcon from '@/assets/plusCircleOutline.svg';
import {messageHandler} from '@/utils/messageHandler';
import Tooltip from '../Tooltip';

export default function CreatePromptTemplateButton() {
    const handleClick = useCallback(
        () => {
            messageHandler.send(EventMessage.PromptTemplateCreateEvent);
            messageHandler.send(EventMessage.UploadUserActionLog, {
                category: 'promptTemplate',
                action: 'create',
                content: 'input create',
            });
        },
        []
    );

    return (
        <>
            <Tooltip
                overlay="创建指令"
                mouseEnterDelay={1}
            >
                <span
                // eslint-disable-next-line max-len
                    className="cursor-pointer mr-1 hover:text-[var(--comate-link-color)]"
                    style={{width: 12}}
                    aria-hidden="true"
                    onClick={handleClick}
                >
                    <i
                    // bca-disable-line
                        dangerouslySetInnerHTML={{__html: addIcon}}
                    />
                </span>
            </Tooltip>
        </>
    );
}


