import {AutoWorkKnowledgeType} from '@shared/protocols';
import fileIcon from '@/assets/file.svg';
import folderIcon from '@/assets/folder.svg';
import knowledgeIcon from '@/assets/knowledge.svg';
import repoIcon from '@/assets/repo.svg';
import currentFileIcon from '@/assets/currentFile.svg';
import hashIcon from '@/assets/hash.svg';
import addIcon from '@/assets/add.svg';
import webIcon from '@/assets/web.svg';
import iapiIcon from '@/assets/api.svg';
import webSearch from '@/assets/webSearch.svg';
import terminalIcon from '@/assets/terminalSquare.svg';
import codeIcon from '@/assets/code.svg';
import {KnowledgeType} from './hooks/useChatInput';

export const KnowledgeIconMapping = {
    [AutoWorkKnowledgeType.FILE]: fileIcon,
    [AutoWorkKnowledgeType.FOLDER]: folderIcon,
    [AutoWorkKnowledgeType.KNOWLEDGE]: knowledgeIcon,
    [AutoWorkKnowledgeType.REPO]: repoIcon,
    [AutoWorkKnowledgeType.CURRENT_FILE]: currentFileIcon,
    [AutoWorkKnowledgeType.WEB]: webIcon,
    [AutoWorkKnowledgeType.IAPI]: iapiIcon,
    [AutoWorkKnowledgeType.ADD_KNOWLEDGE]: addIcon,
    [AutoWorkKnowledgeType.WEB_SEARCH]: webSearch,
    [AutoWorkKnowledgeType.TERMINAL]: terminalIcon,
};

export const appendIconProp = (knowledgeType: KnowledgeType) => {
    return {...knowledgeType, icon: hashIcon};
};

export const appendKnowledgeIconProp = (knowledge: any) => {
    if (knowledge.id === AutoWorkKnowledgeType.ADD_KNOWLEDGE) {
        return {...knowledge, icon: addIcon};
    }
    if (knowledge.retrievalType === 'LOCAL_CODE') {
        return {...knowledge, icon: codeIcon};
    }
    return {...knowledge};
};

export const KnowledgeCascadeMapping = {
    [AutoWorkKnowledgeType.FILE]: true,
    [AutoWorkKnowledgeType.FOLDER]: true,
    [AutoWorkKnowledgeType.KNOWLEDGE]: true,
    [AutoWorkKnowledgeType.REPO]: false,
    [AutoWorkKnowledgeType.CURRENT_FILE]: false,
    [AutoWorkKnowledgeType.WEB]: true,
    [AutoWorkKnowledgeType.IAPI]: true,
    [AutoWorkKnowledgeType.ADD_KNOWLEDGE]: false,
    [AutoWorkKnowledgeType.WEB_SEARCH]: false,
    [AutoWorkKnowledgeType.TERMINAL]: false,
};
