import {File} from '@shared/protocols';
import FileChangeItem from '../FileChangeItem';

interface Props {
    files?: File[];
    summary?: string;
}

function DynamicCodeGenFileList({files, summary}: Props) {
    return (
        <>
            <div className="h-[2px] bg-gradient-to-r from-[#6391F9] to-[#03F8E7]/0 mx-4" />
            <div className="bg-gradient-to-r from-[#03f8e81d] to-[#6391F9]/0 ml-4 mr-1 mb-3 p-3">
                <div className="mx-2">需要 PM 提供此处文案</div>
                {files?.map(({operateType, filepath}) => {
                    return (
                        <FileChangeItem
                            key={filepath}
                            filePath={filepath}
                            operateType={operateType}
                            showActionIcon={false}
                            actions={{diff: () => {}, accept: () => {}}}
                        />
                    );
                })}
                <div className="mt-4 mx-2 h-[1px] bg-white opacity-10" />
                <div className="mt-4 mx-2">{summary}</div>
            </div>
        </>
    );
}

export default DynamicCodeGenFileList;
