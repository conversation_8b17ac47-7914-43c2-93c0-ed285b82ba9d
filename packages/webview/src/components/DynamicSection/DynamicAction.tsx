import {useEffect, useState} from 'preact/hooks';
import {DynamicActionItems} from '@shared/protocols';
import Progress from '../Progress';
import CollapsePanel from '../CollapsePanel';
import RelevantFiles from './RelevantFiles';

export default function DynamicAction({section}: {section: DynamicActionItems}) {
    const {label, tasks, done} = section;
    const [collapsed, setCollapse] = useState(done);

    useEffect(
        () => {
            if (done) {
                setCollapse(true);
            }
        },
        [done]
    );

    return (
        <div className="w-full" aria-hidden="true">
            <CollapsePanel title={label} collapsed={collapsed} onCollapsedChange={setCollapse}>
                <Progress tasks={tasks} />
                <RelevantFiles {...section} />
            </CollapsePanel>
        </div>
    );
}
