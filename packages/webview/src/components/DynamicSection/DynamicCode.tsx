/* bca-disable */
/* eslint-disable complexity */
import {useState, useCallback, useMemo} from 'preact/hooks';
import {partial} from 'lodash';
import {DynamicCodeChunks, EventMessage} from '@shared/protocols';
import {useTranslation} from 'react-i18next';
import {messageHandler} from '@/utils/messageHandler';
import {
    AGENT_AUTOWORK_REFER_CODEBASE,
    AGENT_AUTOWORK_REFER_DOCS,
    AGENT_AUTOWORK_REFER_WEB,
} from '@/i18n/constants';
import CollapsePanel from '../CollapsePanel';
import CodeChunk from '../CodeChunk';
import KnowledgeChunk from '../KnowledgeChunk';
import WebChunk from '../WebChunk';
import CollapseButton from '../CollapsePanel/CollapseButton';

interface MoreProps {
    isCollapsed: boolean;
    onClick: () => void;
}

const maxDisplayNum = 3;

function MoreReferences({isCollapsed, onClick}: MoreProps) {
    return (
        <div className="mt-2">
            <CollapseButton collapsed={isCollapsed} toggle={onClick} />
        </div>
    );
}

// 搜索结果区域
export default function DynamicCode({section}: {messageId?: number | string, section: DynamicCodeChunks}) {
    const {t} = useTranslation();
    const {label, codeChunks: rawCodeChunks = [], knowledgeChunks = [], webChunks = []} = section;

    const codeChunks = useMemo(
        () => rawCodeChunks.filter(chunk => typeof chunk.startLine === 'number' && typeof chunk.endLine === 'number'),
        [rawCodeChunks]
    );

    // 默认收起
    const [collapsed, setCollapse] = useState(true);
    const [codeChunkCollapsed, setCodeChunkCollapse] = useState(true);
    const [knowledgeChunkCollapsed, setKnowledgeChunkCollapse] = useState(true);

    const handleCodeChunkCollapsedChange = useCallback(
        () => {
            setCodeChunkCollapse(!codeChunkCollapsed);
        },
        [codeChunkCollapsed]
    );

    const handleKnowledgeChunkCollapsedChange = useCallback(
        () => {
            setKnowledgeChunkCollapse(!knowledgeChunkCollapsed);
        },
        [knowledgeChunkCollapsed]
    );

    const handleKnowledgeTitleClick = useCallback(
        (url: string) => {
            messageHandler.send(EventMessage.LinkClickEvent, url);
        },
        []
    );

    if (codeChunks.length === 0 && knowledgeChunks.length === 0 && webChunks.length === 0) {
        return null;
    }

    const displayKnowledge = knowledgeChunkCollapsed ? knowledgeChunks.slice(0, maxDisplayNum) : knowledgeChunks;
    return (
        <div className="w-full" aria-hidden="true">
            <CollapsePanel
                noPadding
                title={label}
                collapsed={collapsed}
                onCollapsedChange={setCollapse}
            >
                <div className="flex flex-col gap-3 ml-5">
                    {codeChunks.length !== 0 && (
                        <div>
                            <div className="text-xs mb-2">
                                Base on {t(AGENT_AUTOWORK_REFER_CODEBASE)}（{codeChunks.length}）
                            </div>
                            <div className="flex flex-col gap-3">
                                {/* eslint-disable-next-line max-len */}
                                {(codeChunkCollapsed ? codeChunks.slice(0, maxDisplayNum) : codeChunks).map(chunk => {
                                    return (
                                        <CodeChunk
                                            code={chunk.code}
                                            language={chunk.language}
                                            startLine={chunk.startLine}
                                            endLine={chunk.endLine}
                                            key={`${chunk.filePath}-${chunk.startLine}-${chunk.endLine}`}
                                            filePath={chunk.filePath}
                                        />
                                    );
                                })}
                            </div>
                            {codeChunks.length > maxDisplayNum && (
                                <MoreReferences
                                    isCollapsed={codeChunkCollapsed}
                                    onClick={handleCodeChunkCollapsedChange}
                                />
                            )}
                        </div>
                    )}
                    {knowledgeChunks.length !== 0 && (
                        <div>
                            <div className="text-xs  mb-2">
                                Base on {t(AGENT_AUTOWORK_REFER_DOCS)} ({knowledgeChunks.length})
                            </div>
                            <div className="flex flex-col gap-2">
                                {displayKnowledge.map(chunk => {
                                    const onKnowledgeTitleClick = partial(handleKnowledgeTitleClick, chunk.url);

                                    return (
                                        <KnowledgeChunk
                                            key={chunk.content}
                                            title={chunk.title}
                                            url={chunk.url}
                                            content={chunk.content}
                                            onClick={onKnowledgeTitleClick}
                                        />
                                    );
                                })}
                            </div>
                            {knowledgeChunks.length > maxDisplayNum && (
                                <MoreReferences
                                    isCollapsed={knowledgeChunkCollapsed}
                                    onClick={handleKnowledgeChunkCollapsedChange}
                                />
                            )}
                        </div>
                    )}
                    {webChunks.length !== 0 && (
                        <div>
                            <div className="text-xs mb-2">
                                Base on {t(AGENT_AUTOWORK_REFER_WEB)} ({webChunks.length})
                            </div>
                            <div className="flex flex-col gap-2">
                                {webChunks.map(chunk => {
                                    const onWebTitleClick = partial(handleKnowledgeTitleClick, chunk.url);
                                    return (
                                        <WebChunk
                                            key={chunk.id}
                                            id={chunk.id}
                                            title={chunk.title}
                                            onClick={onWebTitleClick}
                                        />
                                    );
                                })}
                            </div>
                        </div>
                    )}
                </div>
            </CollapsePanel>
        </div>
    );
}
