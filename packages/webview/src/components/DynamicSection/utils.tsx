import {DynamicSection} from '@shared/protocols';
import DynamicAction from './DynamicAction';
import DynamicCodeChunks from './DynamicCode';
import DynamicCodeGenSteps from './DynamicCodeGenSteps';
import DynamicRelativeFiles from './DynamicRelativeFiles';
import DynamicReminder from './DynamicReminder';
import DynamicNotification from './DynamicNotification';

export function DynamicSectionWrapper({messageId, section}: {messageId: number | string, section: DynamicSection}) {
    switch (section.type) {
        case 'actionItems':
            return <DynamicAction section={section} />;
        case 'codeChunks':
            return <DynamicCodeChunks messageId={messageId} section={section} />;
        case 'codeGenSteps':
            return <DynamicCodeGenSteps section={section} />;
        // case 'codePlan':
        //     return <DynamicCodePlan section={section} />;
        case 'relativeFiles':
            return <DynamicRelativeFiles section={section} />;
        case 'reminder':
            return <DynamicReminder section={section} />;
        case 'notification':
            return <DynamicNotification section={section} />;
        default:
            return null;
    }
}
