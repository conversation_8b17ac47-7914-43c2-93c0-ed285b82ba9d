/* bca-disable */
import {useCallback} from 'preact/hooks';
import {
    DynamicNotification as DynamicNotificationProps,
    Notification as NotificationProps,
    EventMessage,
} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import successCircleOutlineIcon from '@/assets/successCircleOutline.svg';
import failCircleOutlineIcon from '@/assets/failCircleOutline.svg';

const renderIcon = (type: string) => {
    switch (type) {
        case 'success':
            return (
                <div
                    className="w-4 h-4 pt-1 text-green-500"
                    dangerouslySetInnerHTML={{__html: successCircleOutlineIcon}}
                />
            );
        case 'fail':
            return (
                <div
                    className="w-4 h-4 pt-1 text-red-500"
                    dangerouslySetInnerHTML={{__html: failCircleOutlineIcon}}
                />
            );
        default:
            return null;
    }
};

const CLASSNAME_MAPPING = {
    info: 'bg-[var(--vscode-notebook-symbolHighlightBackground)] border-[var(--comate-tooltip-border)]',
    success: 'bg-green-500/10 border-green-500/30',
    fail: 'bg-red-500/10 border-red-500/30',
};

function Notification({notificationType, title, path}: NotificationProps) {
    const notificationClass = CLASSNAME_MAPPING[notificationType];

    const handleLinkClick = useCallback(
        () => {
            if (!path) {
                return;
            }
            messageHandler.send(EventMessage.LinkClickEvent, path);
        },
        [path]
    );

    return (
        // eslint-disable-next-line max-len
        <div className={`w-full border px-[12px] py-[6px] flex gap-1 rounded ${notificationClass}`}>
            {renderIcon(notificationType)}
            <div className="flex-1 flex flex-col overflow-hidden">
                <div>
                    {title}
                </div>
                <a
                    // eslint-disable-next-line max-len
                    className="text-[var(--comate-link-color)] hover:cursor-pointer hover:underline active:text-[var(--comate-link-color)] overflow-hidden overflow-ellipsis whitespace-nowrap hover:cursor-pointer"
                    style={{direction: 'rtl', unicodeBidi: 'bidi-override'}}
                    onClick={handleLinkClick}
                >
                    {path?.split('').reverse().join('')}
                </a>
            </div>
        </div>
    );
}

export default function DynamicNotification({section}: {section: DynamicNotificationProps}) {
    const {notifications} = section;

    return (
        <div className="w-full flex flex-col gap-3">
            {notifications.map(notification => {
                return <Notification key={notification.title} {...notification} />;
            })}
        </div>
    );
}
