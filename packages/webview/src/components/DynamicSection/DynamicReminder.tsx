import {DynamicReminder as DynamicReminderProps} from '@shared/protocols';
import gearIcon from '@/assets/gear.svg';

export default function DynamicReminder({section}: {section: DynamicReminderProps}) {
    const {reminder, setting} = section;

    return (
        <div className="w-full opacity-60 break-all">
            {reminder}
            {setting && (
                <a href={`vscode://settings/${setting}`} className="inline-flex items-center indent-[18px] ml-1 relative">
                    <span
                        // bca-disable-line
                        dangerouslySetInnerHTML={{__html: gearIcon}}
                        className="absolute w-4 h-4 left-0"
                    />
                    配置
                </a>
            )}
        </div>
    );
}
