import {useEffect, useState} from 'preact/hooks';
import {DynamicCodeGenSteps as DynamicCodeGenStepsProps, TaskStatus} from '@shared/protocols';
import Steps from '../Steps';
import CollapsePanel from '../CollapsePanel';

const dynamicCodeGenStepsMock: DynamicCodeGenStepsProps = {
    label: '代码生成步骤',
    done: false,
    collapsible: true,
    type: 'codeGenSteps',
    steps: [
        {
            id: 1,
            desc: '开始生成代码',
            status: TaskStatus.INIT,
            tasks: [
                {
                    desc: '搜索相关信息',
                    status: TaskStatus.PROCESSING,
                    taskType: 'SEARCH',
                },
                {
                    desc: '生成理论依据',
                    status: TaskStatus.INIT,
                    taskType: 'REASONING',
                },
            ],
        },
        {
            id: 2,
            desc: '中间阶段生成代码',
            status: TaskStatus.PROCESSING,
            tasks: [
                {
                    desc: '生成理论依据',
                    status: TaskStatus.SUCCEED,
                    taskType: 'REASONING',
                },
                {
                    desc: '形成思路',
                    status: TaskStatus.PROCESSING,
                    taskType: 'THOUGHT',
                },
            ],
        },
        {
            id: 3,
            desc: '完成生成代码',
            status: TaskStatus.SUCCEED,
            tasks: [
                {
                    desc: '生成代码',
                    status: TaskStatus.SUCCEED,
                    taskType: 'CODE_GENERATE',
                },
                {
                    desc: '找到答案',
                    status: TaskStatus.PROCESSING,
                    taskType: 'ANSWER',
                },
            ],
        },
    ],
};

export default function DynamicCodeGenSteps({section = dynamicCodeGenStepsMock}: {section?: DynamicCodeGenStepsProps}) {
    const {label, steps, done} = section;
    const [collapsed, setCollapse] = useState(false);

    useEffect(
        () => {
            if (done) {
                setCollapse(true);
            }
        },
        [done]
    );

    return (
        <div className="w-full" aria-hidden="true">
            <CollapsePanel title={label} collapsed={collapsed} onCollapsedChange={setCollapse}>
                <Steps steps={steps} />
            </CollapsePanel>
        </div>
    );
}
