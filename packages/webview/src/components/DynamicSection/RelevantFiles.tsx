import {DynamicActionItems, TaskStatus} from '@shared/protocols';
import {uniqBy} from 'lodash';
import {useTranslation} from 'react-i18next';
import FileIcon from '../FileIcon';
import './RelevantFiles.css';

interface Props extends DynamicActionItems {
    maxVisible?: number;
}

function RelevantFiles({tasks, relevantFiles, maxVisible = 10}: Props) {
    const {t} = useTranslation();
    const visible = tasks.some(item => {
        return item.taskType === 'SEARCH' && item.status === TaskStatus.PROCESSING;
    });

    if (!visible || !relevantFiles) {
        return null;
    }
    const dedupedFiles = uniqBy(relevantFiles, item => item.replace(/^\.?\//, ''));
    const displayedFiles = dedupedFiles.slice(0, maxVisible);
    const extraFilesCount = dedupedFiles.length - maxVisible;
    return (
        <div className="mt-2 ml-6 flex flex-col gap-2 opacity-60">
            {displayedFiles.map(path => {
                // 兼容windows路径
                const filename = path.split(/[\\/]/).pop()!;
                return (
                    <div key={path} className="flex items-center gap-1">
                        <FileIcon filename={filename} className="w-3 h-3 min-w-3 max-w-3" />
                        <span className="overflow-hidden text-ellipsis whitespace-nowrap">{filename}</span>
                    </div>
                );
            })}
            <div className="flex items-center">
                <span id="dots-flowing">
                    <span></span>
                    <span></span>
                    <span></span>
                </span>
                {extraFilesCount > 0
                    ? <span className="ml-1">{t('agent.autowork.refer.more.files', {count: extraFilesCount})}</span>
                    : null}
            </div>
        </div>
    );
}

export default RelevantFiles;
