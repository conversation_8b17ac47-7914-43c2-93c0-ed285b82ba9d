import {DynamicRelativeFiles as DynamicRelativeFilesProps} from '@shared/protocols';
import FileIcon from '../FileIcon';

export default function DynamicRelativeFiles({section}: {section: DynamicRelativeFilesProps}) {
    const {label, files} = section;

    if (files.length === 0) {
        return null;
    }

    return (
        <div className="w-full" aria-hidden="true">
            <div>{label}：</div>
            <div>
                {files.map((file, index) => {
                    return (
                        // eslint-disable-next-line react/no-array-index-key
                        <div key={`${file}-${index}`}>
                            <FileIcon filename={file} className="w-3 h-3 min-w-fit" />
                            <span>{file.split('/').pop()}</span>
                        </div>
                    );
                })}
            </div>
        </div>
    );
}
