import {DynamicCodePlan, TaskStatus} from '@shared/protocols';
import BulletListEditTextArea from '../BulletListEditTextArea';
import DynamicCodePlanFileList from './DynamicCodePlanFileList';

const mockData = {
    label: '执行步骤',
    done: false,
    collapsible: true,
    type: 'codePlan',
    status: TaskStatus.INIT,
    steps: [
        '编写文档，说明如何在现有项目中使用EasyExcel。',
        '列出新增的依赖项并提供对应的版本号。',
        '提供从旧版POI迁移到EasyExcel的指导手册。',
        '详细描述EasyExcel新增特性和优势。',
        '修改示例，展示EasyExcel的使用方法。',
        '提供从旧版POI迁移到EasyExcel的示例代码。',
    ],
    files: [
        {
            filepath: 'framework-starter-poi/README.md',
            operateType: 'ADD',
            status: TaskStatus.INIT,
            steps: [
                '编写文档，说明如何在现有项目中使用EasyExcel。',
                '列出新增的依赖项并提供对应的版本号。',
                '提供从旧版POI迁移到EasyExcel的指导手册。',
                '详细描述EasyExcel新增特性和优势。',
            ],
        },
        {
            filepath: 'framework-starter-poi/example.md',
            operateType: 'CHANGE',
            status: TaskStatus.INIT,
            steps: [
                '修改示例，展示EasyExcel的使用方法。',
                '提供从旧版POI迁移到EasyExcel的示例代码。',
            ],
        },
    ],
};

export default function DynamicCodePlan() {
    const {label, steps, status} = mockData;

    return (
        <div className="flex flex-col gap-2">
            <BulletListEditTextArea label={label} steps={steps} />
            <div className="flex flex-col">
                修改文件
                <DynamicCodePlanFileList files={[]} status={status} />
            </div>
        </div>
    );
}
