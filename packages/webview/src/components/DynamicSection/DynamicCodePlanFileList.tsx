/* eslint-disable max-len */
/* bca-disable */
import {File, TaskStatus} from '@shared/protocols';
import successIcon from '@/assets/success.svg';
import warnIcon from '@/assets/warn.svg';
import loadIcon from '@/assets/load.png';
import initIcon from '@/assets/init.svg';
import FileChangeItem from '../FileChangeItem';
import CodeBlock from '../Markdown/CodeBlock';

const renderIcon = (status: TaskStatus) => {
    switch (status) {
        case TaskStatus.INIT:
            return (
                <span
                    className="text-white opacity-10 flex-shrink-0 w-3.5 h-3.5"
                    dangerouslySetInnerHTML={{__html: initIcon}}
                />
            );
        case TaskStatus.PROCESSING:
            return <img className="loading-icon opacity-100 flex-shrink-0 w-3.5 h-3.5" src={loadIcon} />;
        case TaskStatus.SUCCEED:
            return <span className="flex-shrink-0 w-3.5 h-3.5" dangerouslySetInnerHTML={{__html: successIcon}} />;
        case TaskStatus.FAIL:
            return (
                <span className="bg-red-600 flex-shrink-0 w-3.5 h-3.5" dangerouslySetInnerHTML={{__html: warnIcon}} />
            );
        default:
            return null;
    }
};

interface Props {
    files: File[];
    status: TaskStatus;
}

function DynamicCodePlanFileList({files, status}: Props) {
    return (
        <>
            {files.map(({operateType, filepath, snippetsDetail = []}) => {
                return (
                    <>
                        <div key={filepath} className="flex items-center">
                            {renderIcon(status)}
                            <FileChangeItem
                                filePath={filepath}
                                operateType={operateType}
                                actions={snippetsDetail.length > 1 ? {accept: () => {}} : {}}
                            />
                        </div>
                        {snippetsDetail.map(snippet => {
                            return (
                                <div key={snippet} className="mx-3">
                                    <CodeBlock
                                        inline={false}
                                        // todo
                                        actions={{diff: () => {}, accept: () => {}, copy: () => {}}}
                                        showActions
                                        showHeader
                                        numLinesToShow={10}
                                    >
                                        {snippet.updated}
                                    </CodeBlock>
                                </div>
                            );
                        })}
                    </>
                );
            })}
        </>
    );
}

export default DynamicCodePlanFileList;
