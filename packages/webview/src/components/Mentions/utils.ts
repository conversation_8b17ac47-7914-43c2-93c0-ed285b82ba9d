import {toastMessage} from '@/utils/message';
import {apiGetGenerateLinkToUuid} from '../AutoWork/utils/api';
import {parseUrlIfPossible} from '../InputBox/utils/is';
import {mention2Text} from '../InputBox/utils/mention';

export const transformPastedText = async (pastedText: string) => {
    const [url, ...rest] = pastedText.trim().split(' ');
    const link = parseUrlIfPossible(url);
    if (link) {
        try {
            const {uuid, title} = await apiGetGenerateLinkToUuid({link});
            if (uuid) {
                const afterText = rest.length ? ' ' + rest.join(' ') : '';
                return mention2Text({display: title, id: `link:${uuid}:${link}`}) + afterText;
            }

            if (title) {
                toastMessage.error(title);
            }
            return pastedText;
        }
        catch (ex) {
            return pastedText;
        }
    }
    return pastedText;
};
