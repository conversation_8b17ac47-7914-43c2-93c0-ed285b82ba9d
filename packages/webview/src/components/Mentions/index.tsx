import {CSSProperties, FC, HTMLAttributes, memo, ReactNode, RefObject, useCallback, useEffect} from 'preact/compat';
import {JSXInternal} from 'preact/src/jsx';
import {MentionsInput, Mention} from 'react-mentions';
import {useTranslation} from 'react-i18next';
import goIcon from '@/assets/go.svg';
import unlinkIcon from '@/assets/unlink.svg';
import {openLink} from '@/actions';
import {copyToClipboard} from '@/utils/clipboard';
import {mention2Text, placeholderChar} from '../InputBox/utils/mention';
import {isLinkKnowledge, linkKnowledgeId2Url} from '../InputBox/utils/is';
import Tooltip from '../Tooltip';
import ToolbarButton from '../ToolbarButton';
import {transformPastedText} from './utils';

interface MentionProps {
    trigger: string;
    data: any[];
    className?: string;
    displayTransform?: (id: string, display: string) => string;
    transformPastedText?: (pastedText: string) => Promise<string> | string;
    markup?: string;
}

const TextAreaMention = Mention as FC<MentionProps>;

interface MentionsInputProps {
    value: string;
    style?: CSSProperties;
    disabled?: boolean;
    expanded?: boolean;
    activeItem?: string;
    onReplace?: (from: string | RegExp, to: string) => void;
    inputStyle?: CSSProperties;
    className?: string;
    placeholder?: string;
    inputRef: RefObject<HTMLTextAreaElement>;
    onChange?: (e: {target: {value: string}}, markedValue: string) => any;
    onFocus?: HTMLAttributes<HTMLTextAreaElement>['onFocus'];
    onBlur?: HTMLAttributes<HTMLTextAreaElement>['onBlur'];
    onKeyDown?: HTMLAttributes<HTMLTextAreaElement>['onKeyDown'];
    onCopy?: HTMLAttributes<HTMLTextAreaElement>['onCopy'];
    onPaste?: (copied: string) => void;
    mentionClassName?: string;
    displayTransform?: (id: string, display: string) => string;
    markup?: string;
    /* 是否开启链接识别功能 */
    disablePasteRecognizeLink?: boolean;
    hasMessageInProgress?: boolean;
}

const EmptyData = [];
export const hashDisplayTransformer = (id: string, display: string) => {
    return `${placeholderChar}#${display}${placeholderChar}`;
};

const isCursorAtEndOfInputAfterHashSymbol = (textarea: HTMLTextAreaElement | null) => {
    if (textarea) {
        const selectionStart = textarea.selectionStart;
        const selectionEnd = textarea.selectionEnd;
        const isEnd = selectionStart === selectionEnd && selectionEnd === textarea.value.length;
        const lastChar = textarea.value.at(-1);
        return isEnd && lastChar === '#';
    }
};


function TextArea({
    inputStyle,
    mentionClassName,
    displayTransform,
    markup,
    onReplace,
    onChange,
    value,
    onPaste,
    disablePasteRecognizeLink: disablePasteReconginzeLink,
    expanded,
    activeItem,
    ...props
}: MentionsInputProps) {
    useEffect(
        () => {
            const textarea = props.inputRef?.current;
            if (!inputStyle || !textarea) {
                return;
            }

            const shadowElement = textarea.parentElement!.children[1] as HTMLDivElement;
            // react-mentions 不支持设置textarea的style, 且textarea的样式需要同步到shadowElement上
            // 当然有可能会有bug, 比如会移除原本存在的样式，先从程序上控制
            // 要注意两个问题：
            // 1. 只能写kebabCase，不能写成React Style的对象
            // 2. value不能写数字，要保留单位
            for (const key of Object.keys(inputStyle)) {
                const value = inputStyle[key];
                if (value) {
                    textarea.style.setProperty(key, String(value));
                    shadowElement.style.setProperty(key, String(value));
                }
            }
            return () => {
                for (const key of Object.keys(inputStyle)) {
                    textarea.style.removeProperty(key);
                    shadowElement.style.removeProperty(key);
                }
            };
        },
        [inputStyle, props.inputRef]
    );

    const preventCopySymbol = useCallback<JSXInternal.ClipboardEventHandler<HTMLTextAreaElement>>(
        e => {
            e.preventDefault();
            const textarea = e.target as HTMLTextAreaElement;
            const selectedText = textarea.value.substring(textarea.selectionStart, textarea.selectionEnd);
            setTimeout(
                () => {
                    copyToClipboard(
                        selectedText.replace(new RegExp(placeholderChar, 'g'), '')
                    );
                },
                100
            );
        },
        []
    );

    const {t} = useTranslation();
    const renderMention = useCallback(
        (node: ReactNode, id: string, rawDisplay: string) => {
            if (isLinkKnowledge(id)) {
                const url = linkKnowledgeId2Url(id);
                const isIapiUrl = ['https://iapi-test.baidu-int.com', 'https://iapi.baidu-int.com'].some(item =>
                    url.startsWith(item)
                );
                const unlink = () => {
                    // 去除前后的占位符, 和开头的#号
                    const display = rawDisplay
                        .replace(new RegExp(placeholderChar, 'g'), '')
                        .slice(1);
                    onReplace?.(mention2Text({display, id}), url);
                };

                const overlay = (
                    <div className="flex items-center gap-1">
                        <ToolbarButton
                            icon={goIcon}
                            // eslint-disable-next-line react/jsx-no-bind
                            onClick={() => openLink(url)}
                            ariaTitle={t('agent.autowork.refer.link.open')}
                            label={t('agent.autowork.refer.link.open')}
                        />
                        <ToolbarButton
                            icon={unlinkIcon}
                            // eslint-disable-next-line react/jsx-no-bind
                            onClick={unlink}
                            ariaTitle={t('agent.autowork.refer.link.unlink')}
                            label={t('agent.autowork.refer.link.unlink')}
                        />
                    </div>
                );
                return (
                    <Tooltip overlay={isIapiUrl ? null : overlay} placement="top" mouseEnterDelay={0.5}>
                        <span>
                            {node}
                        </span>
                    </Tooltip>
                );
            }

            return node;
        },
        [onReplace, t]
    );

    // 粘贴链接后，需要把粘贴前的#也删掉，所以只能改为劫持的形式, 仅支持在光标在行末，且最后一个字符是#，不支持移动光标
    // 1. 如果不是链接，则使用onChange更新value
    // 2. 如果是链接，则使用onReplace替换掉最后的#
    const transformPastedTextAndResetTriggerSymbol = useCallback(
        async (text: string) => {
            if (disablePasteReconginzeLink) {
                onPaste?.(text);
                throw new Error('Break pasted reconginze');
            }

            const enableReconginzedLink = isCursorAtEndOfInputAfterHashSymbol(props.inputRef?.current);
            if (enableReconginzedLink) {
                const transformedText = await transformPastedText(text);
                if (transformedText !== text) {
                    onReplace?.(/#$/, transformedText);
                    return;
                }
            }
            throw new Error('Reconginzed link failed');
        },
        [disablePasteReconginzeLink, onPaste, onReplace, props.inputRef]
    );

    return (
        <MentionsInput
            {...props}
            id="comate-chat-input"
            autocomplete="off"
            type="search"
            role="combobox"
            aria-label="请输入您的问题"
            aria-expanded={`${expanded}`}
            aria-haspopup="listbox"
            aria-owns="input_popover"
            aria-autocomplete="list"
            aria-controls="input_popover"
            {...(activeItem ? {'aria-activedescendant': `input_popover_${activeItem}`} : {})}
            value={value}
            className="comate-chat-input"
            onCopy={preventCopySymbol}
            transformPastedText={transformPastedTextAndResetTriggerSymbol}
            renderMention={renderMention}
            onChange={onChange}
        >
            <TextAreaMention
                data={EmptyData}
                trigger=""
                className="comate-chat-input--mention"
                displayTransform={displayTransform}
                markup="$[__display__](__id__)"
            />
        </MentionsInput>
    );
}

export default {TextArea: memo(TextArea)};
