/* eslint-disable max-len */

import classNames from 'classnames';

export default function Loading({className, ...props}: {className?: string}) {
    return (
        <svg
            version="1.1"
            className={classNames('loading-icon', className)}
            x="0px"
            y="0px"
            width="16px"
            height="16px"
            viewBox="0 0 16 16"
            enableBackground="new 0 0 16 16"
            xmlSpace="preserve"
        >
            <image
                width="16"
                height="16"
                x="0"
                y="0"
                href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACBjSFJN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"
                {...props}
            />
        </svg>
    );
}
