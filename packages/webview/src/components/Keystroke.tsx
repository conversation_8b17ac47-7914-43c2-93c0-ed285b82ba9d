/* eslint-disable max-len */
/* bca-disable */
import windowsLogo from '@/assets/windowsLogo.svg';
export type Key = 'cmd' | 'enter' | 'y' | 'shift' | 'ctrl';

const KEY_ICON_MAPPING: Record<Key, preact.ComponentChildren> = {
    cmd: '⌘',
    enter: '↵',
    y: 'Y',
    shift: '⇧',
    ctrl: 'Ctrl',
};

interface Props {
    value: Key;
}

function Keystroke({value}: Props) {
    const isWindows = window.navigator.userAgent.includes('Windows');
    const isVSCode = $features.WEBVIEW_CONSUMER === 'vscode';
    if (isWindows && value === 'cmd') {
        if (isVSCode) {
            return (
                <div className="text-[var(--comate-descriptionForeground)]">
                    <div
                        className="w-[18px] h-[18px] p-1 flex justify-center items-center rounded text-base bg-[#888888]/20"
                        dangerouslySetInnerHTML={{__html: windowsLogo}}
                    />
                </div>
            );
        }
        return (
            <div className="w-[18px] h-[18px] flex justify-center items-center rounded text-xs text-[var(--comate-descriptionForeground)] bg-[#888888]/20">
                {KEY_ICON_MAPPING.ctrl}
            </div>
        );
    }
    return (
        <div className="w-[18px] h-[18px] flex justify-center items-center rounded text-sm text-[var(--comate-descriptionForeground)] bg-[#888888]/20">
            {KEY_ICON_MAPPING[value]}
        </div>
    );
}

export default Keystroke;
