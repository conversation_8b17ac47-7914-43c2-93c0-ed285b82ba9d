import {useCallback, useState, useRef} from 'preact/hooks';
import {FC, ReactNode} from 'preact/compat';
import classNames from 'classnames';
import Tooltip from '../Tooltip';


interface Props {
    children: ReactNode;
    overlay: ReactNode;
    className?: string;
}


const OverflowTooltip: FC<Props> = ({
    children,
    overlay,
    ...props
}) => {
    const [ellipsis, setEllipsis] = useState<boolean>(false);
    const ref = useRef<HTMLSpanElement>(null);
    const [visible, setVisible] = useState(false);


    const onVisibleChange = useCallback(
        (visible: boolean) => {
            if (visible) {
                const element = ref.current;
                const ellipsis = (element && element.clientWidth < element.scrollWidth) || false;

                if (ellipsis) {
                    setEllipsis(ellipsis);
                    setVisible(true);
                }
            }
            else {
                setVisible(false);
            }
        },
        []
    );

    return (
        <Tooltip
            visible={visible}
            onVisibleChange={onVisibleChange}
            mouseEnterDelay={0.5}
            overlay={overlay}
        >
            <span
                ref={ref}
                className={classNames('block overflow-hidden overflow-ellipsis whitespace-nowrap', props.className)}
            >
                {children}
            </span>
        </Tooltip>
    );
};

export default OverflowTooltip;
