/* eslint-disable complexity */
/* eslint-disable max-len */
/* bca-disable */
import {useCallback, useEffect, useState} from 'preact/hooks';
import {
    EventMessage,
    ContextType,
    DehydratedMessage,
} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import {proxyFetch} from '@/utils/proxyFetch';
import {useLastQuery, useSetLastQuery} from '../LastQueryProvider';
import {
    parseMarkupedText2PlainText,
} from '../InputBox/utils/mention';
import {ariaHelpers} from '../Aria';
import OverflowTooltip from './OverflowTooltip';

interface Props {
    messageOrder: Record<number, number>;
    supportAt: boolean;
    messages: DehydratedMessage[];
    hasMessageInProgress: boolean;
}

interface RecommendQuestionType {
    query: string;
    context?: {
        name: string;
        id: string;
        path: null | string; // 假设path要么为null，要么是string类型
        contents: null | string; // 假设contents要么为null，要么是string类型
        cursorLine: number;
        type: ContextType;
        contextQuery: null | string; // 假设contextQuery要么为null，要么是string类型
        params: null | object; // 假设params可以是null或一个对象，具体对象结构根据实际需要定义
    };
}

// 匹配返回的推荐问题的知识
const splitStringByPatterns = (questionQuery: string): string[] => {
    // 首先，将所有匹配到的分隔符替换为一个特殊的占位符
    let modifiedString = questionQuery;
    const patterns: RegExp[] = [
        /\|<#([^>]*)>\|/g, // 匹配 |<#...>|
        /\|<([^>]*)>\|/g, // 匹配 |<...>|
        /#\|<([^>]*)>\|/g, // 匹配 #|<...>|
        /#([^,|\s]*)\|/g, // 匹配 #...|
    ];
    patterns.forEach((pattern, index) => {
        // 使用一个不会出现在原始字符串中的占位符，比如 `__SPLITTER__${index}__`
        const placeholder = `__SPLITTER__${index}__##1##__SPLITTER__${index}__`;
        modifiedString = modifiedString.replace(pattern, placeholder);
    });

    // 然后，使用占位符来拆分字符串
    const result = modifiedString.split(/__SPLITTER__\d+__/);
    const index = result.findIndex(item => item === '##1##');
    if (index > 0) {
        const pre = result[index - 1];
        if (pre.endsWith('#')) {
            // 如果是，则删除最后一个'#'，并与'##1##'及后面的部分拼接起来
            result[index - 1] = pre.slice(0, -1);
        }
    }

    return result;
};

function RecommendQuestion({messageOrder, supportAt, messages, hasMessageInProgress}: Props) {
    const [recommendQuestions, setRecommendQuestions] = useState<RecommendQuestionType[]>([]);
    const setLastQuery = useSetLastQuery();
    const lastQuery = useLastQuery();

    useEffect(
        () => {
            // 清空上一次的推荐问题
            setRecommendQuestions([]);
            if (lastQuery) {
                // 目前只支持默认问答或者带知识的问答，插件只能选择comate
                if (lastQuery.agent === undefined || lastQuery.agent === 'Comate' || lastQuery.agent === 'AutoWork') {
                    if (lastQuery.prompt !== undefined) {
                        const fetchData = async () => {
                            try {
                                const response = await proxyFetch<RecommendQuestionType[]>(
                                    {
                                        method: 'POST',
                                        url: '/api/aidevops/autocomate/rest/autowork/v1/ask-related-questions',
                                        useBaseUrl: true,
                                        body: JSON.stringify(
                                            {
                                                query: lastQuery.prompt,
                                                answer: '',
                                                count: 3,
                                                contexts: lastQuery.knowledgeList,
                                            }
                                        ),
                                        authorization: true,
                                        headers: {
                                            'Content-Type': 'application/json',
                                        },
                                    }
                                );
                                const filteredQuestions = response.data.data.filter(
                                    question => question.query && question.query.trim() !== ''
                                );
                                setRecommendQuestions(filteredQuestions);
                            }
                            catch (error) {
                                // eslint-disable-next-line no-console
                                console.log('Error', error);
                            }
                        };

                        // 调用异步函数
                        fetchData();
                    }
                }
            }
        },
        [lastQuery]
    );

    // 点击推荐问题函数
    const handleQuestionClick = useCallback(
        (question: RecommendQuestionType, index: number) => {
            const logData = {
                category: 'recommendQuestionClick',
                label: `${index}`,
                content: question.query,
            };
            messageHandler.send(EventMessage.UploadUserActionLog, logData);
            if (question) {
                // 构造基础消息对象
                const nums = splitStringByPatterns(question.query).map(part => {
                    const content = (part === '##1##' && question.context)
                        ? '$[' + question.context.name + '](' + question.context.type + ')'
                        : (part === '##1##' && !question.context)
                            ? ''
                            : part;
                    return content;
                });

                const baseMessage = {
                    prompt: parseMarkupedText2PlainText(nums.join('')),
                    messageOrder,
                    supportAt,
                    chatIntentRecognition: true,
                };

                // 如果有question.context，则添加knowledgeList
                const message = question.context
                    ? {
                        ...baseMessage,
                        knowledgeList: [{
                            name: question.context.name,
                            id: question.context.id,
                            type: question.context.type,
                        }],
                    }
                    : baseMessage;

                // 发送消息
                messageHandler.send(EventMessage.QuerySendEvent, message);
                ariaHelpers.sendChatMessage(message);

                // 设置最后一个查询状态
                setLastQuery(message);
            }
        },
        [messageOrder, setLastQuery, supportAt]
    );

    return (
        <div className="flex flex-col items-start gap-2 mr-4 mb-4 ml-4">
            {recommendQuestions?.length > 0 && (
                recommendQuestions.slice(0, 2).map((question, index) => {
                    const marginTop = index === 0 ? '4' : '0'; // 第一条16px，其余8px
                    // 知识部分提取和加粗
                    const recommendQuestion = splitStringByPatterns(question.query).map((part, partIndex) => {
                        const uniqueKey = `part-${partIndex}-${part}`;
                        const content = (part === '##1##' && question.context)
                            ? '#' + question.context.name
                            : (part === '##1##' && !question.context)
                                ? ''
                                : part;
                        const fontWeight = part === '##1##' ? 900 : 'normal';

                        return (
                            <span key={uniqueKey} style={{fontWeight: fontWeight}}>
                                {content}
                            </span>
                        );
                    });
                    // 使用处理过的部分来生成 tooltip 内容
                    const tooltipContent = <>{recommendQuestion}</>;

                    return (messages.length > 0 && !hasMessageInProgress)
                    && (
                        <a
                            key={question.query}
                            className={`chatbox-operation-button
                                focus:outline-none
                                hover:text-inherit
                                text-inherit
                                text-decoration-none
                                mt-${marginTop}
                                max-w-full
                                h-[28px]
                                rounded-[90px]
                                cursor-pointer
                                gradient-bg
                                pl-3
                                pr-3
                                pt-[5px]
                                pb-[3px]`}
                            onClick={() => handleQuestionClick(question, index)}
                            role="button" // 声明该元素的角色为按钮
                            tabIndex={0} // 使元素可聚焦
                            aria-label={`${question.query}`} // 提供无障碍标签
                            href="#"
                        >
                            <p className="text-xs h-[20px] text-[var(--comate-link-color)] leading-[20px]">
                                <OverflowTooltip overlay={tooltipContent}>
                                    {recommendQuestion}
                                </OverflowTooltip>
                            </p>
                        </a>
                    );
                })
            )}
        </div>
    );
}

export default RecommendQuestion;
