/* eslint-disable complexity */
/* eslint-disable max-len */
/* bca-disable */
import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import {isVSCode} from '@/config';
import sqlIcon from '@/assets/SQLForm.svg';

interface Props {
    onSQLClick: () => void;
}

function MoreOperations({onSQLClick}: Props) {
    return (
        <div className="top-1 right-3 flex flex-col gap-1 border border-solid rounded border-[--comate-tooltip-border] px-2 py-3 fixed z-20 w-[122px] bg-[var(--comate-list-background)]">
            {$features.PLATFORM !== 'poc' && (
                <>
                    <div
                        className={'flex items-center w-[104px] h-[26px] cursor-pointer rounded hover:bg-[var(--comate-listItem-hoverBackground)]'}
                        onClick={() => messageHandler.send(EventMessage.VisitHelpDocsEvent)}
                    >
                        <span className="codicon codicon-question w-4 h-4 mx-1 inline-block" />
                        <p className="text-xs">帮助</p>
                    </div>
                    <div
                        className={'flex items-center w-[104px] h-[26px] cursor-pointer rounded '}
                        onClick={() => messageHandler.send(EventMessage.FeedbackEvent)}
                    >
                        <span className="codicon codicon-feedback w-4 h-4 mx-1 inline-block" />
                        <p className="text-xs">反馈</p>
                    </div>
                </>
            )}

            {$features.PLATFORM === 'poc' && (
                <div
                    className={'flex items-center w-[104px] h-[26px] cursor-pointer rounded hover:bg-[var(--comate-listItem-hoverBackground)]'}
                    onClick={onSQLClick}
                >
                    <div
                        className="w-4 h-4 mx-1 inline-block"
                        dangerouslySetInnerHTML={{__html: sqlIcon}}
                    />
                    <p className="text-xs">SQL生成</p>
                </div>
            )}
            {isVSCode && (
                <div
                    className={'flex items-center w-[104px] h-[26px] cursor-pointer rounded hover:bg-[var(--comate-listItem-hoverBackground)]'}
                    onClick={() => messageHandler.send(EventMessage.DownloadLogEvent)}
                >
                    <span className="codicon codicon-cloud-download w-4 h-4 mx-1 inline-block" />
                    <p className="text-xs">下载日志</p>
                </div>
            )}
        </div>
    );
}

export default MoreOperations;
