import cx from 'classnames';
import {memo} from 'preact/compat';
import {splitMarkupedMentionText2Chunk} from '../InputBox/utils/mention';

/**
 * 高亮内容里的 #mention 即知识
 */
export const MentionHighlightText = memo(function MentionHighlightText({text, className, stringClassName}: {
    text: string;
    className?: string;
    stringClassName?: string;
}) {
    return (
        <p className={cx('break-words', className)}>
            {splitMarkupedMentionText2Chunk(text).map(chunk => {
                if (typeof chunk === 'string') {
                    // eslint-disable-next-line react/jsx-key
                    return <span className={stringClassName}>{chunk}</span>;
                }

                return (
                    // eslint-disable-next-line react/jsx-key
                    <span className="comate-chat-input--mention" style={{padding: '1px 3px'}}>
                        {chunk.display}
                    </span>
                );
            })}
        </p>
    );
});
