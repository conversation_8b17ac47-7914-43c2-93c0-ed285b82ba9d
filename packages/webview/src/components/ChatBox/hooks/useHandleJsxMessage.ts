import {MESSAGE_OPEN_EXTERNAL_LINK, MESSAGE_OPEN_FILE, MESSAGE_PLUGIN_COMMAND, MessageData} from '@comate/plugin-jsx';
import {EventMessage} from '@shared/protocols';
import {useCallback} from 'preact/hooks';
import {messageHandler} from '@/utils/messageHandler';

// TODO 存在事件类型转换过程，需要和webview + vscode + jsx 统一一下
export function useHandleJsxMessage() {
    const handleMessage = useCallback(
        (message: MessageData) => {
            if (message.type === MESSAGE_PLUGIN_COMMAND) {
                const data = {
                    pluginName: message.pluginName,
                    taskId: message.taskId,
                    commandName: message.payload.commandName,
                    data: message.payload.data,
                    replyText: message.payload.replyText,
                };

                messageHandler.send(EventMessage.SubmitCommandEvent, data);
            }

            if (message.type === MESSAGE_OPEN_FILE) {
                const {to, line} = message.payload;
                const href = [to, line].join(':');
                if (!href) {
                    return;
                }
                messageHandler.send(EventMessage.LinkClickEvent, href);
            }

            if (message.type === MESSAGE_OPEN_EXTERNAL_LINK) {
                const {href} = message.payload;
                if (!href) {
                    return;
                }
                messageHandler.send(EventMessage.LinkClickEvent, href);
            }
        },
        []
    );

    return {handleMessage};
}
