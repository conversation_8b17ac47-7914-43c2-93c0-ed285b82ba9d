/* eslint-disable complexity */
/* bca-disable */
import {BuiltinAgent} from '@shared/protocols';
import {useCallback, useState} from 'preact/hooks';
import {t} from 'i18next';
import {WebviewAgentConversationType} from '@shared/protocols';
import comate<PERSON>ogo from '@/assets/comate.png';
import avatar from '@/assets/avatar.svg';
import {MESSAGE_COPY_SUCCESS} from '@/i18n/constants';
import Message from '../Message';
import {useChatResources} from '../InputBox/hooks/useChatResources';
import {renderAgentIcon} from '../SmartAgent/utils';
import CopyButton from './CopyButton';

interface Props {
    role: string;
    username?: string;
    avatarUrl?: string;
    agent?: string;
    uuid?: string;
    isSmartAgent?: boolean;
    smartAgentType?: WebviewAgentConversationType;
}

function Avatar({
    role,
    username,
    avatarUrl,
    agent,
    uuid,
    isSmartAgent,
    smartAgentType,
}: Props) {
    const {agents} = useChatResources();
    const [isLogPopoverVisible, setLogPopoverVisible] = useState(false);
    const [messageVisible, setMessageVisible] = useState(false);
    const currentAgent = agents.find(({name}) => name === agent);
    const handleClickAvatar = useCallback(
        () => {
            $features.WEBVIEW_CONSUMER === 'vscode' && setLogPopoverVisible(true);
        },
        []
    );

    if (role === 'user') {
        if ($features.PLATFORM === 'internal') {
            return (
                <img
                    className="rounded-full w-[22px] h-[22px]"
                    src={`https://eefe.baidu-int.com/avatars/${username ? username : 'unknown'}`}
                />
            );
        }
        else if (avatarUrl) {
            return (
                <img
                    className="rounded-full w-[22px] h-[22px]"
                    src={avatarUrl}
                />
            );
        }
        return (
            <div
                className="rounded-full w-[22px] h-[22px]"
                dangerouslySetInnerHTML={{__html: avatar}}
            />
        );
    }

    if (isSmartAgent && smartAgentType) {
        return renderAgentIcon(smartAgentType, 22);
    }

    switch (agent) {
        case BuiltinAgent.Comate:
        case undefined:
            return (
                <div className="relative">
                    <img
                        className="rounded-full w-[22px] h-[22px]"
                        src={comateLogo}
                    />
                    {isLogPopoverVisible && (
                        <CopyButton
                            uuid={uuid}
                            setLogPopoverVisible={setLogPopoverVisible}
                            setMessageVisible={setMessageVisible}
                        />
                    )}
                    <Message
                        content={t(MESSAGE_COPY_SUCCESS)}
                        messageVisible={messageVisible}
                        setMessageVisible={setMessageVisible}
                    />
                </div>
            );
        default:
            return (
                currentAgent?.icon && !currentAgent?.behaveAsBuiltIn
                    ? (
                        <div className="relative">
                            <img
                                className="rounded-full w-[22px] h-[22px]"
                                src={currentAgent?.icon}
                                onClick={handleClickAvatar}
                            />
                            {isLogPopoverVisible
                                && (
                                    <CopyButton
                                        uuid={uuid}
                                        setLogPopoverVisible={setLogPopoverVisible}
                                        setMessageVisible={setMessageVisible}
                                    />
                                )}
                            <Message
                                content={t(MESSAGE_COPY_SUCCESS)}
                                messageVisible={messageVisible}
                                setMessageVisible={setMessageVisible}
                            />
                        </div>
                    )
                    : (
                        <div className="relative">
                            <img
                                className="rounded-full w-[20px] h-[20px]"
                                src={comateLogo}
                            />
                            {isLogPopoverVisible
                                && (
                                    <CopyButton
                                        uuid={uuid}
                                        setLogPopoverVisible={setLogPopoverVisible}
                                        setMessageVisible={setMessageVisible}
                                    />
                                )}
                            <Message
                                content={t(MESSAGE_COPY_SUCCESS)}
                                messageVisible={messageVisible}
                                setMessageVisible={setMessageVisible}
                            />
                        </div>
                    )
            );
    }
}

export default Avatar;
