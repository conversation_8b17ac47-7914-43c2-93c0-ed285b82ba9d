/* eslint-disable max-len */
/* bca-disable */
import {useCallback, useMemo} from 'preact/hooks';
import {AgentListWithId, BuiltinAgent, DehydratedMessage} from '@shared/protocols';
import {memo} from 'preact/compat';
import {
    FUNCTION_COMMENT,
    INLINE_COMMENT,
    CODE_EXPLAIN,
    OPTIMIZE,
    FUNCTION_SPLIT,
    SlashType,
    INLINE_LOG,
} from '@shared/constants';
import {useTranslation} from 'react-i18next';
import rightIcon from '@/assets/rightSolid.svg';
import safetyIcon from '@/assets/safety.svg';
import {AGENT_COMATE_COMMAND_LOG} from '@/i18n/constants';
import Tooltip from '../Tooltip';
import {useChatResources} from '../InputBox/hooks/useChatResources';
import Avatar from './Avatar';

interface Props {
    avatarUrl: string;
    agent?: string;
    username: string;
    chineseName?: string;
    messages: DehydratedMessage[];
    order: number;
    replyTo?: number;
    setOrder?: (id: number, order: number) => void;
}

const getName = (username: string, chineseName?: string, agent?: string) => {
    switch (agent) {
        case 'user':
            // 会有空字符串的场景，不能用??
            return chineseName || username;
        case undefined:
            return 'Comate';
        default:
            return agent;
    }
};

export const someMessageTypeToCapabilityMap = {
    docstring: FUNCTION_COMMENT,
    optimizeFunction: OPTIMIZE,
    inlineComment: INLINE_COMMENT,
    addInlineLog: INLINE_LOG,
    splitFunction: FUNCTION_SPLIT,
    explain: CODE_EXPLAIN,
};

const matchAgentName = (name: string) => (agent: AgentListWithId) => agent.name === name;
function formatAgentDisplayName(agents: AgentListWithId[], name: string) {
    const current = agents.find(matchAgentName(name));
    return `@${current?.displayName || name}`;
}

function Header({messages, order, replyTo, setOrder, username, chineseName, agent, avatarUrl}: Props) {
    const currentAgent = agent ?? messages[0].name;
    const {role, type, uuid, metadata} = messages[0];
    const command = messages[0].extra?.capability || someMessageTypeToCapabilityMap[type];

    const {agents} = useChatResources();

    const {t} = useTranslation();
    const name = getName(username, chineseName, currentAgent);

    // 插件和comate，autoWork的displayName前都有@，user没有
    const displayName = useMemo(
        () => {
            return role === 'user' ? name : formatAgentDisplayName(agents, name);
        },
        [role, name, agents]
    );

    // comate的功能也要当作能力显示出来
    const commandDisplayName = useMemo(
        () => {
            if (name === BuiltinAgent.Comate && command === SlashType.AUTO_DEBUG) {
                return t('agent.comate.command.explainAndFix');
            }
            // 添加日志功能暂时不开放输入框的指令，暂时支持函数上的 CodeLense 所以得单独翻译下
            if (name === BuiltinAgent.Comate && command === INLINE_LOG) {
                return t(AGENT_COMATE_COMMAND_LOG);
            }
            // 自定义Prompt直接展示command，即文件名
            if (name === BuiltinAgent.PromptTemplate) {
                return messages[0].extra?.capabilityDisplayName || command;
            }
            const current = agents.find(matchAgentName(name));
            const detail = current?.capabilities?.find((item: any) => item.name === command);
            return detail && detail.displayNameKey
                ? t(detail.displayNameKey)
                : (detail?.displayName || name);
        },
        [command, name, agents, t]
    );

    const commandDisplayTag = useMemo(
        () => {
            const current = agents.find(matchAgentName(name));
            const detail = current?.capabilities?.find((item: any) => item.name === command);
            return detail && detail.displayTag;
        },
        [command, name, agents]
    );

    const shouldShowCommand = useMemo(
        () => command && role !== 'user' && commandDisplayName,
        [command, role, commandDisplayName]
    );

    const shouldShowSecurityEnhancement = useMemo(
        () => role !== 'user' && metadata?.secure,
        [role, metadata?.secure]
    );

    const setPrevious = useCallback(
        () => {
            if (typeof replyTo === 'number' && setOrder && order > 0) {
                setOrder(replyTo, order - 1);
            }
        },
        [replyTo, setOrder, order]
    );

    const setNext = useCallback(
        () => {
            if (typeof replyTo === 'number' && setOrder && order < messages.length - 1) {
                setOrder(replyTo, order + 1);
            }
        },
        [replyTo, setOrder, order, messages.length]
    );

    return (
        <div className="flex w-full justify-between mb-4" aria-hidden="true">
            <div style={{alignItems: 'center', display: 'flex', gap: 8}}>
                <span className="display-inline shrink-0">
                    <Avatar role={role} username={username} avatarUrl={avatarUrl} agent={currentAgent} uuid={uuid} />
                </span>
                <div className="shrink-0">{displayName}</div>
                {shouldShowCommand && <div className="opacity-50 text-xs overflow-hidden text-ellipsis whitespace-nowrap">/{t(commandDisplayName)}</div>}
                {commandDisplayTag && (
                    <div className="command-tag shrink-0">
                        <span>{commandDisplayTag}</span>
                    </div>
                )}
                {shouldShowSecurityEnhancement
                    && (
                        <Tooltip
                            destroyTooltipOnHide
                            mouseEnterDelay={2}
                            overlay={t('hint.security.enhanced')}
                            overlayStyle={{width: 'max-content'}}
                            trigger={['hover']}
                            placement="bottomLeft"
                        >
                            <div
                                className="w-4 shrink-0"
                                dangerouslySetInnerHTML={{__html: safetyIcon}}
                            />
                        </Tooltip>
                    )}
            </div>
            {messages.length > 1
                ? (
                    <div className="flex gap-2 items-center select-none">
                        <div
                            onClick={setPrevious}
                            className={`w-[10px] h-[10px] rotate-180 ${
                                order > 0
                                    ? 'cursor-pointer text-[var(--comate-editor-foreground)]'
                                    : 'cursor-not-allowed text-[var(--comate-disabled-iconColor)]'
                            }`}
                            dangerouslySetInnerHTML={{__html: rightIcon}}
                        >
                        </div>
                        <div className="opacity-60 font-mono select-none">
                            <span>{order + 1}</span>
                            <span>/</span>
                            <span>{messages.length}</span>
                        </div>
                        <div
                            onClick={setNext}
                            className={`w-[10px] h-[10px] ${
                                order < messages.length - 1
                                    ? 'cursor-pointer text-[var(--comate-editor-foreground)]'
                                    : 'cursor-not-allowed text-[var(--comate-disabled-iconColor)]'
                            }`}
                            dangerouslySetInnerHTML={{__html: rightIcon}}
                        >
                        </div>
                    </div>
                )
                : null}
        </div>
    );
}

export default memo(Header);
