/* eslint-disable complexity */
/* eslint-disable max-len */
import {Suspense, lazy, memo, useCallback, useEffect, useRef} from 'preact/compat';
import {DehydratedMessage} from '@shared/protocols';
import {actionsToEventHandler} from '@/utils/codeEvent';
import {isJetbrains, isVSCode} from '@/config';
import {isInternal} from '@/utils/features';
import {useChatConfig} from '../ChatConfigProvider';
import Footer from './Footer';
import Header from './Header';
import Content from './Content';
import PromptTemplateInfo from './PromptTemplateInfo';
import './index.css';

const CodeGraphThumbnail = lazy(() => import('../Markdown/CodeGraph/CodeGraphThumbnail'));
interface Props {
    messages: DehydratedMessage[];
    username: string;
    avatarUrl: string;
    chineseName?: string;
    total: number;
    current: number;
    isLastItem: boolean;
}

export const formatOrder = (messages: DehydratedMessage[], order?: number) => {
    if (typeof order === 'number' && order >= 0 && order < messages.length) {
        return order;
    }
    return messages.length - 1;
};

const getAgent = (message: DehydratedMessage) => {
    if (message.role === 'assistant') {
        return message.agent;
    }
    return 'user';
};


function ChatBox({messages, username, avatarUrl, chineseName, total, current, isLastItem}: Props) {
    const chatBoxRef = useRef<HTMLDivElement>(null);
    // const lastItemRef = useRef<HTMLDivElement>(null);
    const {messageOrder, setOrder} = useChatConfig();
    const replyTo = messages[0]?.replyTo;
    const order = formatOrder(messages, typeof replyTo === 'number' ? messageOrder[replyTo] : undefined);
    const message = messages[order];
    const agent = getAgent(message);
    const actions = actionsToEventHandler(message.id, message.actions);
    const onRegenerate = useCallback(
        () => {
            setOrder && typeof replyTo === 'number' && setOrder(replyTo, messages.length);
            chatBoxRef.current?.scrollIntoView({behavior: 'auto', block: 'start', inline: 'nearest'});
        },
        [setOrder, replyTo, messages.length]
    );
    const isWindows = window.navigator.userAgent.includes('Windows');

    useEffect(
        () => {
            const handleKeyDown = event => {
                const isShortcutPressed = event.key === 'l' && (isWindows ? event.ctrlKey : event.metaKey);
                if (isLastItem && isShortcutPressed) {
                    event.preventDefault();

                    if (document.activeElement && (chatBoxRef?.current?.contains(document.activeElement))) {
                        const allCodeElements = chatBoxRef?.current?.querySelectorAll<HTMLDivElement>(
                            '.message-content-code'
                        );
                        let nextElement: HTMLDivElement | null = allCodeElements[0];
                        allCodeElements.forEach((element, index) => {
                            if (element === document.activeElement || element.contains(document.activeElement)) {
                                nextElement = index + 1 < allCodeElements.length
                                    ? allCodeElements[index + 1]
                                    : allCodeElements[0];
                            }
                        });

                        if (nextElement) {
                            nextElement.focus();
                        }
                    }
                    else {
                        chatBoxRef?.current?.focus();
                    }
                }
            };

            window.addEventListener('keydown', handleKeyDown);

            return () => {
                window.removeEventListener('keydown', handleKeyDown);
            };
        },
        [isLastItem, isWindows]
    );

    // 支持插件自定义选中提示
    if (message.role === 'user' && message.content === '' && !message?.code) {
        return null;
    }

    return (
        <div
            ref={chatBoxRef}
            role="listitem"
            aria-label={message.content}
            aria-level={1}
            aria-setsize={total}
            aria-posinset={current}
            id={`message-list-${current}-${order}`}
            className={`focus:outline-none message-content px-4 py-5 mb-1 bg-[--comate-editor-background] ${
                message.status
                    ? `message-content-${message.status}`
                    : ''
            }`}
            tabIndex={-1}
        >
            <Header
                agent={agent}
                messages={messages}
                replyTo={replyTo}
                order={order}
                setOrder={setOrder}
                username={username}
                chineseName={chineseName}
                avatarUrl={avatarUrl}
            />
            <Content message={messages[order]} actions={actions} />
            {isJetbrains && isInternal && message.extra?.codeGraphThumbnail && message.status === 'success' && (
                <Suspense fallback={null}>
                    <CodeGraphThumbnail
                        messageId={message
                            .id}
                        {...message
                            .extra
                            .codeGraphThumbnail}
                    />
                </Suspense>
            )}
            {isVSCode && isInternal && message.extra?.replyMessage && message.status === 'success' && (
                <PromptTemplateInfo
                    userMessage={message.extra?.replyMessage}
                />
            )}
            <Footer
                username={username}
                message={message}
                actions={actions}
                onRegenerate={onRegenerate}
            />
        </div>
    );
}

export default memo(
    ChatBox,
    (prev, next) => JSON.stringify(prev) === JSON.stringify(next)
);
