/* bca-disable */
import {useCallback, useState} from 'preact/hooks';
import {FeedbackOptions} from '@shared/protocols';
import {useTranslation} from 'react-i18next';
import likeIcon from '@/assets/like.svg';
import filledLikeIcon from '@/assets/filledLike.svg';
import dislikeIcon from '@/assets/dislike.svg';
import {ACTION_FEEDBACK_TEXT} from '@/i18n/constants';
import filledDislikeIcon from '@/assets/filledDislike.svg';
import feedbackIcon from '@/assets/feedback.svg';
import {useChatConfig} from '../ChatConfigProvider';
import Tooltip from '../Tooltip';
import FeedbackPopover from './FeedbackPopover';
import {ItemContainer} from './ItemContainer';

interface Props {
    messageId: number;
    sendFeedback: (options: FeedbackOptions) => void;
}

function Feedback({messageId, sendFeedback}: Props) {
    const {messageFeedback, setFeedback} = useChatConfig();
    const value = messageFeedback[messageId];
    const [visible, setVisible] = useState(false);
    const {t} = useTranslation();

    const handleLike = useCallback(
        () => {
            const feedback = value === 'like' ? undefined : 'like';
            const isLike = value === 'like' ? '0' : '1';
            setFeedback(messageId, feedback);
            sendFeedback({isLike});
        },
        [value, messageId, setFeedback, sendFeedback]
    );

    const handleDislike = useCallback(
        () => {
            const feedback = value === 'dislike' ? undefined : 'dislike';
            const isLike = value === 'dislike' ? '0' : '2';
            setFeedback(messageId, feedback);
            sendFeedback({isLike});
        },
        [value, messageId, setFeedback, sendFeedback]
    );

    const handleFeedback = useCallback(
        () => setVisible(true),
        []
    );

    return (
        <div className="flex items-center gap-1 relative">
            <ItemContainer>
                <a
                    aria-label="点赞"
                    aria-pressed={value === 'like'}
                    role="button"
                    className={`chatbox-operation-button
                    focus:outline-none
                    hover:text-inherit
                    text-inherit
                    text-decoration-none
                    items-center
                    cursor-pointer
                    w-4
                    h-4
                    opacity-70
                    hover:opacity-80`}
                    onClick={handleLike}
                    tabIndex={0}
                    href="#"
                >
                    <span
                        aria-hidden="true"
                        dangerouslySetInnerHTML={{__html: value === 'like' ? filledLikeIcon : likeIcon}}
                    />
                </a>
            </ItemContainer>
            <ItemContainer>
                <a
                    className={`chatbox-operation-button
                    focus:outline-none
                    hover:text-inherit
                    text-inherit
                    text-decoration-none
                    cursor-pointer
                    w-4
                    h-4
                    opacity-70
                    hover:opacity-80`}
                    aria-label="点踩"
                    role="button"
                    aria-pressed={value === 'dislike'}
                    onClick={handleDislike}
                    tabIndex={0}
                    href="#"
                >
                    <span
                        aria-hidden="true"
                        dangerouslySetInnerHTML={{__html: value === 'dislike' ? filledDislikeIcon : dislikeIcon}}
                    />
                </a>
            </ItemContainer>
            <ItemContainer>
                <Tooltip overlay={!visible && t(ACTION_FEEDBACK_TEXT)}>
                    <a
                        className={`chatbox-operation-button
                        focus:outline-none
                        hover:text-inherit
                        text-inherit
                        text-decoration-none
                        w-[14px]
                        h-[14px]
                        ${t(ACTION_FEEDBACK_TEXT) ? 'mr-1' : ''}
                        inline-block
                        opacity-70
                        cursor-pointer`}
                        role="button"
                        aria-label="提出反馈"
                        onClick={handleFeedback}
                        tabIndex={0}
                        href="#"
                    >
                        <span
                            aria-hidden="true"
                            dangerouslySetInnerHTML={{__html: feedbackIcon}}
                        />
                    </a>
                </Tooltip>
            </ItemContainer>
            {visible && <FeedbackPopover setVisible={setVisible} sendFeedback={sendFeedback} />}
        </div>
    );
}

export default Feedback;
