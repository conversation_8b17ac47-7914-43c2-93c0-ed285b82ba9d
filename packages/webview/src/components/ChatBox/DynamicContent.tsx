import {DehydratedAssistantMessage} from '@shared/protocols';
import {useMemo} from 'preact/hooks';
import {DynamicSectionWrapper} from '../DynamicSection/utils';
import Markdown from '../Markdown';
import {JsxContent} from './JsxContent';

interface Props {
    message: DehydratedAssistantMessage;
    actions?: Record<string, () => void>;
}

export default function DynamicContent({message, actions}: Props) {
    const renderJSX = !!message.extra?.renderJSX;
    const parsedContent = useMemo(
        () => {
            try {
                return renderJSX ? JSON.parse(message.content) : message.content;
            }
            catch (ex) {
                return null;
            }
        },
        [message.content, renderJSX]
    );

    const dynamicSections = useMemo(
        () => (
            message.dynamicSections?.map((item, idx) => (
                // eslint-disable-next-line react/no-array-index-key
                <DynamicSectionWrapper key={`${message.id}-${idx}`} messageId={message.id} section={item} />
            ))
        ),
        [message.dynamicSections, message.id]
    );

    const dynamicFooterSections = useMemo(
        () => (
            message.dynamicFooterSections?.map((item, idx) => (
                // eslint-disable-next-line react/no-array-index-key
                <DynamicSectionWrapper key={`${message.id}-${idx}`} messageId={message.id} section={item} />
            ))
        ),
        [message.dynamicFooterSections, message.id]
    );

    if (message.dynamicSections === undefined && message.dynamicFooterSections === undefined) {
        return (
            <Markdown
                messageId={message.id}
                content={message.content}
                role={message.role}
                actions={actions}
            />
        );
    }

    return (
        <div className="gap-4 flex flex-col" role="presentation">
            {dynamicSections}
            {renderJSX && parsedContent
                ? <JsxContent message={message} actions={actions} content={parsedContent} />
                : (
                    <Markdown
                        messageId={message.id}
                        content={message.content}
                        role={message.role}
                        actions={actions}
                    />
                )}
            {dynamicFooterSections}
        </div>
    );
}
