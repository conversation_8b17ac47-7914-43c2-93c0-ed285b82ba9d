/* eslint-disable max-len */
/* eslint-disable max-depth, complexity */
import {isEmpty} from 'lodash';
import {useCallback, useEffect, useRef, useState} from 'preact/compat';
import '@comate/plugin-jsx/style.css';
import {
    BuiltinAgent,
    ContextType,
    DehydratedMessage,
    DehydratedUserMessage,
    EventMessage,
    KnowledgeList,
} from '@shared/protocols';
import {useTranslation} from '@/i18n/useTranslation';
import {ChatBoxText} from '@/i18n/constants';
import {Typewriter} from '@/utils/Typewriter';
import {messageHandler} from '@/utils/messageHandler';
import Markdown from '../Markdown';
import {ContextGroup, ITag} from '../InputBox/ContextTag';
import {linkKnowledgeId2Url} from '../InputBox/utils/is';
import Answering from './Answering';
import DynamicContent from './DynamicContent';
import {MentionHighlightText} from './MentionHighlightText';
import {JsxContent} from './JsxContent';

interface Props {
    message: DehydratedMessage;
    actions?: Record<string, () => void>;
}

const contextTagTypeMapping = {
    [ContextType.CURRENT_FILE]: 'file',
    [ContextType.FILE]: 'file',
    [ContextType.FOLDER]: 'folder',
    [ContextType.WEB]: 'webSearch',
    [ContextType.REPO]: 'repo',
    [ContextType.URL]: 'link',
    [ContextType.API]: 'iapi',
    [ContextType.TERMINAL]: 'terminal',
};

function UserQueryMessage({message}: {message: DehydratedUserMessage}) {
    const formattedContent = message.content.replace(/\\n/g, '\n');

    const handleClick = useCallback(
        (tag: ITag) => {
            if (tag.type === 'file') {
                const filePath = tag.id.replace(/^(.*):/, '');
                messageHandler.send(EventMessage.LinkClickEvent, filePath);
            }
            else if (tag.type === 'link') {
                const url = linkKnowledgeId2Url(tag.id);
                messageHandler.send(EventMessage.LinkClickEvent, url);
            }
        },
        []
    );

    const withContexts = children => {
        const isValidKnowledge = (item: KnowledgeList) =>
            ![ContextType.CODE, ContextType.TERMINAL].includes(item.type) && item.name;
        const knowledge2Tag = (item: KnowledgeList) => {
            const type = contextTagTypeMapping[item.type] || 'knowledge';
            return {
                id: item.type === ContextType.URL ? `link:${item.id}:${item.url}` : `${type}:${item.id}`,
                type,
                name: item.name,
            };
        };
        const tags = message.knowledgeList?.filter(isValidKnowledge).map(knowledge2Tag);
        if (tags?.length) {
            return (
                <div className="flex flex-col gap-2">
                    <div className="flex gap-1 flex-wrap">
                        <ContextGroup tags={tags} onClick={handleClick} />
                    </div>
                    {children}
                </div>
            );
        }
        return children;
    };

    if ([BuiltinAgent.Comate].includes(message.agent as BuiltinAgent)) {
        return withContexts(<MentionHighlightText text={formattedContent} />);
    }

    return withContexts(<p className="break-words">{formattedContent}</p>);
}

const MIN_TEXT_SHOW_LENGTH = 15;

export default function Content({message, actions}: Props) {
    const [content, setContent] = useState(message.content);
    const typewriter = useRef<Typewriter>(
        new Typewriter(
            {
                update: setContent,
            },
            // 如果消息返回的不足10个字等等再展示
            typeof content === 'string'
                ? (
                    message.status === 'inProgress' && message.content?.length < MIN_TEXT_SHOW_LENGTH
                        ? ''
                        : (message.content ?? '')
                )
                : (content || '')
        )
    );
    const lastMessageIdRef = useRef(message.id);
    const {t} = useTranslation();

    useEffect(
        () => {
            if (!message.stream) {
                setContent(message.content);
                return;
            }
            if (message.extra?.renderJSX && typeof message.content !== 'string') {
                setContent(message.content);
            }
            if (lastMessageIdRef.current === message.id) {
                if (message.status === 'success' || message.status === 'failed') {
                    typewriter.current.typeAllOut(message.content);
                }
                else if (message.status === 'canceled') {
                    typewriter.current.stop();
                }
                else {
                    typewriter.current.typeString(message.content);
                }
            }
            else {
                setContent(message.content);
                lastMessageIdRef.current = message.id;
                typewriter.current.stop();
                typewriter.current = new Typewriter(
                    {
                        update: setContent,
                    },
                    message.content ?? ''
                );
            }
        },
        [message, setContent]
    );

    if (message.role === 'user') {
        return (
            <div className={`${message.code ? 'mb-[-12px]' : ''}`}>
                {!!message.content.trim() && <UserQueryMessage message={message} />}
                {message.code && (
                    <Markdown
                        messageId={message.id}
                        actions={actions}
                        content={message.code}
                        role={message.role}
                        numLinesToShow={5}
                    />
                )}
            </div>
        );
    }

    if (message.role === 'assistant') {
        // eslint-disable-next-line max-len
        if (
            (message.dynamicSections?.length)
            || (message.dynamicFooterSections?.length)
        ) {
            if (isEmpty(message.dynamicSections) && !content) {
                if (message.status === 'inProgress') {
                    return <Answering showCursor={false} />;
                }
                if (message.status === 'canceled') {
                    return <p>{t(ChatBoxText.GENERATION_STOP)}</p>;
                }
            }
            // 三段式动态内容
            return (
                <DynamicContent
                    message={{
                        ...message,
                        // 消息结束就立刻更新全部
                        content: message.status === 'success' ? (message.content || '生成结果为空') : content,
                    }}
                    actions={actions}
                />
            );
        }
        // 重新生成的场景，切换时会存在content延迟没有清空的场景，如果此时message.content不为空的话，也展示回答中
        if (
            message.status === 'inProgress'
            && (
                !content
                || (
                    content
                    && (!message.content
                        || (typeof message.content === 'string' && message.content.length < MIN_TEXT_SHOW_LENGTH))
                )
            )
        ) {
            return <Answering showCursor={!!message.stream} />;
        }
        if (message.status === 'canceled' && !content) {
            return <p>{t(ChatBoxText.GENERATION_STOP)}</p>;
        }

        if (message.extra?.renderJSX) {
            return <JsxContent message={message} actions={actions} content={content} />;
        }
        return (
            <Markdown
                messageId={message.id}
                actions={actions}
                role={message.role}
                content={content}
            />
        );
    }

    return null;
}
