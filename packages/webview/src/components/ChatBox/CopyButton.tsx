import {EventMessage} from '@shared/protocols';
import {partial} from 'lodash';
import {useCallback, useEffect} from 'preact/hooks';
import {messageHandler} from '@/utils/messageHandler';
import {useTranslation} from '@/i18n/useTranslation';
import {ChatBoxText} from '@/i18n/constants';

interface Props {
    uuid?: string;
    setLogPopoverVisible: (visible: boolean) => void;
    setMessageVisible: (visible: boolean) => void;
}

function CopyButton({uuid, setLogPopoverVisible, setMessageVisible}: Props) {
    const {t} = useTranslation();

    const handleCopy = useCallback(
        (uuid?: string) => {
            setLogPopoverVisible(false);
            setMessageVisible(true);
            messageHandler.send(EventMessage.CopyLogHistoryEvent, uuid);
        },
        [setLogPopoverVisible, setMessageVisible]
    );

    useEffect(
        () => {
            const handleClick = (e: MouseEvent) => {
                if (!(e.target as HTMLElement).closest('.feedback')) {
                    setLogPopoverVisible(false);
                }
            };
            document.addEventListener('click', handleClick);
            return () => {
                document.removeEventListener('click', handleClick);
            };
        },
        [setLogPopoverVisible, setMessageVisible]
    );

    return (
        <div
            // eslint-disable-next-line max-len
            className="absolute left-8 top-6 min-w-24 bg-gray-500/20 ml-1 flex items-center py-[2px] px-[6px] text-xs text-[var(--comate-descriptionForeground)] rounded hover:cursor-pointer hover:opacity-80"
            onClick={partial(handleCopy, uuid)}
        >
            {t(ChatBoxText.COPY_LOG)}
        </div>
    );
}

export default CopyButton;
