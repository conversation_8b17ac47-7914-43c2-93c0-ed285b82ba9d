@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

@keyframes inputcursor {
    0% {
        scale: .8;
    }
    50% {
        scale: 1.2;
    }
    100% {
        scale: .8;
    }
}

.loading-icon {
    animation: spin 1s linear infinite;
}

.command-tag {
    border-radius: 4px;
    background: rgba(75, 126, 229, 0.25);
    padding: 0 8px;

    span {
        color: #4B7EE5;
        font-size: 12px;
        line-height: 20px;
    }
}
.shark-txt {
    background: linear-gradient(45deg, rgba(255, 255, 255, 0) 10%, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0) 70%) -100% / 50% no-repeat;
}
