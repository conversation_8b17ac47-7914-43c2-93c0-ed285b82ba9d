import {EventMessage, MessageExtraReplyMessage} from '@shared/protocols';
import {useCallback, useEffect, useState} from 'preact/hooks';
import classNames from 'classnames';
import {messageHandler} from '@/utils/messageHandler';
import GradientCard from '../SmartAgent/GradientCard';

interface Props {
    userMessage: MessageExtraReplyMessage;
}

export default function PromptTemplateInfo({userMessage}: Props) {
    const [isSaved, setIsSaved] = useState(false);
    const handleClick = useCallback(
        () => {
            messageHandler.send(
                EventMessage.PromptTemplateCreateEvent,
                {query: userMessage.content}
            );
            messageHandler.send(EventMessage.UploadUserActionLog, {
                category: 'promptTemplate',
                action: 'create',
                content: 'message create',
            });
            setIsSaved(true);
        },
        [userMessage]
    );

    useEffect(
        () => {
            messageHandler.send(EventMessage.UploadUserActionLog, {
                category: 'promptTemplate',
                action: 'suggestCreate',
                content: userMessage.content,
            });

        },
        [userMessage.content]
    );

    return (
        <GradientCard color="blue" className="mt-[10px]">
            <div className="pb-[10px]">
                💡 我们注意到您已经重复输入此内容，是否保留当前prompt为自定义指令？
            </div>
            <button
                className={classNames(
                    'rounded px-[7px] leading-[20px] border',
                    // eslint-disable-next-line max-len
                    isSaved ? 'text-[var(--comate-link-color-50)] border-[var(--comate-link-color-15)]' : 'text-[var(--comate-link-color)] border-[var(--comate-link-color-50)]'
                )}
                disabled={isSaved}
                onClick={handleClick}
            >
                {isSaved ? '已保存' : '立即保存'}
            </button>
        </GradientCard>
    );
}
