import {ConfigProvider, withMarkdownArticle} from '@comate/plugin-jsx';
import {ChunkContent} from '@comate/plugin-shared-internals';
import {DehydratedAssistantMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';
import {useChatConfig} from '../ChatConfigProvider';
import {JsxMarkdown} from '../Markdown';
import CodeBlock from '../Markdown/CodeBlock';
import {useHandleJsxMessage} from './hooks/useHandleJsxMessage';

interface Props {
    message: DehydratedAssistantMessage;
    content: ChunkContent;
    actions: any;
}

// @ts-ignore
const Article = withMarkdownArticle(JsxMarkdown, CodeBlock);

export function JsxContent({message, content, actions}: Props) {
    const {handleMessage} = useHandleJsxMessage();
    const {theme} = useChatConfig();

    return (
        <ConfigProvider
            status={message.status}
            onMessage={handleMessage}
            messageHandler={messageHandler}
        >
            {/* @ts-ignore */}
            <Article
                className={theme === 'dark' ? 'comate-theme-dark' : 'comate-theme-light'}
                role={message.role}
                actions={actions}
                // 兼容idea消息内容不统一的情况
                taskId={message.id.toString()}
                pluginName={message.agent || message.name!}
                content={content}
                interactive
            />
        </ConfigProvider>
    );
}
