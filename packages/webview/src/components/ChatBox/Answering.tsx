import platform from '@shared/platform';
import {useTranslation} from 'react-i18next';
import {useEffect, useState} from 'preact/hooks';
import loadIcon from '@/assets/load.png';
import {ANSWER_GENERATE_STEP1, ANSWER_GENERATE_STEP2} from '@/i18n/constants';

interface Props {
    showCursor: boolean;
}

function Answering({showCursor}: Props) {
    const {t} = useTranslation();
    const [text, setText] = useState(t(ANSWER_GENERATE_STEP1));

    useEffect(
        () => {
            const timer = setTimeout(
                () => {
                    setText(t(ANSWER_GENERATE_STEP2));
                },
                400
            );
            return () => clearTimeout(timer);
        },
        [t]
    );
    if (showCursor) {
        return (
            <div className="space-x-1">
                <span className="shark-txt animate-shark bg-blue-500 bg-clip-text text-transparent">
                    {text}
                </span>
            </div>
        );
    }
    return (
        <div className="flex items-center mt-3">
            <img className="loading-icon opacity-100 mr-1 flex-shrink-0 w-3.5 h-3.5" src={loadIcon} />
            {/* eslint-disable-next-line max-len */}
            <p className="bg-clip-text text-transparent bg-[length:200%_200%] animate-gradient bg-gradient-to-r from-[#356AF0] to-[#03FAE7]">
                {platform.resolve('brand')} {t('brand.thinking')}
            </p>
        </div>
    );
}

export default Answering;
