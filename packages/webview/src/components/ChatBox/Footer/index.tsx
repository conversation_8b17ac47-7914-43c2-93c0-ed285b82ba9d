/* eslint-disable complexity */
import {useCallback, useMemo, useEffect, useRef, useState} from 'preact/hooks';
import {useTranslation} from 'react-i18next';
import {BuiltinAgent, DehydratedAssistantMessage, DehydratedMessage, EventMessage} from '@shared/protocols';
import {memo} from 'preact/compat';
import {
    ACTION_COPY_ALL_TEXT,
    ACTION_REGENERATE_INDEX,
    ACTION_REGENERATE_INDEX_PROGRESS,
    ACTION_REGENERATE_TEXT,
} from '@/i18n/constants';
import wandIcon from '@/assets/wand.svg';
import copyIcon from '@/assets/copy.svg';
import embeddingDBIcon from '@/assets/embeddingDB.svg';
import {messageHandler} from '@/utils/messageHandler';
import {toastMessage} from '@/utils/message';
import {isVSCode} from '@/config';
import {copyToClipboard} from '@/utils/clipboard';
import {useToast} from '@/hooks/useToast';
import Feedback from '../Feedback';
import TooltipButton from './TooltipButton';
import ItemTooltipButton from './ItemTooltipButton';
import {Save2iAPIButton} from './CustomButtons';

interface Props {
    username: string;
    message: DehydratedMessage;
    actions?: Record<string, (data: any) => void>;
    onRegenerate?: () => void;
}

const useSubscribeEmbeddingProgress = () => {
    const {t} = useTranslation();
    const [enabled, setEnabled] = useState<boolean>(false);
    const startSubscription = useCallback(
        async () => {
            const res = await messageHandler.send(EventMessage.CodeIndexEvent, {command: 'recreate'});
            // 非.git仓库不支持构建，不需要轮训
            if (res?.progress === -1) {
                return;
            }
            toastMessage.info(`✨${t(ACTION_REGENERATE_INDEX_PROGRESS)}`);
            setEnabled(true);
        },
        [t]
    );

    const intervalRef = useRef<number>();
    useEffect(
        () => {
            if (!enabled) {
                return;
            }
            intervalRef.current = window.setInterval(
                async () => {
                    const {progress} = await messageHandler.send(EventMessage.CodeIndexEvent, {command: 'inquire'});
                    if (progress === 100) {
                        setEnabled(false);
                        window.clearInterval(intervalRef.current);
                    }
                },
                1000
            );
            return () => window.clearInterval(intervalRef.current);
        },
        [enabled]
    );
    return {loading: enabled, subscribe: startSubscription};
};

// 绑定 action 事件与参数
function bindActionFn(fn?: (...args: any[]) => void, ...args: any[]) {
    return () => {
        fn?.(...args);
    };
}

function Footer({message, actions, username, onRegenerate}: Props) {
    const {t} = useTranslation();
    const {toast} = useToast();
    const {loading, subscribe} = useSubscribeEmbeddingProgress();
    const {actionConfigs: actionConfig = {}} = message;

    // 兼容旧版本，将一些 key 在这里定义
    const leftButtonKeys = useMemo(
        () => ['enhancedGenerate', 'batchAccept'],
        []
    );

    // 在IDE测注册一个 action 事件，插件测需要提供的是 actionConfig 相关的显示信息
    const initLeftPositionButtons = useCallback(
        () => {
            for (const key in actionConfig) {
                if (leftButtonKeys.includes(key)) {
                    continue;
                }
                if (actionConfig?.[key]?.position === 'foot-left'
                    || actionConfig?.[key]?.position === 'foot-left-before'
                ) {
                    leftButtonKeys.unshift(key);
                } else if (actionConfig?.[key]?.position === 'foot-left-after') {
                    leftButtonKeys.push(key);
                }
            }
        },
        [actionConfig, leftButtonKeys]
    );

    useEffect(
        () => {
            initLeftPositionButtons();
        },
        [initLeftPositionButtons]
    );

    if (message.role === 'user') {
        return null;
    }

    if (message.status === 'inProgress') {
        return null;
    }

    const {agent} = message as DehydratedAssistantMessage;
    const isComateAskV2 = agent === BuiltinAgent.Comate;

    /** 没有子元素的时候，不要有多余的边距 */
    const hasNoChild = ([
        isComateAskV2 && isVSCode,
        actions?.regenerate,
        actions?.copyAll,
        actions?.regenerate && actions?.feedback,
        actions?.feedback,
        actions?.save2iapi,
    ]).concat(leftButtonKeys.map((key: string) => actions?.[key])).every(v => !v);
    return (
        <div
            className={`flex justify-between items-center ${
                hasNoChild ? '' : 'mt-4'
            }`}
        >
            <div className="flex gap-2 items-center">
                {actions?.save2iapi && <Save2iAPIButton message={message} username={username} />}
                {leftButtonKeys.map(key => ((actions?.[key] && actionConfig?.[key])
                    ? (
                        <TooltipButton
                            key={key}
                            actionConfig={actionConfig?.[key]}
                            // 这里默认会调用 actionConfig 的 data 参数，会存在与实际注册在 action 函数形参不一致的情况
                            onClick={bindActionFn(actions?.[key], actionConfig?.[key].data || message.id)}
                        />
                    )
                    : null))}
            </div>
            <div
                role="toolbar"
                className="flex items-center"
            >
                <div className="flex items-center gap-1">
                    {/* WARN: 更新此处元素，记得检查上面的 hasNoChild */}
                    {actions?.copyAll ? (
                        <ItemTooltipButton
                            overlay={t(ACTION_COPY_ALL_TEXT)}
                            icon={copyIcon}
                            label="全文复制"
                            onClick={bindActionFn(messageId => {
                                copyToClipboard(
                                    typeof message.content === 'string'
                                        ? message.content
                                        : JSON.stringify(message.content));
                                actions?.copyAll(messageId);
                                toast({type: 'success', message: '复制成功'});
                            }, message.id)}
                        />
                    ) : null}
                    {isComateAskV2 && actions?.regenerate ? (
                        <ItemTooltipButton
                            loading={loading}
                            overlay={loading ? t(ACTION_REGENERATE_INDEX_PROGRESS) : t(ACTION_REGENERATE_INDEX)}
                            icon={embeddingDBIcon}
                            label="为当前代码库更新索引"
                            onClick={subscribe}
                        />
                    ) : null}
                    {actions?.regenerate
                        ? (
                            <ItemTooltipButton
                                overlay={t(ACTION_REGENERATE_TEXT)}
                                /* https://console.cloud.baidu-int.com/devops/icafe/issue/DevOps-iScan-15993/show?source=copy-shortcut
                                NOTE: topRight 是为了避免悬浮框超出布局，进而位置计算错乱 */
                                overlapProps={{
                                    placement: 'topRight',
                                }}
                                icon={wandIcon}
                                label="重新生成"
                                onClick={bindActionFn(messageId => {
                                    actions.regenerate(messageId);
                                    onRegenerate?.();
                                }, message.id)}
                            />
                        )
                        : null}
                </div>
                {actions?.regenerate && actions?.feedback
                    ? <span className="w-[1px] h-[12px] mx-2 bg-[var(--vscode-textSeparator-foreground)]"></span>
                    : null}
                {(actions?.feedback)
                    ? <Feedback messageId={message.id} sendFeedback={actions.feedback} />
                    : null}
            </div>
        </div>
    );
}

export default memo(Footer);
