import {useCallback} from 'preact/hooks';
import {DehydratedMessage, EventMessage, HttpUnityResponseType} from '@shared/protocols';
import {useToast} from '@/hooks/useToast';
import TooltipButton from '../TooltipButton';
import { messageHandler } from '@/utils/messageHandler';
import { openLink } from '@/actions';

interface UploadiAPIParams {
    uuid?: string;
    username: string;
}

interface UploadiAPIResponse {
    url: string;
    projectName: string;
    projectId: string;
}

function uploadiAPI({uuid, username}: UploadiAPIParams) {
    const isTest = $features.ENVIRONMENT === 'test';
    const host =  isTest ? 'http://iapi-ai-server.test.bapi.appspace.baidu.com' : 'http://*************:8600';

    return messageHandler.send(
        EventMessage.PassthroughRequest,
        {
            method: 'POST',
            url: `${host}/api/code/apidoc/import`,
            headers: {
                'Content-Type': 'application/json',
            },
            useBaseUrl: false,
            body: JSON.stringify({
                type: 'iapi',
                uuid,
                username,
            }),
        }
    );
}

interface Props {
    username: string;
    message: DehydratedMessage;
}

function Save2iAPIButton({message, username}: Props) {
    const {content, iapiUuid} = message;
    const {toast} = useToast();

    const onClick = useCallback(
        async () => {
            const response: any =  await uploadiAPI({uuid: iapiUuid, username});
            if (response.error) {
                toast({
                    type: 'fail',
                    message: `保存失败${response.error.message ?? ''}`,
                });
            } else if (response.data.code !== 200 && response.data.message) {
                toast({
                    type: 'fail',
                    message: `保存失败，${response.data.message}`,
                });
            } else if (response.data.code === 200) {
                toast({
                    type: 'success',
                    message: '保存成功',
                    render: () => (<div>保存成功! <a className="text-blue-600" onClick={() => openLink(response.data.data.url)} href="#">点击此处</a>查看、调试该接口</div>),
                });
            }
        },
        [content, toast]
    );

    return (
        <TooltipButton
            actionConfig={{name: '保存至iAPI'}}
            onClick={onClick}
        />
    );
}

export default Save2iAPIButton;
