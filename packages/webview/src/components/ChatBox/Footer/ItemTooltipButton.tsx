import {ItemContainer} from '../ItemContainer';
import Tooltip, {Props as TooltipProps} from '../../Tooltip';
import Loading from '../../Loading';

interface Props {
    overlay?: string;
    onClick?: () => void;
    label?: string;
    loading?: boolean;
    icon: string;
    overlapProps?: Pick<TooltipProps, 'placement'>;
}


function ItemTooltipButton({overlay, onClick, loading, label, icon, overlapProps}: Props) {
    return (
        <ItemContainer>
            <Tooltip overlay={<div className="whitespace-nowrap">{overlay}</div>} {...overlapProps}>
                {loading ? <Loading className="mx-2" /> : (
                    <a
                        className={`chatbox-operation-button
                        focus:outline-none
                        hover:text-inherit
                        text-inherit
                        text-decoration-none
                        w-4 h-4
                        opacity-70
                        cursor-pointer`}
                        aria-label={label}
                        role="button"
                        onClick={onClick}
                        tabIndex={0}
                        href="#"
                    >
                        <span
                            className="w-4 h-4"
                            aria-hidden="true"
                            // bca-disable-line
                            dangerouslySetInnerHTML={{__html: icon}}
                        >
                        </span>
                    </a>
                )}
            </Tooltip>
        </ItemContainer>
    );
}

export default ItemTooltipButton;
