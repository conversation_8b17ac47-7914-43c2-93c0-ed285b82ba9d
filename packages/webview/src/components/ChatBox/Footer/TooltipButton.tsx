/* eslint-disable max-len */
/* bca-disable */
import {useCallback, useMemo, useState, useEffect} from 'preact/hooks';
import {ActionType, ActionConfig} from '@comate/plugin-shared-internals';
import wandIcon from '@/assets/wand.svg';
import shinyIcon from '@/assets/shiny.svg';
import acceptIcon from '@/assets/accept.svg';
import checkIcon from '@/assets/check.svg';
import Tooltip from '../../Tooltip';

interface Props {
    actionConfig?: ActionConfig<ActionType>;
    onClick: (data?: ActionConfig<ActionType>['data']) => void;
}

const renderIcon = (icon: string) => {
    switch (icon) {
        case 'shiny':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: shinyIcon}} />;
        case 'accept':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: acceptIcon}} />;
        case 'wand':
            return <div className="w-4 h-4" dangerouslySetInnerHTML={{__html: wandIcon}} />;
        default:
            return null;
    }
};

export default function TooltipButton({actionConfig, onClick}: Props) {
    const [showSuccessLabel, setSuccessLabelVisibility] = useState(false);
    const textClass = actionConfig?.type === 'primary' ? 'text-[var(--comate-link-color)]' : '';

    const overlay = useMemo(
        () => {
            if (!actionConfig || !actionConfig.description) {
                return null;
            }
            return (
                <div className="flex flex-col gap-1">
                    <div>{actionConfig.description}</div>
                    <div className="opacity-70">{actionConfig.subDescription}</div>
                </div>
            );
        },
        [actionConfig]
    );

    const handleActionClick = useCallback(
        () => {
            onClick(actionConfig?.data);
            if (actionConfig?.successLabel) {
                setSuccessLabelVisibility(true);
            }
        },
        [actionConfig, onClick]
    );

    useEffect(
        () => {
            if (showSuccessLabel) {
                const id = setTimeout(
                    () => {
                        setSuccessLabelVisibility(false);
                        clearTimeout(id);
                    },
                    5 * 1000
                );
            }
        },
        [showSuccessLabel]
    );

    if (!actionConfig) {
        return null;
    }

    return (
        <Tooltip overlay={overlay}>
            <button
                disabled={showSuccessLabel}
                className={`${textClass} cursor-pointer flex gap-1 items-center hover:bg-[#FFFFFF]/10 rounded-md py-0.5 px-1`}
                onClick={handleActionClick}
            >
                {showSuccessLabel
                    ? (
                        <>
                            <div className="w-4 h-4 text-green-500" dangerouslySetInnerHTML={{__html: checkIcon}} />
                            <span className="text-green-500">{actionConfig.successLabel}</span>
                        </>
                    )
                    : (
                        <>
                            {actionConfig.icon && renderIcon(actionConfig.icon)}
                            {actionConfig.name}
                        </>
                    )}
            </button>
        </Tooltip>
    );
}
