/* eslint-disable max-len */
import {useCallback, useEffect, useState} from 'preact/hooks';
import {FeedbackOptions, UserFeedback} from '@shared/protocols';
import {useTranslation} from 'react-i18next';
import {
    CommonText,
    FEEDBACK_POPOVER_INCOMPLETE,
    FEEDBACK_POPOVER_INCORRECT,
    FEEDBACK_POPOVER_INPUT_PLACEHOLDER,
    FEEDBACK_POPOVER_IRRELEVANT,
    FEEDBACK_POPOVER_NOHELP,
    FEEDBACK_POPOVER_TITLE,
} from '@/i18n/constants';

interface Props {
    setVisible: (visible: boolean) => void;
    sendFeedback: (options: FeedbackOptions) => void;
}

const defaultUserFeedback: UserFeedback = {
    otherFeedback: '',
    incompleteContent: false,
    incorrectFormat: false,
    irrelevantAnswer: false,
    notHelpful: false,
};

export default function FeedbackPopover({setVisible, sendFeedback}: Props) {
    const [options, setOptions] = useState<UserFeedback>(defaultUserFeedback);
    const {t} = useTranslation();
    const handleChange = useCallback(
        (e: any) => setOptions(pre => ({...pre, otherFeedback: e?.target?.value ?? ''})),
        []
    );

    const incomplete = useCallback(
        (e: any) => setOptions(pre => ({...pre, incompleteContent: e?.target?.checked ?? false})),
        []
    );
    const incorrect = useCallback(
        (e: any) => setOptions(pre => ({...pre, incorrectFormat: e?.target?.checked ?? false})),
        []
    );
    const irrelevant = useCallback(
        (e: any) => setOptions(pre => ({...pre, irrelevantAnswer: e?.target?.checked ?? false})),
        []
    );
    const unhelpful = useCallback(
        (e: any) => setOptions(pre => ({...pre, notHelpful: e?.target?.checked ?? false})),
        []
    );

    const handleCancel = useCallback(
        () => setVisible(false),
        [setVisible]
    );

    const handleSubmit = useCallback(
        () => {
            sendFeedback({userFeedback: options});
            setVisible(false);
        },
        [options, sendFeedback, setVisible]
    );

    useEffect(
        () => {
            const handleClick = (e: MouseEvent) => {
                if (!(e.target as HTMLElement).closest('.feedback')) {
                    setVisible(false);
                }
            };
            document.addEventListener('click', handleClick);
            return () => {
                document.removeEventListener('click', handleClick);
            };
        },
        [setVisible]
    );

    return (
        <div className="feedback absolute right-0 bottom-6">
            <div className="shadow-md bg-[var(--comate-notifications-background)] overflow-hidden rounded px-4 pb-4 pt-5 translate-y-0 border border-[var(--comate-panel-border)]">
                <p className="bottom-1 text-sm">{t(FEEDBACK_POPOVER_TITLE)}</p>
                <div className="flex flex-wrap min-w-[200px] gap-x-4 gap-y-1 mt-2">
                    <label className="flex items-center gap-1">
                        <input type="checkbox" checked={options.incompleteContent} onChange={incomplete} />
                        {t(FEEDBACK_POPOVER_INCOMPLETE)}
                    </label>
                    <label className="flex items-center gap-1">
                        <input type="checkbox" checked={options.incorrectFormat} onChange={incorrect} />
                        {t(FEEDBACK_POPOVER_INCORRECT)}
                    </label>
                    <label className="flex items-center gap-1">
                        <input type="checkbox" checked={options.irrelevantAnswer} onChange={irrelevant} />
                        {t(FEEDBACK_POPOVER_IRRELEVANT)}
                    </label>
                    <label className="flex items-center gap-1">
                        <input type="checkbox" checked={options.notHelpful} onChange={unhelpful} />
                        {t(FEEDBACK_POPOVER_NOHELP)}
                    </label>
                </div>
                <div className="mt-2">
                    <textarea
                        className="w-full rounded bg-[var(--comate-tooltip-background)] text-[var(--comate-input-foreground)] py-2 pl-3 pr-8 resize-none focus:outline-[#6391F9] placeholder:text-[var(--comate-input-placeholderForeground)] placeholder:opacity-90"
                        placeholder={t(FEEDBACK_POPOVER_INPUT_PLACEHOLDER)}
                        value={options.otherFeedback}
                        onInput={handleChange}
                    />
                </div>
                <div className="flex justify-end gap-4 top-1">
                    <button className="cursor-pointer hover:opacity-80" onClick={handleCancel}>
                        {t(CommonText.CANCEL)}
                    </button>
                    <button className="cursor-pointer hover:opacity-80" onClick={handleSubmit}>
                        {t(CommonText.SUBMIT)}
                    </button>
                </div>
            </div>
        </div>
    );
}
