import {EventMessage} from '@shared/protocols';
import {messageHandler} from '@/utils/messageHandler';

interface CheckInParams {
    license: string;
    device: string;
    uuid: string;
}
export function activityCheckInApi({
    license,
    device,
    uuid,
}: CheckInParams) {
    return messageHandler.send(
        EventMessage.PassthroughRequest,
        {
            method: 'GET',
            url: `/api/generate/activity/checkIn?license=${license}&device=${device}&uuid=${uuid}`,
            headers: {
                'Content-Type': 'application/json',
            },
            useBaseUrl: true,
        }
    );
}

export interface Response<T> {
    status: string;
    message?: string;
    data?: T;
}

interface ModelListParams {
    key: string;
    type: 'CHAT' | 'COMPLETION';
}

export interface ModelRes {
    modelId: string;
    modelName: string;
    displayName: string;
    isDefault: boolean;
}

export async function modelListApi({key, type}: ModelListParams) {
    const res = await messageHandler.send(
        EventMessage.PassthroughRequest,
        {
            method: 'GET',
            url: `/api/v2/api/models/available?key=${key}&type=${type}`,
            headers: {
                'Content-Type': 'application/json',
            },
            useBaseUrl: true,
        }
    ) as unknown as {data?: Response<ModelRes[]>};
    return res.data?.data;
}
