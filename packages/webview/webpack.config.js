/* eslint-disable no-console */
'use strict';

const path = require('path');
// const CopyWebpackPlugin = require('copy-webpack-plugin');
const {DefinePlugin} = require('webpack');
const {merge} = require('webpack-merge');
const webviewBase = require('./webpack.webview.base.js');

const platform = process.env.PLATFORM ?? 'internal';
const enterpriseVersion = process.env.ENTERPRISE_VERSION ?? '';
const webviewConsumer = process.env.WEBVIEW_CONSUMER ?? 'vscode';
const environment = process.env.ENVIRONMENT ?? 'production';

console.log('PLATFORM:', platform);
console.log('ENTERPRISE_VERSION:', enterpriseVersion);
console.log('WEBVIEW_CONSUMER:', webviewConsumer);
console.log('ENVIRONMENT:', environment);

const webviewChatEntryPoint = platform === 'internal'
    ? './src/entries/index.tsx'
    : `./src/entries/index.${platform}.tsx`;

const webviewConfig = (env, argv) => {
    return merge(webviewBase(env, argv), {
        name: 'webview',
        entry: {
            'chat': webviewChatEntryPoint,
            'panel': './src/entries/panel.tsx',
        },
        output: {
            filename: '[name].js',
            path: path.resolve(__dirname, '../vscode/dist'), // 开发依赖ide，所以产物直接放vscode目录
        },
        plugins: [
            new DefinePlugin({
                '$features.PLATFORM': JSON.stringify(platform),
                'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
                '$features.WEBVIEW_CONSUMER': JSON.stringify(webviewConsumer),
                '$features.ENVIRONMENT': JSON.stringify(environment),
                '$features.ENTERPRISE_VERSION': JSON.stringify(enterpriseVersion),
            }),
        ],
    });
};

module.exports = [
    webviewConfig,
];
