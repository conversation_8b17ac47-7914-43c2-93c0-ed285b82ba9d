{
  "compilerOptions": {
    "module": "ESNext",
    "moduleResolution": "bundler",
    "target": "es2017",
    "outDir": "./dist/",
    "jsx": "react-jsx",
    "jsxImportSource": "preact",
    "lib": [
      "dom",
      "es6",
      "es2017"
    ],
    "typeRoots": [
      "./node_modules/@types"
    ],
    "rootDirs": [".", "../shared"],
    "sourceMap": false,
    "declaration": false,
    "experimentalDecorators": true,
    "esModuleInterop": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@shared/*": ["../vscode/shared/*"]
    },
    "strict": true, /* enable all strict type-checking options */
    "noImplicitAny": false,
    "resolveJsonModule": true
    /* Additional Checks */
    // "noImplicitReturns": true, /* Report error when not all code paths in function return a value. */
    // "noFallthroughCasesInSwitch": true, /* Report errors for fallthrough cases in switch statement. */
    // "noUnusedParameters": true,  /* Report errors on unused parameters. */
  },
  "include": [
    "./src/**/*",
    "../vscode/shared/**/*"
  ]
}
