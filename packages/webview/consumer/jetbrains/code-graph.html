<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Code Graph</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFangSC-Regular, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            cursor: pointer;
        }

        .tooltip .tooltip-text {
            visibility: hidden;
            width: 298px;
            background-color: #333;
            color: #fff;
            padding: 5px;
            border-radius: 4px;
            position: absolute;
            top: 120%;
            font-size: 12px;
            left: -132px;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            bottom: 100%;
            right: 10px;
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent #333 transparent;
        }

        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
    </style>
    <script src="http://comate/codegraph/lib/echarts.min.js"></script>
</head>

<body style="margin: 0">
    <div id="main" style="width: 100vw; height: 100vh"></div>
    <div class="tooltip" style="position: absolute;top: 12px;right: 12px;z-index: 9999;">
        <div class="tooltip-text">图谱基于代码库索引创建，如需更新，在对话中点击「为当前代码库更新索引」，更新后重新生成对话即可。</div>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd"
                d="M15.0312 8C15.0312 4.11675 11.8833 0.96875 8 0.96875C4.11675 0.96875 0.96875 4.11675 0.96875 8C0.96875 11.8833 4.11675 15.0312 8 15.0312C11.8833 15.0312 15.0312 11.8833 15.0312 8ZM2.21875 8C2.21875 4.8071 4.8071 2.21875 8 2.21875C11.1929 2.21875 13.7812 4.8071 13.7812 8C13.7812 11.1929 11.1929 13.7812 8 13.7812C4.8071 13.7812 2.21875 11.1929 2.21875 8ZM7.35938 10.7479H8.60938V11.9979H7.35938V10.7479ZM8.54745 7.64963C7.76505 8.21261 7.35108 8.95044 7.3577 9.81847L7.35742 10.1202L8.60742 10.1229L8.60767 9.80967C8.60422 9.37121 8.8065 9.00683 9.26508 8.67327L9.27292 8.66759L9.2897 8.65761C10.0335 8.20921 10.5 7.4028 10.5 6.51563C10.5 5.13491 9.38071 4.01562 8 4.01562C6.61929 4.01562 5.5 5.13491 5.5 6.51563H6.75L6.75017 6.49495C6.76121 5.81413 7.31655 5.26562 8 5.26562C8.69036 5.26562 9.25 5.82527 9.25 6.51563C9.25 6.96875 9.00739 7.37889 8.62123 7.60069L8.59549 7.61547L8.57127 7.63263L8.54745 7.64963Z"
                fill="rgba(124, 125, 128, 1)" />
        </svg>
    </div>
    <script type="text/javascript">
        window.send = function (event, data) {
            if (typeof window.sendDataToJava === 'undefined') {
                console.error('window.sendDataToJava is missing, so webview will not work!');
            }
            else {
                const payload = { event, data };
                return new Promise((resolve, reject) => {
                    window.sendDataToJava({
                        request: JSON.stringify(payload),
                        persistent: false,
                        onSuccess: (responseData) => {
                            try {
                                console.log('Extension->Webview(' + event + '-reply):', responseData);
                                const jsonData = JSON.parse(responseData);
                                resolve(jsonData);
                            }
                            catch (e) {
                                console.log('Extension->Webview(reply)(Error): failed to parse response data');
                                reject(e);
                            }
                        },
                        onFailure: (code, message) => {
                            console.error('sendDataToJava received the following error:', code, message);
                            reject(new Error(message));
                        },
                    });
                    console.log('Webview->Extension(' + event + '):', payload);
                });
            }
        }
        const graph = ${{__GRAPH_DATA__}};
        const container = document.getElementById("main");
        const theme = "${{__THEME__}}";
        const chart = echarts.init(container, theme);
        const scale = 1;

        const colors = ['24, 181, 255', '42, 115, 254', '235, 185, 0', '49, 200, 88', '143, 143, 143'];
        const fontColor = ['rgba(59, 59, 59, 1)', 'rgb(212, 212, 212)'];
        const categoryConfig = [
            { symbolSize: 110, value: 110, fontSize: 15, color: `rgb(${colors[0]})` },
            { symbolSize: 39, value: 39, fontSize: 13, color: `rgb(${colors[1]})` },
            { symbolSize: 30, value: 30, fontSize: 12, color: `rgb(${colors[2]})` },
            { symbolSize: 22, value: 22, fontSize: 11, color: `rgb(${colors[3]})` },
            { symbolSize: 16, value: 16, fontSize: 10, color: `rgb(${colors[4]})` },
        ];

        const darkTheme = {
            labelStyle: [
                { fontSize: 15, color: '#FCFDFF' },
                ...colors.slice(1).map((color, i) => {
                    const index = i + 1;
                    const symbolSize = categoryConfig[index].symbolSize * scale;
                    const height = Math.max(symbolSize - 8, symbolSize * 0.8);
                    const fontSize = Math.floor(categoryConfig[index].fontSize * scale);
                    const paddingY = Math.floor((height - fontSize) / 2);
                    const borderRadius = height;
                    const paddingRight = Math.floor(height / 2);
                    const paddingLeft = paddingRight * 2.5;
                    const distance = -1 * height;
                    return {
                        color: fontColor[1],
                        backgroundColor: `rgba(${color}, 0.2)`,
                        borderColor: `rgba(${color}, 0.6)`,
                        borderWidth: 1,
                        borderType: 'solid',
                        padding: [paddingY, paddingRight, paddingY, paddingLeft],
                        borderRadius,
                        fontSize,
                        distance,
                    };
                }),
            ],
            lineStyle: colors.map(color => [`rgba(${color}, 0.3)`, `rgba(${color}, 1)`]),
        };
        const themeVariables = {
            light: { ...darkTheme, labelStyle: darkTheme.labelStyle.map(style => ({ ...style, color: fontColor[0] })) },
            dark: darkTheme,
        };

        const themeConfig = themeVariables[theme]

        const categoryNames = graph.categories.map((c) => c.id);
        const nodeKeyById = graph.nodes.reduce((acc, n) => {
            acc[n.id] = n;
            return acc;
        }, {});

        const nodes = graph.nodes.map((node) => {
            const { value, symbolSize } = categoryConfig[node.category];
            const itemStyle = node.category === 0 ? {
                borderColor: `rgba(${colors[0]}, 0.3)}`,
                borderWidth: 1,
                color: new echarts.graphic.RadialGradient(
                    0.5, 0.5, 0.5, // x, y 中心点和半径
                    [
                        { offset: 0, color: 'rgba(24,181,255,1)' }, // 内部不透明颜色
                        { offset: 0.84, color: 'rgba(24,181,255,1)' }, // 停止透明渐变
                        { offset: 0.86, color: 'rgba(24,181,255,0.2)' }, // 第二层
                    ]
                )
            } : {}
            return {
                ...node,
                label: {
                    position: node.category === 0 ? 'inside' : 'right',
                    ...themeConfig.labelStyle[node.category]
                },
                itemStyle,
                value,
                symbolSize,
            };
        });

        const categories = graph.categories.map((category, i) => {
            return { ...category, itemStyle: { color: categoryConfig[i].color } };
        });

        const links = graph.links.map(link => {
            const sourceCategoryIndex = nodeKeyById[link.source].category;
            const targetCategoryIndex = nodeKeyById[link.target].category;
            const color = themeConfig.lineStyle[sourceCategoryIndex];
            const gradient = new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                { offset: 0, color: color[1] },
                { offset: 1, color: color[0] },
            ])
            return {
                ...link,
                lineStyle: {
                    color: gradient,
                    type: sourceCategoryIndex === 0 && targetCategoryIndex === 1 ? 'dashed' : undefined,
                    width: 2,
                    curveness: 0,
                },
            }
        })

        const option = {
            tooltip: {
                show: false,
            },
            legend: [
                {
                    data: graph.categories.map(function (a) {
                        return a.name;
                    }),
                    top: 12,
                    left: 12,
                    itemWidth: 12,
                    itemHeight: 12,
                },
            ],
            series: [
                {
                    type: "graph",
                    layout: "force",
                    animationEasingUpdate: "quinticInOut",
                    data: nodes,
                    links: links,
                    edgeSymbol: ["none", "arrow"],
                    edgeSymbolSize: [0, 7],
                    categories: categories,
                    roam: true,
                    label: {
                        scale: true,
                        show: true,
                        position: "right",
                        formatter: "{b}",
                    },
                    labelLayout: {
                        hideOverlap: true,
                    },
                    force: {
                        gravity: 0.01,
                        repulsion: 600,
                        layoutAnimation: false,
                        edgeLength: [120, 150],
                    },
                    scaleLimit: {
                        min: 1,
                        max: 4,
                    },
                    emphasis: {
                        focus: "adjacency",
                        itemStyle: {
                            color: 'inherit',
                        }
                    },
                    animation: false,
                },
            ],
        };
        chart.setOption(option);
        chart.on("click", (node) => {
            const send = window.send || console.log;
            send(
                "showCodeBlockEvent",
                {
                    filePath: graph.paths[node.data.pathIndex],
                    type: categoryNames[node.data.category],
                    name: node.name,
                    startLine: node.data.startLine,
                    endLine: node.data.endLine,
                    clickSource: 'codegraph'
                }
            );
        });
        window.addEventListener("resize", function () {
            chart.resize();
        });
    </script>
</body>

</html>