{"name": "webview", "version": "0.9.2", "description": "> TODO: description", "author": "wuweiqi <<EMAIL>>", "homepage": "", "license": "ISC", "main": "lib/webview.js", "files": ["lib"], "publishConfig": {"registry": "http://registry.npm.baidu-int.com"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/ide/comate-plugin-host"}, "scripts": {"test": "vitest", "compile": "npm run compile:css && NODE_ENV=production webpack --mode production --config ./webpack.config.js", "compile:css": "tailwindcss -i ./src/css/tailwind.css -o ./src/css/style.css", "watch": "npm-run-all -p watch:*", "watch:component": "NODE_ENV=development webpack --mode development --watch", "watch:css": "tailwindcss -i ./src/css/tailwind.css -o ./src/css/style.css --watch", "analyze": "export WEBVIEW_CONSUMER=jetbrains && export WEBPACK_MODE=analyze && npm run bundle:webview", "bundle:webview": "npm run compile:css && webpack --mode production --config ./webpack.webview.consumer.js && sh scripts/copyGraphAssets.sh"}, "devDependencies": {"@comate/plugin-jsx": "workspace:^", "@comate/plugin-shared-internals": "workspace:^", "@comate/kernel-shared": "workspace:^", "@preact/preset-vite": "2.8.1", "@reskript/config-lint": "^6.1.1", "@rspack/cli": "^1.0.8", "@rspack/core": "^1.0.8", "@testing-library/preact": "^3.2.3", "@types/jest": "^27.0.1", "@types/lodash": "^4.14.202", "@types/node": "^16.18.21", "@types/raf": "^3.4.3", "@types/react-syntax-highlighter": "^15.5.7", "babel-loader": "^9.1.3", "babel-plugin-module-resolver": "^5.0.2", "confusing-browser-globals": "^1.0.10", "css-loader": "^6.9.1", "eslint": "^8.16.0", "lodash-webpack-plugin": "^0.11.6", "npm-run-all": "^4.1.5", "string-replace-loader": "^3.1.0", "style-loader": "^3.3.4", "svg-inline-loader": "^0.8.2", "tailwindcss": "^3.3.2", "ts-loader": "^9.3.0", "url-loader": "^4.1.1", "vite": "^5.1.6", "webpack": "^5.72.1", "webpack-cli": "^4.9.2", "webpack-merge": "^5.8.0"}, "dependencies": {"@vscode/codicons": "^0.0.35", "classnames": "^2.5.1", "date-fns": "^4.1.0", "echarts": "^5.5.1", "fuse.js": "^7.0.0", "i18next": "^23.10.1", "lodash": "^4.17.21", "material-file-icons": "^2.4.0", "mermaid": "^10.9.1", "preact": "^10.19.5", "raf": "^3.4.1", "rc-checkbox": "^3.2.0", "rc-select": "^14.16.4", "rc-pagination": "^4.3.0", "rc-switch": "^4.1.0", "rc-tooltip": "^6.1.3", "react-datepicker": "^7.5.0", "react-i18next": "^14.1.0", "react-markdown": "9.0.0", "react-mentions": "^4.4.10", "react-syntax-highlighter": "^15.5.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0"}}