'use strict';

const path = require('path');
const rspack = require('@rspack/core');
// const {NormalModuleReplacementPlugin} = require('webpack');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

const webviewConsumer = process.env.WEBVIEW_CONSUMER ?? 'vscode';

const plugins = [
    new rspack.NormalModuleReplacementPlugin(/.*/, resource => {
        if (webviewConsumer === 'vscode') {
            return;
        }
        if (resource.context.includes('node_modules')) {
            return;
        }
        if (resource.request.includes('node_modules')) {
            //
        }
    }),
];

if (process.env.WEBPACK_MODE === 'analyze') {
    plugins.push(new BundleAnalyzerPlugin());
}

module.exports = (_, argv) => {
    return {
        ...(argv.mode !== 'production' && {devtool: 'inline-source-map'}),
        module: {
            rules: [
                {
                    test: /\.tsx?$/,
                    use: {
                        loader: 'babel-loader',
                        options: {
                            presets: [
                                '@babel/preset-env',
                                '@babel/preset-react',
                                '@babel/preset-typescript',
                            ],
                            plugins: [
                                'lodash',
                                ['@babel/plugin-transform-modules-commonjs', {
                                    'allowTopLevelThis': true,
                                }],
                                ['module-resolver', {
                                    'root': ['./'],
                                    'alias': {
                                        '@': './src',
                                        '@shared': '../vscode/shared',
                                    },
                                }],
                                [
                                    '@babel/plugin-transform-react-jsx',
                                    {
                                        'runtime': 'automatic',
                                        'importSource': 'preact',
                                    },
                                ],
                            ],
                        },
                    },
                    exclude: /node_modules/,
                },
                {
                    test: /\.css/,
                    use: ['style-loader', 'css-loader'],
                },
                {
                    // @vscode/codicons/dist/codicon.css 文件用 url() 加载 ttf 文件时路径有 ?xxx 后缀，
                    // 导致部分老版本 JetBrains IDE 无法正常加载字体文件，这里用一个 loader 字符串替换来修复
                    test: /codicon.css$/,
                    loader: 'string-replace-loader',
                    options: {
                        search: /codicon.ttf\?\w*/,
                        replace: 'codicon.ttf',
                    },
                },
                {
                    test: /\.less$/i,
                    use: [
                        'style-loader',
                        {
                            loader: 'css-loader',
                            options: {
                                modules: true,
                            },
                        },
                        'less-loader',
                        {
                            loader: 'style-resources-loader',
                            options: {
                                patterns: path.resolve(__dirname, './*/src/styles/*.var.less'),
                                injector: 'append',
                            },
                        },
                    ],
                },
                {
                    test: /\.svg/,
                    use: ['svg-inline-loader'],
                },
                {
                    test: /\.(png|jpg|gif`)$/i,
                    use: [
                        {
                            loader: 'url-loader',
                            options: {
                                limit: 8 * 1024,
                            },
                        },
                    ],
                },
            ],
        },
        resolve: {
            modules: ['node_modules', path.resolve('./node_modules')],
            // 本地有效但打包有问题
            // symlinks: true,
            extensions: ['.tsx', '.ts', '.js', '.svg'],
            alias: {
                '@': path.resolve(__dirname, 'src'),
                'react': 'preact/compat',
                'react-dom': 'preact/compat',
                'react/jsx-runtime': 'preact/jsx-runtime',
                '@shared': path.resolve(__dirname, '../vscode/shared'),
            },
        },
        plugins,
    };
};
