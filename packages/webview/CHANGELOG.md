# WebView、Engine 更新文档

> 请注意，WebView 和 Engine 的修改需要插件端对应修改，需要在修改能里前标注❌，标识 break change

## 2024-11-26

* 增加 DefaultModelSelectEvent UpdateDefaultModelSelectEvent 方法，选择对话的模型

## 2024-11-20

* 增加 downloadLogEvent 事件，当用户点击顶部菜单更多操作中的「下载日志」时触发（目前只在 VS Code 展示）

## 2024-11-14

* 修复三方插件推荐不能自动清除的问题
* 修复部分情况下自动应用推荐的插件能力影响体验的问题

## 2024-10-31

* 优化侧边栏不同高度的响应式布局

## 2024-10-24

* Engine 写入日志添加 pid 字段

## 2024-09-05

* 修复点击推荐文案没有效果的问题
* 开放平台GDP插件添加推荐效果

## 2024-08-26

* 增加 toastMessageChangeEvent 事件，支持在webview显示toastMessage


## 2024-8-16

* ❌ licenseTypeEvent 更名为 licenseFullDetailFetchEvent
* ❌ 添加 licenseFullDetailChangeEvent


## 2024-08-15

* 增加 repoGuideFetchEvent 事件，#知识 弹框出现时请求这个事件获取 hover 时的解释和例子

## 2024-08-08

* 在文件中检测到飞桨关键词自动选择飞桨插件智能问答

## 2024-07-25

*  更新文心快码 logo
*  顶部用户名hover后的信息框适配浅色模式

## 2024-07-22

*  JetBrains 去掉旧版引导

## 2024-07-15

* ❌ xcode 中，xcode 执行 executeXcodeCallbackFunction 方法执行回调，webview 调用 window.webkit.messageHandlers.xcodeHandler.postMessage 向 xcode 发送消息

## 2024-07-15

* ❌ JetBrains saas版本支持网络检索

## 2024-07-11

* 更新背景logo

## 2024-07-08

* ❌ 首页引导支持换一换

## 2024-07-05

* AutoWorkAPIProxy 增加 getAccessTo/setChatIntentRecognition/getChatIntentRecognition 方法

## 2024-07-04

* AutoWorkAPIProxy 增加 fetchKnowledgeList 方法

## 2024-06-27

* JetBrains saas版本屏蔽网络检索

## 2024-06-25

* 更换安全标识 tooltip 实现，解决部分因浏览器兼容性问题无法出现的情况

## 2024-06-20

* ❌ JetBrains支持网络检索

## 2024-06-19

* JetBrains增加知识集配置禁用和引导

## 2024-06-13

* ❌ engine支持动态地址请求知识中心
* ❌ 增加CustomizeUserFetchEvent和CustomizeUserChangeEvent两个事件，用于获取用户是否是混合云账号

## 2024-6-7

* ❌ 修改webview请求的BannerVersionFetchEvent事件，修改返回数据

## 2024-5-27

* JetBrains首页保留试一试引导和suggestions

## 2024-5-30

* ❌ 添加个人信息、退出登录等相关逻辑，需要插件端配合

## 2024-5-27

* ❌ 首页增加用户引导及相关埋点统计
* 首页隐藏试一试引导和suggestions

## 2024-5-24

* 兼容 gitee 版本
* 隐藏知识集引用、开放平台入口、反馈入口、活动入口、购买引导
* 隐藏 autowork 单测、生成api功能
* 隐藏 第三方Agent和commands
* packages/vscode/shared/utils/features.ts 中兼容是 gitee 版本的各种显隐 visible、标题文案

## 2024-05-23

* 优化侧边栏对话框插件候选逻辑，增加相应快捷键

## 2024-5-17

* AutoWork saas版本 隐藏TestMate和iapi两个指令（本期不涉及JetBrains）
* JetBrains 内部版本 新增TestMate和iapi两个指令,使用slash为智能问答-V2，修改iapi和网页两个分类的类型
* AutoWork 新增网络检索能力（本期不涉及JetBrains）

## 2024-5-15

* AutoWork Ask模式，vscode使用slash为智能问答-V2， JetBrains使用slash为智能问答

## 2024-5-13

* AutoWork使用iapi指令时增加分类选择禁用规则（本期不涉及JetBrains）

## 2024-5-13

* 修复popover展示问题
* AutoWork修改iapi和网页两个分类的类型（本期不涉及JetBrains）,IAPI项目类型为API_PROJECT；IAPI单个接口类型为API
* AutoWork iapi的id格式调整，vscode 插件的 uuid 格式为 123456 Jetbrains 插件的 uuid 格式为 IAPI-123456

## 2024-5-11

* AutoWork新增TestMate和iapi两个指令（本期不涉及JetBrains）

## 2024-5-10

* 解决JetBrains输入卡顿问题

## 2024-5-6

* ❌ 输入框支持上下键服用历史提问内容，新增了两个事件（inputHistoryFetchEvent, inputHistoryRefreshEvent）

## 2024-4-30

* 建议中的生成注释使用codelens的能力

## 2024-4-28

* 找回JetBrains侧调优建议的auowork引导，待插件端适配后下掉
* 修复输入框在 JetBrains 侧 Windows 端快捷键展示错误的问题

## 2024-4-26

* 修复输入框文字颜色在 Visual Studio Light/Light+ 两个主题下展示异常的问题
* ❌ 修改webview请求的BannerVersionFetchEvent事件，修改返回数据
* JetBrains侧调优建议调用原comate一方能力

## 2024-4-25

* ❌ 将ide所有的环境变量透传给 engine
* ❌ 上传engine侧日志
* ❌ 根据用户编辑的内容智能推荐插件

## 2024-4-25 17:00（案例）

* ❌ engine 添加新事件，ACTION_FRESH
* engine 合并进程
* webview 修改建议字体大小
