const restrictedGlobals = require('confusing-browser-globals');

module.exports = {
    extends: require.resolve('@reskript/config-lint/config/eslint'),
    parserOptions: {
        // true indicates to find the closest tsconfig.json for each source file
        project: true,
    },
    rules: {
        'no-underscore-dangle': 'off',
        'react/no-danger': 'off', // 允许在 jsx 中使用 dangerouslySetInnerHTML，目前引用图标都是这么写的
        '@typescript-eslint/restrict-plus-operands': 'off', // 此条规则限制了 number + string，对于现在的代码状态过于严格
        '@typescript-eslint/member-ordering': 'off', // 对于 class field, constructor 和 static method 的顺序定义有问题
        '@typescript-eslint/no-redeclare': 'off', // 有 bug
        'no-restricted-globals': ['error'].concat(restrictedGlobals), // 防止容易混淆的 global 变量，如果使用必须从 window 引用
        'react/jsx-no-bind': [
            'error',
            {
                'ignoreDOMComponents': true,
            },
        ],
        'generator-star-spacing': 'off',
        'react/jsx-wrap-multilines': [ // 强制 jsx 对齐
            'error',
            {
                'declaration': 'parens-new-line',
                'assignment': 'parens-new-line',
                'return': 'parens-new-line',
                'arrow': 'parens-new-line',
                'condition': 'parens-new-line',
                'logical': 'parens-new-line',
            },
        ],
    },
};
