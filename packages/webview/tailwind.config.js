/** @type {import('tailwindcss').Config} */
module.exports = {
    content: ['./src/**/*.tsx'],
    theme: {
        extend: {
            keyframes: {
                appear: {
                    '0%': {
                        opacity: '0',
                    },
                    '50%': {
                        opacity: '0',
                    },
                    '100%': {
                        opacity: '1',
                    },
                },
                gradient: {
                    '0%': {
                        'background-position': '0% 50%',
                    },
                    '50%': {
                        'background-position': '100% 50%',
                    },
                    '100%': {
                        'background-position': '0% 50%',
                    },
                },
                shark: {
                    '0%': {
                        'background-position': '-100%',
                    },
                    '100%': {
                        'background-position': '150%',
                    },
                },
                blink: {
                    '0%': {
                        visibility: 'visible',
                    },
                    '50%': {
                        visibility: 'hidden',
                    },
                    '100%': {
                        visibility: 'hidden',
                    },
                },
                scale: {
                    '0%': {
                        transform: 'scale(1)',
                    },
                    '50%': {
                        transform: 'scale(1.04)',
                    },
                    '100%': {
                        transform: 'scale(1)',
                    },
                },
            },
            zIndex: {
                '100': '100',
            },
        },
        animation: {
            appear: 'appear 3s ease-in-out',
            gradient: 'gradient 3s ease-in-out infinite;',
            blink: 'blink 1.5s ease-in-out infinite;',
            scale: 'scale 2s ease-in-out infinite',
            shark: 'shark 3s  ease-in-out infinite',
        },
    },
    plugins: [],
};
