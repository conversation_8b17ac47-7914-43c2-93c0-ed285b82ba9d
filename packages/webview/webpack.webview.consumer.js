'use strict';
const path = require('path');
const {merge} = require('webpack-merge');
const webviewBase = require('./webpack.webview.base.js');
const {DefinePlugin, NormalModuleReplacementPlugin} = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const {CleanWebpackPlugin} = require('clean-webpack-plugin');

const platform = process.env.PLATFORM ?? 'internal';
const webviewConsumer = process.env.WEBVIEW_CONSUMER;
const environment = process.env.ENVIRONMENT ?? 'production';
const enterpriseVersion = process.env.ENTERPRISE_VERSION;


const webviewChatEntryPoint = platform === 'internal'
    ? './src/entries/index.tsx'
    : `./src/entries/index.${platform}.tsx`;

if (!webviewConsumer) {
    console.error('Error: WEBVIEW_CONSUMER env variable is not set!');
    process.exit(1);
}

module.exports = (env, argv) => {
    if (argv.mode !== 'production') {
        console.error('Error: this configuration should only be used for production!');
        process.exit(1);
    }
    const distFolder = path.resolve(__dirname, 'consumer', 'dist', webviewConsumer);
    return merge(webviewBase(env, argv), {
        entry: {
            'chat': webviewChatEntryPoint,
        },
        output: {
            filename: '[name].js',
            chunkFilename: '[name].chunk.js',
            path: distFolder,
            clean: true,
        },
        module: {
            rules: [
                {
                    test: /\.js?$/,
                    include: /node_modules\/(react-markdown|mermaid)/,
                    use: {
                        loader: 'babel-loader',
                        options: {
                            plugins: [
                                'babel-plugin-transform-object-hasown',
                            ],
                        },
                    },
                },
            ],
        },
        optimization: {
            splitChunks: {
                chunks: 'async',
                cacheGroups: {
                    defaultVendors: false,
                    default: false,

                },
            },
        },
        plugins: [
            new DefinePlugin({
                '$features.WEBVIEW_CONSUMER': JSON.stringify(webviewConsumer),
                '$features.PLATFORM': JSON.stringify(process.env.PLATFORM),
                '$features.ENVIRONMENT': JSON.stringify(environment),
                '$features.ENTERPRISE_VERSION': JSON.stringify(enterpriseVersion),
            }),
            new NormalModuleReplacementPlugin(/.*\/messageHandler$/, resource => {
                if (webviewConsumer !== 'vscode') {
                    // eslint-disable-next-line max-len
                    resource.request = resource.request.replace(/messageHandler$/, `messageHandler.${webviewConsumer}`);
                }
            }),
            new CleanWebpackPlugin({
                cleanOnceBeforeBuildPatterns: [
                    distFolder,
                ],
            }),
            new CopyWebpackPlugin({
                patterns: [
                    {
                        from: path.resolve(__dirname, 'consumer', webviewConsumer),
                        to: distFolder,
                    },
                ],
            }),
        ],
    });
};
