// / <reference types="vitest" />
// / <reference types="vite/client" />
import path from 'path';
import {defineConfig} from 'vite';
import preact from '@preact/preset-vite';
export default defineConfig({
    plugins: [preact()],
    resolve: {
        alias: {
            '@shared': path.resolve(__dirname, '../vscode/shared'),
            '@': path.resolve(__dirname, './src'),
        },
    },
    test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: './test/setup.ts',
        // css: true,
    },
});
