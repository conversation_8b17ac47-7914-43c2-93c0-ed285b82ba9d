---
sidebar_position: 3
---

# 实现一个定时器

:::info
这一章节只是简单地展示插件开发的过程与骨架，并非真正实现一个智能化的插件，也并不会用到大模型。
:::

现在让我们试着在插件上加一个“倒计时”的功能，用户可以输入一个时间后，每秒显示剩余秒数。

为此，我们在`src/providers`下新建一个`countdown.ts`文件，先将以下代码复制到文件中，具体的代码分析我们放到后面说。

```ts
import {
    SkillProvider,
    FunctionParameterDefinition,
    TaskProgressChunk,
    TaskProgressChunkStream,
} from '@comate/plugin-host';

function sleepForOneSecond() {
    return new Promise(r => setTimeout(r, 1000));
}

interface Args {
    seconds: number;
}

export class CountdownSkillProvider extends SkillProvider<Args> {
    static skillName = 'countdown';

    static description = '开始倒计时，以秒为单位';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {
            seconds: {
                type: 'number',
                description: '倒计时秒数',
            },
        },
        required: ['seconds'],
    };

    protected validateArgument(arg: any): arg is Args {
        return typeof arg?.seconds === 'number' && arg.seconds > 0;
    }

    protected async *execute({seconds}: Args): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        yield stream.flush(`现在开始为你倒计时${seconds}秒`);

        const state = {
            timer: null,
            remaining: seconds - 1,
        };
        while (state.remaining > 0) {
            await sleepForOneSecond();
            yield stream.flush(`\n\n还剩${state.remaining}秒`);
            state.remaining--;
        }

        yield stream.flush('\n\n倒计时结束');
    }
}
```

随后，打开`package.json`，找到`comate.capabilities`数组，在数组里加上以下这个JSON对象。

```json
{
  "type": "Skill",
  "name": "countdown",
  "displayName": "倒计时",
  "description": "按指定时间进行倒计时，每1秒提示一次"
}
```

最后，打开`src/index.ts`，先加入一行`import`语句引入`CountdownSkillProvider`，并在`setup`函数中注册，最终全文件的代码是这样的。

```ts
import {PluginSetupContext} from '@comate/plugin-host';
import {HelloFallbackProvider} from './providers/hello.js';
import {CountdownSkillProvider} from './providers/countdown.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerFallbackProvider('hello', HelloFallbackProvider);
    registry.registerSkillProvider('countdown', CountdownSkillProvider);
}
```

在更新完后，重新执行`npm run dev`运行插件，你可以看到插件中多了一个“倒计时”的功能。选中它，试着随意描述希望的时间。

![](assets/countdown-screenshot.png)

下面，我们简单地从`countdown.ts`文件拆解一下开发一个能力的大致结构，更深入详细的说明可以参考后续文档。

- `interface Args`：用TypeScript类型声明执行这个能力时要的参数结构，它**必须是一个对象**。
- `class CountdownSkillProvider extends SkillProvider`：这里我们选择`SkillProvider`作为基类来声明一项“技能”。
- `static skillName`：声明了我们技能的名字，大模型会将它视为一个函数来使用。
- `static displayName`：这是用户界面上能看到的名字。
- `static description`：对技能的说明，大模型也会使用这个说明来选择是不是需要调用以及生成什么样的参数。
- `static parameters`：使用JSON Schema来声明参数结构，它与一开始的`Args`类型是一致的。
- `validateArgument`：校验大模型生成的参数是不是结构正确，这里我们检查了确实有`seconds`属性并且为正数。
- `execute`：真正执行逻辑，此时大模型已经帮助我们生成了参数`seconds`。

在`execute`方法中，我们使用`yield`来输出信息，由于我们每次获取的都是增量的新内容，所以使用`TaskProgressChunkStream`这个类来帮我们进行流式输出。定时器的`execute`方法每1秒都会调用`stream.flush()`方法，输出一个剩余时间，并在最后输出“倒计时结束”。

在实现了逻辑后，修改`package.json`与`index.ts`中的`setup`函数，使这个类能够被插件感知到，我们就完成了一个能力的开发。
