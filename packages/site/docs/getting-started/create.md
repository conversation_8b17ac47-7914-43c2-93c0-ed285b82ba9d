---
sidebar_position: 2
---

# 开发第一个插件

你可以通过以下命令从我们提供的模板中初始化一个Comate+插件项目。

```shell
npx @comate/plugin-init@latest my-comate-plugin
```

在命令执行后，你可以进入`my-comate-plugin`目录，使用你的包管理器安装依赖。

```shell
cd my-comate-plugin
npm install
```

安装完成后，项目已经处于可运行的状态，通过`dev`脚本来启动项目。

```shell
npm run dev
```

命令行中会出现构建中的提示，完成后随命令行的提示通过浏览器打开页面，你可以看到一个用于调试插件功能的界面。你可以在界面左下角选择当前的插件，输入一些内容来观察调用插件的效果。

![](assets/initial-plugin-screenshot.png)

至此，你已经完成了插件项目的初始化，你可以进一步查看`package.json`中的`comate`字段、`src/index.ts`、`src/providers`下的各文件来大致了解插件的配置和代码。通过进一步翻阅文档，你将会逐渐了解如何进行插件能力的开发、构建、发布。
