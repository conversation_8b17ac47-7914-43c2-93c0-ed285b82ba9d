---
sidebar_position: 8
---

# 渲染辅助

Markdown格式是插件与用户交互时很重要的一种格式。在`Provider`类中的`this.render`为你提供了一些辅助生成Markdown内容的方法，它对应一个`MarkdownRender`方法。

```ts
interface TableRenderColumnUsable<K extends keyof T, T> {
    /** 列标题 */
    title: string;
    /** 该列对应的属性 */
    field: keyof T;
    /** 将属性值转化为字符串 */
    format?: (value: T[K]) => string;
}

type TableRenderColumnMap<T> = { [K in keyof T]: TableRenderColumnUsable<K, T> };

/**
 * 配置表格展示的列
 */
type TableRenderColumn<T> = TableRenderColumnMap<T>[keyof T];

/** 渲染表格的相关配置 */
interface TableRenderOptions<T> {
    /** 配置列展示 */
    columns: Array<TableRenderColumn<T>>;
}

class MarkdownRender {
    /**
     * 将数据格式化为Markdown表格
     *
     * @param data 数据数组
     * @param options 表格渲染选项
     * @returns 对应表格的Markdown字符串
     */
    table<T>(data: T[], options: TableRenderOptions<T>) {
        throw new Error('Not implemented.');
    }
}
```

当前`MarkdownRender`提供了生成表格的方法。
