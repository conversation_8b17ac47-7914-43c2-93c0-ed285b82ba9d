---
sidebar_position: 1
---

# 获取配置

每一个插件都可以通过`package.json`中的`comate.configSchema`字段声明自己的配置。这些配置最终会渲染为表单供用户填写，而插件则可以在自己的逻辑中获取这些配置。

对于每一种`Provider`基类，都会有一个`config`属性来提供配置的读取，它返回一个`PluginConfig`类。

```ts
/**
 * 支持获取插件的配置值
 */
interface PluginConfig {
    /**
     * 根据键值获取插件的配置值，可以指定默认值
     *
     * @param key 键值
     * @param defaultValue 默认值（可选）
     * @returns 获取配置值后返回对应的字符串
     */
    getAsString(key: string, defaultValue?: string): Promise<string>;

    /**
     * 根据键值获取插件的配置值，可以指定默认值
     *
     * @param key 键值
     * @param defaultValue 默认值（可选）
     * @returns 获取配置值后返回对应的字符串
     */
    getAsNumber(key: string, defaultValue?: number): Promise<number>;

    /**
     * 根据键值获取插件的配置值，可以指定默认值
     *
     * @param key 键值
     * @param defaultValue 默认值（可选）
     * @returns 获取配置值后返回对应的字符串
     */
    getAsBoolean(key: string, defaultValue?: boolean): Promise<boolean>;

    /**
     * 根据键值获取插件的配置值
     *
     * @param key 键值
     * @returns 获取配置值后返回对应的字符串
     */
    getAsStringArray(key: string): Promise<string[]>;

    /**
     * 根据键值获取插件的配置值
     *
     * @param key 键值
     * @returns 获取配置值后返回对应的字符串
     */
    getAsNumberArray(key: string): Promise<number[]>;

    /**
     * 根据键值获取插件的配置值
     *
     * @param key 键值
     * @returns 获取配置值后返回对应的字符串
     */
    getAsBooleanArray(key: string): Promise<boolean[]>;
}
```

:::important
请务必在获取配置时使用与你的配置项类型相符的方法，错误的强制类型转换可能导致你得到不符预期的值。
:::

你可以通过`this.config.getAsXxx`获取指定名称的配置项，并可选地使用`defaultValue`来提供用户未配置时的默认值，当配置项是一个数组时，默认值为空数组。

获取插件配置项是一个异步的过程，但它的读取过程不应该会严重影响到你的逻辑执行，因此我们不建议从性能角度考虑使用`Promise.all`读取多个配置。
