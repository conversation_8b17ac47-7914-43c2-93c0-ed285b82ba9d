---
sidebar_position: 6
---

# 关联信息处理

在`Provider`类的实现中，可以使用`this.retriever`得到一个信息获取器，这个对象将会负责提供代码库、工作台等静态信息，以及用户私域知识。

## 获取代码库信息

使用`this.retriever.repositoryInfo()`能够拿到代码库的信息：

```ts
const repository = await this.retriever.repositoryInfo();
```

它将根据当前的源码管理工具返回不同的类型，当前仅支持Git代码库：

```ts
interface GitRepositoryInfo {
    versionControl: 'git';
    repositoryUrl: string | null;
    branch: string;
}

interface NoneRepositoryInfo {
    versionControl: 'none';
}

type RepositoryInfo = GitRepositoryInfo | NoneRepositoryInfo;
```

如果当前代码库并不是一个Git仓库，会返回`{versionControl: 'none'}`。

对于Git仓库，返回的对象中会有以下属性：

- `repositoryUrl`：表示Git的远端地址，如果仓库并没有一个远端（纯本地仓库），则为`null`。
- `branch`：当前分支名，如果并不在分支上，则是空字符串。

:::note
Git的远端地址会根据用户使用习惯不同，有诸如`https://`、`ssh://`等协议，以及在URL中带有如`git@`的用户名部分，如果需要提供代码库名，你需要自己进行解析。对于常见的代码托管平台，[hosted-git-info](https://github.com/npm/hosted-git-info)库可以帮助到欠。
:::

## 获取宿主信息

宿主即运行插件的主环境，它包括在IDE中运行或开发调试的特殊环境。你可以使用`this.retriever.hostInfo()`获取宿主信息：

```ts
const host = await this.retriever.hostInfo();
```

一个宿主信息结构如下：

```ts
/** 插件宿主环境信息 */
interface HostInfo {
    /** 运行插件的IDE环境，如`"VS Code"`、`"Playground"` */
    appName: string;
    /** 是否运行在调试模式下 */
    debugMode: boolean;
}
```

其中`appName`的已知值有：

- `VS Code`：在VS Code中运行。
- `Playground`：通过`cpd dev`命令行调试时的值。

## 私域知识获取

:::note
暂未开放
:::
