---
sidebar_position: 5
---

# 文件系统

Comate+插件会运行在一个沙盒环境中，默认无法访问文件系统。对于有读取、写入文件需要的，`Provider`类提供了`requestWorkspaceFileSystem`和`requestFullDiskFileSystem`两个方法获取对文件读写的实现。

:::important
不要在插件中直接使用`node:fs`模块，Comate不保证原生的FS模块可用。
:::

这2个方法均会进行用户的授权，用户同意后返回一个`FileSystem`对象，拒绝则返回`false`。其中`requestWorkspaceFileSystem`支持读写当前项目下的文件，而`requestFullDiskFileSystem`则能够读取全硬盘的文件，用户在请求授权时会看到不同的文案说明。

:::note
如果当前IDE下没有打开的项目，则`requestWorkspaceFileSystem`会返回`null`。
:::

```ts
type FileEntryType = 'file' | 'directory' | 'symlink' | 'block' | 'character' | 'fifo' | 'socket';

interface FileStat {
    type: FileEntryType;
    size: number;
    atimeMs: number;
    ctimeMs: number;
    mtimeMs: number;
}

interface FileEntry {
    name: string;
    type: FileEntryType;
}

interface FileSystem {
    /**
     * 读取一个文件内容
     *
     * @param filePath 文件路径
     * @returns 文件的二进制内容
     */
    readFile(filePath: string): Promise<Buffer>;

    /**
     * 使用指定编码读取一个文件内容
     *
     * @param filePath 文件路径
     * @param encoding 对应的编码
     * @returns 文件内容使用指定编码解码后的字符串
     */
    readFile(filePath: string, encoding: BufferEncoding): Promise<string>;

    /**
     * 读取一个目录下的所有文件名
     *
     * @param filePath 目录路径
     * @returns 目录下的文件列表
     */
    readDirectory(directoryPath: string): Promise<FileEntry[]>;

    /**
     * 向一个文件中写入内容
     *
     * @param filePath 文件路径
     * @param content 写入的内容
     */
    writeFile(filePath: string, content: string | Buffer): Promise<void>;

    /**
     * 获取文件的统计信息
     *
     * @param filePath 文件路径
     * @returns 文件统计信息
     */
    stat(filePath: string): Promise<FileStat>;
}
```

与获取用户详细信息类似，用户只需要进行一次授权，无论同意还是拒绝Comate都会将结果用于未来的文件系统请求。

一个`FileSystem`对象提供了最小的文件读写能力，包括列出目录下文件、读取文件、写入文件。其中`stat`方法与NodeJS的`fs.stat`有所不同，它返回一个固定结构的数据对象，不包含任何方法，仅提供文件类型（`type`）、大小（`size`）、时间戳（`atimeMs`、`ctimeMs`、`mtimeMs`）等信息。
