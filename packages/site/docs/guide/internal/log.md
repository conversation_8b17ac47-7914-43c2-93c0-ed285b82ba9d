---
sidebar_position: 7
---

# 日志记录

作为开发者，自然会需要在逻辑中进行日志记录。`Provider`类中的`this.logger`属性提供的`Logger`对象可以简单地记录日志。

```ts
interface Logger {
    /**
     * 以指定的源生成一个新的日志对象
     *
     * @param source 指定日志源
     */
    for(source: string): Logger;

    /**
     * 以`verbose`级别打印日志
     *
     * @param actionOrContent 日志内容或当前日志的动作
     * @param detail 可选的详情对象，当有该参数时，`actionOrContent`会被认为是当前日志的动作
     */
    verbose(actionOrContent: string, detail?: Record<string, any>): void;

    /**
     * 以`info`级别打印日志
     *
     * @param actionOrContent 日志内容或当前日志的动作
     * @param detail 可选的详情对象，当有该参数时，`actionOrContent`会被认为是当前日志的动作
     */
    info(actionOrContent: string, detail?: Record<string, any>): void;

    /**
     * 以`warn`级别打印日志
     *
     * @param actionOrContent 日志内容或当前日志的动作
     * @param detail 可选的详情对象，当有该参数时，`actionOrContent`会被认为是当前日志的动作
     */
    warn(actionOrContent: string, detail?: Record<string, any>): void;

    /**
     * 以`error`级别打印日志
     *
     * @param actionOrContent 日志内容或当前日志的动作
     * @param detail 可选的详情对象，当有该参数时，`actionOrContent`会被认为是当前日志的动作
     */
    error(actionOrContent: string, detail?: Record<string, any>): void;
}
```

与大多数日志类相同，使用`verbose`、`info`、`warn`和`error`可以记录各级别的日志。

## 记录JSON日志

我们强烈推荐以JSON的格式记录日志，这将使你能够保存更丰富的信息，也更利于结构化的检索和汇聚。

当日志方法（`verbose`、`info`、`warn`和`error`）传递2个参数时，第1个参数被认为是日志的动作，这个参数值会被用来做日志的简单展示并可用于快速查询。第2个参数是需要保存的JSON对象，所有可被序列化的属性都将被记录下来。

例如一个插件在对代码进行分析后，解析出了一些函数名和类名，我们希望记录下来：

````ts
this.logger.info(
    'ParseCode',
    {
        foundFunctionNames,
        foundClassNames,
    }
);
```

这相比把日志格式化成一个字符串，能显著地提升可存储的信息量和未来的查看效率。

:::note
当日志类的方法只传一个字符串参数时，会默认变成一个`action`为`"Log"`的JSON日志。
:::

## 绑定到其它类

在日志体系中，我们往往会希望能够记录日志从哪一个类记录的。

当你在`Provider`中调用`this.logger`时，日志内容中会默认加上你的类名。当你要在其它类中也调用日志时，可以使用`logger.for(name)`来生成一个新的日志对象使用。

```ts
class MyHelper {
    private readonly logger: Logger;

    constructor(logger: Logger) {
        this.logger = logger.for(this.constructor.name);
    }
}

new MyHelper(this.logger);
````

此后，在`MyHelper`中打印的日志都会带上`MyHelper`作为`source`字段。
