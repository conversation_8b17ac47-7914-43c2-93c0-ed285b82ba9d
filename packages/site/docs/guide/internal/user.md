---
sidebar_position: 3
---

# 用户信息

各个`Provider`类都能使用`this.currentUser`来获取用户的信息，这个属性对应一个`User`对象。

```ts
/**
 * 用户的详细信息，仅授权后才可获取
 */
interface UserDetail {
    /** 用户名，如邮箱前缀 */
    readonly name: string;

    /** 用户显示的名称，如中文姓名 */
    readonly displayName: string;

    /** 用户的完整电子邮件地址 */
    readonly email: string;
}

/**
 * 表示一个用户的信息
 */
export class User {
    /** Comate的用户ID，仅在Comate中代表唯一用户 */
    readonly id: string;

    /**
     * 申请获取用户详细信息
     *
     * @returns用户授权后，返回完整的用户信息，拒绝则返回`false`
     */
    async requestDetail(): Promise<UserDetail | false>;
}
```

在默认情况下，你可以通过`this.currentUser.id`拿到一个虚拟的用户ID，这个ID代表唯一一个用户，但不包含任何用户的具体信息。

如果需要得到更丰富的用户信息，你可以调用`this.currentUser.requestDetail`方法，该方法会在用户允许授权的情况下返回详细用户信息，未授权则返回`false`。

当你使用诸如`const userDetail = await this.currentUser.requestDetail()`的代码时，第一次执行会向用户展示一个授权的窗口，用户允许授权后返回。当用户完成一次授权（无论允许还是拒绝）后，后续的`requestDetail()`调用均不会再向用户展示授权，而是会直接根据授权状态返回结果。
