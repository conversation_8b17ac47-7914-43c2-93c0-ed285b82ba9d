---
sidebar_position: 2
---

# 用户IDE现场

在`Provider`类的实现中，使用`this.currentContext`能够获取用户调用插件时的现场信息，该属性返回一个`ActivationContext`对象。

```ts
interface ActivationContext {
    /**
     * 用户在对话界面输入的内容
     */
    readonly query: string;

    /**
     * 当前编辑器中用户选中的代码块
     */
    readonly selectedCode: string;

    /**
     * 当前编辑器中打开的文件的全部内容
     */
    readonly activeFileContent: string;

    /**
     * 当前编辑器中打开的文件路径，是一个相对于项目的相对路径
     */
    readonly activeFilePath: string;

    /**
     * 与activeFilePath类似，但只有最后文件名部分，不包含文件目录
     */
    readonly activeFileName: string;

    /**
     * 当前编辑器打开的文件的编程语言
     */
    readonly activeFileLanguage: string;
}
```

针对activeFileLanguage，参考[Language Identifier](https://code.visualstudio.com/docs/languages/identifiers)获得所有语言的列表。

插件中的`currentContext`对象只代表用户触发时刻的信息快照，当触发后用户切换打开文件、对话界面输入其它内容，都不会影响`currentContext`中的属性值，直到下一次插件被调用并生成新的`Provider`类实例时，才会拥有不同的`currentContext`对象。
