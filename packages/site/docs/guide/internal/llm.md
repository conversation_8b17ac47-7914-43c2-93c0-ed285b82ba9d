---
sidebar_position: 4
---

# 调用大模型

作为智能化的研发平台，与大模型进行交互是不可或缺的能力。在`Provider`类中，Comate提供了`this.llm`属性帮助调用大模型，这个属性返回一个`SuppotingLlm`对象。

```ts
interface RunnablePrompt {
    promptTemplate: string;
    args: Record<string, any> | undefined;
}

interface FunctionCall {
    name: string;
    arguments: string;
    thoughts?: string;
}

enum TextModel {
    Default = 'default-model',
    ErnieBot = 'ernie-bot',
    ErnieBot4 = 'ernie-bot-4',
}

enum FunctionModel {
    Default = 'default-model',
    ErnieBot = TextModel.ErnieBot,
    ErnieBot4 = TextModel.ErnieBot4,
}

interface TextModelOptions {
    model?: TextModel;
    modelOptions?: ModelOptions;
}

interface FunctionModelOptions {
    model?: FunctionModel;
}

interface CodeSpecificOptions {
    filePath: string;
}

interface SuppotingLlm {
    /**
     * 创建一个提示词对象
     *
     * @param promptTemplate 提示词模板，可包含`{{arg}}`参数占位
     * @param args 参数对象
     * @returns 一个提示词对象，用于和大模型交互
     */
    createPrompt(promptTemplate: string, args?: Record<string, any>): RunnablePrompt;

    /**
     * 生成文本内容
     *
     * @param prompt 与大模型交互的提示词对象
     * @param options 配置项
     * @returns 文本大模型的生成内容
     */
    async askForText({promptTemplate, args}: RunnablePrompt, options?: TextModelOptions): Promise<string>;

    /**
     * 生成代码
     *
     * @param prompt 与大模型交互的提示词对象
     * @returns 代码大模型的生成内容
     */
    async askForCode({promptTemplate, args}: RunnablePrompt): Promise<string>;

    /**
     * 进行函数调用
     *
     * @param prompt 与大模型交互的提示词对象
     * @param functions 能够调用的函数定义
     * @param options 配置项
     * @returns 大模型选中的函数以及调用的参数
     */
    async askForFunction(prompt: RunnablePrompt, functions: FunctionDefinition[], options?: FunctionModelOptions): Promise<FunctionCall | null>;

    /**
     * 进行代码解释
     *
     * @param code 要解释的代码字符串
     * @param filePath 文件路径
     * @returns 代码的解释文本
     */
    async explainCode(code: string, {filePath}: CodeSpecificOptions): Promise<string>;

    /**
     * 获取代码的文档注释
     *
     * @param code 要获取文档注释的代码字符串
     * @param filePath 文件路径
     * @returns 代码的文档注释内容
     */
    async getDocCommentForCode(code: string, {filePath}: CodeSpecificOptions): Promise<string>;

    /**
     * 为代码添加行间注释
     *
     * @param code 要添加注释的代码字符串
     * @param filePath 文件路径
     * @returns 添加行间注释后的代码
     */
    async addCommentForCode(code: string, {filePath}: CodeSpecificOptions): Promise<string>;
}
```

## 通用模型功能

在大模型的使用上，`SupportingLlm`首先提供了3个通用场景的方法：

- `askForText`：调用文本模型，通常用于自然语言形式的处理，如文档查询、缩写扩写等。
- `askForCode`：调用代码模型，只会输出代码片段，可以用于函数生成等。
- `askForFunction`：对应大模型的Function Calling功能，可以做意图识别、参数生成等。

使用这几个通用方法，你需要先通过`createPrompt`方法创建一个提示词对象，再使用这个对象调用上述方法。

### 为什么要使用`createPrompt`？

在Comate+平台下，会为开发者提供大模型调用的记录，使用`createPrompt`通过一个模板和参数创建出来的对象能更好地被记录，且后续会提供以模板为准的聚合查询、统计能力。

```ts
const prompt = this.llm.createPrompt('请为{{name}}同志生成一段生日祝福', {name});
const text = await this.llm.askForText(prompt);
```

如上的代码，最终将以`请为{{name}}同志生成一段生日祝福`表现在数据统计中，你可以看到这个模板被调用了多少次、分别用什么参数、返回的内容是什么，这将有助于你分析自己的提示词是否合理，也可以针对性地优化。

### 指定使用模型

对于`askForText`和`askForFunction`方法，你可以指定具体的模型。

对于文本模型，可以选择`TextModel.ErnieBot`或`TextModel.ErnieBot4`，不指定模型时，会由Comate使用默认的模型。

对于函数模型，可以选择`FunctionModel.ErnieBot`或`FunctionModel.ErnieBot4`，同样不指定时会使用默认模型。

当你选择一个模型后，Comate会根据提示词的长度选择使用上下文长度不同的子模型，如`ernie-bot`模型可以在提示词超长时自动转为`ernie-bot-8k`模型。

## 特定代码能力

除此之外，在代码这一特殊场景下，`SupportingLlm`会提供一些内置的能力：

- `explainCode`：对输入的代码做自然语言的解释。
- `getDocCommentForCode`：输入一个完整的方法或函数，可以生成对应的文档注释，包含了函数说明、参数说明、返回值等。
- `addCommentForCode`：输入多行的代码，在代码中合适的位置加上内联的注释，增加代码的可读性。

## 管理提示词模板

在插件的源码中，你可以将提示词模板（包含`{{arg}}`占位的文本）放在`.prompt`文件中，并使用`import`语句在TypeScript中引用。

我们建议将所有`.prompt`文件放置在`src/prompts`目录下统一管理，并以提示词模板的作用为命名，代码结构可以如下：

```
/src
    /prompts
        greeting.prompt
    /providers
        greeting.ts
```

在`src/providers/greeting.ts`中，你可以这样引用：

```ts
import greetingPromptTemplate from '../prompts/greeting.prompt';

const greetingMessage = await this.llm.askForText(greetingPromptTemplate, {name: '小度'});
```
