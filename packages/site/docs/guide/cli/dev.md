---
sidebar_position: 23
---

# 开发调试

使用`cpd dev`会在当前目录下启动一个调试服务器，构建代码后在一个Web界面中托管使用。当你修改代码时，会重新进行构建和服务重启。

为了能够正常的调试，你的代码同样需要符合[构建插件](./build)中的全部要求。

## 参数

| 参数      | 说明                                                                        |
| --------- | --------------------------------------------------------------------------- |
| workspace | 指定一个目录作为项目根目录，插件会认为这是当前IDE打开的目录，默认为当前目录 |

## 环境变量

在当前版本中，为了让`cpd dev`能够正常工作，你需要注册一个文心一言的账号，使用自己的AK/SK来调用大模型，对应以下环境变量：

```shell
export ERNIE_AK="文心一言的AK"
export ERNIE_SK="文心一言的SK"
```

文心一言的AK/SK可以在[百度云 - 千帆大模型平台 - 应用接入](https://console.bce.baidu.com/qianfan/ais/console/applicationConsole/application)页面中找到。
