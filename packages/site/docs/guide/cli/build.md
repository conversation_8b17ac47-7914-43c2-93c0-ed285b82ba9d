---
sidebar_position: 2
---

# 构建插件

使用`cpd build`可以以当前目录为基础构建插件。对于一个插件，成功构建以下前提：

1. 入口文件必须在`src/index.ts`中。
2. 构建产出必须在`dist/index.js`中。
3. 项目本身必须是纯ESM的TypeScript项目，需要注意的是引用TypeScript文件也需要`.js`后续，如`import {format} from './utils/format.js`。

构建会使用Rollup对所有文件进行打包并产出为一个`index.js`，TypeScript代码通过SWC进行编译，以便于插件的分发与使用。

## 参数

| 参数  | 说明                 |
| ----- | -------------------- |
| clean | 构建前清除`dist`目录 |
