---
sidebar_position: 1
---

# 能力开发基础

在Comate+插件中，每一个可以被用户使用到的功能称之为“能力”，在插件的实现上，表现为一个类。

在`@comate/plugin-host`包中，提供了3个能力的基类，它们分别是：

- `PromptProvider`：支持自定义大模型提示词快速实现一项能力。
- `SkillProvider`：支持以函数的形态编写能力逻辑。
- `FallbackProvider`：支持灵活地处理用户的输入。

当一个类继承它们之一时，即实现了一项能力。

进一步地，在`src/index.ts`中，插件需要暴露一个`setup`函数，函数会接收一个`PluginSetupContext`类型的参数，可以通过它来注册能力。一个典型的`setup`的实现即是一系列对`registry.setupXxxProvider`的调用。

```ts
import {PluginSetupContext} from '@comate/plugin-host';
import {CountdownFallbackProvider} from './providers/countdown.js';
import {NowSkillProvider} from './providers/now.js';

export function setup({registry}: PluginSetupContext) {
    registry.registerFallbackProvider('countdown', CountdownFallbackProvider);
    registry.registerSkillProvider('now', NowSkillProvider);
}
```

每一次调用都会注册一项能力（一个`Provider`类）并指定一个名称。在通过`setup`函数注册后，我们还需要在`package.json`中的`comate.capabilities`字段中也声明这些能力。

```json
{
  "comate": {
    "capabilities": [
      {
        "type": "Skill",
        "name": "now",
        "displayName": "当前时间",
        "description": "显示当前时间"
      },
      {
        "type": "Fallback",
        "name": "countdown",
        "displayName": "倒计时",
        "description": "快速启动一个倒计时"
      }
    ]
  }
}
```

这其中的`type`字段必须与能力的基类相匹配，`name`字段必须与`setup`函数中注册时提供的名称相同。

在同时实现了`Provider`类、使用`setup`函数注册、`package.json`中声明后，一个插件就成功增加了一项能力。

:::note
你会在文档中看到部分的`Provider`类也有`description`这一属性，它们之间存在一些区别。在`package.json`中的`description`是被用户所看到的，在类上的`description`是被大模型理解识别的。因此请注意使用不同的视角来编写这2类描述信息。
:::
