---
sidebar_position: 2
---

# 控制展示界面

在插件的执行过程中，我们会需要持续、流式地向用户输出内容，内容会包含文字、图片、代码等。

在多数的`Provider`类中，都会有一个返回类型为`AsyncIterableIterator<TaskProgressChunk>`的方法，不同的类中分布如下：

- `SkillProvider`的`execute`方法。
- `FallbackProvider`的`hanldeQuery`方法。

在TypeScript中，`AsyncIterableIterator`是一个异步可迭代对象，在实际实现时，你可以在方法名前加一个`*`来启用迭代器语法，在方法体中就可以使用`yield`来输出内容。

一个迭代器方法中可以有多个`yield`关键字，每一次执行都会输出一段完整的内容，这内容会被展示在相应的界面上（如作为一条对话消息）。

:::important
方法必须每一次都`yield`一个完整的视图内容，Comate并不具备将多次内容“拼起来”的功能。
:::

## 渲染格式

在Comate中，你可以使用2种格式来输出内容：

- 如果输出一个字符串，则该字符串会以Markdown的格式渲染。
- 你也可以使用[JSX语法](https://react.dev/learn/writing-markup-with-jsx)来描述界面，JSX提供更灵活的界面控制。

## 使用JSX

JSX是在JavaScript语言的基础上扩展的语法，它允许我们使用类似HTML的语法来编写JavaScript代码。

在Comate+插件中，你也可以使用JSX语法来编写视图内容，但不同于标准的HTML兼容的JSX，Comate+仅支持有限的元素。

为了使用JSX语法，你需要确认2个点：

1. 在你项目中的`tsconfig.json`文件中，是否有以下配置：

   ```json
   {
     "compilerOptions": {
       "jsx": "react-jsx",
       "jsxImportSource": "@comate/plugin-host"
     }
   }
   ```

2. 需要使用JSX语法的文件后缀名为`.tsx`。

随后，在各种`Provider`类的实现中，你可以通过`yield`关键字来输出一个JSX元素，你可以使用如`<p>`元素表示分段、`<code-block>`元素表示代码块等，这会让你拥有更细粒度的界面控制。

### 可用JSX标签

在Comate插件中，当前可以用的原生HTML元素是有限的，以下是你可以使用的元素：

- 内容元素：`p`、`span`、 `em`、`strong`、`code`、`blockquote`、`li`、`h1`、`h2`、`h3`、`h4`、`h5`、`h6`，仅支持子元素，不支持其它如`title`等属性。
- 列表元素：`ul`、`ol`，确保子元素是`li`。
- 分隔元素：`br`、`hr`，不允许有任何属性和子元素。
- 图片：`img`，支持`src`和`alt`属性。
- 链接：`a`，仅支持`href`属性，且必须指向一个外部URL，不支持相对路径。

除原生元素外，Comate还提供了一系列高级功能的自定义元素：

- `<code-block>`：表示一个代码块，其内容为展示的代码，使用`language`、`acceptMethods`等属性定制行为。

### 输出代码块

在使用JSX的前提下，你可以使用`<code-block>`元素来输出代码块。

在智能化开发中，代码块除了展示代码的作用外，还有很重要的一个功能是让用户采纳代码到编辑器内。但不同逻辑生成的代码，其采纳的形式也是不同的，诸如插入到光标位置、放到命令行中运行等不一而足。`<code-block>`元素提供了丰富的属性来控制代码块的展示与行为：

| 属性名          | 类型       | 描述                                                                       |
| --------------- | ---------- | -------------------------------------------------------------------------- |
| `language`      | `string`   | 代码语言，如`javascript`、`python`等                                       |
| `closed`        | `boolean`  | 是否为闭合代码块，未闭合的代码块不会有采纳按钮，这在流式输出代码时比较有用 |
| `acceptMethods` | `object[]` | 采纳代码的逻辑，见下文描述                                                 |

其中，`acceptMethods`属性是一个对象数组，每一个对象表示一种采纳方式。根据数组中的顺序，多种采纳按钮将**自右向左**地排列。当前Comate支持以下几种采纳方式：

```ts
/** 指代时替换选中代码（或插入到光标位置） */
interface ReplaceSelectionAcceptMethod {
    method: 'replaceSelection';
    /** 按当前选中还是按触发时选中 */
    cursorMode: 'current' | 'trigger';
}

/** 采纳时替换编辑器中的指定代码 */
interface ReplaceContentAcceptMethod {
    method: 'replaceContent';
    from: string;
    to: string;
}

/** 采纳到终端，可选直接执行 */
interface ToTerminalAcceptMethod {
    method: 'toTerminal';
    /** 是否直接执行 */
    run?: boolean;
}

/** 采纳时复制到剪贴板 */
interface CopyAcceptMethod {
    method: 'copy';
    /** 可以让复制的代码与展示的不同 */
    content?: string;
}
```

如下JSX片段就能声明一个代码块，展示一份函数注释，并支持复制、直接替换原函数签名部分：

```tsx
const comment = `
/**
 * 刷新绘制内容
 *
 * @param content 可选参数，需要绘制的元素
 * @returns 返回一个任务进度块对象，包含绘制命令和绘制内容
 */
`;

const sourceSignature = 'flush(content?: DrawElement): TaskProgressChunk {';

const replace: ReplaceContentAcceptMethod = {
    type: 'replaceContent',
    from: sourceSignature,
    to: comment + '\n' + sourceSignature,
};

// 把代码展示为diff的格式，但采纳依然是注释内容
<code-block
    closed
    language="javascript"
    acceptMethods={[replace, {type: 'copy', content: comment}]}
>
    {comment.split('\n').map(v => `+ ${v}`).join('\n')}
    {' ' + sourceSignature}
</code-block>;
```

## 增量输出

在开发插件时，我们会期望随着执行逻辑，逐渐地、增量地来输出最新的内容。但由于Comate要求每一次`yield`的内容都是完整的，这就使得插件开发者需要自己来做好内容的拼接。

在插件SDK中，提供了2个类来帮助你做增量的输出：

- `StringChunkStream`控制字符串的增量输出。
- `ElementChunkStream`控制JSX元素的增量输出。

注意，你不能混合使用字符串或JSX元素，根据你的场景，在2者之间选择一个使用。

### 字符串输出

```ts
class StringChunkStream {
    append(content: DrawElement): void;

    flush(content?: DrawElement): TaskProgressChunk;

    replaceLast(content: DrawElement): void;

    flushReplaceLast(content: DrawElement): TaskProgressChunk;

    fail(content: DrawElement): TaskProgressChunk;
}
```

我们最常用的可能是`flush`方法，它可以追加一段内容并直接输出到界面上，使用非常简单，以简单的天气预报为例：

```ts
import {SkillProvider, FunctionParameters, StringChunkStream} from '@comate/plugin-host';

interface Args {
    city: string;
}

export class WeatherSkillProvider extends SkillProvider {
    static skillName = 'weatherReport';

    static parameters: FunctionParameters = {
        type: 'object',
        properties: {
            city: {
                type: 'string',
            },
        },
    };

    protected async *execute(params: Args): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new StringChunkStream();
        // 直接输出到界面上
        stream.flush(`你好，以下是${params.city}的天气预报：\n\n`);
        const weather = await fetchWeather(params.city);
        // 可以通过append缓存多段内容再一次性输出
        stream.append(`- 温度：${weather.temperature}℃\n`);
        stream.append(`- 湿度：${weather.humidity}%\n`);
        stream.append(`- 风向：${weather.windDirection}\n`);
        stream.flush();
    }
}
```

### JSX元素输出

```ts
class ElementChunkStream {
    append(element: DrawElement): void;

    flush(element?: DrawElement): TaskProgressChunk;

    replaceLast(element: DrawElement): void;

    flushReplaceLast(element: DrawElement): TaskProgressChunk;

    appendLast(element: DrawElement): boolean;

    flushAppendLast(element: DrawElement): TaskProgressChunk;

    fail(element: DrawElement): TaskProgressChunk;
}
```

相比`StringChunkStream`，`ElementChunkStream`增加了`appendLast`方法，它允许你往最后一个元素的子节点中增加元素，但你需要自行保证在调用该方法时，之前保存的最后一个元素允许有子节点，这通常用来在列表中追加元素：

```ts
import {SkillProvider, FunctionParameters, ElementChunkStream} from '@comate/plugin-host';

interface Args {
    city: string;
}

export class WeatherSkillProvider extends SkillProvider {
    static skillName = 'weatherReport';

    static parameters: FunctionParameters = {
        type: 'object',
        properties: {
            city: {
                type: 'string',
            },
        },
    };

    protected async *execute(params: Args): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new ElementChunkStream();
        // 输出段落
        stream.flush(<p>你好，以下是{params.city}的天气预报：</p>);
        const weather = await fetchWeather(params.city);
        stream.append(<ul />);
        // 往ul中添加li
        stream.appendLast(<li>温度：{weather.temperature}℃</li>);
        stream.appendLast(<li>湿度：{weather.humidity}%</li>);
        stream.appendLast(<li>风向：{weather.windDirection}</li>);
        stream.flush();
    }
}
```
