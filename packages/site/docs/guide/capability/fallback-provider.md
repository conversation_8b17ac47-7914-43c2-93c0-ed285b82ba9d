---
sidebar_position: 5
---

# 兜底能力开发

我们在2种情况下，会使用“兜底”这一能力：

1. 当插件足够智能化，可以灵活地处理用户输入的提示词，不需要Comate协助做能力选择、参数生成等过程的时候。
2. 当插件希望在所有其它的能力（提示词、技能）都不命中用户需求时，可以有一个兜底的处理逻辑，如进行通用的问答的时候。

一个兜底能力通过继承`FallbackProvider`这一基类实现，并使用`registerFallbackProvider`进行注册。

以下我们以一个十分简单的“返回插件使用介绍”的兜底效果为示例。

```ts
import {FallbackProvider, TaskProgressChunk, TaskProgressChunkStream} from '@comate/plugin-host';

export class WelcomeFallbackProvider extends FallbackProvider {
    static description = '插件使用介绍';

    async *handleQuery(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        yield stream.flush('你好，我是智能助手插件，你可以用以下方式使用我：\n\n');
        yield stream.flush('- /查询文档 会为你提供指定的API的文档与示例\n');
        yield stream.flush('- /解释正则 可以为你输入的正则表达示提供详细的解释');
    }
}
```

随后在`setup`函数中进行注册：

```ts
registerFallbackProvider(WelcomeFallbackProvider);
```

`FallbackProvider`的实现非常简单，只有一个`handleQuery`方法，方法没有参数，内部可以实现任何逻辑，并使用`yield`返回消息内容。

如果需要获取用户输入的内容，你可以使用`this.currentContext.query`，当然其它相关的信息、方法也可以使用。

一个兜底的能力在插件开发和用户使用时都有一些与其它能力略微不同的表现：

- 开发插件时，一个插件只能有最多1个兜底能力。当注册2个以上`FallbackProvider`时，插件将无法启动。
- 用户不能通过`/`来主动选择兜底能力，它仅在用户不选择任何能力输入提示词，且插件提供的其它能力都无法处理用户的需求时才会生效。
