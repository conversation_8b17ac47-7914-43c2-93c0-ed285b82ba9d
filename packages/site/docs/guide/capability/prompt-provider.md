---
sidebar_position: 3
---

# 提示词能力开发

提示词（Prompt）能力可以通过注册一个提示词模板，为用户提供特定场景的优化后的大模型提示词。如果一个功能可以通过单次的大模型调用完成，诸如解释SQL语句、进行代码优化等，使用这个能力可以低成本地实现与Comate的整合。

## 注册提示词模板

在插件中，通过继承`PromptProvider`类来实现一个提示词模板，每一个子类均指定一个模板。随后你可以在`setup`函数中通过`registerPromptProvider`将该类注册到Comate环境中。

最简单的实现是，在你的类中重写`promptTemplate`属性即可实现一个固定的提示词模板。

```ts
import {PromptProvider, registerPromptProvider} from 'comate';

class JavaApiExamplePromptProvider extends PromptProvider {
    static promptName = 'searchApiDocument';

    static description = '查询Java中指定API的说明和示例';

    promptTemplate = '你是一个资深的Java开发者，请说出这个API的定义，并给出2个简短的示例代码：\n\n{{query}}';
}
```

在`setup`函数中：

```ts
registerPromptProvider('java-api-example-prompt', JavaApiExamplePromptProvider);
```

形如以上的实现通过名为“Java文档”的插件发布，可以使用户在对话界面通过`@Java文档 /API查询 ArrayList.copy`唤起并由大模型返回`ArrayList.copy`的定义与示例代码。

## 在模板中使用变量

在上述示例中，可以看到插件定义的模板中有一个`{{query}}`占位符，它将在实际发送至大模型时由用户对话框输入的内容替换。
在prompt模板中，你可以使用`{{argName}}`来引用一个内置的变量，可以使用变量的定义与插件基础能力中的ActivationContext对象相同，包含query在内，你可以使用的变量有：

- `query`：用户在对话框中输入的全部内容。
- `selectedCode`：当前编辑器中用户选中的代码块。
- `activeFileContent`：当前编辑器中打开的文件的全部内容。
- `activeFilePath`：当前编辑器中打开的文件路径，**是一个相对于项目的相对路径**。
- `activeFileName`：与activeFilePath类似，但只有最后文件名部分，不包含文件目录。
- `activeFileLanguage`：当前编辑器打开的文件的编程语言，参考[Language Identifier](https://code.visualstudio.com/docs/languages/identifiers)获得所有语言的列表。

## 编程构建提示词

如果使用固定的提示词模板无法满足需求，你可以在继承`PromptProvider`后，通过重写`buildPrompt`方法来实现自定义的提示词生成逻辑。该方法最终返回一个字符串，这个字符串会被直接发送至大模型。

```ts
import {PromptProvider, PromptContext, registerPromptProvider} from 'comate';

class ApiExamplePromptProvider extends PromptProvider {
    static promptName = 'searchApiDocument';

    static description = '查询Java中指定API的说明和示例';

    buildPrompt() {
        if (this.currentContext.activeFileName.endsWith('.java')) {
            return `请在JDK中查询${context.query}的信息，告诉我它的作用并给出2-3个简短的示例。`;
        }
        if (this.currentContext.activeFileName.endsWith('.js')) {
            return `请查询关于${context.query}的JavaScript API信息，明确告诉我这个API的参数、返回值等类型，并给出2个示例。`;
        }
        else {
            return `查询${context.query}在编程开发中的信息并给出2-3个简短的代码示例。`;
        }
    }
}
```

`buildPrompt`方法本身没有参数，但你可以使用基础能力中的`this.currentContext`获取当前代码、文件名等信息，其它模板变量同理。

需要注意的是，`buildPrompt`返回的是最终的提示词内容，在返回的字符串中包含诸如`{query}`的模板变量是无效的，你必须自己完成这些变量的替换。
