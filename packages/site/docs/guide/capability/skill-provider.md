---
sidebar_position: 4
---

# 技能能力开发

插件可以注册一个或多项“技能（Skill）”来声明自己的功能，从而通过Comate来识别场景、提取参数，通过函数调用的形式触发这些功能，较低成本地实现查询、执行等效果。

## 声明一项技能

在插件中，提供一个继承`SkillProvider`的类来声明技能，并通过`registerSkillProvider`将技能注册到Comate环境中。

一个技能在形态上与编程中的“函数”在形态上非常类似。`SkillProvider`中，通过`skillName`、`description`和`parameters`可以声明函数名、用途和参数结构，这些内容将被大模型所接收与理解，并生成合适的参数进行调用。

我们用一个“查询需求”的技能实现为例，来解读`SkillProvider`类中各个方法的作用。

```ts
import {SkillProvider, FunctionParameterDefinition, registerSkillProvider, TaskProgressChunkStream} from 'comate';

interface IssueSearchInput {
    status: 'assigned' | 'working' | 'done';
    keyword: string;
}

class IssueTrackSKillProvider extends SkillProvider<IssueSearchInput> {
    static skillName = 'searchIssueList';

    static description = '根据需求状态与关键字检索当前用户的需求（卡片、任务、待办）列表';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {
            status: {
                type: 'string',
                enum: ['assigned', 'working', 'done'],
                description: '卡片的状态',
            },
            keyword: {
                type: 'string',
                description: '检索卡片的关键字',
            },
        },
        required: ['status'],
    };

    protected validateArgs(args: any): args is IssueSearchInput {
        return args && typeof args.status === 'string';
    }

    protected defaultArgumentValue(): IssueSearchInput {
        return {status: 'working', keyword: ''};
    }

    protected async *execute(args: IssueSearchInput) {
        const stream = new TaskProgressChunkStream();
        const params = new URLSearchParams();
        // 查当前用户下的卡片
        params.set('user', this.currentUser.name);
        params.set('keyword', args.keyword);
        // 如果状态提取失败，则不追加这个参数
        params.set('status', args.status);
        const response = await fetch(`${API_ENDPOINT}?${params}`);
        const issues = await response.json();

        yield stream.flush(`以下是属于你的状态为${args.status}的需求：\n\n`);
        const table = this.render.table(
            issues,
            {
                columns: [
                    {field: 'code', name: '卡片编号'},
                    {field: 'title', name: '卡片标题'},
                ],
            }
        );
        yield stream.flush(table};
    }
}
```

当然，最后还需要在`setup`函数中注册。

```ts
registerSkillProvider('issue-track', IssueTrackSKillProvider);
```

我们可以看到，我们定义了一个参数的类型，并使用泛型放在父类`SkillProvider`上，实现了一个子类，有一些静态的属性：

- `skillName`：对应程序上的函数名，尽量从函数的角度来描述这个名字。
- `description`：对函数作用的描述，它会影响到大模型判断是否能使用这个函数。
- `parameters`：函数的参数结构，这是一个JSON Schema对象，但顶层必须是`object`类型。

如果你了解大模型的Function Calling功能，那么会更容易理解，其中`skillName`、`description`和`parameters`与一个函数定义是完全对应的。

除了静态属性之外，还有3个关键的方法：

- `execute`：这是技能的逻辑实现核心，你将接收一个参数对象，你可以编程实现自己的逻辑，例如发送HTTP请求、做数据处理等。使用`yield`可以向用户返回消息内容。
- `validateArgs`：由于技能调用的参数是大模型生成的，并不能保证100%符合你的类型声明，因此`validateArgs`方法可以在调用`execute`前进行校验。如果你信任大模型的生成结果，这个方法默认会返回`true`通过验证。当你重写它并返回`false`时，Coamte会中断插件的执行并告知用户。
- `defaultArgumentValue`：当用户指定调用一个技能，但没有输入任何提示词时，大模型自然是无法生成对应参数的。此时Comate会调用`defaultArgumentValue`方法来获取默认的参数值。

在重写这3个方法，并在`execute`中使用`yield`输出内容后，我们就实现了一个技能。

## 无参数技能

如果你的技能并不需要参数，那么可以将`parameters`声明为一个空的对象。

```ts
class MySkillProvider extends SkillProvider {
    // 其它静态属性

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    protected execute() {
        // 具体逻辑
    }
}
```

注意此处你必须使用这个固定的结构（`type`为`object`，`properties`为空对象）来表示无参数。当不需要参数时，Comate会跳过参数生成的阶段，直接调用`execute`方法执行逻辑。

## 提供多项技能

当然，一个插件并不仅限于提供一个能力，所以我们也可以在插件中提供多个技能，这只需要实现多个`SkillProvider`的子类并通过`registerSkillProvider`注册即可。

在有多个技能（以及提示词）能力注册时，用户不一定需要指定具体的能力。Comate会根据用户输入的提示词，自动推断应当使用哪一个能力。

1. 当用户输入`@插件名称 [用户需求]`时，该插件被唤起，同时Comate会要求用户需求来选择插件的某一项能力并提取参数。
2. 当用户输入`@插件名称 /技能名称 [用户需求]`时，已经指定了具体的技能，则Comate会直接进行参数提取，调用该技能。

作为示例，我们假设一个“需求管家”插件提供了一个`IssueTrackSKillProvider`来查询需求，一个`IssueUpdateSKillProvider`来更新一个需求的状态，那么用户可能进行的交互会有这些：

- 输入`@需求管家 找到我开发中的需求`则会通过意图识别后调用`IssueTrackSKillProvider`。
- 输入`@需求管家 标记首页文案修改这个需求为已完成`则会识别意图并调用`IssueUpdateSKillProvider`。
- 输入`@需求管家 /更新需求状态 首页文案修改 -> 已完成`会直接调用`IssueUpdateSKillProvider`。

## 快速对接后端服务

在技能类的能力中，Comate+提供了`PipeHttpRequestSkillProvider`基类，它封装了HTTP请求的逻辑，只需简单地指定`Method`、`URL`等参数即可快速将后端响应对接到界面上。

我们以一个“输入城市或区域，输出当地时间”的功能为例，调用[worldtimeapi.org](https://worldtimeapi.org/pages/examples)的开放接口，来展示`PipeHttpRequestSkillProvider`的用法：

```ts
import {FunctionParameterDefinition, HttpRequest, PipeHttpRequestSkillProvider} from '@comate/plugin-host';

const timezones = [
    'Africa/Abidjan',
    'Africa/Algiers',
    'Africa/Bissau',
    'Africa/Cairo',
    // 其它支持的时区，此处省略
];

interface Args {
    timezone: string;
}

export class WorldTimeSkillProvider extends PipeHttpRequestSkillProvider<Args> {
    static skillName = 'getTimeForTimezone';

    static description = '根据指定的时区获取当前时间';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {
            timezone: {
                type: 'string',
                description: '时区名称',
                enum: timezones,
            },
        },
    };

    protected async buildRequest(args: Args): Promise<HttpRequest> {
        return {
            method: 'GET',
            url: `https://worldtimeapi.org/api/timezone/${args.timezone}`,
            responseType: 'json',
        };
    }

    protected transformResponseToText(responseData: any): string {
        const [date, time] = responseData.datetime.split(/T|\./);
        return `${responseData.timezone}时区当前时间是：${date} ${time}`;
    }
}
```

代码中有几个需要关注的点：

1. `PipeHttpRequestSkillProvider`依然可以使用大模型进行参数提取，`skillName`、`description`和`parameters`等静态属性是有效的。
2. 使用`buildRequest`方法来构建HTTP请求，它接收一个参数对象并返回一个`HttpRequest`对象，当服务端返回JSON时，请指定`responseType`为`json`，响应为文本则指定为`text`。
3. 如果你需要使用如`POST`等请求并发送数据时，可以用`headers`属性并指定`content-type`头，根据头的值会自动判断发送的格式。
4. 对于服务端返回JSON结构的情况，可以使用`transformResponseToText`方法来将JSON数据转换为文本。
