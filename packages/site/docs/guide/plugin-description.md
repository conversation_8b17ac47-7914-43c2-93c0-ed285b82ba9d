---
sidebar_position: 2
---

# 插件描述

在每一个插件中，都会有一个`package.json`文件，它首先是一个标准的NodeJS项目的声明。在Comate+插件里，`package.json`中将额外增加一个`comate`字段用来声明插件的相关描述。

以下是一个典型的package.json文件，可以注意到除了项目本身的name和version字段外，comate字段提供了作为Comate插件的完整的描述。

```json
{
  "name": "comate-plugin-code-explain",
  "version": "1.0.0",
  "comate": {
    "name": "code-explain",
    "version": "1.0.0",
    "displayName": "代码解释",
    "description": "可对选中的代码或单个方法、类、文件给出代码的详细解释",
    "entry": "./dist/index.js",
    "icon": "./assets/icon.png",
    "keywords": ["explain"],
    "author": [
      {
        "name": "zhang<PERSON>li01",
        "email": "<EMAIL>"
      }
    ],
    "files": [
      "dist",
      "assets"
    ],
    "capabilities": [
      {
        "name": "log-optimize",
        "type": "Prompt",
        "displayName": "优化日志信息",
        "description": "对选中代码中的日志逻辑进行优化，丰富日志记录的信息",
        "visibilitySelector": {
          "language": ["java", "javascript"],
          "filePath": ["src/**"],
          "fileContent": ["log"],
          "selectedContent": [".log("]
        }
      }
    ],
    "configSchema": {
      "sections": [
        {
          "title": "Prompt配置",
          "properties": {
            "codeExplain.prompt": {
              "type": "string",
              "description": "自定义代码解释使用的prompt"
            }
          }
        }
      ]
    }
  }
}
```

## 基本信息

以下字段用于描述插件的功能与行为，这些描述将会通过插件中心等界面展示。

- `name`：插件的名称，必须是小写字母、数字以及横线组成，如this-is-a-comate-plugin。
- `version`：插件的版本，Comate通过版本来确认插件是否需要更新。版本号为一个标准的semver版本号。
- `displayName`：用户看到的插件名称，用户在对话界面通过@唤起插件、通过/唤起功能、以及其它与插件交互时，将展示该名称。建议将长度控制在10以内。
- `description`：描述插件的功能、作用等，该字段可能会被Comate用于智能唤起插件。
- `icon`：指向一个插件包内的图片文件，用于展示插件的图标。插件的图标文件应当为256x256尺寸，建议为.png格式。
- `keyword`：插件可以指定一系列的关键字，用于插件中心检索等使用。
- `author`：一组插件开发者的信息，每一个信息由name和email组成。

## 插件行为

以下字段描述插件在运行时的行为，它们将影响到Comate与插件的具体交互过程。

- `entry`：指定插件的功能入口文件，通常可以使用如`./dist/index.js`的路径。该路径必须是一个以`./`开始的字符串，路径相对于package.json，且必须是一个`.js`文件。Comate加载插件时会执行该文件，所有插件需要的能力均需通过该文件注册。
- `files`：一个文件路径的数组，在插件打包发布时，仅这些路径对应的文件会进入插件包，你可以使用这个配置将未编译的源码、日志等无关信息剔除。

## 能力描述

在配置中通过`capabilities`描述插件提供的各项能力。`capabilities`是一个数组，它的每一项结构如下：

```ts
interface Capability {
    name: string;
    type: 'Prompt' | 'Skill' | 'Agent';
    displayName: string;
    description: string;
}
```

每一项都声明插件提供的能力，并且需要与插件的实现源码一一对应。其中各字段还会在用户使用插件过程中有所反应。

- name：一个程序用的字段，需要**使用小写英文、数字以及下划线`_`组成**，它与插件实现中的`registerXxxProvider`中的`name`参数对应，用户唤起插件该项能力时，会通过相同的name找到对应的实现再实例化后调用。
- type：指定该能力的类型，分别对应不同的`Provider`基类。该字段也需要与源码实现中的继承关系严格对应，否则将造成插件唤起后报错无法执行。
- displayName：用户所见的能力名称，会出现在用户对话时通过/调起等场景下。
- description：一段对能力的描述，该描述主要用于用户阅读，但**也可能会被Comate用于智能唤起插件**。

### 配置结构和界面

通过`configSchema`这一属性可以定制插件的配置能力，这些配置会被用户在Comate的配置中心看到并可以自定义。

`configSchema`的结构用TypeScript类型表述如下：

```ts
interface ComateConfigSection {
    title: string;
    properties: JsonSchemaFlat;
    required: string[];
}

interface ComateConfigSchema {
    sections: ComateConfigSection[];
}
```

其中，`sections`中的每一项在配置界面表现为一个区块，`title`为这个区块的标题。`properties`表示这一区块下有哪些配置项，每一个配置项`JsonSchemaFlat`是一个类似JSON Schema的结构，但有一些额外的限制，其中能使用的`type`有限，可用的值与视图对应如下：

- `string`：表现为一个文本框（单行，暂不支持多行）。
- `number`：表现为一个输入数字的文本框。
- `integer`：表现为一个只支持输入整数的文本框。
- `boolean`：表现为一个复选框。
- `enum`（在string的基础上）：表现为一个下拉框。
- `array`：表现为可增删的多项，且子项的`type`依然要符合上述要求，且不允许嵌套的数组。

每一个`JsonSchemaFlat`对象（即一个配置项）中，以下字段请务必提供：

- `title`：用于配置表单上显示为填写项的标题。
- `description`：用于指导用户如何填写。

:::important
由于用户不配置就能用插件，实际上虽然声明了`required`，取到的值依然可能是`null`。`requried`只控制用户打开配置界面后的填写中的行为。
:::

### 控制能力可见性

在开发插件时，我们会有类似“优化Java的能力不应该在Python编程时出现”这样的需求，这一控制逻辑被称为“能力的可见性”，即一个能力在当前用户的编程现场是否应该出现。

在插件的`capabilities`配置中，每一项能力都可以通过`visibilitySelector`字段来控制其可见性，一个可见性的配置最多可以包含下面4个字段：

- `language`：指定该能力在哪些语言中可见，如`["java", "javascript"]`。参考[Language Identifier](https://code.visualstudio.com/docs/languages/identifiers)获得所有语言的列表。
- `activeFilePath`：指定该能力在哪些文件路径中可见，如`["src/**"]`。路径使用Glob格式。
- `activeFileContent`：指定当文件内容包含关键字时插件能力可见，如`["log"]`。
- `selectedContent`：指定用户选中的代码块包含关键字时插件能力可见，如`[".log("]`。
- `workspaceFile`：指定当前工作台下有符合条件的文件存在，如`["package.json", "*/packages/package.json"]`。路径使用Glob格式，所有在`.gitignore`中的路径不会被搜索。

:::note
以上4个字段是**AND**的关系，每一个字段的数组中的各项是**OR**的关系。
:::

当一个插件能力**不可见**时，它会有以下表现：

- 在对话界面有，无法通过`@`或`/`来主动选择这一能力。
- 对于由Comate进行用户需求意图识别时，这一能力不会参与意图识别。

以下是一个示例，要求在一个Maven管理的项目中，当前打开的Java文件是Controller类，且包含了日志打点相关的代码：

```json
{
  "language": ["java"],
  "activeFilePath": ["*Controller.java"],
  "activeFileContent": [".log(", ".info(", ".wran(", ".error("],
  "workspaceFile": ["pom.xml"]
}
```
