---
sidebar_position: 1
---

# 项目结构

在使用`npx @comate/plugin-init`后，初始的项目结构如下：

```
/
    /src
        /providers
            hello.ts
        index.ts
    /assets
        icon.svg
    package.json
    tsconfig.json
```

在项目中，我们应该遵守以下规则：

1. 所有源码都存放在`src`目录下，逻辑使用TypeScript编写。
2. 项目使用的素材可以放置在`assets`目录下，至少有一个`icon.svg`文件作为插件的图标。
3. 在`package.json`中，注意配置`"type": "module"`启用ESM模式，Coamte+插件仅能在纯ESM环境下工作。
4. 在`tsconfig.json`中，注意配置`"module": "NodeNext"`和`"moduleResolution": "NodeNext"`配合ESM模块工作。
5. 插件提供的各项能力，放置在`src/providers`目录下，每一个能力使用`camelCase`的形式命名文件，并导出一个实现类。
6. 在插件的`index.ts`中，导入各项能力，并导出`setup`函数注册所有能力到运行环境中。

对于其它的需要，可以在`src`目录下进一步添加诸如`src/utils`、`src/prompts`等目录，只要符合标准TypeScript的项目即可。
