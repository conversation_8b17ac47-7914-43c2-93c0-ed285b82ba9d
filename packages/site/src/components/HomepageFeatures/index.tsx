import clsx from 'clsx';
import Heading from '@theme/Heading';
import styles from './styles.module.css';
import Link from '@docusaurus/Link';

type FeatureItem = {
    title: string;
    Svg: React.ComponentType<React.ComponentProps<'svg'>>;
    description: JSX.Element;
    to: string;
};

const FeatureList: FeatureItem[] = [
    {
        title: '什么是Comate插件',
        Svg: require('@site/static/img/undraw_docusaurus_mountain.svg').default,
        description: (
            <>
            </>
        ),
        to: '/docs/getting-started/intro',
    },
    {
        title: '开发第一个插件',
        Svg: require('@site/static/img/undraw_docusaurus_tree.svg').default,
        description: (
            <>
            </>
        ),
        to: '/docs/getting-started/create',
    },
    {
        title: '实现一个定时器',
        Svg: require('@site/static/img/undraw_docusaurus_react.svg').default,
        description: (
            <>
            </>
        ),
        to: '/docs/getting-started/timer',
    },
];

function Feature({title, Svg, description, to}: FeatureItem) {
    return (
        <div className={clsx('col col--4')}>
            <div className="text--center">
                <Svg className={styles['feature-svg']} role="img" />
            </div>
            <div className="text--center padding-horiz--md">
                <Link to={to}>
                    <Heading as="h3">{title}</Heading>
                </Link>
                <p>{description}</p>
            </div>
        </div>
    );
}

export default function HomepageFeatures(): JSX.Element {
    return (
        <section className={styles.features}>
            <div className="container">
                <div className="row">
                    {FeatureList.map((props, idx) => <Feature key={idx} {...props} />)}
                </div>
            </div>
        </section>
    );
}
