import {themes as prismThemes} from 'prism-react-renderer';
import type {Config} from '@docusaurus/types';
import type * as Preset from '@docusaurus/preset-classic';

const config: Config = {
    title: '<PERSON><PERSON> Comate+',
    tagline: '',
    favicon: 'img/favicon.ico',
    // TODO: 改
    url: 'https://comate.baidu.com',
    baseUrl: '/developer/',
    organizationName: 'Baidu',
    projectName: 'Comate',
    onBrokenLinks: 'throw',
    onBrokenMarkdownLinks: 'warn',
    i18n: {
        defaultLocale: 'zh-Hans',
        locales: ['zh-Hans'],
    },
    presets: [
        [
            'classic',
            {
                docs: {
                    sidebarPath: './sidebars.ts',
                },
                theme: {
                    customCss: './src/css/custom.css',
                },
            } satisfies Preset.Options,
        ],
    ],
    themeConfig: {
        // TODO: 需要一个社交卡
        // image: 'img/docusaurus-social-card.jpg',
        navbar: {
            title: 'Comate+',
            logo: {
                alt: 'Comate+ Logo',
                src: 'img/logo.svg',
            },
            items: [
                {
                    type: 'doc',
                    docId: 'getting-started/intro',
                    position: 'left',
                    label: '快速入门',
                },
                {
                    type: 'doc',
                    docId: 'guide/project-structure',
                    position: 'left',
                    label: '开发指南',
                },
                {
                    href: 'https://comate.baidu.com',
                    label: 'Comate',
                    position: 'right',
                },
            ],
        },
        footer: {
            style: 'dark',
            links: [
                {
                    title: 'Docs',
                    items: [
                        {
                            label: '快速入门',
                            to: './docs/getting-started/intro',
                        },
                        {
                            label: '开发指南',
                            to: './docs/guide/project-structure',
                        },
                    ],
                },
                {
                    title: 'More',
                    items: [
                        {
                            label: 'Comate首页',
                            to: 'https://comate.baidu.com',
                        },
                        {
                            label: '前往购买',
                            href: 'https://cloud.baidu.com/campaign/comate/index.html',
                        },
                    ],
                },
                {
                    title: 'Legal',
                    items: [
                        {
                            label: '使用百度前必读',
                            to: 'https://www.baidu.com/duty/',
                        },
                        {
                            label: '京ICP证030173号',
                            href: 'https://beian.miit.gov.cn/',
                        },
                        {
                            label: '京公网安备11000002000001号',
                            href: 'https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11000002000001',
                        },
                    ],
                },
            ],
            copyright: `© ${new Date().getFullYear()} Baidu Comate`,
        },
        prism: {
            theme: prismThemes.github,
            darkTheme: prismThemes.dracula,
        },
    } satisfies Preset.ThemeConfig,
};

export default config;
