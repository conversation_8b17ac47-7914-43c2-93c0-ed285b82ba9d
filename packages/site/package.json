{"private": true, "name": "@comate/plugin-site", "version": "0.9.2", "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "type-check": "tsc"}, "dependencies": {"@docusaurus/core": "3.1.0", "@docusaurus/preset-classic": "3.1.0", "@mdx-js/react": "^3.0.0", "clsx": "^2.1.0", "prism-react-renderer": "^2.3.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.1.0", "@docusaurus/tsconfig": "3.1.0", "@docusaurus/types": "3.1.0", "typescript": "^5.3.2"}, "browserslist": ["chrome 90"], "engines": {"node": ">=20.10.0"}}