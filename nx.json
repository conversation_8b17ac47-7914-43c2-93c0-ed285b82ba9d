{"tasksRunnerOptions": {"default": {"runner": "nx/tasks-runners/default", "options": {"cacheableOperations": ["build", "lint"]}}}, "targetDefaults": {"build": {"dependsOn": ["^build"], "outputs": ["{projectRoot}/dist"]}, "lint": {"dependsOn": ["^lint"]}}, "$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "sharedGlobals": [], "production": ["default"]}}