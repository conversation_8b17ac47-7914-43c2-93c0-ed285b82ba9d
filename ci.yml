Global:
    version: "2.0"
    group_email: <EMAIL>
Default:
    profile:
        - site
Profiles:
    - profile:
      name: site
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - nodejs: 20.latest
            - pnpm: 8.3.1
      build:
        command: sh scripts/build-site.sh
      artifacts:
        release: true
    - profile:
      name: internal_extension
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - nodejs: 20.latest
            - pnpm: 8.3.1
      build:
        command: export PLUGINS=$PLUGINS && npm run bootstrap && npm run bundle:internal
      artifacts:
        release: true
    - profile:
      name: saas_extension
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - nodejs: 20.latest
            - pnpm: 8.3.1
      build:
        command: export PLUGINS=$PLUGINS && npm run bootstrap && npm run bundle:saas
      artifacts:
        release: true
    - profile:
      name: poc_extension
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - nodejs: 20.latest
            - pnpm: 8.3.1
      build:
        command: export PLUGINS=$PLUGINS && npm run bootstrap && npm run bundle:poc
      artifacts:
        release: true
    - profile:
      name: webview
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - nodejs: 20.latest
            - pnpm: 8.3.1
      build:
        command: export PLATFORM=$PLATFORM WEBVIEW_CONSUMER=$WEBVIEW_CONSUMER ENVIRONMENT=$ENVIRONMENT && npm run bundle:webview
      artifacts:
        release: true
    - profile:
      name: engine
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - nodejs: 20.latest
            - pnpm: 8.3.1
      build:
        command: export PLATFORM=$PLATFORM PLUGINS=$PLUGINS ENVIRONMENT=$ENVIRONMENT && npm run bootstrap && npm run bundle:engine
      artifacts:
        release: true
    - profile:
      name: webview_and_engine
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - nodejs: 20.latest
            - pnpm: 8.3.1
      build:
        command: export PLATFORM=$PLATFORM PLUGINS=$PLUGINS ENVIRONMENT=$ENVIRONMENT WEBVIEW_CONSUMER=$WEBVIEW_CONSUMER && npm run bootstrap && npm run bundle:webview-and-engine && npm run bundle:kernel
      artifacts:
        release: true
    - profile:
      name: webview_doc
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - nodejs: 20.latest
            - pnpm: 8.3.1
      build:
        command: export PLATFORM=$PLATFORM PLUGINS=$PLUGINS ENVIRONMENT=$ENVIRONMENT WEBVIEW_CONSUMER=$WEBVIEW_CONSUMER && sh scripts/build-doc.sh
      artifacts:
        release: true
    - profile:
      name: kernel_doc
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - nodejs: 20.latest
            - pnpm: 8.3.1
      build:
        command: export PLATFORM=$PLATFORM PLUGINS=$PLUGINS ENVIRONMENT=$ENVIRONMENT WEBVIEW_CONSUMER=$WEBVIEW_CONSUMER && sh scripts/build-kernel-doc.sh
      artifacts:
        release: true
