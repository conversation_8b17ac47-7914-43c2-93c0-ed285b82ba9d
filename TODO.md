# Todo

- 当前选择的代码可能多选 string[]
- 提供当前选择的代码的 range
- 支持 commonjs 引入，兼容 vscode extension 的低版本 node（暂时不用）
- internals 可以随时调用，通信的session需要新开一路吗（不用）
- ~~idechannel draw 没用~~
- ~~pluginContainer fork重复赋值~~
- plugin->ide 通道没有特殊逻辑，走一个事件，在末尾分发
- validateCapabilityProviderMatch 报错了需要抛出来给开发者
- config 增加获取完整配置，不然用多个参数要多次调用


# 开发体验优化 Todo

- 后续增加脚本，扫描代码中 PLATFORM 变量使用情况，记录到文件
- 发版流水线增加上传至 iCoding 插件市场步骤，避免忘记上传
- WebView 和插件的通信协议在一个文件中维护，尽量保证设计的统一性
