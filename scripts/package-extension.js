/**
 * @file 发布插件
 * 因为现在 vsce 不支持workspace，需要compile好插件，然后运行脚本适配，然后再到对应package里自己打包
 * 该脚本要在编译之后运行
 * - 把需要打包的extension copy出来
 * - 修改package.json 去掉dependencies和devDependencies
 *
 * <AUTHOR>
 */
import fs from 'fs';
import fse from 'fs-extra';
import path from 'path';
import { createRequire } from 'module';
import { execSync } from 'child_process';
const require = createRequire(import.meta.url);
const ncp = require('ncp').ncp;

const mkVSIXdir = (rootPath, extension) => {
    // 创建vsix文件夹
    const vsixPackageFolder = path.join(rootPath, 'vsix', extension);
    // mkdir targetExtension folder
    if (!fs.existsSync(vsixPackageFolder)) {
        fse.removeSync(vsixPackageFolder);
    }
    fse.mkdirpSync(vsixPackageFolder);

    return vsixPackageFolder;
};

const finalizePackageJson = (vsixFolder) => {
    const packageJson = fse.readJSONSync(path.join(vsixFolder, 'package.json'));
    // remove dependecies/devDependencies 因为打包的时候不想再安装了
    delete packageJson.dependencies;
    delete packageJson.devDependencies;

    // 处理研发专注插件的 extensionDependencies
    if (packageJson.name === 'auto-focus') {
        if (process.env.IDE === 'vscode') {
            // 对微软市场发布的版本里的 publisher 需要是 baidu-int
            packageJson.publisher = 'baidu-int';
            // 同时需要依赖 baidu-int.console-cloud-account 插件获取鉴权信息
            packageJson.extensionDependencies = [
                'baidu-int.console-cloud-account',
            ];
        }
        else {
            delete packageJson.extensionDependencies;
        }
    }

    if (packageJson.name === 'comate') {
        if (process.env.COMATE_TARGET === 'swan') {
            packageJson.contributes.icons = [
                {
                    'id': 'comate-logo',
                    'description': 'Baidu Comate icon',
                    'default': {
                        'fontId': 'comate-font',
                        'fontPath': 'assets/comate.woff',
                        'fontCharacter': '\\e605'
                    }
                },
                {
                    'id': 'comate-disabled',
                    'description': 'Baidu Comate icon',
                    'default': {
                        'fontId': 'comate-font',
                        'fontPath': 'assets/comate.woff',
                        'fontCharacter': '\\e606'
                    }
                }
            ];
            packageJson.engines = {
                'vscode': '^1.63.2'
            };
        }
    }

    fse.writeJsonSync(path.join(vsixFolder, 'package.json'), packageJson);
};

const main = async () => {
    const rootPath = process.cwd();
    // 目标插件必须和packages里面的目录名一致
    const targetExtension = process.argv[2];
    console.log(`packaging ${targetExtension}...`);

    const vsixFolder = mkVSIXdir(rootPath, targetExtension);
    const extensionFolder = path.join(rootPath, 'packages', targetExtension);

    // 把整个extension文件夹复制出来
    await new Promise((resolve, reject) => {
        ncp(path.join(extensionFolder), vsixFolder, (error) => {
            if (error) {
                console.error(error);
                reject();
            }
            resolve();
        });
    });

    finalizePackageJson(vsixFolder);

    console.log(`packaging ${targetExtension} done`);
};

main();
