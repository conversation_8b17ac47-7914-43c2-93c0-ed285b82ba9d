import childProcess from 'node:child_process';
import path from 'node:path';
import fs from 'node:fs/promises';
import {existsSync} from 'node:fs';
import url from 'node:url';

export const __dirname = path.dirname(url.fileURLToPath(import.meta.url));
export const rootPath = path.resolve(__dirname, '../');

export const vscodePath = path.resolve(__dirname, '../packages/vscode');
export const engineConnectorPath = path.resolve(__dirname, '../packages/engine-connector');
export const kernelPath = path.resolve(__dirname, '../packages/kernel');
export const kernelSharedPath = path.resolve(__dirname, '../packages/kernel-shared');
export const enginePath = path.resolve(__dirname, '../packages/engine');
export const clientPath = path.resolve(__dirname, '../packages/client');
export const sharedInternalsPath = path.resolve(__dirname, '../packages/shared-internals');
export const pluginsPath = path.resolve(__dirname, '../plugins');

export const judgePathIn = fullpath => {
    const pathFolders = {
        vscodePath,
        engineConnectorPath,
        enginePath,
        clientPath,
        sharedInternalsPath,
        pluginsPath,
        kernelSharedPath,
        kernelPath,
    };

    const matchingModule = Object.keys(pathFolders).find(moduleName => {
        const modulePath = pathFolders[moduleName];
        return fullpath.startsWith(modulePath);
    });
    return matchingModule ? matchingModule : 'unknown';
};

export async function copyFolder(sourceDir, targetDir) {
    // 创建目标文件夹
    await fs.mkdir(targetDir, {recursive: true});

    // 读取源文件夹中的文件/子文件夹
    const files = await fs.readdir(sourceDir);
    for (const file of files) {
        const sourcePath = path.join(sourceDir, file);
        const targetPath = path.join(targetDir, file);

        // 获取文件/文件夹的详细信息
        const stats = await fs.stat(sourcePath);

        if (stats.isFile()) {
            // 如果是文件，直接拷贝
            await fs.copyFile(sourcePath, targetPath);
        }
        else if (stats.isDirectory()) {
            // 如果是文件夹，递归拷贝子文件夹
            await copyFolder(sourcePath, targetPath);
        }
    }
}

export function exec(command) {
    return new Promise((resolve, reject) => {
        childProcess.exec(command, (error, stdout) => {
            if (error) {
                // always log what running result is
                console.log(stdout);
                reject(error);
                return;
            }
            resolve(stdout);
        });
    });
}

export async function pathExists(path) {
    try {
        await fs.access(path);
        return true;
    }
    catch (error) {
        if (error.code === 'ENOENT') {
            return false;
        }
        throw error;
    }
}

export async function pluginPackageDirectories() {
    const directories = await fs.readdir(pluginsPath);
    return directories.filter(v => existsSync(path.join(pluginsPath, v, 'package.json')));
}

const YOUARETHEBEST = [
    '编译完美通过！干得漂亮 🌟 冲鸭！',
    '所有模块都说YES！你简直是编译之神 ✨',
    '30+个模块全军覆没，就是这么强 💪',
    '完美编译！这波操作太秀了 🎯',
    '编译成功！代码质量杠杠的 🎨',
    '全部通过！今天也是充满干劲的一天呢 ⚡',
    '搞定！这代码写得太优雅了 🎩',
    '编译顺利完成！继续保持这份热情 🔥',
    '全部模块都说OK！不愧是你 👑',
    '完美通过！这状态简直无敌了 🚀',
    '编译成功！这代码太棒了 🌈',
    '编译成功！这代码太棒了 🌈',
    '构建完成！你今天的效率简直MAX 🌈',
    '所有测试点全部通过，太帅了吧 ✌️',
    '编译成功！这代码写得像诗一样优美 🎭',
    '完美！这波操作给满分 🌟',
    '编译通过！你的代码就是我的信仰 💫',
    '搞定啦！这质量简直就是艺术品 🎨',
    '全部模块编译成功，你就是传说中的代码王者 👑',
    '构建完美通过！这么快我都惊呆了 ⚡',
    '编译成功！这效率简直让人窒息 🚀',
    '编译通过！代码之神附体了吧 💫',
    '咱们的代码库又变得更强大了呢 🌹',
    '编译完成！这波操作太极限了 🎯',
    'Perfect！这代码写得太优雅了 ✨',
    '编译顺利通过！今天也是被你帅到的一天 🌞',
    '全部成功！你的代码充满了灵魂 💎',
];

export const randBest = () => {
    return YOUARETHEBEST[Math.floor(Math.random() * YOUARETHEBEST.length)];
};
