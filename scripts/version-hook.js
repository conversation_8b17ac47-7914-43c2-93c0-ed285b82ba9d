import fs from 'node:fs/promises';
import {globby} from 'globby';

const files = await globby('packages/*/package.json');
const packageJsonMap = new Map();

for (const file of files) {
    const json = JSON.parse(await fs.readFile(file, 'utf-8'));
    packageJsonMap.set(json.name, {file, json});
}

for (const [name, value] of packageJsonMap) {
    const {peerDependencies} = value.json;

    if (!peerDependencies) {
        continue;
    }

    for (const peerName of Object.keys(peerDependencies)) {
        if (peerName.startsWith('@comate/')) {
            const version = packageJsonMap.get(peerName).json.version;
            peerDependencies[peerName] = `^${version}`;
        }
    }
    await fs.writeFile(value.file, JSON.stringify(value.json, null, 2) + '\n');
}

const templateJsonFile = 'packages/init/templates/plugin/package.json';
const templateJson = JSON.parse(await fs.readFile(templateJsonFile, 'utf-8'));
for (const name of Object.keys(templateJson.devDependencies)) {
    if (name.startsWith('@comate/')) {
        const version = packageJsonMap.get(name).json.version;
        templateJson.devDependencies[name] = `^${version}`;
    }
}
await fs.writeFile(templateJsonFile, JSON.stringify(templateJson, null, 2) + '\n');
