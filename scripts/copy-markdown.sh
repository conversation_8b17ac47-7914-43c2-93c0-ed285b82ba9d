#!/bin/bash

if [ "$AGILE_MODULE_NAME" = "baidu/ide/comate-plugin-host" ]; then
    echo "流水线跳过copy help markdown";
    exit 0;
fi

ROOT_DIR="plugins"
ENGINE_DIR="packages/engine-connector/dist"

mkdir -p "$ENGINE_DIR" || { echo "Failed to create dist directory $ENGINE_DIR"; exit 1; }

for dir in "$ROOT_DIR"/*; do
    if [ -d "$dir" ]; then
        dir_name=$(basename "$dir")
        source_file="$dir/src/help.md"
        engine_file="$ENGINE_DIR/plugins/$dir_name/dist/help.md"

        if [ -f "$source_file" ]; then
            cp "$source_file" "$engine_file" || { echo "Failed to copy $source_file to $engine_file"; exit 1; }
            echo "Copied $source_file to $engine_file"
        else
            echo "$source_file of plugin $dir_name does not exist. Skipping."
        fi
    fi
done
