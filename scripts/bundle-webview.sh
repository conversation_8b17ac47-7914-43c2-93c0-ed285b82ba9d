#!/usr/bin/env bash

set -e

# Install dependencies
pnpm install --frozen-lockfile

# Build all packages
pnpm run build

# Make the output directory
mkdir -p output/

# Bundle webview
cd packages/vscode
npm run bundle:webview

# Copy the bundled webview to output directory
cp -r ../webview/consumer/dist/$WEBVIEW_CONSUMER ../../output/webview
cd ../../output/webview

touch VERSION.txt
echo "Branch: $AGILE_COMPILE_BRANCH" >> VERSION.txt
echo "Commit: $AGILE_REVISION" >> VERSION.txt
echo "Target: $WEBVIEW_CONSUMER-$PLATFORM-$ENVIRONMENT" >> VERSION.txt
