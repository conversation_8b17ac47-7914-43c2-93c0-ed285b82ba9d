import path from 'node:path';
import chokidar from 'chokidar';
import readline from 'readline';
import chalk from 'chalk';
import fs from 'node:fs/promises';
import childProcess from 'node:child_process';
import _ from 'lodash';
import {
    exec,
    judgePathIn,
    pathExists,
    rootPath,
    vscodePath,
    engineConnectorPath,
    enginePath,
    clientPath,
    sharedInternalsPath,
    pluginsPath,
    kernelPath,
    kernelSharedPath,
    randBest,
} from './util.js';

let vscodeWatcherProcess = null;

let globalFlags = {
    enableVscodeWatcher: true,
    enableComateplusWatcher: true,
};

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
});

// TODO 粒度比较粗，可以考虑优化
const options = {
    ignored: /(^|[\/\\])(\..|node_modules|dist|bundle)/,
    persistent: true,
};

const comateplusWatcher = chokidar.watch(
    [pluginsPath, enginePath, sharedInternalsPath, clientPath, engineConnectorPath, kernelPath, kernelSharedPath],
    options
);

// add debounce 解救你的cpu
comateplusWatcher.on(
    'change',
    _.debounce(async data => {
        if (!globalFlags.enableComateplusWatcher) {
            return;
        }
        console.log(chalk.magenta(new Date().toLocaleString()), chalk.yellow(`module in ${judgePathIn(data)}`));
        console.log(chalk.blue(path.relative(rootPath, data)), chalk.yellow('文件变更，开始构建...'));
        try {
            const start = Date.now();
            await exec('pnpm run build');
            console.log(chalk.cyan(`耗时${(Date.now() - start) / 1000}s`), chalk.green(randBest()));
        }
        catch (e) {
            console.warn(chalk.red('构建异常=========查看上面报哪个模块的错信息=========\n\n'));
            console.error('pmpm run build error:', e);
        }
    }, 500)
);

async function main() {
    await fs.mkdir(
        path.join(engineConnectorPath, 'dist', 'node_modules', '@comate'),
        {recursive: true}
    );

    try {
        const pluginsLinkPath = path.join(engineConnectorPath, 'dist', 'plugins');
        // 建立到plugins的软链
        await fs.rm(pluginsLinkPath, {force: true});
        await fs.symlink(pluginsPath, pluginsLinkPath);
        console.log('建立到plugins的软链成功');
    }
    catch (ex) {
        console.error(`建立到plugins的软链失败: ${ex.message}`);
    }

    if (
        !(await pathExists(
            path.join(
                engineConnectorPath,
                'dist',
                'node_modules',
                '@comate',
                'plugin-shared-internals'
            )
        ))
    ) {
        await exec(
            `ln -s ${path.join(sharedInternalsPath)} ${
                path.join(
                    engineConnectorPath,
                    'dist',
                    'node_modules',
                    '@comate',
                    'plugin-shared-internals'
                )
            }`
        );
    }
    if (
        !(await pathExists(
            path.join(
                engineConnectorPath,
                'dist',
                'node_modules',
                '@comate',
                'plugin-engine'
            )
        ))
    ) {
        await exec(
            `ln -s ${path.join(enginePath)} ${
                path.join(
                    engineConnectorPath,
                    'dist',
                    'node_modules',
                    '@comate',
                    'plugin-engine'
                )
            }`
        );
    }
    if (
        !(await pathExists(
            path.join(
                engineConnectorPath,
                'dist',
                'node_modules',
                '@comate',
                'plugin-host'
            )
        ))
    ) {
        await exec(
            `ln -s ${path.join(clientPath)} ${
                path.join(
                    engineConnectorPath,
                    'dist',
                    'node_modules',
                    '@comate',
                    'plugin-host'
                )
            }`
        );
    }

    // Vscode 插件自身的watch
    const vscodeWatcher = childProcess.spawn('npm', ['run', 'watch'], {
        cwd: vscodePath,
    });

    vscodeWatcher.stdout.on('data', data => {
        console.log(data.toString());
    });

    vscodeWatcher.stderr.on('data', data => {
        console.error(data.toString());
    });

    vscodeWatcher.on('close', code => {
        console.log(`子进程退出，退出码 ${code}`);
    });
    // 父进程退出时杀掉子进程
    process.on('exit', () => {
        vscodeWatcher.kill();
    });

    // 捕获ctrl+c事件 ，退出前杀掉子进程
    process.on('SIGINT', () => {
        vscodeWatcher.kill();
        process.exit();
    });
    return vscodeWatcher;
}

// 开发非ide，不需要这个main，加速开发
vscodeWatcherProcess = main();

// 监听用户输入
rl.on('line', async input => {
    if (input.toLowerCase() === 'cls') {
        console.clear(); // 清除控制台
    }
    if (input.toLowerCase() === '0') {
        globalFlags.enableComateplusWatcher = false;
        usage();
    }
    if (input.toLowerCase() === '1') {
        globalFlags.enableComateplusWatcher = true;
        usage();
    }
    if (input.toLowerCase() === 'fix') {
        try {
            console.log(chalk.blue('尝试全build修复...'));
            globalFlags.enableComateplusWatcher = false;
            await exec('pnpm run build');
        }
        catch (e) {
            console.log(e);
            console.error(chalk.red('修复异常=========查看上面报哪个模块的错信息=========\n\n'));
            globalFlags.enableComateplusWatcher = true;
        }
        console.log(chalk.green('修复完成\n'));
        globalFlags.enableComateplusWatcher = true;
        usage();
    }
});

const usage = () => {
    console.log(chalk.green('输入 cls 并按回车来清除控制台'));
    console.log(chalk.green('输入 fix 尝试全build修复'));
    console.log(chalk.green('输入 0   关闭engine+kernel的watch'));
    console.log(chalk.green('输入 1   打开engine+kernel的watch'));
    console.log('\n');
};
// 显示使用说明
setTimeout(usage, 15000);

// 处理程序退出
rl.on('SIGINT', () => {
    rl.close();
    process.exit();
});
