/* eslint-disable max-len */
import childProcess from 'node:child_process';
import { mkdir, access } from 'node:fs/promises';
import readline from 'node:readline';
import path from 'node:path';
import fs from 'node:fs/promises';
import {__dirname, exec, pathExists} from './util.js';

const {
    BCE_REGION,
    BCE_AK,
    BCE_SK,
    BOS_BUCKET,
    BOS_VSIX_DIR,
    BOS_PACKAGE_DIR
} = process.env;

console.log('BCE_REGION', BCE_REGION);
console.log('BCE_AK', BCE_AK);
console.log('BCE_SK', BCE_SK);
console.log('BOS_BUCKET', BOS_BUCKET);
console.log('BOS_VSIX_DIR', BOS_VSIX_DIR);

if (!BCE_REGION || !BCE_AK || !BCE_SK || !BOS_BUCKET || !BOS_VSIX_DIR || !BOS_PACKAGE_DIR) {
    console.log('缺少环境变量，发布失败');
    process.exit(1);
}

async function main() {
    try {
        await exec('which bos-pages');
    } catch (error) {
        await exec('npm install -g @baidu/bos-pages@5.0.1 --registry=http://registry.npm.baidu-int.com');
    }
    await mkdir(path.resolve(__dirname, '../vsix/output'), { recursive: true });

    const isComateExist = await pathExists(path.resolve(__dirname, '../vsix/vscode'));
    if (!isComateExist) {
        console.log('vsix/vscode not exist, skip publish');
        process.exit(0);
    }

    const vsixList = (await fs.readdir(path.resolve(__dirname, '../vsix')))
        .filter(name => name.endsWith('.vsix'))
        .map(v => v.replace('.vsix', ''))
        .map(v => v.replace('comate-', ''));


    // 展示vsix列表，并且要求用户输入对应版本号
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    const vsixVersion = await new Promise((resolve, reject) => {
        rl.question(`请输入要发布的vsix版本号，已经存在的版本：\n${vsixList.map(v => `\x1b[32m>\x1b[0m ${v}`).join('\n')} \n`,
            (answer) => {
                rl.close();
                resolve(answer);
            });
    });
    if (!vsixList.includes(vsixVersion)) {
        console.log('输入的版本号不在可选范围内');
        process.exit(1);
    }
    console.log(`开始发布vsix: ${vsixVersion}`);
    console.log(`正在上传vsix文件到BOS: ${BOS_VSIX_DIR}`);
    await exec(`cp ${path.resolve(__dirname, `../vsix/comate-${vsixVersion}.vsix`)} ${
        path.resolve(__dirname, `../vsix/output/comate-${vsixVersion}.vsix`)
    }`);
    await exec(`bos-pages ${path.resolve(__dirname, '../vsix/output')} ${BOS_VSIX_DIR} --force`, {
        env: {
            BCE_REGION,
            BCE_AK,
            BCE_SK,
            BOS_BUCKET
        }
    });
    const name = `comate-${vsixVersion}`;
    await exec(`cd ${path.resolve(__dirname, '../vsix/output')} && unzip ${name}.vsix && mkdir comate && mv extension comate/${name} && tar -zcf ${name}.tar.gz comate && mkdir tmp && mv ${name}.tar.gz tmp/${name}.tar.gz`);
    console.log(`正在上传vsix文件到BOS: ${BOS_PACKAGE_DIR}`);
    await exec(`cd ${path.resolve(__dirname, '../vsix/output')} && bos-pages ${path.resolve(__dirname, '../vsix/output/tmp')} ${BOS_PACKAGE_DIR} --force`, {
        env: {
            BCE_REGION,
            BCE_AK,
            BCE_SK,
            BOS_BUCKET
        }
    });
    await exec(`cd ${path.resolve(__dirname, '../vsix/output')} && mv comate/${name} comate/baidu.${name} && tar -zcf ${name}.tar.gz comate && mkdir package && mv ${name}.tar.gz package`);
    console.log(`正在上传vsix文件到BOS: ${BOS_VSIX_DIR}/duguanjia`);
    await exec(`cd ${path.resolve(__dirname, '../vsix/output')} && bos-pages ${path.resolve(__dirname, '../vsix/output/package')} ${BOS_VSIX_DIR}/duguanjia --force`, {
        env: {
            BCE_REGION,
            BCE_AK,
            BCE_SK,
            BOS_BUCKET
        }
    });
    console.log('更新度管家版本');
    await exec(`curl --location --request POST "https://comate.now.baidu-int.com/api/duguanjia/version?ide=VSCODE&version=${vsixVersion}"`);
    console.log('发布成功，在 https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/_SKPgSwp2G/mcV1ZM02Cz/2d40345a031b4c 更新 changelog');
}

main().catch(error => {
    console.error(error);
    process.exit(1);
});
