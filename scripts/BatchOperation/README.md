# 为插件批量生成 `/help 插件介绍`

**注意：正常情况下不应该理会这一脚本，它与项目运行不相关。已经为`plugins`目录下的各插件已经生成了 `help.ts` 和 `help.md` ，并已经在 `index.ts` 和 `package.json` 注册能力，因此这一脚本不应再次使用，除非真的有必要并且你知道你在做什么**

可以为 `plugins` 目录下所有插件生成 `/help 插件介绍` 功能并注册。

具体用途为：
- 为各插件生成 `help.ts`
- 读取 `help` 目录下 `{plugin-name}.md` 文件，并复制到 `plugins` 目录各插件下
- 注册插件能力到 `index.ts`
- 注册插件能力到 `package.json`

使用说明：
1. 进入 `scripts/BatchOperation` 目录下
2. 执行 `go build` 编译
3. 执行 `./BatchOperation` 运行，`-r` 参数声明 `comate-plugin-host` 根目录（在当前位置是 `../..`）

如果需要批量更新 `/help 插件介绍` 对应的markdown文件：
1. 更新 `help` 目录下 `{plugin-name}.md` 文件
2. 在 `main.go` 的 `processPlugin` 函数下注释掉 `Task 2` 以外的函数调用
3. 重新编译后执行

更多请阅读 `main.go` 中的注释
