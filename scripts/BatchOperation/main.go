// this project is to add help.ts, help.md, modify package.json and index.ts for each plugin directory
package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

const (
	fPath = "./help/"
)

// main reads the plugins directory and processes each plugin directory
func main() {
	rootFlag := flag.String("r", ".", "Root directory")
	flag.Parse()
	root := *rootFlag

	pluginDirs, err := os.ReadDir(filepath.Join(root, "plugins"))
	if err != nil {
		fmt.Println("Error reading plugins directory:", err)
		return
	}

	for _, dir := range pluginDirs {
		if !dir.IsDir() {
			continue
		}
		dirName := dir.Name()

		// plugins exempted from processing (tor, demo-feature, demo-timer, postman)
		// tor has been modified manually by someone previously
		// demo-feature, demo-timer, postman are not to be displayed in sidebar, no need to process
		if dirName == "demo-timer" || dirName == "postman" || dirName == "security" {
			continue
		}

		err := processPlugin(root, dirName)
		if err != nil {
			fmt.Printf("Error processing plugin directory %s: %v\n", dirName, err)
		} else {
			fmt.Printf("Processed plugin directory: %s\n", dirName)
		}
	}
}

func processPlugin(root, dirName string) error {
	// Task 1: Write into help.ts
	err := createHelpTs(root, dirName)
	if err != nil {
		return fmt.Errorf("error creating help.ts: %w", err)
	}

	// Task 2: Create {dirName}.help.md and write into it
	err = createHelpMd(root, dirName)
	if err != nil {
		return fmt.Errorf("error creating help.md: %w", err)
	}

	// Task 3: Modify package.json
	err = modifyPackageJson(root, dirName)
	if err != nil {
		return fmt.Errorf("error modifying package.json: %w", err)
	}

	// Task 4: Modify index.ts
	err = modifyIndexTs(root, dirName)
	if err != nil {
		return fmt.Errorf("error modifying index.ts: %w", err)
	}

	return nil
}

// createHelpTs creates help.ts file in the plugin directory
func createHelpTs(root, dirName string) error {

	// latest: discared plugin prefix ( yield ... {dirName}.help.md -> yield ... help.md )
	helpTsContent := `import {
    FunctionParameterDefinition,
    SkillProvider,
    TaskProgressChunk,
    TaskProgressChunkStream,
} from '@comate/plugin-host';
import {readFileSync} from 'fs';
import {fileURLToPath} from 'node:url';
import {dirname} from 'path';

export class HelpSkillProvider extends SkillProvider {
    static skillName = '{dirName}-help';
    static displayName: '插件介绍';
    static description = '插件介绍';

    static parameters: FunctionParameterDefinition = {
        type: 'object',
        properties: {},
    };

    async *execute(): AsyncIterableIterator<TaskProgressChunk> {
        const stream = new TaskProgressChunkStream();
        const filename = fileURLToPath(import.meta.url);
        const content = readFileSync(` + "`${dirname(filename)}/help.md`" + `, 'utf8');
        yield stream.flush(content);
    }
}
`

	helpTsContent = strings.Replace(helpTsContent, "{dirName}", dirName, -1)
	helpTsPath := filepath.Join(root, "plugins", dirName, "src", "providers", "help.ts")

	err := os.MkdirAll(filepath.Dir(helpTsPath), 0755)
	if err != nil {
		return err
	}

	err = os.WriteFile(helpTsPath, []byte(helpTsContent), 0644)
	return err
}

// createHelpMd creates help.md file in the plugin directory
func createHelpMd(root, dirName string) error {
	helpMdSourcePath := filepath.Join(fPath, dirName+".md")
	helpMdContent, err := os.ReadFile(helpMdSourcePath)
	if err != nil {
		if os.IsNotExist(err) {
			// If the source markdown file doesn't exist, create a default content
			helpMdContent = []byte(fmt.Sprintf("# %s - help\n", dirName))
		} else {
			return err
		}
	}

	helpMdDestPath := filepath.Join(root, "plugins", dirName, "src", "help.md")
	err = os.MkdirAll(filepath.Dir(helpMdDestPath), 0755)
	if err != nil {
		return err
	}
	err = os.WriteFile(helpMdDestPath, helpMdContent, 0644)
	return err
}

// modifyPackageJson modifies package.json file in the plugin directory
func modifyPackageJson(root, dirName string) error {
	packageJsonPath := filepath.Join(root, "plugins", dirName, "package.json")
	packageJsonContent, err := os.ReadFile(packageJsonPath)
	if err != nil {
		return err
	}

	packageJsonStr := string(packageJsonContent)
	modification := `
      {
        "type": "Skill",
        "name": "{dirName}-help",
        "displayName": "插件介绍",
        "description": "插件介绍"
      },`
	modContent := strings.Replace(modification, "{dirName}", dirName, -1)
	packageJsonStr = strings.Replace(packageJsonStr, `"capabilities": [`, `"capabilities": [`+modContent, 1)

	err = os.WriteFile(packageJsonPath, []byte(packageJsonStr), 0644)
	return err
}

// modifyIndexTs modifies index.ts file in the plugin directory
func modifyIndexTs(root, dirName string) error {
	indexTsPath := filepath.Join(root, "plugins", dirName, "src", "index.ts")
	indexTsContent, err := os.ReadFile(indexTsPath)
	if err != nil {
		return err
	}

	lines := strings.Split(string(indexTsContent), "\n")
	importStatement := `import {HelpSkillProvider} from './providers/help.js';`
	setupFunction := "export function setup({registry}: PluginSetupContext) {"
	registryLine := `    registry.registerSkillProvider('{dirName}-help', HelpSkillProvider);`
	regLineContent := strings.Replace(registryLine, "{dirName}", dirName, -1)

	importExists := false
	for _, line := range lines {
		if strings.Contains(line, importStatement) {
			importExists = true
			break
		}
	}
	if !importExists {
		lines = append([]string{importStatement}, lines...)
	}

	for i, line := range lines {
		if strings.TrimSpace(line) == setupFunction {
			lines = append(lines[:i+1], append([]string{regLineContent}, lines[i+1:]...)...)
			break
		}
	}

	newIndexTsContent := strings.Join(lines, "\n")

	err = os.WriteFile(indexTsPath, []byte(newIndexTsContent), 0644)
	return err
}
