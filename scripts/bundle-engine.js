import path from 'node:path';
import fs from 'fs-extra';
import {globby} from 'globby';
import {execa} from 'execa';
import {exec, engineConnectorPath, pluginsPath, vscodePath, pathExists, pluginPackageDirectories} from './util.js';

/**
 * @type Array<[src: string, tgt: string]>
 */
const packages = [
    ['shared-internals', 'plugin-shared-internals'],
    ['client', 'plugin-host'],
    ['engine', 'plugin-engine'],
];

/**
 * @param {[src: string, tgt: string]} entry
 */
async function bundleShared([src, tgt]) {
    console.log(`    > ${src}`);
    const cwd = path.join('packages', src);
    await execa('npm', ['run', 'build'], {cwd});
    await execa('npm', ['run', 'bundle'], {cwd});
    const packageOutputDirectory = path.join(engineConnectorPath, 'bundle', 'node_modules', '@comate', tgt);
    await fs.ensureDir(path.join(packageOutputDirectory, 'dist'));
    await fs.copy(path.join(cwd, 'bundle'), path.join(packageOutputDirectory, 'dist'));
    await fs.remove(path.join(cwd, 'bundle'));
    await fs.copy(path.join(cwd, 'package.json'), path.join(packageOutputDirectory, 'package.json'));
}

/**
 * @param {string} plugin
 */
async function bundlePlugin(plugin) {
    console.log(`    > ${plugin}`);
    const cwd = path.join('plugins', plugin);

    await execa('npm', ['run', 'build'], {cwd});
    const pluginOutputDirectory = path.join(engineConnectorPath, 'bundle', 'plugins', plugin);
    await fs.ensureDir(pluginOutputDirectory);

    if (fs.existsSync(path.join(cwd, 'src/help.md'))) {
        await fs.copy(path.join(cwd, 'src/help.md'), path.join(cwd, 'dist/help.md'));
    }

    await fs.copy(path.join(cwd, 'dist'), path.join(pluginOutputDirectory, 'dist'));
    await fs.copy(path.join(cwd, 'assets'), path.join(pluginOutputDirectory, 'assets'));
    await fs.copy(path.join(cwd, 'package.json'), path.join(pluginOutputDirectory, 'package.json'));
}

async function main() {
    console.log('Bundle engine');

    console.log('  > cleanup');
    await fs.emptyDir(path.join(engineConnectorPath, 'bundle'));

    console.log('  > engine-connector');
    await execa(
        'npm',
        ['run', 'bundle'],
        {cwd: engineConnectorPath}
    );
    await fs.copy(
        path.join(engineConnectorPath, 'package.json'),
        path.join(engineConnectorPath, 'bundle', 'package.json')
    );
    await fs.ensureDir(path.join(engineConnectorPath, 'bundle', 'node_modules', '@comate'));
    await fs.ensureDir(path.join(engineConnectorPath, 'bundle', 'plugins'));

    // 构建engine client shared-internals
    console.log('  > shared');
    await Promise.all(packages.map(bundleShared));
    await fs.copy(
        path.join('packages', 'client', 'bin.js'),
        path.join(engineConnectorPath, 'bundle', 'node_modules', '@comate', 'plugin-host', 'bin.js')
    );

    // 构建plugin
    console.log('  > plugins');
    let plugins = await pluginPackageDirectories();
    // 环境变量中指定了要构建的插件
    if (process.env.PLUGINS) {
        const bundlePlugins = process.env.PLUGINS.split(',');
        plugins = plugins.filter(plugin => bundlePlugins.includes(plugin));
    }
    await Promise.all(plugins.map(bundlePlugin));

    // 构建VSCode extension
    console.log(' > vscode');
    await fs.emptyDir(path.join(vscodePath, 'bundle'));
    await execa(
        'npm',
        ['run', 'compile'],
        {
            cwd: vscodePath,
            env: {
                NODE_ENV: 'production',
                BUILD_TARGET: 'stable',
            },
        }
    );
    await fs.copy(path.join(vscodePath, 'dist'), path.join(vscodePath, 'bundle'));
    await fs.ensureDir(path.join(vscodePath, 'bundle', 'comate-engine'));
    await fs.copy(path.join(engineConnectorPath, 'bundle'), path.join(vscodePath, 'bundle', 'comate-engine'));
    await fs.ensureDir(path.join(vscodePath, 'bundle', 'comate-engine', 'bin'));
    await fs.copy(
        path.join(engineConnectorPath, 'bin', 'bundle.js'),
        path.join(vscodePath, 'bundle', 'comate-engine', 'bin', 'comate.js')
    );

    // 同步plugin
    console.log('  > plugin assets');
    for (const plugin of plugins) {
        console.log(`    > ${plugin}`);
        const pluginOutputDirectory = path.join(vscodePath, 'bundle', 'comate-engine', 'plugins', plugin);
        const pluginPackageInfo = await fs.readJson(path.join(pluginOutputDirectory, 'package.json'));
        const icon = pluginPackageInfo.comate?.icon;

        if (!icon) {
            console.error(`  No icon configured in plugin ${plugin}`);
            process.exit(404);
        }

        const pluginAssetDirectory = path.join(vscodePath, 'bundle', 'assets', 'plugins', plugin);
        await fs.ensureDir(pluginAssetDirectory);
        await fs.copyFile(
            path.join(pluginOutputDirectory, icon),
            path.join(pluginAssetDirectory, path.basename(icon))
        );
    }
}

main();
