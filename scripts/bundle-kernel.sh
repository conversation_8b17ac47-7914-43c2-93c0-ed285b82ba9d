#!/usr/bin/env bash

set -e

# Make the output directory
mkdir -p output


cd packages/kernel && pnpm run bundle && mv bundle kernel
touch kernel/VERSION.txt
echo "Branch: $AGILE_COMPILE_BRANCH" >> kernel/VERSION.txt
echo "Commit: $AGILE_REVISION" >> kernel/VERSION.txt
echo "Target: $PLATFORM-$ENVIRONMENT" >> kernel/VERSION.txt


tar -czf ../../output/comate-kernel-$(date '+%m%d_%H%M').tar.gz ./kernel
rm -rf ./kernel


