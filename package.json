{"private": true, "name": "comate-plugin", "version": "0.0.1", "type": "module", "license": "MIT", "engines": {"node": ">18.15.0 <=20.11.0"}, "packageManager": "pnpm@8.10.2", "scripts": {"init:plugin": "cd plugins && node ../packages/init/dist/index.js", "format": "dprint fmt '**/*.{ts,tsx,json}'", "lint": "lerna run --no-bail lint", "bootstrap": "pnpm install && lerna run build && sh packages/engine-connector/ln.sh", "build": "lerna run build --verbose && sh packages/engine-connector/ln.sh && sh scripts/copy-markdown.sh", "playground": "lerna run start --scope=@comate/plugin-playground", "build-clean": "lerna run clean && npm run build", "release": "lerna version --no-push --force-publish", "release-beta": "npm run release -- --conventional-prerelease --preid=beta", "ci": "pnpm install --frozen-lockfile && npm run build-clean && npm run lint", "push:cr": "git push origin HEAD:refs/for/$(git symbolic-ref --short -q HEAD)", "postinstall": "husky install", "preversion": "npm run ci", "version": "node scripts/version-hook.js && git add .", "prepack": "pnpm install --frozen-lockfile && npm run build-clean", "deploy": "lerna publish from-package --registry=https://registry.npmjs.org", "prepare": "husky install", "bundle:internal": "export PLATFORM=internal && node ./scripts/bundle-engine.js && sh ./scripts/bundle-vscode.sh", "bundle:saas": "export PLATFORM=saas && node ./scripts/bundle-engine.js && sh ./scripts/bundle-vscode.sh", "bundle:poc": "export PLATFORM=poc && node ./scripts/bundle-engine.js && sh ./scripts/bundle-vscode.sh", "bundle:internal:test": "export PLATFORM=internal ENVIRONMENT=test && node ./scripts/bundle-engine.js && sh ./scripts/bundle-vscode.sh", "bundle:saas:test": "export PLATFORM=saas ENVIRONMENT=test && node ./scripts/bundle-engine.js && sh ./scripts/bundle-vscode.sh", "bundle:webview": "sh ./scripts/bundle-webview.sh", "bundle:engine": "sh ./scripts/bundle-extension.sh", "bundle:webview-and-engine": "sh ./scripts/bundle-webview.sh && sh ./scripts/bundle-extension.sh", "bundle:kernel": "sh ./scripts/bundle-extension.sh", "watch": "npm run build && node ./scripts/watch.js", "publish:vscode": "node ./scripts/publish.js", "pre-commit": "<PERSON>rna run pre-commit", "typedoc": "cd packages/vscode && pnpm run doc", "typedoc:kernel-internal": "cd packages/kernel-shared && pnpm run doc"}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/eslint-parser": "^7.23.3", "@babel/eslint-plugin": "^7.23.5", "@ecomfe/eslint-config": "^8.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "chalk": "^5.3.0", "chokidar": "^3.5.3", "dprint": "^0.45.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "execa": "^8.0.1", "fs-extra": "^11.2.0", "globby": "13.2.2", "husky": "^8.0.3", "lerna": "^8.0.2", "lodash": "^4.17.21", "ncp": "^2.0.0", "rimraf": "^5.0.5", "rollup": "^4.9.1", "rollup-plugin-dts": "^6.1.0", "typescript": "^5.3.2", "vitest": "^1.2.2"}, "pnpm": {"patchedDependencies": {"react-mentions@4.4.10": "patches/<EMAIL>"}}}